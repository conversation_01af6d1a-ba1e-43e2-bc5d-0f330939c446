# Version control
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
env/
ENV/
.venv/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
media/
staticfiles/
static/
*.sqlite3
*.db

# Logs
*.log
logs/
debug.log

# Documentation
*.md
README*
docs/

# Development files
.env
.env.local
.env.development
.env.test
.env.production

# Backup files
*.bak
*.backup
*.old

# Temporary files
tmp/
temp/
.tmp/

# Build artifacts
dist/
build/
*.egg-info/

# Deployment files
docker-compose.yml
docker-compose.*.yml
Dockerfile.dev

# SSH keys
*.pem
*.key
*.pub
known_hosts

# CSV and data files
csv_files/
*.csv
data/

# Celery beat schedule
celerybeat-schedule.db 