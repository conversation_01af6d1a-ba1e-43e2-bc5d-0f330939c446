services:
  # Main Django Web Service
  - type: web
    name: satoloc-backend
    env: docker
    dockerfilePath: ./Dockerfile
    region: oregon
    plan: starter
    branch: main
    buildCommand: "./render-build.sh"
    startCommand: "gunicorn satoloc_backend.wsgi:application --bind 0.0.0.0:$PORT --workers 3 --timeout 300"
    healthCheckPath: /admin/

    envVars:
      - key: DJANGO_ENV
        value: production
      - key: DEBUG
        value: false
      - key: DJANGO_SECRET_KEY
        generateValue: true
      - key: ALLOWED_HOSTS
        value: "*" # Will be restricted in settings.py
      - key: DATABASE_URL
        fromDatabase:
          name: satoloc-postgres
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: satoloc-redis
          property: connectionString

  # PostgreSQL Database
  - type: pserv
    name: satoloc-postgres
    env: docker
    region: oregon
    plan: starter
    dockerCommand: postgres:15-alpine
    disk:
      name: postgres-data
      mountPath: /var/lib/postgresql/data
      sizeGB: 10

  # Redis Service
  - type: redis
    name: satoloc-redis
    region: oregon
    plan: starter
    maxmemoryPolicy: allkeys-lru

  # Celery Worker Service
  - type: worker
    name: satoloc-celery-worker
    env: docker
    dockerfilePath: ./Dockerfile
    region: oregon
    plan: starter
    branch: main
    buildCommand: "./render-build.sh"
    startCommand: "celery -A satoloc_backend worker -l info --concurrency=2"

    envVars:
      - key: DJANGO_ENV
        value: production
      - key: DEBUG
        value: false
      - key: DJANGO_SECRET_KEY
        sync: false # Will use the same secret as web service
      - key: DATABASE_URL
        fromDatabase:
          name: satoloc-postgres
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: satoloc-redis
          property: connectionString

  # Celery Beat Scheduler Service
  - type: worker
    name: satoloc-celery-beat
    env: docker
    dockerfilePath: ./Dockerfile
    region: oregon
    plan: starter
    branch: main
    buildCommand: "./render-build.sh"
    startCommand: "celery -A satoloc_backend beat -l info"

    envVars:
      - key: DJANGO_ENV
        value: production
      - key: DEBUG
        value: false
      - key: DJANGO_SECRET_KEY
        sync: false # Will use the same secret as web service
      - key: DATABASE_URL
        fromDatabase:
          name: satoloc-postgres
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: satoloc-redis
          property: connectionString

databases:
  - name: satoloc-postgres
    databaseName: satoloc
    user: satoloc_user
