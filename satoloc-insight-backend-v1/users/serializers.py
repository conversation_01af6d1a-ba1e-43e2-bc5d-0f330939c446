from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from django.utils import timezone
from decimal import Decimal, InvalidOperation
import re

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    created_by_username = serializers.CharField(
        source="created_by.username", read_only=True
    )
    created_users_count = serializers.SerializerMethodField()
    remaining_user_slots = serializers.SerializerMethodField()
    can_create_more_users = serializers.SerializerMethodField()
    created_users = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = (
            "id",
            "username",
            "email",
            "first_name",
            "last_name",
            "role",
            "profile_picture",
            "registration_date",
            "is_active",
            "website",
            "industry",
            "created",
            "company_name",
            "subscription_plan",
            "subscription_price",
            "subscription_start_date",
            "created_by",
            "created_by_username",
            "created_users_count",
            "remaining_user_slots",
            "can_create_more_users",
            "created_users",
        )
        read_only_fields = ("registration_date",)

    def get_created_users_count(self, obj):
        """Get the number of users created by this user"""
        return obj.get_created_users_count()

    def get_remaining_user_slots(self, obj):
        """Get remaining user slots for sub-admins"""
        return obj.get_remaining_user_slots()

    def get_can_create_more_users(self, obj):
        """Check if user can create more users"""
        return obj.can_create_more_users()

    def get_created_users(self, obj):
        """Get list of users created by this user (for sub-admins)"""
        if obj.role == "sub_admin":
            created_users = obj.created_users.all()
            return [
                {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "is_active": user.is_active,
                    "registration_date": user.registration_date,
                }
                for user in created_users
            ]
        return []

    def validate_role(self, value):
        request = self.context.get("request")
        if not request or request.user.role != "admin":
            raise serializers.ValidationError("Only admins can change roles")
        return value

    def validate_subscription_start_date(self, value):
        """
        Validate subscription start date - allow admins to modify it
        """
        request = self.context.get("request")
        if not request or request.user.role != "admin":
            raise serializers.ValidationError(
                "Only admins can modify subscription start date"
            )
        return value

    def validate_created_by(self, value):
        """
        Validate created_by field - only admins can modify it
        """
        request = self.context.get("request")
        if not request or request.user.role != "admin":
            raise serializers.ValidationError(
                "Only admins can modify the created_by field"
            )
        # If value is provided, ensure it's a valid sub-admin user
        if value is not None:
            try:
                # Handle both User object and integer ID
                if isinstance(value, User):
                    creator = value
                else:
                    creator_id = int(value)
                    creator = User.objects.get(id=creator_id)

                if creator.role != "sub_admin":
                    raise serializers.ValidationError(
                        "Created by user must be a sub-admin"
                    )

                # Return the User instance for ForeignKey assignment
                return creator
            except (User.DoesNotExist, ValueError, TypeError):
                raise serializers.ValidationError(
                    "Selected creator does not exist or is invalid"
                )
        return value


class RegisterSerializer(serializers.ModelSerializer):
    password = serializers.CharField(
        write_only=True, required=True, validators=[validate_password]
    )
    confirmPassword = serializers.CharField(write_only=True, required=True)
    subscriptionPlan = serializers.CharField(
        write_only=True, required=False, default="freemium"
    )
    subscriptionPrice = serializers.CharField(
        write_only=True, required=False, allow_null=True, allow_blank=True
    )

    class Meta:
        model = User
        fields = (
            "username",
            "email",
            "password",
            "confirmPassword",
            "subscriptionPlan",
            "subscriptionPrice",
        )

    def validate(self, attrs):
        if attrs["password"] != attrs["confirmPassword"]:
            raise serializers.ValidationError(
                {"password": "Password fields didn't match."}
            )
        return attrs

    def validate_email(self, value):
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("A user with this email already exists.")
        return value

    def validate_subscriptionPlan(self, value):
        if value:
            valid_plans = [choice[0] for choice in User.SUBSCRIPTION_PLAN_CHOICES]
            if value not in valid_plans:
                raise serializers.ValidationError(
                    f"'{value}' is not a valid subscription plan."
                )
        return value

    def validate_subscriptionPrice(self, value):
        if not value or value == "":
            return None

        # Strip currency symbols and whitespace
        cleaned_value = re.sub(r"[^\d.-]", "", str(value))

        if not cleaned_value:
            return None

        try:
            decimal_value = Decimal(cleaned_value)
            if decimal_value < 0:
                raise serializers.ValidationError("Price cannot be negative.")
            return decimal_value
        except (InvalidOperation, ValueError):
            raise serializers.ValidationError("Invalid price format.")

    def create(self, validated_data):
        validated_data.pop("confirmPassword", None)
        subscription_plan = validated_data.pop("subscriptionPlan", "freemium")
        subscription_price = validated_data.pop("subscriptionPrice", None)

        # Set subscription fields
        validated_data["subscription_plan"] = subscription_plan
        validated_data["subscription_price"] = subscription_price

        # Set subscription start date if not freemium plan
        if subscription_plan != "freemium":
            validated_data["subscription_start_date"] = timezone.now()

        return User.objects.create_user(**validated_data)


class CreateUserSerializer(serializers.ModelSerializer):
    """Serializer for creating users by admins/sub-admins"""

    password = serializers.CharField(
        write_only=True, required=True, validators=[validate_password]
    )
    confirmPassword = serializers.CharField(write_only=True, required=True)
    subscription_price = serializers.CharField(
        write_only=True, required=False, allow_null=True, allow_blank=True
    )

    class Meta:
        model = User
        fields = (
            "username",
            "email",
            "password",
            "confirmPassword",
            "first_name",
            "last_name",
            "role",
            "subscription_plan",
            "subscription_price",
            "company_name",
            "website",
            "industry",
        )

    def validate(self, attrs):
        if attrs["password"] != attrs["confirmPassword"]:
            raise serializers.ValidationError(
                {"password": "Password fields didn't match."}
            )
        return attrs

    def validate_email(self, value):
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("A user with this email already exists.")
        return value

    def validate_role(self, value):
        """Validate role based on who's creating the user"""
        request = self.context.get("request")
        if not request:
            raise serializers.ValidationError("Request context required")

        creator = request.user

        # Sub-admins can only create regular users
        if creator.role == "sub_admin" and value != "user":
            raise serializers.ValidationError(
                "Sub-admins can only create regular users"
            )

        # Only admins can create sub-admins and admins
        if value in ["admin", "sub_admin"] and creator.role != "admin":
            raise serializers.ValidationError(
                "Only admins can create admin or sub-admin users"
            )

        return value

    def validate_subscription_plan(self, value):
        if value:
            valid_plans = [choice[0] for choice in User.SUBSCRIPTION_PLAN_CHOICES]
            if value not in valid_plans:
                raise serializers.ValidationError(
                    f"'{value}' is not a valid subscription plan."
                )
        return value

    def validate_subscription_price(self, value):
        if not value or value == "":
            return None

        # Strip currency symbols and whitespace
        cleaned_value = re.sub(r"[^\d.-]", "", str(value))

        if not cleaned_value:
            return None

        try:
            decimal_value = Decimal(cleaned_value)
            if decimal_value < 0:
                raise serializers.ValidationError("Price cannot be negative.")
            return decimal_value
        except (InvalidOperation, ValueError):
            raise serializers.ValidationError("Invalid price format.")

    def validate(self, attrs):
        # Call parent validation first
        attrs = super().validate(attrs)

        request = self.context.get("request")
        if not request:
            raise serializers.ValidationError("Request context required")

        creator = request.user

        # Check if creator can create users
        if not creator.can_create_users():
            raise serializers.ValidationError(
                "You don't have permission to create users"
            )

        # Check sub-admin user limit
        if creator.role == "sub_admin" and not creator.can_create_more_users():
            raise serializers.ValidationError(
                f"Sub-admins can only create up to 3 users. You have already created {creator.get_created_users_count()} users."
            )

        return attrs

    def create(self, validated_data):
        validated_data.pop("confirmPassword", None)
        subscription_price = validated_data.pop("subscription_price", None)

        # Set subscription price
        validated_data["subscription_price"] = subscription_price

        # Set subscription start date if not freemium plan
        if validated_data.get("subscription_plan") != "freemium":
            validated_data["subscription_start_date"] = timezone.now()

        # Set creator relationship for both admins and sub-admins
        request = self.context.get("request")
        if request and request.user:
            validated_data["created_by"] = request.user

        return User.objects.create_user(**validated_data)


class UpdateProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = (
            "first_name",
            "last_name",
            "email",
            "company_name",
            "website",
            "industry",
        )
        extra_kwargs = {"email": {"required": False}}

    def validate_email(self, value):
        user = self.context["request"].user
        if User.objects.exclude(pk=user.pk).filter(email=value).exists():
            raise serializers.ValidationError("This email is already in use.")
        return value

    def validate_industry(self, value):
        valid_industries = [choice[0] for choice in User.INDUSTRY_CHOICES]
        if value and value not in valid_industries:
            raise serializers.ValidationError(f"'{value}' is not a valid choice.")
        return value


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    def validate(self, attrs):
        data = super().validate(attrs)
        user = self.user
        data["user"] = {
            "id": str(user.id),
            "username": user.username,
            "email": user.email,
            "role": user.role,
            "isAdmin": user.role == "admin",
            "isSubAdmin": user.role == "sub_admin",
            "first_name": user.first_name,
            "last_name": user.last_name,
            "company_name": user.company_name,
            "website": user.website,
            "industry": user.industry,
            "created": user.created.isoformat() if user.created else None,
            "subscription_plan": user.subscription_plan,
            "subscription_price": (
                str(user.subscription_price) if user.subscription_price else None
            ),
            "subscription_start_date": (
                user.subscription_start_date.isoformat()
                if user.subscription_start_date
                else None
            ),
            "can_create_users": user.can_create_users(),
            "created_users_count": user.get_created_users_count(),
            "remaining_user_slots": user.get_remaining_user_slots(),
            "can_create_more_users": user.can_create_more_users(),
        }
        return data

    def create(self, validated_data):
        raise NotImplementedError("`create()` is not supported for token serializers.")

    def update(self, instance, validated_data):
        raise NotImplementedError("`update()` is not supported for token serializers.")
