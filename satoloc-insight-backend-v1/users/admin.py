# users/admin.py
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import CustomUser


class CreatedUsersInline(admin.TabularInline):
    """Inline admin for users created by sub-admins"""

    model = CustomUser
    fk_name = "created_by"
    extra = 0
    can_delete = False
    verbose_name = "Created User"
    verbose_name_plural = "Users Created by this Sub-Admin"

    fields = (
        "username",
        "email",
        "first_name",
        "last_name",
        "is_active",
        "registration_date",
    )
    readonly_fields = (
        "username",
        "email",
        "first_name",
        "last_name",
        "is_active",
        "registration_date",
    )

    def has_add_permission(self, request, obj=None):
        return False  # Don't allow adding users through inline


@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    list_display = (
        "username",
        "email",
        "first_name",
        "last_name",
        "role",
        "created_by_display",
        "created_users_count_display",
        "remaining_slots_display",
        "industry",
        "company_name",
        "subscription_plan",
        "is_active",
    )
    list_filter = ("role", "is_active", "industry", "subscription_plan", "created_by")
    search_fields = ("username", "email", "first_name", "last_name", "company_name")
    ordering = ("-registration_date",)

    def created_by_display(self, obj):
        """Display who created this user"""
        if obj.created_by:
            url = reverse("admin:users_customuser_change", args=[obj.created_by.pk])
            return format_html('<a href="{}">{}</a>', url, obj.created_by.username)
        return "-"

    created_by_display.short_description = "Created By"

    def created_users_count_display(self, obj):
        """Display count of users created by this user"""
        if obj.role in ["admin", "sub_admin"]:
            count = obj.get_created_users_count()
            if obj.role == "sub_admin":
                return f"{count}/3"
            return f"{count} (unlimited)"
        return "-"

    created_users_count_display.short_description = "Created Users"

    def remaining_slots_display(self, obj):
        """Display remaining user creation slots"""
        if obj.role == "sub_admin":
            remaining = obj.get_remaining_user_slots()
            if remaining == 0:
                return format_html('<span style="color: red;">0 (limit reached)</span>')
            else:
                return format_html('<span style="color: green;">{}</span>', remaining)
        elif obj.role == "admin":
            return format_html('<span style="color: blue;">Unlimited</span>')
        return "-"

    remaining_slots_display.short_description = "Remaining Slots"

    fieldsets = (
        (None, {"fields": ("username", "password")}),
        ("Personal info", {"fields": ("first_name", "last_name", "email")}),
        (
            "Profile",
            {
                "fields": (
                    "profile_picture",
                    "website",
                    "company_name",
                    "industry",
                    "created",
                )
            },
        ),
        (
            "User Management",
            {
                "fields": (
                    "created_by",
                    "created_users_info",
                    "user_creation_stats",
                ),
                "classes": ("collapse",),
            },
        ),
        (
            "Subscription",
            {
                "fields": (
                    "subscription_plan",
                    "subscription_price",
                    "subscription_start_date",
                ),
                "classes": ("collapse",),
            },
        ),
        ("Permissions", {"fields": ("is_active", "is_staff", "is_superuser", "role")}),
        ("Important dates", {"fields": ("last_login", "registration_date")}),
    )
    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": ("username", "email", "password1", "password2", "role"),
            },
        ),
    )
    readonly_fields = (
        "registration_date",
        "subscription_start_date",
        "created_users_info",
        "user_creation_stats",
    )

    def created_users_info(self, obj):
        """Display list of users created by this user"""
        if obj.role in ["admin", "sub_admin"]:
            created_users = obj.created_users.all()
            if created_users.exists():
                user_links = []
                for user in created_users:
                    url = reverse("admin:users_customuser_change", args=[user.pk])
                    user_links.append(
                        format_html('<a href="{}">{}</a>', url, user.username)
                    )
                return format_html("<br>".join(user_links))
            else:
                return "No users created yet"
        return "Not applicable (regular user)"

    created_users_info.short_description = "Created Users List"

    def user_creation_stats(self, obj):
        """Display user creation statistics"""
        if obj.role == "sub_admin":
            count = obj.get_created_users_count()
            remaining = obj.get_remaining_user_slots()
            can_create = obj.can_create_more_users()

            stats = [
                f"<strong>Created:</strong> {count}/3",
                f"<strong>Remaining:</strong> {remaining}",
                f"<strong>Can create more:</strong> {'Yes' if can_create else 'No'}",
            ]

            if remaining == 0:
                stats.append(
                    '<span style="color: red;"><strong>Limit reached!</strong></span>'
                )

            return format_html("<br>".join(stats))
        elif obj.role == "admin":
            count = obj.get_created_users_count()
            return format_html(
                "<strong>Created:</strong> {} (unlimited)<br>"
                "<strong>Can create more:</strong> Yes",
                count,
            )
        return "Not applicable (regular user)"

    user_creation_stats.short_description = "Creation Statistics"

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = super().get_readonly_fields(request, obj)
        if obj:  # If editing existing user
            return readonly_fields + ("subscription_start_date",)
        return readonly_fields

    def get_inlines(self, request, obj):
        """Show inline created users only for sub-admins and admins"""
        if obj and obj.role in ["admin", "sub_admin"]:
            return [CreatedUsersInline]
        return []

    actions = ["promote_to_sub_admin", "reset_user_creation_limit"]

    def promote_to_sub_admin(self, request, queryset):
        """Promote selected regular users to sub-admin"""
        updated = 0
        for user in queryset.filter(role="user"):
            user.role = "sub_admin"
            user.save()
            updated += 1

        if updated:
            self.message_user(
                request, f"Successfully promoted {updated} user(s) to sub-admin."
            )
        else:
            self.message_user(
                request,
                "No regular users were selected for promotion.",
                level="warning",
            )

    promote_to_sub_admin.short_description = "Promote selected users to Sub-Admin"

    def reset_user_creation_limit(self, request, queryset):
        """Reset sub-admin user creation limit by deleting created_by relationships"""
        reset_count = 0
        for user in queryset.filter(role="sub_admin"):
            # This would remove the created_by relationship, effectively "resetting" the limit
            # In a real scenario, you might want to archive rather than delete relationships
            created_users = user.created_users.all()
            count = created_users.count()
            if count > 0:
                # Option 1: Remove the relationship (users remain but aren't "owned" by sub-admin)
                created_users.update(created_by=None)
                reset_count += 1

        if reset_count:
            self.message_user(
                request,
                f"Successfully reset creation limit for {reset_count} sub-admin(s).",
            )
        else:
            self.message_user(
                request,
                "No sub-admins with created users were selected.",
                level="warning",
            )

    reset_user_creation_limit.short_description = (
        "Reset user creation limit for Sub-Admins"
    )
