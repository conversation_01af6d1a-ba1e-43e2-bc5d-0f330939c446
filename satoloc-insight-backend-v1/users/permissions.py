# users/permissions.py

from rest_framework.permissions import BasePermission


class IsAdminRole(BasePermission):
    """
    Allows access only to users with role 'admin'.
    """

    def has_permission(self, request, view):
        print(
            f"IsAdminRole check for user: {request.user}, role: {getattr(request.user, 'role', None)}"
        )
        return bool(
            request.user
            and request.user.is_authenticated
            and (request.user.role == "admin" or request.user.is_superuser)
        )


class IsAdminOrSubAdminRole(BasePermission):
    """
    Allows access to users with role 'admin' or 'sub_admin'.
    """

    def has_permission(self, request, view):
        print(
            f"IsAdminOrSubAdminRole check for user: {request.user}, role: {getattr(request.user, 'role', None)}"
        )
        return bool(
            request.user
            and request.user.is_authenticated
            and (
                request.user.role in ["admin", "sub_admin"] or request.user.is_superuser
            )
        )
