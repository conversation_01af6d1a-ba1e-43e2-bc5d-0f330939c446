from django.db import models
from django.contrib.auth.models import AbstractUser


class CustomUser(AbstractUser):
    class Meta:
        app_label = "users"

    ROLES = (
        ("admin", "Administrator"),
        ("sub_admin", "Sub Administrator"),
        ("user", "Regular User"),
    )

    INDUSTRY_CHOICES = (
        ("web3", "Web3"),
        ("fintech", "Fintech"),
        ("technology", "Technology"),
    )

    SUBSCRIPTION_PLAN_CHOICES = (
        ("freemium", "Freemium"),
        ("pro", "Pro"),
        ("enterprise", "Enterprise"),
    )

    role = models.CharField(max_length=20, choices=ROLES, default="user")
    profile_picture = models.ImageField(
        upload_to="profile_pictures/", null=True, blank=True
    )
    registration_date = models.DateTimeField(auto_now_add=True)
    email = models.EmailField(unique=True)

    website = models.URLField(null=True, blank=True)
    industry = models.Char<PERSON>ield(
        max_length=50, choices=INDUSTRY_CHOICES, null=True, blank=True
    )
    created = models.DateField(null=True, blank=True)
    company_name = models.CharField(max_length=255, null=True, blank=True)

    # Subscription fields
    subscription_plan = models.CharField(
        max_length=20,
        choices=SUBSCRIPTION_PLAN_CHOICES,
        default="freemium",
        help_text="User's current subscription plan",
    )
    subscription_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Monthly subscription price in USD",
    )
    subscription_start_date = models.DateTimeField(
        null=True, blank=True, help_text="When the subscription started"
    )

    # Google OAuth2 fields
    google_id = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        unique=True,
        db_index=True,
        help_text="Google user ID from OAuth2",
    )
    is_google_user = models.BooleanField(
        default=False, help_text="Whether this user was created via Google OAuth2"
    )
    google_email_verified = models.BooleanField(
        default=False, help_text="Whether Google email is verified"
    )
    google_picture = models.URLField(
        null=True, blank=True, help_text="Google profile picture URL"
    )
    google_refresh_token = models.TextField(
        null=True, blank=True, help_text="Encrypted Google refresh token for API access"
    )
    google_access_token_expires = models.DateTimeField(
        null=True, blank=True, help_text="When the Google access token expires"
    )

    # Sub-admin relationship fields
    created_by = models.ForeignKey(
        "self",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="created_users",
        help_text="Sub-admin who created this user",
    )

    groups = models.ManyToManyField(
        "auth.Group",
        related_name="custom_user_set",
        blank=True,
        help_text="The groups this user belongs to.",
    )
    user_permissions = models.ManyToManyField(
        "auth.Permission",
        related_name="custom_user_set",
        blank=True,
        help_text="Specific permissions for this user.",
    )

    def __str__(self) -> str:
        return str(self.username)

    def has_valid_google_token(self):
        """
        Check if user has a valid Google access token
        """
        if not self.google_refresh_token:
            return False

        if not self.google_access_token_expires:
            return False

        from django.utils import timezone

        return timezone.now() < self.google_access_token_expires

    def is_google_authenticated(self):
        """
        Check if user is authenticated with Google
        """
        return bool(self.google_id and self.is_google_user)

    def can_create_users(self):
        """
        Check if this user can create other users
        Only sub-admins and admins can create users
        """
        return self.role in ["admin", "sub_admin"]

    def get_created_users_count(self):
        """
        Get the number of users created by this sub-admin
        """
        return self.created_users.count()

    def can_create_more_users(self):
        """
        Check if this sub-admin can create more users
        Sub-admins are limited to 3 users, admins have no limit
        """
        if self.role == "admin":
            return True
        elif self.role == "sub_admin":
            return self.get_created_users_count() < 3
        else:
            return False

    def get_remaining_user_slots(self):
        """
        Get the remaining number of users this sub-admin can create
        Returns None for admins (unlimited)
        """
        if self.role == "admin":
            return None  # Unlimited
        elif self.role == "sub_admin":
            return max(0, 3 - self.get_created_users_count())
        else:
            return 0
