from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from .google_oauth import GoogleOAuth2Handler, generate_secure_username
from .email_utils import send_user_registration_notification, send_welcome_email_to_user
import logging
import json
from datetime import timed<PERSON><PERSON>
from cryptography.fernet import Fernet
from django.conf import settings
import base64
import hashlib

logger = logging.getLogger(__name__)
User = get_user_model()


def get_encryption_key():
    """
    Generate encryption key from Django secret key for token encryption
    """
    key = hashlib.sha256(settings.SECRET_KEY.encode()).digest()
    return base64.urlsafe_b64encode(key)


def encrypt_token(token):
    """
    Encrypt sensitive tokens before storing in database
    """
    if not token:
        return None

    try:
        f = Fernet(get_encryption_key())
        encrypted_token = f.encrypt(token.encode())
        return encrypted_token.decode()
    except Exception as e:
        logger.error(f"Error encrypting token: {str(e)}")
        return None


def decrypt_token(encrypted_token):
    """
    Decrypt tokens when retrieving from database
    """
    if not encrypted_token:
        return None

    try:
        f = Fernet(get_encryption_key())
        decrypted_token = f.decrypt(encrypted_token.encode())
        return decrypted_token.decode()
    except Exception as e:
        logger.error(f"Error decrypting token: {str(e)}")
        return None


@api_view(["GET"])
@permission_classes([AllowAny])
def google_auth_url(request):
    """
    Generate Google OAuth2 authorization URL

    GET /api/auth/google/url/
    """
    try:
        oauth_handler = GoogleOAuth2Handler()

        # Generate secure state parameter
        state = oauth_handler.generate_state()

        # Get redirect URI from request or use default
        redirect_uri = request.GET.get(
            "redirect_uri", "http://localhost:3000/auth/google/callback"
        )

        # Generate authorization URL
        auth_url = oauth_handler.get_authorization_url(redirect_uri, state)

        return Response(
            {
                "authorization_url": auth_url,
                "state": state,
                "redirect_uri": redirect_uri,
            },
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        logger.error(f"Error generating Google auth URL: {str(e)}")
        return Response(
            {"error": "Failed to generate authorization URL", "detail": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([AllowAny])
def google_auth_callback(request):
    """
    Handle Google OAuth2 callback and authenticate/register user

    POST /api/auth/google/callback/
    {
        "code": "authorization_code",
        "state": "state_parameter",
        "redirect_uri": "redirect_uri_used"
    }
    """
    try:
        data = request.data
        code = data.get("code")
        state = data.get("state")
        redirect_uri = data.get(
            "redirect_uri", "http://localhost:3000/auth/google/callback"
        )

        if not code:
            return Response(
                {"error": "Authorization code is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not state:
            return Response(
                {"error": "State parameter is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        oauth_handler = GoogleOAuth2Handler()

        # Validate state parameter
        state_data = oauth_handler.validate_state(state)
        if not state_data:
            return Response(
                {"error": "Invalid or expired state parameter"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Exchange code for tokens
        credentials = oauth_handler.exchange_code_for_token(code, redirect_uri, state)

        # Extract user information
        user_info = oauth_handler.get_user_info(credentials)

        if not user_info.get("email_verified"):
            return Response(
                {"error": "Google email is not verified"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if user already exists
        google_id = user_info.get("google_id")
        email = user_info.get("email")

        user = None
        is_new_registration = False

        # Try to find user by Google ID first
        if google_id:
            try:
                user = User.objects.get(google_id=google_id)
                logger.info(f"Found existing user by Google ID: {user.username}")
            except User.DoesNotExist:
                pass

        # If not found by Google ID, try by email
        if not user and email:
            try:
                user = User.objects.get(email=email)
                # Link Google account to existing user
                user.google_id = google_id
                user.is_google_user = True
                user.google_email_verified = user_info.get("email_verified", False)
                user.google_picture = user_info.get("picture")
                logger.info(f"Linked Google account to existing user: {user.username}")
            except User.DoesNotExist:
                pass

        # Create new user if not found
        if not user:
            is_new_registration = True
            username = generate_secure_username(email, google_id)

            # Ensure username is unique
            original_username = username
            counter = 1
            while User.objects.filter(username=username).exists():
                username = f"{original_username}_{counter}"
                counter += 1

            user = User.objects.create_user(
                username=username,
                email=email,
                first_name=user_info.get("given_name", ""),
                last_name=user_info.get("family_name", ""),
                google_id=google_id,
                is_google_user=True,
                google_email_verified=user_info.get("email_verified", False),
                google_picture=user_info.get("picture"),
                subscription_plan="freemium",  # Default plan for Google users
            )

            logger.info(f"Created new Google user: {user.username}")

        # Update Google tokens
        if hasattr(credentials, "refresh_token") and credentials.refresh_token:
            user.google_refresh_token = encrypt_token(credentials.refresh_token)

        if hasattr(credentials, "expiry") and credentials.expiry:
            user.google_access_token_expires = credentials.expiry
        else:
            # Default to 1 hour expiry if not provided
            user.google_access_token_expires = timezone.now() + timedelta(hours=1)

        user.save()

        # Send welcome emails for new registrations
        if is_new_registration:
            try:
                # Send notification to admin
                admin_email_sent = send_user_registration_notification(user)

                # Send welcome email to user
                welcome_email_sent = send_welcome_email_to_user(user)

                logger.info(f"Welcome emails sent for new Google user: {user.username}")

            except Exception as email_error:
                logger.error(
                    f"Email notification error for Google user {user.username}: {str(email_error)}"
                )

        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token

        # Add custom claims
        access_token["google_authenticated"] = True
        access_token["is_google_user"] = user.is_google_user

        return Response(
            {
                "message": "Google authentication successful",
                "is_new_registration": is_new_registration,
                "access_token": str(access_token),
                "refresh_token": str(refresh),
                "user": {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "google_id": user.google_id,
                    "google_picture": user.google_picture,
                    "is_google_user": user.is_google_user,
                    "subscription_plan": user.subscription_plan,
                    "role": user.role,
                },
            },
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        logger.error(f"Error in Google OAuth callback: {str(e)}")
        return Response(
            {"error": "Google authentication failed", "detail": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([AllowAny])
def google_auth_token(request):
    """
    Direct Google token authentication (for server-to-server)

    POST /api/auth/google/token/
    {
        "id_token": "google_id_token"
    }
    """
    try:
        data = request.data
        id_token = data.get("id_token")

        if not id_token:
            return Response(
                {"error": "Google ID token is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        oauth_handler = GoogleOAuth2Handler()

        # Verify ID token and extract user info
        user_info = oauth_handler.verify_id_token(id_token)

        if not user_info.get("email_verified"):
            return Response(
                {"error": "Google email is not verified"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Similar user lookup/creation logic as callback
        google_id = user_info.get("sub")
        email = user_info.get("email")

        user = None
        is_new_registration = False

        # Try to find user by Google ID first
        if google_id:
            try:
                user = User.objects.get(google_id=google_id)
            except User.DoesNotExist:
                pass

        # If not found by Google ID, try by email
        if not user and email:
            try:
                user = User.objects.get(email=email)
                # Link Google account to existing user
                user.google_id = google_id
                user.is_google_user = True
                user.google_email_verified = user_info.get("email_verified", False)
                user.google_picture = user_info.get("picture")
            except User.DoesNotExist:
                pass

        # Create new user if not found
        if not user:
            is_new_registration = True
            username = generate_secure_username(email, google_id)

            # Ensure username is unique
            original_username = username
            counter = 1
            while User.objects.filter(username=username).exists():
                username = f"{original_username}_{counter}"
                counter += 1

            user = User.objects.create_user(
                username=username,
                email=email,
                first_name=user_info.get("given_name", ""),
                last_name=user_info.get("family_name", ""),
                google_id=google_id,
                is_google_user=True,
                google_email_verified=user_info.get("email_verified", False),
                google_picture=user_info.get("picture"),
                subscription_plan="freemium",
            )

        user.save()

        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token

        # Add custom claims
        access_token["google_authenticated"] = True
        access_token["is_google_user"] = user.is_google_user

        return Response(
            {
                "message": "Google token authentication successful",
                "is_new_registration": is_new_registration,
                "access_token": str(access_token),
                "refresh_token": str(refresh),
                "user": {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "google_id": user.google_id,
                    "google_picture": user.google_picture,
                    "is_google_user": user.is_google_user,
                    "subscription_plan": user.subscription_plan,
                    "role": user.role,
                },
            },
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        logger.error(f"Error in Google token authentication: {str(e)}")
        return Response(
            {"error": "Google token authentication failed", "detail": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
