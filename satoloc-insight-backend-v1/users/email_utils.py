# users/email_utils.py
from django.core.mail import send_mail, BadHeaderError
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.html import strip_tags
import logging

logger = logging.getLogger(__name__)

def send_user_registration_notification(user):
    """
    Send email notification to admin when a new user registers
    """
    try:
        # Email to admin about new user registration
        subject = f'New User Registration - {user.username}'
        
        # Create email context
        context = {
            'user': user,
            'username': user.username,
            'email': user.email,
            'subscription_plan': user.subscription_plan,
            'subscription_price': user.subscription_price,
            'registration_date': user.date_joined,
        }
        
        # Create HTML and plain text versions
        html_message = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #1279b4; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; background-color: #f9f9f9; }}
                .info-box {{ background-color: white; padding: 15px; margin: 10px 0; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>New User Registration</h1>
                </div>
                <div class="content">
                    <div class="info-box">
                        <h3>User Details:</h3>
                        <p><strong>Username:</strong> {context['username']}</p>
                        <p><strong>Email:</strong> {context['email']}</p>
                        <p><strong>Registration Date:</strong> {context['registration_date'].strftime('%Y-%m-%d %H:%M:%S')}</p>
                        <p><strong>Subscription Plan:</strong> {context['subscription_plan'] or 'Not specified'}</p>
                        <p><strong>Subscription Price:</strong> {context['subscription_price'] or 'Free'}</p>
                    </div>
                    <p>A new user has registered on SatoLOC Insight platform. Please review the user details above.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # Plain text version
        plain_message = f"""
        New User Registration - SatoLOC Insight
        
        A new user has registered on the platform:
        
        User Details:
        - Username: {context['username']}
        - Email: {context['email']}
        - Registration Date: {context['registration_date'].strftime('%Y-%m-%d %H:%M:%S')}
        - Subscription Plan: {context['subscription_plan'] or 'Not specified'}
        - Subscription Price: {context['subscription_price'] or 'Free'}
        
        Please review the user details in the admin panel.
        """
        
        # Send email
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[settings.ADMIN_EMAIL],
            html_message=html_message,
            fail_silently=False,
        )
        
        logger.info(f"Registration notification email sent for user: {user.username}")
        return True
        
    except BadHeaderError:
        logger.error("Invalid header found when sending registration notification email")
        return False
    except Exception as e:
        logger.error(f"Error sending registration notification email: {str(e)}")
        return False

def send_welcome_email_to_user(user):
    """
    Send welcome email to the new user
    """
    try:
        subject = 'Welcome to SatoLOC Insight!'
        
        # Create HTML welcome message
        html_message = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #1279b4; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; background-color: #f9f9f9; }}
                .welcome-box {{ background-color: white; padding: 20px; margin: 10px 0; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                .button {{ background-color: #1279b4; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Welcome to SatoLOC Insight!</h1>
                </div>
                <div class="content">
                    <div class="welcome-box">
                        <h2>Hello {user.username}!</h2>
                        <p>Thank you for registering with SatoLOC Insight. We're excited to have you on board!</p>
                        
                        <h3>What's Next?</h3>
                        <ul>
                            <li>Complete your profile setup</li>
                            <li>Explore our AI-driven localization insights</li>
                            <li>Try our AutoLQA technology</li>
                            <li>Access data-backed SEO strategies</li>
                        </ul>
                        
                        <p>Your subscription plan: <strong>{user.subscription_plan or 'Freemium'}</strong></p>
                        
                        <p>If you have any questions, feel free to reach out to our support team.</p>
                        
                        <p>Best regards,<br>The SatoLOC Insight Team</p>
                    </div>
                </div>
            </div>
        </body>
        </html>
        """
        
        # Plain text version
        plain_message = f"""
        Welcome to SatoLOC Insight!
        
        Hello {user.username}!
        
        Thank you for registering with SatoLOC Insight. We're excited to have you on board!
        
        What's Next?
        - Complete your profile setup
        - Explore our AI-driven localization insights
        - Try our AutoLQA technology
        - Access data-backed SEO strategies
        
        Your subscription plan: {user.subscription_plan or 'Freemium'}
        
        If you have any questions, feel free to reach out to our support team.
        
        Best regards,
        The SatoLOC Insight Team
        """
        
        # Send welcome email to user
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False,
        )
        
        logger.info(f"Welcome email sent to user: {user.email}")
        return True
        
    except BadHeaderError:
        logger.error("Invalid header found when sending welcome email")
        return False
    except Exception as e:
        logger.error(f"Error sending welcome email: {str(e)}")
        return False 