# users/views.py
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework.exceptions import PermissionDenied
from rest_framework import status, generics
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.viewsets import ModelViewSet
from django.contrib.auth import get_user_model, authenticate
from django.db import models
from .permissions import IsAdminRole, IsAdminOrSubAdminRole
from .serializers import (
    UserSerializer,
    RegisterSerializer,
    UpdateProfileSerializer,
    CustomTokenObtainPairSerializer,
    CreateUserSerializer,
)
from .email_utils import send_user_registration_notification, send_welcome_email_to_user
import logging
from rest_framework.decorators import api_view, permission_classes
from rest_framework_simplejwt.tokens import RefreshToken
from .models import CustomUser

logger = logging.getLogger(__name__)

User = get_user_model()


class RegisterView(generics.CreateAPIView):
    permission_classes = (AllowAny,)
    serializer_class = RegisterSerializer

    def create(self, request, *args, **kwargs):
        # Log the incoming registration data for debugging
        print("Registration data received:", request.data)

        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            print("Registration validation errors:", serializer.errors)
            return Response(
                {"error": "Validation failed", "details": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            user = serializer.save()
            print(
                f"User created successfully: {user.username}, Plan: {user.subscription_plan}, Price: {user.subscription_price}"
            )

            # Send email notifications
            try:
                # Send notification to admin
                admin_email_sent = send_user_registration_notification(user)

                # Send welcome email to user
                welcome_email_sent = send_welcome_email_to_user(user)

                if admin_email_sent:
                    logger.info(
                        f"Admin notification email sent for user: {user.username}"
                    )
                else:
                    logger.warning(
                        f"Failed to send admin notification email for user: {user.username}"
                    )

                if welcome_email_sent:
                    logger.info(f"Welcome email sent to user: {user.email}")
                else:
                    logger.warning(
                        f"Failed to send welcome email to user: {user.email}"
                    )

            except Exception as email_error:
                # Log email error but don't fail the registration
                logger.error(
                    f"Email notification error for user {user.username}: {str(email_error)}"
                )

            return Response(
                {
                    "message": "User created successfully",
                    "user": {
                        "id": user.id,
                        "username": user.username,
                        "email": user.email,
                        "subscription_plan": user.subscription_plan,
                        "subscription_price": (
                            str(user.subscription_price)
                            if user.subscription_price
                            else None
                        ),
                    },
                },
                status=status.HTTP_201_CREATED,
            )
        except Exception as e:
            print(f"Registration error: {str(e)}")
            return Response(
                {"error": "Registration failed", "detail": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer


class UserProfileView(generics.RetrieveAPIView):
    serializer_class = UserSerializer
    permission_classes = (IsAuthenticated,)

    def get_object(self):
        return self.request.user

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)


class UpdateProfileView(generics.UpdateAPIView):
    serializer_class = UpdateProfileSerializer
    permission_classes = (IsAuthenticated,)

    def get_object(self):
        return self.request.user

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop("partial", False)
        instance = self.get_object()

        # Print the incoming data for debugging
        print("Received data:", request.data)

        serializer = self.get_serializer(instance, data=request.data, partial=partial)

        if serializer.is_valid():
            self.perform_update(serializer)
            return Response(serializer.data)

        print("Serializer errors:", serializer.errors)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def perform_update(self, serializer):
        serializer.save()


class AdminUserViewSet(ModelViewSet):
    permission_classes = [
        IsAuthenticated,
        IsAdminOrSubAdminRole,
    ]  # Allow admins and sub-admins
    queryset = get_user_model().objects.all()
    serializer_class = UserSerializer

    def get_serializer_class(self):
        """Use different serializers for different actions"""
        if self.action == "create":
            return CreateUserSerializer
        return UserSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["request"] = self.request
        return context

    def get_queryset(self):
        """Filter queryset based on user role"""
        queryset = super().get_queryset()
        user = self.request.user

        if user.role == "admin":
            # Admins can see all users
            return queryset
        elif user.role == "sub_admin":
            # Sub-admins can only see users they created and themselves
            return queryset.filter(models.Q(created_by=user) | models.Q(id=user.id))
        else:
            # Regular users shouldn't access this viewset, but just in case
            return queryset.filter(id=user.id)

    def create(self, request, *args, **kwargs):
        """Create a new user with proper validation"""
        serializer = self.get_serializer(data=request.data)

        if serializer.is_valid():
            try:
                user = serializer.save()
                print(f"User created by {request.user.username}: {user.username}")

                # Send email notifications (optional)
                try:
                    send_user_registration_notification(user)
                    send_welcome_email_to_user(user)
                except Exception as email_error:
                    logger.warning(f"Email notification failed: {str(email_error)}")

                # Return user data using UserSerializer for consistent response
                user_serializer = UserSerializer(user, context={"request": request})
                return Response(user_serializer.data, status=status.HTTP_201_CREATED)

            except Exception as e:
                print(f"User creation error: {str(e)}")
                return Response(
                    {"error": "User creation failed", "detail": str(e)},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            print("User creation validation errors:", serializer.errors)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            return Response(serializer.data)
        else:
            print("Serializer errors:", serializer.errors)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    # def list(self, request, *args, **kwargs):
    #     """Override list method to handle GET /users/"""
    #     queryset = self.get_queryset()
    #     serializer = self.get_serializer(queryset, many=True)
    #     return Response(serializer.data)

    # def get_queryset(self):
    #     """Ensure only admin users can access"""
    #     if not self.request.user.role == 'admin':
    #         raise PermissionDenied("Must be admin to access user list")
    #     return super().get_queryset()

    def perform_destroy(self, instance):
        if instance.role == "admin":
            raise PermissionDenied("Cannot delete admin users")
        print(f"Deleting user: {instance.username}")
        instance.delete()

    def perform_update(self, serializer):
        if (
            self.get_object().role == "admin"
            and self.request.user != self.get_object()
            and serializer.validated_data.get("role") != "admin"
        ):
            raise PermissionDenied("Cannot modify other admin users' roles")
        serializer.save()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_user_data(request):
    """
    Get authenticated user data - supports both regular JWT and Google OAuth tokens
    """
    try:
        user = request.user

        return Response(
            {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "role": "admin" if user.is_staff else "user",
                "is_staff": user.is_staff,
                "is_google_user": getattr(user, "is_google_user", False),
                "subscription_plan": getattr(user, "subscription_plan", "free"),
                "subscription_price": getattr(user, "subscription_price", "0"),
                "date_joined": user.date_joined,
            },
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        logger.error(f"Error fetching user data: {str(e)}")
        return Response(
            {"error": "Failed to fetch user data"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([AllowAny])
def login_view(request):
    """
    Login endpoint for NextAuth integration
    Handles both regular login and Google OAuth
    """
    try:
        username = request.data.get("username")
        password = request.data.get("password")
        is_google_auth = request.data.get("isGoogleAuth") == "true"
        google_access_token = request.data.get("googleAccessToken")

        if not username:
            return Response(
                {"error": "Username is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        # Handle Google OAuth authentication
        if is_google_auth and google_access_token:
            try:
                # Find user by username (Google users)
                user = User.objects.get(username=username)

                if not user.is_google_user:
                    return Response(
                        {"error": "User is not a Google OAuth user"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                # Generate JWT tokens
                refresh = RefreshToken.for_user(user)
                access_token = str(refresh.access_token)
                refresh_token = str(refresh)

                return Response(
                    {
                        "access_token": access_token,
                        "refresh_token": refresh_token,
                        "user": {
                            "id": user.id,
                            "username": user.username,
                            "email": user.email,
                            "role": "admin" if user.is_staff else "user",
                            "is_admin": user.is_staff,
                            "subscription_plan": getattr(
                                user, "subscription_plan", "free"
                            ),
                            "subscription_price": getattr(
                                user, "subscription_price", "0"
                            ),
                        },
                    },
                    status=status.HTTP_200_OK,
                )

            except User.DoesNotExist:
                return Response(
                    {"error": "Google user not found"}, status=status.HTTP_404_NOT_FOUND
                )

        # Handle regular username/password authentication
        if not password:
            return Response(
                {"error": "Password is required for regular login"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        user = authenticate(username=username, password=password)
        if not user:
            return Response(
                {"error": "Invalid credentials"}, status=status.HTTP_401_UNAUTHORIZED
            )

        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        refresh_token = str(refresh)

        return Response(
            {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "user": {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "role": "admin" if user.is_staff else "user",
                    "is_admin": user.is_staff,
                    "subscription_plan": getattr(user, "subscription_plan", "free"),
                    "subscription_price": getattr(user, "subscription_price", "0"),
                },
            },
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        return Response(
            {"error": "Authentication failed"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
