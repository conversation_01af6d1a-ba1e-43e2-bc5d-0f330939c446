# Generated by Django 4.2.16 on 2025-06-07 20:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0002_customuser_company_name_customuser_created_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='customuser',
            name='subscription_plan',
            field=models.CharField(choices=[('freemium', 'Freemium'), ('pro', 'Pro'), ('enterprise', 'Enterprise')], default='freemium', help_text="User's current subscription plan", max_length=20),
        ),
        migrations.AddField(
            model_name='customuser',
            name='subscription_price',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Monthly subscription price in USD', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='customuser',
            name='subscription_start_date',
            field=models.DateTimeField(blank=True, help_text='When the subscription started', null=True),
        ),
    ]
