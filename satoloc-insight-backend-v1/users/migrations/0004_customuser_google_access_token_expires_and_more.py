# Generated by Django 4.2.16 on 2025-06-14 16:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0003_customuser_subscription_plan_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='customuser',
            name='google_access_token_expires',
            field=models.DateTimeField(blank=True, help_text='When the Google access token expires', null=True),
        ),
        migrations.AddField(
            model_name='customuser',
            name='google_email_verified',
            field=models.BooleanField(default=False, help_text='Whether Google email is verified'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='google_id',
            field=models.Char<PERSON>ield(blank=True, db_index=True, help_text='Google user ID from OAuth2', max_length=255, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='customuser',
            name='google_picture',
            field=models.URLField(blank=True, help_text='Google profile picture URL', null=True),
        ),
        migrations.AddField(
            model_name='customuser',
            name='google_refresh_token',
            field=models.TextField(blank=True, help_text='Encrypted Google refresh token for API access', null=True),
        ),
        migrations.AddField(
            model_name='customuser',
            name='is_google_user',
            field=models.BooleanField(default=False, help_text='Whether this user was created via Google OAuth2'),
        ),
    ]
