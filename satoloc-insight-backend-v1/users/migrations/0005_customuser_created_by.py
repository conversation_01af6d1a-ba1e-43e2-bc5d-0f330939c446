# Generated by Django 4.2.16 on 2025-06-25 17:33

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0004_customuser_google_access_token_expires_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="customuser",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                help_text="Sub-admin who created this user",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="created_users",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
