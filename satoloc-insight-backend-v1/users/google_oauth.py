# users/google_oauth.py
import secrets
import string
import base64
import hashlib
from google.oauth2 import id_token
from google.auth.transport import requests
from google_auth_oauthlib.flow import Flow
from django.conf import settings
from django.core.cache import cache
import logging

logger = logging.getLogger(__name__)

class GoogleOAuth2Handler:
    """
    Secure Google OAuth2 handler with PKCE and state validation
    """
    
    def __init__(self):
        self.client_config = settings.GOOGLE_OAUTH2_CONFIG
        self.scopes = settings.GOOGLE_OAUTH2_SCOPES
        
    def generate_state(self, user_id=None):
        """
        Generate secure state parameter with optional user context
        """
        state = secrets.token_urlsafe(settings.GOOGLE_OAUTH2_STATE_LENGTH)
        
        # Store state in cache with 10-minute expiration
        cache_key = f"oauth2_state_{state}"
        cache_data = {
            'user_id': user_id,
            'timestamp': secrets.token_hex(16)
        }
        cache.set(cache_key, cache_data, 600)  # 10 minutes
        
        return state
    
    def validate_state(self, state):
        """
        Validate state parameter and return associated data
        """
        cache_key = f"oauth2_state_{state}"
        cache_data = cache.get(cache_key)
        
        if cache_data:
            # Delete state after use (one-time use)
            cache.delete(cache_key)
            return cache_data
        
        return None
    
    def generate_pkce_challenge(self):
        """
        Generate PKCE code verifier and challenge for extra security
        """
        # Generate code verifier (43-128 characters)
        code_verifier = base64.urlsafe_b64encode(
            secrets.token_bytes(32)
        ).decode('utf-8').rstrip('=')
        
        # Generate code challenge
        code_challenge = base64.urlsafe_b64encode(
            hashlib.sha256(code_verifier.encode('utf-8')).digest()
        ).decode('utf-8').rstrip('=')
        
        return code_verifier, code_challenge
    
    def get_authorization_url(self, redirect_uri, state=None):
        """
        Get Google OAuth2 authorization URL with PKCE
        """
        try:
            # Generate PKCE parameters
            code_verifier, code_challenge = self.generate_pkce_challenge()
            
            # Store code verifier in cache
            if state:
                cache_key = f"oauth2_pkce_{state}"
                cache.set(cache_key, code_verifier, 600)  # 10 minutes
            
            # Create flow
            flow = Flow.from_client_config(
                self.client_config,
                scopes=self.scopes,
                redirect_uri=redirect_uri
            )
            
            # Get authorization URL with PKCE
            authorization_url, _ = flow.authorization_url(
                access_type='offline',
                include_granted_scopes='true',
                state=state,
                code_challenge=code_challenge,
                code_challenge_method='S256',
                prompt='consent'
            )
            
            return authorization_url
            
        except Exception as e:
            logger.error(f"Error generating authorization URL: {str(e)}")
            raise
    
    def exchange_code_for_token(self, code, redirect_uri, state=None):
        """
        Exchange authorization code for access token with PKCE validation
        """
        try:
            # Get code verifier from cache
            code_verifier = None
            if state:
                cache_key = f"oauth2_pkce_{state}"
                code_verifier = cache.get(cache_key)
                if code_verifier:
                    cache.delete(cache_key)  # Delete after use
            
            # Create flow
            flow = Flow.from_client_config(
                self.client_config,
                scopes=self.scopes,
                redirect_uri=redirect_uri
            )
            
            # Exchange code for token with more flexible scope handling
            try:
                if code_verifier:
                    # Use PKCE if available
                    flow.fetch_token(
                        code=code,
                        code_verifier=code_verifier,
                        include_granted_scopes=True  # Allow scope flexibility
                    )
                else:
                    # Fallback without PKCE
                    flow.fetch_token(
                        code=code,
                        include_granted_scopes=True  # Allow scope flexibility
                    )
            except Exception as scope_error:
                # If scope error, try without strict scope validation
                logger.warning(f"Scope validation error, retrying: {str(scope_error)}")
                
                # Create a new flow with minimal scopes for token exchange
                minimal_flow = Flow.from_client_config(
                    self.client_config,
                    scopes=['openid', 'email', 'profile'],  # Minimal required scopes
                    redirect_uri=redirect_uri
                )
                
                if code_verifier:
                    minimal_flow.fetch_token(
                        code=code,
                        code_verifier=code_verifier
                    )
                else:
                    minimal_flow.fetch_token(code=code)
                
                # Use the minimal flow credentials
                flow = minimal_flow
            
            return flow.credentials
            
        except Exception as e:
            logger.error(f"Error exchanging code for token: {str(e)}")
            raise
    
    def verify_id_token(self, id_token_string):
        """
        Verify Google ID token and extract user info
        """
        try:
            # Verify the token
            id_info = id_token.verify_oauth2_token(
                id_token_string,
                requests.Request(),
                self.client_config['web']['client_id']
            )
            
            # Validate token issuer
            if id_info['iss'] not in ['accounts.google.com', 'https://accounts.google.com']:
                raise ValueError('Invalid token issuer')
            
            return id_info
            
        except Exception as e:
            logger.error(f"Error verifying ID token: {str(e)}")
            raise
    
    def get_user_info(self, credentials):
        """
        Extract user information from Google credentials
        """
        try:
            # Get ID token from credentials
            id_token_string = credentials.id_token
            
            if not id_token_string:
                raise ValueError("No ID token found in credentials")
            
            # Verify and extract user info
            user_info = self.verify_id_token(id_token_string)
            
            return {
                'google_id': user_info.get('sub'),
                'email': user_info.get('email'),
                'email_verified': user_info.get('email_verified', False),
                'name': user_info.get('name'),
                'given_name': user_info.get('given_name'),
                'family_name': user_info.get('family_name'),
                'picture': user_info.get('picture'),
                'locale': user_info.get('locale'),
            }
            
        except Exception as e:
            logger.error(f"Error extracting user info: {str(e)}")
            raise

def generate_secure_username(email, google_id):
    """
    Generate a secure username from Google user data
    """
    # Start with email prefix
    username = email.split('@')[0]
    
    # Remove special characters and make lowercase
    username = ''.join(c for c in username if c.isalnum() or c in '_-')
    username = username.lower()
    
    # Add suffix from Google ID to ensure uniqueness
    suffix = google_id[-6:]  # Last 6 characters of Google ID
    username = f"{username}_{suffix}"
    
    return username 