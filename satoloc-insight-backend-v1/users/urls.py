# users/urls.py
from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON>ult<PERSON>out<PERSON>
from rest_framework_simplejwt.views import TokenRefreshView
from .views import (
    RegisterView,
    UserProfileView,
    UpdateProfileView,
    CustomTokenObtainPairView, 
    AdminUserViewSet,
    get_user_data,
    login_view,
)
from .google_views import google_auth_url, google_auth_callback, google_auth_token

router = DefaultRouter()
router.register(r'admin-users', AdminUserViewSet, basename='admin-users')

urlpatterns = [
    # Standard authentication (already under /api/auth/)
    path('token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('login/', login_view, name='auth_login'),
    path('register/', RegisterView.as_view(), name='auth_register'),
    path('profile/', UserProfileView.as_view(), name='user_profile'),
    path('profile/update/', UpdateProfileView.as_view(), name='profile_update'),
    path('user/', get_user_data, name='get_user_data'),
    
    # Google OAuth2 endpoints (already under /api/auth/)
    path('google/url/', google_auth_url, name='google_auth_url'),
    path('google/callback/', google_auth_callback, name='google_auth_callback'),
    path('google/token/', google_auth_token, name='google_auth_token'),
    
    # Admin routes
    path('admin/', include(router.urls)),
]
