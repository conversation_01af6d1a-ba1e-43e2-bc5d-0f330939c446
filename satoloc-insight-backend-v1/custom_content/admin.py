from django.contrib import admin
from .models import CustomContent, ContentEnhancement
import json
from django.utils.safestring import mark_safe

class ContentEnhancementInline(admin.TabularInline):
    model = ContentEnhancement
    extra = 0
    readonly_fields = ('created_at', 'formatted_data')
    fields = ('enhancement_type', 'formatted_data', 'created_at')
    
    def formatted_data(self, obj):
        """Pretty format JSON data for admin viewing"""
        if not obj.data:
            return '-'
        try:
            formatted = json.dumps(obj.data, indent=2)
            return mark_safe(f'<pre style="max-height:200px;overflow-y:auto;">{formatted}</pre>')
        except:
            return str(obj.data)
    formatted_data.short_description = 'Enhancement Data'

@admin.register(CustomContent)
class CustomContentAdmin(admin.ModelAdmin):
    list_display = ('title', 'user', 'language', 'created_at', 'updated_at', 'enhancement_count')
    list_filter = ('created_at', 'language')
    search_fields = ('title', 'prompt', 'content')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [ContentEnhancementInline]
    
    def enhancement_count(self, obj):
        return obj.enhancements.count()
    enhancement_count.short_description = 'Enhancements'
    
    fieldsets = (
        (None, {
            'fields': ('title', 'user', 'language')
        }),
        ('Content', {
            'fields': ('prompt', 'keywords', 'content')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at')
        }),
    )
