"""
S3 utilities for uploading and managing images
"""
import boto3
import base64
import uuid
from datetime import datetime
from django.conf import settings
from botocore.exceptions import ClientError
import logging

logger = logging.getLogger(__name__)


class S3ImageUploader:
    """Utility class for uploading images to S3"""
    
    def __init__(self):
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_S3_REGION_NAME
        )
        self.bucket_name = settings.AWS_STORAGE_BUCKET_NAME
    
    def upload_base64_image(self, base64_data: str, content_type: str = "image/png") -> str:
        """
        Upload a base64 encoded image to S3 and return the public URL
        
        Args:
            base64_data (str): Base64 encoded image data
            content_type (str): MIME type of the image
            
        Returns:
            str: Public URL of the uploaded image
            
        Raises:
            Exception: If upload fails
        """
        try:
            # Decode base64 data
            image_data = base64.b64decode(base64_data)
            
            # Generate unique filename
            now = datetime.now()
            filename = f"custom-content-images/{now.year}/{now.month:02d}/{uuid.uuid4().hex}.png"
            
            # Upload to S3 (bucket is configured for public read access)
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=filename,
                Body=image_data,
                ContentType=content_type
            )
            
            # Generate public URL with region-specific format
            if settings.AWS_S3_REGION_NAME == 'us-east-1':
                public_url = f"https://{self.bucket_name}.s3.amazonaws.com/{filename}"
            else:
                public_url = f"https://{self.bucket_name}.s3.{settings.AWS_S3_REGION_NAME}.amazonaws.com/{filename}"
            
            logger.info(f"Successfully uploaded image to S3: {public_url}")
            return public_url
            
        except ClientError as e:
            logger.error(f"Failed to upload image to S3: {str(e)}")
            raise Exception(f"S3 upload failed: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error during S3 upload: {str(e)}")
            raise Exception(f"Image upload failed: {str(e)}")
    
    def delete_image(self, image_url: str) -> bool:
        """
        Delete an image from S3 using its URL
        
        Args:
            image_url (str): Public URL of the image
            
        Returns:
            bool: True if deletion was successful
        """
        try:
            # Extract key from URL (handle both us-east-1 and regional formats)
            key = None
            if f"{self.bucket_name}.s3.amazonaws.com" in image_url:
                key = image_url.split(f"{self.bucket_name}.s3.amazonaws.com/")[1]
            elif f"{self.bucket_name}.s3.{settings.AWS_S3_REGION_NAME}.amazonaws.com" in image_url:
                key = image_url.split(f"{self.bucket_name}.s3.{settings.AWS_S3_REGION_NAME}.amazonaws.com/")[1]
            
            if key:
                self.s3_client.delete_object(
                    Bucket=self.bucket_name,
                    Key=key
                )
                
                logger.info(f"Successfully deleted image from S3: {key}")
                return True
            else:
                logger.warning(f"Invalid S3 URL format: {image_url}")
                return False
                
        except ClientError as e:
            logger.error(f"Failed to delete image from S3: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during S3 deletion: {str(e)}")
            return False
    
    def test_upload_permission(self) -> bool:
        """
        Test if we can upload to S3 by attempting a small test upload
        
        Returns:
            bool: True if upload is possible
        """
        try:
            # Try to upload a tiny test file
            test_key = f"custom-content-images/test/connection-test-{uuid.uuid4().hex[:8]}.txt"
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=test_key,
                Body=b"test",
                ContentType="text/plain"
            )
            
            # Clean up test file
            try:
                self.s3_client.delete_object(Bucket=self.bucket_name, Key=test_key)
            except:
                pass  # Ignore cleanup errors
            
            logger.info(f"Successfully tested S3 upload to bucket: {self.bucket_name}")
            return True
        except ClientError as e:
            logger.error(f"Cannot upload to S3 bucket {self.bucket_name}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error testing S3 upload: {str(e)}")
            return False


# Convenience function for easy import
def upload_ai_image_to_s3(base64_data: str) -> str:
    """
    Upload an AI-generated image (base64) to S3 and return the public URL
    
    Args:
        base64_data (str): Base64 encoded image data
        
    Returns:
        str: Public URL of the uploaded image
    """
    uploader = S3ImageUploader()
    return uploader.upload_base64_image(base64_data)
