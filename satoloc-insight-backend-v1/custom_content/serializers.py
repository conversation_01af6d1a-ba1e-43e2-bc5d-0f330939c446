from rest_framework import serializers
from .models import CustomContent, ContentEnhancement

class ContentEnhancementSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContentEnhancement
        fields = ['id', 'enhancement_type', 'data', 'created_at']
        read_only_fields = ['created_at']

class CustomContentSerializer(serializers.ModelSerializer):
    enhancements = ContentEnhancementSerializer(many=True, read_only=True)
    
    class Meta:
        model = CustomContent
        fields = ['id', 'title', 'prompt', 'keywords', 'content', 'language', 'created_at', 'updated_at', 'enhancements']
        read_only_fields = ['created_at', 'updated_at', 'enhancements']

    def create(self, validated_data):
        user = self.context['request'].user
        return CustomContent.objects.create(user=user, **validated_data) 