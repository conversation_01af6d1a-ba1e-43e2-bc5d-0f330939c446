from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class CustomContent(models.Model):
    title = models.CharField(max_length=255)
    prompt = models.TextField()
    keywords = models.TextField(blank=True)
    content = models.TextField()
    language = models.CharField(max_length=10, default='en')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='custom_contents')
    wordpress_post_id = models.IntegerField(null=True, blank=True, help_text="WordPress post ID if pushed to WordPress")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.title

class ContentEnhancement(models.Model):
    ENHANCEMENT_TYPES = (
        ('headline', 'Headline'),
        ('image', 'Image Suggestion'),
        ('citation', 'Citation'),
        ('plagiarism', 'Plagiarism Check'),
        ('ai-image', 'AI Generated Image')
    )
    
    custom_content = models.ForeignKey(CustomContent, on_delete=models.CASCADE, related_name='enhancements')
    enhancement_type = models.CharField(max_length=20, choices=ENHANCEMENT_TYPES)
    data = models.JSONField()  # Store the enhancement data as JSON
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
        
    def __str__(self):
        return f"{self.enhancement_type} for {self.custom_content.title}"
