# Generated by Django 4.2.16 on 2025-05-22 18:19

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('custom_content', '0004_remove_customcontent_content_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContentEnhancement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('enhancement_type', models.CharField(choices=[('headline', 'Headline'), ('image', 'Image Suggestion'), ('citation', 'Citation'), ('plagiarism', 'Plagiarism Check'), ('dalle-image', 'AI Generated Image')], max_length=20)),
                ('data', models.JSONField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('custom_content', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enhancements', to='custom_content.customcontent')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
