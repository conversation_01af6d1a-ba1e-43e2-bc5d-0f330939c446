# Generated by Django 4.2.16 on 2025-05-15 16:42

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomContent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('prompt', models.TextField()),
                ('content', models.TextField()),
                ('content_type', models.CharField(choices=[('markdown', 'Markdown'), ('json', 'JSON'), ('html', 'HTML')], default='markdown', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTime<PERSON>ield(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_contents', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
