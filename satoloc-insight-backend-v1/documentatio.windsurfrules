https://docs.crawl4ai.com/
https://docs.crawl4ai.com/core/installation/
https://docs.crawl4ai.com/core/simple-crawling/

https://docs.crawl4ai.com/core/crawler-result/
https://docs.crawl4ai.com/core/browser-crawler-config/
https://docs.crawl4ai.com/core/markdown-generation/

https://docs.crawl4ai.com/core/content-selection/

https://docs.crawl4ai.com/core/link-media/

relevant links:
Setup & Installation
Installation
Docker Deployment
Quick Start
Blog & Changelog
Blog Home
Changelog
Core
Simple Crawling
Crawler Result
Browser & Crawler Config
Markdown Generation
Fit Markdown
Page Interaction
Content Selection
Cache Modes
Local Files & Raw HTML
Link & Media
Advanced
Overview
File Downloading
Lazy Loading
Hooks & Auth
Proxy & Security
Session Management
Multi-URL Crawling
Crawl Dispatcher
Identity Based Crawling
SSL Certificate
Extraction
LLM-Free Strategies
LLM Strategies
Clustering Strategies
Chunking
API Reference
AsyncWebCrawler
arun()
arun_many()
Browser & Crawler Config
CrawlResult
Strategies















