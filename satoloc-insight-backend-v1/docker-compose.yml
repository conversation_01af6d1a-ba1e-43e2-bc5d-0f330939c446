version: "3.8"

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
    command: >
      bash -c "python manage.py migrate &&
               python manage.py collectstatic --noinput &&
               python manage.py createsuperuser --noinput --username=requiemcreatif-admin --email=<EMAIL> || true &&
               gunicorn satoloc_backend.wsgi:application --bind 0.0.0.0:8000"
    volumes:
      - .:/app
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    ports:
      - "8000:8000"
    environment:
      - DJANGO_SECRET_KEY=fucxaj-kymvYk-rakwa4
      - DEBUG=True
      - DB_NAME=satoloc
      - DB_USER=postgres
      - DB_PASSWORD=fucxaj-kymvYk-rakwa4
      - DB_HOST=db
      - DB_PORT=5432
      - CELERY_BROKER_URL=redis://redis:6379/0
      - REDIS_URL=redis://redis:6379/0
      - DJANGO_SUPERUSER_PASSWORD=fucxaj-kymvYk-rakwa4
    depends_on:
      - db
      - redis
    restart: unless-stopped

  db:
    image: postgres:15-alpine
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=fucxaj-kymvYk-rakwa4
      - POSTGRES_DB=satoloc
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

  celery:
    build: .
    command: celery -A satoloc_backend worker -l info
    volumes:
      - .:/app
    environment:
      - DJANGO_SECRET_KEY=fucxaj-kymvYk-rakwa4
      - DEBUG=True
      - DB_NAME=satoloc
      - DB_USER=postgres
      - DB_PASSWORD=fucxaj-kymvYk-rakwa4
      - DB_HOST=db
      - DB_PORT=5432
      - CELERY_BROKER_URL=redis://redis:6379/0
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - web
      - redis
    restart: unless-stopped

  celery-beat:
    build: .
    command: celery -A satoloc_backend beat -l info
    volumes:
      - .:/app
    environment:
      - DJANGO_SECRET_KEY=fucxaj-kymvYk-rakwa4
      - DEBUG=True
      - DB_NAME=satoloc
      - DB_USER=postgres
      - DB_PASSWORD=fucxaj-kymvYk-rakwa4
      - DB_HOST=db
      - DB_PORT=5432
      - CELERY_BROKER_URL=redis://redis:6379/0
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - web
      - redis
    restart: unless-stopped

volumes:
  postgres_data:
  static_volume:
  media_volume:
