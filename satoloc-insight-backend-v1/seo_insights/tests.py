from django.test import TestCase
from django.utils import timezone
from django.contrib.auth import get_user_model
from unittest.mock import patch, MagicMock
from .models import SEOProject, SEOCrawl
from .services.crawl4ai_integration import Crawl4AIService
from .tasks import start_seo_crawl, analyze_seo_data

class TestCrawl4AIIntegration(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create test user
        self.user = get_user_model().objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test project
        self.project = SEOProject.objects.create(
            user=self.user,
            name='Test Project',
            site_url='https://example.com',
            max_pages=10,
            crawl_depth=2,
            enable_competitor_analysis=True,
            enable_regional_analysis=True
        )
        
        self.crawl = SEOCrawl.objects.create(
            project=self.project,
            status='pending',
            start_time=timezone.now()
        )
        
        self.crawl4ai_service = Crawl4AIService()

    @patch('seo_insights.services.crawl4ai_integration.requests.post')
    def test_start_crawl(self, mock_post):
        """Test starting a crawl job"""
        # Mock successful API response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'job_id': 'test_job_123',
            'status': 'started',
            'data': {
                'html_metadata': {
                    'title': 'Example Domain',
                    'meta_description': 'This is an example website',
                    'canonical_url': 'https://example.com'
                },
                'technical_analysis': {
                    'page_speed': 95,
                    'mobile_optimization': 88,
                    'schema_markup': 'valid'
                },
                'content_quality': {
                    'word_count': 500,
                    'readability_score': 75
                }
            }
        }
        mock_post.return_value = mock_response

        # Start crawl
        result = self.crawl4ai_service.start_crawl(
            url=self.project.url,
            config={
                'max_pages': self.project.max_pages,
                'crawl_depth': self.project.crawl_depth,
                'competitor_analysis': self.project.enable_competitor_analysis,
                'regional_performance': self.project.enable_regional_analysis
            }
        )

        # Verify API was called correctly
        self.assertTrue(mock_post.called)
        self.assertEqual(result['status'], 'started')
        
        # Verify crawl data structure
        self.assertIn('data', result)
        self.assertIn('html_metadata', result['data'])
        self.assertIn('technical_analysis', result['data'])
        self.assertIn('content_quality', result['data'])

    @patch('seo_insights.tasks.Crawl4AIService')
    def test_crawl_task(self, mock_service):
        """Test the Celery task for crawling"""
        # Mock service response
        mock_instance = mock_service.return_value
        mock_instance.start_crawl.return_value = {
            'job_id': 'test_job_123',
            'status': 'completed',
            'data': {
                'page_speed': 95,
                'mobile_optimization': 88,
                'schema_markup': 'valid',
                'competitor_analysis': {
                    'ranking': 72,
                    'backlinks': 426
                },
                'regional_performance': {
                    'us': 89,
                    'eu': 85
                }
            }
        }

        # Run task
        result = start_seo_crawl(self.crawl.id)

        # Verify task execution
        self.assertTrue(mock_instance.start_crawl.called)
        self.assertIn('status', result)
        
        # Verify crawl record was updated
        updated_crawl = SEOCrawl.objects.get(id=self.crawl.id)
        self.assertEqual(updated_crawl.status, 'completed')
