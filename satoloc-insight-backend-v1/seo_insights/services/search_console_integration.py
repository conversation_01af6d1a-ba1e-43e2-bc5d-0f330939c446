from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

from django.conf import settings
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from seo_insights.models import (
    SEOProject,
    SEOSearchConsoleData,
    SEOSearchConsoleKeyword,
    SEOSearchConsoleIssue
)


class GoogleSearchConsoleService:
    """Service for interacting with Google Search Console API."""
    
    SCOPES = ['https://www.googleapis.com/auth/webmasters.readonly']
    
    def __init__(self, project: SEOProject):
        self.project = project
        self.credentials = None
        self.service = None
    
    @property
    def is_enabled(self) -> bool:
        """Check if Search Console integration is enabled and verified."""
        return self.project.has_search_console
    
    def enable_integration(self) -> str:
        """Enable Search Console integration and get authorization URL."""
        if not settings.GOOGLE_OAUTH2_CLIENT_CONFIG.get('web', {}).get('client_id'):
            raise Exception("Google OAuth2 client configuration is not set up")
        
        self.project.search_console_enabled = True
        self.project.save()
        
        return self.get_authorization_url()
    
    def disable_integration(self) -> None:
        """Disable Search Console integration."""
        self.project.search_console_enabled = False
        self.project.search_console_verified = False
        self.project.search_console_credentials = None
        self.project.save()
    
    @classmethod
    def create_oauth_flow(cls) -> Flow:
        """Create OAuth2 flow for authorization."""
        return Flow.from_client_config(
            settings.GOOGLE_OAUTH2_CLIENT_CONFIG,
            scopes=cls.SCOPES,
            redirect_uri=settings.GOOGLE_OAUTH2_REDIRECT_URI
        )
    
    def get_authorization_url(self) -> str:
        """Get the URL for authorizing access to a site."""
        flow = self.create_oauth_flow()
        authorization_url, _ = flow.authorization_url(
            access_type='offline',
            include_granted_scopes='true',
            prompt='consent'
        )
        return authorization_url
    
    def process_oauth2_callback(self, code: str) -> Dict[str, Any]:
        """Process OAuth2 callback and store credentials."""
        try:
            flow = self.create_oauth_flow()
            flow.fetch_token(code=code)
            
            credentials = flow.credentials
            self._store_credentials(credentials)
            
            # Verify access to the site
            self.initialize_service()
            self._verify_site_access()
            
            # Mark as verified if we get here
            self.project.search_console_verified = True
            self.project.save()
            
            return {
                'success': True,
                'message': 'Successfully connected to Google Search Console'
            }
            
        except Exception as e:
            self.disable_integration()
            return {
                'success': False,
                'message': f'Failed to connect to Google Search Console: {str(e)}'
            }
    
    def _store_credentials(self, credentials: Credentials) -> None:
        """Store credentials securely for the project."""
        self.project.search_console_credentials = self._encrypt_credentials({
            'token': credentials.token,
            'refresh_token': credentials.refresh_token,
            'token_uri': credentials.token_uri,
            'client_id': credentials.client_id,
            'client_secret': credentials.client_secret
        })
        self.project.save()
    
    def _encrypt_credentials(self, credentials_dict: Dict[str, Any]) -> str:
        """Encrypt credentials before storing."""
        # Implement your encryption method here
        # This is a placeholder - you should use proper encryption
        return str(credentials_dict)
    
    def _decrypt_credentials(self, encrypted_credentials: str) -> Dict[str, Any]:
        """Decrypt stored credentials."""
        # Implement your decryption method here
        # This is a placeholder - you should use proper decryption
        return eval(encrypted_credentials)
    
    def initialize_service(self) -> None:
        """Initialize the Search Console API service with stored credentials."""
        if not self.is_enabled:
            return
            
        try:
            if not self.project.search_console_credentials:
                raise Exception("No credentials stored for this project")
            
            credentials_dict = self._decrypt_credentials(self.project.search_console_credentials)
            self.credentials = Credentials(
                token=credentials_dict.get('token'),
                refresh_token=credentials_dict.get('refresh_token'),
                token_uri=credentials_dict.get('token_uri'),
                client_id=credentials_dict.get('client_id'),
                client_secret=credentials_dict.get('client_secret'),
                scopes=self.SCOPES
            )
            self.service = build('searchconsole', 'v1', credentials=self.credentials)
            
        except Exception as e:
            self.disable_integration()
            raise Exception(f"Failed to initialize Search Console service: {str(e)}")
    
    def _verify_site_access(self) -> None:
        """Verify we have access to the site in Search Console."""
        try:
            sites = self.service.sites().list().execute()
            site_url = self.project.site_url
            
            # Check if site is in permitted sites
            permitted_sites = [s['siteUrl'] for s in sites.get('siteEntry', [])]
            if site_url not in permitted_sites:
                raise Exception(f"No access to {site_url} in Search Console. Available sites: {permitted_sites}")
            
        except HttpError as e:
            raise Exception(f"Error verifying site access: {str(e)}")
    
    def fetch_search_analytics(self, start_date: datetime, end_date: datetime) -> None:
        """Fetch and store search analytics data for the given date range."""
        try:
            site_url = self.project.site_url
            
            # Fetch performance data
            request = {
                'startDate': start_date.strftime('%Y-%m-%d'),
                'endDate': end_date.strftime('%Y-%m-%d'),
                'dimensions': ['date', 'page', 'device'],
                'rowLimit': 25000
            }
            
            response = self.service.searchanalytics().query(siteUrl=site_url, body=request).execute()
            
            for row in response.get('rows', []):
                date = datetime.strptime(row['keys'][0], '%Y-%m-%d').date()
                page_url = row['keys'][1]
                device = row['keys'][2]
                
                # Create or update SEOSearchConsoleData
                data, created = SEOSearchConsoleData.objects.get_or_create(
                    project=self.project,
                    url=page_url,
                    date=date,
                    defaults={
                        'clicks': 0,
                        'impressions': 0,
                        'ctr': 0,
                        'average_position': 0
                    }
                )
                
                # Update device-specific metrics
                if device == 'DESKTOP':
                    data.desktop_clicks = row['clicks']
                    data.desktop_impressions = row['impressions']
                elif device == 'MOBILE':
                    data.mobile_clicks = row['clicks']
                    data.mobile_impressions = row['impressions']
                elif device == 'TABLET':
                    data.tablet_clicks = row['clicks']
                    data.tablet_impressions = row['impressions']
                
                # Update total metrics
                data.clicks = data.desktop_clicks + data.mobile_clicks + data.tablet_clicks
                data.impressions = data.desktop_impressions + data.mobile_impressions + data.tablet_impressions
                data.ctr = row['ctr']
                data.average_position = row['position']
                data.save()
                
        except HttpError as e:
            raise Exception(f"Error fetching search analytics: {str(e)}")
    
    def fetch_keyword_data(self, start_date: datetime, end_date: datetime) -> None:
        """Fetch and store keyword-level data for the given date range."""
        try:
            site_url = self.project.site_url
            
            request = {
                'startDate': start_date.strftime('%Y-%m-%d'),
                'endDate': end_date.strftime('%Y-%m-%d'),
                'dimensions': ['date', 'page', 'query', 'device', 'country', 'language'],
                'rowLimit': 25000
            }
            
            response = self.service.searchanalytics().query(siteUrl=site_url, body=request).execute()
            
            for row in response.get('rows', []):
                date = datetime.strptime(row['keys'][0], '%Y-%m-%d').date()
                page_url = row['keys'][1]
                keyword = row['keys'][2]
                device = row['keys'][3]
                country = row['keys'][4]
                language = row['keys'][5]
                
                # Get or create the parent SEOSearchConsoleData
                search_console_data = SEOSearchConsoleData.objects.get(
                    project=self.project,
                    url=page_url,
                    date=date
                )
                
                # Create or update keyword data with language
                keyword_data, created = SEOSearchConsoleKeyword.objects.get_or_create(
                    search_console_data=search_console_data,
                    keyword=keyword,
                    defaults={
                        'clicks': row['clicks'],
                        'impressions': row['impressions'],
                        'ctr': row['ctr'],
                        'average_position': row['position'],
                        'device': device.lower(),
                        'country': country,
                        'language': language
                    }
                )
                
                if not created:
                    keyword_data.clicks = row['clicks']
                    keyword_data.impressions = row['impressions']
                    keyword_data.ctr = row['ctr']
                    keyword_data.average_position = row['position']
                    keyword_data.language = language
                    keyword_data.save()
                
        except HttpError as e:
            raise Exception(f"Error fetching keyword data: {str(e)}")
    
    def fetch_index_coverage(self) -> None:
        """Fetch and store index coverage data."""
        try:
            site_url = self.project.site_url
            response = self.service.urlInspection().index().list(
                body={
                    'inspectionUrl': site_url,
                    'siteUrl': site_url
                }
            ).execute()
            
            inspection_result = response.get('inspectionResult', {})
            index_status = inspection_result.get('indexStatusResult', {})
            
            # Update indexing status for the URL
            data, created = SEOSearchConsoleData.objects.get_or_create(
                project=self.project,
                url=site_url,
                date=datetime.now().date(),
                defaults={
                    'is_indexed': index_status.get('coverageState') == 'Indexed',
                    'indexing_status': index_status.get('coverageState'),
                    'last_crawled': datetime.fromtimestamp(
                        int(index_status.get('lastCrawlTime', {}).get('seconds', 0))
                    ) if index_status.get('lastCrawlTime') else None
                }
            )
            
            # Store any issues found
            if 'verdict' in inspection_result and inspection_result['verdict'] != 'PASS':
                SEOSearchConsoleIssue.objects.create(
                    project=self.project,
                    url=site_url,
                    issue_type='index_coverage',
                    severity='error' if inspection_result['verdict'] == 'FAIL' else 'warning',
                    discovered=datetime.now(),
                    status='active',
                    details=inspection_result
                )
                
        except HttpError as e:
            raise Exception(f"Error fetching index coverage: {str(e)}")
    
    def sync_search_console_data(self, days: int = 30) -> Dict[str, Any]:
        """Sync all Search Console data for the specified number of days."""
        if not self.is_enabled:
            return {
                'success': False,
                'message': 'Search Console integration is not enabled for this project'
            }
        
        try:
            self.initialize_service()
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            self.fetch_search_analytics(start_date, end_date)
            self.fetch_keyword_data(start_date, end_date)
            self.fetch_index_coverage()
            
            return {
                'success': True,
                'message': f'Successfully synced {days} days of Search Console data'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Error syncing Search Console data: {str(e)}'
            } 
