# seo_insights/services/crawl4ai_integration.py
import logging
import time
from typing import Dict, Optional, Any
import requests
from django.conf import settings
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

class Crawl4AIService:
    """Service for interacting with Crawl4AI API."""

    def __init__(self):
        """Initialize service with base URL from settings."""
        self.base_url = settings.CRAWL4AI_SERVICE_URL.rstrip('/')
        self.api_key = settings.CRAWL4AI_API_KEY
        self.nlp_api_key = settings.GOOGLE_NLP_API_KEY  # Add Google NLP API integration

    def start_crawl(self, url: str, config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Start a new crawl job with comprehensive configuration."""
        if config is None:
            config = {}

        # Extract domain for allowed domains
        domain = urlparse(url).netloc
        
        # Generate a unique job ID
        import uuid
        job_id = str(uuid.uuid4())
        
        # Create payload with required configuration
        payload = {
            "job_id": job_id,
            "start_urls": [url],
            "allowed_domains": [domain],
            "crawl_depth": int(config.get('crawl_depth', 3)),
            "max_pages": int(config.get('max_pages', 100)),
            "selectors": {
                "headings": ["h1", "h2", "h3", "h4", "h5", "h6"],
                "paragraphs": ["p"],
                "lists": ["ul", "ol"],
                "links": ["a"],
                "images": ["img", "picture source"],
                "videos": ["video", "iframe[src*='youtube']", "iframe[src*='vimeo']"],
                "meta": [
                    "meta[name]",
                    "meta[property]",
                    "meta[http-equiv]",
                    "link[rel='alternate']",
                    "link[rel='canonical']",
                    "link[rel='prev']",
                    "link[rel='next']"
                ],
                "schema": ["script[type='application/ld+json']"],
                "social": [
                    "meta[property^='og:']",
                    "meta[name^='twitter:']",
                    "meta[property^='article:']",
                    "meta[property^='product:']"
                ],
                "security": [
                    "meta[http-equiv='Content-Security-Policy']",
                    "link[rel='manifest']",
                    "meta[name='referrer']"
                ],
                "analytics": [
                    "script[src*='google-analytics']",
                    "script[src*='gtag']",
                    "script[src*='facebook']",
                    "script[src*='linkedin']"
                ],
                "javascript": [
                    "script[src]",
                    "script:not([src])",
                    "[onclick]",
                    "[data-*]",
                    ".js-*"
                ]
            }
        }
        
        # Make a non-blocking request to start the crawl
        try:
            response = requests.post(
                f"{self.base_url}/crawl",
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=5  # Short timeout for initial request
            )
            response.raise_for_status()
            return {"job_id": job_id, "status": "started"}
        except requests.exceptions.Timeout:
            # If it times out, assume the crawl started
            return {"job_id": job_id, "status": "started"}
        except Exception as e:
            raise Exception(f"Failed to start crawl: {str(e)}")

    def send_full_config(self, job_id: str, url: str, config: Dict[str, Any]) -> None:
        """Send full configuration for an already started crawl job."""
        domain = urlparse(url).netloc
        payload = {
            "job_id": job_id,
            "start_urls": [url],
            "allowed_domains": [domain],
            "crawl_depth": int(config.get('crawl_depth', 3)),  # Ensure integer
            "max_pages": int(config.get('max_pages', 100)),  # Ensure integer
            "selectors": {
                "headings": ["h1", "h2", "h3", "h4", "h5", "h6"],
                "paragraphs": ["p"],
                "lists": ["ul", "ol"],
                "links": ["a"],
                "images": ["img", "picture source"],
                "meta": ["meta[name]", "meta[property]", "link[rel]"],
                "schema": ["script[type='application/ld+json']"],
            },
            "extract_data": {
                "html_metadata": True,
                "schema_markup": True,
                "mobile_friendliness": True,
                "page_speed": True,
                "content_quality": True,
                "technical_seo": True,
                "competitor_analysis": config.get('competitor_analysis', True),
                "regional_performance": config.get('regional_performance', True)
            },
            "technical_checks": {
                "mobile_optimization": True,
                "schema_validation": True,
                "page_speed_threshold": config.get('page_speed_threshold', 85),
                "check_hreflang": True,
                "check_structured_data": True
            },
            "rate_limit": config.get('rate_limit', {
                "requests_per_second": 2,
                "concurrent_requests": 3,
                "respect_host_limits": True
            }),
            "timeout": {
                "request": 60,
                "total": 3600  # 1 hour total timeout
            },
            "headers": config.get('headers', {
                "User-Agent": "SatoLocInsight SEO Crawler/1.0",
                "Accept-Language": "en-US,en;q=0.9,tr;q=0.8,zh;q=0.7"
            }),
            "selectors": {
                "headings": ["h1", "h2", "h3", "h4", "h5", "h6"],
                "paragraphs": ["p"],
                "lists": ["ul", "ol"],
                "links": ["a"],
                "images": ["img", "picture source"],
                "videos": ["video", "iframe[src*='youtube']", "iframe[src*='vimeo']"],
                "meta": [
                    "meta[name]",
                    "meta[property]",
                    "meta[http-equiv]",
                    "link[rel='alternate']",
                    "link[rel='canonical']",
                    "link[rel='prev']",
                    "link[rel='next']"
                ],
                "schema": ["script[type='application/ld+json']"],
                "social": [
                    "meta[property^='og:']",
                    "meta[name^='twitter:']",
                    "meta[property^='article:']",
                    "meta[property^='product:']"
                ],
                "security": [
                    "meta[http-equiv='Content-Security-Policy']",
                    "link[rel='manifest']",
                    "meta[name='referrer']"
                ],
                "analytics": [
                    "script[src*='google-analytics']",
                    "script[src*='gtag']",
                    "script[src*='facebook']",
                    "script[src*='linkedin']"
                ],
                "javascript": [
                    "script[src]",  # External JS files
                    "script:not([src])",  # Inline JS
                    "[onclick]",  # Click handlers
                    "[data-*]",  # Data attributes
                    ".js-*"  # JS-specific classes
                ]
            },
            "extract": {
                "metadata": True,
                "content": True,
                "links": True,
                "images": True,
                "schema": True,
                "performance": True,
                "ai_seo": True,
                "social_tags": True,
                "hreflang": True,
                "mobile": True,
                "security": True,
                "forms": True,
                "navigation": True,
                "text_content": True,
                "structured_data": True,
                "meta_robots": True,
                "canonical_info": True,
                "pagination_info": True,
                "breadcrumbs": True,
                "author_info": True,
                "published_date": True,
                "modified_date": True,
                "content_type": True,
                "word_count": True,
                "reading_time": True,
                "content_language": True,
                "http_headers": True,
                "response_time": True,
                "status_code": True,
                "redirect_chain": True,
                "resource_timing": True,
                "javascript_content": True,  # Enable JS content extraction
                "dynamic_elements": True,  # Track dynamic elements
                "ajax_requests": True,  # Monitor AJAX calls
                "js_errors": True  # Capture JavaScript errors
            },
            "analyze": {
                "on_page_seo": True,
                "technical_seo": True,
                "content_quality": True,
                "mobile_friendly": True,
                "ai_readiness": True,
                "performance": True,
                "accessibility": True,
                "security": True,
                "structured_data": True,
                "internal_linking": True,
                "external_linking": True,
                "content_structure": True,
                "semantic_analysis": True,
                "competitor_analysis": True,
                "multilingual_seo": True,
                "translation_quality": True,
                "content_similarity": True,
                "keyword_extraction": True,
                "topic_modeling": True,
                "sentiment_analysis": True,
                "readability": True,
                "market_relevance": True,
                "brand_presence": True,
                "user_intent": True,
                "content_gaps": True,
                "conversion_optimization": True,
                "rich_snippets": True,
                "local_seo": True,
                "mobile_usability": True,
                "page_speed": True,
                "core_web_vitals": True,
                "crawl_efficiency": True,
                "indexability": True,
                "duplicate_content": True,
                "thin_content": True,
                "keyword_cannibalization": True,
                "international_targeting": True,
                "social_sharing": True,
                "content_freshness": True,
                "link_quality": True,
                "competitive_gap": True,
                "content_relevance": True,
                "javascript_analysis": True,  # Enable JS analysis
                "serp_analysis": True,  # Enable SERP analysis
                "backlink_analysis": True  # Enable backlink analysis
            },
            "advanced_analysis": {
                "tf_idf": True,
                "topic_clustering": True,
                "content_gap": True,
                "paa_questions": True,
                "competitor_analysis": True,
                "javascript_rendering": True,
                "toxic_backlink_detection": True,
                "anchor_text_analysis": True,
                "serp_preview": True
            },
            "nlp_analysis": {
                "entity_extraction": True,
                "sentiment_analysis": True,
                "user_intent": True,
                "topic_modeling": True
            },
            "sitemap_analysis": {
                "validate_urls": True,
                "check_frequency": True,
                "verify_lastmod": True,
                "compare_indexed": True
            },
            "competitor_tracking": {
                "enabled": True,
                "top_competitors": 5,
                "track_keywords": True,
                "analyze_backlinks": True,
                "content_comparison": True
            },
            "javascript_analysis": {
                "enabled": True,
                "render_timeout": 30,
                "wait_for": ["domContentLoaded", "networkIdle0"],
                "extract_dynamic_content": True,
                "detect_js_frameworks": True,
                "track_ajax_requests": True,
                "analyze_js_errors": True,
                "evaluate_js_dependencies": True
            },
            "serp_analysis": {
                "enabled": True,
                "preview_desktop": True,
                "preview_mobile": True,
                "detect_features": True,
                "analyze_snippets": True,
                "track_position": True,
                "monitor_changes": True,
                "competitor_serps": True
            },
            "backlink_analysis": {
                "enabled": True,
                "detect_toxic": True,
                "analyze_anchor_text": True,
                "evaluate_link_quality": True,
                "check_link_neighborhood": True,
                "track_link_velocity": True,
                "identify_link_patterns": True
            }
        }

        # Add authentication headers
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

        # Make request to start crawl with increased timeout
        try:
            response = requests.post(
                f"{self.base_url}/crawl",
                json=payload,
                headers=headers,
                timeout=60  # Increased timeout for initial request
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.Timeout:
            logger.error("Timeout while starting crawl. The request took too long to complete.")
            raise requests.exceptions.Timeout("Crawl request timed out. Please try again or reduce the scope of the crawl.")
        except requests.exceptions.RequestException as e:
            logger.error("Error starting crawl: %s", str(e))
            if hasattr(e.response, 'text'):
                logger.error("Response content: %s", e.response.text)
            raise requests.exceptions.RequestException(f"Failed to start crawl: {str(e)}")

    def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get status of a crawl job."""
        try:
            response = requests.get(
                f"{self.base_url}/crawl/{job_id}/status",
                timeout=5  # Short timeout for status check
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.Timeout:
            # If server is busy, assume it's still processing
            return {"status": "processing", "progress": None}
        except requests.exceptions.RequestException as e:
            if e.response is not None and e.response.status_code == 404:
                # Job not found
                raise
            # For other errors, assume it's still processing
            return {"status": "processing", "progress": None}

    def get_results(self, job_id: str) -> Dict[str, Any]:
        """Get results of a completed crawl job."""
        response = requests.get(
            f"{self.base_url}/crawl/{job_id}/results",
            timeout=30  # Add timeout
        )
        response.raise_for_status()
        return response.json()

    def get_ai_seo_analysis(self, job_id: str) -> Dict[str, Any]:
        """Get AI SEO analysis for a completed crawl job."""
        response = requests.get(
            f"{self.base_url}/ai_seo/{job_id}",
            timeout=30  # Add timeout
        )
        response.raise_for_status()
        return response.json()

    def wait_for_completion(self, job_id: str, timeout_minutes: int = 60) -> Dict[str, Any]:
        """Wait for a crawl job to complete with extended timeout and better error handling."""
        start_time = time.time()
        timeout_seconds = timeout_minutes * 60
        check_interval = 10  # Check every 10 seconds
        
        while True:
            try:
                # Check if we've exceeded timeout
                if time.time() - start_time > timeout_seconds:
                    raise requests.exceptions.Timeout(f"Crawl job {job_id} did not complete within {timeout_minutes} minutes")
                
                # Get current status with timeout
                try:
                    status = self.get_job_status(job_id)
                except requests.exceptions.Timeout:
                    logger.warning("Timeout while checking status. Retrying...")
                    time.sleep(check_interval)
                    continue
                except requests.exceptions.RequestException as e:
                    logger.error("Error checking status: %s", str(e))
                    time.sleep(check_interval)
                    continue
                
                if status['status'] == 'completed':
                    # Job is done, get full results with timeout
                    try:
                        results = self.get_results(job_id)
                        
                        # Also get AI SEO analysis
                        try:
                            ai_seo_results = self.get_ai_seo_analysis(job_id)
                            results['ai_seo_analysis'] = ai_seo_results
                        except requests.exceptions.RequestException as e:
                            logger.warning("Failed to get AI SEO analysis: %s", str(e))
                        
                        return results
                    except requests.exceptions.Timeout:
                        logger.error("Timeout while fetching results. Retrying...")
                        time.sleep(check_interval)
                        continue
                    except requests.exceptions.RequestException as e:
                        logger.error("Error fetching results: %s", str(e))
                        time.sleep(check_interval)
                        continue
                    
                elif status['status'] == 'failed':
                    error_msg = status.get('error', 'Unknown error')
                    raise requests.exceptions.RequestException(f"Crawl job {job_id} failed: {error_msg}")
                
                # Log progress
                if 'progress' in status:
                    logger.info("Crawl progress: %s%%", status['progress'])
                
                # Wait before checking again
                time.sleep(check_interval)
                
            except Exception as e:
                logger.error("Error in wait_for_completion: %s", str(e))
                raise requests.exceptions.RequestException(f"Error during crawl completion: {str(e)}")

    def analyze_javascript_content(self, url: str, job_id: str) -> Dict[str, Any]:
        """Get JavaScript content analysis for a URL."""
        headers = {'Authorization': f'Bearer {self.api_key}'}
        response = requests.get(
            f"{self.base_url}/analyze/javascript",
            params={'url': url, 'job_id': job_id},
            headers=headers,
            timeout=30  # Add timeout
        )
        response.raise_for_status()
        return response.json()

    def get_serp_analysis(self, url: str, job_id: str) -> Dict[str, Any]:
        """Get SERP analysis for a URL."""
        headers = {'Authorization': f'Bearer {self.api_key}'}
        response = requests.get(
            f"{self.base_url}/analyze/serp",
            params={'url': url, 'job_id': job_id},
            headers=headers,
            timeout=30  # Add timeout
        )
        response.raise_for_status()
        return response.json()

    def analyze_backlinks(self, url: str, job_id: str) -> Dict[str, Any]:
        """Get backlink analysis for a URL."""
        headers = {'Authorization': f'Bearer {self.api_key}'}
        response = requests.get(
            f"{self.base_url}/analyze/backlinks",
            params={'url': url, 'job_id': job_id},
            headers=headers,
            timeout=30  # Add timeout
        )
        response.raise_for_status()
        return response.json()

    def get_competitor_insights(self, url: str, job_id: str) -> Dict[str, Any]:
        """Get competitor insights for a URL."""
        headers = {'Authorization': f'Bearer {self.api_key}'}
        response = requests.get(
            f"{self.base_url}/analyze/competitors",
            params={'url': url, 'job_id': job_id},
            headers=headers,
            timeout=30  # Add timeout
        )
        response.raise_for_status()
        return response.json()
