# seo_insights/services/crawler.py
import requests
import logging
import time
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from requests.exceptions import RequestException
from django.utils import timezone

from seo_insights.models import SEOCrawl, SEOMetadata, SEOLink, SEOImage, SEOIssue

logger = logging.getLogger(__name__)

class SEOCrawler:
    """Service for crawling websites and extracting SEO data."""
    
    def __init__(self, crawl_id, max_pages=100, concurrency=5, respect_robots=True):
        """Initialize the crawler with configuration.
        
        Args:
            crawl_id: ID of the SEOCrawl record
            max_pages: Maximum number of pages to crawl
            concurrency: Number of concurrent requests
            respect_robots: Whether to respect robots.txt directives
        """
        self.crawl = SEOCrawl.objects.get(id=crawl_id)
        self.project = self.crawl.project
        self.start_url = self.project.site_url
        self.domain = urlparse(self.start_url).netloc
        self.max_pages = max_pages
        self.concurrency = concurrency
        self.respect_robots = respect_robots
        
        # Internal state
        self.visited_urls = set()
        self.queue = [self.start_url]
        self.robots_directives = {}
        
        # User agent for requests
        self.headers = {
            'User-Agent': 'SatoLocInsight SEO Crawler/1.0',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
        }
    
    def start(self):
        """Start the crawling process."""
        try:
            # Update crawl status
            self.crawl.status = 'processing'
            self.crawl.save()
            
            # Load robots.txt if needed
            if self.respect_robots:
                self._load_robots_txt()
            
            # Start crawling
            with ThreadPoolExecutor(max_workers=self.concurrency) as executor:
                future_to_url = {}
                
                while self.queue and len(self.visited_urls) < self.max_pages:
                    # Get URLs from queue up to concurrency limit
                    urls_to_process = []
                    while self.queue and len(urls_to_process) < self.concurrency:
                        url = self.queue.pop(0)
                        if url not in self.visited_urls and self._is_allowed_by_robots(url):
                            urls_to_process.append(url)
                            self.visited_urls.add(url)
                    
                    # Submit crawling tasks
                    for url in urls_to_process:
                        future = executor.submit(self._process_url, url)
                        future_to_url[future] = url
                    
                    # Process completed tasks
                    for future in as_completed(future_to_url):
                        url = future_to_url[future]
                        try:
                            new_urls = future.result()
                            # Add new URLs to queue
                            for new_url in new_urls:
                                if new_url not in self.visited_urls and new_url not in self.queue:
                                    self.queue.append(new_url)
                        except Exception as exc:
                            logger.error(f"Error processing {url}: {exc}")
                            self._create_issue(
                                url=url,
                                title=f"Crawling error for {url}",
                                description=str(exc),
                                severity="major",
                                category="technical"
                            )
                    
                    # Clear processed futures
                    future_to_url.clear()
                    
                    # Update crawl progress
                    self.crawl.pages_crawled = len(self.visited_urls)
                    self.crawl.save(update_fields=['pages_crawled'])
            
            # Mark crawl as completed
            self.crawl.status = 'completed'
            self.crawl.end_time = timezone.now()
            self.crawl.save()
            
            logger.info(f"Crawl completed for {self.project.site_url}. Crawled {len(self.visited_urls)} pages.")
            return True
            
        except Exception as e:
            # Mark crawl as failed in case of exception
            self.crawl.status = 'failed'
            self.crawl.error_message = str(e)
            self.crawl.end_time = timezone.now()
            self.crawl.save()
            
            logger.error(f"Crawl failed for {self.project.site_url}: {str(e)}")
            return False
    
    def _process_url(self, url):
        """Process a single URL, extracting SEO data and finding new links.
        
        Args:
            url: URL to process
            
        Returns:
            list: New URLs discovered
        """
        try:
            # Make request
            response = requests.get(url, headers=self.headers, timeout=30, verify=False)
            
            # Check if HTML content
            content_type = response.headers.get('Content-Type', '')
            if 'text/html' not in content_type:
                logger.info(f"Skipping non-HTML content: {url}, type: {content_type}")
                return []
            
            # Parse HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Extract metadata
            metadata = self._extract_metadata(url, soup, response)
            
            # Extract links
            links = self._extract_links(url, soup)
            
            # Extract images
            images = self._extract_images(url, soup)
            
            # Save data to database
            seo_metadata = SEOMetadata.objects.create(
                crawl=self.crawl,
                url=url,
                title=metadata.get('title'),
                meta_description=metadata.get('meta_description'),
                h1=metadata.get('h1'),
                h2=metadata.get('h2'),
                canonical_url=metadata.get('canonical'),
                robots_directives=metadata.get('robots'),
                schema_markup=metadata.get('schema'),
                word_count=metadata.get('word_count', 0),
                status_code=response.status_code,
                content_type=content_type
            )
            
            # Save links
            for link_data in links:
                SEOLink.objects.create(
                    metadata=seo_metadata,
                    url=link_data['url'],
                    text=link_data.get('text'),
                    type=link_data['type'],
                    follow=link_data.get('follow', True)
                )
            
            # Save images
            for img_data in images:
                SEOImage.objects.create(
                    metadata=seo_metadata,
                    url=img_data['url'],
                    alt_text=img_data.get('alt'),
                    title=img_data.get('title'),
                    lazy_loaded=img_data.get('lazy_loaded', False)
                )
            
            # Check for common SEO issues
            self._check_seo_issues(url, metadata, soup)
            
            # Return internal links for further crawling
            return [
                link['url'] for link in links 
                if link['type'] == 'internal' and link['url'] not in self.visited_urls
            ]
            
        except RequestException as e:
            logger.error(f"Request exception for {url}: {e}")
            self._create_issue(
                url=url,
                title=f"Request failed for {url}",
                description=str(e),
                severity="major",
                category="technical"
            )
            return []
        except Exception as e:
            logger.error(f"Error processing {url}: {e}")
            self._create_issue(
                url=url,
                title=f"Processing error for {url}",
                description=str(e),
                severity="major",
                category="technical"
            )
            return []
    
    def _extract_metadata(self, url, soup, response):
        """Extract SEO metadata from a page.
        
        Args:
            url: URL of the page
            soup: BeautifulSoup object
            response: Response object
            
        Returns:
            dict: Metadata
        """
        metadata = {}
        
        # Title
        title_tag = soup.find('title')
        metadata['title'] = title_tag.text.strip() if title_tag else None
        
        # Meta description
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        metadata['meta_description'] = meta_desc['content'] if meta_desc and 'content' in meta_desc.attrs else None
        
        # Headings
        h1_tags = soup.find_all('h1')
        metadata['h1'] = [h.text.strip() for h in h1_tags] if h1_tags else None
        
        h2_tags = soup.find_all('h2')
        metadata['h2'] = [h.text.strip() for h in h2_tags] if h2_tags else None
        
        # Canonical URL
        canonical = soup.find('link', attrs={'rel': 'canonical'})
        metadata['canonical'] = canonical['href'] if canonical and 'href' in canonical.attrs else None
        
        # Robots directives
        robots_meta = soup.find('meta', attrs={'name': 'robots'})
        metadata['robots'] = robots_meta['content'] if robots_meta and 'content' in robots_meta.attrs else None
        
        # Schema markup
        schema_tags = soup.find_all('script', attrs={'type': 'application/ld+json'})
        metadata['schema'] = [tag.string for tag in schema_tags] if schema_tags else None
        
        # Word count
        body_tag = soup.find('body')
        if body_tag:
            text = body_tag.get_text(separator=' ', strip=True)
            metadata['word_count'] = len(text.split())
        else:
            metadata['word_count'] = 0
        
        return metadata
    
    def _extract_links(self, base_url, soup):
        """Extract links from a page.
        
        Args:
            base_url: Base URL for resolving relative links
            soup: BeautifulSoup object
            
        Returns:
            list: Link data
        """
        links = []
        base_domain = urlparse(base_url).netloc
        
        for a_tag in soup.find_all('a', href=True):
            href = a_tag['href']
            absolute_url = urljoin(base_url, href)
            
            # Skip fragment-only URLs and javascript links
            if href.startswith('#') or href.startswith('javascript:'):
                continue
                
            # Determine if internal or external
            url_domain = urlparse(absolute_url).netloc
            link_type = 'internal' if url_domain == base_domain else 'external'
            
            # Check if nofollow
            rel = a_tag.get('rel', [])
            follow = 'nofollow' not in rel if isinstance(rel, list) else 'nofollow' not in rel.split()
            
            links.append({
                'url': absolute_url,
                'text': a_tag.get_text(strip=True),
                'type': link_type,
                'follow': follow
            })
        
        return links
    
    def _extract_images(self, base_url, soup):
        """Extract images from a page.
        
        Args:
            base_url: Base URL for resolving relative paths
            soup: BeautifulSoup object
            
        Returns:
            list: Image data
        """
        images = []
        
        for img_tag in soup.find_all('img', src=True):
            src = img_tag['src']
            # Skip data URIs
            if src.startswith('data:'):
                continue
                
            absolute_url = urljoin(base_url, src)
            
            # Check if lazy loaded
            lazy_loaded = (
                'loading' in img_tag.attrs and img_tag['loading'] == 'lazy'
            ) or any(attr for attr in img_tag.attrs if 'lazy' in attr or attr.startswith('data-'))
            
            images.append({
                'url': absolute_url,
                'alt': img_tag.get('alt', ''),
                'title': img_tag.get('title', ''),
                'lazy_loaded': lazy_loaded
            })
        
        return images
    
    def _check_seo_issues(self, url, metadata, soup):
        """Check for common SEO issues.
        
        Args:
            url: URL of the page
            metadata: Extracted metadata
            soup: BeautifulSoup object
        """
        # Check title length
        if not metadata.get('title'):
            self._create_issue(
                url=url,
                title="Missing page title",
                description=f"The page at {url} is missing a title tag.",
                severity="critical",
                category="metadata"
            )
        elif len(metadata.get('title', '')) < 10:
            self._create_issue(
                url=url,
                title="Title too short",
                description=f"The title of page {url} is too short ({len(metadata['title'])} chars).",
                severity="major",
                category="metadata"
            )
        elif len(metadata.get('title', '')) > 70:
            self._create_issue(
                url=url,
                title="Title too long",
                description=f"The title of page {url} is too long ({len(metadata['title'])} chars).",
                severity="minor",
                category="metadata"
            )
        
        # Check meta description
        if not metadata.get('meta_description'):
            self._create_issue(
                url=url,
                title="Missing meta description",
                description=f"The page at {url} is missing a meta description.",
                severity="major",
                category="metadata"
            )
        elif len(metadata.get('meta_description', '')) < 50:
            self._create_issue(
                url=url,
                title="Meta description too short",
                description=f"The meta description of page {url} is too short ({len(metadata['meta_description'])} chars).",
                severity="minor",
                category="metadata"
            )
        elif len(metadata.get('meta_description', '')) > 160:
            self._create_issue(
                url=url,
                title="Meta description too long",
                description=f"The meta description of page {url} is too long ({len(metadata['meta_description'])} chars).",
                severity="minor",
                category="metadata"
            )
        
        # Check H1 tags
        if not metadata.get('h1'):
            self._create_issue(
                url=url,
                title="Missing H1 tag",
                description=f"The page at {url} doesn't have an H1 heading.",
                severity="major",
                category="content"
            )
        elif len(metadata.get('h1', [])) > 1:
            self._create_issue(
                url=url,
                title="Multiple H1 tags",
                description=f"The page at {url} has {len(metadata['h1'])} H1 headings. Consider using only one H1 tag per page.",
                severity="minor",
                category="content"
            )
        
        # Check for images without alt text
        img_tags = soup.find_all('img')
        img_without_alt = [img for img in img_tags if 'alt' not in img.attrs or not img['alt']]
        if img_without_alt:
            self._create_issue(
                url=url,
                title="Images missing alt text",
                description=f"The page at {url} has {len(img_without_alt)} images without alt text.",
                severity="major",
                category="images"
            )
        
        # Check for structured data
        if not metadata.get('schema'):
            self._create_issue(
                url=url,
                title="Missing structured data",
                description=f"The page at {url} doesn't have any structured data (schema.org).",
                severity="minor",
                category="structured_data"
            )
        
        # Check for canonical URL
        if not metadata.get('canonical'):
            self._create_issue(
                url=url,
                title="Missing canonical tag",
                description=f"The page at {url} doesn't have a canonical URL specified.",
                severity="minor",
                category="metadata"
            )
        
        # Check for mobile viewport
        viewport = soup.find('meta', attrs={'name': 'viewport'})
        if not viewport:
            self._create_issue(
                url=url,
                title="Missing viewport meta tag",
                description=f"The page at {url} doesn't have a viewport meta tag, which is essential for mobile responsiveness.",
                severity="major",
                category="mobile"
            )
        
        # Check for thin content
        if metadata.get('word_count', 0) < 300:
            self._create_issue(
                url=url,
                title="Thin content",
                description=f"The page at {url} has only {metadata.get('word_count')} words, which might be considered thin content.",
                severity="major",
                category="content"
            )
    
    def _create_issue(self, url, title, description, severity, category, recommendation=None):
        """Create an SEO issue in the database.
        
        Args:
            url: URL where the issue was found
            title: Issue title
            description: Issue description
            severity: Issue severity (critical, major, minor, info)
            category: Issue category
            recommendation: Optional recommendation for fixing the issue
        """
        SEOIssue.objects.create(
            crawl=self.crawl,
            url=url,
            title=title,
            description=description,
            severity=severity,
            category=category,
            recommendation=recommendation
        )
    
    def _load_robots_txt(self):
        """Load and parse robots.txt for the site."""
        try:
            robots_url = urljoin(self.start_url, '/robots.txt')
            response = requests.get(robots_url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                lines = response.text.splitlines()
                user_agent = '*'  # Default
                
                for line in lines:
                    line = line.strip()
                    
                    if not line or line.startswith('#'):
                        continue
                    
                    parts = line.split(':', 1)
                    if len(parts) != 2:
                        continue
                    
                    directive, value = parts
                    directive = directive.strip().lower()
                    value = value.strip()
                    
                    if directive == 'user-agent':
                        user_agent = value
                        if user_agent not in self.robots_directives:
                            self.robots_directives[user_agent] = {'disallow': [], 'allow': []}
                    elif user_agent in self.robots_directives:
                        if directive == 'disallow' and value:
                            self.robots_directives[user_agent]['disallow'].append(value)
                        elif directive == 'allow' and value:
                            self.robots_directives[user_agent]['allow'].append(value)
            
            logger.info(f"Loaded robots.txt from {robots_url}")
        except Exception as e:
            logger.warning(f"Error loading robots.txt: {e}")
    
    def _is_allowed_by_robots(self, url):
        """Check if a URL is allowed by robots.txt.
        
        Args:
            url: URL to check
            
        Returns:
            bool: True if URL is allowed, False otherwise
        """
        if not self.respect_robots or not self.robots_directives:
            return True
        
        path = urlparse(url).path
        
        # Check rules for our user agent, then for '*'
        for user_agent in ['SatoLocInsight SEO Crawler/1.0', '*']:
            if user_agent in self.robots_directives:
                rules = self.robots_directives[user_agent]
                
                # First check if the URL is explicitly allowed
                for allow_path in rules.get('allow', []):
                    if path.startswith(allow_path):
                        return True
                
                # Then check if it's disallowed
                for disallow_path in rules.get('disallow', []):
                    if path.startswith(disallow_path):
                        return False
        
        # If no matching rule found, it's allowed
        return True