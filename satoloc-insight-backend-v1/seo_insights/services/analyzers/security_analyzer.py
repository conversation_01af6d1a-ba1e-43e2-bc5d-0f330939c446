import logging
from typing import Dict
import requests
from urllib.parse import urlparse
from .base_analyzer import BaseAnalyzer

logger = logging.getLogger(__name__)

class SecurityAnalyzer(BaseAnalyzer):
    """Security analysis functionality."""

    def _analyze_ssl_implementation(self) -> None:
        """Analyze SSL/TLS implementation."""
        try:
            metadata_entries = self._get_metadata_entries()
            
            for metadata in metadata_entries:
                parsed_url = urlparse(metadata.url)
                if parsed_url.scheme != 'https':
                    self._create_issue(
                        url=metadata.url,
                        title="Non-HTTPS URL",
                        description="Page is not served over HTTPS",
                        severity="critical",
                        category="security",
                        recommendation="Implement HTTPS for all pages"
                    )
                    self._update_technical_score(-10)
                
                # Check SSL certificate (if HTTPS)
                if parsed_url.scheme == 'https':
                    try:
                        response = requests.get(metadata.url, verify=True)
                    except requests.exceptions.SSLError:
                        self._create_issue(
                            url=metadata.url,
                            title="SSL certificate error",
                            description="Invalid or expired SSL certificate",
                            severity="critical",
                            category="security",
                            recommendation="Fix SSL certificate configuration"
                        )
                        self._update_technical_score(-15)
            
            return True
            
        except Exception as e:
            logger.error(f"Error analyzing SSL implementation: {str(e)}")
            return False

    def _analyze_security_headers(self) -> None:
        """Analyze security headers implementation."""
        try:
            metadata_entries = self._get_metadata_entries()
            
            required_headers = {
                'Strict-Transport-Security': 'HSTS not implemented',
                'X-Content-Type-Options': 'Missing protection against MIME type sniffing',
                'X-Frame-Options': 'Missing clickjacking protection',
                'Content-Security-Policy': 'No content security policy defined',
                'X-XSS-Protection': 'Missing XSS protection'
            }
            
            for metadata in metadata_entries:
                headers = metadata.headers or {}
                
                for header, issue in required_headers.items():
                    if header not in headers:
                        self._create_issue(
                            url=metadata.url,
                            title=f"Missing {header}",
                            description=issue,
                            severity="major",
                            category="security",
                            recommendation=f"Implement {header} header"
                        )
                        self._update_technical_score(-5)
            
            return True
            
        except Exception as e:
            logger.error(f"Error analyzing security headers: {str(e)}")
            return False

    def _analyze_mixed_content(self) -> None:
        """Analyze mixed content issues."""
        try:
            metadata_entries = self._get_metadata_entries()
            
            for metadata in metadata_entries:
                if metadata.url.startswith('https://'):
                    # Check for mixed content in links
                    insecure_links = self._get_links().filter(
                        metadata=metadata,
                        url__startswith='http://'
                    )
                    
                    if insecure_links.exists():
                        self._create_issue(
                            url=metadata.url,
                            title="Mixed content detected",
                            description=f"Found {insecure_links.count()} insecure (HTTP) resources on HTTPS page",
                            severity="major",
                            category="security",
                            recommendation="Update all resource URLs to use HTTPS"
                        )
                        self._update_technical_score(-5)
                    
                    # Check for mixed content in images
                    insecure_images = metadata.images.filter(url__startswith='http://')
                    if insecure_images.exists():
                        self._create_issue(
                            url=metadata.url,
                            title="Mixed content in images",
                            description=f"Found {insecure_images.count()} insecure (HTTP) images on HTTPS page",
                            severity="major",
                            category="security",
                            recommendation="Update all image URLs to use HTTPS"
                        )
                        self._update_technical_score(-5)
            
            return True
            
        except Exception as e:
            logger.error(f"Error analyzing mixed content: {str(e)}")
            return False 