# seo_insights/services/analyzer.py
import logging
import re
import json
from collections import Counter
from bs4 import BeautifulSoup
from textblob import TextBlob
from django.db import models
from django.db.models import Count, Avg, Q, F
from functools import reduce
from operator import or_
from typing import Dict, List, Any
from django.utils import timezone
from urllib.parse import urlparse
import math

from seo_insights.models import (
    SEOCrawl, SEOMetadata, SEOContentAnalysis, 
    SEOPerformance, SEOIssue, SEOReport, SEOLink, SEOImage,
    SEOSearchConsoleData, SEOSearchConsoleKeyword, SEOBacklinkAnalysis,
    SEOCompetitorAnalysis, SEORanking, SEOSitemapAnalysis
)

from .analyzers.local_analyzer import LocalSEOAnalyzer
from .analyzers.ux_analyzer import UXAnalyzer
from .analyzers.security_analyzer import SecurityAnalyzer
from .analyzers.schema_analyzer import SchemaAnalyzer
from .analyzers.ecommerce_analyzer import EcommerceAnalyzer
from .analyzers.ai_seo_analyzer import AISEOAnalyzer
from .analyzers.social_analyzer import SocialAnalyzer
from .analyzers.competitive_analyzer import CompetitiveAnalyzer
from .analyzers.base_analyzer import BaseAnalyzer

logger = logging.getLogger(__name__)


class SEOAnalyzer(BaseAnalyzer):
    """Main SEO analyzer that coordinates all specialized analyzers."""
    
    def __init__(self, crawl_id: int):
        """Initialize SEO analyzer with crawl ID."""
        super().__init__(crawl_id)
        self.local_analyzer = LocalSEOAnalyzer(crawl_id)
        self.ux_analyzer = UXAnalyzer(crawl_id)
        self.security_analyzer = SecurityAnalyzer(crawl_id)
        self.schema_analyzer = SchemaAnalyzer(crawl_id)
        self.ecommerce_analyzer = EcommerceAnalyzer(crawl_id)
        self.ai_seo_analyzer = AISEOAnalyzer(crawl_id)
        self.social_analyzer = SocialAnalyzer(crawl_id)
        self.competitive_analyzer = CompetitiveAnalyzer(crawl_id)
    
    def analyze(self):
        """Perform comprehensive SEO analysis."""
        try:
            logger.info(
                "Starting comprehensive SEO analysis for crawl %s",
                self.crawl.id)
            
            # Core analysis
            self._analyze_content()
            self._analyze_technical_seo()
            self._analyze_ai_seo_readiness()
            
            # Content analysis
            self._analyze_content_quality()
            self._analyze_content_structure()
            self._analyze_semantic_relevance()
            self._analyze_keyword_optimization()
            self._analyze_readability_scores()
            self._analyze_content_freshness()
            self._analyze_thin_content()
            self._analyze_duplicate_content()
            
            # Technical analysis
            self._analyze_site_structure()
            self._analyze_internal_linking()
            self._analyze_external_linking()
            self._analyze_broken_links()
            self._analyze_redirect_chains()
            self._analyze_url_structure()
            self._analyze_robots_meta()
            self._analyze_xml_sitemaps()
            self._analyze_canonical_tags()
            self._analyze_pagination()
            
            # Performance analysis
            self._analyze_page_speed()
            self._analyze_core_web_vitals()
            self._analyze_mobile_usability()
            self._analyze_resource_optimization()
            
            # Multilingual analysis
            self._analyze_hreflang_implementation()
            self._analyze_language_targeting()
            self._analyze_translation_quality()
            self._analyze_market_relevance()
            
            # Schema and structured data
            self._analyze_schema_markup()
            self._analyze_rich_snippets()
            self._analyze_breadcrumbs()
            
            # User experience
            self._analyze_mobile_friendliness()
            self._analyze_accessibility()
            self._analyze_navigation_structure()
            self._analyze_conversion_elements()
            
            # Security and technical
            self._analyze_ssl_implementation()
            self._analyze_security_headers()
            self._analyze_mixed_content()
            
            # Social and branding
            self._analyze_social_integration()
            self._analyze_brand_presence()
            self._analyze_social_sharing()
            
            # Competitive analysis
            self._analyze_competitive_gap()
            self._analyze_market_positioning()
            self._analyze_content_gaps()
            
            # AI SEO specific
            self._analyze_ai_content_quality()
            self._analyze_semantic_optimization()
            self._analyze_entity_recognition()
            self._analyze_topic_modeling()
            self._analyze_user_intent_match()
            
            # Local SEO
            self._analyze_local_seo_elements()
            self._analyze_geo_targeting()
            
            # E-commerce specific (if applicable)
            self._analyze_product_schema()
            self._analyze_shopping_optimization()
            
            # Generate final report
            self._generate_comprehensive_report()
            
            logger.info(
                "Completed comprehensive SEO analysis for crawl %s",
                self.crawl.id)
            return True
            
        except Exception as e:
            logger.error("Error during SEO analysis: %s", str(e))
            return False
    
    def _analyze_content(self):
        """Analyze content for each crawled page."""
        metadata_entries = SEOMetadata.objects.filter(crawl=self.crawl)
        
        for metadata in metadata_entries:
            try:
                # Get page content
                # We could use a headless browser or API for more accurate analysis,
                # but we'll use what we already have for this example
                title = metadata.title or ""
                meta_desc = metadata.meta_description or ""
                h1_content = "".join(
                    metadata.h1) if isinstance(
                    metadata.h1,
                    list) else (
                    metadata.h1 or "")
                h2_content = "".join(
                    metadata.h2) if isinstance(
                    metadata.h2,
                    list) else (
                    metadata.h2 or "")
                
                # Combine all content for analysis
                all_content = f"{title} {meta_desc} {h1_content} {h2_content}"
                
                # Calculate keyword density
                keyword_density = self._calculate_keyword_density(all_content)
                
                # Calculate readability
                readability_score = self._calculate_readability(all_content)
                
                # Analyze sentiment
                sentiment_score = self._analyze_sentiment(all_content)
                
                # Create or update content analysis
                SEOContentAnalysis.objects.update_or_create(
                    metadata=metadata,
                    defaults={
                        'keyword_density': keyword_density,
                        'readability_score': readability_score,
                        'sentiment_score': sentiment_score,
                        'topical_relevance': 0.0,  # Would require more complex analysis
                    }
                )
                
                # Check for common content issues
                self._check_content_issues(metadata)
                
            except Exception as e:
                logger.error(
                    "Error analyzing content for %s: %s",
                    metadata.url,
                    str(e))
                continue
    
    def _analyze_technical_seo(self):
        """Analyze technical SEO aspects."""
        # Identify broken links
        self._identify_broken_links()

        # Analyze redirect chains
        self._analyze_redirect_chains()
        
        # Check for duplicate content
        self._check_duplicate_content()
        
        # Check for orphaned pages
        self._check_orphaned_pages()
        
        # Analyze site structure and depth
        self._analyze_site_structure()
        
        # Check canonical tags
        self._check_canonical_tags()
        
        # Check hreflang tags for international sites
        self._check_hreflang_tags()
        
        # Analyze schema markup
        self._analyze_schema_markup()
    
    def _analyze_ai_seo_readiness(self):
        """Analyze AI SEO readiness based on the crawl data."""
        metadata_entries = SEOMetadata.objects.filter(crawl=self.crawl)
        
        for metadata in metadata_entries:
            try:
                # We'll focus on key factors for AI readiness:
                # 1. FAQ Schema presence
                # 2. Speakable schema
                # 3. Proper heading structure
                # 4. Clear content organization
                # 5. Markdown-like structure
                
                ai_readiness_points = 0
                total_possible_points = 5
                
                # Check schema markup for FAQ and Speakable
                if metadata.schema_markup:
                    schemas = []
                    for schema_text in metadata.schema_markup:
                        try:
                            if isinstance(schema_text, str):
                                schema_json = json.loads(schema_text)
                                schemas.append(schema_json)
                        except json.JSONDecodeError:
                            continue
                    
                    # Check for FAQPage schema
                    if any(
                            '@type' in schema and schema['@type'] == 'FAQPage' for schema in schemas):
                        ai_readiness_points += 1
                    
                    # Check for speakable property
                    if any('speakable' in schema for schema in schemas):
                        ai_readiness_points += 1
                
                # Check heading structure (H2, H3)
                if metadata.h2 and (
                    isinstance(
                        metadata.h2,
                        list) and len(
                        metadata.h2) > 0):
                    ai_readiness_points += 1
                
                # Check if content analysis exists
                content_analysis = SEOContentAnalysis.objects.filter(
                    metadata=metadata).first()
                if content_analysis:
                    # Good readability improves AI indexing
                    if content_analysis.readability_score and content_analysis.readability_score > 60:
                        ai_readiness_points += 1
                    
                    # Content with balanced keywords is better for AI
                    # understanding
                    if content_analysis.keyword_density:
                        top_keywords = sorted(
                            content_analysis.keyword_density.items(), 
                            key=lambda x: x[1], 
                            reverse=True
                        )[:5]
                        if len(
                                top_keywords) > 0 and top_keywords[0][1] < 5:  # Not keyword stuffed
                            ai_readiness_points += 1
                
                # Calculate AI readiness score as percentage
                ai_readiness_score = (
                    ai_readiness_points / total_possible_points) * 100
                
                # Update content analysis with AI readiness score
                if content_analysis:
                    content_analysis.ai_readiness_score = ai_readiness_score
                    content_analysis.save(update_fields=['ai_readiness_score'])
                else:
                    SEOContentAnalysis.objects.create(
                        metadata=metadata,
                        ai_readiness_score=ai_readiness_score
                    )
                
                # Create issues for improving AI readiness if score is low
                if ai_readiness_score < 60:
                    self._create_ai_seo_issue(metadata)
                
            except Exception as e:
                logger.error(
                    "Error analyzing AI SEO readiness for %s: %s",
                    metadata.url,
                    str(e))
                continue
    
    def _generate_report(self):
        """Generate comprehensive SEO report."""
        try:
            # Calculate scores
            page_speed_score = self._calculate_page_speed_score()
            mobile_optimization_score = self._calculate_mobile_optimization_score()
            local_schema_status = self._check_local_schema_implementation()
            
            # Calculate metrics
            organic_keywords = self._calculate_organic_keywords()
            competitor_keywords = self._calculate_competitor_keywords()
            avg_position = self._calculate_average_position()
            visibility_percentage = self._calculate_visibility_percentage()
            
            # Calculate other metrics
            domain_rating = self._calculate_domain_rating()
            organic_traffic = self._calculate_organic_traffic()
            search_rankings = self._calculate_search_rankings()
            search_terms = self._count_ranking_search_terms()
            site_links = self._count_total_links()
            content_score = self._calculate_content_score()
            
            # Create or update report with all metrics
            report, created = SEOReport.objects.get_or_create(
                crawl=self.crawl,
                defaults={
                    'domain_rating': domain_rating,
                    'organic_traffic': organic_traffic,
                    'search_rankings': search_rankings,
                    'search_terms': search_terms,
                    'site_links': site_links,
                    'content_score': content_score,
                    'metadata_score': self._calculate_metadata_score(),
                    'technical_score': self._calculate_technical_score(),
                    'mobile_score': mobile_optimization_score,
                    'overall_score': self._calculate_overall_score(),
                    'ai_seo_score': self._calculate_ai_seo_score(),
                    'overview': {
                        'organic_keywords': organic_keywords,
                        'competitor_keywords': competitor_keywords,
                        'average_position': avg_position,
                        'visibility_percentage': visibility_percentage,
                        'page_speed_score': page_speed_score,
                        'mobile_optimization_score': mobile_optimization_score,
                        'local_schema_status': local_schema_status,
                        'domain_rating': domain_rating,
                        'organic_traffic': organic_traffic,
                        'search_rankings': search_rankings,
                        'search_terms': search_terms,
                        'site_links': site_links,
                        'content_score': content_score
                    }
                }
            )
            return True
        except Exception as e:
            logger.error("Error generating report: %s", str(e))
            return False

    def _calculate_domain_rating(self) -> float:
        """Calculate domain rating based on various factors."""
        try:
            # Factors affecting domain rating:
            # 1. Backlink quality and quantity
            # 2. Domain age and authority
            # 3. Technical SEO health
            # 4. Content quality
            # 5. User engagement metrics
            
            technical_score = self._calculate_technical_score() or 0
            content_score = self._calculate_content_score() or 0
            backlink_score = self._calculate_backlink_score() or 0
            
            # Weighted average of factors
            domain_rating = (
                technical_score * 0.3 +
                content_score * 0.4 +
                backlink_score * 0.3
            )
            
            return round(domain_rating, 2)
        except Exception as e:
            logger.error("Error calculating domain rating: %s", str(e))
            return 0.0

    def _calculate_organic_keywords(self) -> int:
        """Calculate number of organic keywords the site ranks for."""
        try:
            return (
                SEOSearchConsoleKeyword.objects.filter(
                    search_console_data__project=self.crawl.project
                ).values('keyword').distinct().count()
            )
        except Exception as e:
            logger.error("Error calculating organic keywords: %s", str(e))
            return 0

    def _calculate_competitor_keywords(self) -> int:
        """Calculate number of competitor keywords."""
        try:
            competitor_analyses = SEOCompetitorAnalysis.objects.filter(
                metadata__crawl=self.crawl
            )
            
            if not competitor_analyses.exists():
                return 0
            
            total_competitor_keywords = 0
            for analysis in competitor_analyses:
                if analysis.keyword_overlap:
                    # Get unique keywords from competitor
                    competitor_keywords = set()
                    for keyword_data in analysis.keyword_overlap:
                        if isinstance(
                                keyword_data,
                                dict) and 'keyword' in keyword_data:
                            competitor_keywords.add(keyword_data['keyword'])
                    total_competitor_keywords += len(competitor_keywords)
            
            return total_competitor_keywords
        except Exception as e:
            logger.error("Error calculating competitor keywords: %s", str(e))
            return 0

    def _calculate_average_position(self) -> float:
        """Calculate average search position.
        
        Returns:
            float: Average position in search results
        """
        try:
            analyses = SEOPerformance.objects.filter(
                metadata__crawl=self.crawl,
                average_position__isnull=False
            )
            
            if not analyses.exists():
                return 0.0
                
            avg_position = analyses.aggregate(
                avg_pos=Avg('average_position')
            )['avg_pos'] or 0
            
            return round(avg_position, 2)
            
        except Exception as e:
            logger.error("Error calculating average position: %s", str(e))
            return 0.0

    def _calculate_visibility_percentage(self) -> float:
        """Calculate search visibility percentage.
        
        Returns:
            float: Visibility percentage (0-100)
        """
        try:
            total_keywords = 0
            weighted_visibility = 0
            
            keywords = SEOSearchConsoleKeyword.objects.filter(
                search_console_data__project=self.crawl.project
            ).values('average_position', 'impressions')
            
            for keyword in keywords:
                position = keyword['average_position']
                impressions = keyword['impressions']
                total_keywords += 1
                
                # Weight visibility based on position
                if position <= 3:
                    weighted_visibility += 1.0 * impressions
                elif position <= 5:
                    weighted_visibility += 0.7 * impressions
                elif position <= 10:
                    weighted_visibility += 0.4 * impressions
                elif position <= 20:
                    weighted_visibility += 0.1 * impressions
                    
            if total_keywords > 0:
                max_possible_visibility = total_keywords * \
                    max(keyword['impressions'] for keyword in keywords) if keywords else 0
                visibility_percentage = (
                    weighted_visibility /
                    max_possible_visibility *
                    100) if max_possible_visibility > 0 else 0
                return round(visibility_percentage, 2)
            return 0.0
        except Exception as e:
            logger.error("Error calculating visibility percentage: %s", str(e))
            return 0.0

    def _calculate_organic_traffic(self) -> int:
        """Calculate estimated monthly organic traffic."""
        try:
            # Get traffic data from Search Console if available
            if hasattr(self.crawl.project, 'search_console_data'):
                monthly_traffic = (
                    SEOSearchConsoleData.objects
                    .filter(project=self.crawl.project)
                    .aggregate(total_clicks=models.Sum('clicks'))
                    .get('total_clicks', 0)
                )
                return monthly_traffic or 0
            return 0
        except Exception as e:
            logger.error("Error calculating organic traffic: %s", str(e))
            return 0

    def _calculate_search_rankings(self) -> float:
        """Calculate percentage of keywords ranking in top positions."""
        try:
            total_keywords = 0
            ranking_keywords = 0
            
            keyword_data = (
                SEOSearchConsoleKeyword.objects
                .filter(search_console_data__project=self.crawl.project)
                .values('average_position')
            )
            
            for keyword in keyword_data:
                total_keywords += 1
                if keyword['average_position'] <= 10:  # Top 10 positions
                    ranking_keywords += 1
            
            if total_keywords > 0:
                return round((ranking_keywords / total_keywords) * 100, 2)
            return 0.0
        except Exception as e:
            logger.error("Error calculating search rankings: %s", str(e))
            return 0.0

    def _count_ranking_search_terms(self) -> int:
        """Count number of search terms the site ranks for."""
        try:
            return (
                SEOSearchConsoleKeyword.objects
                .filter(search_console_data__project=self.crawl.project)
                .values('keyword')
                .distinct()
                .count()
            )
        except Exception as e:
            logger.error("Error counting ranking search terms: %s", str(e))
            return 0

    def _count_total_links(self) -> int:
        """Count total number of valid links."""
        try:
            return SEOLink.objects.filter(
                metadata__crawl=self.crawl
            ).exclude(
                status_code__gte=400  # Exclude broken links
            ).count()
        except Exception as e:
            logger.error("Error counting total links: %s", str(e))
            return 0

    def _calculate_backlink_score(self) -> float:
        """Calculate backlink quality score."""
        try:
            backlinks = SEOBacklinkAnalysis.objects.filter(
                metadata__crawl=self.crawl
            )
            
            if not backlinks.exists():
                return 0.0
            
            total_score = 0
            total_backlinks = 0
            
            for backlink in backlinks:
                if backlink.domain_rating and backlink.trust_flow:
                    score = (
                        backlink.domain_rating *
                        0.6 +
                        backlink.trust_flow *
                        0.4)
                    total_score += score
                    total_backlinks += 1
            
            if total_backlinks > 0:
                return round(total_score / total_backlinks, 2)
            return 0.0
        except Exception as e:
            logger.error("Error calculating backlink score: %s", str(e))
            return 0.0
    
    def _calculate_keyword_density(self, content: str) -> Dict[str, float]:
        """Calculate keyword density for given content.
        
        Args:
            content: Text content to analyze
            
        Returns:
            Dict mapping keywords to their density percentage
        """
        if not content:
            return {}

        # Tokenize and clean content
        words = re.findall(r'\w+', content.lower())
        if not words:
            return {}

        # Count word frequencies
        word_count = Counter(words)
        total_words = len(words)

        # Calculate density percentages
        densities = {
            word: (count / total_words) * 100
            for word, count in word_count.items()
            if len(word) > 3  # Filter out short words
        }

        # Sort by density and return top 20
        return dict(sorted(
            densities.items(),
            key=lambda x: x[1],
            reverse=True
        )[:20])

    def _calculate_readability(self, content: str) -> float:
        """Calculate readability score using Flesch Reading Ease.
        
        Args:
            content: Text content to analyze
            
        Returns:
            float: Readability score (0-100)
        """
        if not content:
            return 0.0

        try:
            # Use TextBlob for sentence parsing
            blob = TextBlob(content)

            # Count syllables, words and sentences
            words = len(blob.words)
            sentences = len(blob.sentences)

            if words == 0 or sentences == 0:
                return 0.0
        
        # Calculate average sentence length
            avg_sentence_length = words / sentences

            # Estimate syllables (simplified)
            syllables = sum(self._count_syllables(word) for word in blob.words)
            avg_syllables_per_word = syllables / words

            # Flesch Reading Ease formula
            score = 206.835 - (1.015 * avg_sentence_length) - \
                (84.6 * avg_syllables_per_word)

            # Clamp score between 0-100
            return max(0, min(100, score))

        except Exception as e:
            logger.error("Error calculating readability: %s", str(e))
            return 0.0

    def _count_syllables(self, word: str) -> int:
        """Helper method to count syllables in a word."""
        word = word.lower()
        count = 0
        vowels = "aeiouy"
        
        # Handle special cases
        if word.endswith("e"):
            word = word[:-1]

        prev_char_is_vowel = False
        for char in word:
            is_vowel = char in vowels
            if is_vowel and not prev_char_is_vowel:
                count += 1
            prev_char_is_vowel = is_vowel

        return max(1, count)  # Every word has at least one syllable

    def _analyze_sentiment(self, content: str) -> float:
        """Analyze sentiment of content using TextBlob.
        
        Args:
            content: Text content to analyze
            
        Returns:
            float: Sentiment score (-1 to 1)
        """
        if not content:
            return 0.0

        try:
            blob = TextBlob(content)
            return blob.sentiment.polarity
        except Exception as e:
            logger.error("Error analyzing sentiment: %s", str(e))
            return 0.0
    
    def _check_content_issues(self, metadata):
        """Check content-related issues.
        
        Args:
            metadata: SEOMetadata instance to check
        """
        try:
            # Check title length
            if metadata.title:
                title_length = len(metadata.title)
                if title_length < 30:
                    self._create_issue(
                        url=metadata.url,
                        title="Title too short",
                        description=f"Title length ({title_length} chars) below recommended minimum of 30",
                        severity="low",
                        category="content"
                    )
                elif title_length > 60:
                    self._create_issue(
                        url=metadata.url,
                        title="Title too long",
                        description=f"Title length ({title_length} chars) exceeds recommended maximum of 60",
                        severity="low",
                        category="content"
                    )
            else:
                self._create_issue(
                    url=metadata.url,
                    title="Missing title",
                    description="Page does not have a title tag",
                    severity="major",
                    category="content"
                )
            
            # Check meta description
            if metadata.meta_description:
                desc_length = len(metadata.meta_description)
                if desc_length < 120:
                    self._create_issue(
                        url=metadata.url,
                        title="Meta description too short",
                        description=f"Meta description length ({desc_length} chars) below recommended minimum of 120",
                        severity="low",
                        category="content"
                    )
                elif desc_length > 160:
                    self._create_issue(
                        url=metadata.url,
                        title="Meta description too long",
                        description=f"Meta description length ({desc_length} chars) exceeds recommended maximum of 160",
                        severity="low",
                        category="content"
                    )
            else:
                self._create_issue(
                    url=metadata.url,
                    title="Missing meta description",
                    description="Page does not have a meta description",
                    severity="major",
                    category="content"
                )

            # Check heading structure
            h1_tags = metadata.h1 if isinstance(metadata.h1, list) else [metadata.h1] if metadata.h1 else []
            if not h1_tags:
                self._create_issue(
                    url=metadata.url,
                    title="Missing H1 heading",
                    description="Page does not have an H1 heading",
                    severity="major",
                    category="content"
                )
            elif len(h1_tags) > 1:
                self._create_issue(
                    url=metadata.url,
                    title="Multiple H1 headings",
                    description=f"Page has {len(h1_tags)} H1 headings",
                    severity="minor",
                    category="content"
                )

        except Exception as e:
            logger.error(
                "Error checking content issues for %s: %s",
                metadata.url,
                str(e)
            )

    def _identify_broken_links(self):
        """Identify broken links and create issues for them."""
        try:
            # Get all links from the current crawl
            links = SEOLink.objects.filter(
                metadata__crawl=self.crawl
            )
            
            # Filter broken links
            broken_links = links.filter(status_code__gte=400)
            
            # Create issues for broken links
            for link in broken_links:
                self._create_issue(
                    url=link.metadata.url,
                    title="Broken link found",
                    description=f"Link to {link.url} returns status code {link.status_code}",
                    severity="major",
                    category="links",
                    recommendation="Fix or remove broken link"
                )
            
            # Update technical score based on broken links
            if broken_links.exists():
                penalty = min(30, broken_links.count() * 5)  # Max 30% penalty
                self._update_technical_score(-penalty)
            
            return {
                'total_broken_links': broken_links.count(),
                'broken_internal_links': broken_links.filter(type='internal').count(),
                'broken_external_links': broken_links.filter(type='external').count(),
                'status_codes': dict(broken_links.values_list('status_code').annotate(count=models.Count('id')))
            }
            
        except Exception as e:
            logger.error("Error identifying broken links: %s", str(e))
            return {
                'total_broken_links': 0,
                'broken_internal_links': 0,
                'broken_external_links': 0,
                'status_codes': {}
            }

    def _check_duplicate_content(self):
        """Check for duplicate or similar content."""
        # This is a simplified approach for demonstration
        # In a real system, we'd use more sophisticated content similarity
        # algorithms
        
        # Check for duplicate titles
        duplicate_titles = (
            SEOMetadata.objects.filter(crawl=self.crawl)
            .values('title')
            .annotate(count=Count('id'))
            .filter(count__gt=1, title__isnull=False)
        )
        
        for dup in duplicate_titles:
            if not dup['title']:
                continue
                
            pages = SEOMetadata.objects.filter(
                crawl=self.crawl, 
                title=dup['title']
            )
            
            for page in pages:
                SEOIssue.objects.get_or_create(
                    crawl=self.crawl,
                    url=page.url,
                    title="Duplicate page title",
                    severity="major",
                    category="content",
                    defaults={
                        'description': f"This page shares its title with {dup['count']-1} other pages. This can confuse search engines about which page to rank.",
                        'recommendation': "Create unique, descriptive titles for each page on your site."
                    }
                )
        
        # Check for duplicate meta descriptions
        duplicate_metas = (
            SEOMetadata.objects.filter(crawl=self.crawl)
            .values('meta_description')
            .annotate(count=Count('id'))
            .filter(count__gt=1, meta_description__isnull=False)
        )
        
        for dup in duplicate_metas:
            if not dup['meta_description']:
                continue
                
            pages = SEOMetadata.objects.filter(
                crawl=self.crawl, 
                meta_description=dup['meta_description']
            )
            
            for page in pages:
                SEOIssue.objects.get_or_create(
                    crawl=self.crawl,
                    url=page.url,
                    title="Duplicate meta description",
                    severity="minor",
                    category="metadata",
                    defaults={
                        'description': f"This page shares its meta description with {dup['count']-1} other pages.",
                        'recommendation': "Write unique meta descriptions for each page to improve click-through rates from search results."
                    }
                )
    
    def _check_orphaned_pages(self):
        """Identify orphaned pages (pages with no internal links to them)."""
        # Get all URLs that were crawled
        all_urls = set(
            SEOMetadata.objects.filter(
                crawl=self.crawl).values_list(
                'url',
                flat=True))
        
        # Get all URLs that are linked to internally
        linked_urls = set(SEOLink.objects.filter(
            metadata__crawl=self.crawl,
            type='internal'
        ).values_list('url', flat=True))
        
        # Orphaned pages are those that were crawled but have no internal links to them
        # (excluding the start URL)
        orphaned_urls = all_urls - linked_urls - {self.project.site_url}
        
        for url in orphaned_urls:
            try:
                page = SEOMetadata.objects.get(crawl=self.crawl, url=url)
                SEOIssue.objects.get_or_create(
                    crawl=self.crawl,
                    url=url,
                    title="Orphaned page",
                    severity="major",
                    category="links",
                    defaults={
                        'description': "This page has no internal links pointing to it, making it difficult for users and search engines to discover.",
                        'recommendation': "Add internal links to this page from related content to improve its visibility and crawlability."
                    }
                )
            except SEOMetadata.DoesNotExist:
                continue
    
    def _analyze_site_structure(self):
        """Analyze site structure and create issues for pages that are too deep."""
        try:
            # Get all internal links
            internal_links = SEOLink.objects.filter(
                metadata__crawl=self.crawl,
                type='internal'
            )

            # Build page depth mapping
            page_depths = {}
            queue = [(self.crawl.project.site_url, 0)]
            visited = set()

            while queue:
                url, depth = queue.pop(0)
                if url in visited:
                    continue
                    
                visited.add(url)
                page_depths[url] = depth

                # Add child pages to queue
                child_links = internal_links.filter(metadata__url=url)
                for link in child_links:
                    if link.url not in visited:
                        queue.append((link.url, depth + 1))

            # Create issues for deep pages
            for url, depth in page_depths.items():
                if depth > 3:  # Pages deeper than 3 levels
                    self._create_issue(
                        url=url,
                        title=f"Deep page structure (depth: {depth})",
                        description=f"This page is {depth} clicks away from the homepage, which may affect its SEO value.",
                        severity="minor" if depth <= 5 else "major",
                        category="technical")

        except Exception as e:
            logger.error("Error analyzing site structure: %s", str(e))

    def _analyze_internal_linking(self):
        """Analyze internal linking structure and identify issues."""
        try:
            internal_links = SEOLink.objects.filter(
                metadata__crawl=self.crawl,
                type='internal'
            )

            # Analyze link distribution
            pages = SEOMetadata.objects.filter(crawl=self.crawl)
            for page in pages:
                outbound_links = internal_links.filter(metadata=page).count()
                inbound_links = internal_links.filter(url=page.url).count()

                # Check for pages with few inbound links
                if inbound_links < 2:
                    self._create_issue(
                        url=page.url,
                        title="Low number of inbound links",
                        description=f"This page has only {inbound_links} internal links pointing to it.",
                        severity="minor",
                        category="links")

                # Check for pages with excessive outbound links
                if outbound_links > 100:
                    self._create_issue(
                        url=page.url,
                        title="Excessive outbound links",
                        description=f"This page has {outbound_links} internal links, which may dilute link equity.",
                        severity="minor",
                        category="links")

        except Exception as e:
            logger.error("Error analyzing internal linking: %s", str(e))

    def _check_canonical_tags(self):
        """Check canonical tag implementation."""
        try:
            metadata_entries = SEOMetadata.objects.filter(crawl=self.crawl)
            
            for metadata in metadata_entries:
                if not metadata.canonical_url:
                    self._create_issue(
                        url=metadata.url,
                        title="Missing canonical tag",
                        description="Page does not have a canonical tag",
                        severity="minor",
                        category="technical",
                        recommendation="Add a canonical tag to indicate the preferred version of this page"
                    )
                elif metadata.canonical_url != metadata.url:
                    self._create_issue(
                        url=metadata.url,
                        title="Non-self-referential canonical",
                        description=f"Page canonicalized to: {metadata.canonical_url}",
                        severity="info",
                        category="technical",
                        recommendation="Verify this canonicalization is intended"
                    )
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking canonical tags: {str(e)}")
            return False

    def _check_hreflang_tags(self):
        """Check hreflang tag implementation."""
        metadata_entries = SEOMetadata.objects.filter(crawl=self.crawl)
        
        for metadata in metadata_entries:
            try:
                soup = BeautifulSoup(metadata.raw_html, 'html.parser')
                hreflang_tags = soup.find_all(
                    'link', {'rel': 'alternate', 'hreflang': True})
                
                if not hreflang_tags and self.project.is_multilingual:
                    SEOIssue.objects.create(
                        metadata=metadata,
                        issue_type='technical',
                        severity='major',
                        description='Missing hreflang tags for multilingual site',
                        recommendation='Add appropriate hreflang tags for all language versions.')
                
                # Check for common hreflang issues
                for tag in hreflang_tags:
                    hreflang = tag.get('hreflang', '')
                    href = tag.get('href', '')
                    
                    if not href:
                        SEOIssue.objects.create(
                            metadata=metadata,
                            issue_type='technical',
                            severity='major',
                            description=f'Invalid hreflang tag: missing href attribute',
                            recommendation='Add href attribute to hreflang tag.')
                    
                    if not re.match(
                        r'^[a-z]{2}-[A-Z]{2}$|^[a-z]{2}$|^x-default$',
                            hreflang):
                        SEOIssue.objects.create(
                            metadata=metadata,
                            issue_type='technical',
                            severity='major',
                            description=f'Invalid hreflang value: {hreflang}',
                            recommendation='Use correct language and country codes (e.g., en-US).')
                
            except Exception as e:
                logger.error(
                    f"Error checking hreflang tags for {metadata.url}: {str(e)}")
                continue
    
    def _analyze_schema_markup(self):
        """Analyze schema markup for issues and opportunities."""
        # Get pages with schema markup
        pages_with_schema = SEOMetadata.objects.filter(
            crawl=self.crawl,
            schema_markup__isnull=False
        ).exclude(schema_markup=[])
        
        # Check for important schema types
        schema_types_to_check = [
            'Organization', 'LocalBusiness', 'Product', 'FAQPage',
            'Article', 'BlogPosting', 'WebPage', 'Review', 'BreadcrumbList'
        ]
        
        for page in pages_with_schema:
            found_types = set()
            
            if isinstance(page.schema_markup, list):
                for schema_text in page.schema_markup:
                    try:
                        if isinstance(schema_text, str):
                            schema_json = json.loads(schema_text)
                            if '@type' in schema_json:
                                schema_type = schema_json['@type']
                                if isinstance(schema_type, list):
                                    found_types.update(schema_type)
                                else:
                                    found_types.add(schema_type)
                    except (json.JSONDecodeError, TypeError):
                        continue
            
            # Check for missing important schema types based on URL patterns
            url_path = page.url.split('/')[-1].lower()
            
            if 'product' in url_path and 'Product' not in found_types:
                SEOIssue.objects.get_or_create(
                    crawl=self.crawl,
                    url=page.url,
                    title="Missing Product schema",
                    severity="minor",
                    category="structured_data",
                    defaults={
                        'description': "This appears to be a product page but lacks Product schema markup.",
                        'recommendation': "Add Product schema to enhance search results with rich product information."
                    }
                )
            
            if ('faq' in url_path or 'frequently-asked-questions' in url_path) and 'FAQPage' not in found_types:
                SEOIssue.objects.get_or_create(
                    crawl=self.crawl,
                    url=page.url,
                    title="Missing FAQPage schema",
                    severity="minor",
                    category="structured_data",
                    defaults={
                        'description': "This appears to be an FAQ page but lacks FAQPage schema markup.",
                        'recommendation': "Add FAQPage schema to enhance search results with rich FAQ information."
                    }
                )
            
            if ('article' in url_path or 'blog' in url_path) and not any(
                    t in found_types for t in ['Article', 'BlogPosting']):
                SEOIssue.objects.get_or_create(
                    crawl=self.crawl,
                    url=page.url,
                    title="Missing Article schema",
                    severity="minor",
                    category="structured_data",
                    defaults={
                        'description': "This appears to be an article or blog post but lacks Article/BlogPosting schema markup.",
                        'recommendation': "Add Article or BlogPosting schema to enhance search results with rich article information."
                    }
                )
    
    def _create_ai_seo_issue(self, metadata):
        """Create AI SEO-related issues for a page.
        
        Args:
            metadata: SEOMetadata instance
        """
        # Check for important AI SEO features
        issues = []
        
        # Check for FAQPage schema
        if metadata.schema_markup:
            found_faq = False
            for schema_text in metadata.schema_markup:
                try:
                    if isinstance(schema_text, str):
                        schema_json = json.loads(schema_text)
                        if '@type' in schema_json and schema_json['@type'] == 'FAQPage':
                            found_faq = True
                            break
                except (json.JSONDecodeError, TypeError):
                    continue
            
            if not found_faq:
                issues.append({
                    'title': "Missing FAQPage schema for AI Search",
                    'description': "Adding FAQ schema can improve visibility in AI-powered search results.",
                    'recommendation': "Implement FAQPage schema for questions and answers relevant to your content."
                })
        
        # Check for proper heading structure
        if not metadata.h1 or (
            isinstance(
                metadata.h1,
                list) and len(
                metadata.h1) == 0):
            issues.append({
                'title': "Missing H1 heading for AI readability",
                'description': "AI search engines rely on proper heading structure to understand content organization.",
                'recommendation': "Add a clear H1 heading that summarizes the page content."
            })
            
        if not metadata.h2 or (
            isinstance(
                metadata.h2,
                list) and len(
                metadata.h2) == 0):
            issues.append({
                'title': "Missing H2 headings for AI content structure",
                'description': "AI search engines use H2 headings to extract key sections for featured snippets.",
                'recommendation': "Structure your content with descriptive H2 headings for each major section."
            })
        
        # Create the issues
        for issue in issues:
            SEOIssue.objects.get_or_create(
                crawl=self.crawl,
                url=metadata.url,
                title=issue['title'],
                severity="minor",
                category="ai_seo",
                defaults={
                    'description': issue['description'],
                    'recommendation': issue['recommendation']
                }
            )
    
    def _calculate_metadata_score(self):
        """Calculate metadata quality score (0-100).
        
        Returns:
            float: Metadata score
        """
        total_pages = SEOMetadata.objects.filter(crawl=self.crawl).count()
        if total_pages == 0:
            return 0
        
        # Get counts for key metadata issues
        missing_title = SEOMetadata.objects.filter(
            crawl=self.crawl, title__isnull=True).count()
        short_title = SEOMetadata.objects.filter(
            crawl=self.crawl,
            title__isnull=False).exclude(
            title='').annotate(
            title_len=models.functions.Length('title')).filter(
                title_len__lt=20).count()

        long_title = SEOMetadata.objects.filter(
            crawl=self.crawl,
            title__isnull=False).exclude(
            title='').annotate(
            title_len=models.functions.Length('title')).filter(
                title_len__gt=70).count()

        missing_meta_desc = SEOMetadata.objects.filter(
            crawl=self.crawl, meta_description__isnull=True).count()
        short_meta_desc = SEOMetadata.objects.filter(
            crawl=self.crawl,
            meta_description__isnull=False).exclude(
            meta_description='').annotate(
            desc_len=models.functions.Length('meta_description')).filter(
                desc_len__lt=70).count()

        long_meta_desc = SEOMetadata.objects.filter(
            crawl=self.crawl,
            meta_description__isnull=False).exclude(
            meta_description='').annotate(
            desc_len=models.functions.Length('meta_description')).filter(
                desc_len__gt=160).count()
        
        # Use reduce to combine Q objects
        missing_h1_conditions = reduce(or_, [
            Q(h1__isnull=True),
            Q(h1=[]),
            Q(h1='')
        ])
        
        missing_h1 = SEOMetadata.objects.filter(
            Q(crawl=self.crawl) & missing_h1_conditions
        ).count()
        
        # Calculate deductions
        title_deduction = ((missing_title * 1.0) + (short_title *
                           0.5) + (long_title * 0.3)) / total_pages * 40
        meta_desc_deduction = ((missing_meta_desc * 0.8) + (short_meta_desc *
                               0.4) + (long_meta_desc * 0.2)) / total_pages * 35
        h1_deduction = (missing_h1 / total_pages) * 25
        
        # Calculate final score
        metadata_score = 100 - \
            (title_deduction + meta_desc_deduction + h1_deduction)
        return max(0, min(100, metadata_score))
    
    def _calculate_content_score(self):
        """Calculate content quality score (0-100).
        
        Returns:
            float: Content score
        """
        total_pages = SEOMetadata.objects.filter(crawl=self.crawl).count()
        if total_pages == 0:
            return 0
        
        # Get content analysis metrics
        content_analyses = SEOContentAnalysis.objects.filter(
            metadata__crawl=self.crawl
        )
        
        # Calculate average scores
        avg_readability = content_analyses.aggregate(Avg('readability_score'))[
            'readability_score__avg'] or 0
        avg_word_count = SEOMetadata.objects.filter(
            crawl=self.crawl).aggregate(
            Avg('word_count'))['word_count__avg'] or 0
        
        # Count thin content pages
        thin_content_pages = SEOMetadata.objects.filter(
            crawl=self.crawl, word_count__lt=300).count()
        
        # Content score components
        readability_score = (avg_readability / 100) * \
            40  # 40% of score from readability
        
        # Word count score (assume ideal avg is 800+)
        if avg_word_count >= 800:
            word_count_score = 35
        else:
            word_count_score = (avg_word_count / 800) * 35
        
        # Thin content penalty
        thin_content_penalty = (thin_content_pages / total_pages) * 25
        
        # Calculate final score
        content_score = readability_score + word_count_score - thin_content_penalty
        return max(0, min(100, content_score))
    
    def _calculate_technical_score(self):
        """Calculate technical SEO score (0-100).
        
        Returns:
            float: Technical score
        """
        total_pages = SEOMetadata.objects.filter(crawl=self.crawl).count()
        if total_pages == 0:
            return 0
        
        # Count various technical issues
        broken_links_count = SEOLink.objects.filter(
            metadata__crawl=self.crawl,
            status_code__gte=400
        ).count()
        
        pages_without_canonical = SEOMetadata.objects.filter(
            crawl=self.crawl,
            canonical_url__isnull=True
        ).count()
        
        pages_without_schema = SEOMetadata.objects.filter(
            crawl=self.crawl,
            schema_markup__isnull=True
        ).count() + SEOMetadata.objects.filter(
            crawl=self.crawl,
            schema_markup=[]
        ).count()
        
        # Count orphaned pages
        all_urls = set(
            SEOMetadata.objects.filter(
                crawl=self.crawl).values_list(
                'url',
                flat=True))
        linked_urls = set(SEOLink.objects.filter(
            metadata__crawl=self.crawl,
            type='internal'
        ).values_list('url', flat=True))
        orphaned_pages_count = len(
            all_urls - linked_urls - {self.project.site_url})
        
        # Calculate deductions
        broken_links_deduction = min(
            30, (broken_links_count / max(1, total_pages)) * 100)
        canonical_deduction = (pages_without_canonical / total_pages) * 20
        schema_deduction = (pages_without_schema / total_pages) * 25
        orphaned_pages_deduction = min(
            25, (orphaned_pages_count / max(1, total_pages)) * 100)
        
        # Calculate final score
        technical_score = 100 - \
            (broken_links_deduction + canonical_deduction + schema_deduction + orphaned_pages_deduction)
        return max(0, min(100, technical_score))
    
    def _calculate_mobile_score(self):
        """Calculate mobile SEO score (0-100).
        
        Returns:
            float: Mobile score
        """
        total_pages = SEOMetadata.objects.filter(crawl=self.crawl).count()
        if total_pages == 0:
            return 0
        
        # Since we don't have real mobile metrics in our simplified model,
        # we'll estimate based on limited data
        
        # Check how many pages have viewport meta tags
        # This would be extracted during crawling in a real implementation
        mobile_friendly_pages = self._count_mobile_friendly_pages()
        
        # Calculate simple mobile score
        mobile_score = (mobile_friendly_pages / total_pages) * 100
        return max(0, min(100, mobile_score))
    
    def _calculate_ai_seo_score(self):
        """Calculate AI SEO readiness score (0-100).
        
        Returns:
            float: AI SEO score
        """
        # Get average AI readiness score from content analyses
        avg_ai_readiness = SEOContentAnalysis.objects.filter(
            metadata__crawl=self.crawl,
            ai_readiness_score__isnull=False
        ).aggregate(Avg('ai_readiness_score'))['ai_readiness_score__avg'] or 0
        
        return avg_ai_readiness
    
    def _get_issues_summary(self):
        """Get a summary of SEO issues by category and severity.
        
        Returns:
            dict: Issues summary
        """
        # Get counts by category and severity
        category_counts = (
            SEOIssue.objects.filter(crawl=self.crawl)
            .values('category')
            .annotate(count=Count('id'))
            .order_by('-count')
        )
        
        severity_counts = (
            SEOIssue.objects.filter(crawl=self.crawl)
            .values('severity')
            .annotate(count=Count('id'))
            .order_by('-count')
        )
        
        # Count total issues
        total_issues = SEOIssue.objects.filter(crawl=self.crawl).count()
        
        # Format the summary
        result = {
            'total_issues': total_issues,
            'by_category': {
                item['category']: item['count'] for item in category_counts},
            'by_severity': {
                item['severity']: item['count'] for item in severity_counts},
            'top_issues': [],
        }
        
        # Get top issues
        top_issues = (
            SEOIssue.objects.filter(crawl=self.crawl)
            .values('title', 'severity')
            .annotate(count=Count('id'))
            .order_by('-count')[:5]
        )
        
        result['top_issues'] = list(top_issues)
        
        return result
    
    def _generate_improvement_suggestions(self):
        """Generate SEO improvement suggestions based on issues.
        
        Returns:
            dict: Improvement suggestions by category
        """
        suggestions = {}
        
        # Get issues by category
        categories = SEOIssue.objects.filter(
            crawl=self.crawl).values_list(
            'category', flat=True).distinct()
        
        for category in categories:
            # Get top issues in this category
            top_category_issues = (
                SEOIssue.objects.filter(crawl=self.crawl, category=category)
                .values('title')
                .annotate(count=Count('id'))
                .order_by('-count')[:3]
            )
            
            # Compile suggestions for this category
            if top_category_issues:
                category_suggestions = []
                for issue in top_category_issues:
                    # Get a sample of this issue to use its recommendation
                    sample_issue = SEOIssue.objects.filter(
                        crawl=self.crawl,
                        category=category,
                        title=issue['title']
                    ).first()
                    
                    if sample_issue and sample_issue.recommendation:
                        category_suggestions.append({
                            'issue': issue['title'],
                            'count': issue['count'],
                            'recommendation': sample_issue.recommendation
                        })
                
                if category_suggestions:
                    suggestions[category] = category_suggestions
        
        return suggestions
    
    def _get_avg_word_count(self) -> float:
        """Calculate average word count across all pages."""
        try:
            metadata_entries = self._get_metadata_entries()
            if not metadata_entries.exists():
                return 0.0
                
            total_words = sum(entry.word_count or 0 for entry in metadata_entries)
            page_count = metadata_entries.count()
            
            return total_words / page_count if page_count > 0 else 0.0
            
        except Exception as e:
            logger.error("Error calculating average word count: %s", str(e))
            return 0.0

    def _get_readability_stats(self) -> Dict:
        """Get readability statistics across all pages.
        
        Returns:
            Dict with readability stats
        """
        try:
            analyses = SEOContentAnalysis.objects.filter(
                metadata__crawl=self.crawl,
                readability_score__isnull=False
            )

            if not analyses.exists():
                return {
                    'average_score': 0.0,
                    'score_distribution': {},
                    'pages_by_difficulty': {
                        'easy': 0,
                        'medium': 0,
                        'hard': 0
                    }
                }

            # Calculate average score
            avg_score = analyses.aggregate(
                avg_score=Avg('readability_score')
            )['avg_score'] or 0

            # Get score distribution
            score_ranges = {
                'poor': (0, 30),
                'fair': (30, 50),
                'good': (50, 70),
                'excellent': (70, 100)
            }

            distribution = {}
            for label, (min_score, max_score) in score_ranges.items():
                count = analyses.filter(
                    readability_score__gte=min_score,
                    readability_score__lt=max_score
                ).count()
                distribution[label] = count

            # Categorize pages by difficulty
            difficulty_ranges = {
                'easy': (70, 100),
                'medium': (40, 70),
                'hard': (0, 40)
            }

            pages_by_difficulty = {}
            for level, (min_score, max_score) in difficulty_ranges.items():
                count = analyses.filter(
                    readability_score__gte=min_score,
                    readability_score__lt=max_score
                ).count()
                pages_by_difficulty[level] = count

            return {
                'average_score': avg_score,
                'score_distribution': distribution,
                'pages_by_difficulty': pages_by_difficulty
            }

        except Exception as e:
            logger.error("Error getting readability stats: %s", str(e))
            return {
                'average_score': 0.0,
                'score_distribution': {},
                'pages_by_difficulty': {
                    'easy': 0,
                    'medium': 0,
                    'hard': 0
                }
            }

    def _analyze_keyword_coverage(self) -> Dict:
        """Analyze keyword coverage across the site.
        
        Returns:
            Dict with keyword coverage analysis
        """
        try:
            # Get all content analyses
            analyses = SEOContentAnalysis.objects.filter(
                metadata__crawl=self.crawl
            )

            # Collect all keywords and their densities
            all_keywords = {}
            page_count = 0

            for analysis in analyses:
                if analysis.keyword_density:
                    page_count += 1
                    for keyword, density in analysis.keyword_density.items():
                        if keyword in all_keywords:
                            all_keywords[keyword]['total_density'] += density
                            all_keywords[keyword]['page_count'] += 1
                        else:
                            all_keywords[keyword] = {
                                'total_density': density,
                                'page_count': 1
                            }

            if page_count == 0:
                return {
                    'top_keywords': [],
                    'coverage_stats': {
                        'total_keywords': 0,
                        'avg_keywords_per_page': 0
                    }
                }

            # Calculate average densities and sort keywords
            keyword_stats = []
            for keyword, stats in all_keywords.items():
                avg_density = stats['total_density'] / stats['page_count']
                coverage_percentage = (stats['page_count'] / page_count) * 100
                keyword_stats.append({
                    'keyword': keyword,
                    'avg_density': round(avg_density, 2),
                    'page_count': stats['page_count'],
                    'coverage_percentage': round(coverage_percentage, 2)
                })

            # Sort by coverage percentage and average density
            keyword_stats.sort(
                key=lambda x: (x['coverage_percentage'], x['avg_density']),
                reverse=True
            )

            return {
                'top_keywords': keyword_stats[:20],
                'coverage_stats': {
                    'total_keywords': len(all_keywords),
                    'avg_keywords_per_page': round(len(all_keywords) / page_count, 2)
                }
            }

        except Exception as e:
            logger.error("Error analyzing keyword coverage: %s", str(e))
            return {
                'top_keywords': [],
                'coverage_stats': {
                    'total_keywords': 0,
                    'avg_keywords_per_page': 0
                }
            }

    def _analyze_overall_content_quality(self) -> Dict:
        """Analyze overall content quality metrics.
        
        Returns:
            Dict with content quality analysis
        """
        try:
            analyses = SEOContentAnalysis.objects.filter(
                metadata__crawl=self.crawl
            ).select_related('metadata')

            if not analyses.exists():
                return {
                    'average_quality_score': 0.0,
                    'content_issues': [],
                    'improvement_opportunities': []
                }

            # Calculate average quality score
            quality_scores = []
            content_issues = []
            improvement_opportunities = []

            for analysis in analyses:
                # Quality score
                if analysis.content_quality_score is not None:
                    quality_scores.append(analysis.content_quality_score)

                # Check for content issues
                if analysis.metadata.content:
                    word_count = len(
                        re.findall(
                            r'\w+',
                            analysis.metadata.content))
                    if word_count < 300:
                        content_issues.append({
                            'url': analysis.metadata.url,
                            'issue': 'thin_content',
                            'details': f'Only {word_count} words'
                        })

                    if analysis.readability_score and analysis.readability_score < 60:
                        content_issues.append({
                            'url': analysis.metadata.url,
                            'issue': 'low_readability',
                            'details': f'Readability score: {analysis.readability_score}'
                        })

                # Identify improvement opportunities
                if analysis.content_quality_score and analysis.content_quality_score < 70:
                    improvement_opportunities.append({
                        'url': analysis.metadata.url,
                        'score': analysis.content_quality_score,
                        'suggestions': [
                            'Improve content depth',
                            'Add more relevant keywords',
                            'Enhance readability'
                        ]
                    })

            avg_quality_score = sum(quality_scores) / \
                len(quality_scores) if quality_scores else 0

            return {
                'average_quality_score': round(avg_quality_score, 2),
                'content_issues': content_issues[:10],  # Top 10 issues
                # Top 5 opportunities
                'improvement_opportunities': improvement_opportunities[:5]
            }

        except Exception as e:
            logger.error("Error analyzing overall content quality: %s", str(e))
            return {
                'average_quality_score': 0.0,
                'content_issues': [],
                'improvement_opportunities': []
            }

    def _count_mobile_friendly_pages(self) -> int:
        """Count number of mobile-friendly pages.

        Returns:
            int: Number of mobile-friendly pages
        """
        try:
            return SEOPerformance.objects.filter(
            metadata__crawl=self.crawl,
                mobile_friendly=True
        ).count()
        except Exception as e:
            logger.error("Error counting mobile-friendly pages: %s", str(e))
            return 0
    
    def _count_pages_with_issues(self) -> Dict[str, int]:
        """Count pages with various types of issues.
        
        Returns:
            Dict mapping issue categories to counts
        """
        try:
            issues = SEOIssue.objects.filter(
                crawl=self.crawl
            ).values('category').annotate(
                count=Count('id')
            )

            return {
                issue['category']: issue['count']
                for issue in issues
            }

        except Exception as e:
            logger.error("Error counting pages with issues: %s", str(e))
            return {}

    def _analyze_crawl_efficiency(self) -> Dict:
        """Analyze crawl efficiency metrics.
        
        Returns:
            Dict with crawl efficiency metrics
        """
        try:
            total_pages = SEOMetadata.objects.filter(crawl=self.crawl).count()
            if total_pages == 0:
                return {
                    'crawl_rate': 0.0,
                    'crawl_errors': 0,
                    'blocked_resources': 0
                }

            # Count pages with errors
            error_pages = SEOMetadata.objects.filter(
                crawl=self.crawl,
                status_code__gte=400
            ).count()

            # Count blocked resources
            blocked_resources = SEOMetadata.objects.filter(
                crawl=self.crawl,
                robots_directives__contains='noindex'
            ).count()

            # Calculate crawl rate (pages/minute)
            if self.crawl.completed_at and self.crawl.started_at:
                duration = (self.crawl.completed_at -
                            self.crawl.started_at).total_seconds() / 60
                crawl_rate = total_pages / duration if duration > 0 else 0
            else:
                crawl_rate = 0

            return {
                'crawl_rate': round(crawl_rate, 2),
                'crawl_errors': error_pages,
                'blocked_resources': blocked_resources
            }

        except Exception as e:
            logger.error("Error analyzing crawl efficiency: %s", str(e))
            return {
                'crawl_rate': 0.0,
                'crawl_errors': 0,
                'blocked_resources': 0
            }

    def _analyze_site_hierarchy(self) -> Dict:
        """Analyze site structure and hierarchy.

        Returns:
            Dict with site hierarchy analysis
        """
        try:
            metadata_entries = SEOMetadata.objects.filter(crawl=self.crawl)

            # Analyze URL structure
            url_depths = {}
            orphaned_pages = []

            for metadata in metadata_entries:
                # Calculate URL depth
                path = urlparse(metadata.url).path
                depth = len([p for p in path.split('/') if p])
                url_depths[depth] = url_depths.get(depth, 0) + 1

                # Check for orphaned pages (no incoming internal links)
                inbound_links = SEOLink.objects.filter(
            metadata__crawl=self.crawl,
                    url=metadata.url,
                    type='internal'
        ).count()

                if inbound_links == 0 and depth > 1:  # Exclude homepage
                    orphaned_pages.append(metadata.url)

            # Calculate average depth
            total_depth = sum(
                depth * count for depth,
                count in url_depths.items())
            total_pages = sum(url_depths.values())
            avg_depth = total_depth / total_pages if total_pages > 0 else 0

            return {
                'url_depth_distribution': url_depths,
                'average_depth': round(avg_depth, 2),
                # List first 10 orphaned pages
                'orphaned_pages': orphaned_pages[:10],
                'total_orphaned': len(orphaned_pages)
            }

        except Exception as e:
            logger.error("Error analyzing site hierarchy: %s", str(e))
            return {
                'url_depth_distribution': {},
                'average_depth': 0.0,
                'orphaned_pages': [],
                'total_orphaned': 0
            }

    def _calculate_avg_page_speed(self) -> Dict:
        """Calculate average page speed metrics.

        Returns:
            Dict with average speed metrics
        """
        try:
            performances = SEOPerformance.objects.filter(
                metadata__crawl=self.crawl
            ).exclude(
                lcp__isnull=True,
                fid__isnull=True,
                cls__isnull=True
            )

            if not performances.exists():
                return {
                    'avg_lcp': 0.0,
                    'avg_fid': 0.0,
                    'avg_cls': 0.0,
                    'avg_ttfb': 0.0
                }

            metrics = performances.aggregate(
                avg_lcp=Avg('lcp'),
                avg_fid=Avg('fid'),
                avg_cls=Avg('cls'),
                avg_ttfb=Avg('ttfb')
            )

            return {
                'avg_lcp': round(metrics['avg_lcp'] or 0, 2),
                'avg_fid': round(metrics['avg_fid'] or 0, 2),
                'avg_cls': round(metrics['avg_cls'] or 0, 3),
                'avg_ttfb': round(metrics['avg_ttfb'] or 0, 2)
            }

        except Exception as e:
            logger.error("Error calculating average page speed: %s", str(e))
            return {
                'avg_lcp': 0.0,
                'avg_fid': 0.0,
                'avg_cls': 0.0,
                'avg_ttfb': 0.0
            }

    def _analyze_core_web_vitals_compliance(self) -> Dict:
        """Analyze Core Web Vitals compliance.

        Returns:
            Dict with Core Web Vitals analysis
        """
        try:
            performances = SEOPerformance.objects.filter(
                metadata__crawl=self.crawl
            ).exclude(
                lcp__isnull=True,
                fid__isnull=True,
                cls__isnull=True
            )

            total_pages = performances.count()
            if total_pages == 0:
                return {
                    'overall_compliance': 0.0,
                    'metric_compliance': {
                        'lcp': 0.0,
                        'fid': 0.0,
                        'cls': 0.0
                    },
                    'failing_pages': []
                }

            # Count pages passing each metric
            lcp_passing = performances.filter(lcp__lte=2.5).count()
            fid_passing = performances.filter(fid__lte=100).count()
            cls_passing = performances.filter(cls__lte=0.1).count()

            # Calculate compliance percentages
            lcp_compliance = (lcp_passing / total_pages) * 100
            fid_compliance = (fid_passing / total_pages) * 100
            cls_compliance = (cls_passing / total_pages) * 100

            # Overall compliance (all metrics must pass)
            passing_pages = performances.filter(
                lcp__lte=2.5,
                fid__lte=100,
                cls__lte=0.1
            ).count()

            overall_compliance = (passing_pages / total_pages) * 100

            # Get failing pages
            failing_pages = []
            for perf in performances.exclude(
                lcp__lte=2.5,
                fid__lte=100,
                cls__lte=0.1
            ).select_related('metadata')[:10]:  # Get top 10 failing pages
                failing_pages.append({
                    'url': perf.metadata.url,
                    'metrics': {
                        'lcp': perf.lcp,
                        'fid': perf.fid,
                        'cls': perf.cls
                    }
                })

            return {
                'overall_compliance': round(overall_compliance, 2),
                'metric_compliance': {
                    'lcp': round(lcp_compliance, 2),
                    'fid': round(fid_compliance, 2),
                    'cls': round(cls_compliance, 2)
                },
                'failing_pages': failing_pages
            }

        except Exception as e:
            logger.error(
                "Error analyzing Core Web Vitals compliance: %s",
                str(e))
            return {
                'overall_compliance': 0.0,
                'metric_compliance': {
                    'lcp': 0.0,
                    'fid': 0.0,
                    'cls': 0.0
                },
                'failing_pages': []
            }

    def process_crawl4ai_results(
            self,
            crawl_results: Dict,
            ai_seo_results: Dict) -> bool:
        """Process results from Crawl4AI API.
        
        Args:
            crawl_results: Complete results from Crawl4AI
            ai_seo_results: AI SEO specific analysis from Crawl4AI
            
        Returns:
            bool: Success status
        """
        try:
            logger.info(
                "Processing Crawl4AI results for crawl %s",
                self.crawl.id)
            
            # Process pages data
            pages = crawl_results.get('pages', [])
            for page_data in pages:
                self._process_crawl4ai_page(page_data)
            
            # Process AI SEO data
            self._process_crawl4ai_ai_seo(ai_seo_results)
            
            # Update crawl status
            self.crawl.pages_crawled = len(pages)
            self.crawl.external_results = {
                'processed': True,
                'crawl4ai_job_id': crawl_results.get('job_id'),
                'crawl4ai_stats': crawl_results.get('stats', {}),
                'crawl4ai_summary': crawl_results.get('summary', {})
            }
            self.crawl.save()
            
            return True
        except Exception as e:
            logger.error("Error processing Crawl4AI results: %s", str(e))
            return False

    def _process_crawl4ai_page(self, page_data: Dict) -> None:
        """Process a single page from Crawl4AI results.
        
        Args:
            page_data: Page data from Crawl4AI
        """
        # Extract core data
        url = page_data.get('url')
        if not url:
            logger.warning("Skipping page without URL in Crawl4AI results")
            return
        
        try:
            # Create or update metadata record
            metadata = SEOMetadata.objects.create(
                crawl=self.crawl,
                url=url,
                title=page_data.get('metadata', {}).get('title'),
                meta_description=page_data.get('metadata', {}).get('description'),
                h1=page_data.get('headings', {}).get('h1', []),
                h2=page_data.get('headings', {}).get('h2', []),
                canonical_url=page_data.get('metadata', {}).get('canonical'),
                robots_directives=page_data.get('metadata', {}).get('robots'),
                schema_markup=page_data.get('schema', []),
                word_count=page_data.get('content', {}).get('word_count', 0),
                status_code=page_data.get('status_code', 200),
                content_type=page_data.get('content_type'),
                language=page_data.get('metadata', {}).get('language'),
                viewport=page_data.get('metadata', {}).get('viewport'),
                encoding=page_data.get('metadata', {}).get('charset')
            )
            
            # Process performance data if available
            performance_data = page_data.get('performance', {})
            if performance_data:
                SEOPerformance.objects.create(
                    metadata=metadata,
                    lcp=performance_data.get('lcp'),
                    fid=performance_data.get('fid'),
                    cls=performance_data.get('cls'),
                    ttfb=performance_data.get('ttfb'),
                    page_speed_mobile=performance_data.get('mobile_score'),
                    page_speed_desktop=performance_data.get('desktop_score'),
                    mobile_friendly=performance_data.get(
                        'is_mobile_friendly',
                        False),
                    mobile_issues=performance_data.get(
                        'mobile_issues',
                        []))
            
            # Process links
            for link in page_data.get('links', []):
                SEOLink.objects.create(
                    metadata=metadata,
                    url=link.get('url', ''),
                    text=link.get('text', ''),
                    type=link.get('type', 'internal'),
                    follow=not link.get('nofollow', False),
                    status_code=link.get('status_code'),
                    anchor_type=link.get('anchor_type', 'text'),
                    rel_attributes=link.get('rel', [])
                )
            
            # Process images
            for img in page_data.get('images', []):
                SEOImage.objects.create(
                    metadata=metadata,
                    url=img.get('src', ''),
                    alt_text=img.get('alt', ''),
                    title=img.get('title', ''),
                    filename=img.get('filename', ''),
                    filesize=img.get('size'),
                    width=img.get('width'),
                    height=img.get('height'),
                    lazy_loaded=img.get('lazy_loaded', False),
                    format=img.get('format'),
                    compression_ratio=img.get('compression_ratio')
                )
            
            # Process content analysis from Crawl4AI
            content_analysis = page_data.get('content_analysis', {})
            if content_analysis:
                SEOContentAnalysis.objects.create(
                    metadata=metadata,
                    keyword_density=content_analysis.get('keyword_density', {}),
                    readability_score=content_analysis.get('readability_score'),
                    sentiment_score=content_analysis.get('sentiment_score'),
                    topical_relevance=content_analysis.get('topical_relevance'),
                    ai_readiness_score=content_analysis.get('ai_readiness_score'),
                    content_quality_score=content_analysis.get('quality_score'),
                    semantic_topics=content_analysis.get('semantic_topics', []),
                    content_structure=content_analysis.get('structure', {}),
                    content_uniqueness=content_analysis.get('uniqueness_score')
                )
            
            # Process issues
            for issue in page_data.get('issues', []):
                SEOIssue.objects.create(
                    crawl=self.crawl,
                    url=url,
                    title=issue.get('title', 'Unknown issue'),
                    description=issue.get('description', ''),
                    severity=issue.get('severity', 'info'),
                    category=issue.get('category', 'other'),
                    recommendation=issue.get('recommendation', ''),
                    impact_score=issue.get('impact_score', 0),
                    priority=issue.get('priority', 'low')
                )
            
        except Exception as e:
            logger.error("Error processing Crawl4AI page %s: %s", url, str(e))

    def _process_crawl4ai_ai_seo(self, ai_seo_results: Dict) -> None:
        """Process AI SEO results from Crawl4AI.
        
        Args:
            ai_seo_results: AI SEO analysis results
        """
        try:
            # Process page-level AI SEO results
            page_results = ai_seo_results.get('pages', {})
            for url, data in page_results.items():
                try:
                    metadata = SEOMetadata.objects.get(
                        crawl=self.crawl, url=url)
                    
                    # Update or create content analysis with AI SEO data
                    content_analysis = SEOContentAnalysis.objects.get_or_create(
                        metadata=metadata
                    )[0]
                    
                    # Update AI SEO specific fields
                    content_analysis.ai_readiness_score = data.get(
                        'ai_readiness_score')
                    content_analysis.semantic_topics = data.get(
                        'semantic_topics', [])
                    content_analysis.content_structure = data.get(
                        'content_structure', {})
                    content_analysis.save()
                    
                    # Process AI SEO issues
                    for issue in data.get('issues', []):
                        SEOIssue.objects.get_or_create(
                            crawl=self.crawl,
                            url=url,
                            title=issue.get('title'),
                            defaults={
                                'description': issue.get('description', ''),
                                'severity': issue.get('severity', 'info'),
                                'category': 'ai_seo',
                                'recommendation': issue.get('recommendation', ''),
                                'impact_score': issue.get('impact_score', 0),
                                'priority': issue.get('priority', 'low')
                            }
                        )
                    
                except SEOMetadata.DoesNotExist:
                    logger.warning(
                        "Cannot find metadata for %s when processing AI SEO results", url)
            
            # Process site-wide AI SEO insights
            site_insights = ai_seo_results.get('site_insights', {})
            if site_insights:
                # Store site-wide AI SEO metrics in crawl's external_results
                self.crawl.external_results.setdefault('ai_seo', {})
                self.crawl.external_results['ai_seo'].update({
                    'overall_ai_readiness': site_insights.get('overall_ai_readiness'),
                    'semantic_coverage': site_insights.get('semantic_coverage'),
                    'content_quality': site_insights.get('content_quality'),
                    'improvement_areas': site_insights.get('improvement_areas', [])
                })
                self.crawl.save()
                
                # Create site-wide issues from insights
                for insight in site_insights.get('improvements', []):
                    SEOIssue.objects.get_or_create(
                        crawl=self.crawl,
                        url=self.project.site_url,  # Use site URL for site-wide issues
                        title=insight.get('title'),
                        defaults={
                            'description': insight.get('description', ''),
                            'severity': insight.get('priority', 'info'),
                            'category': 'ai_seo',
                            'recommendation': insight.get('recommendation', ''),
                            'impact_score': insight.get('impact_score', 0),
                            'priority': insight.get('priority', 'low')
                        }
                    )
            
        except Exception as e:
            logger.error(
                "Error processing Crawl4AI AI SEO results: %s",
                str(e))
            raise

    def _analyze_content_quality(self):
        """Analyze content quality metrics."""
        try:
            metadata_entries = self._get_metadata_entries()
            
            for metadata in metadata_entries:
                try:
                    content_analysis = metadata.seocontentanalysis
                except SEOContentAnalysis.DoesNotExist:
                    continue

                # Check word count
                if metadata.word_count < 300:  # Minimum threshold
                    self._create_issue(
                        url=metadata.url,
                        title="Thin content",
                        description=f"Page has only {metadata.word_count} words",
                        severity="major",
                        category="content",
                        recommendation="Expand content to at least 300 words"
                    )

                # Check readability
                if content_analysis.readability_score and content_analysis.readability_score < 60:
                    self._create_issue(
                        url=metadata.url,
                        title="Poor readability",
                        description="Content may be difficult to read",
                        severity="major",
                        category="content",
                        recommendation="Improve content readability"
                    )

                # Check content quality score
                if content_analysis.content_quality_score and content_analysis.content_quality_score < 70:
                    self._create_issue(
                        url=metadata.url,
                        title="Low content quality",
                        description="Content quality score below threshold",
                        severity="major",
                        category="content",
                        recommendation="Improve overall content quality"
                    )

            return True
            
        except Exception as e:
            logger.error("Error analyzing content quality: %s", str(e))
            return False

    def _get_page_text_content(self, metadata: 'SEOMetadata') -> str:
        """Get combined text content from various metadata fields."""
        text_parts = []
        
        if metadata.title:
            text_parts.append(metadata.title)
            
        if metadata.meta_description:
            text_parts.append(metadata.meta_description)
            
        if metadata.h1:
            if isinstance(metadata.h1, list):
                text_parts.extend(metadata.h1)
            else:
                text_parts.append(metadata.h1)
                
        if metadata.h2:
            if isinstance(metadata.h2, list):
                text_parts.extend(metadata.h2)
            else:
                text_parts.append(metadata.h2)
        
        return ' '.join(text_parts)

    def _calculate_keyword_density(self, metadata: 'SEOMetadata') -> Dict[str, float]:
        """Calculate keyword density using text content from metadata fields."""
        content = self._get_page_text_content(metadata)
        if not content:
            return {}
            
        # Tokenize and clean content
        words = re.findall(r'\w+', content.lower())
        if not words:
            return {}
            
        # Count word frequencies
        word_count = Counter(words)
        total_words = len(words)
        
        # Calculate density percentages
        densities = {
            word: (count / total_words) * 100
            for word, count in word_count.items()
            if len(word) > 3  # Filter out short words
        }
        
        # Sort by density and return top 20
        return dict(sorted(
            densities.items(),
            key=lambda x: x[1],
            reverse=True
        )[:20])

    def _calculate_readability(self, metadata: 'SEOMetadata') -> float:
        """Calculate readability score using text content from metadata fields."""
        content = self._get_page_text_content(metadata)
        if not content:
            return 0.0
            
        try:
            # Use TextBlob for sentence parsing
            blob = TextBlob(content)
            
            if not blob.sentences:
                return 0.0
                
            # Calculate averages
            avg_sentence_length = sum(len(sentence.words) for sentence in blob.sentences) / len(blob.sentences)
            avg_syllables_per_word = sum(self._count_syllables(word) for word in blob.words) / len(blob.words)
            
            # Flesch Reading Ease formula
            score = 206.835 - (1.015 * avg_sentence_length) - (84.6 * avg_syllables_per_word)
            
            # Clamp score between 0 and 100
            return max(0.0, min(100.0, score))
            
        except Exception as e:
            logger.error("Error calculating readability score: %s", str(e))
            return 0.0

    def _analyze_technical_elements(self):
        """Analyze technical SEO elements."""
        for metadata in SEOMetadata.objects.filter(crawl=self.crawl):
            # Check canonical implementation
            if not metadata.canonical_url:
                self._create_issue(
                    url=metadata.url,
                    title="Missing canonical tag",
                    description="Page should specify a canonical URL to prevent duplicate content issues.",
                    severity="major",
                    category="technical")
            
            # Check meta robots
            if not metadata.robots_directives:
                self._create_issue(
                    url=metadata.url,
                    title="Missing robots directives",
                    description="Page should specify robots directives for proper indexing control.",
                    severity="minor",
                    category="technical")
            
            # Check mobile viewport
            if not metadata.viewport:
                self._create_issue(
                    url=metadata.url,
                    title="Missing viewport meta tag",
                    description="Page lacks mobile viewport configuration.",
                    severity="critical",
                    category="mobile")
            
            # Check schema markup
            if not metadata.schema_markup:
                self._create_issue(
                    url=metadata.url,
                    title="Missing structured data",
                    description="Page could benefit from schema.org markup.",
                    severity="minor",
                    category="structured_data")
            
            # Performance metrics
            performance = SEOPerformance.objects.get(metadata=metadata)
            if performance.lcp and performance.lcp > 2.5:
                self._create_issue(
                    url=metadata.url,
                    title="Poor loading performance",
                    description=f"Largest Contentful Paint (LCP) is {performance.lcp}s. Should be under 2.5s.",
                    severity="major",
                    category="performance")

    def _analyze_multilingual_elements(self):
        """Analyze multilingual SEO implementation."""
        # Group pages by language
        pages_by_lang = {}
        for metadata in SEOMetadata.objects.filter(crawl=self.crawl):
            lang = metadata.language or 'unknown'
            if lang not in pages_by_lang:
                pages_by_lang[lang] = []
            pages_by_lang[lang].append(metadata)
        
        # Check hreflang implementation
        for lang, pages in pages_by_lang.items():
            for metadata in pages:
                # Check if page has proper language declaration
                if not metadata.language:
                    self._create_issue(
                        url=metadata.url,
                        title="Missing language declaration",
                        description="Page should specify its language using the html lang attribute.",
                        severity="major",
                        category="international")
                
                # Check hreflang links
                if not any(
                        'hreflang' in link.rel_attributes for link in metadata.links.all()):
                    self._create_issue(
                        url=metadata.url,
                        title="Missing hreflang tags",
                        description="Multilingual page should implement hreflang tags.",
                        severity="major",
                        category="international")
        
        # Check content parity across languages
        self._analyze_translation_quality()
        self._check_content_completeness()

    def _generate_comprehensive_report(self):
        """Generate comprehensive SEO report."""
        report_data = {
            'overview': {
                'total_pages': SEOMetadata.objects.filter(crawl=self.crawl).count(),
                'total_issues': SEOIssue.objects.filter(crawl=self.crawl).count(),
                'critical_issues': SEOIssue.objects.filter(crawl=self.crawl, severity='critical').count(),
                'major_issues': SEOIssue.objects.filter(crawl=self.crawl, severity='major').count(),
                'minor_issues': SEOIssue.objects.filter(crawl=self.crawl, severity='minor').count()
            },
            'scores': {
                'metadata_score': self._calculate_metadata_score(),
                'content_score': self._calculate_content_score(),
                'technical_score': self._calculate_technical_score(),
                'mobile_score': self._calculate_mobile_score(),
                'performance_score': self._calculate_page_speed_score(),
                'ai_seo_score': self._calculate_ai_seo_score()
            },
            'content_analysis': {
                'avg_word_count': self._get_avg_word_count(),
                'readability_stats': self._get_readability_stats(),
                'keyword_coverage': self._analyze_keyword_coverage(),
                'content_quality': self._analyze_overall_content_quality()
            },
            'technical_analysis': {
                'mobile_friendly_pages': self._count_mobile_friendly_pages(),
                'pages_with_issues': self._count_pages_with_issues(),
                'crawl_efficiency': self._analyze_crawl_efficiency(),
                'site_structure': self._analyze_site_hierarchy()
            },
            'performance_metrics': {
                'avg_page_speed': self._calculate_avg_page_speed(),
                'core_web_vitals': self._analyze_core_web_vitals_compliance(),
                'mobile_performance': self._analyze_mobile_performance()
            },
            'multilingual_analysis': {
                'language_coverage': self._analyze_language_coverage(),
                'translation_quality': self._analyze_translation_metrics(),
                'market_relevance': self._analyze_market_specific_metrics()
            },
            'recommendations': self._generate_prioritized_recommendations()
        }
        
        # Create or update report
        SEOReport.objects.update_or_create(
            crawl=self.crawl,
            defaults={
                'overview': report_data['overview'],
                'metadata_score': report_data['scores']['metadata_score'],
                'content_score': report_data['scores']['content_score'],
                'technical_score': report_data['scores']['technical_score'],
                'mobile_score': report_data['scores']['mobile_score'],
                'overall_score': sum(report_data['scores'].values()) / len(report_data['scores']),
                'ai_seo_score': report_data['scores']['ai_seo_score'],
                'improvement_suggestions': self._generate_improvement_suggestions()
            }
        )

    def _calculate_page_speed_score(self) -> float:
        """Calculate overall page speed score (0-100).

        Returns:
            float: Page speed score
        """
        try:
            # Get performance data
            performances = SEOPerformance.objects.filter(
                metadata__crawl=self.crawl
            ).exclude(
                page_speed_mobile__isnull=True,
                page_speed_desktop__isnull=True
            )

            if not performances.exists():
                return 0.0

            # Calculate average of mobile and desktop scores
            avg_scores = performances.aggregate(
                avg_mobile=Avg('page_speed_mobile'),
                avg_desktop=Avg('page_speed_desktop')
            )

            mobile_score = avg_scores['avg_mobile'] or 0
            desktop_score = avg_scores['avg_desktop'] or 0

            # Weight mobile slightly higher (60/40 split)
            return (mobile_score * 0.6) + (desktop_score * 0.4)

        except Exception as e:
            logger.error("Error calculating page speed score: %s", str(e))
            return 0.0

    def _calculate_mobile_optimization_score(self) -> float:
        """Calculate mobile optimization score (0-100).

        Returns:
            float: Mobile optimization score
        """
        try:
            performances = SEOPerformance.objects.filter(
                metadata__crawl=self.crawl
            ).exclude(mobile_friendly__isnull=True)

            if not performances.exists():
                return 0.0

            # Calculate percentage of mobile-friendly pages
            total_pages = performances.count()
            mobile_friendly_pages = performances.filter(
                mobile_friendly=True).count()

            base_score = (mobile_friendly_pages / total_pages) * 100

            # Analyze mobile issues
            pages_with_issues = performances.exclude(mobile_issues=[]).count()
            issue_penalty = (pages_with_issues / total_pages) * \
                20  # Up to 20 point penalty

            return max(0, min(100, base_score - issue_penalty))

        except Exception as e:
            logger.error(
                "Error calculating mobile optimization score: %s",
                str(e))
            return 0.0

    def _check_local_schema_implementation(self) -> Dict:
        """Check implementation of local business schema markup.

        Returns:
            Dict with schema implementation status
        """
        try:
            metadata_entries = SEOMetadata.objects.filter(
                crawl=self.crawl,
                schema_markup__isnull=False
            )

            total_pages = metadata_entries.count()
            if total_pages == 0:
                return {
                    'status': 'not_implemented',
                    'coverage': 0,
                    'issues': []
                }

            # Check for local business schema
            pages_with_local = metadata_entries.filter(
                schema_markup__contains='LocalBusiness'
            ).count()

            coverage = (pages_with_local / total_pages) * 100

            if pages_with_local == 0:
                status = 'not_implemented'
            elif coverage < 50:
                status = 'partial'
            else:
                status = 'implemented'

            return {
                'status': status,
                'coverage': coverage,
                'issues': self._get_schema_issues()
            }

        except Exception as e:
            logger.error(
                "Error checking local schema implementation: %s",
                str(e))
            return {
                'status': 'error',
                'coverage': 0,
                'issues': []
            }

    def _calculate_organic_keywords(self) -> int:
        """Calculate number of organic keywords ranking in top 100.

        Returns:
            int: Number of ranking keywords
        """
        try:
            return SEOSearchConsoleKeyword.objects.filter(
                search_console_data__project=self.crawl.project,
                average_position__lte=100
            ).count()
        except Exception as e:
            logger.error("Error calculating organic keywords: %s", str(e))
            return 0

    def _calculate_competitor_keywords(self) -> int:
        """Calculate number of competitor keywords we don't rank for.

        Returns:
            int: Number of competitor keywords
        """
        try:
            competitor_analyses = SEOCompetitorAnalysis.objects.filter(
                metadata__crawl=self.crawl
            )

            competitor_keywords = set()
            for analysis in competitor_analyses:
                if analysis.keyword_overlap:
                    competitor_keywords.update(
                        kw for kw, data in analysis.keyword_overlap.items() if data.get(
                            'competitor_ranks', False) and not data.get(
                            'we_rank', False))

            return len(competitor_keywords)

        except Exception as e:
            logger.error("Error calculating competitor keywords: %s", str(e))
            return 0

    def _calculate_average_position(self) -> float:
        """Calculate average position in search results.

        Returns:
            float: Average position
        """
        try:
            result = SEOSearchConsoleKeyword.objects.filter(
                search_console_data__project=self.crawl.project,
                average_position__isnull=False
            ).aggregate(
                avg_position=Avg('average_position')
            )
            return round(result['avg_position'] or 0, 2)
        except Exception as e:
            logger.error("Error calculating average position: %s", str(e))
            return 0.0

    def _calculate_visibility_percentage(self) -> float:
        """Calculate search visibility percentage.

        Returns:
            float: Visibility percentage (0-100)
        """
        try:
            keywords = SEOSearchConsoleKeyword.objects.filter(
                search_console_data__project=self.crawl.project
            )

            total_keywords = keywords.count()
            if total_keywords == 0:
                return 0.0

            # Weight positions by search volume
            weighted_sum = 0
            total_volume = 0

            for kw in keywords:
                position = kw.average_position or 100  # Default to 100 if no position
                volume = kw.search_volume or 1  # Default to 1 if no volume

                # Calculate visibility score (higher positions = better
                # visibility)
                if position <= 3:
                    score = 1.0
                elif position <= 10:
                    score = 0.8
                elif position <= 20:
                    score = 0.5
                elif position <= 50:
                    score = 0.3
                else:
                    score = 0.1

                weighted_sum += score * volume
                total_volume += volume

            if total_volume == 0:
                return 0.0

            visibility = (weighted_sum / total_volume) * 100
            return round(visibility, 2)

        except Exception as e:
            logger.error("Error calculating visibility percentage: %s", str(e))
            return 0.0

    def _calculate_domain_rating(self) -> float:
        """Calculate domain rating based on backlink metrics.

        Returns:
            float: Domain rating (0-100)
        """
        try:
            backlinks = SEOBacklinkAnalysis.objects.filter(
                project=self.crawl.project
            )

            if not backlinks.exists():
                return 0.0

            # Get aggregate metrics
            metrics = backlinks.aggregate(
                total_backlinks=Count('id'),
                avg_domain_rating=Avg('referring_domain_rating'),
                unique_domains=Count('referring_domain', distinct=True)
            )

            total_backlinks = metrics['total_backlinks'] or 0
            avg_domain_rating = metrics['avg_domain_rating'] or 0
            unique_domains = metrics['unique_domains'] or 0

            # Calculate score components
            # Scale up to 1000 backlinks
            quantity_score = min(100, (total_backlinks / 1000) * 100)
            quality_score = avg_domain_rating
            # Scale up to 100 domains
            diversity_score = min(100, (unique_domains / 100) * 100)

            # Weighted average
            domain_rating = (
                quantity_score * 0.3 +
                quality_score * 0.4 +
                diversity_score * 0.3
            )

            return round(domain_rating, 2)

        except Exception as e:
            logger.error("Error calculating domain rating: %s", str(e))
            return 0.0

    def _calculate_organic_traffic(self) -> int:
        """Calculate estimated monthly organic traffic.

        Returns:
            int: Estimated monthly organic traffic
        """
        try:
            # Get search console data for last 30 days
            month_ago = timezone.now() - timezone.timedelta(days=30)

            traffic = SEOSearchConsoleData.objects.filter(
                project=self.crawl.project,
                date__gte=month_ago
            ).aggregate(
                total_clicks=models.Sum('clicks')
            )['total_clicks'] or 0

            return int(traffic)

        except Exception as e:
            logger.error("Error calculating organic traffic: %s", str(e))
            return 0

    def _calculate_search_rankings(self) -> float:
        """Calculate percentage of keywords ranking in top 10.

        Returns:
            float: Percentage of keywords in top 10 (0-100)
        """
        try:
            keywords = SEOSearchConsoleKeyword.objects.filter(
                search_console_data__project=self.crawl.project
            )

            total_keywords = keywords.count()
            if total_keywords == 0:
                return 0.0

            top_10_keywords = keywords.filter(
                average_position__lte=10
            ).count()

            return round((top_10_keywords / total_keywords) * 100, 2)

        except Exception as e:
            logger.error("Error calculating search rankings: %s", str(e))
            return 0.0

    def _count_ranking_search_terms(self) -> int:
        """Count total number of search terms we rank for.

        Returns:
            int: Number of ranking search terms
        """
        try:
            return SEOSearchConsoleKeyword.objects.filter(
                search_console_data__project=self.crawl.project
            ).count()
        except Exception as e:
            logger.error("Error counting ranking search terms: %s", str(e))
            return 0

    def _count_total_links(self) -> int:
        """Count total number of internal and external links.

        Returns:
            int: Total number of links
        """
        try:
            return SEOLink.objects.filter(
                metadata__crawl=self.crawl
            ).count()
        except Exception as e:
            logger.error("Error counting total links: %s", str(e))
            return 0

    def _calculate_content_score(self) -> float:
        """Calculate overall content quality score (0-100).

        Returns:
            float: Content quality score
        """
        try:
            analyses = SEOContentAnalysis.objects.filter(
                metadata__crawl=self.crawl
            ).exclude(
                readability_score__isnull=True,
                content_quality_score__isnull=True
            )

            if not analyses.exists():
                return 0.0

            # Get average scores
            avg_scores = analyses.aggregate(
                avg_readability=Avg('readability_score'),
                avg_quality=Avg('content_quality_score')
            )

            readability_score = avg_scores['avg_readability'] or 0
            quality_score = avg_scores['avg_quality'] or 0

            # Calculate final score (50/50 weight)
            return round((readability_score * 0.5) + (quality_score * 0.5), 2)

        except Exception as e:
            logger.error("Error calculating content score: %s", str(e))
            return 0.0

    def _calculate_metadata_score(self) -> float:
        """Calculate metadata optimization score (0-100).

        Returns:
            float: Metadata score
        """
        try:
            metadata_entries = SEOMetadata.objects.filter(crawl=self.crawl)
            total_entries = metadata_entries.count()

            if total_entries == 0:
                return 0.0

            # Check title optimization
            titles_optimized = metadata_entries.filter(
                title__isnull=False,
                title__regex=r'^.{50,60}$'  # Optimal title length
            ).count()

            # Check meta description optimization
            descriptions_optimized = metadata_entries.filter(
                meta_description__isnull=False,
                # Optimal description length
                meta_description__regex=r'^.{120,158}$'
            ).count()

            # Check canonical implementation
            canonicals_implemented = metadata_entries.filter(
                canonical_url__isnull=False
            ).count()

            # Calculate component scores
            title_score = (titles_optimized / total_entries) * 100
            description_score = (descriptions_optimized / total_entries) * 100
            canonical_score = (canonicals_implemented / total_entries) * 100

            # Calculate final score (weighted average)
            metadata_score = (
                title_score * 0.4 +
                description_score * 0.4 +
                canonical_score * 0.2
            )

            return round(metadata_score, 2)

        except Exception as e:
            logger.error("Error calculating metadata score: %s", str(e))
            return 0.0

    def _calculate_technical_score(self) -> float:
        """Calculate technical SEO score (0-100).

        Returns:
            float: Technical SEO score
        """
        try:
            # Get performance data
            performances = SEOPerformance.objects.filter(
                metadata__crawl=self.crawl
            )

            total_pages = performances.count()
            if total_pages == 0:
                return 0.0

            # Calculate component scores
            mobile_score = self._calculate_mobile_optimization_score()
            speed_score = self._calculate_page_speed_score()

            # Count pages with technical issues
            pages_with_issues = SEOIssue.objects.filter(
                crawl=self.crawl,
                category='technical'
            ).values('url').distinct().count()

            issue_score = 100 - ((pages_with_issues / total_pages) * 100)

            # Calculate final score (weighted average)
            technical_score = (
                mobile_score * 0.3 +
                speed_score * 0.3 +
                issue_score * 0.4
            )

            return round(technical_score, 2)

        except Exception as e:
            logger.error("Error calculating technical score: %s", str(e))
            return 0.0

    def _calculate_overall_score(self):
        """Calculate overall SEO score (0-100) based on various metrics."""
        try:
            # Get individual scores
            metadata_score = self._calculate_metadata_score()
            content_score = self._calculate_content_score()
            technical_score = self._calculate_technical_score()
            mobile_score = self._calculate_mobile_score()
            performance_score = self._calculate_page_speed_score()
            ai_seo_score = self._calculate_ai_seo_score()
            
            # Calculate overall score based on weighted average
            total_score = (
                metadata_score * 0.2 +
                content_score * 0.2 +
                technical_score * 0.2 +
                mobile_score * 0.2 +
                performance_score * 0.2 +
                ai_seo_score * 0.2
            )
            
            return max(0, min(100, round(total_score, 2)))
        except Exception as e:
            logger.error("Error calculating overall score: %s", str(e))
            return 0

    def get_keywords_by_language(self) -> Dict:
        """Get keywords grouped by language.
        
        Returns:
            dict: Dictionary with language codes as keys and lists of keyword data as values
        """
        try:
            keywords_by_language = {}
            
            # Query all keywords with their metrics
            keywords = (
                SEOSearchConsoleKeyword.objects
                .filter(search_console_data__project=self.crawl.project)
                .values('keyword', 'language', 'average_position', 'clicks', 'impressions', 'ctr')
                # Sort by language and then by impressions
                .order_by('language', '-impressions')
            )
            
            # Group keywords by language
            for keyword in keywords:
                language = keyword['language'] or 'unknown'
                if language not in keywords_by_language:
                    keywords_by_language[language] = []
                
                keywords_by_language[language].append({
                    'keyword': keyword['keyword'],
                    'average_position': round(keyword['average_position'], 2),
                    'clicks': keyword['clicks'],
                    'impressions': keyword['impressions'],
                    # Convert to percentage
                    'ctr': round(keyword['ctr'] * 100, 2)
                })
            
            return keywords_by_language
            
        except Exception as e:
            logger.error("Error retrieving keywords by language: %s", str(e))
            return {}

    def generate_optimization_checklist(self) -> Dict:
        """Generate SEO optimization checklist with actionable insights."""
        try:
            # 1. HIGH COMPETITION GAPS
            competitor_analyses = SEOCompetitorAnalysis.objects.filter(
                metadata__crawl=self.crawl
            ).order_by('serp_position')
            
            competition_gaps = []
            for analysis in competitor_analyses:
                if analysis.keyword_overlap and analysis.serp_position:
                    gap = analysis.serp_position - 1  # Position difference
                    if gap > 0:
                        competition_gaps.append({
                            'url': analysis.metadata.url,
                            'competitor_url': analysis.competitor_url,
                            'competitor_rank': analysis.serp_position,
                            'position_gap': gap,
                            'traffic_potential': analysis.estimated_traffic or 0
                        })
            
            # 2. TRENDING TOKENS
            trending_keywords = (
                SEOSearchConsoleKeyword.objects .filter(
                    search_console_data__project=self.crawl.project) .values('keyword') .annotate(
                    avg_position=Avg('average_position'),
                    total_impressions=models.Sum('impressions'),
                    impression_growth=models.F('impressions') -
                    models.F('previous_impressions')) .filter(
                    impression_growth__gt=0) .order_by('-impression_growth')[
                    :5])
            
            # 3. TECHNICAL ISSUES
            performances = SEOPerformance.objects.filter(
                metadata__crawl=self.crawl
            )
            
            avg_mobile_speed = performances.aggregate(
                avg_speed=Avg('page_speed_mobile')
            )['avg_speed'] or 0
            
            competitor_speed = (
                competitor_analyses
                .exclude(page_speed_comparison__isnull=True)
                .aggregate(avg_speed=Avg('page_speed_comparison__mobile_speed'))
                ['avg_speed'] or 0
            )
            
            return {
                'high_competition_gaps': {
                    'main_trading_pages': [
                        {
                            'url': gap['url'],
                            'competitor': gap['competitor_url'],
                            'gap': gap['position_gap'],
                            'traffic_potential': f"+{gap['traffic_potential']}% traffic potential"
                        }
                        for gap in sorted(competition_gaps, key=lambda x: x['traffic_potential'], reverse=True)[:3]
                    ]
                },
                'trending_tokens': {
                    'new_token_pages': [
                        {
                            'keyword': kw['keyword'],
                            'search_volume': kw['total_impressions'],
                            'trend': 'growing interest' if kw['impression_growth'] > 100 else 'trending'
                        }
                        for kw in trending_keywords
                    ]
                },
                'technical_updates': {
                    'infrastructure': [
                        {
                            'issue': 'Mobile page speed',
                            'current': f"{avg_mobile_speed:.1f}s",
                            'competitor': f"{competitor_speed:.1f}s",
                            'gap': f"{avg_mobile_speed - competitor_speed:.1f}s"
                        },
                        {
                            'issue': 'Schema markup',
                            'status': self._check_local_schema_implementation()
                        },
                        {
                            'issue': 'Turkish hreflang',
                            'status': self._check_hreflang_implementation('tr')
                        }
                    ]
                }
            }
        except Exception as e:
            logger.error("Error generating optimization checklist: %s", str(e))
            return {}

    def _check_hreflang_implementation(self, language_code: str) -> str:
        """Check if hreflang is implemented for a specific language."""
        try:
            has_hreflang = False
            for metadata in SEOMetadata.objects.filter(crawl=self.crawl):
                if metadata.hreflang_tags and any(
                    tag.get('lang') == language_code 
                    for tag in metadata.hreflang_tags
                ):
                    has_hreflang = True
                    break
            return "Implemented" if has_hreflang else "Missing"
        except Exception as e:
            logger.error("Error checking hreflang implementation: %s", str(e))
            return "Error"

    def _analyze_content_structure(self):
        """Analyze content structure and hierarchy of pages."""
        for metadata in SEOMetadata.objects.filter(crawl=self.crawl):
            try:
                # Analyze heading hierarchy
                headings = {
                    'h1': metadata.h1 if isinstance(metadata.h1, list) else [],
                    'h2': metadata.h2 if isinstance(metadata.h2, list) else []
                }
                
                # Check heading hierarchy issues
                if not headings['h1']:
                    self._create_issue(
                        url=metadata.url,
                        title="Missing H1 heading",
                        description="Page lacks a main heading (H1 tag). Each page should have exactly one H1 heading.",
                        severity="major",
                        category="content")
                elif len(headings['h1']) > 1:
                    self._create_issue(
                        url=metadata.url,
                        title="Multiple H1 headings",
                        description=f"Page has {len(headings['h1'])} H1 headings. Each page should have exactly one H1 heading.",
                        severity="major",
                        category="content")
                
                # Check for proper content structure
                if not headings['h2']:
                    self._create_issue(
                        url=metadata.url,
                        title="Missing subheadings",
                        description="Page lacks H2 subheadings. Consider adding subheadings to improve content structure.",
                        severity="minor",
                        category="content")
                
                # Update content analysis with structure information
                content_analysis = SEOContentAnalysis.objects.get_or_create(metadata=metadata)[
                    0]
                content_analysis.content_structure = {
                    'heading_hierarchy': headings,
                    'has_proper_h1': len(headings['h1']) == 1,
                    'has_subheadings': bool(headings['h2']),
                    'total_headings': len(headings['h1']) + len(headings['h2'])
                }
                content_analysis.save()
                
            except Exception as e:
                logger.error(
                    "Error analyzing content structure for %s: %s",
                    metadata.url,
                    str(e))
                continue

    def _analyze_semantic_relevance(self):
        """Analyze semantic relevance of content across pages."""
        try:
            metadata_entries = SEOMetadata.objects.filter(crawl=self.crawl)
            
            for metadata in metadata_entries:
                try:
                    # Combine all relevant content for analysis
                    content = " ".join(
                        filter(
                            None, [
                                metadata.title, metadata.meta_description, " ".join(
                                    metadata.h1) if isinstance(
                                    metadata.h1, list) else metadata.h1, " ".join(
                                    metadata.h2) if isinstance(
                                    metadata.h2, list) else metadata.h2]))
                    
                    # Get content analysis or create new one
                    content_analysis, created = SEOContentAnalysis.objects.get_or_create(
                        metadata=metadata,
                        defaults={
                            'topical_relevance': 0.0,
                            'semantic_topics': []
                        }
                    )
                    
                    # Extract main topics using TextBlob for basic NLP
                    if content:
                        blob = TextBlob(content)
                        # Get noun phrases as topics
                        topics = [phrase for phrase in blob.noun_phrases]
                        # Calculate relevance score based on topic coherence
                        relevance_score = min(
                            len(set(topics)) / 10.0, 1.0) if topics else 0.0
                        
                        # Update content analysis
                        content_analysis.topical_relevance = relevance_score
                        content_analysis.semantic_topics = list(
                            set(topics))[:10]  # Store top 10 unique topics
                        content_analysis.save()
                        
                        # Create issues for low relevance
                        if relevance_score < 0.3:
                            self._create_issue(
                                url=metadata.url,
                                title="Low semantic relevance",
                                description="The page content lacks clear topical focus and semantic coherence.",
                                severity="major",
                                category="content",
                                recommendation="Consider improving content focus and topical depth.")
                    
                except Exception as e:
                    logger.error(
                        "Error analyzing semantic relevance for %s: %s",
                        metadata.url,
                        str(e))
                    continue
                    
        except Exception as e:
            logger.error("Error in semantic relevance analysis: %s", str(e))

    def _analyze_keyword_optimization(self):
        """Analyze keyword optimization for each page."""
        for metadata in SEOMetadata.objects.filter(crawl=self.crawl):
            try:
                # Get page content
                title = metadata.title or ""
                meta_desc = metadata.meta_description or ""
                h1_content = "".join(
                    metadata.h1) if isinstance(
                    metadata.h1,
                    list) else (
                    metadata.h1 or "")
                content = metadata.content or ""
                
                # Calculate keyword density
                keyword_density = self._calculate_keyword_density(content)
                
                # Check keyword placement in important elements
                top_keywords = sorted(
                    keyword_density.items(),
                    key=lambda x: x[1],
                    reverse=True
                )[:5]
                
                for keyword, density in top_keywords:
                    # Check if keyword is in title
                    if keyword.lower() not in title.lower():
                        self._create_issue(
                            url=metadata.url,
                            title=f"Keyword '{keyword}' missing from title",
                            description="Important keyword not found in page title.",
                            severity="minor",
                            category="content")
                    
                    # Check if keyword is in meta description
                    if keyword.lower() not in meta_desc.lower():
                        self._create_issue(
                            url=metadata.url,
                            title=f"Keyword '{keyword}' missing from meta description",
                            description="Important keyword not found in meta description.",
                            severity="minor",
                            category="content")
                    
                    # Check keyword density
                    if density > 0.05:  # More than 5% density
                        self._create_issue(
                            url=metadata.url,
                            title=f"High keyword density for '{keyword}'",
                            description=f"Keyword density of {density:.1%} may be considered keyword stuffing.",
                            severity="major",
                            category="content")
                
                # Update content analysis
                content_analysis = SEOContentAnalysis.objects.get_or_create(metadata=metadata)[
                    0]
                content_analysis.keyword_density = keyword_density
                content_analysis.save(update_fields=['keyword_density'])
                
            except Exception as e:
                logger.error(
                    "Error analyzing keyword optimization for %s: %s",
                    metadata.url,
                    str(e))

    def _analyze_readability_scores(self):
        """Analyze readability scores for each page."""
        for metadata in SEOMetadata.objects.filter(crawl=self.crawl):
            try:
                content = metadata.content or ""
                
                # Calculate readability score
                readability_score = self._calculate_readability(content)
                
                # Create issues based on readability score
                if readability_score < 30:
                    self._create_issue(
                        url=metadata.url,
                        title="Very poor readability",
                        description="Content is very difficult to read. Consider simplifying the language.",
                        severity="critical",
                        category="content")
                elif readability_score < 50:
                    self._create_issue(
                        url=metadata.url,
                        title="Poor readability",
                        description="Content is difficult to read. Consider making it more accessible.",
                        severity="major",
                        category="content")
                elif readability_score < 60:
                    self._create_issue(
                        url=metadata.url,
                        title="Below average readability",
                        description="Content could be easier to read. Consider improvements.",
                        severity="minor",
                        category="content")
                
                # Update content analysis
                content_analysis = SEOContentAnalysis.objects.get_or_create(metadata=metadata)[
                    0]
                content_analysis.readability_score = readability_score
                content_analysis.save(update_fields=['readability_score'])
                
            except Exception as e:
                logger.error(
                    "Error analyzing readability scores for %s: %s",
                    metadata.url,
                    str(e))

    def _analyze_content_freshness(self):
        """Analyze content freshness and update frequency."""
        for metadata in SEOMetadata.objects.filter(crawl=self.crawl):
            try:
                # Get last modified date from metadata
                last_modified = metadata.last_modified
                
                if not last_modified:
                    self._create_issue(
                        url=metadata.url,
                        title="Missing last modified date",
                        description="Page does not indicate when it was last updated.",
                        severity="minor",
                        category="content")
                    continue
                
                # Calculate days since last update
                days_since_update = (timezone.now() - last_modified).days
                
                # Create issues based on content age
                if days_since_update > 365:  # More than a year old
                    self._create_issue(
                        url=metadata.url,
                        title="Outdated content",
                        description=f"Content has not been updated in {days_since_update} days.",
                        severity="major",
                        category="content")
                elif days_since_update > 180:  # More than 6 months old
                    self._create_issue(
                        url=metadata.url,
                        title="Content needs review",
                        description=f"Content has not been updated in {days_since_update} days.",
                        severity="minor",
                        category="content")
                
                # Check for seasonal content
                if metadata.content and any(
                    season.lower() in metadata.content.lower() for season in [
                        'spring', 'summer', 'autumn', 'winter']):
                    if days_since_update > 90:  # More than 3 months old
                        self._create_issue(
                            url=metadata.url,
                            title="Outdated seasonal content",
                            description="Seasonal content should be updated quarterly.",
                            severity="major",
                            category="content")
                
            except Exception as e:
                logger.error(
                    "Error analyzing content freshness for %s: %s",
                    metadata.url,
                    str(e))

    def _analyze_thin_content(self):
        """Analyze pages for thin content issues."""
        metadata_entries = SEOMetadata.objects.filter(crawl=self.crawl)
        
        for metadata in metadata_entries:
            try:
                # Get page content
                content = metadata.content or ""
                word_count = len(content.split())
                
                # Calculate content-to-HTML ratio
                soup = BeautifulSoup(metadata.raw_html, 'html.parser')
                html_length = len(metadata.raw_html)
                text_length = len(soup.get_text())
                content_ratio = text_length / html_length if html_length > 0 else 0
                
                # Define thresholds
                critical_threshold = 100  # Extremely thin content
                major_threshold = 300    # Thin content
                minor_threshold = 500    # Could be improved
                ratio_threshold = 0.15   # Minimum content-to-HTML ratio
                
                # Create content analysis record if it doesn't exist
                content_analysis, _ = SEOContentAnalysis.objects.get_or_create(
                    metadata=metadata
                )
                
                # Check for thin content issues
                if word_count < critical_threshold:
                    SEOIssue.objects.create(
                        metadata=metadata,
                        issue_type='content',
                        severity='critical',
                        description=f'Extremely thin content: {word_count} words',
                        recommendation='Add more high-quality, relevant content to this page.')
                    content_analysis.content_score = max(
                        0, content_analysis.content_score - 30)
                
                elif word_count < major_threshold:
                    SEOIssue.objects.create(
                        metadata=metadata,
                        issue_type='content',
                        severity='major',
                        description=f'Thin content: {word_count} words',
                        recommendation='Expand the content with more valuable information.')
                    content_analysis.content_score = max(
                        0, content_analysis.content_score - 20)
                
                elif word_count < minor_threshold:
                    SEOIssue.objects.create(
                        metadata=metadata,
                        issue_type='content',
                        severity='minor',
                        description=f'Content could be improved: {word_count} words',
                        recommendation='Consider adding more detailed content.')
                    content_analysis.content_score = max(
                        0, content_analysis.content_score - 10)
                
                # Check content-to-HTML ratio
                if content_ratio < ratio_threshold:
                    SEOIssue.objects.create(
                        metadata=metadata,
                        issue_type='content',
                        severity='major',
                        description=f'Low content-to-HTML ratio: {content_ratio:.2%}',
                        recommendation='Improve content-to-HTML ratio by adding more content or reducing HTML bloat.'
                    )
                    content_analysis.content_score = max(
                        0, content_analysis.content_score - 15)
                
                content_analysis.save()
                
            except Exception as e:
                logger.error(
                    f"Error analyzing thin content for {metadata.url}: {str(e)}")
                continue

    def _analyze_duplicate_content(self):
        """Analyze pages for duplicate content."""
        metadata_entries = SEOMetadata.objects.filter(crawl=self.crawl)
        content_hashes = {}
        
        for metadata in metadata_entries:
            try:
                # Get main content without HTML
                soup = BeautifulSoup(metadata.raw_html, 'html.parser')
                content = soup.get_text().strip()
                
                # Create a simple hash of the content
                content_hash = hash(content)
                
                if content_hash in content_hashes:
                    # Found duplicate content
                    duplicate_url = content_hashes[content_hash]
                    SEOIssue.objects.create(
                        metadata=metadata,
                        issue_type='content',
                        severity='major',
                        description=f'Duplicate content found: Similar to {duplicate_url}',
                        recommendation='Use canonical tags or modify content to be unique.')
                    
                    # Update content score
                    content_analysis = SEOContentAnalysis.objects.get_or_create(metadata=metadata)[
                        0]
                    content_analysis.content_score = max(
                        0, content_analysis.content_score - 25)
                    content_analysis.save()
                else:
                    content_hashes[content_hash] = metadata.url
                    
            except Exception as e:
                logger.error(
                    f"Error analyzing duplicate content for {metadata.url}: {str(e)}")
                continue

    def _analyze_external_linking(self):
        """Analyze external linking structure and identify potential issues."""
        try:
            # Get all external links from the crawl
            external_links = SEOLink.objects.filter(
                metadata__crawl=self.crawl,
                type='external'
            ).select_related('metadata')

            # Group links by target domain
            domain_links = {}
            for link in external_links:
                domain = urlparse(link.url).netloc
                if domain not in domain_links:
                    domain_links[domain] = []
                domain_links[domain].append(link)

            # Analyze external linking patterns
            for domain, links in domain_links.items():
                # Check for broken external links
                broken_links = [
                    link for link in links if link.status_code not in [
                        200, 301, 302]]
                if broken_links:
                    for link in broken_links:
                        self._create_issue(
                            url=link.metadata.url,
                            title="Broken External Link",
                            description=f"External link to {link.url} returns status code {link.status_code}",
                            severity="major",
                            category="links",
                            recommendation="Fix or remove the broken external link.")

                # Check for excessive linking to the same domain
                if len(links) > 50:  # Threshold for excessive external linking
                    self._create_issue(
                        url=self.crawl.project.site_url,
                        title="Excessive External Linking",
                        description=f"Found {len(links)} links to domain {domain}. This may be seen as spammy.",
                        severity="minor",
                        category="links",
                        recommendation="Consider reducing the number of links to this external domain.")

                # Check for nofollow attributes on external links
                nofollow_missing = [
                    link for link in links if 'nofollow' not in (
                        link.rel_attributes or [])]
                if nofollow_missing:
                    self._create_issue(
                        url=self.crawl.project.site_url,
                        title="Missing Nofollow Attributes",
                        description=f"Found {len(nofollow_missing)} external links without nofollow attributes",
                        severity="minor",
                        category="links",
                        recommendation="Consider adding nofollow attributes to external links where appropriate."
                    )

            return True
        except Exception as e:
            logger.error("Error analyzing external linking: %s", str(e))
            return False

    def _analyze_mobile_performance(self) -> Dict:
        """Analyze mobile performance metrics.

        Returns:
            Dict with mobile performance analysis
        """
        try:
            performances = SEOPerformance.objects.filter(
                metadata__crawl=self.crawl
            ).exclude(
                page_speed_mobile__isnull=True,
                mobile_friendly__isnull=True
            )

            total_pages = performances.count()
            if total_pages == 0:
                return {
                    'mobile_score': 0.0,
                    'friendly_percentage': 0.0,
                    'speed_distribution': {},
                    'issues': []
                }

            # Calculate mobile metrics
            mobile_friendly = performances.filter(mobile_friendly=True).count()
            friendly_percentage = (mobile_friendly / total_pages) * 100

            # Calculate speed distribution
            speeds = list(
                performances.values_list(
                    'page_speed_mobile',
                    flat=True))
            speed_distribution = {}
            for speed in speeds:
                if speed is not None:
                    range_key = f"{(speed // 10) * 10}-{((speed // 10) * 10) + 9}"
                    speed_distribution[range_key] = speed_distribution.get(
                        range_key, 0) + 1

            # Get mobile issues
            issues = []
            for perf in performances.exclude(mobile_issues=[])[:10]:
                issues.append({
                    'url': perf.metadata.url,
                    'issues': perf.mobile_issues
                })

            return {
                'mobile_score': round(sum(s for s in speeds if s is not None) / len(speeds), 2),
                'friendly_percentage': round(friendly_percentage, 2),
                'speed_distribution': speed_distribution,
                'issues': issues
            }

        except Exception as e:
            logger.error("Error analyzing mobile performance: %s", str(e))
            return {
                'mobile_score': 0.0,
                'friendly_percentage': 0.0,
                'speed_distribution': {},
                'issues': []
            }

    def _analyze_language_coverage(self) -> Dict:
        """Analyze language coverage across pages.

        Returns:
            Dict with language coverage analysis
        """
        try:
            metadata_entries = SEOMetadata.objects.filter(
                crawl=self.crawl,
                language__isnull=False
            )

            total_pages = metadata_entries.count()
            if total_pages == 0:
                return {
                    'languages': {},
                    'missing_lang': 0,
                    'hreflang_coverage': 0.0
                }

            # Count pages by language
            languages = {}
            for metadata in metadata_entries:
                lang = metadata.language
                languages[lang] = languages.get(lang, 0) + 1

            # Count pages missing language tag
            missing_lang = SEOMetadata.objects.filter(
                crawl=self.crawl,
                language__isnull=True
            ).count()

            # Check hreflang implementation
            pages_with_hreflang = metadata_entries.filter(
                schema_markup__contains='hreflang'
            ).count()

            hreflang_coverage = (
                pages_with_hreflang / total_pages) * 100 if total_pages > 0 else 0

            return {
                'languages': languages,
                'missing_lang': missing_lang,
                'hreflang_coverage': round(hreflang_coverage, 2)
            }

        except Exception as e:
            logger.error("Error analyzing language coverage: %s", str(e))
            return {
                'languages': {},
                'missing_lang': 0,
                'hreflang_coverage': 0.0
            }

    def _analyze_translation_metrics(self) -> Dict:
        """Analyze translation quality metrics.

        Returns:
            Dict with translation metrics
        """
        try:
            # Get all content analyses grouped by language
            analyses = SEOContentAnalysis.objects.filter(
                metadata__crawl=self.crawl,
                metadata__language__isnull=False
            ).select_related('metadata')

            if not analyses.exists():
                return {
                    'translation_scores': {},
                    'content_parity': 0.0,
                    'issues': []
                }

            # Group analyses by language
            lang_groups = {}
            for analysis in analyses:
                lang = analysis.metadata.language
                if lang not in lang_groups:
                    lang_groups[lang] = []
                lang_groups[lang].append(analysis)

            # Calculate metrics for each language
            translation_scores = {}
            content_lengths = {}
            translation_issues = []

            for lang, group in lang_groups.items():
                # Calculate average content quality score
                quality_scores = [
                    a.content_quality_score for a in group if a.content_quality_score]
                avg_score = sum(quality_scores) / \
                    len(quality_scores) if quality_scores else 0
                translation_scores[lang] = round(avg_score, 2)

                # Calculate average content length
                lengths = []
                for analysis in group:
                    if analysis.metadata.content:
                        lengths.append(
                            len(re.findall(r'\w+', analysis.metadata.content)))
                content_lengths[lang] = sum(
                    lengths) / len(lengths) if lengths else 0

                # Check for potential issues
                for analysis in group:
                    if analysis.content_quality_score and analysis.content_quality_score < 60:
                        translation_issues.append({
                            'url': analysis.metadata.url,
                            'language': lang,
                            'score': analysis.content_quality_score,
                            'type': 'low_quality'
                        })

            # Calculate content parity
            if len(content_lengths) > 1:
                avg_length = sum(content_lengths.values()) / \
                    len(content_lengths)
                variances = []
                for length in content_lengths.values():
                    variance = abs(length - avg_length) / avg_length
                    variances.append(variance)
                content_parity = 100 * (1 - (sum(variances) / len(variances)))
            else:
                content_parity = 100.0

            return {
                'translation_scores': translation_scores,
                'content_parity': round(content_parity, 2),
                'issues': translation_issues[:10]  # Return top 10 issues
            }

        except Exception as e:
            logger.error("Error analyzing translation metrics: %s", str(e))
            return {
                'translation_scores': {},
                'content_parity': 0.0,
                'issues': []
            }

    def _analyze_market_specific_metrics(self) -> Dict:
        """Analyze market-specific SEO metrics.

        Returns:
            Dict with market-specific analysis
        """
        try:
            # Get competitor analyses by market
            competitor_analyses = SEOCompetitorAnalysis.objects.filter(
                metadata__crawl=self.crawl
            ).select_related('metadata')

            if not competitor_analyses.exists():
                return {
                    'market_share': {},
                    'competition_level': {},
                    'opportunities': []
                }

            # Group analyses by market/domain
            market_data = {}
            for analysis in competitor_analyses:
                domain = analysis.competitor_domain
                if domain not in market_data:
                    market_data[domain] = {
                        'traffic': 0,
                        'keywords': 0,
                        'ranking_keywords': set(),
                        'opportunities': []
                    }

                # Aggregate metrics
                if analysis.estimated_traffic:
                    market_data[domain]['traffic'] += analysis.estimated_traffic

                if analysis.keyword_rankings:
                    market_data[domain]['keywords'] += len(
                        analysis.keyword_rankings)
                    market_data[domain]['ranking_keywords'].update(
                        kw for kw, rank in analysis.keyword_rankings.items()
                        if rank <= 10
                    )

                # Identify opportunities
                if analysis.content_gaps:
                    market_data[domain]['opportunities'].extend([
                        {
                            'type': gap.get('type'),
                            'keyword': gap.get('keyword'),
                            'volume': gap.get('volume', 0),
                            'difficulty': gap.get('difficulty', 0)
                        }
                        for gap in analysis.content_gaps
                        if gap.get('priority', 'low') in ['high', 'medium']
                    ])

            # Calculate market share and competition level
            total_traffic = sum(data['traffic']
                                for data in market_data.values())
            total_keywords = sum(len(data['ranking_keywords'])
                                 for data in market_data.values())

            market_share = {}
            competition_level = {}

            for domain, data in market_data.items():
                # Calculate market share
                if total_traffic > 0:
                    market_share[domain] = round(
                        (data['traffic'] / total_traffic) * 100, 2)
                else:
                    market_share[domain] = 0.0

                # Calculate competition level (0-100)
                keyword_share = len(
                    data['ranking_keywords']) / total_keywords if total_keywords > 0 else 0
                traffic_share = data['traffic'] / \
                    total_traffic if total_traffic > 0 else 0
                competition_level[domain] = round(
                    (keyword_share * 0.6 + traffic_share * 0.4) * 100, 2)

            # Aggregate opportunities across markets
            all_opportunities = []
            for domain, data in market_data.items():
                for opp in data['opportunities']:
                    opp['market'] = domain
                    all_opportunities.append(opp)

            # Sort opportunities by volume and difficulty
            all_opportunities.sort(
                key=lambda x: (x.get('volume', 0), -x.get('difficulty', 0)),
                reverse=True
            )

            return {
                'market_share': market_share,
                'competition_level': competition_level,
                # Return top 10 opportunities
                'opportunities': all_opportunities[:10]
            }

        except Exception as e:
            logger.error("Error analyzing market-specific metrics: %s", str(e))
            return {
                'market_share': {},
                'competition_level': {},
                'opportunities': []
            }

    def _create_issue(
            self,
            url: str,
            title: str,
            description: str,
            severity: str,
            category: str,
            recommendation: str = None) -> None:
        """Create an SEO issue.

        Args:
            url: URL where the issue was found
            title: Issue title
            description: Issue description
            severity: Issue severity (critical, major, minor)
            category: Issue category
            recommendation: Optional recommendation for fixing the issue
        """
        try:
            SEOIssue.objects.create(
                crawl=self.crawl,
                url=url,
                title=title,
                description=description,
                severity=severity,
                category=category,
                recommendation=recommendation or f"Fix the {severity} {category} issue: {title}"
            )
        except Exception as e:
            logger.error("Error creating issue: %s", str(e))

    def _get_schema_issues(self) -> List[Dict]:
        """Get schema markup implementation issues.

        Returns:
            List of schema issues
        """
        try:
            issues = []
            metadata_entries = SEOMetadata.objects.filter(
                crawl=self.crawl,
                schema_markup__isnull=False
            )

            for metadata in metadata_entries:
                if not metadata.schema_markup:
                    continue

                for schema in metadata.schema_markup:
                    try:
                        if isinstance(schema, str):
                            schema_data = json.loads(schema)

                            # Check for required properties
                            if '@type' not in schema_data:
                                issues.append({
                                    'url': metadata.url,
                                    'type': 'missing_type',
                                    'message': 'Schema markup missing @type property'
                                })
                                continue

                            schema_type = schema_data['@type']

                            # Check LocalBusiness schema
                            if schema_type == 'LocalBusiness':
                                required_props = [
                                    'name', 'address', 'telephone']
                                missing_props = [
                                    prop for prop in required_props
                                    if prop not in schema_data
                                ]

                                if missing_props:
                                    issues.append({
                                        'url': metadata.url,
                                        'type': 'incomplete_schema',
                                        'message': f'LocalBusiness schema missing properties: {", ".join(missing_props)}'
                                    })

                            # Check Product schema
                            elif schema_type == 'Product':
                                required_props = [
                                    'name', 'description', 'offers']
                                missing_props = [
                                    prop for prop in required_props
                                    if prop not in schema_data
                                ]

                                if missing_props:
                                    issues.append({
                                        'url': metadata.url,
                                        'type': 'incomplete_schema',
                                        'message': f'Product schema missing properties: {", ".join(missing_props)}'
                                    })

                    except json.JSONDecodeError:
                        issues.append({
                            'url': metadata.url,
                            'type': 'invalid_json',
                            'message': 'Invalid JSON in schema markup'
                        })
                        continue

            return issues

        except Exception as e:
            logger.error("Error getting schema issues: %s", str(e))
            return []

    def _generate_prioritized_recommendations(self) -> List[Dict]:
        """Generate prioritized SEO recommendations.

        Returns:
            List of prioritized recommendations
        """
        try:
            recommendations = []

            # Get all issues
            issues = SEOIssue.objects.filter(crawl=self.crawl)

            # Group issues by category and severity
            issue_groups = {}
            for issue in issues:
                key = (issue.category, issue.severity)
                if key not in issue_groups:
                    issue_groups[key] = []
                issue_groups[key].append(issue)

            # Priority weights
            severity_weights = {
                'critical': 1.0,
                'major': 0.7,
                'minor': 0.4
            }

            category_weights = {
                'technical': 1.0,
                'content': 0.9,
                'mobile': 0.8,
                'performance': 0.8,
                'links': 0.7,
                'metadata': 0.7,
                'structured_data': 0.6,
                'international': 0.6,
                'ai_seo': 0.5
            }

            # Generate recommendations for each group
            for (category, severity), group in issue_groups.items():
                # Calculate priority score
                severity_weight = severity_weights.get(severity, 0.3)
                category_weight = category_weights.get(category, 0.5)
                priority_score = severity_weight * category_weight * len(group)

                # Group similar issues
                issue_patterns = {}
                for issue in group:
                    pattern = issue.title.split(
                        ':')[0] if ':' in issue.title else issue.title
                    if pattern not in issue_patterns:
                        issue_patterns[pattern] = {
                            'count': 0,
                            'urls': [],
                            'recommendation': issue.recommendation
                        }
                    issue_patterns[pattern]['count'] += 1
                    issue_patterns[pattern]['urls'].append(issue.url)

                # Create recommendations for patterns
                for pattern, data in issue_patterns.items():
                    recommendations.append({
                        'title': pattern,
                        'category': category,
                        'severity': severity,
                        'priority_score': priority_score,
                        'affected_pages': data['count'],
                        # Include up to 5 example URLs
                        'sample_urls': data['urls'][:5],
                        'recommendation': data['recommendation'],
                        'estimated_impact': self._estimate_recommendation_impact(
                            category, severity, data['count']
                        )
                    })

            # Sort recommendations by priority score
            recommendations.sort(
                key=lambda x: x['priority_score'],
                reverse=True)

            return recommendations[:20]  # Return top 20 recommendations

        except Exception as e:
            logger.error(
                "Error generating prioritized recommendations: %s",
                str(e))
            return []

    def _estimate_recommendation_impact(
            self,
            category: str,
            severity: str,
            affected_pages: int) -> Dict:
        """Estimate the potential impact of implementing a recommendation.

        Args:
            category: Issue category
            severity: Issue severity
            affected_pages: Number of affected pages

        Returns:
            Dict with impact estimates
        """
        try:
            # Base impact scores by severity
            base_scores = {
                'critical': {
                    'traffic': 0.3, 'rankings': 0.3, 'conversions': 0.3}, 'major': {
                    'traffic': 0.2, 'rankings': 0.2, 'conversions': 0.2}, 'minor': {
                    'traffic': 0.1, 'rankings': 0.1, 'conversions': 0.1}}

            # Category-specific impact multipliers
            category_multipliers = {
                'technical': {'traffic': 1.2, 'rankings': 1.3, 'conversions': 1.0},
                'content': {'traffic': 1.4, 'rankings': 1.4, 'conversions': 1.2},
                'mobile': {'traffic': 1.3, 'rankings': 1.2, 'conversions': 1.4},
                'performance': {'traffic': 1.2, 'rankings': 1.1, 'conversions': 1.5},
                'links': {'traffic': 1.3, 'rankings': 1.4, 'conversions': 1.0},
                'metadata': {'traffic': 1.2, 'rankings': 1.2, 'conversions': 1.1},
                'structured_data': {'traffic': 1.1, 'rankings': 1.2, 'conversions': 1.1},
                'international': {'traffic': 1.2, 'rankings': 1.2, 'conversions': 1.2},
                'ai_seo': {'traffic': 1.3, 'rankings': 1.3, 'conversions': 1.1}
            }

            # Get base scores for severity
            base = base_scores.get(
                severity, {
                    'traffic': 0.1, 'rankings': 0.1, 'conversions': 0.1})

            # Get multipliers for category
            multipliers = category_multipliers.get(
                category, {'traffic': 1.0, 'rankings': 1.0, 'conversions': 1.0})

            # Calculate page coverage factor (logarithmic scale)
            total_pages = SEOMetadata.objects.filter(crawl=self.crawl).count()
            coverage_factor = math.log2(
                affected_pages + 1) / math.log2(total_pages + 1) if total_pages > 0 else 0

            # Calculate final impact scores
            impact = {
                'traffic': min(
                    100,
                    base['traffic'] *
                    multipliers['traffic'] *
                    coverage_factor *
                    100),
                'rankings': min(
                    100,
                    base['rankings'] *
                    multipliers['rankings'] *
                    coverage_factor *
                    100),
                'conversions': min(
                    100,
                    base['conversions'] *
                    multipliers['conversions'] *
                    coverage_factor *
                    100)}

            # Calculate implementation difficulty (1-5 scale)
            difficulty_factors = {
                'technical': 4,
                'content': 3,
                'mobile': 4,
                'performance': 5,
                'links': 2,
                'metadata': 1,
                'structured_data': 3,
                'international': 4,
                'ai_seo': 3
            }

            base_difficulty = difficulty_factors.get(category, 3)
            difficulty = min(
                5, max(1, base_difficulty + (coverage_factor * 2)))

            return {
                'estimated_impact': impact,
                'implementation_difficulty': round(
                    difficulty,
                    1),
                'priority': 'high' if sum(
                    impact.values()) /
                3 > 50 else 'medium' if sum(
                    impact.values()) /
                3 > 25 else 'low'}

        except Exception as e:
            logger.error("Error estimating recommendation impact: %s", str(e))
            return {
                'estimated_impact': {
                    'traffic': 0,
                    'rankings': 0,
                    'conversions': 0},
                'implementation_difficulty': 3,
                'priority': 'low'}

    def _generate_action_plan(self) -> Dict:
        """Generate an actionable SEO improvement plan.

        Returns:
            Dict with prioritized action plan
        """
        try:
            # Get prioritized recommendations
            recommendations = self._generate_prioritized_recommendations()

            # Group recommendations by timeframe
            quick_wins = []
            short_term = []
            long_term = []

            for rec in recommendations:
                impact = rec.get('estimated_impact', {})
                difficulty = rec.get('implementation_difficulty', 3)

                # Calculate effort-to-impact ratio
                avg_impact = sum(
                    impact.get(
                        'estimated_impact',
                        {}).values()) / 3
                ratio = avg_impact / difficulty if difficulty > 0 else 0

                # Categorize based on ratio and difficulty
                if ratio >= 20 and difficulty <= 2:
                    quick_wins.append(rec)
                elif difficulty <= 3 or ratio >= 10:
                    short_term.append(rec)
                else:
                    long_term.append(rec)

            # Calculate estimated resources needed
            resource_estimates = {
                'development_hours': sum(
                    rec.get('implementation_difficulty', 3) * 2
                    for rec in recommendations
                ),
                'content_hours': sum(
                    rec.get('implementation_difficulty', 3) * 3
                    for rec in recommendations
                    if rec.get('category') == 'content'
                )
            }

            # Calculate potential impact
            total_impact = {
                'traffic': 0,
                'rankings': 0,
                'conversions': 0
            }

            for rec in recommendations:
                impact = rec.get(
                    'estimated_impact', {}).get(
                    'estimated_impact', {})
                for metric in total_impact:
                    total_impact[metric] += impact.get(metric, 0)

            # Average the total impact
            for metric in total_impact:
                total_impact[metric] = round(
                    total_impact[metric] / len(recommendations) if recommendations else 0, 2)

            return {
                'quick_wins': quick_wins[:5],
                'short_term': short_term[:10],
                'long_term': long_term[:5],
                'resource_estimates': resource_estimates,
                'potential_impact': total_impact,
                'timeline_estimates': {
                    'quick_wins': '1-2 weeks',
                    'short_term': '1-3 months',
                    'long_term': '3-6 months'
                }
            }

        except Exception as e:
            logger.error("Error generating action plan: %s", str(e))
            return {
                'quick_wins': [],
                'short_term': [],
                'long_term': [],
                'resource_estimates': {
                    'development_hours': 0,
                    'content_hours': 0},
                'potential_impact': {
                    'traffic': 0,
                    'rankings': 0,
                    'conversions': 0},
                'timeline_estimates': {
                    'quick_wins': '1-2 weeks',
                    'short_term': '1-3 months',
                    'long_term': '3-6 months'}}

    def _analyze_trends(self) -> Dict:
        """Analyze SEO trends over time.

        Returns:
            Dict with trend analysis
        """
        try:
            # Get historical crawls for this project
            historical_crawls = SEOCrawl.objects.filter(
                project=self.project,
                completed_at__lt=self.crawl.completed_at
            ).order_by('-completed_at')[:5]  # Last 5 crawls

            trends = {
                'metrics': self._analyze_metric_trends(historical_crawls),
                'issues': self._analyze_issue_trends(historical_crawls),
                'rankings': self._analyze_ranking_trends(historical_crawls),
                'content': self._analyze_content_trends(historical_crawls)
            }

            return trends

        except Exception as e:
            logger.error("Error analyzing trends: %s", str(e))
            return {}

    def _analyze_metric_trends(self, historical_crawls) -> Dict:
        """Analyze trends in key metrics.

        Args:
            historical_crawls: QuerySet of historical crawls

        Returns:
            Dict with metric trends
        """
        try:
            metric_trends = {
                'scores': {
                    'metadata': [],
                    'content': [],
                    'technical': [],
                    'mobile': [],
                    'overall': []
                },
                'performance': {
                    'page_speed': [],
                    'mobile_friendly': []
                }
            }

            # Include current crawl
            all_crawls = list(historical_crawls) + [self.crawl]

            for crawl in all_crawls:
                # Get report for this crawl
                report = SEOReport.objects.filter(crawl=crawl).first()
                if not report:
                    continue

                # Add scores
                metric_trends['scores']['metadata'].append({
                    'date': crawl.completed_at,
                    'value': report.metadata_score
                })

                metric_trends['scores']['content'].append({
                    'date': crawl.completed_at,
                    'value': report.content_score
                })

                metric_trends['scores']['technical'].append({
                    'date': crawl.completed_at,
                    'value': report.technical_score
                })

                metric_trends['scores']['mobile'].append({
                    'date': crawl.completed_at,
                    'value': report.mobile_score
                })

                metric_trends['scores']['overall'].append({
                    'date': crawl.completed_at,
                    'value': report.overall_score
                })

                # Add performance metrics
                avg_speed = SEOPerformance.objects.filter(
                    metadata__crawl=crawl
                ).aggregate(
                    avg_speed=Avg('page_speed_mobile')
                )['avg_speed'] or 0

                mobile_friendly = SEOPerformance.objects.filter(
                    metadata__crawl=crawl,
                    mobile_friendly=True
                ).count() / SEOPerformance.objects.filter(
                    metadata__crawl=crawl
                ).count() * 100

                metric_trends['performance']['page_speed'].append({
                    'date': crawl.completed_at,
                    'value': avg_speed
                })

                metric_trends['performance']['mobile_friendly'].append({
                    'date': crawl.completed_at,
                    'value': mobile_friendly
                })

            return metric_trends

        except Exception as e:
            logger.error("Error analyzing metric trends: %s", str(e))
            return {}

    def _analyze_issue_trends(self, historical_crawls) -> Dict:
        """Analyze trends in SEO issues.

        Args:
            historical_crawls: QuerySet of historical crawls

        Returns:
            Dict with issue trends
        """
        try:
            issue_trends = {
                'total_issues': [],
                'by_severity': {
                    'critical': [],
                    'major': [],
                    'minor': []
                },
                'by_category': {},
                'resolved_issues': [],
                'new_issues': []
            }

            # Include current crawl
            all_crawls = list(historical_crawls) + [self.crawl]
            previous_issues = set()

            for crawl in all_crawls:
                # Get issues for this crawl
                issues = SEOIssue.objects.filter(crawl=crawl)

                # Track total issues
                issue_trends['total_issues'].append({
                    'date': crawl.completed_at,
                    'value': issues.count()
                })

                # Track issues by severity
                for severity in ['critical', 'major', 'minor']:
                    count = issues.filter(severity=severity).count()
                    issue_trends['by_severity'][severity].append({
                        'date': crawl.completed_at,
                        'value': count
                    })

                # Track issues by category
                for issue in issues:
                    if issue.category not in issue_trends['by_category']:
                        issue_trends['by_category'][issue.category] = []

                    issue_trends['by_category'][issue.category].append({
                        'date': crawl.completed_at,
                        'value': issues.filter(category=issue.category).count()
                    })

                # Track resolved and new issues
                current_issues = {(issue.url, issue.title) for issue in issues}

                if previous_issues:
                    resolved = previous_issues - current_issues
                    new = current_issues - previous_issues

                    issue_trends['resolved_issues'].append({
                        'date': crawl.completed_at,
                        'value': len(resolved)
                    })

                    issue_trends['new_issues'].append({
                        'date': crawl.completed_at,
                        'value': len(new)
                    })

                previous_issues = current_issues

            return issue_trends

        except Exception as e:
            logger.error("Error analyzing issue trends: %s", str(e))
            return {}

    def _analyze_ranking_trends(self, historical_crawls) -> Dict:
        """Analyze trends in search rankings.

        Args:
            historical_crawls: QuerySet of historical crawls

        Returns:
            Dict with ranking trends
        """
        try:
            ranking_trends = {
                'average_position': [],
                'visibility': [],
                'top_10_keywords': [],
                'keyword_distribution': []
            }

            # Include current crawl
            all_crawls = list(historical_crawls) + [self.crawl]

            for crawl in all_crawls:
                # Get search console data for this period
                search_data = SEOSearchConsoleData.objects.filter(
                    project=crawl.project,
                    date__lte=crawl.completed_at
                )

                if not search_data.exists():
                    continue

                # Calculate average position
                avg_position = search_data.aggregate(
                    avg_position=Avg('average_position')
                )['avg_position'] or 0

                ranking_trends['average_position'].append({
                    'date': crawl.completed_at,
                    'value': round(avg_position, 2)
                })

                # Calculate visibility
                visibility = self._calculate_visibility_percentage()
                ranking_trends['visibility'].append({
                    'date': crawl.completed_at,
                    'value': visibility
                })

                # Count top 10 keywords
                top_10 = search_data.filter(
                    average_position__lte=10
                ).count()

                ranking_trends['top_10_keywords'].append({
                    'date': crawl.completed_at,
                    'value': top_10
                })

                # Calculate keyword position distribution
                distribution = {
                    '1-3': search_data.filter(average_position__lte=3).count(),
                    '4-10': search_data.filter(average_position__gt=3, average_position__lte=10).count(),
                    '11-20': search_data.filter(average_position__gt=10, average_position__lte=20).count(),
                    '21-50': search_data.filter(average_position__gt=20, average_position__lte=50).count(),
                    '51+': search_data.filter(average_position__gt=50).count()
                }

                ranking_trends['keyword_distribution'].append({
                    'date': crawl.completed_at,
                    'distribution': distribution
                })

            return ranking_trends

        except Exception as e:
            logger.error("Error analyzing ranking trends: %s", str(e))
            return {}

    def _analyze_content_trends(self, historical_crawls) -> Dict:
        """Analyze trends in content metrics.

        Args:
            historical_crawls: QuerySet of historical crawls

        Returns:
            Dict with content trends
        """
        try:
            content_trends = {
                'avg_word_count': [],
                'readability': [],
                'content_quality': [],
                'keyword_coverage': []
            }

            # Include current crawl
            all_crawls = list(historical_crawls) + [self.crawl]

            for crawl in all_crawls:
                # Get content analyses for this crawl
                analyses = SEOContentAnalysis.objects.filter(
                    metadata__crawl=crawl
                )

                if not analyses.exists():
                    continue

                # Calculate average word count
                avg_word_count = analyses.aggregate(
                    avg_count=Avg('metadata__word_count')
                )['avg_count'] or 0

                content_trends['avg_word_count'].append({
                    'date': crawl.completed_at,
                    'value': round(avg_word_count, 2)
                })

                # Calculate average readability
                avg_readability = analyses.aggregate(
                    avg_score=Avg('readability_score')
                )['avg_score'] or 0

                content_trends['readability'].append({
                    'date': crawl.completed_at,
                    'value': round(avg_readability, 2)
                })

                # Calculate average content quality
                avg_quality = analyses.aggregate(
                    avg_score=Avg('content_quality_score')
                )['avg_score'] or 0

                content_trends['content_quality'].append({
                    'date': crawl.completed_at,
                    'value': round(avg_quality, 2)
                })

                # Calculate keyword coverage
                coverage_data = self._analyze_keyword_coverage()
                avg_coverage = sum(
                    kw['coverage_percentage']
                    for kw in coverage_data.get('top_keywords', [])
                ) / len(coverage_data.get('top_keywords', [1])) if coverage_data.get('top_keywords') else 0

                content_trends['keyword_coverage'].append({
                    'date': crawl.completed_at,
                    'value': round(avg_coverage, 2)
                })

            return content_trends

        except Exception as e:
            logger.error("Error analyzing content trends: %s", str(e))
            return {}

    def _generate_trend_insights(self) -> List[Dict]:
        """Generate insights from trend analysis.

        Returns:
            List of trend insights
        """
        try:
            trends = self._analyze_trends()
            insights = []

            # Analyze score trends
            for metric, data in trends.get(
                    'metrics', {}).get(
                    'scores', {}).items():
                if len(data) >= 2:
                    current = data[-1]['value']
                    previous = data[-2]['value']
                    change = current - previous

                    if abs(change) >= 5:  # Significant change threshold
                        insights.append({
                            'type': 'score_change',
                            'metric': metric,
                            'change': round(change, 2),
                            'current': round(current, 2),
                            'previous': round(previous, 2),
                            'direction': 'improved' if change > 0 else 'declined',
                            'significance': 'significant' if abs(change) >= 10 else 'moderate',
                            'priority': 'high' if abs(change) >= 10 else 'medium'
                        })

            # Analyze issue trends
            issue_trends = trends.get('issues', {})
            if issue_trends:
                total_issues = issue_trends.get('total_issues', [])
                if len(total_issues) >= 2:
                    current = total_issues[-1]['value']
                    previous = total_issues[-2]['value']
                    change = current - previous

                    if abs(change) >= 5:
                        insights.append({
                            'type': 'issue_change',
                            'change': change,
                            'current': current,
                            'previous': previous,
                            'direction': 'increased' if change > 0 else 'decreased',
                            'priority': 'high' if change > 0 else 'info'
                        })

            # Analyze ranking trends
            ranking_trends = trends.get('rankings', {})
            if ranking_trends:
                position_data = ranking_trends.get('average_position', [])
                if len(position_data) >= 2:
                    current = position_data[-1]['value']
                    previous = position_data[-2]['value']
                    change = previous - current  # Note: Lower position is better

                    if abs(change) >= 1:
                        insights.append({
                            'type': 'ranking_change',
                            'change': abs(change),
                            'current': current,
                            'previous': previous,
                            'direction': 'improved' if change > 0 else 'declined',
                            'priority': 'high' if abs(change) >= 3 else 'medium'
                        })

            # Sort insights by priority
            priority_weights = {'high': 3, 'medium': 2, 'low': 1}
            insights.sort(
                key=lambda x: priority_weights.get(
                    x.get(
                        'priority',
                        'low'),
                    0),
                reverse=True)

            return insights

        except Exception as e:
            logger.error("Error generating trend insights: %s", str(e))
            return []

    def _generate_report_summary(self) -> Dict:
        """Generate a summary of the SEO analysis report.

        Returns:
            Dict with report summary
        """
        try:
            summary = {
                'overview': {
                    'total_pages': SEOMetadata.objects.filter(
                        crawl=self.crawl).count(),
                    'total_issues': SEOIssue.objects.filter(
                        crawl=self.crawl).count(),
                    'scores': {
                        'overall': self._calculate_overall_score(),
                        'content': self._calculate_content_score(),
                        'technical': self._calculate_technical_score(),
                        'mobile': self._calculate_mobile_score()}},
                'key_metrics': {
                    'organic_keywords': self._calculate_organic_keywords(),
                    'avg_position': self._calculate_average_position(),
                    'visibility': self._calculate_visibility_percentage(),
                    'mobile_friendly': self._count_mobile_friendly_pages()},
                'critical_issues': self._get_critical_issues(),
                'quick_wins': self._get_quick_wins(),
                'trends': self._generate_trend_insights(),
                'action_plan': self._generate_action_plan()}

            return summary

        except Exception as e:
            logger.error("Error generating report summary: %s", str(e))
            return {}

    def _get_critical_issues(self) -> List[Dict]:
        """Get list of critical issues.

        Returns:
            List of critical issues
        """
        try:
            critical_issues = []
            issues = SEOIssue.objects.filter(
                crawl=self.crawl,
                severity='critical'
            ).order_by('-impact_score')

            for issue in issues:
                critical_issues.append({
                    'title': issue.title,
                    'description': issue.description,
                    'category': issue.category,
                    'url': issue.url,
                    'recommendation': issue.recommendation,
                    'impact_score': issue.impact_score
                })

            return critical_issues

        except Exception as e:
            logger.error("Error getting critical issues: %s", str(e))
            return []

    def _get_quick_wins(self) -> List[Dict]:
        """Get list of quick win opportunities.

        Returns:
            List of quick win opportunities
        """
        try:
            quick_wins = []

            # Get issues that are easy to fix
            issues = SEOIssue.objects.filter(
                crawl=self.crawl
            ).exclude(
                severity='critical'
            ).order_by('-impact_score')

            for issue in issues:
                # Estimate implementation difficulty
                difficulty = self._estimate_implementation_difficulty(issue)

                if difficulty <= 2:  # Easy to implement
                    quick_wins.append({
                        'title': issue.title,
                        'description': issue.description,
                        'category': issue.category,
                        'url': issue.url,
                        'recommendation': issue.recommendation,
                        'impact_score': issue.impact_score,
                        'difficulty': difficulty
                    })

            # Sort by impact score and return top 10
            quick_wins.sort(key=lambda x: x['impact_score'], reverse=True)
            return quick_wins[:10]

        except Exception as e:
            logger.error("Error getting quick wins: %s", str(e))
            return []

    def _estimate_implementation_difficulty(self, issue: SEOIssue) -> int:
        """Estimate difficulty of implementing a fix for an issue.

        Args:
            issue: SEOIssue instance

        Returns:
            int: Difficulty score (1-5)
        """
        try:
            # Base difficulty by category
            base_difficulty = {
                'metadata': 1,
                'content': 2,
                'technical': 3,
                'mobile': 3,
                'performance': 4,
                'structured_data': 3,
                'international': 4,
                'ai_seo': 3
            }.get(issue.category, 2)

            # Adjust for severity
            severity_modifier = {
                'critical': 1,
                'major': 0,
                'minor': -1
            }.get(issue.severity, 0)

            # Adjust for number of affected pages
            affected_pages = SEOIssue.objects.filter(
                crawl=self.crawl,
                title=issue.title
            ).count()

            if affected_pages > 100:
                pages_modifier = 2
            elif affected_pages > 10:
                pages_modifier = 1
            else:
                pages_modifier = 0

            # Calculate final difficulty
            difficulty = base_difficulty + severity_modifier + pages_modifier

            # Clamp between 1 and 5
            return max(1, min(5, difficulty))

        except Exception as e:
            logger.error(
                "Error estimating implementation difficulty: %s",
                str(e))
            return 3

    def _analyze_broken_links(self):
        """Analyze broken links and create issues for them."""
        try:
            # Get all links from the current crawl
            links = SEOLink.objects.filter(
                metadata__crawl=self.crawl
            ).select_related('metadata')

            # Group links by status code
            broken_links = links.filter(
                status_code__gte=400
            ).order_by('status_code')

            # Create issues for broken links
            for link in broken_links:
                severity = 'critical' if link.type == 'internal' else 'major'
                status_text = f"HTTP {link.status_code}"
                
                self._create_issue(
                    url=link.metadata.url,
                    title=f"Broken {link.type} link found",
                    description=f"Link to {link.url} returns {status_text}",
                    severity=severity,
                    category='links',
                    recommendation=f"Fix or remove the broken link to {link.url}"
                )

            # Update technical score based on broken links
            if broken_links.exists():
                penalty = min(30, broken_links.count() * 5)  # Max 30% penalty
                self._update_technical_score(-penalty)

            return {
                'total_broken_links': broken_links.count(),
                'broken_internal_links': broken_links.filter(type='internal').count(),
                'broken_external_links': broken_links.filter(type='external').count(),
                'status_codes': dict(broken_links.values_list('status_code').annotate(count=models.Count('id')))
            }

        except Exception as e:
            logger.error("Error analyzing broken links: %s", str(e))
            return {
                'total_broken_links': 0,
                'broken_internal_links': 0,
                'broken_external_links': 0,
                'status_codes': {}
            }

    def _update_technical_score(self, adjustment):
        """Update the technical score with the given adjustment."""
        try:
            report = SEOReport.objects.get(crawl=self.crawl)
            if report.technical_score is not None:
                report.technical_score = max(0, min(100, report.technical_score + adjustment))
                report.save(update_fields=['technical_score'])
        except SEOReport.DoesNotExist:
            pass
        except Exception as e:
            logger.error("Error updating technical score: %s", str(e))

    def _analyze_redirect_chains(self):
        """Analyze redirect chains and identify potential issues."""
        try:
            # Get all links from the current crawl
            links = SEOLink.objects.filter(
                metadata__crawl=self.crawl,
                type='internal'  # Only analyze internal links
            ).select_related('metadata')

            # Group links by their redirect chain
            redirect_chains = {}
            for link in links:
                if not link.status_code or link.status_code < 300 or link.status_code >= 400:
                    continue

                # Track redirect chain
                chain = []
                current_url = link.url
                visited = set()

                while current_url and current_url not in visited:
                    visited.add(current_url)
                    chain.append(current_url)
                    
                    # Find the next URL in the redirect chain
                    next_link = links.filter(url=current_url, status_code__gte=300, status_code__lt=400).first()
                    if not next_link:
                        break
                    
                    current_url = next_link.url
                    
                    if current_url in chain:  # Circular redirect
                        self._create_issue(
                            url=link.metadata.url,
                            title="Circular redirect detected",
                            description=f"Circular redirect chain detected starting from {link.url}",
                            severity="critical",
                            category="technical",
                            recommendation="Fix the circular redirect chain to improve crawlability and user experience"
                        )
                        break

                if len(chain) > 2:  # Long redirect chain
                    self._create_issue(
                        url=link.metadata.url,
                        title="Long redirect chain",
                        description=f"Long redirect chain ({len(chain)} redirects) detected for {link.url}",
                        severity="major",
                        category="technical",
                        recommendation="Reduce the number of redirects to improve page load time and crawl efficiency"
                    )

                # Store the redirect chain
                redirect_chains[link.url] = chain

            # Update technical score based on redirect issues
            total_issues = len([c for c in redirect_chains.values() if len(c) > 2])
            if total_issues > 0:
                penalty = min(20, total_issues * 2)  # Max 20% penalty
                self._update_technical_score(-penalty)

            return {
                'total_redirects': len(redirect_chains),
                'circular_redirects': len([c for c in redirect_chains.values() if c[-1] in c[:-1]]),
                'long_chains': len([c for c in redirect_chains.values() if len(c) > 2])
            }

        except Exception as e:
            logger.error("Error analyzing redirect chains: %s", str(e))
            return {
                'total_redirects': 0,
                'circular_redirects': 0,
                'long_chains': 0
            }

    def _analyze_url_structure(self):
        """Analyze URL structure and identify potential issues."""
        try:
            metadata_entries = SEOMetadata.objects.filter(crawl=self.crawl)
            
            for metadata in metadata_entries:
                url = metadata.url
                parsed_url = urlparse(url)
                
                # Check URL length
                if len(url) > 100:
                    self._create_issue(
                        url=url,
                        title="URL too long",
                        description=f"URL length ({len(url)} chars) exceeds recommended maximum of 100 chars",
                        severity="medium",
                        category="technical",
                        recommendation="Shorten the URL to improve usability and SEO"
                    )
                
                # Check for uppercase characters
                if any(c.isupper() for c in parsed_url.path):
                    self._create_issue(
                        url=url,
                        title="Uppercase characters in URL",
                        description="URL contains uppercase characters which can cause duplicate content issues",
                        severity="low",
                        category="technical",
                        recommendation="Convert URL to lowercase"
                    )
                
                # Check for special characters
                special_chars = re.findall(r'[^a-zA-Z0-9/-]', parsed_url.path)
                if special_chars:
                    self._create_issue(
                        url=url,
                        title="Special characters in URL",
                        description=f"URL contains special characters: {', '.join(set(special_chars))}",
                        severity="medium",
                        category="technical",
                        recommendation="Remove or encode special characters in URL"
                    )
                
                # Check for multiple slashes
                if '//' in parsed_url.path:
                    self._create_issue(
                        url=url,
                        title="Multiple slashes in URL",
                        description="URL contains consecutive slashes which can cause crawling issues",
                        severity="low",
                        category="technical",
                        recommendation="Remove consecutive slashes from URL"
                    )
                
                # Check for trailing slash consistency
                if len(metadata_entries) > 1:
                    has_trailing = parsed_url.path.endswith('/')
                    other_urls = metadata_entries.exclude(id=metadata.id)
                    inconsistent_slashes = other_urls.filter(
                        url__endswith='/' if not has_trailing else ~Q(url__endswith='/')
                    ).exists()
                    
                    if inconsistent_slashes:
                        self._create_issue(
                            url=url,
                            title="Inconsistent trailing slashes",
                            description="Some URLs end with a slash while others don't",
                            severity="low",
                            category="technical",
                            recommendation="Maintain consistency in trailing slash usage"
                        )
            
            return True
            
        except Exception as e:
            logger.error("Error analyzing URL structure: %s", str(e))
            return False

    def _analyze_robots_meta(self):
        """Analyze robots meta directives."""
        try:
            metadata_entries = SEOMetadata.objects.filter(crawl=self.crawl)
            
            for metadata in metadata_entries:
                if not metadata.robots_directives:
                    self._create_issue(
                        url=metadata.url,
                        title="Missing robots meta tag",
                        description="Page does not have a robots meta tag",
                        severity="low",
                        category="technical",
                        recommendation="Add a robots meta tag to control search engine behavior"
                    )
                    continue
                
                directives = metadata.robots_directives
                if 'noindex' in directives:
                    self._create_issue(
                        url=metadata.url,
                        title="Page set to noindex",
                        description="Page is set to not be indexed by search engines",
                        severity="medium",
                        category="technical",
                        recommendation="Review if noindex directive is intentional"
                    )
                
                if 'nofollow' in directives:
                    self._create_issue(
                        url=metadata.url,
                        title="Page set to nofollow",
                        description="Page is set to not pass link equity",
                        severity="medium",
                        category="technical",
                        recommendation="Review if nofollow directive is intentional"
                    )
            
            return True
            
        except Exception as e:
            logger.error("Error analyzing robots meta: %s", str(e))
            return False

    def _analyze_xml_sitemaps(self):
        """Analyze XML sitemaps and their coverage."""
        try:
            # Get all URLs from the crawl
            all_urls = set(SEOMetadata.objects.filter(
                crawl=self.crawl
            ).values_list('url', flat=True))
            
            # Get URLs from sitemap
            sitemap_entries = SEOSitemapAnalysis.objects.filter(
                crawl=self.crawl
            )
            sitemap_urls = set(sitemap_entries.values_list('url', flat=True))
            
            # Find URLs missing from sitemap
            missing_urls = all_urls - sitemap_urls
            if missing_urls:
                for url in missing_urls:
                    self._create_issue(
                        url=url,
                        title="URL missing from sitemap",
                        description="Page is not included in the XML sitemap",
                        severity="low",
                        category="technical",
                        recommendation="Add the URL to the XML sitemap"
                    )
            
            # Check sitemap entries
            for entry in sitemap_entries:
                # Check last modified date
                if not entry.last_modified:
                    self._create_issue(
                        url=entry.url,
                        title="Missing lastmod in sitemap",
                        description="Sitemap entry does not have a last modified date",
                        severity="low",
                        category="technical",
                        recommendation="Add lastmod date to sitemap entry"
                    )
                
                # Check if URL is indexed
                if not entry.indexed:
                    self._create_issue(
                        url=entry.url,
                        title="Sitemap URL not indexed",
                        description="URL in sitemap is not indexed by search engines",
                        severity="medium",
                        category="technical",
                        recommendation="Investigate why the URL is not being indexed"
                    )
            
            return True
            
        except Exception as e:
            logger.error("Error analyzing XML sitemaps: %s", str(e))
            return False

    def _analyze_pagination(self):
        """Analyze pagination implementation."""
        try:
            metadata_entries = SEOMetadata.objects.filter(crawl=self.crawl)
            
            for metadata in metadata_entries:
                # Check for rel="next" and rel="prev" links
                links = SEOLink.objects.filter(metadata=metadata)
                has_next = links.filter(rel_attributes__contains=['next']).exists()
                has_prev = links.filter(rel_attributes__contains=['prev']).exists()
                
                if has_next or has_prev:  # This is a paginated page
                    # Check canonical implementation
                    if not metadata.canonical_url:
                        self._create_issue(
                            url=metadata.url,
                            title="Missing canonical on paginated page",
                            description="Paginated page does not have a canonical URL",
                            severity="medium",
                            category="technical",
                            recommendation="Add canonical URL to paginated pages"
                        )
                    
                    # Check for proper sequence
                    if has_next and not has_prev and 'page=1' not in metadata.url:
                        self._create_issue(
                            url=metadata.url,
                            title="Incomplete pagination sequence",
                            description="Page has next but no prev link and is not first page",
                            severity="medium",
                            category="technical",
                            recommendation="Ensure proper pagination sequence"
                        )
            
            return True
            
        except Exception as e:
            logger.error("Error analyzing pagination: %s", str(e))
            return False

    def _analyze_page_speed(self):
        """Analyze page speed metrics."""
        try:
            performance_entries = SEOPerformance.objects.filter(
                metadata__crawl=self.crawl
            ).select_related('metadata')
            
            for performance in performance_entries:
                # Check mobile speed score
                if performance.page_speed_mobile and performance.page_speed_mobile < 50:
                    self._create_issue(
                        url=performance.metadata.url,
                        title="Poor mobile page speed",
                        description=f"Mobile speed score is {performance.page_speed_mobile}/100",
                        severity="high",
                        category="performance",
                        recommendation="Optimize page for mobile performance"
                    )
                
                # Check desktop speed score
                if performance.page_speed_desktop and performance.page_speed_desktop < 50:
                    self._create_issue(
                        url=performance.metadata.url,
                        title="Poor desktop page speed",
                        description=f"Desktop speed score is {performance.page_speed_desktop}/100",
                        severity="medium",
                        category="performance",
                        recommendation="Optimize page for desktop performance"
                    )
                
                # Check TTFB (Time to First Byte)
                if performance.ttfb and performance.ttfb > 0.6:  # More than 600ms
                    self._create_issue(
                        url=performance.metadata.url,
                        title="Slow server response time",
                        description=f"Time to First Byte is {performance.ttfb:.2f} seconds",
                        severity="high",
                        category="performance",
                        recommendation="Optimize server response time"
                    )
            
            return True
            
        except Exception as e:
            logger.error("Error analyzing page speed: %s", str(e))
            return False

    def _analyze_core_web_vitals(self):
        """Analyze Core Web Vitals metrics."""
        try:
            performance_entries = SEOPerformance.objects.filter(
                metadata__crawl=self.crawl
            ).select_related('metadata')
            
            for performance in performance_entries:
                # Check LCP (Largest Contentful Paint)
                if performance.lcp and performance.lcp > 2.5:  # More than 2.5 seconds
                    self._create_issue(
                        url=performance.metadata.url,
                        title="Poor LCP score",
                        description=f"Largest Contentful Paint is {performance.lcp:.2f} seconds",
                        severity="high",
                        category="performance",
                        recommendation="Optimize LCP by improving loading performance"
                    )
                
                # Check FID (First Input Delay)
                if performance.fid and performance.fid > 100:  # More than 100ms
                    self._create_issue(
                        url=performance.metadata.url,
                        title="Poor FID score",
                        description=f"First Input Delay is {performance.fid:.2f} milliseconds",
                        severity="high",
                        category="performance",
                        recommendation="Optimize FID by improving interactivity"
                    )
                
                # Check CLS (Cumulative Layout Shift)
                if performance.cls and performance.cls > 0.1:  # More than 0.1
                    self._create_issue(
                        url=performance.metadata.url,
                        title="Poor CLS score",
                        description=f"Cumulative Layout Shift is {performance.cls:.3f}",
                        severity="high",
                        category="performance",
                        recommendation="Optimize CLS by reducing layout shifts"
                    )
            
            return True
            
        except Exception as e:
            logger.error("Error analyzing Core Web Vitals: %s", str(e))
            return False

    def _analyze_mobile_usability(self):
        """Analyze mobile usability issues."""
        try:
            performance_entries = SEOPerformance.objects.filter(
                metadata__crawl=self.crawl
            ).select_related('metadata')
            
            for performance in performance_entries:
                if not performance.mobile_friendly:
                    self._create_issue(
                        url=performance.metadata.url,
                        title="Not mobile-friendly",
                        description="Page is not optimized for mobile devices",
                        severity="critical",
                        category="mobile",
                        recommendation="Implement responsive design and fix mobile usability issues"
                    )
                
                if performance.mobile_issues:
                    for issue in performance.mobile_issues:
                        self._create_issue(
                            url=performance.metadata.url,
                            title=f"Mobile issue: {issue.get('type', 'Unknown')}",
                            description=issue.get('description', 'Mobile usability issue detected'),
                            severity="high",
                            category="mobile",
                            recommendation=issue.get('recommendation', 'Fix mobile usability issue')
                        )
            
            return True
            
        except Exception as e:
            logger.error("Error analyzing mobile usability: %s", str(e))
            return False

    def _analyze_resource_optimization(self):
        """Analyze resource optimization issues."""
        try:
            metadata_entries = SEOMetadata.objects.filter(crawl=self.crawl)
            
            for metadata in metadata_entries:
                # Check images
                images = SEOImage.objects.filter(metadata=metadata)
                for image in images:
                    # Check image size
                    if image.filesize and image.filesize > 200000:  # Larger than 200KB
                        self._create_issue(
                            url=metadata.url,
                            title="Large image file",
                            description=f"Image {image.url} is {image.filesize/1024:.1f}KB",
                            severity="medium",
                            category="performance",
                            recommendation="Optimize image size and use appropriate compression"
                        )
                    
                    # Check for missing lazy loading
                    if not image.lazy_loaded:
                        self._create_issue(
                            url=metadata.url,
                            title="Image not lazy loaded",
                            description=f"Image {image.url} is not using lazy loading",
                            severity="low",
                            category="performance",
                            recommendation="Implement lazy loading for images"
                        )
                
                # Check for render-blocking resources
                if metadata.js_dependencies:
                    blocking_resources = [
                        dep for dep in metadata.js_dependencies
                        if dep.get('blocking', False)
                    ]
                    if blocking_resources:
                        self._create_issue(
                            url=metadata.url,
                            title="Render-blocking resources",
                            description=f"Found {len(blocking_resources)} render-blocking resources",
                            severity="high",
                            category="performance",
                            recommendation="Optimize or defer render-blocking resources"
                        )
            
            return True
            
        except Exception as e:
            logger.error("Error analyzing resource optimization: %s", str(e))
            return False

    def _analyze_hreflang_implementation(self):
        """Analyze hreflang implementation."""
        try:
            metadata_entries = SEOMetadata.objects.filter(crawl=self.crawl)
            
            for metadata in metadata_entries:
                # Get all hreflang links
                links = SEOLink.objects.filter(
                    metadata=metadata,
                    rel_attributes__contains=['alternate']
                )
                
                hreflang_links = [
                    link for link in links
                    if any(attr.startswith('hreflang=') for attr in link.rel_attributes)
                ]
                
                if hreflang_links:
                    # Check for self-referencing hreflang
                    languages = [
                        attr.split('=')[1]
                        for link in hreflang_links
                        for attr in link.rel_attributes
                        if attr.startswith('hreflang=')
                    ]
                    
                    if metadata.language and f"hreflang={metadata.language}" not in languages:
                        self._create_issue(
                            url=metadata.url,
                            title="Missing self-referencing hreflang",
                            description="Page does not have a self-referencing hreflang tag",
                            severity="medium",
                            category="international",
                            recommendation="Add self-referencing hreflang tag"
                        )
                    
                    # Check for reciprocal links
                    for link in hreflang_links:
                        target_metadata = SEOMetadata.objects.filter(
                            crawl=self.crawl,
                            url=link.url
                        ).first()
                        
                        if target_metadata:
                            reciprocal_links = SEOLink.objects.filter(
                                metadata=target_metadata,
                                url=metadata.url,
                                rel_attributes__contains=['alternate']
                            )
                            
                            if not reciprocal_links.exists():
                                self._create_issue(
                                    url=metadata.url,
                                    title="Missing reciprocal hreflang",
                                    description=f"No reciprocal hreflang link found on {link.url}",
                                    severity="medium",
                                    category="international",
                                    recommendation="Implement reciprocal hreflang links"
                                )
            
            return True
            
        except Exception as e:
            logger.error("Error analyzing hreflang implementation: %s", str(e))
            return False

    def _analyze_canonical_tags(self):
        """Analyze canonical tag implementation."""
        try:
            metadata_entries = SEOMetadata.objects.filter(crawl=self.crawl)
            
            for metadata in metadata_entries:
                if not metadata.canonical_url:
                    self._create_issue(
                        url=metadata.url,
                        title="Missing canonical tag",
                        description="Page does not have a canonical tag specified",
                        severity="medium",
                        category="technical",
                        recommendation="Add a canonical tag to indicate the preferred version of this page"
                    )
                elif metadata.canonical_url != metadata.url:
                    # Check if the canonical URL points to a different page
                    self._create_issue(
                        url=metadata.url,
                        title="Non-self-referential canonical",
                        description=f"Page canonicalized to: {metadata.canonical_url}",
                        severity="info",
                        category="technical",
                        recommendation="Verify this canonicalization is intended"
                    )
                    
                # Check for canonical consistency in pagination
                if 'page=' in metadata.url and metadata.canonical_url == metadata.url:
                    self._create_issue(
                        url=metadata.url,
                        title="Incorrect canonical on paginated page",
                        description="Paginated page should not be self-canonical",
                        severity="medium",
                        category="technical",
                        recommendation="Point canonical to the first page of the pagination series"
                    )
            
            return True
            
        except Exception as e:
            logger.error("Error analyzing canonical tags: %s", str(e))
            return False

    def _analyze_language_targeting(self):
        return self.local_analyzer._analyze_language_targeting()

    def _analyze_translation_quality(self):
        return self.local_analyzer._analyze_translation_quality()

    def _analyze_market_relevance(self):
        return self.local_analyzer._analyze_market_relevance()

    def _analyze_mobile_friendliness(self):
        return self.ux_analyzer._analyze_mobile_friendliness()

    def _analyze_accessibility(self):
        return self.ux_analyzer._analyze_accessibility()

    def _analyze_navigation_structure(self):
        return self.ux_analyzer._analyze_navigation_structure()

    def _analyze_conversion_elements(self):
        return self.ux_analyzer._analyze_conversion_elements()

    def _analyze_ssl_implementation(self):
        return self.security_analyzer._analyze_ssl_implementation()

    def _analyze_security_headers(self):
        return self.security_analyzer._analyze_security_headers()

    def _analyze_mixed_content(self):
        return self.security_analyzer._analyze_mixed_content()

    def _analyze_breadcrumbs(self):
        return self.schema_analyzer._analyze_breadcrumbs()

    def _analyze_social_integration(self):
        return self.social_analyzer._analyze_social_integration()

    def _analyze_brand_presence(self):
        return self.social_analyzer._analyze_brand_presence()

    def _analyze_social_sharing(self):
        return self.social_analyzer._analyze_social_sharing()

    def _analyze_competitive_gap(self):
        return self.competitive_analyzer._analyze_competitive_gap()

    def _analyze_market_positioning(self):
        return self.competitive_analyzer._analyze_market_positioning()

    def _analyze_content_gaps(self):
        return self.competitive_analyzer._analyze_content_gaps()

    def _analyze_ai_content_quality(self):
        return self.ai_seo_analyzer._analyze_ai_content_quality()

    def _analyze_semantic_optimization(self):
        return self.ai_seo_analyzer._analyze_semantic_optimization()

    def _analyze_entity_recognition(self):
        return self.ai_seo_analyzer._analyze_entity_recognition()

    def _analyze_topic_modeling(self):
        return self.ai_seo_analyzer._analyze_topic_modeling()

    def _analyze_user_intent_match(self):
        return self.ai_seo_analyzer._analyze_user_intent_match()

    def _analyze_local_seo_elements(self):
        return self.local_analyzer._analyze_local_seo_elements()

    def _analyze_geo_targeting(self):
        return self.local_analyzer._analyze_geo_targeting()

    def _analyze_product_schema(self):
        return self.ecommerce_analyzer._analyze_product_schema()

    def _analyze_shopping_optimization(self):
        return self.ecommerce_analyzer._analyze_shopping_optimization()

    def _analyze_schema_markup(self):
        return self.schema_analyzer._analyze_schema_markup()

    def _analyze_rich_snippets(self):
        return self.schema_analyzer._analyze_rich_snippets()
