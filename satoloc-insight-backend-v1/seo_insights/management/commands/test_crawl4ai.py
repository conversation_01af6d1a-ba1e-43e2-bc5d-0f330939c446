# seo_insights/management/commands/test_crawl4ai.py
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from seo_insights.models import SEOProject, SEOCrawl
from seo_insights.services.crawl4ai_integration import Crawl4AIService
import time

User = get_user_model()

class Command(BaseCommand):
    help = 'Test Crawl4AI integration'

    def add_arguments(self, parser):
        parser.add_argument('--url', type=str, help='URL to crawl')

    def handle(self, *args, **options):
        url = options.get('url')
        if not url:
            self.stdout.write(self.style.ERROR('Please provide a URL to crawl'))
            return

        self.stdout.write('Testing Crawl4AI connection...')

        try:
            # Create a test user if needed
            user, _ = User.objects.get_or_create(
                username='test_user',
                email='<EMAIL>'
            )

            # Create or get a project
            project, _ = SEOProject.objects.get_or_create(
                user=user,
                site_url=url,
                defaults={
                    'name': 'Test Project',
                    'description': 'Test crawl project'
                }
            )

            # Create a new crawl
            crawl = SEOCrawl.objects.create(
                project=project,
                status='pending'
            )

            # Initialize service
            service = Crawl4AIService()

            # Start crawl
            crawl_config = {
                'max_pages': 100,
                'respect_robots_txt': True,
                'follow_redirects': True,
                'crawl_ajax': True,
                'extract': {
                    'metadata': True,
                    'content': True,
                    'links': True,
                    'images': True,
                    'schema': True,
                    'performance': True,
                    'ai_seo': True
                }
            }

            result = service.start_crawl(url, crawl_config)
            job_id = result.get('job_id')

            if not job_id:
                raise Exception('No job ID returned from crawler')

            self.stdout.write(self.style.SUCCESS(f'Successfully connected to Crawl4AI API. Job ID: {job_id}'))

            # Update crawl with job ID
            crawl.external_job_id = job_id
            crawl.save()

            # Wait for results
            self.stdout.write('Waiting for crawl to complete...')
            try:
                # Poll for results
                while True:
                    status = service.get_job_status(job_id)
                    if status['status'] == 'completed':
                        results = service.get_results(job_id)
                        self.stdout.write(self.style.SUCCESS('Crawl completed successfully!'))
                        self.stdout.write(f'Pages crawled: {results["stats"]["pages_crawled"]}')
                        self.stdout.write(f'Total links found: {results["stats"]["total_links"]}')
                        self.stdout.write(f'Total images found: {results["stats"]["total_images"]}')
                        break
                    elif status['status'] == 'failed':
                        raise Exception(f'Crawl failed: {status.get("error", "Unknown error")}')
                    
                    time.sleep(5)  # Wait 5 seconds before checking again

                # Update crawl status
                crawl.status = 'completed'
                crawl.pages_crawled = results['stats']['pages_crawled']
                crawl.external_results = results
                crawl.save()

            except Exception as e:
                self.stdout.write(self.style.ERROR(f'Error waiting for results: {str(e)}'))
                crawl.status = 'failed'
                crawl.error_message = str(e)
                crawl.save()
                raise

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error testing Crawl4AI connection: {str(e)}'))
            self.stdout.write(self.style.ERROR('Error in test_crawl4ai command'))
            raise
