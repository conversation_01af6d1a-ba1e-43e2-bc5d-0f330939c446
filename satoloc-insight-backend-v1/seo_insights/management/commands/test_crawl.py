from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from seo_insights.models import SEOProject, SEOCrawl
from seo_insights.services.crawl4ai_integration import Crawl4AIService
from django.utils import timezone

class Command(BaseCommand):
    help = 'Test crawl functionality with a specific URL'

    def handle(self, *args, **options):
        # Get test user
        User = get_user_model()
        try:
            # First try to get by username
            user = User.objects.get(username='satoloc-admin')
        except User.DoesNotExist:
            try:
                # Then try by email
                user = User.objects.get(email='<EMAIL>')
            except User.DoesNotExist:
                # If user doesn't exist at all, create it
                user = User.objects.create_user(
                    username='satoloc-admin',
                    email='<EMAIL>',
                    password='fucxaj-kymvYk-rakwa4',
                    is_staff=True
                )
        
        self.stdout.write(f'Using user: {user.username} ({user.email})')

        # Create or update test project
        project, created = SEOProject.objects.get_or_create(
            user=user,
            site_url='https://www.satoloc.com/',
            defaults={
                'name': 'Satoloc Website Analysis',
                'description': 'Comprehensive SEO analysis of Satoloc website',
                'max_pages': 100,
                'crawl_depth': 3,
                'enable_competitor_analysis': True,
                'enable_regional_analysis': True
            }
        )
        
        # Update values if they are None
        if project.max_pages is None or project.crawl_depth is None:
            project.max_pages = 100
            project.crawl_depth = 3
            project.save()
        
        self.stdout.write(f'Project values - max_pages: {project.max_pages}, crawl_depth: {project.crawl_depth}')

        # Prepare crawl configuration
        crawl_config = {
            'max_pages': project.max_pages,
            'crawl_depth': project.crawl_depth,
            'competitor_analysis': project.enable_competitor_analysis,
            'regional_performance': project.enable_regional_analysis,
            'headers': {
                'User-Agent': 'Satoloc-SEO-Crawler/1.0'
            },
            'technical_checks': {
                'mobile_optimization': True,
                'schema_validation': True,
                'page_speed_threshold': 85,
                'check_hreflang': True,
                'check_structured_data': True
            }
        }
        
        # Create new crawl
        crawl = SEOCrawl.objects.create(
            project=project,
            status='pending',
            start_time=timezone.now(),
            configuration=crawl_config
        )

        self.stdout.write(self.style.SUCCESS(f'Created crawl with ID: {crawl.id}'))

        # Initialize crawler service
        crawler = Crawl4AIService()

        # Start crawl with comprehensive configuration
        try:
            # Start the crawl - this will return quickly
            result = crawler.start_crawl(
                url=project.site_url,
                config=crawl_config
            )
            
            self.stdout.write(self.style.SUCCESS('Crawl started successfully'))
            self.stdout.write('The crawl is now running in the background. You can check the FastAPI logs to see progress.')
            
            # Update crawl status
            crawl.status = 'processing'
            crawl.external_job_id = result.get('job_id')
            crawl.save()

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error starting crawl: {str(e)}'))
            crawl.status = 'failed'
            crawl.save()
