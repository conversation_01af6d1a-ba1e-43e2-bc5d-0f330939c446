from django.core.management.base import BaseCommand
from seo_insights.models import SEOCrawl
from seo_insights.services.crawl4ai_integration import Crawl4AIService
import json

class Command(BaseCommand):
    help = 'Check the status of a crawl job'

    def add_arguments(self, parser):
        parser.add_argument('crawl_id', type=int, help='ID of the SEOCrawl to check')

    def handle(self, *args, **options):
        try:
            # Get the crawl
            crawl = SEOCrawl.objects.get(id=options['crawl_id'])
            
            if not crawl.external_job_id:
                self.stdout.write(self.style.ERROR('No external job ID found for this crawl'))
                return
            
            # Initialize crawler service
            crawler = Crawl4AIService()
            
            # Get status with timeout and error handling
            try:
                # Try to get current status without waiting
                status = crawler.get_job_status(crawl.external_job_id)
                
                if status['status'] == 'completed':
                    # Get full results
                    results = crawler.get_results(crawl.external_job_id)
                    crawl.external_results = results
                    crawl.status = 'completed'
                    crawl.save()
                    
                    # Pretty print the results
                    self.stdout.write(self.style.SUCCESS('Crawl completed successfully'))
                    self.stdout.write('Results:')
                    self.stdout.write(json.dumps(results, indent=2))
                    
                elif status['status'] == 'failed':
                    error_msg = status.get('error', 'Unknown error')
                    self.stdout.write(self.style.ERROR(f'Crawl failed: {error_msg}'))
                    crawl.status = 'failed'
                    crawl.error_message = error_msg
                    crawl.save()
                    
                else:  # processing or pending
                    progress = status.get('progress', 0)
                    self.stdout.write(f'Crawl is still running (Progress: {progress}%)')
                    self.stdout.write('Run this command again to check the latest status')
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'Error checking crawl status: {str(e)}'))
                # Don't update crawl status since this might be a temporary error

        except SEOCrawl.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'No crawl found with ID {options["crawl_id"]}'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error: {str(e)}'))
