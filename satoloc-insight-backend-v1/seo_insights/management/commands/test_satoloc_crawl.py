from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from seo_insights.models import SEOProject, SEOCrawl
from seo_insights.services.crawl4ai_integration import Crawl4AIService
import logging
import json

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Test crawling satoloc.com'

    def handle(self, *args, **options):
        try:
            # Get or create test user
            User = get_user_model()
            user, created = User.objects.get_or_create(
                username='test_user',
                defaults={'email': '<EMAIL>'}
            )

            # Create project
            project = SEOProject.objects.create(
                user=user,
                name='SatoLoc Test Project',
                description='Test crawl of satoloc.com',
                site_url='https://www.satoloc.com/'
            )
            self.stdout.write(self.style.SUCCESS(f'Created project: {project.name}'))

            # Create crawl
            crawl = SEOCrawl.objects.create(
                project=project,
                status='pending'
            )
            self.stdout.write(self.style.SUCCESS(f'Created crawl with ID: {crawl.id}'))

            # Initialize crawler service
            crawler = Crawl4AIService()

            # Comprehensive crawl configuration
            crawl_config = {
                'max_pages': 10,
                'crawl_depth': 2,
                'respect_robots_txt': True,
                'follow_redirects': True,
                'crawl_ajax': True,
                'discover_sitemaps': True,
                'detect_language': True,
                
                # URL patterns
                'follow_patterns': [
                    "/*",
                    "/localization-solutions/*",
                    "/content-solutions/*",
                    "/who-we-are/*",
                    "/sitemap*.xml",  # Include sitemap files
                    "/robots.txt"     # Include robots.txt
                ],
                
                'exclude_patterns': [
                    "*.pdf",
                    "*.jpg",
                    "*.png",
                    "*.gif",
                    "*?utm_*",
                    "*?fbclid=*",
                    "*/feed/*",
                    "*/page/*",
                    "*?s=*",
                    "*?filter=*",
                    "*?sort=*",
                    "*#*"
                ],
                
                # Rate limiting
                'rate_limit': {
                    'requests_per_second': 1,
                    'concurrent_requests': 2
                },
                
                # Headers
                'headers': {
                    'User-Agent': 'SatoLocInsight SEO Crawler/1.0',
                    'Accept-Language': 'en-US,en;q=0.9'
                },
                
                # Enhanced data extraction
                'extract': {
                    # Basic metadata
                    'metadata': True,
                    'content': True,
                    'links': True,
                    'images': True,
                    
                    # Technical SEO
                    'schema': True,
                    'performance': True,
                    'mobile': True,
                    'security': True,
                    'structured_data': True,
                    'meta_robots': True,
                    'canonical_info': True,
                    
                    # Content analysis
                    'text_content': True,
                    'word_count': True,
                    'reading_time': True,
                    'content_language': True,
                    'content_type': True,
                    
                    # Multilingual
                    'hreflang': True,
                    'language_versions': True,
                    
                    # Social and sharing
                    'social_tags': True,
                    'share_buttons': True,
                    
                    # Navigation and structure
                    'navigation': True,
                    'breadcrumbs': True,
                    'site_structure': True,
                    
                    # JavaScript and dynamic content
                    'javascript_content': True,
                    'dynamic_elements': True,
                    'ajax_requests': True,
                    
                    # Performance metrics
                    'load_time': True,
                    'page_size': True,
                    'resource_timing': True,
                    
                    # Forms and conversion
                    'forms': True,
                    'cta_elements': True,
                    
                    # Media
                    'video_elements': True,
                    'audio_elements': True,
                    'iframe_content': True
                },
                
                # Selectors for specific elements
                'selectors': {
                    # Meta information
                    'meta': [
                        'meta[name]',
                        'meta[property]',
                        'link[rel="canonical"]',
                        'link[rel="alternate"][hreflang]',
                        'meta[name="robots"]',
                        'meta[name="viewport"]',
                        'meta[name="description"]',
                        'meta[name="keywords"]'
                    ],
                    
                    # Social media
                    'social': [
                        'meta[property^="og:"]',
                        'meta[name^="twitter:"]',
                        '.social-share',
                        '.share-button'
                    ],
                    
                    # Structured data
                    'schema': [
                        'script[type="application/ld+json"]',
                        '[itemscope]',
                        '[itemtype]'
                    ],
                    
                    # Content structure
                    'headings': [
                        'h1', 'h2', 'h3', 'h4', 'h5', 'h6'
                    ],
                    'content': [
                        'p',
                        'article',
                        'section',
                        'main',
                        '.content',
                        '.post-content'
                    ],
                    
                    # Navigation
                    'navigation': [
                        'nav',
                        'header',
                        'footer',
                        '.menu',
                        '.navigation'
                    ],
                    
                    # JavaScript elements
                    'javascript': [
                        'script[src]',
                        'script:not([src])',
                        '[onclick]',
                        '[data-*]',
                        '.js-*'
                    ],
                    
                    # Forms and conversion
                    'forms': [
                        'form',
                        'input',
                        'button',
                        '.cta',
                        '.submit-button'
                    ],
                    
                    # Media elements
                    'media': [
                        'img',
                        'video',
                        'audio',
                        'iframe',
                        'picture source'
                    ]
                },
                
                # Analysis configuration
                'analyze': {
                    # On-page SEO
                    'title_tags': True,
                    'meta_descriptions': True,
                    'heading_structure': True,
                    'image_optimization': True,
                    'link_structure': True,
                    'content_quality': True,
                    
                    # Technical SEO
                    'technical_seo': True,
                    'mobile_friendly': True,
                    'page_speed': True,
                    'structured_data': True,
                    'indexability': True,
                    'http_status': True,
                    
                    # Content analysis
                    'keyword_usage': True,
                    'content_structure': True,
                    'readability': True,
                    'word_count': True,
                    'text_ratio': True,
                    'duplicate_content': True,
                    
                    # User experience
                    'mobile_usability': True,
                    'navigation_usability': True,
                    'form_usability': True,
                    'page_layout': True,
                    
                    # Performance
                    'load_time_analysis': True,
                    'resource_usage': True,
                    'render_blocking': True,
                    
                    # Internal linking
                    'internal_links': True,
                    'navigation_depth': True,
                    'orphan_pages': True,
                    'broken_links': True,
                    
                    # Content optimization
                    'semantic_analysis': True,
                    'keyword_density': True,
                    'content_relevance': True,
                    'heading_hierarchy': True
                }
            }
            
            self.stdout.write(self.style.SUCCESS('Starting crawl with configuration:'))
            self.stdout.write(json.dumps(crawl_config, indent=2))
            
            # Start crawl
            result = crawler.start_crawl(project.site_url, crawl_config)
            job_id = result.get('job_id')
            
            if not job_id:
                raise Exception('No job ID returned from crawler service')

            self.stdout.write(self.style.SUCCESS(f'Started crawl job: {job_id}'))
            
            # Update crawl with job ID
            crawl.external_job_id = job_id
            crawl.status = 'processing'
            crawl.save()

            # Wait for completion with progress updates
            try:
                self.stdout.write('Waiting for crawl to complete...')
                results = crawler.wait_for_completion(job_id, timeout_minutes=5)
                
                # Log detailed results
                self.stdout.write(self.style.SUCCESS('Crawl completed successfully'))
                self.stdout.write('Summary:')
                self.stdout.write(f"Pages crawled: {results.get('stats', {}).get('pages_crawled', 0)}")
                self.stdout.write(f"Total links found: {results.get('stats', {}).get('total_links', 0)}")
                self.stdout.write(f"Total images found: {results.get('stats', {}).get('total_images', 0)}")
                
                # Store results in crawl object
                crawl.external_results = results
                crawl.pages_crawled = results.get('stats', {}).get('pages_crawled', 0)
                crawl.status = 'completed'
                crawl.save()
                
                # Print first page details for verification
                if results.get('pages'):
                    first_page = results['pages'][0]
                    self.stdout.write('\nFirst page details:')
                    self.stdout.write(f"URL: {first_page.get('url')}")
                    self.stdout.write(f"Title: {first_page.get('metadata', {}).get('title')}")
                    self.stdout.write(f"Links found: {len(first_page.get('links', []))}")
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'Error waiting for crawl completion: {e}'))
                crawl.status = 'failed'
                crawl.error_message = str(e)
                crawl.save()
                return

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error: {e}'))
            logger.exception('Error in test crawl command') 