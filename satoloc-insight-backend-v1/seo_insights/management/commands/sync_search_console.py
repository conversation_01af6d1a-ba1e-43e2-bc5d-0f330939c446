from django.core.management.base import BaseCommand
from django.conf import settings

from seo_insights.models import SEOProject
from seo_insights.services.search_console_integration import GoogleSearchConsoleService


class Command(BaseCommand):
    help = 'Sync data from Google Search Console for all projects'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='Number of days of data to sync (default: 30)'
        )
        parser.add_argument(
            '--project-id',
            type=int,
            help='Sync data for a specific project ID only'
        )
    
    def handle(self, *args, **options):
        days = options['days']
        project_id = options.get('project_id')
        
        # Get projects to process
        if project_id:
            projects = SEOProject.objects.filter(id=project_id)
        else:
            projects = SEOProject.objects.filter(search_console_enabled=True, search_console_verified=True)
        
        for project in projects:
            try:
                self.stdout.write(f"Syncing Search Console data for project: {project.name}")
                
                # Initialize service
                service = GoogleSearchConsoleService(project)
                
                # Sync data
                result = service.sync_search_console_data(days=days)
                
                if result['success']:
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Successfully synced {days} days of Search Console data for {project.name}"
                        )
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(
                            f"Sync failed for {project.name}: {result['message']}"
                        )
                    )
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f"Error syncing Search Console data for {project.name}: {str(e)}"
                    )
                ) 