# seo_insights/tasks.py
from celery import shared_task
from celery.result import AsyncResult
import logging
import time
from django.utils import timezone
from django.conf import settings

from .models import SEOCrawl, SEOProject
from .services.crawl4ai_integration import Crawl4AIService
from .services.analyzer import SEOAnalyzer

logger = logging.getLogger(__name__)

@shared_task
def start_seo_crawl(crawl_id):
    """Start crawling a website using Crawl4AI for comprehensive SEO analysis.
    
    Args:
        crawl_id: ID of the SEOCrawl to process
    
    Returns:
        dict: Result information
    """
    try:
        logger.info("Starting comprehensive SEO crawl task for crawl_id %s", crawl_id)
        
        # Get crawl record
        crawl = SEOCrawl.objects.get(id=crawl_id)
        project = crawl.project
        
        # Update crawl status
        crawl.status = 'processing'
        crawl.save()
        
        # Initialize Crawl4AI service
        crawl4ai = Crawl4AIService()
        
        # Define comprehensive crawl options based on project settings
        crawl_options = {
            "max_pages": project.max_pages or None,
            "respect_robots_txt": True,
            "follow_redirects": True,
            "crawl_ajax": True,
            "recursive": True,
            "max_depth": project.crawl_depth or None,
            "discover_sitemaps": True,
            "detect_language": True,
            
            # Technical analysis configuration
            "page_speed_threshold": 85,  # Based on industry standards
            "competitor_analysis": project.enable_competitor_analysis,
            "regional_performance": project.enable_regional_analysis,
            
            # URL patterns to follow
            "follow_patterns": [
                "/*",              # All paths
                "/tr/*",          # Turkish pages
                "/zh/*",          # Chinese pages
                "/en/*",          # English pages
                "/who-we-are/*",
                "/localization-solutions/*",
                "/content-solutions/*",
                "/blog/*",
                "/get-in-touch/*",
                "*/sitemap*.xml",  # Sitemaps
                "*/feed/*",        # RSS/Atom feeds
                "*/?lang=*",       # Language variations
                "*/category/*",    # Category pages
                "*/tag/*",         # Tag pages
                "*/author/*",      # Author pages
                "*/archive/*"      # Archive pages
            ],
            
            # Patterns to exclude
            "exclude_patterns": [
                "*.pdf",
                "*.jpg",
                "*.png",
                "*.gif",
                "/wp-admin/*",
                "/wp-json/*",
                "/feed/*",
                "*?add-to-cart=*",
                "*?utm_*",
                "*?fbclid=*",
                "*?gclid=*",
                "*/page/[0-9]+/*",  # Pagination
                "*?s=*",            # Search results
                "*?filter=*",       # Filter pages
                "*?sort=*"          # Sort pages
            ],
            
            # Data extraction configuration
            "extract": {
                "metadata": True,
                "content": True,
                "links": True,
                "images": True,
                "schema": True,
                "performance": True,
                "ai_seo": True,
                "social_tags": True,
                "hreflang": True,
                "mobile": True,
                "security": True,
                "forms": True,
                "navigation": True,
                "text_content": True,
                "structured_data": True,
                "meta_robots": True,
                "canonical_info": True,
                "pagination_info": True,
                "breadcrumbs": True,
                "author_info": True,
                "published_date": True,
                "modified_date": True,
                "content_type": True,
                "word_count": True,
                "reading_time": True,
                "content_language": True,
                "http_headers": True,
                "response_time": True,
                "status_code": True,
                "redirect_chain": True,
                "resource_timing": True
            },
            
            # Analysis configuration
            "analyze": {
                "on_page_seo": True,
                "technical_seo": True,
                "content_quality": True,
                "mobile_friendly": True,
                "ai_readiness": True,
                "performance": True,
                "accessibility": True,
                "security": True,
                "structured_data": True,
                "internal_linking": True,
                "external_linking": True,
                "content_structure": True,
                "semantic_analysis": True,
                "competitor_analysis": True,
                "multilingual_seo": True,
                "translation_quality": True,
                "content_similarity": True,
                "keyword_extraction": True,
                "topic_modeling": True,
                "sentiment_analysis": True,
                "readability": True,
                "market_relevance": True,
                "brand_presence": True,
                "user_intent": True,
                "content_gaps": True,
                "conversion_optimization": True,
                "rich_snippets": True,
                "local_seo": True,
                "mobile_usability": True,
                "page_speed": True,
                "core_web_vitals": True,
                "crawl_efficiency": True,
                "indexability": True,
                "duplicate_content": True,
                "thin_content": True,
                "keyword_cannibalization": True,
                "international_targeting": True,
                "social_sharing": True,
                "content_freshness": True,
                "link_quality": True,
                "competitive_gap": True,
                "content_relevance": True
            },
            
            # Rate limiting configuration
            "rate_limit": {
                "requests_per_second": 2,
                "concurrent_requests": 3,
                "respect_host_limits": True
            },
            
            # Timeout configuration
            "timeout": {
                "request": 30,
                "total": 7200  # 2 hours maximum
            },
            
            # Request headers
            "headers": {
                "User-Agent": "SatoLocInsight SEO Crawler/1.0",
                "Accept-Language": "en-US,en;q=0.9,tr;q=0.8,zh;q=0.7"
            }
        }
        
        # Start crawl in Crawl4AI
        crawl_job = crawl4ai.start_crawl(project.site_url, crawl_options)
        
        # Store Crawl4AI job_id
        crawl.external_job_id = crawl_job.get('job_id')
        crawl.save()
        
        # Wait for crawl to complete with extended timeout
        try:
            results = crawl4ai.wait_for_completion(
                crawl_job.get('job_id'),
                timeout_minutes=120  # Extended to 2 hours
            )
            
            # Update crawl with results
            crawl.status = 'completed'
            crawl.pages_crawled = results.get('stats', {}).get('pages_crawled', 0)
            crawl.end_time = timezone.now()
            
            # Store comprehensive results
            crawl.external_results = {
                'job_id': crawl_job.get('job_id'),
                'summary': results.get('summary', {}),
                'stats': results.get('stats', {}),
                'technical_seo': results.get('technical_seo', {}),
                'content_analysis': results.get('content_analysis', {}),
                'performance': results.get('performance', {}),
                'mobile': results.get('mobile', {}),
                'security': results.get('security', {}),
                'multilingual': results.get('multilingual', {})
            }
            crawl.save()
            
            # Trigger comprehensive analysis
            analyze_seo_data.delay(crawl_id)
            
            logger.info("Comprehensive SEO crawl completed successfully for crawl_id %s", crawl_id)
            return {
                'status': 'success',
                'crawl_id': crawl_id,
                'job_id': crawl_job.get('job_id'),
                'message': 'Crawling completed successfully',
                'stats': results.get('stats', {})
            }
            
        except TimeoutError:
            crawl.status = 'failed'
            crawl.error_message = "Crawl timed out"
            crawl.end_time = timezone.now()
            crawl.save()
            
            logger.error("SEO crawl timed out for crawl_id %s", crawl_id)
            return {
                'status': 'failed',
                'crawl_id': crawl_id,
                'message': 'Crawling timed out'
            }
            
    except Exception as e:
        logger.exception("Error in SEO crawl task for crawl_id %s: %s", crawl_id, str(e))
        
        try:
            crawl = SEOCrawl.objects.get(id=crawl_id)
            crawl.status = 'failed'
            crawl.error_message = str(e)
            crawl.end_time = timezone.now()
            crawl.save()
        except Exception as update_error:
            logger.error("Error updating crawl status: %s", str(update_error))
        
        return {
            'status': 'error',
            'crawl_id': crawl_id,
            'message': f'Error during crawling: {str(e)}'
        }

@shared_task
def analyze_seo_data(crawl_id):
    """Analyze SEO data from a completed crawl.
    
    Args:
        crawl_id: ID of the SEOCrawl to analyze
    
    Returns:
        dict: Analysis results
    """
    try:
        logger.info("Starting SEO analysis task for crawl_id %s", crawl_id)
        
        # Get crawl record
        crawl = SEOCrawl.objects.get(id=crawl_id)
        
        # Initialize analyzer with crawl_id
        analyzer = SEOAnalyzer(crawl_id=crawl_id)
        
        # Run analysis
        try:
            success = analyzer.analyze()
            
            if success:
                logger.info("SEO analysis completed successfully for crawl_id %s", crawl_id)
                return {
                    'status': 'success',
                    'crawl_id': crawl_id,
                    'message': 'Analysis completed successfully'
                }
            else:
                logger.error("SEO analysis failed for crawl_id %s", crawl_id)
                return {
                    'status': 'error',
                    'crawl_id': crawl_id,
                    'message': 'Analysis failed'
                }
            
        except Exception as e:
            logger.error("Error during SEO analysis for crawl_id %s: %s", crawl_id, str(e))
            crawl.analysis_error = str(e)
            crawl.save()
            return {
                'status': 'error',
                'crawl_id': crawl_id,
                'message': f'Error during analysis: {str(e)}'
            }
            
    except Exception as e:
        logger.exception("Error in SEO analysis task for crawl_id %s: %s", crawl_id, str(e))
        return {
            'status': 'error',
            'crawl_id': crawl_id,
            'message': f'Error during analysis task: {str(e)}'
        }
