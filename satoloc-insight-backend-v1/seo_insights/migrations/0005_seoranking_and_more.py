# Generated by Django 4.2.16 on 2025-02-19 21:19

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('seo_insights', '0004_seoproject_search_console_credentials_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SEORanking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('keyword', models.CharField(max_length=500)),
                ('url', models.URLField(max_length=2048)),
                ('position', models.IntegerField()),
                ('search_volume', models.IntegerField(blank=True, null=True)),
                ('competition', models.FloatField(blank=True, null=True)),
                ('cpc', models.FloatField(blank=True, null=True)),
                ('serp_features', models.J<PERSON><PERSON>ield(blank=True, null=True)),
                ('featured_snippet', models.<PERSON><PERSON>an<PERSON>ield(default=False)),
                ('local_pack', models.BooleanField(default=False)),
                ('previous_position', models.IntegerField(blank=True, null=True)),
                ('position_change', models.IntegerField(blank=True, null=True)),
                ('first_seen', models.DateTimeField(auto_now_add=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('difficulty', models.FloatField(blank=True, null=True)),
                ('intent', models.CharField(blank=True, max_length=50, null=True)),
                ('seasonal_trend', models.JSONField(blank=True, null=True)),
            ],
        ),
        migrations.RemoveIndex(
            model_name='seosearchconsoleissue',
            name='seo_insight_project_41266d_idx',
        ),
        migrations.RemoveIndex(
            model_name='seosearchconsoleissue',
            name='seo_insight_severit_6b0331_idx',
        ),
        migrations.AddField(
            model_name='seoreport',
            name='domain_rating',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='seoreport',
            name='organic_traffic',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='seoreport',
            name='search_rankings',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='seoreport',
            name='search_terms',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='seoreport',
            name='site_links',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddIndex(
            model_name='seosearchconsoleissue',
            index=models.Index(fields=['project', 'url'], name='seo_insight_project_c7c27e_idx'),
        ),
        migrations.AddIndex(
            model_name='seosearchconsoleissue',
            index=models.Index(fields=['issue_type'], name='seo_insight_issue_t_8648b1_idx'),
        ),
        migrations.AddIndex(
            model_name='seosearchconsoleissue',
            index=models.Index(fields=['discovered'], name='seo_insight_discove_2d8c8e_idx'),
        ),
        migrations.AddField(
            model_name='seoranking',
            name='crawl',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='seo_insights.seocrawl'),
        ),
        migrations.AddIndex(
            model_name='seoranking',
            index=models.Index(fields=['crawl', 'keyword'], name='seo_insight_crawl_i_238450_idx'),
        ),
        migrations.AddIndex(
            model_name='seoranking',
            index=models.Index(fields=['position'], name='seo_insight_positio_7c1bba_idx'),
        ),
        migrations.AddIndex(
            model_name='seoranking',
            index=models.Index(fields=['search_volume'], name='seo_insight_search__a1c763_idx'),
        ),
        migrations.AddIndex(
            model_name='seoranking',
            index=models.Index(fields=['last_updated'], name='seo_insight_last_up_f5b6e7_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='seoranking',
            unique_together={('crawl', 'keyword', 'url')},
        ),
    ]
