# Generated by Django 4.2.16 on 2025-02-19 18:17

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('seo_insights', '0002_seositemapanalysis_seoadvancedanalysis_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SEOSearchConsoleData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.URLField(max_length=2048)),
                ('date', models.DateField()),
                ('clicks', models.IntegerField(default=0)),
                ('impressions', models.IntegerField(default=0)),
                ('ctr', models.FloatField(default=0)),
                ('average_position', models.FloatField(default=0)),
                ('desktop_clicks', models.IntegerField(default=0)),
                ('desktop_impressions', models.IntegerField(default=0)),
                ('mobile_clicks', models.IntegerField(default=0)),
                ('mobile_impressions', models.IntegerField(default=0)),
                ('tablet_clicks', models.IntegerField(default=0)),
                ('tablet_impressions', models.IntegerField(default=0)),
                ('search_appearance', models.JSONField(blank=True, null=True)),
                ('is_indexed', models.BooleanField(default=True)),
                ('indexing_status', models.CharField(blank=True, max_length=50, null=True)),
                ('last_crawled', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='seo_insights.seoproject')),
            ],
        ),
        migrations.RenameField(
            model_name='seoadvancedanalysis',
            old_name='competitive_advantages',
            new_name='anchor_text_analysis',
        ),
        migrations.RenameField(
            model_name='seoadvancedanalysis',
            old_name='competitor_comparison',
            new_name='featured_snippets',
        ),
        migrations.RenameField(
            model_name='seoadvancedanalysis',
            old_name='content_gaps',
            new_name='js_dependencies',
        ),
        migrations.RenameField(
            model_name='seoadvancedanalysis',
            old_name='js_rendered_content',
            new_name='js_errors',
        ),
        migrations.AddField(
            model_name='seoadvancedanalysis',
            name='link_neighborhood',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='seoadvancedanalysis',
            name='rich_results',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='seoadvancedanalysis',
            name='serp_features',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='seoadvancedanalysis',
            name='serp_preview',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='seoadvancedanalysis',
            name='toxic_backlinks',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='seometadata',
            name='anchor_text_analysis',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='seometadata',
            name='dynamic_elements',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='seometadata',
            name='featured_snippets',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='seometadata',
            name='javascript_content',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='seometadata',
            name='js_dependencies',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='seometadata',
            name='js_errors',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='seometadata',
            name='rich_results',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='seometadata',
            name='serp_preview',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='seometadata',
            name='toxic_backlinks',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name='SEOSearchConsoleKeyword',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('keyword', models.CharField(max_length=500)),
                ('clicks', models.IntegerField(default=0)),
                ('impressions', models.IntegerField(default=0)),
                ('ctr', models.FloatField(default=0)),
                ('average_position', models.FloatField(default=0)),
                ('search_type', models.CharField(blank=True, max_length=50, null=True)),
                ('country', models.CharField(blank=True, max_length=2, null=True)),
                ('device', models.CharField(blank=True, max_length=20, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('search_console_data', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='seo_insights.seosearchconsoledata')),
            ],
            options={
                'indexes': [models.Index(fields=['search_console_data', 'keyword'], name='seo_insight_search__29029e_idx'), models.Index(fields=['average_position'], name='seo_insight_average_02a61a_idx')],
            },
        ),
        migrations.CreateModel(
            name='SEOSearchConsoleIssue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.URLField(max_length=2048)),
                ('issue_type', models.CharField(max_length=100)),
                ('severity', models.CharField(choices=[('error', 'Error'), ('warning', 'Warning'), ('info', 'Info')], max_length=20)),
                ('discovered', models.DateTimeField()),
                ('status', models.CharField(choices=[('active', 'Active'), ('fixed', 'Fixed')], max_length=20)),
                ('fixed_date', models.DateTimeField(blank=True, null=True)),
                ('details', models.JSONField(blank=True, null=True)),
                ('validation_state', models.CharField(blank=True, max_length=50, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='seo_insights.seoproject')),
            ],
            options={
                'indexes': [models.Index(fields=['project', 'issue_type'], name='seo_insight_project_41266d_idx'), models.Index(fields=['severity'], name='seo_insight_severit_6b0331_idx'), models.Index(fields=['status'], name='seo_insight_status_1826a5_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='seosearchconsoledata',
            index=models.Index(fields=['project', 'date'], name='seo_insight_project_aeef5e_idx'),
        ),
        migrations.AddIndex(
            model_name='seosearchconsoledata',
            index=models.Index(fields=['url'], name='seo_insight_url_d374dd_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='seosearchconsoledata',
            unique_together={('project', 'url', 'date')},
        ),
    ]
