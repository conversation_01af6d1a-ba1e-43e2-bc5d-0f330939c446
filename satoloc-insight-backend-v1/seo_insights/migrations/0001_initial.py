# Generated by Django 4.2.16 on 2025-02-19 15:55

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SEOCrawl',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('pages_crawled', models.IntegerField(default=0)),
                ('start_time', models.DateTimeField(default=django.utils.timezone.now)),
                ('end_time', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('external_job_id', models.CharField(blank=True, max_length=255, null=True)),
                ('external_results', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SEOMetadata',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.URLField(max_length=2048)),
                ('title', models.CharField(blank=True, max_length=1000, null=True)),
                ('meta_description', models.TextField(blank=True, null=True)),
                ('h1', models.JSONField(blank=True, null=True)),
                ('h2', models.JSONField(blank=True, null=True)),
                ('canonical_url', models.URLField(blank=True, max_length=2048, null=True)),
                ('robots_directives', models.JSONField(blank=True, null=True)),
                ('schema_markup', models.JSONField(blank=True, null=True)),
                ('word_count', models.IntegerField(default=0)),
                ('status_code', models.IntegerField(default=200)),
                ('content_type', models.CharField(blank=True, max_length=100, null=True)),
                ('language', models.CharField(blank=True, max_length=10, null=True)),
                ('viewport', models.CharField(blank=True, max_length=500, null=True)),
                ('encoding', models.CharField(blank=True, max_length=50, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('crawl', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='seo_insights.seocrawl')),
            ],
        ),
        migrations.CreateModel(
            name='SEOReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('overview', models.JSONField(default=dict)),
                ('metadata_score', models.FloatField(blank=True, null=True)),
                ('content_score', models.FloatField(blank=True, null=True)),
                ('technical_score', models.FloatField(blank=True, null=True)),
                ('mobile_score', models.FloatField(blank=True, null=True)),
                ('overall_score', models.FloatField(blank=True, null=True)),
                ('ai_seo_score', models.FloatField(blank=True, null=True)),
                ('improvement_suggestions', models.JSONField(blank=True, null=True)),
                ('generated_at', models.DateTimeField(auto_now_add=True)),
                ('crawl', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='report', to='seo_insights.seocrawl')),
            ],
        ),
        migrations.CreateModel(
            name='SEOProject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('site_url', models.URLField(max_length=2000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='seo_projects', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'SEO Project',
                'verbose_name_plural': 'SEO Projects',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SEOPerformance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lcp', models.FloatField(blank=True, null=True)),
                ('fid', models.FloatField(blank=True, null=True)),
                ('cls', models.FloatField(blank=True, null=True)),
                ('ttfb', models.FloatField(blank=True, null=True)),
                ('page_speed_mobile', models.IntegerField(blank=True, null=True)),
                ('page_speed_desktop', models.IntegerField(blank=True, null=True)),
                ('mobile_friendly', models.BooleanField(default=False)),
                ('mobile_issues', models.JSONField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('metadata', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='seo_insights.seometadata')),
            ],
        ),
        migrations.CreateModel(
            name='SEOLink',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.URLField(max_length=2048)),
                ('text', models.TextField(blank=True, null=True)),
                ('type', models.CharField(choices=[('internal', 'Internal'), ('external', 'External'), ('resource', 'Resource')], max_length=20)),
                ('follow', models.BooleanField(default=True)),
                ('status_code', models.IntegerField(blank=True, null=True)),
                ('anchor_type', models.CharField(choices=[('text', 'Text'), ('image', 'Image'), ('button', 'Button')], default='text', max_length=20)),
                ('rel_attributes', models.JSONField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('metadata', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='seo_insights.seometadata')),
            ],
        ),
        migrations.CreateModel(
            name='SEOIssue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.URLField(max_length=2048)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('severity', models.CharField(choices=[('critical', 'Critical'), ('major', 'Major'), ('minor', 'Minor'), ('info', 'Info')], max_length=20)),
                ('category', models.CharField(choices=[('metadata', 'Metadata'), ('content', 'Content'), ('technical', 'Technical'), ('links', 'Links'), ('images', 'Images'), ('performance', 'Performance'), ('mobile', 'Mobile'), ('structured_data', 'Structured Data'), ('ai_seo', 'AI SEO'), ('other', 'Other')], max_length=50)),
                ('recommendation', models.TextField(blank=True, null=True)),
                ('impact_score', models.FloatField(default=0)),
                ('priority', models.CharField(choices=[('critical', 'Critical'), ('high', 'High'), ('medium', 'Medium'), ('low', 'Low')], default='low', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('crawl', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='seo_insights.seocrawl')),
            ],
        ),
        migrations.CreateModel(
            name='SEOImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.URLField(max_length=2048)),
                ('alt_text', models.TextField(blank=True, null=True)),
                ('title', models.CharField(blank=True, max_length=1000, null=True)),
                ('filename', models.CharField(blank=True, max_length=500, null=True)),
                ('filesize', models.IntegerField(blank=True, null=True)),
                ('width', models.IntegerField(blank=True, null=True)),
                ('height', models.IntegerField(blank=True, null=True)),
                ('lazy_loaded', models.BooleanField(default=False)),
                ('format', models.CharField(blank=True, max_length=20, null=True)),
                ('compression_ratio', models.FloatField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('metadata', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='seo_insights.seometadata')),
            ],
        ),
        migrations.AddField(
            model_name='seocrawl',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='crawls', to='seo_insights.seoproject'),
        ),
        migrations.CreateModel(
            name='SEOContentAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('keyword_density', models.JSONField(blank=True, null=True)),
                ('readability_score', models.FloatField(blank=True, null=True)),
                ('sentiment_score', models.FloatField(blank=True, null=True)),
                ('topical_relevance', models.FloatField(blank=True, null=True)),
                ('ai_readiness_score', models.FloatField(blank=True, null=True)),
                ('content_quality_score', models.FloatField(blank=True, null=True)),
                ('semantic_topics', models.JSONField(blank=True, null=True)),
                ('content_structure', models.JSONField(blank=True, null=True)),
                ('content_uniqueness', models.FloatField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('metadata', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='seo_insights.seometadata')),
            ],
        ),
        migrations.AddIndex(
            model_name='seometadata',
            index=models.Index(fields=['crawl', 'url'], name='seo_insight_crawl_i_d3c336_idx'),
        ),
        migrations.AddIndex(
            model_name='seometadata',
            index=models.Index(fields=['status_code'], name='seo_insight_status__bfcce2_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='seometadata',
            unique_together={('crawl', 'url')},
        ),
        migrations.AddIndex(
            model_name='seolink',
            index=models.Index(fields=['metadata', 'type'], name='seo_insight_metadat_828df3_idx'),
        ),
        migrations.AddIndex(
            model_name='seolink',
            index=models.Index(fields=['status_code'], name='seo_insight_status__95204d_idx'),
        ),
        migrations.AddIndex(
            model_name='seoissue',
            index=models.Index(fields=['crawl', 'category'], name='seo_insight_crawl_i_faa9fe_idx'),
        ),
        migrations.AddIndex(
            model_name='seoissue',
            index=models.Index(fields=['severity'], name='seo_insight_severit_bd2a58_idx'),
        ),
        migrations.AddIndex(
            model_name='seoissue',
            index=models.Index(fields=['priority'], name='seo_insight_priorit_80dd04_idx'),
        ),
        migrations.AddIndex(
            model_name='seoimage',
            index=models.Index(fields=['metadata'], name='seo_insight_metadat_4b23bb_idx'),
        ),
    ]
