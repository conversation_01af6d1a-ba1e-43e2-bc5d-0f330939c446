# Generated by Django 4.2.16 on 2025-02-19 17:35

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('seo_insights', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SEOSitemapAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.URLField(max_length=2048)),
                ('in_sitemap', models.BooleanField(default=False)),
                ('indexed', models.BooleanField(default=False)),
                ('last_modified', models.DateTimeField(blank=True, null=True)),
                ('change_frequency', models.CharField(blank=True, max_length=20, null=True)),
                ('priority', models.FloatField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('crawl', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='seo_insights.seocrawl')),
            ],
        ),
        migrations.CreateModel(
            name='SEOAdvancedAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tf_idf_analysis', models.JSONField(blank=True, null=True)),
                ('tf_idf_keywords', models.JSONField(blank=True, null=True)),
                ('lsi_keywords', models.JSONField(blank=True, null=True)),
                ('topic_clusters', models.JSONField(blank=True, null=True)),
                ('semantic_topics', models.JSONField(blank=True, null=True)),
                ('content_categories', models.JSONField(blank=True, null=True)),
                ('competitor_topics', models.JSONField(blank=True, null=True)),
                ('missing_topics', models.JSONField(blank=True, null=True)),
                ('topic_opportunities', models.JSONField(blank=True, null=True)),
                ('paa_questions', models.JSONField(blank=True, null=True)),
                ('related_questions', models.JSONField(blank=True, null=True)),
                ('question_clusters', models.JSONField(blank=True, null=True)),
                ('user_intent', models.CharField(blank=True, max_length=50, null=True)),
                ('search_intent_signals', models.JSONField(blank=True, null=True)),
                ('user_journey_stage', models.CharField(blank=True, max_length=50, null=True)),
                ('entity_analysis', models.JSONField(blank=True, null=True)),
                ('named_entities', models.JSONField(blank=True, null=True)),
                ('entity_relationships', models.JSONField(blank=True, null=True)),
                ('javascript_content', models.JSONField(blank=True, null=True)),
                ('dynamic_elements', models.JSONField(blank=True, null=True)),
                ('js_rendered_content', models.JSONField(blank=True, null=True)),
                ('competitor_comparison', models.JSONField(blank=True, null=True)),
                ('content_gaps', models.JSONField(blank=True, null=True)),
                ('competitive_advantages', models.JSONField(blank=True, null=True)),
                ('readability_scores', models.JSONField(blank=True, null=True)),
                ('content_depth_score', models.FloatField(blank=True, null=True)),
                ('expertise_signals', models.JSONField(blank=True, null=True)),
                ('sentiment_analysis', models.JSONField(blank=True, null=True)),
                ('key_phrases', models.JSONField(blank=True, null=True)),
                ('content_classification', models.JSONField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('metadata', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='seo_insights.seometadata')),
            ],
        ),
        migrations.CreateModel(
            name='SEOCompetitorAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('competitor_url', models.URLField(max_length=2048)),
                ('competitor_domain', models.CharField(max_length=255)),
                ('last_analyzed', models.DateTimeField(auto_now=True)),
                ('keyword_overlap', models.JSONField(blank=True, null=True)),
                ('content_gaps', models.JSONField(blank=True, null=True)),
                ('content_length_diff', models.IntegerField(blank=True, null=True)),
                ('content_quality_comparison', models.JSONField(blank=True, null=True)),
                ('backlink_comparison', models.JSONField(blank=True, null=True)),
                ('common_backlinks', models.JSONField(blank=True, null=True)),
                ('unique_backlinks', models.JSONField(blank=True, null=True)),
                ('serp_position', models.IntegerField(blank=True, null=True)),
                ('serp_features', models.JSONField(blank=True, null=True)),
                ('featured_snippets', models.JSONField(blank=True, null=True)),
                ('page_speed_comparison', models.JSONField(blank=True, null=True)),
                ('mobile_friendliness_comparison', models.JSONField(blank=True, null=True)),
                ('technical_issues_comparison', models.JSONField(blank=True, null=True)),
                ('heading_structure', models.JSONField(blank=True, null=True)),
                ('content_sections', models.JSONField(blank=True, null=True)),
                ('media_usage', models.JSONField(blank=True, null=True)),
                ('meta_tags_comparison', models.JSONField(blank=True, null=True)),
                ('schema_markup_comparison', models.JSONField(blank=True, null=True)),
                ('internal_linking_comparison', models.JSONField(blank=True, null=True)),
                ('estimated_traffic', models.IntegerField(blank=True, null=True)),
                ('keyword_rankings', models.JSONField(blank=True, null=True)),
                ('market_share', models.FloatField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('metadata', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='seo_insights.seometadata')),
            ],
            options={
                'indexes': [models.Index(fields=['metadata', 'competitor_domain'], name='seo_insight_metadat_afa258_idx'), models.Index(fields=['serp_position'], name='seo_insight_serp_po_e1b9bb_idx')],
            },
        ),
        migrations.CreateModel(
            name='SEOBacklinkAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('referring_domain', models.URLField(max_length=2048)),
                ('referring_page', models.URLField(max_length=2048)),
                ('first_seen', models.DateTimeField(blank=True, null=True)),
                ('last_seen', models.DateTimeField(blank=True, null=True)),
                ('anchor_text', models.TextField(blank=True, null=True)),
                ('link_type', models.CharField(choices=[('dofollow', 'Dofollow'), ('nofollow', 'Nofollow'), ('ugc', 'UGC'), ('sponsored', 'Sponsored')], max_length=20)),
                ('toxic_score', models.FloatField(blank=True, null=True)),
                ('spam_score', models.FloatField(blank=True, null=True)),
                ('trust_flow', models.FloatField(blank=True, null=True)),
                ('citation_flow', models.FloatField(blank=True, null=True)),
                ('domain_authority', models.IntegerField(blank=True, null=True)),
                ('page_authority', models.IntegerField(blank=True, null=True)),
                ('domain_rating', models.FloatField(blank=True, null=True)),
                ('surrounding_text', models.TextField(blank=True, null=True)),
                ('link_placement', models.CharField(blank=True, max_length=50, null=True)),
                ('content_relevance', models.FloatField(blank=True, null=True)),
                ('status_code', models.IntegerField(blank=True, null=True)),
                ('redirect_chain', models.JSONField(blank=True, null=True)),
                ('is_broken', models.BooleanField(default=False)),
                ('traffic_value', models.FloatField(blank=True, null=True)),
                ('ranking_impact', models.FloatField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('metadata', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='seo_insights.seometadata')),
            ],
            options={
                'indexes': [models.Index(fields=['metadata', 'referring_domain'], name='seo_insight_metadat_3d34fd_idx'), models.Index(fields=['toxic_score'], name='seo_insight_toxic_s_ac854f_idx'), models.Index(fields=['domain_authority'], name='seo_insight_domain__415a87_idx')],
            },
        ),
    ]
