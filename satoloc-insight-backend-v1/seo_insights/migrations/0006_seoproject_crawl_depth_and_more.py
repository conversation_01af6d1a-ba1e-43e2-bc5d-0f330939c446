# Generated by Django 4.2.16 on 2025-02-21 09:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('seo_insights', '0005_seoranking_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='seoproject',
            name='crawl_depth',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='seoproject',
            name='enable_competitor_analysis',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='seoproject',
            name='enable_regional_analysis',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='seoproject',
            name='max_pages',
            field=models.IntegerField(blank=True, null=True),
        ),
    ]
