# seo_insights/signals.py
from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import SEOCrawl, SEOReport
from .tasks import analyze_seo_data

@receiver(post_save, sender=SEOCrawl)
def handle_crawl_completed(sender, instance, created, **kwargs):
    """Automatically trigger analysis when a crawl is completed."""
    if not created and instance.status == 'completed':
        # Check if a report already exists
        if not SEOReport.objects.filter(crawl=instance).exists():
            # Start analysis task
            analyze_seo_data.delay(instance.id)
