# seo_insights/views.py
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from celery.result import AsyncResult

from .models import (
    SEOProject, SEOCrawl, SEOMetadata, SEOContentAnalysis,
    SEOPerformance, SEOIssue, SEOReport
)
from .serializers import (
    SEOProjectSerializer, SEOCrawlSerializer, SEOMetadataSerializer,
    SEOContentAnalysisSerializer, SEOIssueSerializer, SEOReportSerializer
)
from .tasks import start_seo_crawl, analyze_seo_data

class SEOProjectViewSet(viewsets.ModelViewSet):
    """ViewSet for managing SEO projects"""
    serializer_class = SEOProjectSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return SEOProject.objects.filter(user=self.request.user)
    
    def perform_create(self, serializer):
        serializer.save(user=self.request.user)
    
    @action(detail=True, methods=['post'])
    def start_crawl(self, request, pk=None):
        """Start crawling the project's site"""
        project = self.get_object()
        
        # Create a new crawl record
        crawl = SEOCrawl.objects.create(
            project=project,
            status='pending'
        )
        
        # Start the crawling task asynchronously
        task = start_seo_crawl.delay(crawl.id)
        
        return Response({
            'status': 'Crawling started',
            'crawl_id': crawl.id,
            'task_id': task.id
        })
    
    @action(detail=True, methods=['get'])
    def crawls(self, request, pk=None):
        """Get all crawls for this project"""
        project = self.get_object()
        crawls = SEOCrawl.objects.filter(project=project)
        serializer = SEOCrawlSerializer(crawls, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def latest_report(self, request, pk=None):
        """Get the latest SEO report for this project"""
        project = self.get_object()
        latest_crawl = SEOCrawl.objects.filter(
            project=project,
            status='completed'
        ).order_by('-created_at').first()
        
        if not latest_crawl:
            return Response(
                {'detail': 'No completed crawls found for this project'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        report = SEOReport.objects.filter(crawl=latest_crawl).first()
        
        if not report:
            return Response(
                {'detail': 'No report found for the latest completed crawl'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        serializer = SEOReportSerializer(report)
        return Response(serializer.data)

class SEOCrawlViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for viewing SEO crawls"""
    serializer_class = SEOCrawlSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return SEOCrawl.objects.filter(project__user=self.request.user)
    
    @action(detail=True, methods=['get'])
    def status(self, request, pk=None):
        """Get the status of a crawl"""
        crawl = self.get_object()
        return Response({
            'id': crawl.id,
            'status': crawl.status,
            'pages_crawled': crawl.pages_crawled,
            'start_time': crawl.start_time,
            'end_time': crawl.end_time,
            'duration': crawl.duration,
            'error_message': crawl.error_message
        })
    
    @action(detail=True, methods=['post'])
    def analyze(self, request, pk=None):
        """Start analyzing the crawled data"""
        crawl = self.get_object()
        
        if crawl.status != 'completed':
            return Response(
                {'detail': 'Cannot analyze crawl that is not completed'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Start the analysis task asynchronously
        task = analyze_seo_data.delay(crawl.id)
        
        return Response({
            'status': 'Analysis started',
            'task_id': task.id
        })
    
    @action(detail=True, methods=['get'])
    def report(self, request, pk=None):
        """Get the SEO report for this crawl"""
        crawl = self.get_object()
        report = get_object_or_404(SEOReport, crawl=crawl)
        serializer = SEOReportSerializer(report)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def issues(self, request, pk=None):
        """Get SEO issues found during this crawl"""
        crawl = self.get_object()
        
        # Apply filters if specified
        category = request.query_params.get('category')
        severity = request.query_params.get('severity')
        
        issues = SEOIssue.objects.filter(crawl=crawl)
        
        if category:
            issues = issues.filter(category=category)
        
        if severity:
            issues = issues.filter(severity=severity)
        
        # Paginate results
        page = self.paginate_queryset(issues)
        if page is not None:
            serializer = SEOIssueSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = SEOIssueSerializer(issues, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def metadata(self, request, pk=None):
        """Get metadata for pages crawled in this crawl"""
        crawl = self.get_object()
        
        # Apply filters if specified
        url_filter = request.query_params.get('url')
        status_code = request.query_params.get('status_code')
        
        metadata = SEOMetadata.objects.filter(crawl=crawl)
        
        if url_filter:
            metadata = metadata.filter(url__contains=url_filter)
        
        if status_code:
            metadata = metadata.filter(status_code=int(status_code))
        
        # Paginate results
        page = self.paginate_queryset(metadata)
        if page is not None:
            serializer = SEOMetadataSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = SEOMetadataSerializer(metadata, many=True)
        return Response(serializer.data)

class SEOIssueViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for viewing SEO issues"""
    serializer_class = SEOIssueSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return SEOIssue.objects.filter(crawl__project__user=self.request.user)

class SEOReportViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for viewing SEO reports"""
    serializer_class = SEOReportSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return SEOReport.objects.filter(crawl__project__user=self.request.user)