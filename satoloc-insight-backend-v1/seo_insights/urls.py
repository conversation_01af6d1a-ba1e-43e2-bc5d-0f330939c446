# seo_insights/urls.py
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'projects', views.SEOProjectViewSet, basename='seo-project')
router.register(r'crawls', views.SEOCrawlViewSet, basename='seo-crawl')
router.register(r'issues', views.SEOIssueViewSet, basename='seo-issue')
router.register(r'reports', views.SEOReportViewSet, basename='seo-report')

app_name = 'seo_insights'

urlpatterns = [
    path('', include(router.urls)),
]
