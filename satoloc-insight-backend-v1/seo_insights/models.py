# seo_insights/models.py
from django.db import models
from django.conf import settings
from django.utils import timezone

class SEOProject(models.Model):
    """Model to store SEO project information."""
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='seo_projects'
    )
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    site_url = models.URLField(max_length=2000)
    
    # Crawl configuration
    max_pages = models.IntegerField(null=True, blank=True)
    crawl_depth = models.IntegerField(null=True, blank=True)
    enable_competitor_analysis = models.BooleanField(default=True)
    enable_regional_analysis = models.BooleanField(default=True)
    
    # Google Search Console integration
    search_console_credentials = models.TextField(null=True, blank=True)  # Encrypted credentials
    search_console_enabled = models.BooleanField(default=False)  # Track if GSC is enabled
    search_console_verified = models.BooleanField(default=False)  # Track if site is verified in GSC
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = 'SEO Project'
        verbose_name_plural = 'SEO Projects'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.site_url})"
    
    @property
    def has_search_console(self):
        """Check if project has valid Search Console integration."""
        return bool(self.search_console_enabled and 
                   self.search_console_verified and 
                   self.search_console_credentials)

class SEOCrawl(models.Model):
    """Model to store SEO crawl execution details."""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed')
    ]
    
    project = models.ForeignKey(
        SEOProject, 
        on_delete=models.CASCADE,
        related_name='crawls'
    )
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    pages_crawled = models.IntegerField(default=0)
    start_time = models.DateTimeField(default=timezone.now)
    end_time = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(null=True, blank=True)
    external_job_id = models.CharField(max_length=255, null=True, blank=True)
    external_results = models.JSONField(default=dict, blank=True)
    configuration = models.JSONField(default=dict, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Crawl for {self.project.name} - {self.status}"
    
    @property
    def duration(self):
        if self.end_time and self.start_time:
            return (self.end_time - self.start_time).total_seconds()
        return None

class SEOMetadata(models.Model):
    """Model for storing page metadata."""
    crawl = models.ForeignKey(SEOCrawl, on_delete=models.CASCADE)
    url = models.URLField(max_length=2048)
    title = models.CharField(max_length=1000, null=True, blank=True)
    meta_description = models.TextField(null=True, blank=True)
    h1 = models.JSONField(null=True, blank=True)  # List of H1 headings
    h2 = models.JSONField(null=True, blank=True)  # List of H2 headings
    canonical_url = models.URLField(max_length=2048, null=True, blank=True)
    robots_directives = models.JSONField(null=True, blank=True)  # List of robots directives
    schema_markup = models.JSONField(null=True, blank=True)  # List of schema.org markup
    word_count = models.IntegerField(default=0)
    status_code = models.IntegerField(default=200)
    content_type = models.CharField(max_length=100, null=True, blank=True)
    language = models.CharField(max_length=10, null=True, blank=True)
    viewport = models.CharField(max_length=500, null=True, blank=True)
    encoding = models.CharField(max_length=50, null=True, blank=True)
    
    # New fields for JavaScript content
    javascript_content = models.JSONField(null=True, blank=True)  # Dynamic content loaded via JS
    dynamic_elements = models.JSONField(null=True, blank=True)  # Interactive elements
    js_dependencies = models.JSONField(null=True, blank=True)  # JS framework dependencies
    js_errors = models.JSONField(null=True, blank=True)  # JavaScript errors detected
    
    # New fields for SERP analysis
    serp_preview = models.JSONField(null=True, blank=True)  # SERP preview data
    featured_snippets = models.JSONField(null=True, blank=True)  # Featured snippet opportunities
    rich_results = models.JSONField(null=True, blank=True)  # Rich result types detected
    
    # New fields for backlink analysis
    toxic_backlinks = models.JSONField(null=True, blank=True)  # List of toxic backlinks
    anchor_text_analysis = models.JSONField(null=True, blank=True)  # Detailed anchor text data
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('crawl', 'url')
        indexes = [
            models.Index(fields=['crawl', 'url']),
            models.Index(fields=['status_code']),
        ]

    def __str__(self):
        return f"{self.url} ({self.crawl.id})"

class SEOPerformance(models.Model):
    """Model for storing page performance metrics."""
    metadata = models.OneToOneField(SEOMetadata, on_delete=models.CASCADE)
    lcp = models.FloatField(null=True, blank=True)  # Largest Contentful Paint
    fid = models.FloatField(null=True, blank=True)  # First Input Delay
    cls = models.FloatField(null=True, blank=True)  # Cumulative Layout Shift
    ttfb = models.FloatField(null=True, blank=True)  # Time to First Byte
    page_speed_mobile = models.IntegerField(null=True, blank=True)
    page_speed_desktop = models.IntegerField(null=True, blank=True)
    mobile_friendly = models.BooleanField(default=False)
    mobile_issues = models.JSONField(null=True, blank=True)  # List of mobile-specific issues
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Performance for {self.metadata.url}"

class SEOContentAnalysis(models.Model):
    """Model for storing content analysis results."""
    metadata = models.OneToOneField(SEOMetadata, on_delete=models.CASCADE)
    keyword_density = models.JSONField(null=True, blank=True)  # Dict of keywords and their density
    readability_score = models.FloatField(null=True, blank=True)
    sentiment_score = models.FloatField(null=True, blank=True)
    topical_relevance = models.FloatField(null=True, blank=True)
    ai_readiness_score = models.FloatField(null=True, blank=True)
    content_quality_score = models.FloatField(null=True, blank=True)
    semantic_topics = models.JSONField(null=True, blank=True)  # List of semantic topics
    content_structure = models.JSONField(null=True, blank=True)  # Dict of content structure analysis
    content_uniqueness = models.FloatField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Content Analysis for {self.metadata.url}"

class SEOLink(models.Model):
    """Model for storing links found on pages."""
    metadata = models.ForeignKey(SEOMetadata, on_delete=models.CASCADE)
    url = models.URLField(max_length=2048)
    text = models.TextField(null=True, blank=True)
    type = models.CharField(max_length=20, choices=[
        ('internal', 'Internal'),
        ('external', 'External'),
        ('resource', 'Resource')
    ])
    follow = models.BooleanField(default=True)
    status_code = models.IntegerField(null=True, blank=True)
    anchor_type = models.CharField(max_length=20, default='text', choices=[
        ('text', 'Text'),
        ('image', 'Image'),
        ('button', 'Button')
    ])
    rel_attributes = models.JSONField(null=True, blank=True)  # List of rel attributes
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['metadata', 'type']),
            models.Index(fields=['status_code']),
        ]

    def __str__(self):
        return f"{self.url} ({self.type})"

class SEOImage(models.Model):
    """Model for storing images found on pages."""
    metadata = models.ForeignKey(SEOMetadata, on_delete=models.CASCADE)
    url = models.URLField(max_length=2048)
    alt_text = models.TextField(null=True, blank=True)
    title = models.CharField(max_length=1000, null=True, blank=True)
    filename = models.CharField(max_length=500, null=True, blank=True)
    filesize = models.IntegerField(null=True, blank=True)
    width = models.IntegerField(null=True, blank=True)
    height = models.IntegerField(null=True, blank=True)
    lazy_loaded = models.BooleanField(default=False)
    format = models.CharField(max_length=20, null=True, blank=True)
    compression_ratio = models.FloatField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['metadata']),
        ]

    def __str__(self):
        return f"{self.url}"

class SEOIssue(models.Model):
    """Model for storing SEO issues."""
    crawl = models.ForeignKey(SEOCrawl, on_delete=models.CASCADE)
    url = models.URLField(max_length=2048)
    title = models.CharField(max_length=200)
    description = models.TextField()
    severity = models.CharField(max_length=20, choices=[
        ('critical', 'Critical'),
        ('major', 'Major'),
        ('minor', 'Minor'),
        ('info', 'Info')
    ])
    category = models.CharField(max_length=50, choices=[
        ('metadata', 'Metadata'),
        ('content', 'Content'),
        ('technical', 'Technical'),
        ('links', 'Links'),
        ('images', 'Images'),
        ('performance', 'Performance'),
        ('mobile', 'Mobile'),
        ('structured_data', 'Structured Data'),
        ('ai_seo', 'AI SEO'),
        ('other', 'Other')
    ])
    recommendation = models.TextField(null=True, blank=True)
    impact_score = models.FloatField(default=0)  # 0-100 score indicating issue impact
    priority = models.CharField(max_length=20, choices=[
        ('critical', 'Critical'),
        ('high', 'High'),
        ('medium', 'Medium'),
        ('low', 'Low')
    ], default='low')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['crawl', 'category']),
            models.Index(fields=['severity']),
            models.Index(fields=['priority']),
        ]

    def __str__(self):
        return f"{self.title} - {self.url}"

class SEOReport(models.Model):
    """Model to store generated SEO reports."""
    crawl = models.OneToOneField(
        SEOCrawl, 
        on_delete=models.CASCADE,
        related_name='report'
    )
    
    # Overview metrics matching frontend display
    domain_rating = models.FloatField(null=True, blank=True)  # Domain Rating (0-100)
    organic_traffic = models.IntegerField(null=True, blank=True)  # Monthly organic traffic
    search_rankings = models.FloatField(null=True, blank=True)  # Search rankings percentage
    search_terms = models.IntegerField(null=True, blank=True)  # Number of ranking search terms
    site_links = models.IntegerField(null=True, blank=True)  # Total number of site links
    content_score = models.FloatField(null=True, blank=True)  # Content quality score (0-100)
    
    # Existing fields
    overview = models.JSONField(default=dict)
    metadata_score = models.FloatField(null=True, blank=True)
    content_score = models.FloatField(null=True, blank=True)
    technical_score = models.FloatField(null=True, blank=True)
    mobile_score = models.FloatField(null=True, blank=True)
    overall_score = models.FloatField(null=True, blank=True)
    ai_seo_score = models.FloatField(null=True, blank=True)
    improvement_suggestions = models.JSONField(null=True, blank=True)
    generated_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"SEO Report for {self.crawl.project.name} - {self.generated_at}"

class SEOCompetitorAnalysis(models.Model):
    """Model for storing competitor analysis data."""
    metadata = models.ForeignKey(SEOMetadata, on_delete=models.CASCADE)
    competitor_url = models.URLField(max_length=2048)
    competitor_domain = models.CharField(max_length=255)
    last_analyzed = models.DateTimeField(auto_now=True)
    
    # Content Comparison
    keyword_overlap = models.JSONField(null=True, blank=True)
    content_gaps = models.JSONField(null=True, blank=True)
    content_length_diff = models.IntegerField(null=True, blank=True)
    content_quality_comparison = models.JSONField(null=True, blank=True)
    
    # Backlink Comparison
    backlink_comparison = models.JSONField(null=True, blank=True)
    common_backlinks = models.JSONField(null=True, blank=True)
    unique_backlinks = models.JSONField(null=True, blank=True)
    
    # SERP Analysis
    serp_position = models.IntegerField(null=True, blank=True)
    serp_features = models.JSONField(null=True, blank=True)
    featured_snippets = models.JSONField(null=True, blank=True)
    
    # Technical Comparison
    page_speed_comparison = models.JSONField(null=True, blank=True)
    mobile_friendliness_comparison = models.JSONField(null=True, blank=True)
    technical_issues_comparison = models.JSONField(null=True, blank=True)
    
    # Content Structure
    heading_structure = models.JSONField(null=True, blank=True)
    content_sections = models.JSONField(null=True, blank=True)
    media_usage = models.JSONField(null=True, blank=True)
    
    # On-Page Elements
    meta_tags_comparison = models.JSONField(null=True, blank=True)
    schema_markup_comparison = models.JSONField(null=True, blank=True)
    internal_linking_comparison = models.JSONField(null=True, blank=True)
    
    # Performance Metrics
    estimated_traffic = models.IntegerField(null=True, blank=True)
    keyword_rankings = models.JSONField(null=True, blank=True)
    market_share = models.FloatField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['metadata', 'competitor_domain']),
            models.Index(fields=['serp_position']),
        ]

    def __str__(self):
        return f"Competitor Analysis: {self.competitor_domain}"

class SEOAdvancedAnalysis(models.Model):
    """Model for storing advanced SEO analysis data."""
    metadata = models.ForeignKey(SEOMetadata, on_delete=models.CASCADE)
    
    # JavaScript Content Analysis
    javascript_content = models.JSONField(null=True, blank=True)  # JavaScript rendered content
    dynamic_elements = models.JSONField(null=True, blank=True)    # Dynamic page elements
    js_dependencies = models.JSONField(null=True, blank=True)     # JavaScript library dependencies
    js_errors = models.JSONField(null=True, blank=True)          # JavaScript execution errors
    
    # SERP Analysis
    serp_preview = models.JSONField(null=True, blank=True)       # How the page appears in search results
    featured_snippets = models.JSONField(null=True, blank=True)  # Featured snippet opportunities
    rich_results = models.JSONField(null=True, blank=True)       # Rich result types detected
    serp_features = models.JSONField(null=True, blank=True)      # SERP features present
    
    # Backlink Analysis
    toxic_backlinks = models.JSONField(null=True, blank=True)    # List of potentially harmful backlinks
    anchor_text_analysis = models.JSONField(null=True, blank=True)  # Detailed anchor text distribution
    link_neighborhood = models.JSONField(null=True, blank=True)   # Analysis of linking sites' quality
    
    # TF-IDF Analysis
    tf_idf_analysis = models.JSONField(null=True, blank=True)
    tf_idf_keywords = models.JSONField(null=True, blank=True)  # Top keywords by TF-IDF score
    lsi_keywords = models.JSONField(null=True, blank=True)     # Latent Semantic Indexing keywords
    
    # Topic Analysis
    topic_clusters = models.JSONField(null=True, blank=True)
    semantic_topics = models.JSONField(null=True, blank=True)
    content_categories = models.JSONField(null=True, blank=True)
    
    # Content Gap Analysis
    competitor_topics = models.JSONField(null=True, blank=True)
    missing_topics = models.JSONField(null=True, blank=True)
    topic_opportunities = models.JSONField(null=True, blank=True)
    
    # Question Analysis
    paa_questions = models.JSONField(null=True, blank=True)    # People Also Ask questions
    related_questions = models.JSONField(null=True, blank=True)
    question_clusters = models.JSONField(null=True, blank=True)
    
    # User Intent Analysis
    user_intent = models.CharField(max_length=50, null=True, blank=True)
    search_intent_signals = models.JSONField(null=True, blank=True)
    user_journey_stage = models.CharField(max_length=50, null=True, blank=True)
    
    # Entity Analysis
    entity_analysis = models.JSONField(null=True, blank=True)
    named_entities = models.JSONField(null=True, blank=True)
    entity_relationships = models.JSONField(null=True, blank=True)
    
    # Content Quality Metrics
    readability_scores = models.JSONField(null=True, blank=True)
    content_depth_score = models.FloatField(null=True, blank=True)
    expertise_signals = models.JSONField(null=True, blank=True)
    
    # NLP Analysis Results
    sentiment_analysis = models.JSONField(null=True, blank=True)
    key_phrases = models.JSONField(null=True, blank=True)
    content_classification = models.JSONField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Advanced Analysis for {self.metadata.url}"

class SEOBacklinkAnalysis(models.Model):
    """Model for storing detailed backlink analysis."""
    metadata = models.ForeignKey(SEOMetadata, on_delete=models.CASCADE)
    referring_domain = models.URLField(max_length=2048)
    referring_page = models.URLField(max_length=2048)
    first_seen = models.DateTimeField(null=True, blank=True)
    last_seen = models.DateTimeField(null=True, blank=True)
    
    # Link Attributes
    anchor_text = models.TextField(null=True, blank=True)
    link_type = models.CharField(max_length=20, choices=[
        ('dofollow', 'Dofollow'),
        ('nofollow', 'Nofollow'),
        ('ugc', 'UGC'),
        ('sponsored', 'Sponsored')
    ])
    
    # Link Quality Metrics
    toxic_score = models.FloatField(null=True, blank=True)
    spam_score = models.FloatField(null=True, blank=True)
    trust_flow = models.FloatField(null=True, blank=True)
    citation_flow = models.FloatField(null=True, blank=True)
    
    # Domain Metrics
    domain_authority = models.IntegerField(null=True, blank=True)
    page_authority = models.IntegerField(null=True, blank=True)
    domain_rating = models.FloatField(null=True, blank=True)
    
    # Link Context
    surrounding_text = models.TextField(null=True, blank=True)
    link_placement = models.CharField(max_length=50, null=True, blank=True)  # header, content, footer, etc.
    content_relevance = models.FloatField(null=True, blank=True)
    
    # Technical Details
    status_code = models.IntegerField(null=True, blank=True)
    redirect_chain = models.JSONField(null=True, blank=True)
    is_broken = models.BooleanField(default=False)
    
    # Link Impact
    traffic_value = models.FloatField(null=True, blank=True)
    ranking_impact = models.FloatField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['metadata', 'referring_domain']),
            models.Index(fields=['toxic_score']),
            models.Index(fields=['domain_authority']),
        ]

    def __str__(self):
        return f"Backlink from {self.referring_domain}"

class SEOSitemapAnalysis(models.Model):
    """Model for storing sitemap analysis data."""
    crawl = models.ForeignKey(SEOCrawl, on_delete=models.CASCADE)
    url = models.URLField(max_length=2048)
    in_sitemap = models.BooleanField(default=False)
    indexed = models.BooleanField(default=False)
    last_modified = models.DateTimeField(null=True, blank=True)
    change_frequency = models.CharField(max_length=20, null=True, blank=True)
    priority = models.FloatField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

class SEOSearchConsoleData(models.Model):
    """Model for storing Google Search Console data."""
    project = models.ForeignKey(SEOProject, on_delete=models.CASCADE)
    url = models.URLField(max_length=2048)
    date = models.DateField()
    
    # Search performance metrics
    clicks = models.IntegerField(default=0)
    impressions = models.IntegerField(default=0)
    ctr = models.FloatField(default=0)  # Click-through rate
    average_position = models.FloatField(default=0)
    
    # Device breakdown
    desktop_clicks = models.IntegerField(default=0)
    desktop_impressions = models.IntegerField(default=0)
    mobile_clicks = models.IntegerField(default=0)
    mobile_impressions = models.IntegerField(default=0)
    tablet_clicks = models.IntegerField(default=0)
    tablet_impressions = models.IntegerField(default=0)
    
    # Search appearance
    search_appearance = models.JSONField(null=True, blank=True)  # Different SERP features
    
    # Page indexing status
    is_indexed = models.BooleanField(default=True)
    indexing_status = models.CharField(max_length=50, null=True, blank=True)
    last_crawled = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ('project', 'url', 'date')
        indexes = [
            models.Index(fields=['project', 'date']),
            models.Index(fields=['url']),
        ]
    
    def __str__(self):
        return f"{self.url} - {self.date}"

class SEOSearchConsoleKeyword(models.Model):
    """Model for storing keyword-level data from Search Console."""
    search_console_data = models.ForeignKey(SEOSearchConsoleData, on_delete=models.CASCADE)
    keyword = models.CharField(max_length=500)
    
    # Keyword performance metrics
    clicks = models.IntegerField(default=0)
    impressions = models.IntegerField(default=0)
    ctr = models.FloatField(default=0)
    average_position = models.FloatField(default=0)
    
    # Search intent and context
    search_type = models.CharField(max_length=50, null=True, blank=True)  # web, image, video, etc.
    country = models.CharField(max_length=2, null=True, blank=True)  # ISO country code
    device = models.CharField(max_length=20, null=True, blank=True)  # desktop, mobile, tablet
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['search_console_data', 'keyword']),
            models.Index(fields=['average_position']),
        ]
    
    def __str__(self):
        return f"{self.keyword} ({self.search_console_data.date})"

class SEOSearchConsoleIssue(models.Model):
    """Model for storing Search Console issues and warnings."""
    project = models.ForeignKey(SEOProject, on_delete=models.CASCADE)
    url = models.URLField(max_length=2048)
    
    # Issue details
    issue_type = models.CharField(max_length=100)
    severity = models.CharField(max_length=20, choices=[
        ('error', 'Error'),
        ('warning', 'Warning'),
        ('info', 'Info')
    ])
    discovered = models.DateTimeField()
    
    # Issue status
    status = models.CharField(max_length=20, choices=[
        ('active', 'Active'),
        ('fixed', 'Fixed')
    ])
    fixed_date = models.DateTimeField(null=True, blank=True)
    
    # Additional details
    details = models.JSONField(null=True, blank=True)
    validation_state = models.CharField(max_length=50, null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['project', 'url']),
            models.Index(fields=['issue_type']),
            models.Index(fields=['status']),
            models.Index(fields=['discovered'])
        ]
    
    def __str__(self):
        return f"{self.issue_type} - {self.url}"

class SEORanking(models.Model):
    """Model for storing keyword ranking data."""
    crawl = models.ForeignKey(SEOCrawl, on_delete=models.CASCADE)
    keyword = models.CharField(max_length=500)
    url = models.URLField(max_length=2048)
    position = models.IntegerField()
    search_volume = models.IntegerField(null=True, blank=True)
    competition = models.FloatField(null=True, blank=True)  # 0-1 scale
    cpc = models.FloatField(null=True, blank=True)  # Cost per click
    
    # SERP features
    serp_features = models.JSONField(null=True, blank=True)  # List of SERP features for this keyword
    featured_snippet = models.BooleanField(default=False)
    local_pack = models.BooleanField(default=False)
    
    # Historical data
    previous_position = models.IntegerField(null=True, blank=True)
    position_change = models.IntegerField(null=True, blank=True)
    first_seen = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    
    # Additional metrics
    difficulty = models.FloatField(null=True, blank=True)  # Keyword difficulty score (0-100)
    intent = models.CharField(max_length=50, null=True, blank=True)  # Search intent (informational, transactional, etc.)
    seasonal_trend = models.JSONField(null=True, blank=True)  # Monthly search volume trends
    
    class Meta:
        unique_together = ('crawl', 'keyword', 'url')
        indexes = [
            models.Index(fields=['crawl', 'keyword']),
            models.Index(fields=['position']),
            models.Index(fields=['search_volume']),
            models.Index(fields=['last_updated'])
        ]
    
    def __str__(self):
        return f"{self.keyword} - Position {self.position}"
