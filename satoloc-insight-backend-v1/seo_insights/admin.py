# seo_insights/admin.py
from django.contrib import admin
from django.utils.html import format_html
from .models import (
    SEOProject, SEOCrawl, SEOMetadata, SEOContentAnalysis,
    SEOPerformance, SEOIssue, SEOReport, SEOLink, SEOImage
)

@admin.register(SEOProject)
class SEOProjectAdmin(admin.ModelAdmin):
    list_display = ('name', 'site_url', 'user', 'created_at', 'updated_at')
    list_filter = ('created_at', 'updated_at')
    search_fields = ('name', 'site_url', 'user__email')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(SEOCrawl)
class SEOCrawlAdmin(admin.ModelAdmin):
    list_display = ('id', 'project', 'status', 'pages_crawled', 'start_time', 'end_time', 'get_duration')
    list_filter = ('status', 'created_at')
    search_fields = ('project__name', 'project__site_url')
    readonly_fields = ('created_at', 'pages_crawled', 'duration')
    
    def get_duration(self, obj):
        if obj.duration is not None:
            # Format duration in minutes and seconds
            minutes = int(obj.duration // 60)
            seconds = int(obj.duration % 60)
            return f"{minutes}m {seconds}s"
        return "-"
    get_duration.short_description = 'Duration'

@admin.register(SEOMetadata)
class SEOMetadataAdmin(admin.ModelAdmin):
    list_display = ('url', 'get_title', 'status_code', 'word_count', 'created_at')
    list_filter = ('status_code', 'created_at', 'crawl')
    search_fields = ('url', 'title')
    readonly_fields = ('created_at',)
    
    def get_title(self, obj):
        if obj.title:
            # Truncate long titles
            return obj.title if len(obj.title) <= 50 else f"{obj.title[:50]}..."
        return "-"
    get_title.short_description = 'Title'

@admin.register(SEOIssue)
class SEOIssueAdmin(admin.ModelAdmin):
    list_display = ('title', 'url', 'get_severity_badge', 'category', 'created_at')
    list_filter = ('severity', 'category', 'created_at', 'crawl')
    search_fields = ('title', 'url', 'description')
    readonly_fields = ('created_at',)
    
    def get_severity_badge(self, obj):
        colors = {
            'critical': 'red',
            'major': 'orange',
            'minor': 'blue',
            'info': 'green'
        }
        return format_html(
            '<span style="color: white; background-color: {}; padding: 3px 7px; border-radius: 10px;">{}</span>',
            colors.get(obj.severity, 'gray'),
            obj.severity.title()
        )
    get_severity_badge.short_description = 'Severity'

@admin.register(SEOReport)
class SEOReportAdmin(admin.ModelAdmin):
    list_display = ('id', 'crawl', 'get_overall_score', 'get_metadata_score', 'get_content_score', 'get_technical_score', 'generated_at')
    readonly_fields = ('generated_at',)
    
    def get_overall_score(self, obj):
        if obj.overall_score is not None:
            color = self._get_score_color(obj.overall_score)
            return format_html(
                '<span style="color: white; background-color: {}; padding: 3px 7px; border-radius: 10px;">{:.1f}</span>',
                color,
                obj.overall_score
            )
        return "-"
    get_overall_score.short_description = 'Overall Score'
    
    def get_metadata_score(self, obj):
        if obj.metadata_score is not None:
            color = self._get_score_color(obj.metadata_score)
            return format_html(
                '<span style="color: {};">{:.1f}</span>',
                color,
                obj.metadata_score
            )
        return "-"
    get_metadata_score.short_description = 'Metadata'
    
    def get_content_score(self, obj):
        if obj.content_score is not None:
            color = self._get_score_color(obj.content_score)
            return format_html(
                '<span style="color: {};">{:.1f}</span>',
                color,
                obj.content_score
            )
        return "-"
    get_content_score.short_description = 'Content'
    
    def get_technical_score(self, obj):
        if obj.technical_score is not None:
            color = self._get_score_color(obj.technical_score)
            return format_html(
                '<span style="color: {};">{:.1f}</span>',
                color,
                obj.technical_score
            )
        return "-"
    get_technical_score.short_description = 'Technical'
    
    def _get_score_color(self, score):
        if score >= 90:
            return '#4CAF50'  # Green
        elif score >= 70:
            return '#FFC107'  # Yellow
        elif score >= 50:
            return '#FF9800'  # Orange
        else:
            return '#F44336'  # Red

@admin.register(SEOContentAnalysis)
class SEOContentAnalysisAdmin(admin.ModelAdmin):
    list_display = ('id', 'get_url', 'readability_score', 'sentiment_score', 'ai_readiness_score', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('metadata__url',)
    readonly_fields = ('created_at',)
    
    def get_url(self, obj):
        return obj.metadata.url if obj.metadata else "-"
    get_url.short_description = 'URL'

@admin.register(SEOLink)
class SEOLinkAdmin(admin.ModelAdmin):
    list_display = ('url', 'text', 'type', 'follow', 'status_code', 'created_at')
    list_filter = ('type', 'follow', 'status_code', 'created_at')
    search_fields = ('url', 'text', 'metadata__url')
    readonly_fields = ('created_at',)

@admin.register(SEOImage)
class SEOImageAdmin(admin.ModelAdmin):
    list_display = ('url', 'alt_text', 'lazy_loaded', 'created_at')
    list_filter = ('lazy_loaded', 'created_at')
    search_fields = ('url', 'alt_text', 'metadata__url')
    readonly_fields = ('created_at',)
