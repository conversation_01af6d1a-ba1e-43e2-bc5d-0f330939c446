# seo_insights/serializers.py
from rest_framework import serializers
from .models import (
    SEOProject, SEOCrawl, SEOMetadata, SEOContentAnalysis,
    SEOPerformance, SEOIssue, SEOReport, SEOLink, SEOImage
)

class SEOProjectSerializer(serializers.ModelSerializer):
    class Meta:
        model = SEOProject
        fields = [
            'id', 'name', 'description', 'site_url', 
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class SEOCrawlSerializer(serializers.ModelSerializer):
    class Meta:
        model = SEOCrawl
        fields = [
            'id', 'project', 'status', 'pages_crawled',
            'start_time', 'end_time', 'error_message', 'created_at'
        ]
        read_only_fields = ['pages_crawled', 'start_time', 'end_time', 'error_message', 'created_at']

class SEOMetadataSerializer(serializers.ModelSerializer):
    class Meta:
        model = SEOMetadata
        fields = [
            'id', 'crawl', 'url', 'title', 'meta_description',
            'h1', 'h2', 'canonical_url', 'robots_directives',
            'schema_markup', 'word_count', 'status_code', 'content_type',
            'created_at'
        ]
        read_only_fields = ['created_at']

class SEOContentAnalysisSerializer(serializers.ModelSerializer):
    class Meta:
        model = SEOContentAnalysis
        fields = [
            'id', 'metadata', 'keyword_density', 'readability_score',
            'sentiment_score', 'topical_relevance', 'content_gaps',
            'ai_readiness_score', 'created_at'
        ]
        read_only_fields = ['created_at']

class SEOIssueSerializer(serializers.ModelSerializer):
    class Meta:
        model = SEOIssue
        fields = [
            'id', 'crawl', 'url', 'title', 'description',
            'severity', 'category', 'recommendation', 'created_at'
        ]
        read_only_fields = ['created_at']

class SEOReportSerializer(serializers.ModelSerializer):
    class Meta:
        model = SEOReport
        fields = [
            'id', 'crawl', 'overview', 'metadata_score',
            'content_score', 'technical_score', 'mobile_score',
            'overall_score', 'ai_seo_score', 'improvement_suggestions',
            'generated_at'
        ]
        read_only_fields = ['generated_at']

class SEOLinkSerializer(serializers.ModelSerializer):
    class Meta:
        model = SEOLink
        fields = [
            'id', 'metadata', 'url', 'text', 'type',
            'follow', 'status_code', 'created_at'
        ]
        read_only_fields = ['created_at']

class SEOImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = SEOImage
        fields = [
            'id', 'metadata', 'url', 'alt_text', 'title',
            'filename', 'filesize', 'width', 'height',
            'lazy_loaded', 'created_at'
        ]
        read_only_fields = ['created_at']
