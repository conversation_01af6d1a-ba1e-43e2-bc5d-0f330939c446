aiofiles==24.1.0
aiohappyeyeballs==2.4.6
aiohttp==3.11.13
aiosignal==1.3.2
aiosqlite==0.21.0
amqp==5.2.0
annotated-types==0.7.0
anyio==4.6.2.post1
asgiref==3.8.1
astroid==3.3.5
async-timeout==5.0.1
attrs==25.1.0
Automat==24.8.1
awscli==1.36.7
awsebcli==3.21.0
beautifulsoup4==4.12.2
billiard==4.2.1
blessed==1.20.0
blis==0.7.11
botocore==1.35.66
boto3==1.35.9
cachetools==5.5.2
catalogue==2.0.10
celery==5.4.0
cement==2.10.14
certifi==2024.8.30
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.0
click==8.1.7
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
cloudpathlib==0.16.0
colorama==0.4.6
confection==0.1.5
constantly==23.10.4
coverage==7.6.12
cron-descriptor==1.4.5
cryptography==42.0.8
cssselect==1.2.0
cymem==2.0.11
defusedxml==0.7.1
dill==0.3.9
distro==1.9.0
dj-database-url==2.3.0
Django==4.2.16
django-celery-beat==2.7.0
django-celery-results==2.5.1
django-cors-headers==4.6.0
django-redis==5.4.0
django-timezone-field==7.0
djangorestframework==3.15.2
djangorestframework-simplejwt==5.3.1
docutils==0.16
en-core-web-sm @ https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.7.1/en_core_web_sm-3.7.1-py3-none-any.whl#sha256=86cc141f63942d4b2c5fcee06630fd6f904788d2f0ab005cce45aadb8fb73889
exceptiongroup==1.2.2
fake-http-header==0.3.5
fake-useragent==2.0.3
feedfinder2==0.0.4
feedparser==6.0.11
filelock==3.17.0
frozenlist==1.5.0
fsspec==2025.2.0
google-api-core==2.24.1
google-api-python-client==2.120.0
google-auth==2.28.1
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.2.0
googleapis-common-protos==1.68.0
greenlet==3.1.1
gunicorn==23.0.0
h11==0.14.0
html5lib==1.1
httpcore==1.0.6
httplib2==0.22.0
httpx==0.27.2
huggingface-hub==0.29.1
hyperlink==21.0.0
idna==3.10
importlib_metadata==8.6.1
incremental==24.7.2
iniconfig==2.0.0
isort==5.13.2
itemadapter==0.11.0
itemloaders==1.3.2
jieba3k==0.35.1
Jinja2==3.1.5
jiter==0.7.0
jmespath==1.0.1
joblib==1.4.2
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
kombu==5.4.2
langcodes==3.5.0
language_data==1.3.0
litellm==1.53.3
lxml==5.3.1
lxml_html_clean==0.4.1
marisa-trie==1.2.1
markdown-it-py==3.0.0
MarkupSafe==3.0.2
mccabe==0.7.0
mdurl==0.1.2
multidict==6.1.0
murmurhash==1.0.12
newspaper3k==0.2.8
nltk==3.8.1
numpy==1.26.4
oauthlib==3.2.2
openai==1.54.4
outcome==1.3.0.post0
packaging==24.2
pandas==2.2.3
parsel==1.10.0
pathspec==0.10.1
pillow==10.4.0
platformdirs==4.3.6
playwright==1.50.0
pluggy==1.5.0
preshed==3.0.9
prompt_toolkit==3.0.48
propcache==0.3.0
Protego==0.4.0
proto-plus==1.26.0
protobuf==5.29.3
psutil==7.0.0
psycopg2-binary==2.9.10
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
pydantic==2.10.6
pydantic_core==2.27.2
PyDispatcher==2.0.7
pyee==12.1.1
Pygments==2.19.1
PyJWT==2.9.0
pylint==3.3.1
pylint-django==2.6.1
pylint-plugin-utils==0.8.2
pyOpenSSL==25.0.0
pyparsing==3.2.1
PySocks==1.7.1
pytest==8.0.0
pytest-cov==4.1.0
pytest-django==4.8.0
python-crontab==3.2.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-whois==0.9.5
pytz==2024.2
PyYAML==6.0.2
queuelib==1.7.0
rank-bm25==0.2.2
readability-lxml==0.8.1
redis==5.2.0
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-file==2.1.0
requests-oauthlib==2.0.0
rich==13.9.4
rpds-py==0.23.1
rsa==4.7.2
boto3==1.35.9
s3transfer==0.10.4
scikit-learn==1.5.2
scipy==1.13.1
Scrapy==2.11.1
selenium==4.18.1
semantic-version==2.10.0
service-identity==24.2.0
sgmllib3k==1.0.0
shellingham==1.5.4
six==1.16.0
smart-open==6.4.0
sniffio==1.3.1
snowballstemmer==2.2.0
sortedcontainers==2.4.0
soupsieve==2.6
spacy==3.7.4
stripe==11.2.0
spacy-legacy==3.0.12
spacy-loggers==1.0.5
sqlparse==0.5.1
srsly==2.5.1
termcolor==2.5.0
textblob==0.17.1
tf-playwright-stealth==1.1.2
thinc==8.2.4
threadpoolctl==3.5.0
tiktoken==0.8.0
tinysegmenter==0.3
tldextract==5.1.3
tokenizers==0.21.0
tomli==2.1.0
tomlkit==0.13.2
tqdm==4.67.0
trio==0.29.0
trio-websocket==0.12.1
Twisted==24.11.0
typer==0.9.4
typing_extensions==4.12.2
tzdata==2024.2
uritemplate==4.1.1
urllib3==1.26.20
validators==0.22.0
vine==5.1.0
w3lib==2.3.1
wasabi==1.1.3
wcwidth==0.2.13
weasel==0.3.4
webdriver-manager==4.0.1
webencodings==0.5.1
websocket-client==1.8.0
whitenoise==6.8.2
wrapt==1.17.2
wsproto==1.2.0
xmltodict==0.13.0
xxhash==3.5.0
yamllint==1.35.1
yarl==1.18.3
zipp==3.21.0
zope.interface==7.2
