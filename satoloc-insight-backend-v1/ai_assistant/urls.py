# ai_assistant/urls.py

from django.urls import path
from .views import AIAssistantView, ConversationHistoryView
from .seo_views import SEOAIInsightsView, SEOAIHealthCheckView, SEOAIAnalysisHistoryView

urlpatterns = [
    # General AI Chat
    path("chat/", AIAssistantView.as_view(), name="ai-chat"),
    path("conversations/", ConversationHistoryView.as_view(), name="conversation-list"),
    path(
        "conversations/<int:conversation_id>/",
        ConversationHistoryView.as_view(),
        name="conversation-detail",
    ),
    # SEO AI Insights
    path("seo/insights/", SEOAIInsightsView.as_view(), name="seo-ai-insights"),
    path("seo/health/", SEOAIHealthCheckView.as_view(), name="seo-ai-health"),
    path("seo/history/", SEOAIAnalysisHistoryView.as_view(), name="seo-ai-history"),
]
