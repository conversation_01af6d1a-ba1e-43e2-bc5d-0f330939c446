import os
import json
from typing import Dict, List, Any, Optional
from openai import OpenAI
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured
from advance_seo.models import (
    Website,
    SEOMetrics,
    TechnicalAnalysis,
    CoreWebVitalsAnalysis,
    PerformanceOpportunity,
    PerformanceDiagnostic,
)


class SEOAIService:
    """
    Specialized AI service for SEO insights and recommendations
    """

    def __init__(self):
        api_key = settings.OPENAI_API_KEY
        if not api_key:
            raise ImproperlyConfigured(
                "OPENAI_API_KEY is not set. Please set it in your environment variables."
            )

        self.client = OpenAI(api_key=api_key)

    def get_seo_insights(
        self, component_type: str, website_url: str, user_id: int
    ) -> Dict[str, Any]:
        """
        Get AI-powered SEO insights for a specific component and website
        """
        try:
            # Get website data from database
            website_data = self._get_website_data(website_url, user_id)

            if not website_data:
                return {
                    "success": False,
                    "error": "No data found for this website. Please run an analysis first.",
                }

            # Generate insights based on component type
            insights = self._generate_insights(component_type, website_data)

            return {
                "success": True,
                "insights": insights,
                "website_url": website_url,
                "component_type": component_type,
            }

        except Exception as e:
            print(f"Error generating SEO insights: {str(e)}")
            return {
                "success": False,
                "error": "Failed to generate insights. Please try again later.",
            }

    def _get_website_data(
        self, website_url: str, user_id: int
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieve comprehensive website data from database
        """
        try:
            # Get website instance
            website = Website.objects.filter(
                url__icontains=website_url.replace("http://", "").replace(
                    "https://", ""
                )
            ).first()

            if not website:
                return None

            data = {
                "url": website.url,
                "industry": website.industry,
                "language": website.language,
                "created_at": (
                    website.created_at.isoformat() if website.created_at else None
                ),
            }

            # Get SEO metrics
            try:
                seo_metrics = website.seo_metrics
                data["seo_metrics"] = {
                    "domain_rating_score": seo_metrics.domain_rating_score,
                    "organic_traffic_score": seo_metrics.organic_traffic_score,
                    "search_rankings_score": seo_metrics.search_rankings_score,
                    "search_terms_score": seo_metrics.search_terms_score,
                    "site_links_score": seo_metrics.site_links_score,
                    "content_score": seo_metrics.content_score,
                    "backlinks_count": seo_metrics.backlinks_count,
                }
            except SEOMetrics.DoesNotExist:
                data["seo_metrics"] = None

            # Get technical analysis
            try:
                tech_analysis = website.technical_analysis
                data["technical_analysis"] = {
                    "page_speed": tech_analysis.page_speed,
                    "mobile_optimization": tech_analysis.mobile_optimization,
                    "core_web_vitals": tech_analysis.core_web_vitals,
                    "local_schema": tech_analysis.local_schema,
                    "https_security": tech_analysis.https_security,
                    "canonical_url": tech_analysis.canonical_url,
                    "hreflang_implementation": tech_analysis.hreflang_implementation,
                    "schema_markup": tech_analysis.schema_markup,
                }
            except TechnicalAnalysis.DoesNotExist:
                data["technical_analysis"] = None

            # Get Core Web Vitals (latest analysis)
            cwv_analysis = (
                CoreWebVitalsAnalysis.objects.filter(website=website)
                .order_by("-created_at")
                .first()
            )

            if cwv_analysis:
                data["core_web_vitals"] = {
                    "strategy": cwv_analysis.strategy,
                    "performance_score": cwv_analysis.performance_score,
                    "accessibility_score": cwv_analysis.accessibility_score,
                    "best_practices_score": cwv_analysis.best_practices_score,
                    "seo_score": cwv_analysis.seo_score,
                    "lcp_value": cwv_analysis.lcp_value,
                    "lcp_field_category": cwv_analysis.lcp_field_category,
                    "fid_value": cwv_analysis.fid_value,
                    "fid_field_category": cwv_analysis.fid_field_category,
                    "cls_value": cwv_analysis.cls_value,
                    "cls_field_category": cwv_analysis.cls_field_category,
                    "fcp_value": cwv_analysis.fcp_value,
                    "fcp_field_category": cwv_analysis.fcp_field_category,
                    "opportunities_count": cwv_analysis.opportunities.count(),
                    "diagnostics_count": cwv_analysis.diagnostics.count(),
                }

                # Get top opportunities and diagnostics
                opportunities = list(
                    cwv_analysis.opportunities.values(
                        "title",
                        "description",
                        "score",
                        "display_value",
                        "overall_savings_ms",
                        "overall_savings_bytes",
                    )[:5]
                )

                diagnostics = list(
                    cwv_analysis.diagnostics.values(
                        "title", "description", "score", "display_value"
                    )[:5]
                )

                data["core_web_vitals"]["opportunities"] = opportunities
                data["core_web_vitals"]["diagnostics"] = diagnostics
            else:
                data["core_web_vitals"] = None

            # Get competitors data
            competitors = list(
                website.competitors.values("name", "target_url", "description", "rank")[
                    :3
                ]
            )
            data["competitors"] = competitors

            # Get keywords
            keywords = list(website.keywords.values("keyword", "is_target")[:10])
            data["keywords"] = keywords

            return data

        except Exception as e:
            print(f"Error retrieving website data: {str(e)}")
            return None

    def _generate_insights(
        self, component_type: str, website_data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Use OpenAI to generate intelligent insights based on component type and data
        """

        # Create specialized prompts for different components
        system_prompts = {
            "performance-chart": self._get_performance_chart_prompt(),
            "core-web-vitals": self._get_core_web_vitals_prompt(),
            "gsc-data": self._get_gsc_data_prompt(),
            "competitor-analysis": self._get_competitor_analysis_prompt(),
            "technical-analysis": self._get_technical_analysis_prompt(),
        }

        system_prompt = system_prompts.get(
            component_type, self._get_general_seo_prompt()
        )

        # Prepare data summary for AI
        data_summary = self._prepare_data_summary(component_type, website_data)

        user_message = f"""
        Please analyze the following SEO data for {website_data['url']} and provide actionable insights:

        {data_summary}

        Please provide insights in JSON format with the following structure:
        {{
            "insights": [
                {{
                    "type": "opportunity|warning|info|success",
                    "title": "Clear, specific title",
                    "description": "Detailed explanation with specific recommendations",
                    "priority": "high|medium|low",
                    "actionable": true|false,
                    "metrics": ["relevant", "metric", "names"]
                }}
            ]
        }}
        """

        try:
            response = self.client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_message},
                ],
                max_tokens=2000,
                temperature=0.3,  # Lower temperature for more consistent analysis
                response_format={"type": "json_object"},
            )

            # Parse the JSON response
            insights_json = response.choices[0].message.content
            insights_data = json.loads(insights_json)

            # Ensure we return a list
            if isinstance(insights_data, dict) and "insights" in insights_data:
                return insights_data["insights"]
            elif isinstance(insights_data, list):
                return insights_data
            else:
                return [insights_data] if insights_data else []

        except json.JSONDecodeError:
            # Fallback if JSON parsing fails
            return self._get_fallback_insights(component_type, website_data)
        except Exception as e:
            print(f"Error calling OpenAI API: {str(e)}")
            return self._get_fallback_insights(component_type, website_data)

    def _prepare_data_summary(
        self, component_type: str, website_data: Dict[str, Any]
    ) -> str:
        """
        Prepare a concise data summary for AI analysis
        """
        summary_parts = []

        # Basic website info
        summary_parts.append(f"Website: {website_data['url']}")
        summary_parts.append(f"Industry: {website_data.get('industry', 'Unknown')}")
        summary_parts.append(f"Language: {website_data.get('language', 'Unknown')}")

        # Component-specific data
        if component_type == "core-web-vitals" and website_data.get("core_web_vitals"):
            cwv = website_data["core_web_vitals"]
            summary_parts.append("\n=== Core Web Vitals ===")
            summary_parts.append(f"Performance Score: {cwv['performance_score']}")
            summary_parts.append(
                f"LCP: {cwv['lcp_value']}s ({cwv['lcp_field_category']})"
            )
            summary_parts.append(
                f"FID: {cwv['fid_value']}ms ({cwv['fid_field_category']})"
            )
            summary_parts.append(
                f"CLS: {cwv['cls_value']} ({cwv['cls_field_category']})"
            )
            summary_parts.append(
                f"FCP: {cwv['fcp_value']}s ({cwv['fcp_field_category']})"
            )

            if cwv.get("opportunities"):
                summary_parts.append("\nTop Opportunities:")
                for opp in cwv["opportunities"][:3]:
                    summary_parts.append(f"- {opp['title']}: {opp['display_value']}")

        elif component_type == "performance-chart" and website_data.get("seo_metrics"):
            metrics = website_data["seo_metrics"]
            summary_parts.append("\n=== SEO Performance ===")
            summary_parts.append(f"Domain Rating: {metrics['domain_rating_score']}")
            summary_parts.append(f"Organic Traffic: {metrics['organic_traffic_score']}")
            summary_parts.append(f"Search Rankings: {metrics['search_rankings_score']}")
            summary_parts.append(f"Content Score: {metrics['content_score']}")

        # Technical analysis
        if website_data.get("technical_analysis"):
            tech = website_data["technical_analysis"]
            summary_parts.append("\n=== Technical Analysis ===")
            summary_parts.append(f"Page Speed: {tech['page_speed']}")
            summary_parts.append(f"Mobile Optimization: {tech['mobile_optimization']}")
            summary_parts.append(f"HTTPS Security: {tech['https_security']}")
            summary_parts.append(f"Schema Markup: {tech['schema_markup']}")

        # Competitors
        if website_data.get("competitors"):
            summary_parts.append(
                f"\n=== Competitors ({len(website_data['competitors'])}) ==="
            )
            for comp in website_data["competitors"][:3]:
                summary_parts.append(
                    f"- {comp['name']}: {comp['percentage']}% similarity"
                )

        return "\n".join(summary_parts)

    def _get_performance_chart_prompt(self) -> str:
        return """You are an expert SEO analyst specializing in performance metrics analysis. 
        Analyze the provided SEO performance data and provide actionable insights focusing on:
        - Search rankings and position improvements
        - Organic traffic optimization opportunities  
        - Click-through rate enhancement strategies
        - Content performance recommendations
        - Competitive positioning advice
        
        Prioritize insights that can lead to measurable improvements in search visibility and user engagement."""

    def _get_core_web_vitals_prompt(self) -> str:
        return """You are a web performance expert specializing in Core Web Vitals optimization.
        Analyze the provided performance data and provide specific, technical recommendations for:
        - Largest Contentful Paint (LCP) optimization
        - First Input Delay (FID) improvements
        - Cumulative Layout Shift (CLS) stability
        - First Contentful Paint (FCP) enhancement
        - Overall performance score improvements
        
        Focus on actionable technical solutions that developers can implement to improve user experience and SEO rankings."""

    def _get_gsc_data_prompt(self) -> str:
        return """You are a Google Search Console data specialist.
        Analyze the search performance data and provide insights on:
        - Search query optimization opportunities
        - Click-through rate improvement strategies
        - Position ranking enhancement tactics
        - Impression-to-click conversion optimization
        - Seasonal and trend-based recommendations
        
        Focus on data-driven strategies to improve organic search performance."""

    def _get_competitor_analysis_prompt(self) -> str:
        return """You are a competitive SEO analyst.
        Analyze the competitor landscape and provide strategic insights on:
        - Competitive gap analysis
        - Content strategy recommendations
        - Keyword opportunity identification
        - Market positioning strategies
        - Differentiation opportunities
        
        Focus on actionable strategies to outperform competitors in search results."""

    def _get_technical_analysis_prompt(self) -> str:
        return """You are a technical SEO specialist.
        Analyze the technical SEO factors and provide recommendations for:
        - Site speed and performance optimization
        - Mobile-first indexing compliance
        - Schema markup implementation
        - Security and HTTPS configuration
        - Crawlability and indexing improvements
        
        Focus on technical implementations that directly impact search engine rankings."""

    def _get_general_seo_prompt(self) -> str:
        return """You are a comprehensive SEO expert.
        Analyze the provided SEO data and provide well-rounded insights covering:
        - Technical SEO improvements
        - Content optimization opportunities
        - Performance enhancement strategies
        - User experience optimizations
        - Strategic SEO recommendations
        
        Provide actionable, prioritized insights that can improve overall search engine visibility."""

    def _get_fallback_insights(
        self, component_type: str, website_data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Provide fallback insights when AI analysis fails
        """
        fallback_insights = []

        # Core Web Vitals fallbacks
        if component_type == "core-web-vitals" and website_data.get("core_web_vitals"):
            cwv = website_data["core_web_vitals"]

            if cwv["performance_score"] < 50:
                fallback_insights.append(
                    {
                        "type": "warning",
                        "title": "Poor Performance Score Detected",
                        "description": f"Your performance score of {cwv['performance_score']} needs immediate attention. Focus on optimizing images, reducing server response times, and minimizing JavaScript.",
                        "priority": "high",
                        "actionable": True,
                        "metrics": ["performance"],
                    }
                )

            if cwv["lcp_field_category"] == "POOR":
                fallback_insights.append(
                    {
                        "type": "opportunity",
                        "title": "Optimize Largest Contentful Paint",
                        "description": "Your LCP is in the poor range. Consider optimizing your largest content element through image compression and server response improvements.",
                        "priority": "high",
                        "actionable": True,
                        "metrics": ["lcp"],
                    }
                )

        # Performance chart fallbacks
        elif component_type == "performance-chart" and website_data.get("seo_metrics"):
            metrics = website_data["seo_metrics"]

            if metrics["search_rankings_score"] < 50:
                fallback_insights.append(
                    {
                        "type": "opportunity",
                        "title": "Improve Search Rankings",
                        "description": "Your search rankings score suggests room for improvement. Focus on content optimization and technical SEO enhancements.",
                        "priority": "medium",
                        "actionable": True,
                        "metrics": ["rankings"],
                    }
                )

        # Default fallback
        if not fallback_insights:
            fallback_insights.append(
                {
                    "type": "info",
                    "title": "SEO Analysis Available",
                    "description": "Your SEO data has been collected. Run regular analyses to track improvements and identify optimization opportunities.",
                    "priority": "low",
                    "actionable": False,
                    "metrics": ["general"],
                }
            )

        return fallback_insights
