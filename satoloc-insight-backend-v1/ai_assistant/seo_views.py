from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import json
import logging

from .seo_ai_service import SEOAIService

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name="dispatch")
class SEOAIInsightsView(APIView):
    """
    API view for generating SEO AI insights
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Generate AI insights for a specific SEO component

        Expected payload:
        {
            "component_type": "performance-chart|core-web-vitals|gsc-data|competitor-analysis|technical-analysis",
            "website_url": "https://example.com",
            "additional_data": {}  # Optional additional context
        }
        """
        try:
            # Extract request data
            component_type = request.data.get("component_type")
            website_url = request.data.get("website_url")
            additional_data = request.data.get("additional_data", {})

            # Validate required fields
            if not component_type:
                return Response(
                    {"success": False, "error": "component_type is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not website_url:
                return Response(
                    {"success": False, "error": "website_url is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Validate component type
            valid_components = [
                "performance-chart",
                "core-web-vitals",
                "gsc-data",
                "competitor-analysis",
                "technical-analysis",
            ]

            if component_type not in valid_components:
                return Response(
                    {
                        "success": False,
                        "error": f"Invalid component_type. Must be one of: {', '.join(valid_components)}",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Clean website URL
            website_url = website_url.strip().lower()
            if not website_url.startswith(("http://", "https://")):
                website_url = "https://" + website_url

            # Initialize SEO AI service
            seo_ai = SEOAIService()

            # Generate insights
            result = seo_ai.get_seo_insights(
                component_type=component_type,
                website_url=website_url,
                user_id=request.user.id,
            )

            # Add user context to response
            result["user_id"] = request.user.id
            result["timestamp"] = timezone.now().isoformat()

            # Log the request for analytics
            logger.info(
                f"SEO AI insights generated - User: {request.user.id}, "
                f"Component: {component_type}, URL: {website_url}, "
                f"Success: {result['success']}"
            )

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error in SEOAIInsightsView: {str(e)}")
            return Response(
                {
                    "success": False,
                    "error": "An unexpected error occurred while generating insights.",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class SEOAIHealthCheckView(APIView):
    """
    Health check endpoint for SEO AI service
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Check if the SEO AI service is available and properly configured
        """
        try:
            # Test service initialization
            seo_ai = SEOAIService()

            return Response(
                {
                    "success": True,
                    "message": "SEO AI service is healthy and ready",
                    "openai_configured": bool(
                        getattr(settings, "OPENAI_API_KEY", None)
                    ),
                    "timestamp": timezone.now().isoformat(),
                }
            )

        except Exception as e:
            return Response(
                {
                    "success": False,
                    "error": str(e),
                    "timestamp": timezone.now().isoformat(),
                },
                status=status.HTTP_503_SERVICE_UNAVAILABLE,
            )


class SEOAIAnalysisHistoryView(APIView):
    """
    Get analysis history for a specific website
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get SEO analysis history for a website
        """
        website_url = request.query_params.get("website_url")

        if not website_url:
            return Response(
                {"success": False, "error": "website_url parameter is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # Clean website URL
            website_url = website_url.strip().lower()
            if not website_url.startswith(("http://", "https://")):
                website_url = "https://" + website_url

            # Initialize service and get data
            seo_ai = SEOAIService()
            website_data = seo_ai._get_website_data(website_url, request.user.id)

            if not website_data:
                return Response(
                    {
                        "success": False,
                        "error": "No analysis data found for this website",
                    }
                )

            # Return summary of available data
            summary = {
                "success": True,
                "website_url": website_url,
                "data_available": {
                    "seo_metrics": website_data.get("seo_metrics") is not None,
                    "technical_analysis": website_data.get("technical_analysis")
                    is not None,
                    "core_web_vitals": website_data.get("core_web_vitals") is not None,
                    "competitors": len(website_data.get("competitors", [])),
                    "keywords": len(website_data.get("keywords", [])),
                },
                "last_analysis": website_data.get("created_at"),
                "timestamp": timezone.now().isoformat(),
            }

            return Response(summary)

        except Exception as e:
            logger.error(f"Error in SEOAIAnalysisHistoryView: {str(e)}")
            return Response(
                {"success": False, "error": "Failed to retrieve analysis history"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# Import required modules at the top
from django.conf import settings
from django.utils import timezone
