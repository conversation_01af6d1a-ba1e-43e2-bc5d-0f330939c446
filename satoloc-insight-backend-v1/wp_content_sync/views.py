# wp_content_sync/views.py
from rest_framework import viewsets, status, permissions, exceptions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import serializers
from django.utils import timezone
from django.utils.dateparse import parse_datetime
from django.utils.timezone import is_naive
from django.shortcuts import get_object_or_404
from django.core.exceptions import ImproperlyConfigured, ValidationError
from django.conf import settings
from django.db.models import Q
from django.utils.text import slugify
import logging
from django.contrib.auth import get_user_model
import json
import requests
from datetime import datetime
import html
import re
from typing import Dict, Any, List, Tuple, Optional

from .models import SyncedContent, SyncLog, WordPressConnection
from .serializers import (
    SyncedContentSerializer,
    TranslationRequestSerializer,
    TranslationPreviewRequestSerializer,
    SyncLogSerializer,
    WordPressConnectionSerializer,
)

# Ensure you are importing the corrected AITranslationService and clients
from .api_client import WordPressAPIClient, WordPressAPIClientError
from .ai_translation import AITranslationService

User = get_user_model()
logger = logging.getLogger(__name__)


class MarkdownTranslationRequestSerializer(serializers.Serializer):
    target_language_code = serializers.CharField(max_length=10)
    markdown_content = serializers.CharField(required=True)

    def validate_target_language_code(self, value):
        if not value:
            raise serializers.ValidationError("Target language code cannot be empty.")
        return value.lower()


class WordPressConnectionViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing WordPress connections.
    """

    serializer_class = WordPressConnectionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """
        Return only connections owned by the current user.
        """
        if self.request.user.is_authenticated:
            return WordPressConnection.objects.filter(user=self.request.user)
        return WordPressConnection.objects.none()

    def perform_create(self, serializer):
        """
        Set the user to the current authenticated user when creating.
        """
        logger.info(
            "Creating WordPress connection for user: %s (ID: %s)",
            self.request.user.username,
            self.request.user.id,
        )
        serializer.save(user=self.request.user)

    def create(self, request, *args, **kwargs):
        """
        Override create to provide more detailed error responses and handle duplicate connections.
        """
        try:
            logger.info("WordPress connection create request: %s", request.data)

            # Ensure user is authenticated
            if not request.user.is_authenticated:
                return Response(
                    {"error": "Authentication required"},
                    status=status.HTTP_401_UNAUTHORIZED,
                )

            # Check if this site URL already exists for the *current* user
            site_url = request.data.get("site_url", "")
            site_url = site_url.rstrip("/")  # Normalize URL

            # Use filter instead of first() to check specifically for the current user
            existing_connection_for_user = WordPressConnection.objects.filter(
                site_url=site_url, user=request.user  # Filter by the current user
            ).first()

            if existing_connection_for_user:
                # If this user already has this connection, return it
                serializer = self.get_serializer(existing_connection_for_user)
                response_data = serializer.data
                response_data["already_exists"] = True
                response_data["message"] = f"You are already connected to {site_url}"
                logger.info(
                    "User %s already connected to %s, returning existing connection.",
                    request.user.username,
                    site_url,
                )
                return Response(
                    response_data,
                    status=status.HTTP_200_OK,  # OK status since it exists for this user
                )

            # ---- The check for *other* users is now removed ----
            # If we reach here, it means the current user doesn't have a connection
            # to this site_url, so we proceed to create one for them.

            # Create serializer with request data
            serializer = self.get_serializer(data=request.data)

            # Validate with detailed errors
            if not serializer.is_valid():
                logger.error("Validation errors: %s", serializer.errors)
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            # Save with user assigned
            # perform_create already sets the user correctly
            self.perform_create(serializer)
            headers = self.get_success_headers(serializer.data)

            logger.info(
                "WordPress connection created successfully for user %s: %s",
                request.user.username,
                serializer.data,
            )
            return Response(
                serializer.data, status=status.HTTP_201_CREATED, headers=headers
            )
        except Exception as e:
            logger.exception("Error creating WordPress connection: %s", str(e))
            # Check for potential database integrity errors (like unique_together violation)
            # although the initial check should prevent this for the same user.
            if "unique constraint" in str(e).lower():
                return Response(
                    {
                        "error": "A unique constraint violation occurred. This might happen if you try to add the same connection very quickly.",
                        "detail": str(e),
                    },
                    status=status.HTTP_409_CONFLICT,
                )
            return Response(
                {
                    "error": "An unexpected error occurred while creating the connection.",
                    "detail": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["post"])
    def verify_connection(self, request, pk=None):
        """
        Verify that the connection to WordPress is working.
        Tests the connection by attempting to fetch content.
        """
        connection = self.get_object()
        try:
            # Make a simple request to the WordPress site
            api_url = f"{connection.site_url.rstrip('/')}/wp-json/satoloc-insight-connector/v1/content"
            headers = {"X-Satoloc-Insight-Key": connection.api_key}

            logger.info("Verifying connection to WordPress: %s", api_url)
            response = requests.get(api_url, headers=headers, timeout=10)

            if response.status_code == 200:
                logger.info("Connection verified successfully: %s", connection.site_url)
                return Response(
                    {"status": "success", "message": "Connection verified successfully"}
                )
            else:
                logger.error(
                    "Connection verification failed: %s - %s",
                    response.status_code,
                    response.text,
                )
                return Response(
                    {
                        "status": "error",
                        "message": f"Connection verification failed: {response.status_code}",
                        "details": response.text,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        except Exception as e:
            logger.exception("Connection verification error: %s", str(e))
            return Response(
                {
                    "status": "error",
                    "message": f"Connection verification failed: {str(e)}",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def deactivate(self, request, pk=None):
        """
        Deactivate a connection instead of deleting it.
        This allows reconnecting later while avoiding duplicate connection errors.
        """
        connection = self.get_object()
        connection.is_active = False
        connection.save(update_fields=["is_active"])

        logger.info("Deactivated connection to: %s", connection.site_url)
        return Response(
            {
                "status": "success",
                "message": "Connection deactivated",
                "site_url": connection.site_url,
            }
        )

    @action(detail=True, methods=["post"])
    def update_last_sync(self, request, pk=None):
        """
        Update the last_sync timestamp for a connection.
        """
        connection = self.get_object()
        connection.last_sync = timezone.now()
        connection.save(update_fields=["last_sync"])

        logger.info("Updated last_sync for connection: %s", connection.site_url)
        return Response(
            {
                "status": "success",
                "message": "Last sync timestamp updated",
                "last_sync": connection.last_sync,
            }
        )

    @action(detail=True, methods=["get"], url_path="wp-categories")
    def get_wp_categories(self, request, pk=None):
        """
        Fetches the list of categories directly from the connected WordPress site.
        """
        connection = (
            self.get_object()
        )  # Gets the connection by pk, ensures user owns it
        logger.info(
            "Fetching categories for connection ID %s (%s)", pk, connection.site_url
        )
        try:
            client = WordPressAPIClient(
                base_url=connection.site_url, api_key=connection.api_key
            )
            categories = client.get_all_categories()
            # The API client returns a list of dicts if successful
            if categories is None:  # Handle cases where API might return null/empty
                categories = []
            logger.info(
                "Successfully fetched %s categories from %s",
                len(categories),
                connection.site_url,
            )
            return Response(categories, status=status.HTTP_200_OK)
        except WordPressAPIClientError as e:
            logger.error(
                "WP API Error fetching categories for %s: %s",
                connection.site_url,
                e,
                exc_info=True,
            )
            return Response(
                {"error": f"Failed fetch categories from WP: {e}"},
                status=e.status_code or status.HTTP_502_BAD_GATEWAY,
            )
        except Exception as e:
            logger.error(
                "Unexpected error fetching categories for %s: %s",
                connection.site_url,
                e,
                exc_info=True,
            )
            return Response(
                {"error": f"Unexpected error: {e}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class SyncedContentViewSet(viewsets.ModelViewSet):
    serializer_class = SyncedContentSerializer
    permission_classes = [permissions.AllowAny]

    def get_queryset(self):
        queryset = SyncedContent.objects.all()
        site_url = self.request.query_params.get("wp_site_url", None)
        if site_url:
            normalized_url = site_url.rstrip("/")
            logger.debug("Filtering content list for site: %s", normalized_url)
            queryset = queryset.filter(wp_site_url=normalized_url)
        return queryset.order_by("wp_site_url", "-date_modified_gmt", "-updated_at")

    def _get_wp_credentials_from_request(self, request):
        baseUrl = request.data.get("baseUrl")
        apiKey = request.data.get("apiKey")
        if not baseUrl or not apiKey:
            raise exceptions.ParseError(
                "Request body must include 'baseUrl' and 'apiKey'."
            )
        return baseUrl.rstrip("/"), apiKey

    def _parse_datetime_with_timezone(self, datetime_str):
        if not datetime_str:
            return None
        dt = parse_datetime(datetime_str)
        if not dt:
            return None
        return timezone.make_aware(dt, timezone.utc) if is_naive(dt) else dt

    @action(detail=False, methods=["post"], url_path="fetch-all-from-wp")
    def fetch_all_from_wp(self, request):
        try:
            baseUrl, apiKey = self._get_wp_credentials_from_request(request)
            client = WordPressAPIClient(base_url=baseUrl, api_key=apiKey)
        except (exceptions.ParseError, ValueError, ImproperlyConfigured) as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        sync_start_time = timezone.now()
        created_count, updated_count, skipped_count = 0, 0, 0
        processed_wp_ids = set()

        try:
            wp_content_list = client.get_all_content_items()
            total_fetched = len(wp_content_list)
            logger.info(
                "Fetched total %s items from WP site: %s", total_fetched, baseUrl
            )

            for item_data in wp_content_list:
                wp_id = item_data.get("id")
                if not wp_id or not isinstance(wp_id, int) or wp_id <= 0:
                    skipped_count += 1
                    continue
                processed_wp_ids.add(wp_id)

                defaults = {
                    "title": item_data.get("title", ""),
                    "content": item_data.get("content", ""),
                    "post_type": item_data.get("post_type", "post"),
                    "status": item_data.get("status", "draft"),
                    "date_created_gmt": self._parse_datetime_with_timezone(
                        item_data.get("date_created_gmt")
                    ),
                    "date_modified_gmt": self._parse_datetime_with_timezone(
                        item_data.get("date_modified_gmt")
                    ),
                    "wp_link": item_data.get("link"),
                    "featured_image_url": item_data.get("featured_image_url"),
                    "last_synced_at": sync_start_time,
                    "language_code": item_data.get("language_code"),
                    "language_name": item_data.get("language_name"),
                    # Don't add categories here yet, process below
                }

                # Get categories from the API response
                # WordPress connector returns null when a post has no categories
                categories = item_data.get("categories")

                # Convert null to empty list for consistent database storage
                if categories is None:
                    logger.debug(
                        "WP post ID %s has NULL categories, converting to empty list",
                        wp_id,
                    )
                    categories = []

                # --- REMOVED THE BLOCK THAT ADDED DEFAULT "Uncategorized" ---
                # # If categories is now empty, add "Uncategorized"
                # if not categories:
                #     logger.debug("WP post ID %s has no categories, adding Uncategorized as default", wp_id)
                #     categories = [{'id': 1, 'name': 'Uncategorized', 'slug': 'uncategorized'}]

                # Assign the processed categories (original list or empty list)
                defaults["categories"] = categories

                # Validate post_type and status
                if defaults["post_type"] not in [
                    c[0] for c in SyncedContent.POST_TYPE_CHOICES
                ] or defaults["status"] not in [
                    c[0] for c in SyncedContent.STATUS_CHOICES
                ]:
                    logger.warning(
                        "Skipping WP ID %s due to invalid type/status.", wp_id
                    )
                    skipped_count += 1
                    continue

                # Update or create the local record
                try:
                    obj, created = SyncedContent.objects.update_or_create(
                        wp_site_url=baseUrl, wp_id=wp_id, defaults=defaults
                    )
                    if created:
                        created_count += 1
                    else:
                        updated_count += 1
                except Exception as db_error:
                    logger.error(
                        "DB error processing WP ID %s: %s",
                        wp_id,
                        db_error,
                        exc_info=True,
                    )
                    skipped_count += 1

            # Find and handle existing database records for posts that no longer exist in WordPress
            removed_count = 0
            keep_wp_generated_translations = request.data.get(
                "keep_wp_generated_translations", True
            )
            cleanup_missing = request.data.get(
                "cleanup_missing", True
            )  # Allow optional disabling

            if cleanup_missing:
                # Only process positive wp_ids (actual WordPress posts)
                existing_records = SyncedContent.objects.filter(
                    wp_site_url=baseUrl, wp_id__gt=0
                ).exclude(wp_id__in=processed_wp_ids)

                for record in existing_records:
                    # Skip locally created content (wp_id <= 0)
                    if record.wp_id <= 0:
                        continue

                    # Option to keep AI-generated translations even if original is gone
                    if keep_wp_generated_translations and record.wp_id < 0:
                        logger.info(
                            "Keeping locally generated translation (ID: %s)", record.id
                        )
                        continue

                    logger.warning(
                        "Post with WP ID %s no longer exists in WordPress - removing from database",
                        record.id,
                    )
                    record.delete()
                    removed_count += 1

            logger.info(
                "Sync finished for %s. Created: %d, Updated: %d, Removed: %d, Skipped: %d.",
                baseUrl,
                created_count,
                updated_count,
                removed_count,
                skipped_count,
            )

            # Log the operation
            details = {
                "total_fetched_from_wp": total_fetched,
                "created_locally": created_count,
                "updated_locally": updated_count,
                "removed_locally": removed_count,
                "skipped": skipped_count,
                "cleanup_missing": cleanup_missing,
                "keep_wp_generated_translations": keep_wp_generated_translations,
            }

            # Get user from request if authenticated
            user = request.user if request.user.is_authenticated else None

            # Create log entry
            SyncLog.log_operation(
                wp_site_url=baseUrl,
                operation="fetch_all",
                status="success",
                details=details,
                user=user,
            )

            return Response(
                {
                    "message": f"Sync from {baseUrl} completed.",
                    "target_site": baseUrl,
                    "total_fetched_from_wp": total_fetched,
                    "created_locally": created_count,
                    "updated_locally": updated_count,
                    "removed_locally": removed_count,  # Add count of removed items
                    "skipped": skipped_count,
                },
                status=status.HTTP_200_OK,
            )

        except WordPressAPIClientError as e:
            logger.error(
                "WP API Error during fetch from %s: %s", baseUrl, e, exc_info=True
            )

            # Log the failure
            user = request.user if request.user.is_authenticated else None
            SyncLog.log_operation(
                wp_site_url=baseUrl,
                operation="fetch_all",
                status="failed",
                details={
                    "error": str(e),
                    "status_code": getattr(e, "status_code", None),
                },
                user=user,
            )

            return Response(
                {"error": f"Failed fetch from WP: {e}"},
                status=e.status_code or status.HTTP_502_BAD_GATEWAY,
            )
        except Exception as e:
            logger.error(
                "Unexpected error during fetch-all for %s: %s",
                baseUrl,
                e,
                exc_info=True,
            )

            # Log the failure
            user = request.user if request.user.is_authenticated else None
            SyncLog.log_operation(
                wp_site_url=baseUrl,
                operation="fetch_all",
                status="failed",
                details={"error": str(e)},
                user=user,
            )

            return Response(
                {"error": f"Unexpected error: {e}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["post"], url_path="push-to-wp")
    def push_to_wp(self, request, pk=None):
        local_instance = self.get_object()
        apiKey = request.data.get("apiKey")
        if not apiKey:
            raise exceptions.ParseError("'apiKey' required.")
        try:
            target_baseUrl = local_instance.wp_site_url
            if not target_baseUrl:
                raise exceptions.ValidationError("Missing source URL.")
            client = WordPressAPIClient(base_url=target_baseUrl, api_key=apiKey)
        except (ValueError, ImproperlyConfigured, exceptions.ValidationError) as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        # Prepare base data
        wp_data = {
            "title": request.data.get(
                "title", local_instance.title
            ),  # Use request data if available
            "content": request.data.get("content", local_instance.content),
            "status": request.data.get("status", local_instance.status),
        }

        # --- NEW: Check for categories in the request ---
        if "category_ids" in request.data:
            category_ids = request.data["category_ids"]
            if isinstance(category_ids, list):  # Basic validation
                # Ensure all are integers (could add more validation)
                valid_ids = [
                    int(id)
                    for id in category_ids
                    if isinstance(id, (int, str)) and str(id).isdigit()
                ]
                if valid_ids:
                    wp_data["categories"] = valid_ids
                    logger.info(
                        "Pushing WP ID %s with provided category IDs: %s",
                        local_instance.wp_id,
                        valid_ids,
                    )
                else:
                    wp_data["categories"] = [1]  # Assign default if empty list provided
                    logger.info(
                        "Pushing WP ID %s with default category ID 1 (empty list provided).",
                        local_instance.wp_id,
                    )
            else:
                logger.warning(
                    "Received non-list 'category_ids' in push request, ignoring."
                )
        # --- If 'category_ids' NOT in request, the key won't be sent, WP might preserve existing ---
        else:
            # Send category IDs if the local instance has them and they are valid
            if (
                isinstance(local_instance.categories, list)
                and local_instance.categories
            ):
                category_ids = [
                    cat["id"]
                    for cat in local_instance.categories
                    if isinstance(cat, dict) and "id" in cat and cat.get("id") != 1
                ]  # Exclude 'Uncategorized' ID 1 if needed? Or let WP handle it?
                if (
                    category_ids
                ):  # Only send if there are valid IDs other than potentially 'Uncategorized'
                    wp_data["categories"] = category_ids
                    logger.info("Pushing with category IDs: %s", category_ids)
                else:
                    # Decide how to handle if only 'Uncategorized' is present or the list is empty after filtering
                    # Option 1: Send empty array to potentially remove categories (depends on WP API behavior)
                    # wp_data['categories'] = []
                    # Option 2: Send the default category ID (1)
                    wp_data["categories"] = [1]  # Assign to default 'Uncategorized'
                    logger.info("Pushing with default category ID 1.")
                    # Option 3: Don't send the 'categories' key at all if empty/default
                    # (This might leave existing categories unchanged on WP)

        try:
            logger.info(
                "Pushing local ID %s (WP ID: %s) to %s",
                pk,
                local_instance.wp_id,
                target_baseUrl,
            )
            wp_updated_data = client.update_content_item(local_instance.wp_id, wp_data)

            # --- Update local instance based on response ---
            if wp_updated_data:
                # Update title, content, status from request data first, then WP response if keys exist
                local_instance.title = request.data.get(
                    "title", wp_updated_data.get("title", local_instance.title)
                )
                local_instance.content = request.data.get(
                    "content", wp_updated_data.get("content", local_instance.content)
                )
                local_instance.status = request.data.get(
                    "status", wp_updated_data.get("status", local_instance.status)
                )

                # Update other fields only from WP response
                local_instance.date_modified_gmt = (
                    self._parse_datetime_with_timezone(
                        wp_updated_data.get("date_modified_gmt")
                    )
                    or timezone.now()
                )
                local_instance.wp_link = wp_updated_data.get(
                    "link", local_instance.wp_link
                )
                local_instance.featured_image_url = wp_updated_data.get(
                    "featured_image_url", local_instance.featured_image_url
                )
                local_instance.language_code = wp_updated_data.get(
                    "language_code", local_instance.language_code
                )
                local_instance.language_name = wp_updated_data.get(
                    "language_name", local_instance.language_name
                )
                # Update categories based on what WP returns after update
                local_instance.categories = wp_updated_data.get(
                    "categories", local_instance.categories
                )  # Keep local if WP doesn't return
                local_instance.last_synced_at = timezone.now()
                local_instance.save()
                logger.info("Push successful for WP ID %s.", local_instance.wp_id)
            else:
                # If no data returned, still update local fields that were sent in request
                local_instance.title = request.data.get("title", local_instance.title)
                local_instance.content = request.data.get(
                    "content", local_instance.content
                )
                local_instance.status = request.data.get(
                    "status", local_instance.status
                )
                logger.warning(
                    "Push to WP ID %s successful but received no update data. Updating local fields from request.",
                    local_instance.wp_id,
                )
                local_instance.last_synced_at = timezone.now()
                local_instance.save()
            # --- End Update local instance ---

            # Log the successful operation
            user = request.user if request.user.is_authenticated else None
            SyncLog.log_operation(
                wp_site_url=target_baseUrl,
                operation="push_to_wp",
                status="success",
                details={"wp_id": local_instance.wp_id},
                content_item=local_instance,
                user=user,
            )

            return Response(
                self.get_serializer(local_instance).data, status=status.HTTP_200_OK
            )
        except WordPressAPIClientError as e:
            logger.error(
                "WP API Error during push for local ID %s: %s", pk, e, exc_info=True
            )

            # Log the failure
            user = request.user if request.user.is_authenticated else None
            SyncLog.log_operation(
                wp_site_url=target_baseUrl,
                operation="push_to_wp",
                status="failed",
                details={
                    "error": str(e),
                    "status_code": getattr(e, "status_code", None),
                    "wp_id": local_instance.wp_id,
                },
                content_item=local_instance,
                user=user,
            )

            return Response(
                {"error": f"Failed push to WP: {e}"},
                status=e.status_code or status.HTTP_502_BAD_GATEWAY,
            )
        except Exception as e:
            logger.error(
                "Unexpected error during push for local ID %s: %s", pk, e, exc_info=True
            )

            # Log the failure
            user = request.user if request.user.is_authenticated else None
            SyncLog.log_operation(
                wp_site_url=target_baseUrl,
                operation="push_to_wp",
                status="failed",
                details={"error": str(e), "wp_id": local_instance.wp_id},
                content_item=local_instance,
                user=user,
            )

            return Response(
                {"error": f"Unexpected push error: {e}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["post"], url_path="delete-from-wp")
    def delete_from_wp(self, request, pk=None):
        local_instance = self.get_object()
        apiKey = request.data.get("apiKey")
        if not apiKey:
            raise exceptions.ParseError("'apiKey' required.")
        try:
            target_baseUrl = local_instance.wp_site_url
            if not target_baseUrl:
                raise exceptions.ValidationError("Missing source URL.")
            client = WordPressAPIClient(base_url=target_baseUrl, api_key=apiKey)
        except (ValueError, ImproperlyConfigured, exceptions.ValidationError) as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        wp_id_to_delete = local_instance.wp_id
        force_delete = request.data.get("force", False) == True

        # Check if this is a locally created post that hasn't been pushed to WordPress (wp_id <= 0)
        if wp_id_to_delete <= 0:
            logger.info(
                "Deleting local-only record ID %s (never pushed to WordPress).", pk
            )
            local_instance_id = local_instance.id
            local_instance.delete()
            return Response(
                {
                    "message": f"Local content deleted.",
                    "local_id_deleted": local_instance_id,
                },
                status=status.HTTP_200_OK,
            )

        try:
            logger.warning(
                "Deleting local ID %s (WP ID: %s) from %s (force=%s).",
                pk,
                wp_id_to_delete,
                target_baseUrl,
                force_delete,
            )
            delete_response = client.delete_content_item(
                wp_id_to_delete, force=force_delete
            )
            logger.info(
                "Successfully deleted WP ID %s. Deleting local record.", wp_id_to_delete
            )
            local_instance_id = local_instance.id
            local_instance.delete()
            return Response(
                {
                    "message": f"WP ID {wp_id_to_delete} deleted from WP and locally.",
                    "local_id_deleted": local_instance_id,
                    "wp_response": delete_response,
                },
                status=status.HTTP_200_OK,
            )
        except WordPressAPIClientError as e:
            logger.error(
                "WP API Error during delete for local ID %s: %s", pk, e, exc_info=True
            )
            if e.status_code == 404:
                # If the post doesn't exist in WordPress anymore, delete it locally as well
                logger.warning(
                    "WP ID %s not found on WordPress. Deleting local copy.",
                    wp_id_to_delete,
                )
                local_instance_id = local_instance.id
                local_instance.delete()
                return Response(
                    {
                        "message": f"WP ID {wp_id_to_delete} not found on WordPress. Local copy has been deleted.",
                        "local_id_deleted": local_instance_id,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"error": f"Failed delete from WP: {e}"},
                    status=e.status_code or status.HTTP_502_BAD_GATEWAY,
                )
        except Exception as e:
            logger.error(
                "Unexpected error during delete for local ID %s: %s",
                pk,
                e,
                exc_info=True,
            )
            return Response(
                {"error": f"Unexpected delete error: {e}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["post"], url_path="delete-local-only")
    def delete_local_only(self, request, pk=None):
        """
        Deletes content from the local database only, without affecting WordPress
        """
        local_instance = self.get_object()

        try:
            local_instance_id = local_instance.id
            wp_id = local_instance.wp_id
            wp_site_url = local_instance.wp_site_url

            logger.info(
                "Deleting local-only record ID %s (WP ID: %s) from database.", pk, wp_id
            )
            local_instance.delete()

            return Response(
                {
                    "message": f"Local content with ID {local_instance_id} deleted. WordPress content was NOT affected.",
                    "local_id_deleted": local_instance_id,
                    "wp_id": wp_id,
                    "wp_site_url": wp_site_url,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.error("Error deleting local content ID %s: %s", pk, e, exc_info=True)
            return Response(
                {"error": f"Failed to delete local content: {e}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    # --- ACTION: Get AI Translation Preview ---
    @action(detail=True, methods=["post"], url_path="ai-translate-preview")
    def ai_translate_preview(self, request, pk=None):
        """
        Gets an AI translation preview for the specified content item (pk).
        Expects {"target_language_code": "..."} in request body.
        Returns {"translated_title": "...", "translated_content": "..."}.
        Uses structure-preserving HTML translation for content.
        """
        source_instance = self.get_object()
        serializer = TranslationPreviewRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        target_language_code = serializer.validated_data["target_language_code"]
        source_language_code = source_instance.language_code or "en"  # Default source

        if source_language_code == target_language_code:
            return Response(
                {"error": "Target language cannot be the same as the source language."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Initialize AI Service
        try:
            if not settings.OPENAI_API_KEY:
                raise ImproperlyConfigured("OpenAI API Key is not configured.")
            ai_service = AITranslationService()
        except (ValueError, ImproperlyConfigured) as e:
            logger.error("AI Service initialization error: %s", e)
            return Response(
                {"error": f"Configuration error: {e}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        # Perform Translation
        try:
            logger.info(
                "Generating AI translation preview for local ID %s from '%s' to '%s'.",
                pk,
                source_language_code,
                target_language_code,
            )
            # Always translate title separately
            translated_title = ai_service.translate_title(
                title=source_instance.title,
                source_language=source_language_code,
                target_language=target_language_code,
            )

            # Use custom content if provided, otherwise use source content
            content_to_translate = serializer.validated_data.get("translated_content") or source_instance.content
            
            # Log if we're using custom content (likely cleaned AVADA content)
            if serializer.validated_data.get("translated_content"):
                logger.info("Using custom content for translation (likely cleaned AVADA content).")
                logger.info("Custom content length: %d", len(content_to_translate))
                logger.info("Original content length: %d", len(source_instance.content))
            else:
                logger.info("Using original source content for translation.")

            # Use the HTML-aware method for content
            logger.info("Using HTML-aware translation for content.")
            translated_content = ai_service.translate_html_content(
                html_content=content_to_translate,
                source_language=source_language_code,
                target_language=target_language_code,
            )

            logger.info("AI Translation preview generated for local ID %s.", pk)

            return Response(
                {
                    "translated_title": translated_title,
                    "translated_content": translated_content,  # This contains structured HTML
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.error(
                "AI Translation preview failed for local ID %s: %s",
                pk,
                e,
                exc_info=True,
            )
            return Response(
                {"error": f"AI Translation failed: {e}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    # --- NEW ACTION: Markdown-based AI Translation Preview ---
    @action(detail=True, methods=["post"], url_path="ai-translate-markdown")
    def ai_translate_markdown(self, request, pk=None):
        """
        Gets an AI translation for markdown content directly.
        Expects {"target_language_code": "...", "markdown_content": "..."}
        Returns {"translated_title": "...", "translated_markdown": "..."}

        This endpoint is optimized for complex languages like Chinese, Japanese, and Russian
        by preserving markdown structure rather than HTML structure.
        """
        source_instance = self.get_object()
        serializer = MarkdownTranslationRequestSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        target_language_code = serializer.validated_data["target_language_code"]
        markdown_content = serializer.validated_data["markdown_content"]
        source_language_code = source_instance.language_code or "en"  # Default source

        if source_language_code == target_language_code:
            return Response(
                {"error": "Target language cannot be the same as the source language."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Initialize AI Service
        try:
            if not settings.OPENAI_API_KEY:
                raise ImproperlyConfigured("OpenAI API Key is not configured.")
            ai_service = AITranslationService()
        except (ValueError, ImproperlyConfigured) as e:
            logger.error("AI Service initialization error: %s", e)
            return Response(
                {"error": f"Configuration error: {e}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        # Perform Translation
        try:
            logger.info(
                "Generating markdown-based AI translation for local ID %s from '%s' to '%s'.",
                pk,
                source_language_code,
                target_language_code,
            )

            # Always translate title separately
            translated_title = ai_service.translate_title(
                title=source_instance.title,
                source_language=source_language_code,
                target_language=target_language_code,
            )

            # Use the markdown-aware method for content
            logger.info("Using markdown translation for content.")
            translated_markdown = ai_service.translate_markdown_content(
                markdown_content=markdown_content,
                source_language=source_language_code,
                target_language=target_language_code,
            )

            logger.info("Markdown-based AI translation generated for local ID %s.", pk)

            return Response(
                {
                    "translated_title": translated_title,
                    "translated_markdown": translated_markdown,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.error(
                "Markdown-based AI translation failed for local ID %s: %s",
                pk,
                e,
                exc_info=True,
            )
            return Response(
                {"error": f"Markdown-based AI translation failed: {e}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    # --- UPDATED: Translate and Create with Markdown Support ---
    @action(detail=True, methods=["post"], url_path="translate-and-create")
    def translate_and_create(self, request, pk=None):
        """
        Creates a new translated post on WordPress based on the source item (pk).
        Can perform AI translation (preserving structure) OR use pre-translated content.
        Supports both HTML and markdown inputs:
        - For HTML: {"target_language_code": "...", "apiKey": "...", "translated_title": "...", "translated_content": "..."}
        - For markdown: {"target_language_code": "...", "apiKey": "...", "translated_title": "...", "translated_markdown": "...", "is_markdown": true}
        """
        # 1. Get Source Item & Validate Request
        source_instance = self.get_object()

        # Modify validation to accept either translated_content or translated_markdown
        serializer = TranslationRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        target_language_code = serializer.validated_data["target_language_code"]
        apiKey = serializer.validated_data["apiKey"]
        provided_title = serializer.validated_data.get("translated_title")
        provided_content = serializer.validated_data.get(
            "translated_content"
        )  # HTML content

        # New: Check if markdown was provided
        is_markdown = request.data.get("is_markdown", False)
        provided_markdown = request.data.get("translated_markdown")

        # --- NEW: Get selected category IDs ---
        selected_category_ids = serializer.validated_data.get(
            "category_ids"
        )  # Might be None if not provided
        # --- END NEW ---

        # Convert markdown to HTML if needed
        if is_markdown and provided_markdown:
            from .serializers import MarkdownToHtmlConverter

            converter = MarkdownToHtmlConverter()
            provided_content = converter.convert(provided_markdown)
            logger.info("Converted markdown to HTML for WordPress compatibility")

        source_language_code = source_instance.language_code or "en"

        if source_language_code == target_language_code:
            return Response(
                {"error": "Target language cannot be the same."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # 2. Initialize Services
        try:
            wp_client = WordPressAPIClient(
                base_url=source_instance.wp_site_url, api_key=apiKey
            )
            ai_service = None
            if not provided_title or not provided_content:  # Need AI translation?
                if not settings.OPENAI_API_KEY:
                    raise ImproperlyConfigured("OpenAI API Key needed.")
                ai_service = AITranslationService()
        except (ValueError, ImproperlyConfigured) as e:
            logger.error("Service initialization error: %s", e)
            return Response(
                {"error": f"Configuration error: {e}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        # 3. Perform AI Translation (if necessary)
        translated_title = provided_title
        translated_content = (
            provided_content  # Use provided structured HTML if available
        )

        if not translated_title or not translated_content:
            if not ai_service:
                return Response(
                    {"error": "AI Service could not be initialized."},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
            try:
                logger.info("Performing AI translation for create request...")
                translated_title = ai_service.translate_title(
                    title=source_instance.title,
                    source_language=source_language_code,
                    target_language=target_language_code,
                )

                # Check if we should use markdown-based translation
                if is_markdown and request.data.get("source_markdown"):
                    # If source markdown was provided, use markdown translation
                    source_markdown = request.data.get("source_markdown")
                    logger.info("Using markdown-based translation")
                    translated_markdown = ai_service.translate_markdown_content(
                        markdown_content=source_markdown,
                        source_language=source_language_code,
                        target_language=target_language_code,
                    )

                    # Convert to HTML for WordPress
                    from .serializers import MarkdownToHtmlConverter

                    converter = MarkdownToHtmlConverter()
                    translated_content = converter.convert(translated_markdown)
                    logger.info("Converted translated markdown to HTML for WordPress")
                else:
                    # Use HTML-aware method for content translation
                    logger.info(
                        "Using HTML-aware translation for content during creation."
                    )
                    translated_content = ai_service.translate_html_content(
                        html_content=source_instance.content,
                        source_language=source_language_code,
                        target_language=target_language_code,
                    )

                logger.info("AI Translation completed for create request.")
            except Exception as e:
                logger.error(
                    "AI Translation failed during create request: %s", e, exc_info=True
                )
                return Response(
                    {"error": f"AI Translation failed: {e}"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        # 4. Prepare Data for New WP Post (using translated structured HTML)
        new_post_data = {
            "title": translated_title,
            "content": translated_content,  # Send structured HTML
            "status": "draft",
            "post_type": source_instance.post_type,
            # Include translation metadata
            "meta_input": {
                "_satoloc_original_post": source_instance.wp_id,
                "_satoloc_source_language": source_language_code,
                "_satoloc_translation_language": target_language_code,
                "_satoloc_is_translation": True,
            },
        }

        # Add language code to the post data for non-English translations
        if target_language_code != "en":
            # Set language code for multilingual plugins (Polylang, WPML)
            new_post_data["language_code"] = target_language_code
            # Add 'lang' parameter for Polylang compatibility
            new_post_data["lang"] = target_language_code
            # For WPML compatibility
            new_post_data["wpml_language"] = target_language_code

            # Set slug with language code prefix for URL structure (example.com/fr/post-name)
            # This helps WordPress with multilingual URL structure
            if "slug" not in new_post_data:
                # Create slug from title if not provided
                slug = slugify(translated_title)
                new_post_data["slug"] = f"{target_language_code}/{slug}"
                # Also add post_name for direct WP integration
                new_post_data["post_name"] = f"{target_language_code}-{slug}"
                logger.info(
                    "Added language code %s to URL slug: %s",
                    target_language_code,
                    new_post_data["slug"],
                )

            # Store the relationship with the original content
            new_post_data["translation_of"] = source_instance.wp_id

        # --- MODIFIED: Add categories based on selection ---
        if (
            selected_category_ids is not None
        ):  # Check if IDs were provided in the request
            if selected_category_ids:  # If the list is not empty
                new_post_data["categories"] = selected_category_ids
                logger.info(
                    "Assigning selected categories %s to new translated post.",
                    selected_category_ids,
                )
            else:  # If an empty list was explicitly provided
                # Assign default category ID 1 (Uncategorized)
                new_post_data["categories"] = [1]
                logger.info(
                    "No categories selected or empty list provided, assigning default category ID 1."
                )
        else:
            # Fallback: If category_ids was NOT in the request, copy from source (original behavior)
            source_category_ids = []
            if isinstance(source_instance.categories, list):
                for cat in source_instance.categories:
                    if isinstance(cat, dict) and "id" in cat:
                        source_category_ids.append(cat["id"])

            if source_category_ids:
                new_post_data["categories"] = source_category_ids
                logger.info(
                    "No categories selected in request, adding source categories %s to new translated post.",
                    source_category_ids,
                )
            else:
                new_post_data["categories"] = [1]
                logger.info(
                    "No categories selected and source has none, assigning default category ID 1."
                )
        # --- END MODIFIED ---

        # 5. Create New Post on WordPress
        try:
            logger.info(
                "Creating translated post on WP site %s...", source_instance.wp_site_url
            )
            created_wp_post = wp_client.create_content_item(new_post_data)
            if not created_wp_post or "id" not in created_wp_post:
                raise WordPressAPIClientError(
                    "WP API did not return valid post object."
                )
            new_wp_id = created_wp_post["id"]
            logger.info(
                "Successfully created translated post on WP, new WP ID: %s", new_wp_id
            )
        except WordPressAPIClientError as e:
            logger.error("Failed to create translated post on WP: %s", e, exc_info=True)
            return Response(
                {"error": f"Failed to create post on WP: {e}"},
                status=e.status_code or status.HTTP_502_BAD_GATEWAY,
            )
        except Exception as e:
            logger.error("Unexpected error creating post on WP: %s", e, exc_info=True)
            return Response(
                {"error": f"Unexpected error creating WP post: {e}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        # 6. Create New Local Record for the Translation
        try:
            # Use AI service instance if available, otherwise create temporary one
            lang_name_service = ai_service or AITranslationService()
            target_language_name = (
                lang_name_service.get_language_name(target_language_code)
                if not created_wp_post.get("language_name")
                else created_wp_post.get("language_name")
            )

            new_local_defaults = {
                "wp_site_url": source_instance.wp_site_url,
                "wp_id": new_wp_id,
                "title": created_wp_post.get("title", translated_title),
                "content": created_wp_post.get(
                    "content", translated_content
                ),  # Store translated HTML
                "post_type": created_wp_post.get(
                    "post_type", source_instance.post_type
                ),
                "status": created_wp_post.get("status", "draft"),
                "date_created_gmt": self._parse_datetime_with_timezone(
                    created_wp_post.get("date_created_gmt")
                ),
                "date_modified_gmt": self._parse_datetime_with_timezone(
                    created_wp_post.get("date_modified_gmt")
                ),
                "wp_link": created_wp_post.get("link"),
                "featured_image_url": created_wp_post.get("featured_image_url"),
                "language_code": created_wp_post.get(
                    "language_code", target_language_code
                ),
                "language_name": target_language_name,
                # Save categories returned by WP (default to empty list)
                "categories": created_wp_post.get("categories", []),
                "last_synced_at": timezone.now(),
            }
            new_local_instance = SyncedContent.objects.create(**new_local_defaults)
            logger.info(
                "Successfully created local record (ID: %s) for new translation.",
                new_local_instance.id,
            )
        except Exception as db_error:
            logger.error(
                "DB error creating local record for new translation WP ID %s: %s",
                new_wp_id,
                db_error,
                exc_info=True,
            )
            intended_data_serializer = SyncedContentSerializer(data=new_local_defaults)
            intended_data_serializer.is_valid()
            return Response(
                {
                    "message": f"WP post created (ID: {new_wp_id}), but failed to save locally.",
                    "created_wp_post": created_wp_post,
                    "local_save_error": str(db_error),
                    "intended_local_data": intended_data_serializer.data,
                },
                status=status.HTTP_201_CREATED,
            )

        # 7. Return Success Response with info if markdown was used
        serializer = SyncedContentSerializer(new_local_instance)
        response_data = {
            "message": f"AI translation to {target_language_code} created successfully.",
            "new_content_item": serializer.data,
        }

        # Add info if markdown was used
        if is_markdown:
            response_data["translation_method"] = "markdown"

        return Response(response_data, status=status.HTTP_201_CREATED)

    # --- perform_update, perform_destroy ---
    def perform_update(self, serializer):
        logger.info(
            "Updating local record ID %s (WP ID: %s) via API.",
            serializer.instance.id,
            serializer.instance.wp_id,
        )
        serializer.save()

    def perform_destroy(self, instance):
        logger.warning(
            "Deleting local record ID %s (WP ID: %s) via API. This does NOT affect WordPress.",
            instance.id,
            instance.wp_id,
        )
        instance.delete()


class SyncLogViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing sync logs.
    This is a read-only viewset as logs should not be modified via the API.
    """

    serializer_class = SyncLogSerializer
    permission_classes = [permissions.AllowAny]  # Adjust permissions as needed

    def get_queryset(self):
        """
        Get logs with optional filtering by site URL, operation type, or status.
        """
        queryset = SyncLog.objects.all()

        # Filter by site URL if provided
        site_url = self.request.query_params.get("wp_site_url", None)
        if site_url:
            normalized_url = site_url.rstrip("/")
            queryset = queryset.filter(wp_site_url=normalized_url)

        # Filter by operation type if provided
        operation = self.request.query_params.get("operation", None)
        if operation:
            queryset = queryset.filter(operation=operation)

        # Filter by status if provided
        status_param = self.request.query_params.get("status", None)
        if status_param:
            queryset = queryset.filter(status=status_param)

        # Filter by content item ID if provided
        content_item_id = self.request.query_params.get("content_item_id", None)
        if content_item_id:
            queryset = queryset.filter(content_item_id=content_item_id)

        # Filter by user ID if provided
        user_id = self.request.query_params.get("user_id", None)
        if user_id:
            queryset = queryset.filter(user_id=user_id)

        # Return ordered by most recent first
        return queryset.order_by("-created_at")
