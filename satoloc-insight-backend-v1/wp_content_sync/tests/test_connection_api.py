#!/usr/bin/env python
"""
WordPress Connection API Test Script

This script tests:
1. The Django backend API for WordPress connections
2. The WordPress plugin API directly

Usage:
    python test_connection_api.py [--django_url URL] [--wp_url URL] [--wp_api_key KEY]

If arguments are not provided, it will use the default test values.
"""

import argparse
import requests
import sys
import json
from datetime import datetime

# Default test values
DEFAULT_DJANGO_URL = "http://127.0.0.1:8000/api"
DEFAULT_DJANGO_USERNAME = "requiemcreatif-admin"
DEFAULT_DJANGO_PASSWORD = "fucxaj-kymvYk-rakwa4"

# WordPress Plugin API defaults
DEFAULT_WP_URL = "http://satoloc-connector.local/wp-json/satoloc-insight-connector/v1"
DEFAULT_WP_API_KEY = "iu10A349UUe1u7GXftnmwHX4ZtYDIlTsEfTbm4xrBjUJJWGM8E0UXRC0RsmAGOjD"

# Test configuration
TIMEOUT = 10
DJ<PERSON>G<PERSON>_ENDPOINT = "/wp-content-sync/connections/"
WP_HEADER_KEY = "X-Satoloc-Insight-Key"

def test_django_auth(base_url, username, password):
    """Test various Django auth endpoints"""
    print(f"\n=== Testing Django Authentication for {username} ===")
    
    # Try multiple possible auth endpoints
    auth_endpoints = [
        "/token/",
        "/auth/token/",
        "/api-auth/login/",
        "/users/token/",
        "/users/login/",
        "/auth/login/"
    ]
    
    for endpoint in auth_endpoints:
        auth_url = f"{base_url}{endpoint}"
        print(f"\nTrying auth endpoint: {auth_url}")
        
        try:
            # Try POST with JSON
            response = requests.post(
                auth_url,
                json={"username": username, "password": password},
                headers={"Content-Type": "application/json"},
                timeout=TIMEOUT
            )
            
            print(f"POST JSON: Status code: {response.status_code}")
            
            if response.ok:
                print("✅ Authentication successful!")
                try:
                    data = response.json()
                    print(f"Response: {json.dumps(data, indent=2)}")
                    if "token" in data or "access" in data:
                        return data.get("token") or data.get("access")
                except:
                    print(f"Response (not JSON): {response.text[:200]}")
            
            # Try POST with form data
            response = requests.post(
                auth_url,
                data={"username": username, "password": password},
                timeout=TIMEOUT
            )
            
            print(f"POST Form: Status code: {response.status_code}")
            
            if response.ok:
                print("✅ Authentication successful!")
                try:
                    data = response.json()
                    print(f"Response: {json.dumps(data, indent=2)}")
                    if "token" in data or "access" in data:
                        return data.get("token") or data.get("access")
                except:
                    print(f"Response (not JSON): {response.text[:200]}")
            
        except Exception as e:
            print(f"❌ Error trying {auth_url}: {str(e)}")
    
    # If no endpoint worked, try direct cookie-based authentication
    try:
        print("\nTrying cookie-based auth with session login")
        session = requests.Session()
        login_url = f"{base_url.split('/api')[0]}/admin/login/"
        
        # Get CSRF token
        response = session.get(login_url, timeout=TIMEOUT)
        if 'csrftoken' in session.cookies:
            csrftoken = session.cookies['csrftoken']
            print(f"Got CSRF token: {csrftoken[:10]}...")
            
            # Login with the token
            login_data = {
                'username': username,
                'password': password,
                'csrfmiddlewaretoken': csrftoken,
                'next': '/admin/'
            }
            response = session.post(
                login_url, 
                data=login_data, 
                headers={'Referer': login_url},
                timeout=TIMEOUT
            )
            
            print(f"Login response status: {response.status_code}")
            if response.status_code == 200 or response.status_code == 302:
                print("✅ Cookie-based authentication may have succeeded")
                print("Using session cookies for subsequent requests")
                return session
    except Exception as e:
        print(f"❌ Cookie-based auth error: {str(e)}")
    
    print("❌ All authentication methods failed")
    return None

def test_django_connection_api(base_url, auth):
    """Test the Django WordPress connection API endpoints"""
    print(f"\n=== Testing Django API at {base_url} ===\n")
    
    # Prepare headers and session
    headers = {'Content-Type': 'application/json'}
    session = None
    
    if isinstance(auth, str):  # Token auth
        headers['Authorization'] = f"Bearer {auth}"
    elif isinstance(auth, requests.Session):  # Session auth
        session = auth
    
    # 1. List connections (GET)
    list_url = f"{base_url}{DJANGO_ENDPOINT}"
    print(f"Testing GET {list_url}")
    print(f"Headers: {headers if not session else 'Using session cookies'}")
    
    try:
        if session:
            response = session.get(list_url, timeout=TIMEOUT)
        else:
            response = requests.get(list_url, headers=headers, timeout=TIMEOUT)
        
        print(f"Status code: {response.status_code}")
        
        if response.ok:
            print("✅ GET connections successful")
            try:
                connections = response.json()
                if connections:
                    print(f"Found {len(connections)} connection(s):")
                    for i, conn in enumerate(connections):
                        print(f"  {i+1}. {conn.get('site_url')} (ID: {conn.get('id')}, User: {conn.get('username')})")
                else:
                    print("No connections found in the database")
                    
                # Try another endpoint without filtering (just in case)
                print("\nTrying alternative endpoints for connections...")
                alt_endpoints = [
                    "/wp-content-sync/content/",
                    "/wp-content-sync/all-connections/",
                    "/wp-content-sync/connections/all/"
                ]
                
                for alt_endpoint in alt_endpoints:
                    alt_url = f"{base_url}{alt_endpoint}"
                    print(f"Testing {alt_url}...")
                    
                    try:
                        if session:
                            alt_response = session.get(alt_url, timeout=TIMEOUT)
                        else:
                            alt_response = requests.get(alt_url, headers=headers, timeout=TIMEOUT)
                        
                        print(f"Status code: {alt_response.status_code}")
                        
                        if alt_response.ok:
                            print(f"✅ GET {alt_endpoint} successful")
                            try:
                                data = alt_response.json()
                                if isinstance(data, list) and data:
                                    print(f"Found {len(data)} items")
                                    print(f"First item: {json.dumps(data[0], indent=2)[:500]}")
                                elif isinstance(data, dict) and data:
                                    print(f"Data: {json.dumps(data, indent=2)[:500]}")
                                else:
                                    print(f"Empty or unexpected response format: {data}")
                            except:
                                print(f"Response (not JSON): {alt_response.text[:200]}")
                    except Exception as e:
                        print(f"Error trying {alt_url}: {str(e)}")
            except:
                print(f"Response (not JSON): {response.text[:200]}")
        else:
            print(f"❌ GET connections failed: {response.status_code}")
            try:
                print(f"Error details: {response.json()}")
            except:
                print(f"Error details: {response.text[:200]}")
    except Exception as e:
        print(f"❌ GET connections exception: {str(e)}")
    
    return True

def test_wp_plugin_api(base_url, api_key):
    """Test the WordPress plugin API directly"""
    print(f"\n=== Testing WordPress Plugin API at {base_url} ===\n")
    
    # Prepare headers
    headers = {
        'Content-Type': 'application/json',
        WP_HEADER_KEY: api_key
    }
    
    # 1. List content (GET)
    content_url = f"{base_url}/content"
    print(f"Testing GET {content_url}")
    print(f"Headers: {headers}")
    
    try:
        response = requests.get(content_url, headers=headers, timeout=TIMEOUT)
        print(f"Status code: {response.status_code}")
        
        if response.ok:
            print("✅ GET content successful")
            try:
                content_items = response.json()
                if content_items:
                    print(f"Found {len(content_items)} content item(s)")
                    for i, item in enumerate(content_items[:3]):  # Show first 3 items
                        # Extract relevant fields
                        item_id = item.get('id')
                        title = item.get('title', {}).get('rendered', 'Untitled')
                        status = item.get('status')
                        item_type = item.get('type')
                        
                        print(f"  {i+1}. ID: {item_id}, Title: {title}, Type: {item_type}, Status: {status}")
                else:
                    print("No content items found")
            except:
                print(f"Response (not JSON): {response.text[:200]}")
        else:
            print(f"❌ GET content failed: {response.status_code}")
            try:
                print(f"Error details: {response.json()}")
            except:
                print(f"Error details: {response.text[:200]}")
    except Exception as e:
        print(f"❌ GET content exception: {str(e)}")
    
    # 2. Check other available endpoints
    print("\nChecking available endpoints...")
    
    for endpoint in ["", "/settings", "/info", "/status"]:
        test_url = f"{base_url}{endpoint}"
        print(f"\nTesting GET {test_url}")
        
        try:
            response = requests.get(test_url, headers=headers, timeout=TIMEOUT)
            print(f"Status code: {response.status_code}")
            
            if response.ok:
                print(f"✅ GET {endpoint or '/'} successful")
                try:
                    data = response.json()
                    print(f"Response: {json.dumps(data, indent=2)[:500]}")
                except:
                    print(f"Response (not JSON): {response.text[:200]}")
            else:
                print(f"❌ GET {endpoint or '/'} failed: {response.status_code}")
        except Exception as e:
            print(f"❌ GET {endpoint or '/'} exception: {str(e)}")
    
    return True

def main():
    parser = argparse.ArgumentParser(description="Test WordPress Connection API")
    parser.add_argument("--django_url", default=DEFAULT_DJANGO_URL, help="Django API URL")
    parser.add_argument("--django_username", default=DEFAULT_DJANGO_USERNAME, help="Django Username")
    parser.add_argument("--django_password", default=DEFAULT_DJANGO_PASSWORD, help="Django Password")
    parser.add_argument("--wp_url", default=DEFAULT_WP_URL, help="WordPress Plugin API URL")
    parser.add_argument("--wp_api_key", default=DEFAULT_WP_API_KEY, help="WordPress Plugin API Key")
    parser.add_argument("--skip_django", action="store_true", help="Skip Django API tests")
    parser.add_argument("--skip_wp", action="store_true", help="Skip WordPress Plugin tests")
    
    args = parser.parse_args()
    
    # Normalize URLs (remove trailing slash)
    django_url = args.django_url.rstrip('/')
    wp_url = args.wp_url.rstrip('/')
    
    # Print configuration
    print("\n=== WordPress Connection Test ===")
    print("\n--- Django Backend ---")
    print(f"URL: {django_url}")
    print(f"Username: {args.django_username}")
    print(f"Password: {'*' * len(args.django_password)}")
    
    print("\n--- WordPress Plugin ---")
    print(f"URL: {wp_url}")
    print(f"API Key: {'*' * 10}...{'*' * 5}")
    
    success = True
    
    # Test Django backend
    if not args.skip_django:
        auth = test_django_auth(django_url, args.django_username, args.django_password)
        django_success = test_django_connection_api(django_url, auth)
        success = success and django_success
    
    # Test WordPress plugin API
    if not args.skip_wp:
        wp_success = test_wp_plugin_api(wp_url, args.wp_api_key)
        success = success and wp_success
    
    # Final status
    if success:
        print("\n✅ Connection tests completed")
        return 0
    else:
        print("\n⚠️ Connection tests completed with warnings/errors")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 