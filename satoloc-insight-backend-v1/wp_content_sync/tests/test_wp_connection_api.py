"""
Tests for WordPress Connection API endpoints.

These tests verify that the WordPress Connection API is working correctly:
- Retrieving connections for a user
- Creating connections
- Updating connections
- Deleting connections
- Verifying connections work
"""

from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
import json
from unittest.mock import patch, MagicMock

from wp_content_sync.models import WordPressConnection

User = get_user_model()

class WordPressConnectionAPITestCase(APITestCase):
    """Test case for the WordPress Connection API endpoints."""
    
    def setUp(self):
        """Set up test data."""
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='password123'
        )
        
        # Create another user to test isolation
        self.another_user = User.objects.create_user(
            username='anotheruser',
            email='<EMAIL>',
            password='password123'
        )
        
        # Create a test WordPress connection
        self.connection = WordPressConnection.objects.create(
            site_url='http://test-wordpress.com',
            user=self.user,
            api_key='test_api_key',
            is_active=True
        )
        
        # Create another connection for the other user
        self.other_connection = WordPressConnection.objects.create(
            site_url='http://another-wordpress.com',
            user=self.another_user,
            api_key='another_api_key',
            is_active=True
        )
        
        # Set up the API client
        self.client = APIClient()
        
        # API endpoint
        self.list_url = reverse('wp-connections-list')
        self.detail_url = reverse('wp-connections-detail', kwargs={'pk': self.connection.pk})
        
    def test_list_connections_authenticated(self):
        """Test that authenticated users can list their connections."""
        # Authenticate the user
        self.client.force_authenticate(user=self.user)
        
        # Make the request
        response = self.client.get(self.list_url)
        
        # Check response status and data
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify response contains connection data
        data = response.json()
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['site_url'], 'http://test-wordpress.com')
        self.assertEqual(data[0]['username'], 'testuser')
        self.assertTrue(data[0]['is_active'])
        
    def test_list_connections_unauthenticated(self):
        """Test that unauthenticated users get an empty list."""
        # Make the request without authentication
        response = self.client.get(self.list_url)
        
        # Check response status
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Should be an empty list
        self.assertEqual(response.json(), [])
        
    def test_connection_user_isolation(self):
        """Test that users can only see their own connections."""
        # Authenticate the other user
        self.client.force_authenticate(user=self.another_user)
        
        # Make the request
        response = self.client.get(self.list_url)
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Should only see their own connection
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['site_url'], 'http://another-wordpress.com')
        
    def test_create_connection(self):
        """Test creating a new WordPress connection."""
        # Authenticate the user
        self.client.force_authenticate(user=self.user)
        
        # New connection data
        new_connection = {
            'site_url': 'http://new-wordpress.com',
            'api_key': 'new_api_key',
            'is_active': True
        }
        
        # Make the request
        response = self.client.post(
            self.list_url,
            data=json.dumps(new_connection),
            content_type='application/json'
        )
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        data = response.json()
        self.assertEqual(data['site_url'], 'http://new-wordpress.com')
        self.assertEqual(data['username'], 'testuser')
        self.assertTrue(data['is_active'])
        
        # Verify it was created in the database
        self.assertTrue(
            WordPressConnection.objects.filter(
                site_url='http://new-wordpress.com',
                user=self.user
            ).exists()
        )
        
    @patch('wp_content_sync.views.requests.get')
    def test_verify_connection(self, mock_get):
        """Test verifying a WordPress connection."""
        # Set up the mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        # Authenticate the user
        self.client.force_authenticate(user=self.user)
        
        # Make the request
        url = reverse('wp-connections-verify-connection', kwargs={'pk': self.connection.pk})
        response = self.client.post(url)
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data['status'], 'success')
        
        # Verify the mock was called with correct parameters
        mock_get.assert_called_once()
        args, kwargs = mock_get.call_args
        self.assertEqual(
            args[0],
            'http://test-wordpress.com/wp-json/satoloc-insight-connector/v1/content'
        )
        self.assertEqual(
            kwargs['headers'],
            {'X-Satoloc-Insight-Key': 'test_api_key'}
        )
        
    def test_update_connection(self):
        """Test updating a WordPress connection."""
        # Authenticate the user
        self.client.force_authenticate(user=self.user)
        
        # Update data
        update_data = {
            'api_key': 'updated_api_key',
            'is_active': False
        }
        
        # Make the request
        response = self.client.patch(
            self.detail_url,
            data=json.dumps(update_data),
            content_type='application/json'
        )
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data['site_url'], 'http://test-wordpress.com')
        self.assertFalse(data['is_active'])
        
        # Verify it was updated in the database
        connection = WordPressConnection.objects.get(pk=self.connection.pk)
        self.assertEqual(connection.api_key, 'updated_api_key')
        self.assertFalse(connection.is_active)
        
    def test_delete_connection(self):
        """Test deleting a WordPress connection."""
        # Authenticate the user
        self.client.force_authenticate(user=self.user)
        
        # Make the request
        response = self.client.delete(self.detail_url)
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # Verify it was deleted from the database
        self.assertFalse(
            WordPressConnection.objects.filter(pk=self.connection.pk).exists()
        )
        
    def test_update_last_sync(self):
        """Test updating the last_sync timestamp."""
        # Authenticate the user
        self.client.force_authenticate(user=self.user)
        
        # Verify the last_sync is initially None
        self.assertIsNone(self.connection.last_sync)
        
        # Make the request
        url = reverse('wp-connections-update-last-sync', kwargs={'pk': self.connection.pk})
        response = self.client.post(url)
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data['status'], 'success')
        self.assertIsNotNone(data['last_sync'])
        
        # Verify the last_sync was updated in the database
        connection = WordPressConnection.objects.get(pk=self.connection.pk)
        self.assertIsNotNone(connection.last_sync) 