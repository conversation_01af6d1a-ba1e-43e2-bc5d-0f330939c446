import unittest
import os
import json
import requests
import logging
from unittest import mock
from django.test import TestCase, override_settings
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient, APITestCase
from rest_framework import status

from wp_content_sync.models import WordPressConnection, SyncedContent, SyncLog
from wp_content_sync.api_client import Word<PERSON>ress<PERSON>IClient, WordPressAPIClientError

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

User = get_user_model()

class MockResponse:
    """Mock response class for simulating HTTP responses in tests"""
    def __init__(self, json_data=None, status_code=200, text="", headers=None):
        self.json_data = json_data
        self.status_code = status_code
        self.text = text
        self.headers = headers or {}
        self.ok = 200 <= status_code < 300
    
    def json(self):
        return self.json_data
    
    def raise_for_status(self):
        if not self.ok:
            raise requests.HTTPError(f"HTTP Error: {self.status_code}")


class WordPressConnectionModelTest(TestCase):
    """Test the WordPressConnection model"""
    
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create a test connection
        self.connection = WordPressConnection.objects.create(
            user=self.user,
            site_url='http://satoloc-connector.local',
            api_key='test_api_key',
            is_active=True
        )
    
    def test_connection_created(self):
        """Test that a connection can be created"""
        connection = WordPressConnection.objects.get(id=self.connection.id)
        self.assertEqual(connection.site_url, 'http://satoloc-connector.local')
        self.assertEqual(connection.api_key, 'test_api_key')
        self.assertTrue(connection.is_active)
        self.assertIsNone(connection.last_sync)
    
    def test_connection_string_representation(self):
        """Test the string representation of a connection"""
        self.assertIn('satoloc-connector.local', str(self.connection))
        self.assertIn('testuser', str(self.connection))


class WordPressAPIClientTest(TestCase):
    """Test the WordPress API client functionality"""
    
    def setUp(self):
        self.base_url = 'http://satoloc-connector.local'
        self.api_key = 'test_api_key'
    
    @mock.patch('requests.head')
    @mock.patch('requests.request')
    def test_client_initialization(self, mock_request, mock_head):
        """Test client initialization and namespace discovery"""
        # Mock the head request for namespace discovery
        mock_head.return_value = MockResponse(status_code=404)
        
        # Then mock a successful discovery with alternative namespace
        mock_request.return_value = MockResponse(
            json_data={'status': 'ok'},
            status_code=200
        )
        
        # Initialize the client
        client = WordPressAPIClient(
            base_url=self.base_url,
            api_key=self.api_key
        )
        
        self.assertEqual(client.base_url, self.base_url)
        self.assertEqual(client.api_key, self.api_key)
        
        # Verify the api_base correctly includes /wp-json/
        self.assertTrue('/wp-json/' in client.api_base)
    
    @mock.patch('requests.head')
    @mock.patch('requests.request')
    def test_get_content_items(self, mock_request, mock_head):
        """Test fetching content items from WordPress"""
        # Mock the head request for namespace discovery
        mock_head.return_value = MockResponse(status_code=200)
        
        # Mock the content response
        mock_content = [
            {
                'id': 1,
                'title': 'Test Post',
                'content': 'This is test content',
                'status': 'publish',
                'post_type': 'post'
            }
        ]
        
        mock_request.return_value = MockResponse(
            json_data=mock_content,
            status_code=200
        )
        
        # Initialize client and get content
        client = WordPressAPIClient(
            base_url=self.base_url,
            api_key=self.api_key
        )
        
        content = client._make_request('GET', 'content')
        
        # Verify the request was made with correct parameters
        mock_request.assert_called_with(
            method='GET',
            url=mock.ANY,  # We'll check this contains the right parts
            headers=mock.ANY,
            params=None,
            json=None,
            timeout=mock.ANY
        )
        
        # Check the URL contains the expected parts
        call_args = mock_request.call_args
        request_url = call_args[1]['url']
        self.assertIn(self.base_url, request_url)
        self.assertIn('/wp-json/', request_url)
        
        # Check response parsing
        self.assertEqual(content, mock_content)
    
    @mock.patch('requests.head')
    def test_connection_error_handling(self, mock_head):
        """Test error handling for connection errors"""
        # Mock a connection error
        mock_head.side_effect = requests.ConnectionError("Connection refused")
        
        # Initialize client which should handle the error gracefully
        client = None
        try:
            client = WordPressAPIClient(
                base_url=self.base_url,
                api_key=self.api_key
            )
        except Exception as e:
            self.fail(f"Client initialization raised unexpected exception: {e}")
        
        self.assertIsNotNone(client)


class WordPressConnectionViewSetTest(APITestCase):
    """Test the WordPressConnection API endpoints"""
    
    def setUp(self):
        # Create users
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        self.admin_user = User.objects.create_user(
            username='adminuser',
            email='<EMAIL>',
            password='adminpassword',
            is_staff=True
        )
        
        # Create connections
        self.connection = WordPressConnection.objects.create(
            user=self.user,
            site_url='http://satoloc-connector.local',
            api_key='test_api_key',
            is_active=True
        )
        
        # Set up the API client
        self.client = APIClient()
    
    def test_connection_list_requires_auth(self):
        """Test that listing connections requires authentication"""
        url = reverse('wp-connections-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_connection_list_for_user(self):
        """Test listing connections for an authenticated user"""
        url = reverse('wp-connections-list')
        self.client.force_authenticate(user=self.user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # User should only see their own connections
        data = response.json()
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['site_url'], 'http://satoloc-connector.local')
    
    def test_create_connection(self):
        """Test creating a new connection"""
        url = reverse('wp-connections-list')
        self.client.force_authenticate(user=self.user)
        
        # New connection data
        new_connection = {
            'site_url': 'http://another-site.com',
            'api_key': 'another_key',
            'is_active': True
        }
        
        response = self.client.post(url, new_connection, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check that the connection was created
        self.assertEqual(WordPressConnection.objects.count(), 2)
        self.assertTrue(WordPressConnection.objects.filter(site_url='http://another-site.com').exists())
    
    @mock.patch('requests.get')
    def test_verify_connection(self, mock_get):
        """Test verifying a connection"""
        url = reverse('wp-connections-detail', args=[self.connection.id]) + 'verify_connection/'
        self.client.force_authenticate(user=self.user)
        
        # Mock a successful verification
        mock_get.return_value = MockResponse(
            json_data={'status': 'ok'},
            status_code=200
        )
        
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify that the correct API endpoint was called
        mock_get.assert_called_with(
            f"{self.connection.site_url}/wp-json/satoloc-insight-connector/v1/content",
            headers={"X-Satoloc-Insight-Key": self.connection.api_key},
            timeout=10
        )
    
    @mock.patch('requests.get')
    def test_verify_connection_failure(self, mock_get):
        """Test failed connection verification"""
        url = reverse('wp-connections-detail', args=[self.connection.id]) + 'verify_connection/'
        self.client.force_authenticate(user=self.user)
        
        # Mock a failure
        mock_get.return_value = MockResponse(
            status_code=404,
            text="Not Found"
        )
        
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Error message should be in the response
        self.assertIn('status', response.json())
        self.assertEqual(response.json()['status'], 'error')


class SyncedContentTest(APITestCase):
    """Test the SyncedContent functionality"""
    
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create a test connection
        self.connection = WordPressConnection.objects.create(
            user=self.user,
            site_url='http://satoloc-connector.local',
            api_key='test_api_key',
            is_active=True
        )
        
        # Create some test content
        self.content = SyncedContent.objects.create(
            wp_site_url=self.connection.site_url,
            wp_id=1,
            title='Test Post',
            content='This is test content',
            post_type='post',
            status='publish'
        )
        
        # Set up the API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
    
    def test_content_list(self):
        """Test listing content"""
        url = reverse('synced-content-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check the content is returned
        data = response.json()
        if isinstance(data, dict) and 'results' in data:
            # Paginated response
            results = data['results']
        else:
            # List response
            results = data
            
        self.assertTrue(len(results) > 0)
        self.assertEqual(results[0]['title'], 'Test Post')
    
    def test_content_filtering(self):
        """Test filtering content by site URL"""
        url = reverse('synced-content-list') + f'?wp_site_url={self.connection.site_url}'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check filter works
        data = response.json()
        if isinstance(data, dict) and 'results' in data:
            results = data['results']
        else:
            results = data
            
        self.assertTrue(len(results) > 0)
        for item in results:
            self.assertEqual(item['wp_site_url'], self.connection.site_url)
    
    @mock.patch('wp_content_sync.views.WordPressAPIClient')
    def test_fetch_all_from_wp(self, mock_client_class):
        """Test fetching all content from WordPress"""
        url = reverse('synced-content-list') + 'fetch-all-from-wp/'
        
        # Set up the mock client
        mock_client = mock.MagicMock()
        mock_client_class.return_value = mock_client
        
        # Mock WordPress content response
        mock_content = [
            {
                'id': 2,
                'title': 'New Post',
                'content': 'New content',
                'status': 'publish',
                'post_type': 'post'
            }
        ]
        mock_client.get_all_content_items.return_value = mock_content
        
        # Test the API
        response = self.client.post(
            url,
            {
                'baseUrl': self.connection.site_url,
                'apiKey': self.connection.api_key
            },
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check that the content was synced
        self.assertTrue(SyncedContent.objects.filter(title='New Post').exists())


class ComprehensiveEndToEndTest(APITestCase):
    """End-to-end test of the WordPress connection process"""
    
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Authenticate the API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
    
    @mock.patch('requests.get')
    @mock.patch('wp_content_sync.api_client.WordPressAPIClient._make_request')
    def test_end_to_end_connection_and_sync(self, mock_make_request, mock_get):
        """Test the entire connection and sync process"""
        # Step 1: Create a connection
        connection_url = reverse('wp-connections-list')
        connection_data = {
            'site_url': 'http://satoloc-connector.local',
            'api_key': 'test_api_key',
            'is_active': True
        }
        
        response = self.client.post(connection_url, connection_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Get the connection ID
        connection_id = response.json()['id']
        
        # Step 2: Verify the connection
        # Mock the verification response
        mock_get.return_value = MockResponse(
            json_data={'status': 'ok'},
            status_code=200
        )
        
        verify_url = reverse('wp-connections-detail', args=[connection_id]) + 'verify_connection/'
        response = self.client.post(verify_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['status'], 'success')
        
        # Step 3: Fetch content from WordPress
        # Mock the WordPress API response
        mock_content = [
            {
                'id': 1,
                'title': 'Sample Post',
                'content': 'Sample content',
                'post_type': 'post',
                'status': 'publish',
                'date_created_gmt': '2023-10-01T12:00:00Z',
                'date_modified_gmt': '2023-10-01T12:00:00Z',
                'link': 'http://satoloc-connector.local/sample-post'
            }
        ]
        mock_make_request.return_value = mock_content
        
        fetch_url = reverse('synced-content-list') + 'fetch-all-from-wp/'
        response = self.client.post(
            fetch_url,
            {
                'baseUrl': 'http://satoloc-connector.local',
                'apiKey': 'test_api_key'
            },
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['status'], 'success')
        
        # Step 4: Verify content was synced
        content_url = reverse('synced-content-list') + '?wp_site_url=http://satoloc-connector.local'
        response = self.client.get(content_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check that content is in the response
        data = response.json()
        if isinstance(data, dict) and 'results' in data:
            results = data['results']
        else:
            results = data
            
        self.assertTrue(len(results) > 0)
        self.assertEqual(results[0]['title'], 'Sample Post')


if __name__ == '__main__':
    unittest.main() 