#!/usr/bin/env python
"""
Direct WordPress API Test Script

This script tests direct connections to a WordPress site with the Satoloc Insight Connector plugin,
which is useful for diagnosing connection issues without going through the Django backend.

Usage:
    python direct_wp_test.py [--site_url URL] [--api_key KEY]

If arguments are not provided, it will use the default test values.
"""

import argparse
import requests
import sys
import json

# Default test values (modify as needed)
DEFAULT_SITE_URL = "http://satoloc-connector.local"
DEFAULT_API_KEY = "test_api_key"

# Test configuration
TIMEOUT = 10
WP_API_NAMESPACE = "satoloc-insight-connector/v1"
WP_API_KEY_HEADER = "X-Satoloc-Insight-Key"
ALTERNATIVE_NAMESPACES = ["wp-content-sync/v1"]
ALTERNATIVE_HEADERS = ["X-WP-Content-Sync-Key"]

def get_wp_api_url(base_url, namespace, endpoint):
    """Build a WordPress API URL"""
    clean_base = base_url.rstrip('/')
    clean_namespace = namespace.strip('/')
    clean_endpoint = endpoint.strip('/')
    
    return f"{clean_base}/wp-json/{clean_namespace}/{clean_endpoint}"

def test_connection(site_url, api_key):
    """Test a direct connection to the WordPress site"""
    print(f"\n=== Testing connection to {site_url} ===\n")
    
    # First try the primary namespace
    primary_url = get_wp_api_url(site_url, WP_API_NAMESPACE, "content")
    primary_headers = {WP_API_KEY_HEADER: api_key}
    
    print(f"Testing primary configuration:")
    print(f"URL: {primary_url}")
    print(f"Headers: {primary_headers}")
    
    try:
        response = requests.get(
            primary_url,
            headers=primary_headers,
            timeout=TIMEOUT
        )
        
        print(f"Status code: {response.status_code}")
        if response.ok:
            print("✅ Primary connection successful!")
            try:
                data = response.json()
                print(f"Response data preview: {json.dumps(data[:2] if isinstance(data, list) else data, indent=2)[:500]}...")
                return True
            except:
                print(f"Response text preview: {response.text[:500]}...")
                return True
        else:
            print(f"❌ Primary connection failed: {response.status_code}")
            try:
                print(f"Error details: {response.text[:500]}")
            except:
                print("Could not read error details")
    except Exception as e:
        print(f"❌ Primary connection failed with exception: {str(e)}")
    
    # Try alternative namespaces if primary fails
    print("\nTrying alternative configurations...")
    
    for i, (namespace, header_key) in enumerate(zip(ALTERNATIVE_NAMESPACES, ALTERNATIVE_HEADERS)):
        alt_url = get_wp_api_url(site_url, namespace, "content")
        alt_headers = {header_key: api_key}
        
        print(f"\nAlternative {i+1}:")
        print(f"URL: {alt_url}")
        print(f"Headers: {alt_headers}")
        
        try:
            response = requests.get(
                alt_url,
                headers=alt_headers,
                timeout=TIMEOUT
            )
            
            print(f"Status code: {response.status_code}")
            if response.ok:
                print(f"✅ Alternative {i+1} connection successful!")
                try:
                    data = response.json()
                    print(f"Response data preview: {json.dumps(data[:2] if isinstance(data, list) else data, indent=2)[:500]}...")
                    return True
                except:
                    print(f"Response text preview: {response.text[:500]}...")
                    return True
            else:
                print(f"❌ Alternative {i+1} connection failed: {response.status_code}")
                try:
                    print(f"Error details: {response.text[:500]}")
                except:
                    print("Could not read error details")
        except Exception as e:
            print(f"❌ Alternative {i+1} connection failed with exception: {str(e)}")
    
    print("\n❌ All connection attempts failed!")
    return False

def check_server_setup():
    """Check if the WordPress plugin is properly installed and accessible"""
    print("\n=== Checking Server Setup ===\n")
    
    for site, api_key in [
        ("http://satoloc-connector.local", "test_api_key"),
        ("https://www.satoloc.com", "test_api_key")
    ]:
        print(f"\nChecking site: {site}")
        
        # Test wp-admin is available (plugin should be installed here)
        admin_url = f"{site.rstrip('/')}/wp-admin/"
        try:
            response = requests.head(admin_url, timeout=5)
            print(f"WordPress admin check: {'✅ Available' if response.status_code < 400 else '❌ Not available'} (Status: {response.status_code})")
        except Exception as e:
            print(f"WordPress admin check: ❌ Error - {str(e)}")
        
        # Check if /wp-json/ is available (REST API is enabled)
        wpjson_url = f"{site.rstrip('/')}/wp-json/"
        try:
            response = requests.get(wpjson_url, timeout=5)
            print(f"WordPress REST API: {'✅ Enabled' if response.status_code < 400 else '❌ Disabled'} (Status: {response.status_code})")
        except Exception as e:
            print(f"WordPress REST API check: ❌ Error - {str(e)}")

def main():
    parser = argparse.ArgumentParser(description="Test WordPress API connection")
    parser.add_argument("--site_url", default=DEFAULT_SITE_URL, help="WordPress site URL")
    parser.add_argument("--api_key", default=DEFAULT_API_KEY, help="API key for authentication")
    
    args = parser.parse_args()
    
    # Print configuration
    print("\n=== WordPress API Connection Test ===")
    print(f"Site URL: {args.site_url}")
    print(f"API Key: {'*' * len(args.api_key)}")
    
    # Check server setup
    check_server_setup()
    
    # Test the connection
    success = test_connection(args.site_url, args.api_key)
    
    # Final status
    if success:
        print("\n✅ TEST PASSED: WordPress connection is working properly")
        return 0
    else:
        print("\n❌ TEST FAILED: Could not connect to WordPress")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 