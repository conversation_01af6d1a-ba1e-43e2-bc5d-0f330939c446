# backend/wp_content_sync/ai_translation.py
import os
import logging
import re
import uuid
import math
import time  # Keep for potential API error retries
from django.conf import settings
from openai import OpenAI, APIError
from typing import List, Dict, Any, Tuple
from bs4 import BeautifulSoup, NavigableString, Comment, Tag

logger = logging.getLogger(__name__)

TARGET_CHARS_PER_CHUNK = 6000
MAX_COMPLETION_TOKENS_PER_CHUNK = 2000
MAX_API_RETRIES = 1  # Retries specifically for API errors, not segment count mismatch


class AITranslationService:
    """Service for translating content using OpenAI models."""

    def __init__(self):
        api_key = settings.OPENAI_API_KEY
        if not api_key:
            if settings.DEBUG:
                logger.warning(
                    "OPENAI_API_KEY not set. AI translation features unavailable."
                )
                self.client = None
            else:
                raise ValueError("OPENAI_API_KEY not set")
        else:
            self.client = OpenAI(api_key=api_key)
        self.model = getattr(settings, "OPENAI_TRANSLATION_MODEL", "gpt-4.1")
        self.placeholder_prefix = "@@PLACEHOLDER_"
        self.placeholder_suffix = "@@"

    def _generate_placeholder(self) -> str:
        return f"{self.placeholder_prefix}{uuid.uuid4().hex}{self.placeholder_suffix}"

    def _protect_elements(self, html_content: str) -> Tuple[str, Dict[str, str]]:
        """Replaces shortcodes and block editor comments with placeholders."""
        # (Code remains the same as your previous version)
        placeholders = {}
        processed_html = html_content
        # Protect Shortcodes
        shortcode_pattern = re.compile(
            r"(\[[\w\-]+(?: [^\]]*)?\](?:.*?\[\/[\w\-]+\])?|\[[\w\-]+(?: [^\]]*)?\])"
        )

        def replace_shortcode(match):
            original = match.group(0)
            placeholder = self._generate_placeholder()
            placeholders[placeholder] = original
            return placeholder

        processed_html = shortcode_pattern.sub(replace_shortcode, processed_html)
        # Protect Block Editor Comments
        comment_pattern = re.compile(
            r"(<!--\s*(?:/?wp:[\w\-/]+(?:\{.*?\}|[^>])*?)\s*-->|<!--\s*/?wp:[\w\-/]+\s*-->)"
        )

        def replace_comment(match):
            original = match.group(0)
            placeholder = self._generate_placeholder()
            placeholders[placeholder] = original
            return placeholder

        processed_html = comment_pattern.sub(replace_comment, processed_html)
        logger.debug("Protected %d elements.", len(placeholders))
        return processed_html, placeholders

    def _restore_elements(self, html_content: str, placeholders: Dict[str, str]) -> str:
        """Restores original elements from placeholders."""
        # (Code remains the same as your previous version)
        restored_html = html_content
        for placeholder, original in placeholders.items():
            restored_html = restored_html.replace(placeholder, original, 1)
        logger.debug("Attempted to restore %d elements.", len(placeholders))
        return restored_html

    # --- Method to translate a single chunk - Simplified Retry ---
    def _translate_chunk(
        self,
        text_chunk: List[str],
        source_language: str,
        target_language: str,
        attempt: int = 1,
    ) -> List[str]:
        """
        Translates a list of text segments.
        Logs warnings on segment count mismatch but returns the received segments.
        Retries ONLY on specific API errors.
        """
        if not self.client:
            raise Exception("OpenAI client not initialized.")
        if not text_chunk:
            return []

        unique_id = uuid.uuid4().hex[:8]
        separator = f"\n---|||{unique_id}|||---\n"
        combined_text = separator.join(text_chunk)

        try:
            system_prompt = f"""You are an expert translator. Translate the following text segments from {source_language} to {target_language}.
The segments are separated by "{separator}".
CRITICAL: Preserve the EXACT structure, formatting, and whitespace of the original text including:
- Leading and trailing spaces (NEVER remove spaces at the beginning or end of segments)
- Spaces before and after punctuation 
- Spaces around links and HTML elements
- Line breaks and paragraph spacing
- Any special formatting or spacing patterns
- Markdown syntax (lists, images, links, headers, etc.)

IMPORTANT: Many segments will be text that appears before or after HTML links/elements. These segments often start or end with spaces that are ESSENTIAL for proper formatting. You MUST preserve these spaces exactly.

MARKDOWN PRESERVATION: If the content contains Markdown syntax, preserve it EXACTLY:
- List items: "- item" or "1. item" → keep the "- " or "1. " prefix
- Images: "![alt](url)" → keep the exact ![alt](url) format
- Links: "[text](url)" → keep the exact [text](url) format
- Headers: "# Header" → keep the "# " prefix
- Bold/Italic: "**bold**" or "*italic*" → keep the asterisks

Examples of what to preserve:
- "Text before " → should stay "Text before " (keep the trailing space)
- " text after" → should stay " text after" (keep the leading space)  
- " " → should stay " " (preserve whitespace-only segments)
- "- List item" → should stay "- Translated list item" (keep the "- ")
- "![Image](url)" → should stay "![Translated Alt](url)" (keep the markdown image syntax)

Preserve meaning, tone, and context. Ensure translations are natural in the target language while maintaining identical structure.
Return the translations in the exact same order, separated by the exact same separator "{separator}".
Do not add any text before the first translation or after the last one.
If an input segment is empty or just whitespace, return it exactly as is to maintain spacing."""

            logger.debug(
                "Attempt %s: Sending chunk with %s segments. Separator: '%s'",
                attempt,
                len(text_chunk),
                separator,
            )
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": combined_text},
                ],
                temperature=0.3,
                max_tokens=MAX_COMPLETION_TOKENS_PER_CHUNK,
                top_p=1.0,
            )
            translated_combined = response.choices[0].message.content

            if not translated_combined:  # Handle empty response
                logger.warning(
                    "Attempt %s: OpenAI returned empty response for chunk. Returning original.",
                    attempt,
                )
                # No retry for empty response unless it's treated as an API error later
                return text_chunk

            translated_segments = translated_combined.split(separator)
            received_count = len(translated_segments)
            expected_count = len(text_chunk)

            logger.debug(
                "Attempt %s: Expected segments: %s, Received segments: %s",
                attempt,
                expected_count,
                received_count,
            )

            # --- Log Warning on Mismatch, but return segments anyway ---
            if received_count != expected_count:
                logger.warning(
                    "Segment count mismatch in chunk! Expected %s, got %s. "
                    "Proceeding with received segments, but final structure might be affected if total count is wrong.",
                    expected_count,
                    received_count,
                )
                # Log snippets for debugging
                logger.debug(
                    "Mismatch Response Snippet (Start): %s", translated_combined[:200]
                )
                logger.debug(
                    "Mismatch Response Snippet (End): %s", translated_combined[-200:]
                )
            else:
                logger.debug("Chunk translation successful - segment counts match!")

            # Return the segments received, preserving original whitespace structure
            return translated_segments

        except APIError as e:
            logger.error(
                "Attempt %s: OpenAI API Error: %s - %s",
                attempt,
                e.status_code,
                e.message,
                exc_info=True,
            )
            # --- Retry ONLY for API Errors ---
            if attempt <= MAX_API_RETRIES:
                logger.warning(
                    "Retrying chunk after API error (Attempt %s)...", attempt + 1
                )
                time.sleep(1 + attempt)  # Exponential backoff slightly
                return self._translate_chunk(
                    text_chunk, source_language, target_language, attempt + 1
                )
            else:
                logger.error("API error after retries. Returning original chunk.")
                return text_chunk  # Fallback after retries
        except Exception as e:
            logger.error(
                "Attempt %s: Generic translation error: %s",
                attempt,
                str(e),
                exc_info=True,
            )
            return text_chunk  # Fallback on other errors

    # --- Title and Text Translation (remain the same) ---
    def translate_text(
        self, text: str, source_language: str, target_language: str
    ) -> str:
        # (Code remains the same as your previous version)
        if not self.client:
            raise Exception("OpenAI client not initialized.")
        if not text or text.isspace():
            return text
        try:
            logger.info(
                "Translating text from %s to %s: %s...",
                source_language,
                target_language,
                text[:50],
            )
            system_prompt = f"""Translate the following text from {source_language} to {target_language}. 
CRITICAL: Preserve the EXACT formatting and whitespace structure including:
- Leading and trailing spaces
- Spaces around punctuation and links
- Line breaks and spacing patterns
Preserve meaning and context while maintaining identical structure. Only return the translation."""
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": text},
                ],
                temperature=0.3,
                max_tokens=math.ceil(len(text) * 1.5) + 50,
                top_p=1.0,
            )
            translated_text = response.choices[0].message.content
            logger.info("Text translation successful: %s...", translated_text[:50])
            return translated_text
        except Exception as e:
            logger.error("Text translation error: %s", str(e), exc_info=True)
            raise Exception(f"Failed to translate text: {str(e)}")

    def translate_title(
        self, title: str, source_language: str, target_language: str
    ) -> str:
        # (Code remains the same as your previous version)
        if not self.client:
            raise Exception("OpenAI client not initialized.")
        if not title or title.isspace():
            return title
        try:
            logger.info(
                "Translating title from %s to %s: %s",
                source_language,
                target_language,
                title,
            )
            system_prompt = f"""Translate the following title from {source_language} to {target_language}. 
CRITICAL: Preserve any spacing and formatting exactly as in the original.
Be concise and preserve meaning. Use appropriate capitalization for the target language. Only return the translated title."""
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": title},
                ],
                temperature=0.3,
                max_tokens=256,
                top_p=1.0,
            )
            translated_title = response.choices[0].message.content
            logger.info("Title translation successful: %s", translated_title)
            return translated_title
        except Exception as e:
            logger.error("Title translation error: %s", str(e), exc_info=True)
            raise Exception(f"Failed to translate title: {str(e)}")

    # --- Method for HTML Translation using Chunking - FINAL COUNT CHECK ---
    def translate_html_content(
        self, html_content: str, source_language: str, target_language: str
    ) -> str:
        """
        Translates text content within HTML, performing a final count check for safety.
        """
        if not self.client:
            raise Exception("OpenAI client not initialized.")
        if not html_content:
            return ""

        logger.info(
            "Starting HTML translation from %s to %s...",
            source_language,
            target_language,
        )
        processed_html, placeholders = self._protect_elements(html_content)
        original_restored_html_fallback = self._restore_elements(
            processed_html, placeholders
        )  # Prepare fallback early

        try:
            soup = BeautifulSoup(processed_html, "lxml")
        except Exception as e:
            logger.error(
                "HTML parsing failed: %s. Returning original content.", e, exc_info=True
            )
            return original_restored_html_fallback  # Use prepared fallback

        texts_to_translate = []
        text_nodes_references = []
        ignore_tags_content = {"script", "style", "code", "pre"}

        for text_node in soup.find_all(string=True):
            if isinstance(text_node, (Comment)):
                continue  # Skip comments
            if isinstance(text_node, NavigableString):
                # Preserve original text including whitespace - don't strip
                original_text = str(text_node)
                stripped_text = original_text.strip()
                parent = text_node.parent

                # Include text nodes if they have content OR if they're whitespace between elements
                should_translate = False

                if (
                    stripped_text
                    and parent
                    and parent.name not in ignore_tags_content
                    and not stripped_text.startswith(self.placeholder_prefix)
                ):
                    # Has actual content to translate
                    should_translate = True
                elif (
                    original_text
                    and original_text.isspace()
                    and parent
                    and parent.name not in ignore_tags_content
                ):
                    # Whitespace-only node that might be important for spacing around links
                    # Check if it's between elements (has element siblings)
                    prev_sibling = text_node.previous_sibling
                    next_sibling = text_node.next_sibling
                    if (prev_sibling and hasattr(prev_sibling, "name")) or (
                        next_sibling and hasattr(next_sibling, "name")
                    ):
                        should_translate = True
                        logger.debug(
                            "Including whitespace-only segment: '%s' between elements",
                            original_text.replace(" ", "[SPACE]"),
                        )

                if should_translate:
                    # Use original text with whitespace, not stripped version
                    texts_to_translate.append(original_text)
                    text_nodes_references.append(text_node)

        total_nodes_to_translate = len(text_nodes_references)
        logger.debug(
            "Found %d text segments for translation.", total_nodes_to_translate
        )

        # Add debug logging to see what text was extracted
        if total_nodes_to_translate > 0:
            logger.debug(
                "Sample text segments to translate (showing spaces with [SPACE]):"
            )
            for i, text in enumerate(texts_to_translate[:5]):  # Log first 5 segments
                # Show spaces visually in logs
                visible_text = (
                    text.replace(" ", "[SPACE]")
                    .replace("\n", "[NEWLINE]")
                    .replace("\t", "[TAB]")
                )
                parent_tag = (
                    text_nodes_references[i].parent.name
                    if text_nodes_references[i].parent
                    else "None"
                )
                logger.debug(
                    "  Segment %s (parent: %s): '%s' (length: %s)",
                    i,
                    parent_tag,
                    visible_text,
                    len(text),
                )
                # Also show preceding and following siblings to understand context
                node = text_nodes_references[i]
                prev_sibling = node.previous_sibling
                next_sibling = node.next_sibling
                if prev_sibling:
                    prev_text = (
                        str(prev_sibling)[:50]
                        if hasattr(prev_sibling, "name")
                        else str(prev_sibling)[:50]
                    )
                    logger.debug("    Previous: %s", prev_text)
                if next_sibling:
                    next_text = (
                        str(next_sibling)[:50]
                        if hasattr(next_sibling, "name")
                        else str(next_sibling)[:50]
                    )
                    logger.debug("    Next: %s", next_text)

        if not texts_to_translate:
            logger.warning(
                "No translatable text found. HTML content might be all protected elements or empty."
            )
            # Return original content, but ensure placeholders are restored
            return original_restored_html_fallback

        # Chunking and Translation
        all_translated_segments = []
        current_chunk_texts = []
        current_chunk_chars = 0
        num_chunks = 0
        estimated_separator_len = 20  # Conservative estimate

        for i, text in enumerate(texts_to_translate):
            text_len = len(text)
            if current_chunk_texts and (
                current_chunk_chars
                + text_len
                + len(current_chunk_texts) * estimated_separator_len
                > TARGET_CHARS_PER_CHUNK
            ):
                num_chunks += 1
                logger.info(
                    "Translating chunk %d (%d segments, %d chars)...",
                    num_chunks,
                    len(current_chunk_texts),
                    current_chunk_chars,
                )
                # Call _translate_chunk, it handles API retries and returns list (translated or original)
                translated_chunk_segments = self._translate_chunk(
                    current_chunk_texts, source_language, target_language
                )
                all_translated_segments.extend(translated_chunk_segments)
                current_chunk_texts = [text]
                current_chunk_chars = text_len
            else:
                current_chunk_texts.append(text)
                current_chunk_chars += text_len

        if current_chunk_texts:
            num_chunks += 1
            logger.info(
                "Translating final chunk %d (%d segments, %d chars)...",
                num_chunks,
                len(current_chunk_texts),
                current_chunk_chars,
            )
            translated_chunk_segments = self._translate_chunk(
                current_chunk_texts, source_language, target_language
            )
            all_translated_segments.extend(translated_chunk_segments)
        # --- End Chunking ---

        # --- FINAL COUNT CHECK ---
        total_translated_segments = len(all_translated_segments)
        logger.info(
            "Translation summary: Expected %d segments, received %d segments",
            total_nodes_to_translate,
            total_translated_segments,
        )

        if total_translated_segments != total_nodes_to_translate:
            # Calculate the difference to decide whether to proceed or abort
            count_difference = abs(total_translated_segments - total_nodes_to_translate)
            difference_percentage = (
                (count_difference / total_nodes_to_translate) * 100
                if total_nodes_to_translate > 0
                else 100
            )

            logger.warning(
                "Segment count mismatch: Expected %d, received %d (difference: %d, %.1f%%)",
                total_nodes_to_translate,
                total_translated_segments,
                count_difference,
                difference_percentage,
            )

            # If the difference is small (less than 10% or less than 3 segments), proceed with available segments
            if difference_percentage <= 10 or count_difference <= 3:
                logger.warning(
                    "Difference is small (%.1f%%), proceeding with available segments. "
                    "Some text might not be translated properly.",
                    difference_percentage,
                )
                # Use the minimum count to avoid index errors
                segments_to_use = min(
                    total_translated_segments, total_nodes_to_translate
                )
                logger.info("Using %d segments for replacement", segments_to_use)
            else:
                logger.error(
                    "CRITICAL: Large segment count mismatch (%.1f%%). "
                    "Aborting replacement to prevent broken HTML. Returning original content.",
                    difference_percentage,
                )
                # Log some samples for debugging
                if total_translated_segments > 0:
                    logger.debug(
                        "Sample translated segments received (showing spaces with [SPACE]):"
                    )
                    for i, seg in enumerate(all_translated_segments[:3]):
                        visible_seg = (
                            seg.replace(" ", "[SPACE]")
                            .replace("\n", "[NEWLINE]")
                            .replace("\t", "[TAB]")
                        )
                        logger.debug(
                            "  Translated %s: '%s' (length: %s)",
                            i,
                            visible_seg,
                            len(seg),
                        )
                # Return the original, placeholder-restored HTML
                return original_restored_html_fallback
        else:
            segments_to_use = total_translated_segments

        # Counts match, proceed with replacement
        logger.info(
            "Total segment counts match (%d). Replacing text nodes...", segments_to_use
        )
        for i in range(segments_to_use):
            node = text_nodes_references[i]
            node.replace_with(NavigableString(all_translated_segments[i]))

        # Serialize and clean
        try:
            # Use html.parser for serialization, often better for standard HTML output
            # Avoid prettify if it causes issues, decode_contents might be sufficient
            # translated_html_structure = soup.prettify(formatter="html5")
            # Use decode_contents which gets the inner HTML of the parsed body/root
            translated_html_structure = soup.decode_contents(formatter="html")
        except Exception as serial_error:
            logger.error(
                "Error serializing translated HTML: %s. Trying basic string conversion.",
                serial_error,
            )
            try:
                translated_html_structure = str(soup)  # Fallback to basic string
            except Exception as str_error:
                logger.error(
                    "Basic string conversion also failed: %s. Returning original.",
                    str_error,
                )
                return original_restored_html_fallback

        final_html = self._restore_elements(translated_html_structure, placeholders)
        final_html = self._clean_translated_html(final_html)

        logger.info("HTML content translation finished successfully.")
        return final_html

    # --- Helper to clean HTML structure (Remains the same) ---
    def _clean_translated_html(self, html: str) -> str:
        """Removes potential extra tags added by parsers and fixes common issues."""
        # Remove <html>, <body>, <!DOCTYPE> tags potentially added by parsers
        content = re.sub(r"<\/?html[^>]*>", "", html, flags=re.IGNORECASE | re.DOTALL)
        content = re.sub(
            r"<\/?body[^>]*>", "", content, flags=re.IGNORECASE | re.DOTALL
        )
        content = re.sub(
            r"<!DOCTYPE[^>]*>", "", content, flags=re.IGNORECASE | re.DOTALL
        )

        # Fix <p> tags around block comments more robustly
        content = re.sub(
            r"<p>\s*(<!--\s*(?:/?wp:.*?)\s*-->)\s*</p>",
            r"\1",
            content,
            flags=re.DOTALL | re.IGNORECASE,
        )

        # Optional: Collapse excessive whitespace (be careful not to remove intentional breaks)
        # content = re.sub(r'\s{2,}', ' ', content)
        # content = re.sub(r'(\n\s*){2,}', '\n', content) # Consolidate multiple blank lines

        return content

    # --- Method to get language name (Remains the same) ---
    def get_language_name(self, language_code: str) -> str:
        # (Code remains the same as your previous version)
        language_map = {
            "tr": "Turkish",
            "vi": "Vietnamese",
            "es": "Spanish",
            "ru": "Russian",
            "ar": "Arabic",
            "fr": "French",
            "pt-br": "Portuguese (Brazil)",
            "de": "German",
            "pl": "Polish",
            "it": "Italian",
            "zh": "Chinese",
            "en": "English",
            "ja": "Japanese",
            "pt": "Portuguese",
        }
        return language_map.get(language_code.lower(), language_code)

    # --- NEW: Markdown Translation Method ---
    def translate_markdown_content(
        self, markdown_content: str, source_language: str, target_language: str
    ) -> str:
        """
        Translates Markdown content while attempting to preserve Markdown structure such as code fences,
        links, headings, lists, etc.  The implementation splits the Markdown into paragraph-like segments
        (double new-line separated) and leverages the existing ``_translate_chunk`` logic which already
        handles chunking, token limits, and OpenAI retries.  Segments that are detected to be inside
        triple-backtick code fences are NOT translated to avoid breaking code examples.
        """

        if not self.client:
            raise Exception("OpenAI client not initialized.")

        if not markdown_content or markdown_content.isspace():
            return markdown_content

        import re

        # Split the markdown into lines to detect fenced code blocks
        lines = markdown_content.splitlines(keepends=False)
        segments: List[str] = []
        buffer: List[str] = []
        in_code_block = False

        code_fence_pattern = re.compile(r"^\s*```")

        def push_buffer():
            if buffer:
                segments.append("\n".join(buffer))
                buffer.clear()

        for line in lines:
            if code_fence_pattern.match(line):
                # Toggle code block state – always keep the fence line with the code block segment
                push_buffer()
                in_code_block = not in_code_block
                segments.append(
                    line
                )  # store the fence line itself as its own segment (not translated)
            else:
                if in_code_block:
                    # Inside a code block – keep as-is, do not translate
                    segments.append(line)
                else:
                    # Accumulate normal markdown lines; flush on blank line to keep paragraphs separate
                    if line.strip() == "":
                        push_buffer()
                    buffer.append(line)

        # Push any remaining buffered lines
        push_buffer()

        # Now translate only the segments that should be translated (those that are not code fences/inside code)
        translatable_segments: List[str] = []
        translatable_indices: List[int] = []

        for idx, seg in enumerate(segments):
            # Skip segments that are code fences or inside code (they won't contain alphabetical chars)
            if code_fence_pattern.match(seg):
                continue  # fence lines themselves
            # If we are inside a code block, we skipped lines already; they are plain code lines – skip translation
            if seg.strip() and not seg.strip().startswith("```") and not in_code_block:
                translatable_segments.append(seg)
                translatable_indices.append(idx)

        # Chunk translation similar to HTML method
        translated_results: Dict[int, str] = {}

        if translatable_segments:
            current_chunk: List[str] = []
            current_indices: List[int] = []
            current_chars = 0
            estimated_separator_len = 20

            def translate_and_store(chunk: List[str], idxs: List[int]):
                if not chunk:
                    return
                result_segments = self._translate_chunk(
                    chunk, source_language, target_language
                )
                for original_idx, translated_text in zip(idxs, result_segments):
                    translated_results[original_idx] = translated_text

            for seg, idx in zip(translatable_segments, translatable_indices):
                seg_len = len(seg)
                if current_chunk and (
                    current_chars
                    + seg_len
                    + len(current_chunk) * estimated_separator_len
                    > TARGET_CHARS_PER_CHUNK
                ):
                    translate_and_store(current_chunk, current_indices)
                    current_chunk = [seg]
                    current_indices = [idx]
                    current_chars = seg_len
                else:
                    current_chunk.append(seg)
                    current_indices.append(idx)
                    current_chars += seg_len

            # Translate remaining
            translate_and_store(current_chunk, current_indices)

        # Reconstruct markdown from segments, replacing translated ones
        reconstructed_segments: List[str] = []
        for idx, seg in enumerate(segments):
            if idx in translated_results:
                reconstructed_segments.append(translated_results[idx])
            else:
                reconstructed_segments.append(seg)

        translated_markdown = "\n".join(reconstructed_segments)
        

        
        return translated_markdown
