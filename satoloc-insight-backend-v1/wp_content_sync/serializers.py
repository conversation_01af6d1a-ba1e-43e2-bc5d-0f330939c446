# wp_content_sync/serializers.py

from rest_framework import serializers
from .models import Synced<PERSON>onte<PERSON>, SyncLog, WordPressConnection
from django.utils.dateparse import parse_datetime
from django.conf import settings
import re
from typing import Dict, Any

class MarkdownToHtmlConverter:
    """
    Utility class to convert markdown to HTML, preserving structure
    and ensuring WordPress compatibility.
    """

    def convert(self, markdown: str) -> str:
        """
        Converts markdown to HTML.
        """
        if not markdown:
            return ""

        html = markdown

        # Headers
        html = re.sub(r'^# (.*?)$', r'<h1>\1</h1>', html, flags=re.MULTILINE)
        html = re.sub(r'^## (.*?)$', r'<h2>\1</h2>', html, flags=re.MULTILINE)
        html = re.sub(r'^### (.*?)$', r'<h3>\1</h3>', html, flags=re.MULTILINE)
        html = re.sub(r'^#### (.*?)$', r'<h4>\1</h4>', html, flags=re.MULTILINE)
        html = re.sub(r'^##### (.*?)$', r'<h5>\1</h5>', html, flags=re.MULTILINE)
        html = re.sub(r'^###### (.*?)$', r'<h6>\1</h6>', html, flags=re.MULTILINE)

        # Bold and Italic
        html = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', html)
        html = re.sub(r'\*(.*?)\*', r'<em>\1</em>', html)

        # Lists - process in sequence to avoid nesting issues
        # First, convert individual list items
        html = re.sub(r'^- (.*?)$', r'<li>\1</li>', html, flags=re.MULTILINE)
        html = re.sub(r'^\d+\. (.*?)$', r'<li>\1</li>', html, flags=re.MULTILINE)

        # Next, group list items
        # This is a simpler approach - for complex lists consider a markdown library
        html = re.sub(r'(<li>.*?</li>\s*)+', r'<ul>\g<0></ul>', html, flags=re.DOTALL)

        # Fix any double-wrapped <ul> tags
        html = re.sub(r'<ul>(\s*<ul>.*?</ul>\s*)</ul>', r'\1', html, flags=re.DOTALL)

        # Links
        html = re.sub(r'\[(.*?)\]\((.*?)\)', r'<a href="\2">\1</a>', html)

        # Images
        html = re.sub(r'!\[(.*?)\]\((.*?)\)', r'<img src="\2" alt="\1" />', html)

        # Code blocks - preserve WordPress code blocks
        def code_block_replacement(match):
            code = match.group(1).strip()
            return f'<pre><code>{code}</code></pre>'

        html = re.sub(r'```(?:\w+)?\s*(.*?)```', code_block_replacement, html, flags=re.DOTALL)

        # Inline code
        html = re.sub(r'`(.*?)`', r'<code>\1</code>', html)

        # Paragraphs - wrap remaining non-wrapped text
        # First, split the HTML into blocks (handling existing tags)
        blocks = re.split(r'\n\s*\n', html)
        wrapped_blocks = []

        for block in blocks:
            # Skip if the entire block is already wrapped in a tag
            if block.strip() and not re.match(r'^\s*<\w+[^>]*>.*</\w+>\s*$', block, re.DOTALL):
                # Skip if this is a list or header
                if not re.match(r'^\s*<(ul|ol|li|h\d)', block.strip()):
                    # Wrap in paragraph
                    wrapped_blocks.append(f'<p>{block.strip()}</p>')
                else:
                    wrapped_blocks.append(block)
            else:
                wrapped_blocks.append(block)

        html = '\n\n'.join(wrapped_blocks)

        # Clean up: remove empty paragraphs
        html = re.sub(r'<p>\s*</p>', '', html)

        # Ensure WordPress compatibility by preserving <!-- wp: --> tags
        # if they were in the markdown

        return html.strip()

class HtmlToMarkdownConverter:
    """
    Utility class to convert HTML to Markdown, preserving structure
    for better translation compatibility.
    """

    def convert(self, html: str) -> str:
        """
        Converts HTML to Markdown.
        """
        if not html:
            return ""

        markdown = html

        # Headers
        markdown = re.sub(r'<h1[^>]*>(.*?)</h1>', r'# \1\n\n', markdown, flags=re.IGNORECASE)
        markdown = re.sub(r'<h2[^>]*>(.*?)</h2>', r'## \1\n\n', markdown, flags=re.IGNORECASE)
        markdown = re.sub(r'<h3[^>]*>(.*?)</h3>', r'### \1\n\n', markdown, flags=re.IGNORECASE)
        markdown = re.sub(r'<h4[^>]*>(.*?)</h4>', r'#### \1\n\n', markdown, flags=re.IGNORECASE)
        markdown = re.sub(r'<h5[^>]*>(.*?)</h5>', r'##### \1\n\n', markdown, flags=re.IGNORECASE)
        markdown = re.sub(r'<h6[^>]*>(.*?)</h6>', r'###### \1\n\n', markdown, flags=re.IGNORECASE)

        # Paragraphs
        markdown = re.sub(r'<p[^>]*>(.*?)</p>', r'\1\n\n', markdown, flags=re.IGNORECASE|re.DOTALL)

        # Bold and Italic
        markdown = re.sub(r'<(?:b|strong)[^>]*>(.*?)</(?:b|strong)>', r'**\1**', markdown, flags=re.IGNORECASE)
        markdown = re.sub(r'<(?:i|em)[^>]*>(.*?)</(?:i|em)>', r'*\1*', markdown, flags=re.IGNORECASE)

        # Lists
        def process_ul(match):
            content = match.group(1)
            # Replace each <li> with "- " prefix
            content = re.sub(r'<li[^>]*>(.*?)</li>', r'- \1\n', content, flags=re.DOTALL|re.IGNORECASE)
            return content

        def process_ol(match):
            content = match.group(1)
            # Find all <li> elements
            items = re.findall(r'<li[^>]*>(.*?)</li>', content, flags=re.DOTALL|re.IGNORECASE)
            # Replace each with numbered prefix
            result = ""
            for i, item in enumerate(items, 1):
                result += f"{i}. {item}\n"
            return result

        # Process lists - handle nested lists with care
        markdown = re.sub(r'<ul[^>]*>(.*?)</ul>', process_ul, markdown, flags=re.DOTALL|re.IGNORECASE)
        markdown = re.sub(r'<ol[^>]*>(.*?)</ol>', process_ol, markdown, flags=re.DOTALL|re.IGNORECASE)

        # Links
        markdown = re.sub(r'<a[^>]*href="([^"]*)"[^>]*>(.*?)</a>', r'[\2](\1)', markdown, flags=re.IGNORECASE)

        # Images
        markdown = re.sub(r'<img[^>]*src="([^"]*)"[^>]*alt="([^"]*)"[^>]*/?>', r'![\2](\1)', markdown, flags=re.IGNORECASE)
        markdown = re.sub(r'<img[^>]*alt="([^"]*)"[^>]*src="([^"]*)"[^>]*/?>', r'![\1](\2)', markdown, flags=re.IGNORECASE)
        markdown = re.sub(r'<img[^>]*src="([^"]*)"[^>]*/?>', r'![](\1)', markdown, flags=re.IGNORECASE)

        # Code blocks
        def code_block_replacement(match):
            code = match.group(1).strip()
            return f'```\n{code}\n```'

        markdown = re.sub(r'<pre><code>(.*?)</code></pre>', code_block_replacement, markdown, flags=re.DOTALL|re.IGNORECASE)

        # Inline code
        markdown = re.sub(r'<code>(.*?)</code>', r'`\1`', markdown, flags=re.IGNORECASE)

        # Line breaks
        markdown = re.sub(r'<br\s*/?>', r'\n', markdown, flags=re.IGNORECASE)

        # Protect WordPress block comments (important for compatibility)
        def protect_wp_comment(match):
            comment = match.group(0)
            # Keep WordPress comments as-is
            return comment

        markdown = re.sub(r'<!--\s*wp:.*?-->', protect_wp_comment, markdown)

        # Remove remaining HTML tags, but keep their content
        markdown = re.sub(r'<[^>]*>', '', markdown)

        # Fix excessive whitespace
        markdown = re.sub(r'\n\s*\n\s*\n', r'\n\n', markdown)

        return markdown.strip()

class SyncedContentSerializer(serializers.ModelSerializer):
    """
    Serializer for the SyncedContent model. Includes the source WP site URL.
    """

    markdown_content = serializers.SerializerMethodField(read_only=True)
    # --- NEW: Make categories readable ---
    categories = serializers.JSONField(read_only=True)

    class Meta:
        model = SyncedContent
        fields = [
            'id', 'wp_site_url', 'wp_id', 'title', 'content', 'markdown_content',
            'post_type', 'status', 'date_created_gmt', 'date_modified_gmt',
            'wp_link', 'featured_image_url', 'created_at', 'updated_at',
            'last_synced_at', 'language_code', 'language_name', 'categories' # <-- Added 'categories'
        ]
        read_only_fields = ['created_at', 'updated_at', 'last_synced_at', 'categories'] # <-- Added 'categories' to read_only

    def validate_status(self, value):
        """Ensure the status is one of the allowed choices."""
        allowed_statuses = [choice[0] for choice in SyncedContent.STATUS_CHOICES]
        if value not in allowed_statuses:
            raise serializers.ValidationError(
                f"Invalid status '{value}'. Must be one of: {', '.join(allowed_statuses)}"
            )
        return value

    def get_markdown_content(self, obj):
        """Convert HTML content to markdown for better editing and translation"""
        if obj.content:
            converter = HtmlToMarkdownConverter()
            return converter.convert(obj.content)
        return ""

# --- Serializer for Translation Request ---
class TranslationRequestSerializer(serializers.Serializer):
    target_language_code = serializers.CharField(max_length=10)
    apiKey = serializers.CharField()
    translated_title = serializers.CharField(required=False)
    translated_content = serializers.CharField(required=False)
    translated_markdown = serializers.CharField(required=False)
    is_markdown = serializers.BooleanField(required=False, default=False)
    source_markdown = serializers.CharField(required=False)
    
    # --- NEW ---
    category_ids = serializers.ListField(
        child=serializers.IntegerField(min_value=1), # Ensure IDs are positive integers
        required=False, # Make it optional for now
        allow_empty=True,
        help_text="List of category IDs to assign to the new translation."
    )

    def validate_target_language_code(self, value):
        # Add basic validation if needed, e.g., check against a list of supported codes
        # For now, just ensure it's not empty
        if not value:
            raise serializers.ValidationError("Target language code cannot be empty.")
        return value.lower() # Normalize to lowercase

    def validate(self, data):
        """
        Custom validation to ensure that either translated_content or translated_markdown
        is provided when using is_markdown=True.
        """
        is_markdown = data.get('is_markdown', False)

        if is_markdown:
            if 'translated_content' not in data and 'translated_markdown' not in data:
                raise serializers.ValidationError("Either translated_content or translated_markdown must be provided when is_markdown is True")

        return data

# --- Serializer for Translation Preview Request ---
class TranslationPreviewRequestSerializer(serializers.Serializer):
    target_language_code = serializers.CharField(max_length=10)
    translated_title = serializers.CharField(required=False)
    translated_content = serializers.CharField(required=False)

    def validate_target_language_code(self, value):
        if not value:
            raise serializers.ValidationError("Target language code cannot be empty.")
        return value.lower()

# --- NEW: Serializer for Markdown Translation Request ---
class MarkdownTranslationRequestSerializer(serializers.Serializer):
    target_language_code = serializers.CharField(max_length=10)
    markdown_content = serializers.CharField(required=True)

    def validate_target_language_code(self, value):
        if not value:
            raise serializers.ValidationError("Target language code cannot be empty.")
        return value.lower()

class SyncLogSerializer(serializers.ModelSerializer):
    """Serializer for the SyncLog model."""
    operation_display = serializers.CharField(source='get_operation_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    username = serializers.SerializerMethodField(read_only=True)
    content_item_title = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = SyncLog
        fields = [
            'id', 'wp_site_url', 'operation', 'operation_display',
            'status', 'status_display', 'details', 'content_item',
            'content_item_title', 'user', 'username', 'created_at'
        ]
        read_only_fields = ['created_at']

    def get_username(self, obj):
        """Get the username of the user who performed the operation."""
        if obj.user:
            return obj.user.username
        return None

    def get_content_item_title(self, obj):
        """Get the title of the content item if available."""
        if obj.content_item:
            return obj.content_item.title
        return None

class WordPressConnectionSerializer(serializers.ModelSerializer):
    """Serializer for the WordPressConnection model."""
    username = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = WordPressConnection
        fields = [
            'id', 'site_url', 'user', 'username', 'api_key',
            'is_active', 'last_sync', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'last_sync', 'user', 'username']
        extra_kwargs = {
            'api_key': {'write_only': True},  # Don't expose API key in responses
            'user': {'required': False},      # User will be set automatically in perform_create
        }

    def get_username(self, obj):
        """Get the username of the user who owns this connection."""
        if obj.user:
            return obj.user.username
        return None

    def validate_site_url(self, value):
        """Normalize the site URL."""
        if value:
            # Remove trailing slashes for consistency
            return value.rstrip('/')
        return value

    def to_representation(self, instance):
        """Override to add debugging info in dev."""
        data = super().to_representation(instance)
        if settings.DEBUG:
            data['_debug'] = {
                'user_id': instance.user.id if instance.user else None,
                'username': instance.user.username if instance.user else None,
            }
        return data