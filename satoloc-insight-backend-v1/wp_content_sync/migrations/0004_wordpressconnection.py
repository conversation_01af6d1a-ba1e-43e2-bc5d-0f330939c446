# Generated by Django 4.2.16 on 2025-04-08 11:32

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('wp_content_sync', '0003_synclog'),
    ]

    operations = [
        migrations.CreateModel(
            name='WordPressConnection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('site_url', models.URLField(db_index=True, help_text='The base URL of the WordPress site for content synchronization.', max_length=255, validators=[django.core.validators.URLValidator()], verbose_name='WordPress Site URL')),
                ('api_key', models.CharField(help_text="For WordPress Application Passwords, use format 'username:password'. For API keys, enter the key directly.", max_length=255, verbose_name='API Key')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this connection is currently active for synchronization.', verbose_name='Is Active')),
                ('last_sync', models.DateTimeField(blank=True, help_text='Timestamp of the last successful synchronization with this WordPress site.', null=True, verbose_name='Last Sync')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When this connection was created.', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='When this connection was last updated.', verbose_name='Updated At')),
                ('user', models.ForeignKey(help_text='User who owns this WordPress connection.', on_delete=django.db.models.deletion.CASCADE, related_name='wp_sync_connections', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'WordPress Connection',
                'verbose_name_plural': 'WordPress Connections',
                'ordering': ['-created_at'],
                'unique_together': {('user', 'site_url')},
            },
        ),
    ]
