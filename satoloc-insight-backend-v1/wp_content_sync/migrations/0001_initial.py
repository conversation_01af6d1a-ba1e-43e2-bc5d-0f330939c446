# Generated by Django 4.2.20 on 2025-03-30 13:32

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SyncedContent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('wp_site_url', models.URLField(db_index=True, help_text='The base URL of the WordPress site this content belongs to.', max_length=255, verbose_name='Source WordPress Site URL')),
                ('wp_id', models.PositiveIntegerField(db_index=True, help_text='The original ID of the content item in WordPress.', verbose_name='WordPress ID')),
                ('title', models.CharField(max_length=255, verbose_name='Title')),
                ('content', models.TextField(blank=True, verbose_name='Content (HTML)')),
                ('post_type', models.Char<PERSON>ield(choices=[('post', 'Post'), ('page', 'Page')], max_length=50, verbose_name='Post Type')),
                ('status', models.<PERSON>r<PERSON>ield(choices=[('publish', 'Published'), ('draft', 'Draft'), ('pending', 'Pending Review'), ('private', 'Private'), ('future', 'Scheduled')], max_length=20, verbose_name='Status')),
                ('date_created_gmt', models.DateTimeField(blank=True, help_text='Original creation date from WordPress (in GMT).', null=True, verbose_name='WP Created Date (GMT)')),
                ('date_modified_gmt', models.DateTimeField(blank=True, help_text='Last modification date from WordPress (in GMT).', null=True, verbose_name='WP Modified Date (GMT)')),
                ('wp_link', models.URLField(blank=True, help_text='The permalink URL on the WordPress site.', max_length=1024, null=True, verbose_name='WordPress Permalink')),
                ('featured_image_url', models.URLField(blank=True, help_text='URL of the full-sized featured image from WordPress.', max_length=1024, null=True, verbose_name='Featured Image URL')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Timestamp when this record was created locally.', verbose_name='Locally Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Timestamp when this record was last updated locally.', verbose_name='Locally Updated At')),
                ('last_synced_at', models.DateTimeField(blank=True, help_text='Timestamp of the last successful sync operation from WordPress for this item.', null=True, verbose_name='Last Synced At')),
            ],
            options={
                'verbose_name': 'Synced Content Item',
                'verbose_name_plural': 'Synced Content Items',
                'ordering': ['wp_site_url', '-date_modified_gmt', '-updated_at'],
                'indexes': [models.Index(fields=['post_type'], name='wp_content__post_ty_55c56f_idx'), models.Index(fields=['status'], name='wp_content__status_ce3190_idx'), models.Index(fields=['date_modified_gmt'], name='wp_content__date_mo_60154c_idx')],
            },
        ),
        migrations.AddConstraint(
            model_name='syncedcontent',
            constraint=models.UniqueConstraint(fields=('wp_site_url', 'wp_id'), name='unique_wp_content_per_site'),
        ),
    ]
