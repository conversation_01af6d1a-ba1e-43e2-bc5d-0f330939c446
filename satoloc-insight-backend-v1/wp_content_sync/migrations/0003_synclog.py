# Generated by Django 4.2.16 on 2025-04-08 11:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('wp_content_sync', '0002_syncedcontent_language_code_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SyncLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('wp_site_url', models.URLField(db_index=True, help_text='The base URL of the WordPress site this operation was performed on.', max_length=255, verbose_name='WordPress Site URL')),
                ('operation', models.CharField(choices=[('fetch_all', 'Fetch All Content'), ('push_to_wp', 'Push to WordPress'), ('delete_from_wp', 'Delete from WordPress'), ('delete_local_only', 'Delete Locally Only'), ('translate_preview', 'Translation Preview'), ('translate_and_create', 'Translate and Create')], db_index=True, help_text='The type of operation performed.', max_length=50, verbose_name='Operation Type')),
                ('status', models.CharField(choices=[('success', 'Success'), ('failed', 'Failed')], db_index=True, help_text='Whether the operation was successful or failed.', max_length=20, verbose_name='Operation Status')),
                ('details', models.JSONField(blank=True, default=dict, help_text='Additional details about the operation, such as error messages or success metrics.', null=True, verbose_name='Operation Details')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Timestamp of when the operation was performed.', verbose_name='Created At')),
                ('content_item', models.ForeignKey(blank=True, help_text='The content item this operation was performed on (if applicable).', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sync_logs', to='wp_content_sync.syncedcontent', verbose_name='Related Content')),
                ('user', models.ForeignKey(blank=True, help_text='The user who initiated the operation.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='wp_sync_logs', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Sync Log',
                'verbose_name_plural': 'Sync Logs',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['wp_site_url', 'operation'], name='wp_content__wp_site_339706_idx'), models.Index(fields=['status'], name='wp_content__status_dfc6ad_idx'), models.Index(fields=['created_at'], name='wp_content__created_90de7b_idx')],
            },
        ),
    ]
