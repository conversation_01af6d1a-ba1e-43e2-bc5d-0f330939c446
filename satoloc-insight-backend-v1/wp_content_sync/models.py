# wp_content_sync/models.py

from django.db import models # type: ignore
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.conf import settings
from django.core.validators import URLValidator

class WordPressConnection(models.Model):
    """
    Model to store WordPress site connection details for content synchronization.
    """
    site_url = models.URLField(
        max_length=255,
        validators=[URLValidator()],
        db_index=True,
        verbose_name="WordPress Site URL",
        help_text="The base URL of the WordPress site for content synchronization."
    )

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='wp_sync_connections',
        verbose_name="User",
        help_text="User who owns this WordPress connection."
    )

    api_key = models.CharField(
        max_length=255,
        verbose_name="API Key",
        help_text="For WordPress Application Passwords, use format 'username:password'. For API keys, enter the key directly."
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name="Is Active",
        help_text="Whether this connection is currently active for synchronization."
    )

    last_sync = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="Last Sync",
        help_text="Timestamp of the last successful synchronization with this WordPress site."
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Created At",
        help_text="When this connection was created."
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name="Updated At",
        help_text="When this connection was last updated."
    )

    class Meta:
        verbose_name = "WordPress Connection"
        verbose_name_plural = "WordPress Connections"
        unique_together = ['user', 'site_url']
        ordering = ['-created_at']

    def __str__(self):
        site_display = self.site_url.replace('http://','').replace('https://','').rstrip('/')
        return f"{site_display} - {self.user.username}"

    def clean(self):
        # Normalize the URL by removing trailing slash
        self.site_url = self.site_url.rstrip('/')
        super().clean()

class SyncedContent(models.Model):
    """
    Represents a piece of content (post, page) synced from a specific WordPress site.
    """
    POST_TYPE_CHOICES = [
        ('post', 'Post'),
        ('page', 'Page'),
    ]
    STATUS_CHOICES = [
        ('publish', 'Published'),
        ('draft', 'Draft'),
        ('pending', 'Pending Review'),
        ('private', 'Private'),
        ('future', 'Scheduled'),
    ]

    # NEW FIELD: Store the source site URL
    wp_site_url = models.URLField(
        max_length=255,
        db_index=True, # Index for efficient filtering by site
        verbose_name="Source WordPress Site URL",
        help_text="The base URL of the WordPress site this content belongs to."
    )

    wp_id = models.PositiveIntegerField(
        # Removed unique=True here, uniqueness is now per site
        db_index=True,
        verbose_name="WordPress ID",
        help_text="The original ID of the content item in WordPress."
    )
    title = models.CharField(
        max_length=255,
        verbose_name="Title"
    )
    content = models.TextField(
        blank=True,
        verbose_name="Content (HTML)"
    )
    post_type = models.CharField(
        max_length=50,
        choices=POST_TYPE_CHOICES,
        verbose_name="Post Type"
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        verbose_name="Status"
    )
    date_created_gmt = models.DateTimeField(
        null=True, blank=True,
        verbose_name="WP Created Date (GMT)",
        help_text="Original creation date from WordPress (in GMT)."
    )
    date_modified_gmt = models.DateTimeField(
        null=True, blank=True,
        verbose_name="WP Modified Date (GMT)",
        help_text="Last modification date from WordPress (in GMT)."
    )
    wp_link = models.URLField(
        max_length=1024, blank=True, null=True,
        verbose_name="WordPress Permalink",
        help_text="The permalink URL on the WordPress site."
    )
    featured_image_url = models.URLField(
        max_length=1024, blank=True, null=True,
        verbose_name="Featured Image URL",
        help_text="URL of the full-sized featured image from WordPress."
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Locally Created At",
        help_text="Timestamp when this record was created locally."
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name="Locally Updated At",
        help_text="Timestamp when this record was last updated locally."
    )
    last_synced_at = models.DateTimeField(
        null=True, blank=True,
        verbose_name="Last Synced At",
        help_text="Timestamp of the last successful sync operation from WordPress for this item."
    )

    # Language fields
    language_code = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name="Language Code",
        help_text="The ISO language code (e.g., 'en', 'fr', 'es')."
    )
    language_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name="Language Name",
        help_text="The human-readable language name (e.g., 'English', 'French', 'Spanish')."
    )

    # --- NEW: Field to store categories ---
    categories = models.JSONField(
        null=True,
        blank=True,
        default=list, # Default to an empty list
        verbose_name="Categories",
        help_text="List of categories associated with the content (ID, name, slug)."
    )

    def __str__(self):
        # Include site URL in string representation
        site_display = self.wp_site_url.replace('http://','').replace('https://','').rstrip('/')
        return f"{self.title} (WP ID: {self.wp_id} @ {site_display})"

    def clean(self):
        if self.wp_id is not None and self.wp_id <= 0:
            raise ValidationError({'wp_id': 'WordPress ID must be a positive integer.'})
        if not self.wp_site_url:
            raise ValidationError({'wp_site_url': 'Source WordPress Site URL cannot be empty.'})
        # Normalize URL? e.g., remove trailing slash
        self.wp_site_url = self.wp_site_url.rstrip('/')
        super().clean()

    class Meta:
        ordering = ['wp_site_url', '-date_modified_gmt', '-updated_at']
        verbose_name = "Synced Content Item"
        verbose_name_plural = "Synced Content Items"
        # Ensure wp_id is unique *per site*
        constraints = [
            models.UniqueConstraint(fields=['wp_site_url', 'wp_id'], name='unique_wp_content_per_site')
        ]
        indexes = [
            models.Index(fields=['post_type']),
            models.Index(fields=['status']),
            models.Index(fields=['date_modified_gmt']),
            # Index on wp_site_url already added via db_index=True
        ]

class SyncLog(models.Model):
    """
    Model to store logs of sync operations between WordPress and the application.
    Tracks operations like content fetching, translation pushing, and more.
    """
    OPERATION_CHOICES = [
        ('fetch_all', 'Fetch All Content'),
        ('push_to_wp', 'Push to WordPress'),
        ('delete_from_wp', 'Delete from WordPress'),
        ('delete_local_only', 'Delete Locally Only'),
        ('translate_preview', 'Translation Preview'),
        ('translate_and_create', 'Translate and Create'),
    ]

    STATUS_CHOICES = [
        ('success', 'Success'),
        ('failed', 'Failed'),
    ]

    # The WordPress site URL this operation was performed on
    wp_site_url = models.URLField(
        max_length=255,
        db_index=True,
        verbose_name="WordPress Site URL",
        help_text="The base URL of the WordPress site this operation was performed on."
    )

    # The type of operation performed
    operation = models.CharField(
        max_length=50,
        choices=OPERATION_CHOICES,
        db_index=True,
        verbose_name="Operation Type",
        help_text="The type of operation performed."
    )

    # The status of the operation (success/failed)
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        db_index=True,
        verbose_name="Operation Status",
        help_text="Whether the operation was successful or failed."
    )

    # Details about the operation (JSON serializable)
    details = models.JSONField(
        default=dict,
        blank=True,
        null=True,
        verbose_name="Operation Details",
        help_text="Additional details about the operation, such as error messages or success metrics."
    )

    # The content item this operation was performed on (if applicable)
    content_item = models.ForeignKey(
        SyncedContent,
        on_delete=models.SET_NULL,
        related_name="sync_logs",
        blank=True,
        null=True,
        verbose_name="Related Content",
        help_text="The content item this operation was performed on (if applicable)."
    )

    # The user who initiated the operation
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name="wp_sync_logs",
        blank=True,
        null=True,
        verbose_name="User",
        help_text="The user who initiated the operation."
    )

    # Timestamp of when the operation was performed
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Created At",
        help_text="Timestamp of when the operation was performed."
    )

    class Meta:
        verbose_name = "Sync Log"
        verbose_name_plural = "Sync Logs"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['wp_site_url', 'operation']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        """String representation of the SyncLog."""
        site_display = self.wp_site_url.replace('http://','').replace('https://','').rstrip('/')
        status_display = "✓" if self.status == "success" else "✗"
        return f"{self.get_operation_display()} on {site_display} - {status_display} ({self.created_at})"

    @classmethod
    def log_operation(cls, wp_site_url, operation, status, details=None, content_item=None, user=None):
        """
        Convenience method to create a new sync log entry.

        Args:
            wp_site_url (str): The WordPress site URL
            operation (str): The operation type (use values from OPERATION_CHOICES)
            status (str): The status ('success' or 'failed')
            details (dict, optional): Additional details about the operation
            content_item (SyncedContent, optional): The related content item
            user (User, optional): The user who initiated the operation

        Returns:
            SyncLog: The created log entry
        """
        return cls.objects.create(
            wp_site_url=wp_site_url,
            operation=operation,
            status=status,
            details=details or {},
            content_item=content_item,
            user=user
        )