import requests
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured
import logging
from urllib.parse import urljoin

logger = logging.getLogger(__name__)

# Default plugin constants
WP_API_NAMESPACE = 'wp-content-sync/v1'
WP_API_KEY_HEADER = 'X-WP-Content-Sync-Key'

# Alternative plugin namespaces to check if the default one fails
ALTERNATIVE_PLUGIN_CONFIGS = [
    {
        'namespace': 'satoloc-insight-connector/v1',
        'header': 'X-Satoloc-Insight-Key'
    },
    # Add other known plugin configurations here
]

class WordPressAPIClientError(Exception):
    """Custom exception for WordPress API client errors."""
    def __init__(self, message, status_code=None, response_data=None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data

class WordPressAPIClient:
    """
    Client for interacting with the custom WP Content Sync Plugin API.
    Requires base_url and api_key for the target site upon initialization.
    """
    def __init__(self, base_url: str, api_key: str):
        if not base_url or not api_key:
            raise ValueError("base_url and api_key are required to initialize WordPressAPIClient.")

        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        
        # Start with default namespace and header
        self.namespace = WP_API_NAMESPACE
        self.api_key_header = WP_API_KEY_HEADER
        
        try:
            # Use timeout from settings if available, otherwise default
            self.timeout = settings.WP_REQUEST_TIMEOUT
        except AttributeError:
            self.timeout = 30  # Default timeout

        # Ensure base URL includes the /wp-json part correctly
        if '/wp-json' not in self.base_url:
            # Append /wp-json/ if it's likely a site root URL
            self.api_base = urljoin(self.base_url + '/', 'wp-json/')
        else:
            # Handle cases where it might already end with /wp-json or /wp-json/
            # Take the part before /wp-json and append /wp-json/
            self.api_base = self.base_url.split('/wp-json')[0].rstrip('/') + '/wp-json/'

        # Construct the full base URL for the namespace
        self.full_api_url_base = urljoin(self.api_base, self.namespace.strip('/') + '/')
        logger.debug("WordPressAPIClient initialized for %s with API base %s and header %s", 
                     self.base_url, self.full_api_url_base, self.api_key_header)
        
        # Try to discover the correct API namespace on initialization
        # This proactively checks if we need a different namespace
        try:
            # Make a test request to verify if the default namespace works
            test_url = urljoin(self.full_api_url_base, 'content')
            logger.info("Testing if default namespace works: %s", test_url)
            test_response = requests.head(
                url=test_url,
                headers=self._get_headers(),
                timeout=self.timeout
            )
            
            # If we get a 404, try to discover the correct namespace
            if test_response.status_code == 404:
                logger.info("Default namespace returned 404, trying to discover correct namespace")
                self._discover_api_namespace()
        except Exception as e:
            # Don't fail initialization if discovery fails
            logger.warning("Exception during API namespace discovery: %s", str(e))

    def _discover_api_namespace(self):
        """Attempt to discover the correct API namespace by testing alternatives"""
        logger.info("Default namespace failed, attempting to discover the correct API namespace")
        
        for config in ALTERNATIVE_PLUGIN_CONFIGS:
            test_namespace = config['namespace']
            test_header = config['header']
            
            # Build a test URL with this namespace - use content endpoint directly
            # since some plugins might not implement a ping endpoint
            test_url = urljoin(self.api_base, test_namespace.strip('/') + '/content')
            
            try:
                logger.info("Testing alternative namespace: %s with URL: %s", test_namespace, test_url)
                # Use HEAD request to check if endpoint exists without downloading data
                response = requests.head(
                    url=test_url,
                    headers={test_header: self.api_key},
                    timeout=self.timeout
                )
                
                # Accept 200 OK, 401 Unauthorized, or 403 Forbidden as positive signals
                # (These indicate the endpoint exists but might need auth)
                if response.status_code in [200, 401, 403]:
                    logger.info("Found working namespace: %s (status: %s)", test_namespace, response.status_code)
                    # Update the client configuration with the working namespace
                    self.namespace = test_namespace
                    self.api_key_header = test_header
                    self.full_api_url_base = urljoin(self.api_base, self.namespace.strip('/') + '/')
                    return True
            except Exception as e:
                logger.debug("Testing namespace %s failed: %s", test_namespace, str(e))
                continue
                
        logger.warning("All alternative namespaces failed, keeping default")
        return False

    def _get_headers(self):
        """Returns the necessary headers for API requests."""
        headers = {
            self.api_key_header: self.api_key,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        }
        # Add detailed logging to debug the exact headers being sent
        logger.info("Request headers for %s: %s", self.base_url, headers)
        return headers

    def _get_url(self, endpoint=''):
        """Constructs the full URL for a given API endpoint relative to the namespace."""
        # Ensure endpoint doesn't have leading slash if already present in base
        return urljoin(self.full_api_url_base, endpoint.lstrip('/'))

    def _make_request(self, method, endpoint, params=None, json_data=None, retry_with_alternatives=True):
        """Makes an HTTP request to the target WordPress API."""
        url = self._get_url(endpoint)
        headers = self._get_headers()

        # Add more detailed logging for debugging
        logger.info("Making WP API request: %s %s", method, url)
        logger.info("Headers: %s", headers)
        if params:
            logger.info("Params: %s", params)
        if json_data:
            logger.info("Data: %s", json_data)

        try:
            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                params=params,
                json=json_data,
                timeout=self.timeout
            )

            logger.info("WP API Response Status: %s", response.status_code)
            logger.debug("WP API Response Headers: %s", response.headers)
            
            # For error responses, log the response body to help with debugging
            if response.status_code >= 400:
                try:
                    error_body = response.json()
                    logger.error("Error response body: %s", error_body)
                except:
                    logger.error("Error response text: %s", response.text)
            
                # If 404 and this is the first attempt, try alternative namespaces
                if response.status_code == 404 and retry_with_alternatives:
                    if self._discover_api_namespace():
                        # Retry the request with the new namespace
                        logger.info("Retrying request with discovered namespace")
                        return self._make_request(method, endpoint, params, json_data, retry_with_alternatives=False)
            
            response.raise_for_status()  # Check for HTTP errors (4xx or 5xx)

            if response.status_code == 204:  # Handle No Content success
                return None

            return response.json()

        except requests.exceptions.Timeout:
            logger.error("WP API request timeout (%ss) for %s %s @ %s", self.timeout, method, url, self.base_url)
            raise WordPressAPIClientError(f"Request timed out after {self.timeout} seconds.", status_code=408)
        except requests.exceptions.ConnectionError as e:
            logger.error("WP API connection error for %s %s @ %s: %s", method, url, self.base_url, e)
            raise WordPressAPIClientError(f"Could not connect to WordPress API at {self.base_url}: {e}", status_code=503)
        except requests.exceptions.HTTPError as e:
            status_code = e.response.status_code
            response_data = None
            error_message = f"HTTP Error {status_code}"
            try:
                response_data = e.response.json()
                wp_error_message = response_data.get('message', str(e))
                wp_error_code = response_data.get('code', 'unknown_error')
                error_message = f"HTTP Error {status_code} ({wp_error_code}): {wp_error_message}"
            except requests.exceptions.JSONDecodeError:
                error_message = f"HTTP Error {status_code}: {e.response.text}"

            # If 404 and this is the first attempt, try alternative namespaces
            if status_code == 404 and retry_with_alternatives:
                if self._discover_api_namespace():
                    # Retry the request with the new namespace
                    logger.info("Retrying request with discovered namespace after 404")
                    return self._make_request(method, endpoint, params, json_data, retry_with_alternatives=False)

            logger.error("WP API HTTP error for %s %s @ %s: %s", method, url, self.base_url, error_message, exc_info=False)
            raise WordPressAPIClientError(error_message, status_code=status_code, response_data=response_data) from e
        except requests.exceptions.RequestException as e:
            logger.error("WP API request error for %s %s @ %s: %s", method, url, self.base_url, e, exc_info=True)
            raise WordPressAPIClientError(f"An unexpected error occurred during the API request to {self.base_url}: {e}") from e
        except Exception as e:
            logger.error("Unexpected error during WP API request processing for %s %s @ %s: %s", method, url, self.base_url, e, exc_info=True)
            raise WordPressAPIClientError(f"An unexpected error occurred: {e}") from e

    # --- Public API Methods ---
    # These methods now operate on the specific site configured during instantiation.

    def get_all_content_items(self, initial_params=None):
        """Fetches ALL content items from the configured WP site by handling pagination."""
        all_items = []
        page = 1
        params = {
            'post_type': 'post,page',
            'post_status': 'publish,draft,pending,future,private',
            'per_page': 100, # Max per page
            'page': page,
        }
        if initial_params:
            params.update(initial_params)

        logger.info("Fetching all content items from %s, starting with params: %s", self.base_url, params)

        # Use a generic 'content' endpoint
        endpoint = 'content'

        while True:
            try:
                # Use internal _make_request which now uses instance URL/key
                response_headers = {} # Need headers for pagination info
                request_url = self._get_url(endpoint)

                # Need raw response to get headers, so can't use _make_request directly here
                raw_response = requests.request(
                    method='GET',
                    url=request_url,
                    headers=self._get_headers(),
                    params=params,
                    timeout=self.timeout
                )
                
                # Auto-discover if we get a 404
                if raw_response.status_code == 404:
                    if self._discover_api_namespace():
                        # Retry with the new namespace
                        logger.info("Retrying content fetch with discovered namespace")
                        request_url = self._get_url(endpoint)
                        raw_response = requests.request(
                            method='GET',
                            url=request_url,
                            headers=self._get_headers(),
                            params=params,
                            timeout=self.timeout
                        )
                
                raw_response.raise_for_status() # Check for HTTP errors first

                # --- Log the raw response text ---
                logger.debug("RAW WP RESPONSE Status: %s", raw_response.status_code)
                logger.debug("RAW WP RESPONSE Headers: %s", raw_response.headers)
                try:
                    raw_text = raw_response.text
                    logger.debug("RAW WP RESPONSE Text (first 500 chars): %s", raw_text[:500])
                    if not raw_text.strip():
                        logger.warning("RAW WP RESPONSE Text is empty or only whitespace!")
                except Exception as log_err:
                    logger.error("Error trying to log raw response text: %s", log_err)
                # --- End Logging ---

                page_items = raw_response.json()
                response_headers = raw_response.headers

                if not page_items: break

                all_items.extend(page_items)

                total_pages = int(response_headers.get('X-WP-TotalPages', 0))
                logger.debug("Page %s fetched. Items: %s. Total Pages: %s", params['page'], len(page_items), total_pages)
                if not total_pages or params['page'] >= total_pages: break

                params['page'] += 1
                logger.info("Fetching page %s of %s from %s...", params['page'], total_pages, self.base_url)

            # --- First catch the specific JSONDecodeError ---
            except requests.exceptions.JSONDecodeError as json_e:
                logger.error(f"JSON Decode Error fetching page {params['page']} from {self.base_url}. Response Text: {raw_response.text[:500]}", exc_info=True)
                raise WordPressAPIClientError(f"Error fetching page {params['page']} from {self.base_url}: Failed to decode JSON. Response text started with: {raw_response.text[:100]}") from json_e
            # --- Then catch the more general RequestException ---
            except requests.exceptions.RequestException as e:
                # Log the response text here too if possible in case of RequestException
                if hasattr(e, 'response') and e.response is not None:
                    logger.error("Error during request, Response Text (if available): %s", e.response.text[:500])
                logger.error("Error fetching page %s from %s: %s", params['page'], self.base_url, e)
                raise WordPressAPIClientError(f"Error fetching page {params['page']} from {self.base_url}: {e}") from e

        logger.info("Fetched a total of %s content items from %s.", len(all_items), self.base_url)
        return all_items

    def get_content_item(self, wp_id):
        """Fetches a single content item by its WordPress ID from the configured site."""
        logger.info("Fetching content item WP ID %s from %s.", wp_id, self.base_url)
        # Determine endpoint based on the domain
        endpoint = f'content/{wp_id}'
        return self._make_request('GET', endpoint)

    def create_content_item(self, data):
        """Creates a new content item in the configured WordPress site."""
        logger.info("Creating new content item in %s: %s", self.base_url, data.get('title', 'N/A'))
        required_fields = ['title', 'post_type', 'status']
        if not all(field in data for field in required_fields):
            raise ValueError(f"Missing required fields for creating content: {required_fields}")
        # Determine endpoint based on the domain
        endpoint = 'content'
        return self._make_request('POST', endpoint, json_data=data)

    def update_content_item(self, wp_id, data):
        """Updates an existing content item in the configured WordPress site."""
        logger.info("Updating content item WP ID %s in %s.", wp_id, self.base_url)
        # Determine endpoint based on the domain
        endpoint = f'content/{wp_id}'
        return self._make_request('POST', endpoint, json_data=data)

    def delete_content_item(self, wp_id, force=False):
        """Deletes a content item from the configured WordPress site."""
        logger.warning("Deleting content item WP ID %s from %s (force=%s).", wp_id, self.base_url, force)
        params = {'force': 'true'} if force else {}
        # Determine endpoint based on the domain
        endpoint = f'content/{wp_id}'
        return self._make_request('DELETE', endpoint, params=params)
    
    def get_all_categories(self):
        """Fetches all categories from the configured WP site using standard WordPress REST API."""
        logger.info("Fetching all categories from %s.", self.base_url)
        
        # Try to use standard WordPress REST API for categories first
        try:
            # Use the standard WordPress REST API endpoint for categories
            standard_wp_url = f"{self.base_url}/wp-json/wp/v2/categories"
            logger.info("Trying standard WordPress categories endpoint: %s", standard_wp_url)
            
            response = requests.get(
                standard_wp_url,
                params={'hide_empty': 'false', 'per_page': 100},
                headers={
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                timeout=30
            )
            
            if response.status_code == 200:
                categories = response.json()
                logger.info("Successfully fetched %d categories using standard WordPress API", len(categories))
                return categories
            else:
                logger.warning("Standard WordPress categories API returned status %d", response.status_code)
                
        except Exception as e:
            logger.warning("Failed to fetch categories using standard WordPress API: %s", e)
        
        # Fallback: try custom endpoint (this will likely fail but we'll keep it for compatibility)
        logger.info("Falling back to custom categories endpoint")
        endpoint = 'categories'
        try:
            return self._make_request('GET', endpoint, params={'hide_empty': 'false'})
        except WordPressAPIClientError as e:
            logger.error("Custom categories endpoint also failed: %s", e)
            # Return empty list instead of raising error to prevent breaking the app
            logger.info("Returning empty categories list to prevent app breakage")
            return []