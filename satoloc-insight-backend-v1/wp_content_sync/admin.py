from django.contrib import admin # type: ignore
from .models import Synced<PERSON><PERSON><PERSON>, SyncLog, WordPressConnection
from django.utils.html import format_html
import json

class WordPressConnectionAdmin(admin.ModelAdmin):
    list_display = ('site_url', 'user', 'is_active', 'last_sync', 'created_at')
    list_filter = ('is_active', 'user')
    search_fields = ('site_url', 'user__username', 'user__email')
    readonly_fields = ('created_at', 'updated_at', 'last_sync')
    fieldsets = (
        ('Connection Details', {
            'fields': ('site_url', 'user', 'api_key', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('last_sync', 'created_at', 'updated_at')
        }),
    )

class SyncedContentAdmin(admin.ModelAdmin):
    list_display = ('id', 'title', 'wp_site_url', 'wp_id', 'post_type', 'status', 'language_name', 'display_categories', 'date_modified_gmt', 'last_synced_at') # Added display_categories
    list_filter = ('post_type', 'status', 'wp_site_url', 'language_code')
    search_fields = ('title', 'content', 'wp_site_url', 'language_name')
    readonly_fields = ('created_at', 'updated_at', 'last_synced_at', 'categories') # Added categories to readonly
    fieldsets = (
        ('WordPress Source', {
            'fields': ('wp_site_url', 'wp_id', 'wp_link',)
        }),
        ('Content', {
            'fields': ('title', 'content', 'post_type', 'status', 'featured_image_url', 'categories') # Added categories
        }),
        ('Dates', {
            'fields': ('date_created_gmt', 'date_modified_gmt', 'created_at', 'updated_at', 'last_synced_at')
        }),
        ('Language', {
            'fields': ('language_code', 'language_name')
        }),
    )

    # --- UPDATED: Method to display categories nicely ---
    def display_categories(self, obj):
        # Check if categories is a list and is not empty
        if isinstance(obj.categories, list) and obj.categories:
            names = [cat.get('name', 'N/A') for cat in obj.categories if isinstance(cat, dict)]
            # If the list only contains 'Uncategorized' after potential WP default assignment, show it.
            # Otherwise, join all names found.
            return ", ".join(names)
        # If it's an empty list (meaning WP reported no categories or only default was assigned and filtered),
        # display "Uncategorized" as a fallback, mimicking WP behavior.
        elif isinstance(obj.categories, list) and not obj.categories:
             # This case now represents posts that truly had no category assigned in WP (API returned null/empty list)
            return "Uncategorized" # Display default
        # Fallback if it's not a list or None (shouldn't happen with default=list)
        return "-"
    display_categories.short_description = "Categories"

class SyncLogAdmin(admin.ModelAdmin):
    list_display = ('id', 'operation', 'wp_site_url', 'status', 'content_item_display', 'user_display', 'created_at')
    list_filter = ('operation', 'status', 'wp_site_url', 'created_at')
    search_fields = ('wp_site_url', 'details')
    readonly_fields = ('created_at',)

    def content_item_display(self, obj):
        if obj.content_item:
            return f"{obj.content_item.title} (ID: {obj.content_item.id})"
        return "-"
    content_item_display.short_description = "Content Item"

    def user_display(self, obj):
        if obj.user:
            return obj.user.username
        return "-"
    user_display.short_description = "User"

    fieldsets = (
        ('Operation Info', {
            'fields': ('operation', 'status', 'wp_site_url', 'created_at')
        }),
        ('Related Data', {
            'fields': ('content_item', 'user')
        }),
        ('Details', {
            'fields': ('details',)
        }),
    )

admin.site.register(SyncedContent, SyncedContentAdmin)
admin.site.register(SyncLog, SyncLogAdmin)
admin.site.register(WordPressConnection, WordPressConnectionAdmin)