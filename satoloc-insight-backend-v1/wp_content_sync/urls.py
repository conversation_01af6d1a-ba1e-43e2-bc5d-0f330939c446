from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import SyncedContentViewSet, SyncLogViewSet, WordPressConnectionViewSet

# Create a router and register our viewsets with it.
router = DefaultRouter()
router.register(r'content', SyncedContentViewSet, basename='synced-content')
router.register(r'logs', SyncLogViewSet, basename='sync-logs')
router.register(r'connections', WordPressConnectionViewSet, basename='wp-connections')

# The API URLs are now determined automatically by the router.
urlpatterns = [
    path('', include(router.urls)),
]