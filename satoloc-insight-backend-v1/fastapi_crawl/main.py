"""FastAPI crawler service main module."""
from typing import List, Dict, Any, Optional, Set
import json
import logging
import time
from urllib.parse import urljoin, urlparse
from collections import deque

import requests
from bs4 import BeautifulSoup
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="SatoLoc SEO Crawler Service")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class CrawlConfig(BaseModel):
    start_urls: List[str]
    allowed_domains: List[str]
    crawl_depth: int
    selectors: Dict[str, List[str]]
    max_pages: int

# Store crawl results in memory (replace with proper storage in production)
crawl_results = {}

def extract_metadata(soup: BeautifulSoup) -> Dict[str, Any]:
    """Extract metadata from page."""
    metadata = {}
    
    # Title
    title_tag = soup.find('title')
    metadata['title'] = title_tag.text.strip() if title_tag else None
    
    # Meta description
    meta_desc = soup.find('meta', attrs={'name': 'description'})
    metadata['description'] = meta_desc['content'] if meta_desc and 'content' in meta_desc.attrs else None
    
    # Other meta tags
    metadata['meta_tags'] = {}
    for meta in soup.find_all('meta'):
        name = meta.get('name') or meta.get('property')
        if name and 'content' in meta.attrs:
            metadata['meta_tags'][name] = meta['content']
    
    return metadata

def extract_links(soup: BeautifulSoup, base_url: str) -> list:
    """Extract links from page."""
    links = []
    for a in soup.find_all('a', href=True):
        href = a['href']
        absolute_url = urljoin(base_url, href)
        
        links.append({
            'url': absolute_url,
            'text': a.get_text(strip=True),
            'rel': a.get('rel', []),
            'is_internal': urlparse(absolute_url).netloc == urlparse(base_url).netloc
        })
    
    return links

def extract_images(soup: BeautifulSoup, base_url: str) -> list:
    """Extract images from page."""
    images = []
    for img in soup.find_all('img'):
        src = img.get('src')
        if src:
            images.append({
                'url': urljoin(base_url, src),
                'alt': img.get('alt', ''),
                'title': img.get('title', ''),
                'width': img.get('width'),
                'height': img.get('height')
            })
    
    return images

def analyze_technical_seo(soup: BeautifulSoup, response: requests.Response) -> Dict[str, Any]:
    """Analyze technical SEO aspects."""
    return {
        'load_time': response.elapsed.total_seconds(),
        'page_size': len(response.content),
        'status_code': response.status_code,
        'has_robots_meta': bool(soup.find('meta', attrs={'name': 'robots'})),
        'has_sitemap_link': bool(soup.find('link', attrs={'rel': 'sitemap'})),
        'has_canonical': bool(soup.find('link', attrs={'rel': 'canonical'})),
        'has_schema_markup': bool(soup.find_all('script', attrs={'type': 'application/ld+json'}))
    }

def analyze_mobile_friendliness(soup: BeautifulSoup) -> Dict[str, Any]:
    """Analyze mobile-friendliness aspects."""
    viewport = soup.find('meta', attrs={'name': 'viewport'})
    return {
        'has_viewport_meta': bool(viewport),
        'viewport_content': viewport['content'] if viewport else None,
        'has_touch_icons': bool(soup.find_all('link', attrs={'rel': ['apple-touch-icon', 'icon']}))
    }

def crawl_url(url: str, selectors: Dict[str, List[str]]) -> Dict[str, Any]:
    """Crawl a single URL and extract all relevant data."""
    try:
        headers = {
            'User-Agent': 'SatoLocInsight SEO Crawler/1.0',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9,tr;q=0.8,zh;q=0.7'
        }
        
        # Disable SSL verification warning
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        response = requests.get(url, headers=headers, timeout=30, verify=False)
        response.raise_for_status()
        
        if 'text/html' not in response.headers.get('Content-Type', ''):
            return None
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Extract all data
        metadata = extract_metadata(soup)
        links = extract_links(soup, url)
        images = extract_images(soup, url)
        technical_seo = analyze_technical_seo(soup, response)
        mobile_analysis = analyze_mobile_friendliness(soup)
        
        # Extract content based on selectors, handling each selector type separately
        content = []
        for selector_type, selector_list in selectors.items():
            if selector_type == 'javascript':
                # Handle JavaScript elements differently
                js_content = []
                # Find script tags
                for script in soup.find_all('script'):
                    js_content.append({
                        'type': 'script',
                        'content': script.string if script.string else '',
                        'src': script.get('src', ''),
                        'async': script.get('async', False),
                        'defer': script.get('defer', False)
                    })
                # Find elements with onclick attributes
                for elem in soup.find_all(attrs={'onclick': True}):
                    js_content.append({
                        'type': 'onclick',
                        'content': elem.get('onclick', ''),
                        'element': elem.name
                    })
                # Find elements with data attributes
                for elem in soup.find_all(True):
                    data_attrs = {k: v for k, v in elem.attrs.items() if k.startswith('data-')}
                    if data_attrs:
                        js_content.append({
                            'type': 'data-attributes',
                            'attributes': data_attrs,
                            'element': elem.name
                        })
                content.extend(js_content)
            else:
                # Handle other selectors normally
                for selector in selector_list:
                    try:
                        for element in soup.select(selector):
                            content.append({
                                'type': selector_type,
                                'content': element.get_text(strip=True),
                                'html': str(element)
                            })
                    except Exception as e:
                        logger.warning(f"Error with selector '{selector}': {str(e)}")
                        continue
        
        return {
            'url': url,
            'metadata': metadata,
            'links': links,
            'images': images,
            'content': content,
            'technical_seo': technical_seo,
            'mobile_analysis': mobile_analysis,
            'status_code': response.status_code,
            'content_type': response.headers.get('Content-Type', '')
        }
        
    except Exception as e:
        logger.error(f"Error crawling {url}: {str(e)}")
        return None

def should_crawl_url(url: str, allowed_domains: List[str], follow_patterns: List[str], exclude_patterns: List[str]) -> bool:
    """Check if URL should be crawled based on patterns."""
    parsed_url = urlparse(url)
    
    # Check if domain is allowed
    if parsed_url.netloc not in allowed_domains:
        return False
    
    # Check exclude patterns
    for pattern in exclude_patterns:
        if '*' in pattern:
            # Convert glob pattern to simple check
            pattern = pattern.replace('*', '')
            if pattern in url:
                return False
        elif pattern == url:
            return False
    
    # Check follow patterns
    for pattern in follow_patterns:
        if '*' in pattern:
            # Convert glob pattern to simple check
            pattern = pattern.replace('*', '')
            if pattern in url:
                return True
        elif pattern == url:
            return True
    
    return True

def recursive_crawl(start_url: str, config: Dict[str, Any], job_id: str) -> None:
    """Recursively crawl website starting from the given URL."""
    allowed_domains = config.get('allowed_domains', [urlparse(start_url).netloc])
    max_pages = config.get('max_pages', 100)
    crawl_depth = config.get('crawl_depth', 3)
    follow_patterns = config.get('follow_patterns', ["/*"])
    exclude_patterns = config.get('exclude_patterns', [])
    selectors = config.get('selectors', {})
    
    # Initialize tracking sets
    visited_urls = set()
    queue = deque([(start_url, 0)])  # (url, depth)
    
    while queue and len(visited_urls) < max_pages:
        url, depth = queue.popleft()
        
        if url in visited_urls or depth > crawl_depth:
            continue
        
        logger.info(f"Crawling {url} at depth {depth}")
        
        # Crawl the URL
        page_data = crawl_url(url, selectors)
        if page_data:
            # Add to results
            crawl_results[job_id]['pages'].append(page_data)
            crawl_results[job_id]['stats']['pages_crawled'] += 1
            crawl_results[job_id]['stats']['total_links'] += len(page_data['links'])
            crawl_results[job_id]['stats']['total_images'] += len(page_data['images'])
            
            # Mark as visited
            visited_urls.add(url)
            
            # Add internal links to queue
            if depth < crawl_depth:
                for link in page_data['links']:
                    next_url = link['url']
                    if (next_url not in visited_urls and 
                        link['is_internal'] and 
                        should_crawl_url(next_url, allowed_domains, follow_patterns, exclude_patterns)):
                        queue.append((next_url, depth + 1))
        
        # Update progress
        crawl_results[job_id]['stats']['current_depth'] = depth
        crawl_results[job_id]['stats']['urls_in_queue'] = len(queue)

@app.post("/crawl")
async def start_crawl(config: CrawlConfig):
    """Start a new crawl job."""
    logger.info(f"Received crawl request: {config}")
    
    if not config.start_urls:
        raise HTTPException(status_code=400, detail="No start URLs provided")
    
    # Generate job ID
    job_id = f"crawl_{int(time.time())}"
    
    # Initialize results
    crawl_results[job_id] = {
        'status': 'processing',
        'pages': [],
        'stats': {
            'pages_crawled': 0,
            'total_links': 0,
            'total_images': 0,
            'current_depth': 0,
            'urls_in_queue': 0
        }
    }
    
    # Create a queue of URLs to crawl
    queue = []
    visited = set()
    
    # Add start URLs to queue with depth 0
    for url in config.start_urls:
        queue.append((url, 0))  # (url, depth)
    
    while queue and len(visited) < config.max_pages:
        current_url, depth = queue.pop(0)
        
        if current_url in visited or depth > config.crawl_depth:
            continue
        
        logger.info(f"Crawling {current_url} at depth {depth}")
        
        try:
            # Crawl the current URL
            page_data = crawl_url(current_url, config.selectors)
            if page_data:
                # Add to results
                crawl_results[job_id]['pages'].append(page_data)
                crawl_results[job_id]['stats']['pages_crawled'] += 1
                crawl_results[job_id]['stats']['total_links'] += len(page_data['links'])
                crawl_results[job_id]['stats']['total_images'] += len(page_data['images'])
                
                # Mark as visited
                visited.add(current_url)
                
                # Add internal links to queue if within depth limit
                if depth < config.crawl_depth:
                    for link in page_data['links']:
                        if (link['is_internal'] and 
                            link['url'] not in visited and 
                            urlparse(link['url']).netloc in config.allowed_domains):
                            queue.append((link['url'], depth + 1))
                
                # Update progress
                crawl_results[job_id]['stats']['current_depth'] = depth
                crawl_results[job_id]['stats']['urls_in_queue'] = len(queue)
                
                # Add small delay to respect rate limiting
                time.sleep(0.5)  # 2 requests per second
                
        except Exception as e:
            logger.error(f"Error processing {current_url}: {str(e)}")
            continue
    
    # Update final status
    crawl_results[job_id]['status'] = 'completed'
    logger.info(f"Crawl completed. Processed {len(visited)} pages.")
    
    return {
        'job_id': job_id,
        'status': 'completed',
        'message': f'Crawled {len(visited)} pages'
    }

@app.get("/results/{job_id}")
async def get_results(job_id: str):
    """Get results for a specific crawl job."""
    if job_id not in crawl_results:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return crawl_results[job_id]

@app.get("/status/{job_id}")
async def get_status(job_id: str):
    """Get status of a specific crawl job."""
    if job_id not in crawl_results:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return {
        'status': crawl_results[job_id]['status'],
        'stats': crawl_results[job_id]['stats']
    }

@app.get("/ai_seo/{job_id}")
async def get_ai_seo_analysis(job_id: str):
    """Get AI SEO analysis for a specific crawl job."""
    if job_id not in crawl_results:
        raise HTTPException(status_code=404, detail="Job not found")
    
    # Perform AI SEO analysis on the crawled content
    # For now, return placeholder scores
    return {
        'readability_score': 0.8,
        'sentiment_score': 0.7,
        'keyword_relevance': 0.85,
        'content_quality': 0.75,
        'ai_readiness': 0.9
    }

@app.get("/")
async def root():
    """Root endpoint to check service status."""
    return {"message": "SatoLoc SEO Crawler service is running"} 