[MASTER]
# Python code to execute, usually for sys.path manipulation such as pygtk.require().
init-hook='import sys; import os; sys.path.extend([os.getcwd(), os.path.dirname(os.getcwd())])'

[MESSAGES CONTROL]
# Disable specific warnings
disable=C0111,  # missing-docstring
        C0103,  # invalid-name
        C0303,  # trailing-whitespace
        W0621,  # redefined-outer-name
        W0622,  # redefined-builtin
        W0703,  # broad-except
        W0707,  # raise-missing-from
        E0401,  # import-error
        C0114,  # missing-module-docstring
        C0115,  # missing-class-docstring
        C0116,  # missing-function-docstring
        C0412,  # ungrouped-imports
        C0411   # wrong-import-order

[FORMAT]
# Maximum number of characters on a single line
max-line-length=120

[TYPECHECK]
# List of module names for which member attributes should not be checked
ignored-modules=fastapi,pydantic,uvicorn,fastapi.middleware,fastapi.middleware.cors

# List of class names for which member attributes should not be checked
ignored-classes=<PERSON>AP<PERSON>,BaseModel,<PERSON><PERSON><PERSON>Ex<PERSON>,CORSMiddleware

[BASIC]
# Regular expressions for naming styles
good-names=i,j,k,ex,Run,_,id,db,app

[MISCELLANEOUS]
# List of note tags to take into consideration
notes=FIXME,XXX,TODO 