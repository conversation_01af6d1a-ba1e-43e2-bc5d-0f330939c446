import uvicorn
import multiprocessing
import os
import platform

def main():
    """Run the FastAPI server with appropriate configuration."""
    # Force spawn on macOS to avoid CoreFoundation issues
    if platform.system() == 'Darwin':  # macOS
        # Set environment variable to handle CoreFoundation fork issue
        os.environ['OBJC_DISABLE_INITIALIZE_FORK_SAFETY'] = 'YES'
        if multiprocessing.get_start_method(allow_none=True) != 'spawn':
            multiprocessing.set_start_method('spawn')

    # Run with a single worker to avoid multiprocessing issues
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        workers=1,  # Single worker to avoid multiprocessing issues
        log_level="info"
    )

if __name__ == "__main__":
    main() 
