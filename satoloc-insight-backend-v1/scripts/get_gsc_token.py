import os
from google_auth_oauthlib.flow import InstalledAppFlow
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# OAuth 2.0 scopes for Google Search Console API
SCOPES = ['https://www.googleapis.com/auth/webmasters.readonly']

def get_refresh_token():
    # Load client configuration
    client_config = {
        'web': {  # Using web application type
            'client_id': os.getenv('GOOGLE_CLIENT_ID'),
            'project_id': 'omeruta-seo',
            'auth_uri': 'https://accounts.google.com/o/oauth2/auth',
            'token_uri': 'https://oauth2.googleapis.com/token',
            'auth_provider_x509_cert_url': 'https://www.googleapis.com/oauth2/v1/certs',
            'client_secret': os.getenv('GOOGLE_CLIENT_SECRET'),
            'redirect_uris': ['http://localhost:3000/oauth2/callback'],
            'javascript_origins': ['http://localhost:3000', 'https://www.omeruta.com']
        }
    }

    try:
        # Create flow instance with offline access
        flow = InstalledAppFlow.from_client_config(
            client_config,
            scopes=SCOPES,
            redirect_uri='http://localhost:3000/oauth2/callback'
        )

        # Run the OAuth flow with offline access
        credentials = flow.run_local_server(
            port=3000,
            access_type='offline',
            prompt='consent'
        )

        print('\nRefresh Token:', credentials.refresh_token)
        print('Access Token:', credentials.token)
        print('Expiry:', credentials.expiry)
        
        # Save tokens to a file
        token_data = {
            'refresh_token': credentials.refresh_token,
            'token': credentials.token,
            'expiry': credentials.expiry.isoformat() if credentials.expiry else None
        }
        
        with open('gsc_tokens.json', 'w') as f:
            json.dump(token_data, f, indent=2)
            print('\nTokens saved to gsc_tokens.json')

    except Exception as e:
        print(f'Error during OAuth flow: {str(e)}')
        raise

if __name__ == '__main__':
    get_refresh_token()
