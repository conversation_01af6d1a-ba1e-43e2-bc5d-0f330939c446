from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from .models import StripeSubscription, StripeCustomer
from .services import StripeService
import logging

User = get_user_model()
logger = logging.getLogger(__name__)


@receiver(post_save, sender=User)
def create_stripe_customer_for_new_user(sender, instance, created, **kwargs):
    """Create Stripe customer for new non-Google users"""
    if created and not getattr(instance, "is_google_user", False):
        try:
            # Only create Stripe customer for non-Google users
            # Google users will get their Stripe customer created when they first try to subscribe
            stripe_customer = StripeService.get_or_create_customer(instance)
            logger.info(f"Created Stripe customer for new user: {instance.username}")
        except Exception as e:
            logger.error(
                f"Failed to create Stripe customer for user {instance.username}: {str(e)}"
            )
            # Don't fail the user creation if Stripe customer creation fails
            pass


@receiver(post_save, sender=StripeSubscription)
def update_user_subscription_info(sender, instance, created, **kwargs):
    """Update user's subscription information when subscription changes"""
    if instance.is_active:
        # Update user's subscription plan and details
        user = instance.user
        user.subscription_plan = instance.product.plan_type
        user.subscription_price = instance.price.amount
        user.subscription_start_date = instance.current_period_start
        user.save(
            update_fields=[
                "subscription_plan",
                "subscription_price",
                "subscription_start_date",
            ]
        )
    elif instance.status in ["canceled", "unpaid", "incomplete_expired"]:
        # Reset to freemium if subscription is no longer active
        user = instance.user
        user.subscription_plan = "freemium"
        user.subscription_price = None
        user.subscription_start_date = None
        user.save(
            update_fields=[
                "subscription_plan",
                "subscription_price",
                "subscription_start_date",
            ]
        )
