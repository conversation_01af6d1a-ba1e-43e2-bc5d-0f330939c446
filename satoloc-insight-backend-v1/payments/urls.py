from django.urls import path
from .views import (
    CreateCheckoutSessionView,
    CreateBillingPortalSessionView,
    CancelSubscriptionView,
    UserSubscriptionView,
    ProductsAndPricesView,
    UserPaymentHistoryView,
    StripeWebhookView,
    sync_products_prices,
    test_payments_view,
)

urlpatterns = [
    # Checkout and subscription management
    path(
        "create-checkout-session/",
        CreateCheckoutSessionView.as_view(),
        name="create-checkout-session",
    ),
    path(
        "create-billing-portal-session/",
        CreateBillingPortalSessionView.as_view(),
        name="create-billing-portal-session",
    ),
    path(
        "cancel-subscription/",
        CancelSubscriptionView.as_view(),
        name="cancel-subscription",
    ),
    # User subscription and payment info
    path("subscription/", UserSubscriptionView.as_view(), name="user-subscription"),
    path("payment-history/", UserPaymentHistoryView.as_view(), name="payment-history"),
    # Products and pricing
    path("products/", ProductsAndPricesView.as_view(), name="products-and-prices"),
    # Webhooks
    path("webhook/", StripeWebhookView.as_view(), name="stripe-webhook"),
    # Admin endpoints
    path("admin/sync-products/", sync_products_prices, name="sync-products-prices"),
    # Test endpoint
    path("test/", test_payments_view, name="test-payments"),
]
