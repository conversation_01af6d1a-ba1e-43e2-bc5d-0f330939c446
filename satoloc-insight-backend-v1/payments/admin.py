from django.contrib import admin
from .models import (
    StripeCustomer,
    StripeProduct,
    StripePrice,
    StripeSubscription,
    StripePayment,
    StripeWebhookEvent,
)


@admin.register(StripeCustomer)
class StripeCustomerAdmin(admin.ModelAdmin):
    list_display = ["user", "stripe_customer_id", "created_at"]
    list_filter = ["created_at"]
    search_fields = ["user__username", "user__email", "stripe_customer_id"]
    readonly_fields = ["stripe_customer_id", "created_at", "updated_at"]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("user")


@admin.register(StripeProduct)
class StripeProductAdmin(admin.ModelAdmin):
    list_display = ["name", "plan_type", "stripe_product_id", "is_active", "created_at"]
    list_filter = ["plan_type", "is_active", "created_at"]
    search_fields = ["name", "stripe_product_id", "plan_type"]
    readonly_fields = ["stripe_product_id", "created_at", "updated_at"]


@admin.register(StripePrice)
class StripePriceAdmin(admin.ModelAdmin):
    list_display = [
        "product",
        "amount",
        "currency",
        "billing_interval",
        "stripe_price_id",
        "is_active",
    ]
    list_filter = ["billing_interval", "currency", "is_active", "created_at"]
    search_fields = ["stripe_price_id", "product__name"]
    readonly_fields = ["stripe_price_id", "created_at", "updated_at"]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("product")


@admin.register(StripeSubscription)
class StripeSubscriptionAdmin(admin.ModelAdmin):
    list_display = [
        "user",
        "product",
        "status",
        "current_period_start",
        "current_period_end",
        "cancel_at_period_end",
        "created_at",
    ]
    list_filter = [
        "status",
        "cancel_at_period_end",
        "product__plan_type",
        "price__billing_interval",
        "created_at",
    ]
    search_fields = [
        "user__username",
        "user__email",
        "stripe_subscription_id",
        "product__name",
    ]
    readonly_fields = [
        "stripe_subscription_id",
        "stripe_customer",
        "current_period_start",
        "current_period_end",
        "trial_start",
        "trial_end",
        "canceled_at",
        "created_at",
        "updated_at",
    ]
    date_hierarchy = "created_at"

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .select_related("user", "product", "price", "stripe_customer")
        )


@admin.register(StripePayment)
class StripePaymentAdmin(admin.ModelAdmin):
    list_display = [
        "user",
        "amount",
        "currency",
        "status",
        "subscription",
        "created_at",
    ]
    list_filter = ["status", "currency", "created_at"]
    search_fields = [
        "user__username",
        "user__email",
        "stripe_payment_intent_id",
        "subscription__stripe_subscription_id",
    ]
    readonly_fields = [
        "stripe_payment_intent_id",
        "stripe_customer",
        "created_at",
        "updated_at",
    ]
    date_hierarchy = "created_at"

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .select_related("user", "stripe_customer", "subscription")
        )


@admin.register(StripeWebhookEvent)
class StripeWebhookEventAdmin(admin.ModelAdmin):
    list_display = [
        "stripe_event_id",
        "event_type",
        "processed",
        "created_at",
        "processed_at",
    ]
    list_filter = ["event_type", "processed", "created_at"]
    search_fields = ["stripe_event_id", "event_type"]
    readonly_fields = ["stripe_event_id", "data", "created_at"]
    date_hierarchy = "created_at"

    fieldsets = (
        (
            "Event Information",
            {"fields": ("stripe_event_id", "event_type", "created_at")},
        ),
        (
            "Processing Status",
            {"fields": ("processed", "processed_at", "processing_error")},
        ),
        ("Event Data", {"fields": ("data",), "classes": ("collapse",)}),
    )
