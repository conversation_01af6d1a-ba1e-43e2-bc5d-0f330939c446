from rest_framework import serializers
from .models import (
    StripeCustomer,
    StripeProduct,
    StripePrice,
    StripeSubscription,
    StripePayment,
    StripeWebhookEvent,
)


class StripeCustomerSerializer(serializers.ModelSerializer):
    username = serializers.CharField(source="user.username", read_only=True)
    email = serializers.CharField(source="user.email", read_only=True)

    class Meta:
        model = StripeCustomer
        fields = [
            "id",
            "username",
            "email",
            "stripe_customer_id",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class StripeProductSerializer(serializers.ModelSerializer):
    class Meta:
        model = StripeProduct
        fields = [
            "id",
            "name",
            "plan_type",
            "stripe_product_id",
            "description",
            "is_active",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class StripePriceSerializer(serializers.ModelSerializer):
    product_name = serializers.Char<PERSON>ield(source="product.name", read_only=True)
    product_plan_type = serializers.CharField(
        source="product.plan_type", read_only=True
    )

    class Meta:
        model = StripePrice
        fields = [
            "id",
            "stripe_price_id",
            "amount",
            "currency",
            "billing_interval",
            "is_active",
            "product_name",
            "product_plan_type",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class StripeSubscriptionSerializer(serializers.ModelSerializer):
    username = serializers.CharField(source="user.username", read_only=True)
    email = serializers.CharField(source="user.email", read_only=True)
    product_name = serializers.CharField(source="product.name", read_only=True)
    plan_type = serializers.CharField(source="product.plan_type", read_only=True)
    price_amount = serializers.DecimalField(
        source="price.amount", max_digits=10, decimal_places=2, read_only=True
    )
    billing_interval = serializers.CharField(
        source="price.billing_interval", read_only=True
    )
    is_active = serializers.BooleanField(read_only=True)
    days_until_renewal = serializers.IntegerField(read_only=True)

    class Meta:
        model = StripeSubscription
        fields = [
            "id",
            "stripe_subscription_id",
            "username",
            "email",
            "product_name",
            "plan_type",
            "price_amount",
            "billing_interval",
            "status",
            "is_active",
            "current_period_start",
            "current_period_end",
            "cancel_at_period_end",
            "canceled_at",
            "trial_start",
            "trial_end",
            "days_until_renewal",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class StripePaymentSerializer(serializers.ModelSerializer):
    username = serializers.CharField(source="user.username", read_only=True)
    email = serializers.CharField(source="user.email", read_only=True)
    subscription_id = serializers.CharField(
        source="subscription.stripe_subscription_id", read_only=True
    )

    class Meta:
        model = StripePayment
        fields = [
            "id",
            "stripe_payment_intent_id",
            "username",
            "email",
            "subscription_id",
            "amount",
            "currency",
            "status",
            "description",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class StripeWebhookEventSerializer(serializers.ModelSerializer):
    class Meta:
        model = StripeWebhookEvent
        fields = [
            "id",
            "stripe_event_id",
            "event_type",
            "processed",
            "processing_error",
            "data",
            "created_at",
            "processed_at",
        ]
        read_only_fields = ["id", "created_at"]


class CheckoutSessionRequestSerializer(serializers.Serializer):
    price_id = serializers.CharField(max_length=255, help_text="Stripe price ID")
    success_url = serializers.URLField(
        help_text="URL to redirect after successful payment"
    )
    cancel_url = serializers.URLField(
        help_text="URL to redirect after canceled payment"
    )
    trial_days = serializers.IntegerField(
        required=False, min_value=0, help_text="Trial period in days"
    )


class CheckoutSessionResponseSerializer(serializers.Serializer):
    session_id = serializers.CharField()
    url = serializers.URLField()
    customer_id = serializers.CharField()


class BillingPortalRequestSerializer(serializers.Serializer):
    return_url = serializers.URLField(
        help_text="URL to return to after managing subscription"
    )


class BillingPortalResponseSerializer(serializers.Serializer):
    url = serializers.URLField()


class SubscriptionCancelRequestSerializer(serializers.Serializer):
    subscription_id = serializers.CharField(
        max_length=255, help_text="Stripe subscription ID"
    )
    at_period_end = serializers.BooleanField(
        default=True, help_text="Cancel at period end or immediately"
    )


class ProductPriceSerializer(serializers.ModelSerializer):
    """Serializer for product with prices for frontend consumption"""

    prices = StripePriceSerializer(many=True, read_only=True)

    class Meta:
        model = StripeProduct
        fields = [
            "id",
            "name",
            "plan_type",
            "stripe_product_id",
            "description",
            "is_active",
            "prices",
        ]
