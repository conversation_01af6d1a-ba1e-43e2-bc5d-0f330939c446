import stripe
import logging
from django.conf import settings
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.utils.decorators import method_decorator
from django.utils import timezone
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import generics
from .models import (
    StripeCustomer,
    StripeProduct,
    StripePrice,
    StripeSubscription,
    StripePayment,
    StripeWebhookEvent,
)
from .serializers import (
    StripeCustomerSerializer,
    StripeProductSerializer,
    StripePriceSerializer,
    StripeSubscriptionSerializer,
    StripePaymentSerializer,
    CheckoutSessionRequestSerializer,
    CheckoutSessionResponseSerializer,
    BillingPortalRequestSerializer,
    BillingPortalResponseSerializer,
    SubscriptionCancelRequestSerializer,
    ProductPriceSerializer,
)
from .services import StripeService

logger = logging.getLogger(__name__)

# Initialize Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY


class CreateCheckoutSessionView(APIView):
    """Create a Stripe checkout session for subscription"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = CheckoutSessionRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            session_data = StripeService.create_checkout_session(
                user=request.user,
                price_id=serializer.validated_data["price_id"],
                success_url=serializer.validated_data["success_url"],
                cancel_url=serializer.validated_data["cancel_url"],
                trial_days=serializer.validated_data.get("trial_days"),
            )

            response_serializer = CheckoutSessionResponseSerializer(session_data)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        except ValueError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating checkout session: {str(e)}")
            return Response(
                {"error": "Payment processing error. Please try again later."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        except Exception as e:
            logger.error(f"Unexpected error creating checkout session: {str(e)}")
            return Response(
                {"error": "An unexpected error occurred. Please try again later."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class CreateBillingPortalSessionView(APIView):
    """Create a Stripe billing portal session for subscription management"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = BillingPortalRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            session_data = StripeService.create_billing_portal_session(
                user=request.user, return_url=serializer.validated_data["return_url"]
            )

            response_serializer = BillingPortalResponseSerializer(session_data)
            return Response(response_serializer.data, status=status.HTTP_200_OK)

        except ValueError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating billing portal session: {str(e)}")
            return Response(
                {"error": "Unable to access billing portal. Please try again later."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        except Exception as e:
            logger.error(f"Unexpected error creating billing portal session: {str(e)}")
            return Response(
                {"error": "An unexpected error occurred. Please try again later."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class CancelSubscriptionView(APIView):
    """Cancel a user's subscription"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = SubscriptionCancelRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            success = StripeService.cancel_subscription(
                user=request.user,
                subscription_id=serializer.validated_data["subscription_id"],
                at_period_end=serializer.validated_data["at_period_end"],
            )

            if success:
                return Response(
                    {"message": "Subscription canceled successfully"},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"error": "Subscription not found or already canceled"},
                    status=status.HTTP_404_NOT_FOUND,
                )

        except Exception as e:
            logger.error(f"Error canceling subscription: {str(e)}")
            return Response(
                {"error": "Failed to cancel subscription. Please try again later."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class UserSubscriptionView(APIView):
    """Get user's current subscription details"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            subscription = StripeService.get_user_active_subscription(request.user)

            if subscription:
                serializer = StripeSubscriptionSerializer(subscription)
                return Response(serializer.data, status=status.HTTP_200_OK)
            else:
                return Response(
                    {"message": "No active subscription found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

        except Exception as e:
            logger.error(f"Error fetching user subscription: {str(e)}")
            return Response(
                {"error": "Failed to fetch subscription details"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ProductsAndPricesView(APIView):
    """Get all available products and their prices"""

    permission_classes = [AllowAny]

    def get(self, request):
        try:
            products = StripeProduct.objects.filter(is_active=True).prefetch_related(
                "prices"
            )
            serializer = ProductPriceSerializer(products, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error fetching products and prices: {str(e)}")
            return Response(
                {"error": "Failed to fetch pricing information"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class UserPaymentHistoryView(generics.ListAPIView):
    """Get user's payment history"""

    permission_classes = [IsAuthenticated]
    serializer_class = StripePaymentSerializer

    def get_queryset(self):
        return StripePayment.objects.filter(user=self.request.user)


@method_decorator(csrf_exempt, name="dispatch")
class StripeWebhookView(APIView):
    """Handle Stripe webhooks"""

    permission_classes = [AllowAny]

    def post(self, request):
        payload = request.body
        sig_header = request.META.get("HTTP_STRIPE_SIGNATURE")
        endpoint_secret = settings.STRIPE_WEBHOOK_SECRET

        try:
            event = stripe.Webhook.construct_event(payload, sig_header, endpoint_secret)
        except ValueError as e:
            logger.error(f"Invalid payload in webhook: {str(e)}")
            return HttpResponse(status=400)
        except stripe.error.SignatureVerificationError as e:
            logger.error(f"Invalid signature in webhook: {str(e)}")
            return HttpResponse(status=400)

        # Store the webhook event
        webhook_event, created = StripeWebhookEvent.objects.get_or_create(
            stripe_event_id=event["id"],
            defaults={
                "event_type": event["type"],
                "data": event["data"],
                "processed": False,
            },
        )

        if not created and webhook_event.processed:
            logger.info(f"Webhook {event['id']} already processed")
            return HttpResponse(status=200)

        try:
            # Handle the event
            if event["type"] == "customer.subscription.created":
                self._handle_subscription_created(event["data"]["object"])
            elif event["type"] == "customer.subscription.updated":
                self._handle_subscription_updated(event["data"]["object"])
            elif event["type"] == "customer.subscription.deleted":
                self._handle_subscription_deleted(event["data"]["object"])
            elif event["type"] == "invoice.payment_succeeded":
                self._handle_payment_succeeded(event["data"]["object"])
            elif event["type"] == "invoice.payment_failed":
                self._handle_payment_failed(event["data"]["object"])
            else:
                logger.info(f"Unhandled event type: {event['type']}")

            # Mark as processed
            webhook_event.processed = True
            webhook_event.processed_at = timezone.now()
            webhook_event.save()

        except Exception as e:
            error_message = f"Error processing webhook {event['id']}: {str(e)}"
            logger.error(error_message)
            webhook_event.processing_error = error_message
            webhook_event.save()
            return HttpResponse(status=500)

        return HttpResponse(status=200)

    def _handle_subscription_created(self, subscription):
        """Handle subscription.created webhook"""
        try:
            StripeService.sync_subscription_from_stripe(subscription["id"])
            logger.info(f"Processed subscription.created for {subscription['id']}")
        except Exception as e:
            logger.error(f"Failed to process subscription.created: {str(e)}")
            raise

    def _handle_subscription_updated(self, subscription):
        """Handle subscription.updated webhook"""
        try:
            StripeService.sync_subscription_from_stripe(subscription["id"])
            logger.info(f"Processed subscription.updated for {subscription['id']}")
        except Exception as e:
            logger.error(f"Failed to process subscription.updated: {str(e)}")
            raise

    def _handle_subscription_deleted(self, subscription):
        """Handle subscription.deleted webhook"""
        try:
            StripeService.sync_subscription_from_stripe(subscription["id"])
            logger.info(f"Processed subscription.deleted for {subscription['id']}")
        except Exception as e:
            logger.error(f"Failed to process subscription.deleted: {str(e)}")
            raise

    def _handle_payment_succeeded(self, invoice):
        """Handle invoice.payment_succeeded webhook"""
        try:
            if invoice.get("subscription"):
                StripeService.sync_subscription_from_stripe(invoice["subscription"])
            logger.info(f"Processed payment.succeeded for invoice {invoice['id']}")
        except Exception as e:
            logger.error(f"Failed to process payment.succeeded: {str(e)}")
            raise

    def _handle_payment_failed(self, invoice):
        """Handle invoice.payment_failed webhook"""
        try:
            if invoice.get("subscription"):
                StripeService.sync_subscription_from_stripe(invoice["subscription"])
            logger.info(f"Processed payment.failed for invoice {invoice['id']}")
        except Exception as e:
            logger.error(f"Failed to process payment.failed: {str(e)}")
            raise


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def sync_products_prices(request):
    """Admin endpoint to sync products and prices from Stripe"""
    if not request.user.is_staff:
        return Response(
            {"error": "Admin access required"}, status=status.HTTP_403_FORBIDDEN
        )

    try:
        StripeService.sync_products_and_prices()
        return Response(
            {"message": "Products and prices synced successfully"},
            status=status.HTTP_200_OK,
        )
    except Exception as e:
        logger.error(f"Failed to sync products and prices: {str(e)}")
        return Response(
            {"error": "Failed to sync products and prices"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def test_payments_view(request):
    """Test endpoint for payments app"""
    return Response({"message": "Payments app is working"}, status=status.HTTP_200_OK)
