from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
import openai
import os
import logging
from django.conf import settings

logger = logging.getLogger(__name__)

class GenerateContentView(APIView):
    """
    API endpoint for generating AI content based on keywords and prompts.
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Generate content using OpenAI's API based on the provided keyword, content type, and prompt.
        """
        keyword = request.data.get('keyword')
        content_type = request.data.get('contentType')
        prompt = request.data.get('prompt')

        if not keyword or not content_type or not prompt:
            return Response(
                {"error": "Keyword, content type, and prompt are required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Set up OpenAI client with API key from settings
            openai_api_key = settings.OPENAI_API_KEY
            client = openai.OpenAI(api_key=openai_api_key)

            # Prepare system message based on content type
            system_message = self._get_system_message(content_type)

            # Make API call to OpenAI
            response = client.chat.completions.create(
                model="gpt-4o",  # Use GPT-4 for high-quality content
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.5,
                max_tokens=16000,
                top_p=1.0,
                frequency_penalty=0.0,
                presence_penalty=0.0
            )

            # Extract and return the generated content
            content = response.choices[0].message.content

            # Log the successful generation
            logger.info(f"Generated {content_type} content for keyword: {keyword}")

            return Response({"content": content}, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error generating content: {str(e)}")
            return Response(
                {"error": f"Failed to generate content: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _get_system_message(self, content_type):
        """
        Get the appropriate system message based on content type.
        """
        system_messages = {
            "blog": "You are an expert SEO content writer specializing in creating comprehensive, engaging blog posts. Your content is well-structured, informative, and optimized for search engines while maintaining a natural, conversational tone. Use markdown formatting for headings and lists.",
            
            "learn": "You are an educational content specialist who creates in-depth, structured learning resources. Your content explains concepts clearly from basic to advanced levels, with a focus on helping readers truly understand the topic. Use markdown formatting for headings and lists.",
            
            "faq": "You are a Q&A content expert who creates comprehensive FAQ pages. You anticipate user questions and provide clear, concise, yet thorough answers. Your content addresses common misconceptions and provides practical information. Use markdown formatting for headings and questions.",
            
            "meta": "You are an SEO metadata specialist who creates optimized titles, descriptions, and related content for web pages. Your metadata is concise, compelling, and optimized for both search engines and user engagement. Format your response clearly with numbered sections.",
        }
        
        return system_messages.get(content_type, "You are an expert content creator who specializes in creating high-quality, SEO-optimized content. Use markdown formatting for structure.") 