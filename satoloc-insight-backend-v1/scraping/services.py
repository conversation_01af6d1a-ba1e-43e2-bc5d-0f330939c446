# scraping/services.py
import logging
import json
import csv
import os
from django.conf import settings
from openai import OpenAI
import tiktoken
import re
from typing import Dict, List

logger = logging.getLogger(__name__)

# LQA weighting constants
LQA_WEIGHTS = {"Critical": 25, "Major": 5, "Minor": 1, "Neutral": 0}
QUALITY_THRESHOLD = 95

# Edit distance thresholds
EDIT_DISTANCE_THRESHOLDS = {
    "minimal": 10,
    "moderate": 30,
    "significant": 31,
}

def load_translation_glossary():
    """Load the translation glossary from CSV file."""
    glossary_path = os.path.join(
        settings.BASE_DIR,
        "scraping",
        "data",
        "bybit_translation_glossary_TR _all_terms.csv"
    )
    
    glossary = {}
    try:
        with open(glossary_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                # Assuming CSV has 'term' and 'translation' columns
                glossary[row['term']] = row['translation']
        logger.info(f"Loaded {len(glossary)} terms from translation glossary")
    except Exception as e:
        logger.error(f"Error loading translation glossary: {str(e)}")
        glossary = {}
    
    return glossary

# Global glossary dictionary
glossary_dict = load_translation_glossary()

def calculate_edit_distance(source: str, target: str) -> int:
    """Standard Levenshtein distance function."""
    if len(source) < len(target):
        return calculate_edit_distance(target, source)
    if len(target) == 0:
        return len(source)

    previous_row = range(len(target) + 1)
    for i, c1 in enumerate(source):
        current_row = [i + 1]
        for j, c2 in enumerate(target):
            insertions = previous_row[j + 1] + 1
            deletions = current_row[j] + 1
            substitutions = previous_row[j] + (c1 != c2)
            current_row.append(min(insertions, deletions, substitutions))
        previous_row = current_row

    return previous_row[-1]


def calculate_lqa_score(error_category_severity: list, word_count: int) -> dict:
    """
    Calculates a simple LQA score based on penalty weights and a target threshold.
    """
    total_penalty_points = 0
    error_details = []

    for category in error_category_severity:
        category_penalty = (
            category.get("Critical", 0) * LQA_WEIGHTS["Critical"]
            + category.get("Major", 0) * LQA_WEIGHTS["Major"]
            + category.get("Minor", 0) * LQA_WEIGHTS["Minor"]
        )
        total_penalty_points += category_penalty
        error_details.append(
            {"category": category["category"], "penalty_points": category_penalty}
        )

    pwpt = total_penalty_points / word_count if word_count > 0 else 0
    quality_score = max(0, 100 - (pwpt * 100))

    return {
        "quality_score": round(quality_score, 2),
        "penalty_points": total_penalty_points,
        "pwpt": round(pwpt, 4),
        "passed": quality_score >= QUALITY_THRESHOLD,
        "error_details": error_details,
    }


class AIAnalyzer:
    """
    AIAnalyzer is responsible for building a prompt, sending it to OpenAI,
    and returning the final JSON with all analysis fields.
    """

    def __init__(
        self,
        scraped_content: dict,
        service: str,
        source_language: str,
        target_language: str,
        industry: str,
        glossary: dict = None,
    ):
        self.scraped_content = scraped_content
        self.service = service
        self.source_language = source_language
        self.target_language = target_language
        self.industry = industry
        self.glossary = glossary or {}
        self.max_total_tokens = 128000
        self.desired_output_tokens = 16000

        self.api_key = settings.OPENAI_API_KEY
        if not self.api_key:
            raise ValueError("OpenAI API key is not set.")

        # Make sure your environment or library supports "gpt-4o". 
        # If not, use "gpt-4" or "gpt-3.5-turbo".
        self.client = OpenAI(api_key=self.api_key)

    def _clean_html(self, text: str) -> str:
        """Remove HTML tags and clean up the text."""
        # Remove HTML tags
        text = re.sub(r'<[^>]+>', '', text)
        # Replace multiple spaces with single space
        text = re.sub(r'\s+', ' ', text)
        # Replace multiple newlines with single newline
        text = re.sub(r'\n+', '\n', text)
        return text.strip()

    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences intelligently."""
        # Common abbreviations to avoid splitting
        abbreviations = ['Mr.', 'Mrs.', 'Dr.', 'Prof.', 'Sr.', 'Jr.', 'vs.', 'e.g.', 'i.e.', 'etc.']
        
        # Temporarily replace abbreviations
        for abbr in abbreviations:
            text = text.replace(abbr, abbr.replace('.', '@'))
        
        # Split on sentence endings
        sentences = re.split(r'(?<=[.!?])\s+', text)
        
        # Restore abbreviations
        sentences = [s.replace('@', '.') for s in sentences]
        
        # Filter out empty sentences and clean each sentence
        return [s.strip() for s in sentences if s.strip()]

    def _process_content(self, content: str) -> List[str]:
        """Process content by cleaning HTML and splitting into sentences."""
        # First extract answer-title elements if they exist
        answer_titles = []
        answer_title_pattern = r'<p class="answer-title">(.*?)</p>'
        title_matches = re.finditer(answer_title_pattern, content)
        for match in title_matches:
            title = match.group(1).strip()
            if title:
                answer_titles.append(title)
        
        # Clean HTML tags
        cleaned_content = self._clean_html(content)
        # Split into sentences
        sentences = self._split_into_sentences(cleaned_content)
        # Filter out very short sentences (likely noise)
        sentences = [s for s in sentences if len(s.split()) > 2]
        
        # Add answer titles at the beginning of the list
        return answer_titles + sentences

    def _extract_faq_content(self, schema_markup: List[dict]) -> List[dict]:
        """Extract FAQ content including answer titles."""
        faq_content = []
        
        for schema in schema_markup:
            try:
                # Handle both string and dict schema data
                if isinstance(schema, str):
                    schema_data = json.loads(schema)
                else:
                    schema_data = schema

                # Handle both single object and list of objects
                if isinstance(schema_data, list):
                    schema_items = schema_data
                else:
                    schema_items = [schema_data]

                # Process each schema item
                for item in schema_items:
                    if isinstance(item, dict) and item.get("@type") == "FAQPage":
                        for qa in item.get("mainEntity", []):
                            if isinstance(qa, dict) and qa.get("@type") == "Question":
                                question = self._clean_html(qa.get("name", ""))
                                answer_text = qa.get("acceptedAnswer", {}).get("text", "")
                                
                                if question and answer_text:
                                    # First extract any answer-title elements
                                    answer_title_pattern = r'<p class="answer-title">(.*?)</p>'
                                    titles = re.findall(answer_title_pattern, answer_text)
                                    
                                    # Add question as first entry
                                    faq_content.append({
                                        "type": "question",
                                        "content": question
                                    })
                                    
                                    # Add any answer titles found
                                    for title in titles:
                                        if title.strip():
                                            faq_content.append({
                                                "type": "answer-title",
                                                "content": title.strip()
                                            })
                                    
                                    # Clean and split the remaining answer text
                                    cleaned_answer = self._clean_html(answer_text)
                                    answer_sentences = self._split_into_sentences(cleaned_answer)
                                    
                                    # Add each answer sentence
                                    for sentence in answer_sentences:
                                        if len(sentence.split()) > 2:  # Filter out very short sentences
                                            faq_content.append({
                                                "type": "answer-content",
                                                "content": sentence
                                            })
            except (json.JSONDecodeError, AttributeError, TypeError) as e:
                logger.warning(f"Error processing schema markup: {str(e)}")
                continue
                
        return faq_content

    def _count_tokens(self, text: str) -> int:
        """Count tokens for a given text using tiktoken."""
        try:
            encoding = tiktoken.encoding_for_model("gpt-4.1")
        except KeyError:
            encoding = tiktoken.get_encoding("cl100k_base")
        return len(encoding.encode(text))

    def generate_report(self) -> dict:
        prompt = self._build_prompt()
        prompt_tokens = self._count_tokens(prompt)
        logger.debug("Prompt tokens: %s", prompt_tokens)

        total_requested_tokens = prompt_tokens + self.desired_output_tokens

        if total_requested_tokens > self.max_total_tokens:
            self.desired_output_tokens = self.max_total_tokens - prompt_tokens - 100
            logger.warning("Reducing desired_output_tokens to %s due to token limit.", self.desired_output_tokens)
            if self.desired_output_tokens <= 0:
                raise ValueError("Prompt is too long to fit within the token limit.")

        try:
            response = self.client.chat.completions.create(
                model="gpt-4.1",  # Using GPT-4 for better analysis
                messages=[{"role": "user", "content": prompt}],
                max_tokens=self.desired_output_tokens,
                temperature=0.5,
            )
            report = response.choices[0].message.content.strip()

            # Remove code fences if present
            if report.startswith("```json"):
                report = report[len("```json"):].strip()
            if report.endswith("```"):
                report = report[:-3].strip()

            if not (report.startswith("{") and report.endswith("}")):
                logger.error("AI did not return properly formatted JSON.")
                logger.error("Raw report: %s", report)
                raise ValueError("AI did not return a JSON object as expected.")

            try:
                report_json = json.loads(report)
            except ValueError as e:
                logger.error("Failed to parse AI response as JSON: %s", e)
                logger.error("Raw response: %s", report)
                raise ValueError(f"Invalid JSON format: {str(e)}")

            # Mark whether single or dual
            is_single_url = not self.scraped_content.get("targetAnalysis")
            report_json["is_single_url"] = is_single_url

            # For single URL, ensure we preserve all content
            if is_single_url:
                source_analysis = self.scraped_content.get("sourceAnalysis", {})
                report_json["body_text"] = source_analysis.get("body_text", "")
                report_json["word_count"] = source_analysis.get("word_count", 0)
                report_json["segments"] = source_analysis.get("segments", 0)

            # For dual URLs, we also compute LQA or edit distances if needed
            if not is_single_url:
                word_count = self.scraped_content.get("word_count", 0)
                lqa_scores = calculate_lqa_score(
                    report_json["error_category_severity"], word_count
                )

                edit_distances = []
                for error in report_json.get("report_table", []):
                    # For dual URL, we can compute if targetContent and enhancedContent exist
                    if error.get("targetContent") and error.get("enhancedContent"):
                        distance = calculate_edit_distance(
                            error["targetContent"], error["enhancedContent"]
                        )
                        longer_length = max(
                            len(error["targetContent"]), len(error["enhancedContent"])
                        )
                        distance_percentage = (
                            (distance / longer_length * 100) if longer_length > 0 else 0
                        )

                        if distance_percentage <= EDIT_DISTANCE_THRESHOLDS["minimal"]:
                            severity = "minimal"
                        elif distance_percentage <= EDIT_DISTANCE_THRESHOLDS["moderate"]:
                            severity = "moderate"
                        else:
                            severity = "significant"

                        edit_distances.append(
                            {
                                "original": error["targetContent"],
                                "suggestion": error["enhancedContent"],
                                "distance": distance,
                                "percentage": round(distance_percentage, 2),
                                "severity": severity,
                            }
                        )

                report_json["lqa_analysis"] = {
                    "scores": lqa_scores,
                    "edit_distances": edit_distances,
                    "word_count": word_count,
                    "threshold": QUALITY_THRESHOLD,
                }
            else:
                # Single URL: we do not push LQA data or error categories in the final JSON
                pass

            return report_json

        except Exception as e:
            logger.error("AI analysis failed: %s", str(e))
            raise RuntimeError(f"AI Analysis failed: {str(e)}") from e

    def _build_prompt(self) -> str:
        """
        Build the prompt. We now have two TOTALLY different required JSON outputs:
        
        1) Single URL => minimal structure with "report_table" containing only 
           sourceContent and aiTranslation, plus an "ai_solutions" array.
        2) Dual URL => full LQA structure with error_category_severity, lqa_score, 
           report_table with sourceContent/targetContent/aiTranslation/enhancedContent, etc.
        """
        source_analysis = self.scraped_content.get("sourceAnalysis", {})
        target_analysis = self.scraped_content.get("targetAnalysis", {})
        is_single_url = not target_analysis

        # Extract FAQ content with special handling for answer titles
        schema_markup = source_analysis.get("seo", {}).get("schema_markup", [])
        faq_content = self._extract_faq_content(schema_markup)

        # Process body text into sentences
        body_text = source_analysis.get("body_text", "")
        body_sentences = self._process_content(body_text)

        # Convert glossary to JSON
        glossary_entries = []
        for term, translation in self.glossary.items():
            glossary_entries.append({"term": term, "translation": translation})
        glossary_json = json.dumps(glossary_entries, indent=2, ensure_ascii=False)

        # Basic info we pass to the AI
        essential_data = {
            "service": self.service,
            "source_language": self.source_language,
            "target_language": self.target_language,
            "industry": self.industry,
            "url": self.scraped_content.get("url", ""),
            "seo": self.scraped_content.get("seo", {}),
            "accessibility": self.scraped_content.get("accessibility", {}),
            "localization": self.scraped_content.get("localization", {}),
            "scores": self.scraped_content.get("scores", {}),
            "word_count": self.scraped_content.get("word_count", 0),
            "segments": self.scraped_content.get("segments", 0),
            "source_words": self.scraped_content.get("source_words", []),
            "analytical_score_data": self.scraped_content.get("analytical_score_data", {}),
            "body_sentences": body_sentences,
            "faq_content": faq_content
        }
        essential_data_json = json.dumps(essential_data, indent=2)

        source_analysis_json = (
            json.dumps(source_analysis, indent=2) if source_analysis else "{}"
        )
        target_analysis_json = (
            json.dumps(target_analysis, indent=2) if target_analysis else "{}"
        )

        if is_single_url:
            # SINGLE URL PROMPT
            prompt = f"""
You are an AI assistant analyzing a SINGLE website (monolingual) in {self.source_language}.
We want to produce a list of all content from the source, along with their AI translation into {self.target_language}.
We do NOT need error categories or LQA data for single-url. Instead, we only have "report_table" that contains the list of all content, each with "sourceContent" and "aiTranslation". We also have "ai_solutions".

IMPORTANT CONTENT PROCESSING RULES:
1. Each entry in the report_table should be a single, complete sentence or meaningful phrase
2. Long paragraphs should be split into individual sentences
3. For FAQ content:
   - Each question should be a separate entry
   - Each answer title should be a separate entry
   - Each answer sentence should be a separate entry
   - Maintain the logical connection between questions, titles, and answers
   - Answer titles should be treated as distinct entries, not merged with answer content
4. Preserve the original meaning while keeping sentences concise and clear
5. Remove any HTML formatting or special characters
6. Ensure translations are natural and fluent in the target language
7. Keep answer titles as standalone entries in the report table

-- Source Analysis --
{source_analysis_json}

-- Glossary JSON --
{glossary_json}

-- Essential Data --
{essential_data_json}

INSTRUCTIONS (SINGLE URL):
1. Process ALL the source content, including:
   - Main content (split into sentences)
   - FAQ questions and their complete answers (as separate entries)
   - Headers and important text
2. For each content piece:
   - Clean any remaining HTML or special characters
   - Split long content into natural sentences
   - Generate "aiTranslation" in {self.target_language}
3. The final JSON must ONLY have these fields (no extra commentary):
   {{
     "url": "string",
     "is_single_url": bool,
     "word_count": int,
     "report_table": [
       {{
         "id": "string",
         "sourceContent": "string",
         "aiTranslation": "string"
       }}
     ],
     "ai_solutions": [
       {{
         "id": "string",
         "title": "string",
         "description": "string",
         "benefits": ["string"],
         "example": "string",
         "industry_fit": "string"
       }}
     ],
     "common_issues": [
       {{
         "id": "string",
         "title": "string",
         "description": "string",
         "frequency": int,
         "impact": "High|Medium|Low",
         "category": "string"
       }}
     ],
     "metrics": {{
       "total_issues": int,
       "critical_issues": int,
       "improvement_rate": int,
       "quality_score": int,
       "industry_benchmark": {{
         "score": int,
         "percentile": int
       }}
     }}
   }}
4. Return ONLY that JSON. No explanations, no other fields.
5. IMPORTANT: Each entry in report_table should be a complete, self-contained sentence or meaningful phrase.
"""
        else:
            # DUAL URL PROMPT
            prompt = f"""
You are an AI assistant analyzing a DUAL website scenario. The source is in {self.source_language}, and the target is in {self.target_language}.
We DO need to produce the full LQA structure, error categories, etc. 
We also have "aiTranslation" for each sourceContent line if needed.

-- Source Analysis --
{source_analysis_json}

-- Target Analysis --
{target_analysis_json}

-- Glossary JSON --
{glossary_json}

-- Essential Data --
{essential_data_json}

Translation Glossary Reference:
{glossary_json}

Your task: Analyze this {self.industry} website pair, focusing on {self.service} in the context of {self.source_language}→{self.target_language} translation.
Use the provided translation glossary to verify terminology consistency.

CRITICAL INSTRUCTIONS:
1. In your final JSON "report_table", use these EXACT fields:
   - "sourceContent": the original {self.source_language} text
   - "targetContent": the original {self.target_language} text
   - "aiTranslation": your best direct translation of "sourceContent" from {self.source_language} to {self.target_language} (helpful if "targetContent" is incorrect or for clarity)
   - "enhancedContent": the improved {self.target_language} text
   - "errorCategory": the category of the issue
   - "errorSeverity": severity level of the issue (Critical, Major, Minor, or Neutral)
   - "errorDescription": description of the issue
2. DO NOT alter "sourceContent" or "targetContent". Only "enhancedContent" is improved {self.target_language} text.
3. "aiTranslation" is a direct translation from {self.source_language} → {self.target_language}, even if "targetContent" is present.
4. Include all categories of errors if any appear: Terminology, Accuracy, Linguistic, Style, Locale, Cultural, Design.
5. Use the translation glossary to verify terminology consistency.
6. IMPORTANT: The total number of errors in error_category_severity MUST match the number of entries in report_table. For each error counted in error_category_severity, there must be a corresponding entry in report_table with matching errorCategory and errorSeverity.

"""
        # COMMON JSON STRUCTURE (with "aiTranslation" in report_table and "is_single_url" at top-level).
        prompt += """
7. Return ONLY the final JSON object, no extra commentary.

**Mandatory JSON structure**:
{{
  "url": "string",
  "is_single_url": bool,
  "word_count": int,
  "error_category_severity": [
    { "category": "Terminology", "Critical": int, "Major": int, "Minor": int, "Neutral": int },
    { "category": "Accuracy", "Critical": int, "Major": int, "Minor": int, "Neutral": int },
    { "category": "Linguistic", "Critical": int, "Major": int, "Minor": int, "Neutral": int },
    { "category": "Style", "Critical": int, "Major": int, "Minor": int, "Neutral": int },
    { "category": "Locale", "Critical": int, "Major": int, "Minor": int, "Neutral": int },
    { "category": "Cultural", "Critical": int, "Major": int, "Minor": int, "Neutral": int },
    { "category": "Design", "Critical": int, "Major": int, "Minor": int, "Neutral": int }
  ],
  "lqa_score": {
    "severity_labels": ["Critical", "Major", "Minor", "Neutral"],
    "severity_data": [int, int, int, int],
    "category_labels": ["Terminology", "Accuracy", "Linguistic", "Style", "Locale", "Cultural", "Design"],
    "category_data": [int, int, int, int, int, int, int]
  },
  "report_table": [
    {
      "id": "string",
      "errorCategory": "string",
      "errorSeverity": "string",
      "errorDescription": "string",
      "sourceContent": "string",
      "targetContent": "string",
      "aiTranslation": "string",
      "enhancedContent": "string"
    }
  ],
  "industry_specific_analysis": {
    "seo_recommendations": [
      {
        "title": "string",
        "description": "string",
        "industry_relevance": "string",
        "priority": "High|Medium|Low"
      }
    ],
    "localization_insights": [
      {
        "title": "string",
        "description": "string",
        "language_pair_specific": true,
        "cultural_considerations": "string"
      }
    ]
  },
  "common_issues": [
    {
      "id": "string",
      "title": "string",
      "description": "string",
      "frequency": int,
      "impact": "High|Medium|Low",
      "category": "string"
    }
  ],
  "ai_solutions": [
    {
      "id": "string",
      "title": "string",
      "description": "string",
      "benefits": ["string"],
      "example": "string",
      "industry_fit": "string"
    }
  ],
  "metrics": {
    "total_issues": int,
    "critical_issues": int,
    "improvement_rate": int,
    "quality_score": int,
    "industry_benchmark": {
      "score": int,
      "percentile": int
    }
  }
}

Return ONLY this JSON. Do not include any extra commentary.
"""
        return prompt