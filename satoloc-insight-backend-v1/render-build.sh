#!/bin/bash

# Render build script for Django backend
set -o errexit  # exit on error

echo "Starting Render build process..."

# Install Python dependencies
echo "Installing Python dependencies..."
pip install --upgrade pip
pip install -r requirements.txt

# Collect static files
echo "Collecting static files..."
python manage.py collectstatic --noinput --clear

# Run database migrations
echo "Running database migrations..."
python manage.py migrate --noinput

# Create cache table (if using database cache)
echo "Creating cache table..."
python manage.py createcachetable || echo "Cache table creation failed or already exists"

echo "Build process completed successfully!" 