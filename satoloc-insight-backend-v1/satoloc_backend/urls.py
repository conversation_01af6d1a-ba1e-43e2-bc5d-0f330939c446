# satoloc_backend/urls.py
"""
URL configuration for satoloc_backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.http import HttpResponse


def health_check(request):
    return HttpResponse("OK")


urlpatterns = [
    path("admin/", admin.site.urls),
    path(
        "api/",
        include(
            [
                path("auth/", include("users.urls")),
                path("scraping/", include("scraping.urls")),
                path("insights/", include("insights.urls")),
                path("ai-assistant/", include("ai_assistant.urls")),
                path("advance-seo/", include("advance_seo.urls")),
                path("ai/", include("ai_content.urls")),
                path("health/", health_check, name="health_check"),
                path("wp-content-sync/", include("wp_content_sync.urls")),
                path("custom-content/", include("custom_content.urls")),
                path("payments/", include("payments.urls")),
            ]
        ),
    ),
] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

# Add these handlers for development
if settings.DEBUG:
    from django.views.generic import RedirectView

    urlpatterns += [
        path("", RedirectView.as_view(url="/admin/")),
    ]
