# satoloc_backend/settings.py

import os
from pathlib import Path
from datetime import timedelta
import urllib3
from dotenv import load_dotenv


# Only load .env if it exists and the environment is not production
if os.getenv("DJANGO_ENV") != "production":
    load_dotenv()

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get("DJANGO_SECRET_KEY", "your-secret-key-here")

# Crawl4AI settings
CRAWL4AI_API_KEY = os.environ.get("CRAWL4AI_API_KEY", "your-crawl4ai-api-key")
CRAWL4AI_PROJECT_ID = os.environ.get("CRAWL4AI_PROJECT_ID", "")
CRAWL4AI_MAX_PAGES = int(os.environ.get("CRAWL4AI_MAX_PAGES", "100"))
CRAWL4AI_TIMEOUT_MINUTES = int(os.environ.get("CRAWL4AI_TIMEOUT_MINUTES", "30"))
CRAWL4AI_SERVICE_URL = os.environ.get("CRAWL4AI_SERVICE_URL", "http://localhost:8001")


def str_to_bool(value):
    return str(value).lower() in ("true", "1", "t", "yes")


# SECURITY WARNING: don't run with debug turned on in production!

DEBUG = str_to_bool(os.environ.get("DEBUG", "False"))

# Base allowed hosts
ALLOWED_HOSTS = [
    "localhost",
    "127.0.0.1",
    ".elasticbeanstalk.com",
    ".amazonaws.com",
    ".onrender.com",
    "satoloc-insight-django-be-v1.onrender.com",
    "staging-satoloc-insight-backend.onrender.com",  # Staging backend
    "satoloc-insight-frontend-ffft67haj-satoloc-insight.vercel.app",
    "satolocinsight.com",
    "staging.satolocinsight.com",  # Staging frontend
]

# Add additional allowed hosts from environment variable
ADDITIONAL_ALLOWED_HOSTS = os.environ.get("ADDITIONAL_ALLOWED_HOSTS", "")
if ADDITIONAL_ALLOWED_HOSTS:
    ALLOWED_HOSTS.extend(
        [host.strip() for host in ADDITIONAL_ALLOWED_HOSTS.split(",") if host.strip()]
    )

# Important: Add ROOT_URLCONF setting
ROOT_URLCONF = "satoloc_backend.urls"

# Application definition
INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # Third-party apps
    "rest_framework",
    "rest_framework_simplejwt",
    "corsheaders",
    "django_celery_results",
    "django_celery_beat",
    # Local apps
    "users",
    # "scraping",
    "scraping.apps.ScrapingConfig",
    "insights",
    "ai_assistant",
    "advance_seo.apps.AdvanceSeoConfig",
    "ai_content.apps.AiContentConfig",
    "wp_content_sync.apps.WpContentSyncConfig",
    "custom_content.apps.CustomContentConfig",
    "payments.apps.PaymentsConfig",
]

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [os.path.join(BASE_DIR, "templates")],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "satoloc_backend.wsgi.application"

# Database
# DATABASES = {
#     "default": {
#         "ENGINE": "django.db.backends.postgresql",
#         "NAME": os.environ.get("DB_NAME", "satoloc"),
#         "USER": os.environ.get("DB_USER", "postgres"),
#         "PASSWORD": os.environ.get("DB_PASSWORD", "fucxaj-kymvYk-rakwa4"),
#         "HOST": os.environ.get("DB_HOST", "localhost"),
#         "PORT": os.environ.get("DB_PORT", "5432"),
#     }
# }
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": os.environ.get("DB_NAME"),
        "USER": os.environ.get("DB_USER"),
        "PASSWORD": os.environ.get("DB_PASSWORD"),
        "HOST": os.environ.get("DB_HOST"),
        "PORT": os.environ.get("DB_PORT", "5432"),
        "DATABASE_URL": os.environ.get("DATABASE_URL"),
    }
}

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# OpenAI settings
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

# Make sure to validate the API key is set
if not OPENAI_API_KEY:
    print("Warning: OPENAI_API_KEY is not set in environment variables!")

# AWS S3 Configuration
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
AWS_STORAGE_BUCKET_NAME = os.getenv("AWS_STORAGE_BUCKET_NAME", "satoloc-insight-images")
AWS_S3_REGION_NAME = os.getenv("AWS_S3_REGION_NAME", "eu-north-1")
AWS_S3_CUSTOM_DOMAIN = f"{AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com"

# Validate AWS configuration
if not AWS_ACCESS_KEY_ID:
    print("Warning: AWS_ACCESS_KEY_ID is not set in environment variables!")
if not AWS_SECRET_ACCESS_KEY:
    print("Warning: AWS_SECRET_ACCESS_KEY is not set in environment variables!")

# Internationalization
LANGUAGE_CODE = "en-us"
TIME_ZONE = "UTC"
USE_I18N = True
USE_TZ = True

# Static files configuration
STATIC_URL = "/static/"
STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles")
STATICFILES_DIRS = [os.path.join(BASE_DIR, "static")]
# Whitenoise configuration
STATICFILES_STORAGE = "whitenoise.storage.CompressedManifestStaticFilesStorage"

# Media files configuration
MEDIA_URL = "/media/"
MEDIA_ROOT = os.path.join(BASE_DIR, "mediafiles")

# Default primary key field type
DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# Custom user model
AUTH_USER_MODEL = "users.CustomUser"

USER_AGENT = "YourCustomUserAgent/1.0"

# REST Framework settings
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework_simplejwt.authentication.JWTAuthentication",
    ),
    "DEFAULT_PERMISSION_CLASSES": (
        "rest_framework.permissions.IsAuthenticated",  # Default to requiring authentication
    ),
}

# JWT settings
SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(hours=12),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=7),
    "ROTATE_REFRESH_TOKENS": True,
    "BLACKLIST_AFTER_ROTATION": True,
}

# Use REDIS_URL directly for both broker and cache
REDIS_URL = os.environ.get("REDIS_URL")

# Celery Configuration
CELERY_BROKER_URL = os.environ.get("REDIS_URL")
CELERY_RESULT_BACKEND = os.environ.get(
    "REDIS_URL"
)  # Update to use Redis as the result backend
CELERY_ACCEPT_CONTENT = ["json"]
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_SERIALIZER = "json"

# Fix for macOS fork issue with Objective-C runtime
# Use 'solo' pool on macOS to avoid fork-related issues
import platform

if platform.system() == "Darwin":
    CELERY_WORKER_POOL = "solo"
    # Alternative: CELERY_WORKER_POOL = 'prefork'
    # CELERY_WORKER_POOL_RESTARTS = True

# Celery Task Settings
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT = 30 * 60  # 30 minutes
CELERY_TASK_SOFT_TIME_LIMIT = 25 * 60  # 25 minutes
CELERY_WORKER_MAX_TASKS_PER_CHILD = 50
CELERY_WORKER_PREFETCH_MULTIPLIER = 1
CELERY_TASK_ACKS_LATE = True

# Redis Cache Configuration
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": os.environ.get("REDIS_URL"),
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "CONNECTION_POOL_KWARGS": {"max_connections": 100},
            "SOCKET_CONNECT_TIMEOUT": 5,
            "SOCKET_TIMEOUT": 5,
            "RETRY_ON_TIMEOUT": True,
        },
    }
}

# Redis Settings
REDIS_URL = os.environ.get("REDIS_URL")

# Redis-specific settings
REDIS_HOST = os.environ.get("REDIS_HOST", "red-ct8a92a3esus738872t0.render.com")
REDIS_PORT = os.environ.get("REDIS_PORT", "6379")

# Authentication Backends
AUTHENTICATION_BACKENDS = [
    "django.contrib.auth.backends.ModelBackend",
]

# CSRF Settings
CSRF_COOKIE_HTTPONLY = False  # Only if you need to access the CSRF token via JavaScript

# CORS settings
if DEBUG:
    CORS_ALLOW_ALL_ORIGINS = True
else:
    # Base CORS allowed origins
    CORS_ALLOWED_ORIGINS = [
        "https://satoloc-insight-frontend.vercel.app",
        "https://satoloc-insight-frontend-ffft67haj-satoloc-insight.vercel.app",
        "https://satoloc-insight-frontend-git-staging-v2-satoloc-insight.vercel.app",
        "https://satolocinsight.com",
        "https://staging.satolocinsight.com",  # Staging frontend
        "http://localhost:3000",
        "https://www.satolocinsight.com",
        "http://www.satolocinsight.com",
    ]

    # Add additional CORS origins from environment variable
    ADDITIONAL_CORS_ORIGINS = os.environ.get("ADDITIONAL_CORS_ORIGINS", "")
    if ADDITIONAL_CORS_ORIGINS:
        CORS_ALLOWED_ORIGINS.extend(
            [
                origin.strip()
                for origin in ADDITIONAL_CORS_ORIGINS.split(",")
                if origin.strip()
            ]
        )

CORS_ALLOW_CREDENTIALS = True

CORS_ALLOW_METHODS = [
    "DELETE",
    "GET",
    "OPTIONS",
    "PATCH",
    "POST",
    "PUT",
]

CORS_ALLOW_HEADERS = [
    "accept",
    "accept-encoding",
    "authorization",
    "content-type",
    "dnt",
    "origin",
    "user-agent",
    "x-csrftoken",
    "x-requested-with",
]

# Make sure these are in your MIDDLEWARE setting
MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",  # Must be right after SecurityMiddleware
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",  # Required for admin
    "django.contrib.messages.middleware.MessageMiddleware",  # Required for admin
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

# Google OAuth2 Configuration
GOOGLE_OAUTH2_CLIENT_CONFIG = {
    "web": {
        "client_id": os.environ.get("GOOGLE_OAUTH2_CLIENT_ID"),
        "client_secret": os.environ.get("GOOGLE_OAUTH2_CLIENT_SECRET"),
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
    }
}

GOOGLE_OAUTH2_REDIRECT_URI = os.environ.get(
    "GOOGLE_OAUTH2_REDIRECT_URI", "http://localhost:8000/seo/oauth2callback"
)

# Add logging configuration
# satoloc_backend/settings.py

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
        },
    },
    "root": {
        "handlers": ["console"],
        "level": "INFO",
    },
    "loggers": {
        "django": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "scraping": {
            "handlers": ["console"],
            "level": "DEBUG",
            "propagate": False,
        },
        "celery": {
            "handlers": ["console"],
            "level": "DEBUG",
            "propagate": True,
        },
    },
}

# Create necessary directories
os.makedirs(os.path.join(BASE_DIR, "templates"), exist_ok=True)
os.makedirs(os.path.join(BASE_DIR, "static"), exist_ok=True)
os.makedirs(os.path.join(BASE_DIR, "media"), exist_ok=True)
os.makedirs(os.path.join(BASE_DIR, "staticfiles"), exist_ok=True)

# Email configuration
EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
EMAIL_HOST = os.environ.get("OUTGOING_MAIL_SERVER", "smtp.hostinger.com")
EMAIL_PORT = int(os.environ.get("PORT", "465"))
EMAIL_USE_SSL = True  # Use SSL for port 465
EMAIL_USE_TLS = False  # Don't use TLS when using SSL
EMAIL_HOST_USER = os.environ.get("EMAIL_USER", "<EMAIL>")
EMAIL_HOST_PASSWORD = os.environ.get("EMAIL_PASS", "")
DEFAULT_FROM_EMAIL = os.environ.get("EMAIL_USER", "<EMAIL>")

# Email settings for notifications
ADMIN_EMAIL = os.environ.get("EMAIL_USER", "<EMAIL>")

# Email timeout settings
EMAIL_TIMEOUT = 30

# Google OAuth2 Settings
GOOGLE_OAUTH2_CLIENT_ID = os.environ.get("GOOGLE_CLIENT_ID")
GOOGLE_OAUTH2_CLIENT_SECRET = os.environ.get("GOOGLE_CLIENT_SECRET")

# Google OAuth2 Configuration
GOOGLE_OAUTH2_CONFIG = {
    "web": {
        "client_id": GOOGLE_OAUTH2_CLIENT_ID,
        "project_id": os.environ.get("GOOGLE_PROJECT_ID", "satoloc-insight"),
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_secret": GOOGLE_OAUTH2_CLIENT_SECRET,
        "redirect_uris": [
            "http://localhost:3000/auth/google/callback",
            "http://localhost:8000/auth/google/callback",
            "https://satolocinsight.com/auth/google/callback",
            "https://www.satolocinsight.com/auth/google/callback",
        ],
    }
}

# Google OAuth2 Scopes
GOOGLE_OAUTH2_SCOPES = [
    "openid",
    "https://www.googleapis.com/auth/userinfo.email",
    "https://www.googleapis.com/auth/userinfo.profile",
    "https://www.googleapis.com/auth/webmasters.readonly",  # For GSC integration later
]

# Security settings for OAuth2
GOOGLE_OAUTH2_STATE_LENGTH = 32
GOOGLE_OAUTH2_NONCE_LENGTH = 32

# Validate Google OAuth2 configuration
if not GOOGLE_OAUTH2_CLIENT_ID:
    print("Warning: GOOGLE_CLIENT_ID is not set in environment variables!")
if not GOOGLE_OAUTH2_CLIENT_SECRET:
    print("Warning: GOOGLE_CLIENT_SECRET is not set in environment variables!")

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY = os.environ.get("STRIPE_PUBLISHABLE_KEY")
STRIPE_SECRET_KEY = os.environ.get("STRIPE_SECRET_KEY")
STRIPE_WEBHOOK_SECRET = os.environ.get("STRIPE_WEBHOOK_SECRET")

# Validate Stripe configuration
if not STRIPE_SECRET_KEY:
    print("Warning: STRIPE_SECRET_KEY is not set in environment variables!")
if not STRIPE_PUBLISHABLE_KEY:
    print("Warning: STRIPE_PUBLISHABLE_KEY is not set in environment variables!")
if not STRIPE_WEBHOOK_SECRET:
    print("Warning: STRIPE_WEBHOOK_SECRET is not set in environment variables!")
