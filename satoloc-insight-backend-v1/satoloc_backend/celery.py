# satoloc_backend/celery.py
import os
from celery import Celery

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "satoloc_backend.settings")

# Fix for macOS fork issue with Objective-C runtime
# This must be set before creating the app instance
os.environ.setdefault("FORKED_BY_MULTIPROCESSING", "1")

# Create Celery app
app = Celery("satoloc_backend")

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object("django.conf:settings", namespace="CELERY")

# Load task modules from all registered Django app configs.
app.autodiscover_tasks()

# Set the multiprocessing start method to 'spawn' on macOS to avoid fork issues
if os.uname().sysname == "Darwin":  # Check if running on macOS
    from multiprocessing import set_start_method

    try:
        set_start_method("spawn")
    except RuntimeError:
        # Method already set
        pass


@app.task(bind=True)
def debug_task(self):
    print(f"Request: {self.request!r}")
