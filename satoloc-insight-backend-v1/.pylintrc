[MASTER]
# Load Django plugin
load-plugins=pylint_django
# Python code to execute, usually for sys.path manipulation such as
# pygtk.require().
init-hook='import sys; import os; sys.path.append(os.path.dirname(os.path.abspath(__file__)))'
# Add the external modules that <PERSON><PERSON><PERSON> should recognize
extension-pkg-whitelist=crawl4ai

[MESSAGES CONTROL]
# Disable specific warnings
disable=C0111,  # missing-docstring
       C0103,   # invalid-name
       C0114,   # missing-module-docstring
       C0301,   # line-too-long
       C0303,   # trailing-whitespace
       E0013,   # too-many-ancestors
       E0015,   # bad-super-call
       W0611,   # unused-import
       R0903,   # too-few-public-methods
       R0913,   # too-many-arguments
       R0912,   # too-many-branches
       R0914,   # too-many-locals
       R0915,   # too-many-statements
       W0231,   # super-init-not-called
       W0223,   # abstract-method
       broad-except  # too-broad-exception-caught

[FORMAT]
# Maximum number of characters on a single line
max-line-length=165

# Maximum number of lines in a module
max-module-lines=2000

[BASIC]
# Regular expressions for naming styles
good-names=i,j,k,ex,Run,_,pk,id,up
# Regular expression which should only match function or class names
function-rgx=[a-z_][a-z0-9_]{2,50}$
variable-rgx=[a-z_][a-z0-9_]{2,50}$
const-rgx=(([A-Z_][A-Z0-9_]*)|(__.*__))$
attr-rgx=[a-z_][a-z0-9_]{2,50}$
argument-rgx=[a-z_][a-z0-9_]{2,50}$
class-rgx=[A-Z_][a-zA-Z0-9]+$
module-rgx=(([a-z_][a-z0-9_]*)|([A-Z][a-zA-Z0-9]+))$

[TYPECHECK]
# List of module names for which member attributes should not be checked
ignored-modules=numpy,tensorflow,torch,cv2,django.db.models.fields,crawl4ai
# List of classes names for which member attributes should not be checked
ignored-classes=SQLObject,WSGIRequest,Request,Response,ScrapedData,Project
# List of members which are set dynamically and missed by pylint inference system
generated-members=REQUEST,acl_users,aq_parent,objects,_meta,id,[a-zA-Z]+_set,DoesNotExist,MultipleObjectsReturned

[VARIABLES]
# A regular expression matching names of dummy variables
dummy-variables-rgx=_|dummy|unused|i|j|k|x|y|z|e|f

# List of additional names to consider as builtins
additional-builtins=

[SIMILARITIES]
# Minimum lines number of a similarity
min-similarity-lines=10

# Ignore imports when computing similarities
ignore-imports=yes

[MISCELLANEOUS]
# List of note tags to take into consideration
notes=FIXME,XXX,TODO

[IMPORTS]
# Allow wildcard imports from modules that define __all__
allow-wildcard-with-all=yes

[DESIGN]
# Maximum number of arguments for function / method
max-args=10

# Maximum number of locals for function / method body
max-locals=20

# Maximum number of return / yield for function / method body
max-returns=10

# Maximum number of branch for function / method body
max-branches=20

# Maximum number of statements in function / method body
max-statements=50

# Maximum number of parents for a class
max-parents=7

# Maximum number of attributes for a class
max-attributes=10

# Minimum number of public methods for a class
min-public-methods=0

# Maximum number of public methods for a class
max-public-methods=20

[CLASSES]
# List of method names used to declare (i.e. assign) instance attributes
defining-attr-methods=__init__,__new__,setUp
# List of valid names for the first argument in a class method
valid-classmethod-first-arg=cls
# List of valid names for the first argument in a metaclass class method
valid-metaclass-classmethod-first-arg=mcs

[DJANGO]
# List of module names where Django settings are located
django-settings-module=satoloc_backend.settings

# List of known third party modules
known-third-party=crawl4ai

[REPORTS]
# Set the output format
output-format=text

# Include a brief explanation of each error
msg-template={path}:{line}: [{msg_id}({symbol}), {obj}] {msg}