import logging
from django.core.management.base import BaseCommand
from advance_seo.models import Website
from advance_seo.services import TechnicalAnalysisService

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Fetch technical analysis data for a website using DataForSEO API"

    def add_arguments(self, parser):
        parser.add_argument(
            "url", type=str, help="URL of the website to fetch technical analysis for"
        )

    def handle(self, *args, **options):
        url = options["url"]

        self.stdout.write(
            self.style.SUCCESS(f"Fetching technical analysis for {url}...")
        )

        try:
            # Get or create the website
            website, created = Website.objects.get_or_create(url=url)

            # Create a service instance
            service = TechnicalAnalysisService()

            # Process technical analysis
            tech_analysis = service.process_technical_analysis(website)

            if tech_analysis:
                # Display results
                self.stdout.write(
                    self.style.SUCCESS(f"Technical analysis for {website.url}:")
                )
                self.stdout.write(f"  Page Speed: {tech_analysis.page_speed}")
                self.stdout.write(
                    f"  Mobile Optimization: {tech_analysis.mobile_optimization}"
                )
                self.stdout.write(f"  Core Web Vitals: {tech_analysis.core_web_vitals}")
                self.stdout.write(f"  HTTPS Security: {tech_analysis.https_security}")
                self.stdout.write(f"  Canonical URL: {tech_analysis.canonical_url}")
                self.stdout.write(f"  Schema Markup: {tech_analysis.schema_markup}")

                self.stdout.write(self.style.SUCCESS("Done!"))
            else:
                self.stdout.write(
                    self.style.WARNING(
                        f"Failed to retrieve technical analysis for {url}"
                    )
                )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error fetching technical analysis: {e}")
            )
            logger.exception(e)
