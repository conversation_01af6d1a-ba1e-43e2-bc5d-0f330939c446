import csv
from django.core.management.base import BaseCommand
from advance_seo.models import Website, Competitor, CompetitorKeyword

class Command(BaseCommand):
    help = 'Import competitors from CSV file for yapikredi.com.tr'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--manual',
            action='store_true',
            help='Use manually defined competitors',
        )
    
    def handle(self, *args, **options):
        website_url = "yapikredi.com.tr"
        
        # Get the website
        try:
            website = Website.objects.get(url=website_url)
        except Website.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"Website {website_url} not found"))
            return
        
        # Clear existing competitors
        competitors_count = Competitor.objects.filter(website=website).count()
        Competitor.objects.filter(website=website).delete()
        self.stdout.write(self.style.SUCCESS(f"Cleared {competitors_count} existing competitors for {website_url}"))
        
        if options['manual']:
            self.import_manual_competitors(website)
        else:
            self.import_from_csv(website)
            
    def import_from_csv(self, website):
        csv_file = "csv_files/www.yapikredi.com.tr_orgcompetitors_subdomai_2025-03-13_15-19-48.csv"
        
        # Try different encodings
        encodings = ['utf-8', 'latin-1', 'cp1252', 'utf-16']
        
        for encoding in encodings:
            try:
                with open(csv_file, 'r', encoding=encoding) as f:
                    reader = csv.DictReader(f, delimiter='\t')
                    
                    added_count = 0
                    for i, row in enumerate(reader):
                        domain = row['Domain']
                        rank = i + 1  # Rank based on order in CSV
                        
                        # Create the competitor
                        competitor, created = Competitor.objects.update_or_create(
                            website=website,
                            target_url=f"https://{domain}",
                            defaults={
                                'name': domain,
                                'description': f"Competitor of {website.url} with {row['Common keywords']} common keywords",
                                'rank': rank
                            }
                        )
                        
                        # Add some keywords based on the domain
                        keywords = [
                            f"{domain} banking",
                            f"{domain} online",
                            f"{domain} account",
                            f"{domain} login",
                            f"{domain} services"
                        ]
                        
                        for keyword in keywords:
                            CompetitorKeyword.objects.create(
                                competitor=competitor,
                                keyword=keyword
                            )
                        
                        added_count += 1
                        self.stdout.write(f"Added competitor {domain} with rank {rank}")
                    
                    self.stdout.write(self.style.SUCCESS(f"Successfully imported {added_count} competitors for {website.url} using {encoding} encoding"))
                    return
            except UnicodeDecodeError:
                self.stdout.write(f"Could not decode with {encoding} encoding, trying next...")
            except FileNotFoundError:
                self.stdout.write(self.style.ERROR(f"CSV file not found: {csv_file}"))
                return
        
        # If all encodings fail
        self.import_manual_competitors(website)
    
    def import_manual_competitors(self, website):
        """
        Import manually defined competitors
        """
        competitors = [
            {"domain": "garantibbva.com.tr", "rank": 1, "common_keywords": "102616"},
            {"domain": "isbank.com.tr", "rank": 2, "common_keywords": "99509"},
            {"domain": "akbank.com", "rank": 3, "common_keywords": "89874"},
            {"domain": "vakifbank.com.tr", "rank": 4, "common_keywords": "79328"},
            {"domain": "hangikredi.com", "rank": 5, "common_keywords": "84257"},
            {"domain": "ziraatbank.com.tr", "rank": 6, "common_keywords": "65220"},
            {"domain": "denizbank.com", "rank": 7, "common_keywords": "56712"},
            {"domain": "teb.com.tr", "rank": 8, "common_keywords": "49405"},
            {"domain": "ing.com.tr", "rank": 9, "common_keywords": "45491"},
            {"domain": "worldcard.com.tr", "rank": 10, "common_keywords": "10653"}
        ]
        
        added_count = 0
        for comp in competitors:
            domain = comp["domain"]
            rank = comp["rank"]
            common_keywords = comp["common_keywords"]
            
            # Create the competitor
            competitor, created = Competitor.objects.update_or_create(
                website=website,
                target_url=f"https://{domain}",
                defaults={
                    'name': domain,
                    'description': f"Competitor of {website.url} with {common_keywords} common keywords",
                    'rank': rank
                }
            )
            
            # Add some keywords based on the domain
            keywords = [
                f"{domain} banking",
                f"{domain} online",
                f"{domain} account",
                f"{domain} login",
                f"{domain} services"
            ]
            
            for keyword in keywords:
                CompetitorKeyword.objects.create(
                    competitor=competitor,
                    keyword=keyword
                )
            
            added_count += 1
            self.stdout.write(f"Added competitor {domain} with rank {rank}")
        
        self.stdout.write(self.style.SUCCESS(f"Successfully imported {added_count} competitors for {website.url} using manual data")) 