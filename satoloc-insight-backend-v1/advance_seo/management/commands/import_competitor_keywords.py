import csv
from django.core.management.base import BaseCommand
from advance_seo.models import Website, Competitor, CompetitorKeyword

class Command(BaseCommand):
    help = 'Import keywords for specific competitors from CSV files'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--competitor',
            type=str,
            help='Competitor domain to import keywords for (e.g., akbank.com)',
        )
        parser.add_argument(
            '--csv_file',
            type=str,
            help='Path to CSV file containing keywords',
        )
        parser.add_argument(
            '--limit',
            type=int,
            default=100,
            help='Maximum number of keywords to import per competitor (default: 100)',
        )

    def handle(self, *args, **options):
        competitor_domain = options.get('competitor')
        csv_file_path = options.get('csv_file')
        limit = options.get('limit')
        
        if not competitor_domain or not csv_file_path:
            # Default behavior: import keywords for akbank.com and garantibbva.com.tr
            self.import_akbank_keywords(limit)
            self.import_garantibbva_keywords(limit)
        else:
            # Import keywords for the specified competitor
            self.import_competitor_keywords(competitor_domain, csv_file_path, limit)

    def import_competitor_keywords(self, competitor_domain, csv_file_path, limit):
        """Import keywords for a specific competitor from a CSV file"""
        try:
            # Find the competitor in the database
            competitor = Competitor.objects.get(name=competitor_domain)
        except Competitor.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"Competitor {competitor_domain} not found"))
            return
        
        # Delete existing keywords for this competitor
        existing_count = CompetitorKeyword.objects.filter(competitor=competitor).count()
        CompetitorKeyword.objects.filter(competitor=competitor).delete()
        self.stdout.write(f"Cleared {existing_count} existing keywords for {competitor_domain}")
        
        # Try different encodings
        encodings = ['utf-8', 'latin-1', 'cp1252', 'utf-16']
        
        for encoding in encodings:
            try:
                with open(csv_file_path, 'r', encoding=encoding) as f:
                    file_content = f.read()
                    
                    # Determine delimiter - we know it's comma from inspecting the file
                    delimiter = ','
                    
                    # Reset file pointer to beginning
                    f.seek(0)
                    
                    # Read the file as CSV
                    reader = csv.reader(f, delimiter=delimiter)
                    headers = next(reader)  # Read the header row
                    
                    # Find the index of the Keyword column (second column based on inspection)
                    keyword_index = 1  # Default to second column (Keyword)
                    volume_index = 2   # Default to third column (Volume)
                    
                    # Create a dictionary reader for easier access to columns
                    f.seek(0)
                    headers = next(csv.reader(f, delimiter=delimiter))
                    reader = csv.DictReader(f, fieldnames=headers, delimiter=delimiter)
                    next(reader)  # Skip the header row
                    
                    # Process keywords and sort by volume
                    keywords = []
                    for row in reader:
                        try:
                            keyword = row[headers[keyword_index]].strip()
                            # Some volumes have commas, so we need to handle that
                            volume_str = row[headers[volume_index]].replace(',', '')
                            volume = int(volume_str) if volume_str.isdigit() else 0
                            
                            if keyword and volume > 0:
                                keywords.append((keyword, volume))
                        except (IndexError, ValueError) as e:
                            continue
                    
                    # Sort keywords by volume (descending) and take the top ones
                    keywords.sort(key=lambda x: x[1], reverse=True)
                    top_keywords = keywords[:limit]
                    
                    # Add keywords to the database
                    added_count = 0
                    for keyword, volume in top_keywords:
                        CompetitorKeyword.objects.create(
                            competitor=competitor,
                            keyword=keyword
                        )
                        added_count += 1
                        self.stdout.write(f"Added keyword: {keyword} (volume: {volume})")
                    
                    self.stdout.write(self.style.SUCCESS(
                        f"Successfully imported {added_count} keywords for {competitor_domain}"
                    ))
                    return
            except UnicodeDecodeError:
                self.stdout.write(f"Could not decode with {encoding} encoding, trying next...")
            except FileNotFoundError:
                self.stdout.write(self.style.ERROR(f"CSV file not found: {csv_file_path}"))
                return
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error processing file: {str(e)}"))
                continue
                
        self.stdout.write(self.style.ERROR(f"Failed to import keywords for {competitor_domain} - could not process file"))

    def import_akbank_keywords(self, limit):
        """Import keywords for akbank.com"""
        csv_file = "csv_files/www.akbank.com-organic-keywords-subdomains-allg_2025-04-27_16-14-51 - Sheet1.csv"
        self.import_competitor_keywords("akbank.com", csv_file, limit)

    def import_garantibbva_keywords(self, limit):
        """Import keywords for garantibbva.com.tr"""
        csv_file = "csv_files/www.garantibbva.com.tr-organic-keywords-subdoma_2025-04-27_16-08-22 - Sheet1.csv"
        self.import_competitor_keywords("garantibbva.com.tr", csv_file, limit) 