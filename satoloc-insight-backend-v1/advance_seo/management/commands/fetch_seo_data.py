import logging
from django.core.management.base import BaseCommand
from advance_seo.models import Website
from advance_seo.services import SEODataService

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Fetch all SEO data (competitors, technical analysis, and SEO metrics) for a website using DataForSEO API"

    def add_arguments(self, parser):
        parser.add_argument(
            "url", type=str, help="URL of the website to fetch SEO data for"
        )
        parser.add_argument(
            "--industry", type=str, help="Industry of the website", default=None
        )
        parser.add_argument(
            "--language", type=str, help="Language of the website", default=None
        )
        parser.add_argument(
            "--skip-competitors",
            action="store_true",
            help="Skip fetching competitors data",
        )
        parser.add_argument(
            "--skip-technical",
            action="store_true",
            help="Skip fetching technical analysis data",
        )
        parser.add_argument(
            "--skip-metrics", action="store_true", help="Skip fetching SEO metrics data"
        )

    def handle(self, *args, **options):
        url = options["url"]
        industry = options["industry"]
        language = options["language"]
        skip_competitors = options["skip_competitors"]
        skip_technical = options["skip_technical"]
        skip_metrics = options["skip_metrics"]

        self.stdout.write(
            self.style.SUCCESS(f"Fetching comprehensive SEO data for {url}...")
        )

        try:
            # Create or get the website
            website, created = Website.objects.get_or_create(
                url=url, defaults={"industry": industry, "language": language}
            )

            if not created and (industry or language):
                # Update website if needed
                if industry:
                    website.industry = industry
                if language:
                    website.language = language
                website.save()

            # Create a service instance
            service = SEODataService()

            # Process SEO metrics
            if not skip_metrics:
                self.stdout.write(
                    self.style.SUCCESS(f"Fetching SEO metrics for {url}...")
                )
                seo_metrics = service.process_seo_metrics(website)

                if seo_metrics:
                    # Print the results
                    self.stdout.write(self.style.SUCCESS(f"SEO Metrics for {url}:"))
                    self.stdout.write(
                        f"  - Domain Rating: {seo_metrics.domain_rating_score}"
                    )
                    self.stdout.write(
                        f"  - Organic Traffic: {seo_metrics.organic_traffic_score}"
                    )
                    self.stdout.write(
                        f"  - Search Rankings: {seo_metrics.search_rankings_score}%"
                    )
                    self.stdout.write(
                        f"  - Search Terms: {seo_metrics.search_terms_score}"
                    )
                    self.stdout.write(f"  - Site Links: {seo_metrics.site_links_score}")
                    self.stdout.write(
                        f"  - Content Score: {seo_metrics.content_score}%"
                    )
                    self.stdout.write(
                        f"  - Backlinks Count: {seo_metrics.backlinks_count}"
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f"Failed to retrieve SEO metrics for {url}")
                    )

            # Process technical analysis
            if not skip_technical:
                self.stdout.write(
                    self.style.SUCCESS(f"Fetching technical analysis for {url}...")
                )
                tech_analysis = service.process_technical_analysis(website)

                if tech_analysis:
                    # Print the results
                    self.stdout.write(
                        self.style.SUCCESS(f"Technical Analysis for {url}:")
                    )
                    self.stdout.write(f"  - Page Speed: {tech_analysis.page_speed}")
                    self.stdout.write(
                        f"  - Mobile Optimization: {tech_analysis.mobile_optimization}"
                    )
                    self.stdout.write(
                        f"  - Core Web Vitals: {tech_analysis.core_web_vitals}"
                    )
                    self.stdout.write(
                        f"  - HTTPS Security: {tech_analysis.https_security}"
                    )
                    self.stdout.write(
                        f"  - Canonical URL: {tech_analysis.canonical_url}"
                    )
                    self.stdout.write(
                        f"  - Schema Markup: {tech_analysis.schema_markup}"
                    )

                    # Print technical issues
                    if hasattr(website, "technical_analysis"):
                        tech_analysis = website.technical_analysis
                        self.stdout.write(f"  - Page Speed: {tech_analysis.page_speed}")
                        self.stdout.write(
                            f"  - Mobile Optimization: {tech_analysis.mobile_optimization}"
                        )
                        self.stdout.write(
                            f"  - Core Web Vitals: {tech_analysis.core_web_vitals}"
                        )
                    else:
                        self.stdout.write(
                            self.style.SUCCESS("No technical analysis found.")
                        )
                else:
                    self.stdout.write(
                        self.style.WARNING(
                            f"Failed to retrieve technical analysis for {url}"
                        )
                    )

            # Process competitors
            if not skip_competitors:
                self.stdout.write(
                    self.style.SUCCESS(f"Fetching competitors for {url}...")
                )
                service.process_competitors(website)

                # Get the competitors
                competitors = website.competitors.all()

                # Print the results
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Found {competitors.count()} competitors for {url}:"
                    )
                )

                for competitor in competitors:
                    self.stdout.write(
                        f"  - {competitor.name} ({competitor.target_url}), Rank: {competitor.rank}"
                    )

                    # Print top keywords
                    keywords = competitor.top_keywords.all()
                    if keywords:
                        self.stdout.write("    Top keywords:")
                        for keyword in keywords[:5]:  # Show only the first 5 keywords
                            self.stdout.write(f"      - {keyword.keyword}")

            self.stdout.write(
                self.style.SUCCESS("All SEO data fetching completed successfully!")
            )

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error fetching SEO data: {e}"))
            logger.exception(e)
