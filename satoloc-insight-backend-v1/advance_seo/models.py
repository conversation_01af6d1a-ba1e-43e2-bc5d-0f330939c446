import json
from django.db import models
from django.utils import timezone
from django.conf import settings
from django.contrib.auth import get_user_model

User = get_user_model()


class Website(models.Model):
    """Model to store website information"""

    url = models.CharField(max_length=255, unique=True)
    industry = models.CharField(max_length=100, blank=True, null=True)
    language = models.CharField(max_length=50, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.url


class WebsiteLanguage(models.Model):
    """Model to store available languages for a website"""

    website = models.ForeignKey(
        Website, on_delete=models.CASCADE, related_name="available_languages"
    )
    language = models.CharField(max_length=50)

    class Meta:
        unique_together = ("website", "language")

    def __str__(self):
        return f"{self.website.url} - {self.language}"


class SEOMetrics(models.Model):
    """Model to store SEO metrics for a website"""

    website = models.OneToOneField(
        Website, on_delete=models.CASCADE, related_name="seo_metrics"
    )
    domain_rating_score = models.FloatField(null=True, blank=True)
    domain_rating_previous_score = models.FloatField(null=True, blank=True)
    organic_traffic_score = models.IntegerField(null=True, blank=True)
    organic_traffic_previous_score = models.IntegerField(null=True, blank=True)
    search_rankings_score = models.FloatField(null=True, blank=True)
    search_rankings_previous_score = models.FloatField(null=True, blank=True)
    search_terms_score = models.IntegerField(null=True, blank=True)
    search_terms_previous_score = models.IntegerField(null=True, blank=True)
    site_links_score = models.IntegerField(null=True, blank=True)
    site_links_previous_score = models.IntegerField(null=True, blank=True)
    content_score = models.FloatField(null=True, blank=True)
    content_score_previous_score = models.FloatField(null=True, blank=True)
    backlinks_count = models.IntegerField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"SEO Metrics for {self.website.url}"


class TechnicalAnalysis(models.Model):
    """Model to store technical analysis data for a website"""

    website = models.OneToOneField(
        Website, on_delete=models.CASCADE, related_name="technical_analysis"
    )
    page_speed = models.FloatField(null=True, blank=True)
    mobile_optimization = models.FloatField(null=True, blank=True)
    core_web_vitals = models.FloatField(null=True, blank=True)
    local_schema = models.BooleanField(default=False)
    https_security = models.BooleanField(default=False)
    canonical_url = models.BooleanField(default=False)
    hreflang_implementation = models.BooleanField(default=False)
    schema_markup = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Technical Analysis for {self.website.url}"


class CoreWebVitalsAnalysis(models.Model):
    """Model to store Core Web Vitals analysis data"""

    STRATEGY_CHOICES = [
        ("mobile", "Mobile"),
        ("desktop", "Desktop"),
    ]

    website = models.ForeignKey(
        Website, on_delete=models.CASCADE, related_name="core_web_vitals_analyses"
    )
    strategy = models.CharField(max_length=10, choices=STRATEGY_CHOICES)

    # Analysis metadata
    analysis_timestamp = models.DateTimeField()
    lighthouse_version = models.CharField(max_length=50, blank=True, null=True)

    # Performance scores
    performance_score = models.IntegerField(null=True, blank=True)  # 0-100
    accessibility_score = models.IntegerField(null=True, blank=True)  # 0-100
    best_practices_score = models.IntegerField(null=True, blank=True)  # 0-100
    seo_score = models.IntegerField(null=True, blank=True)  # 0-100

    # Core Web Vitals metrics (lab data)
    lcp_value = models.FloatField(
        null=True, blank=True
    )  # Largest Contentful Paint in milliseconds
    lcp_score = models.FloatField(null=True, blank=True)  # 0-1
    lcp_assessment = models.CharField(
        max_length=20, blank=True, null=True
    )  # good, needs-improvement, poor

    fid_value = models.FloatField(
        null=True, blank=True
    )  # First Input Delay in milliseconds
    fid_score = models.FloatField(null=True, blank=True)  # 0-1
    fid_assessment = models.CharField(max_length=20, blank=True, null=True)

    cls_value = models.FloatField(null=True, blank=True)  # Cumulative Layout Shift
    cls_score = models.FloatField(null=True, blank=True)  # 0-1
    cls_assessment = models.CharField(max_length=20, blank=True, null=True)

    fcp_value = models.FloatField(
        null=True, blank=True
    )  # First Contentful Paint in milliseconds
    fcp_score = models.FloatField(null=True, blank=True)  # 0-1
    fcp_assessment = models.CharField(max_length=20, blank=True, null=True)

    # Field data (Real User Monitoring) - can be null if no field data available
    lcp_field_value = models.FloatField(null=True, blank=True)
    lcp_field_category = models.CharField(max_length=20, blank=True, null=True)

    fid_field_value = models.FloatField(null=True, blank=True)
    fid_field_category = models.CharField(max_length=20, blank=True, null=True)

    cls_field_value = models.FloatField(null=True, blank=True)
    cls_field_category = models.CharField(max_length=20, blank=True, null=True)

    fcp_field_value = models.FloatField(null=True, blank=True)
    fcp_field_category = models.CharField(max_length=20, blank=True, null=True)

    # Additional metrics
    speed_index_value = models.FloatField(null=True, blank=True)
    speed_index_score = models.FloatField(null=True, blank=True)

    tti_value = models.FloatField(null=True, blank=True)  # Time to Interactive
    tti_score = models.FloatField(null=True, blank=True)

    tbt_value = models.FloatField(null=True, blank=True)  # Total Blocking Time
    tbt_score = models.FloatField(null=True, blank=True)

    # JSON fields for detailed data
    raw_data = models.JSONField(blank=True, null=True)  # Store complete raw response

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ["website", "strategy"]
        indexes = [
            models.Index(fields=["website", "strategy"]),
            models.Index(fields=["created_at"]),
        ]

    def __str__(self):
        return f"Core Web Vitals for {self.website.url} ({self.strategy})"


class PerformanceOpportunity(models.Model):
    """Model to store performance optimization opportunities"""

    analysis = models.ForeignKey(
        CoreWebVitalsAnalysis, on_delete=models.CASCADE, related_name="opportunities"
    )

    opportunity_id = models.CharField(max_length=100)  # e.g., 'unused-css-rules'
    title = models.CharField(max_length=255)
    description = models.TextField()
    score = models.FloatField(null=True, blank=True)  # 0-1
    display_value = models.CharField(
        max_length=255, blank=True, null=True
    )  # e.g., "Est savings of 115 KiB"

    # Savings information
    overall_savings_ms = models.IntegerField(null=True, blank=True)
    overall_savings_bytes = models.IntegerField(null=True, blank=True)

    # Detailed data
    details = models.JSONField(blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ["analysis", "opportunity_id"]

    def __str__(self):
        return f"{self.title} - {self.analysis.website.url}"


class PerformanceDiagnostic(models.Model):
    """Model to store performance diagnostic information"""

    analysis = models.ForeignKey(
        CoreWebVitalsAnalysis, on_delete=models.CASCADE, related_name="diagnostics"
    )

    diagnostic_id = models.CharField(
        max_length=100
    )  # e.g., 'mainthread-work-breakdown'
    title = models.CharField(max_length=255)
    description = models.TextField()
    score = models.FloatField(null=True, blank=True)  # 0-1
    display_value = models.CharField(max_length=255, blank=True, null=True)

    # Detailed data
    details = models.JSONField(blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ["analysis", "diagnostic_id"]

    def __str__(self):
        return f"{self.title} - {self.analysis.website.url}"


class Keyword(models.Model):
    """Model to store keywords for a website"""

    website = models.ForeignKey(
        Website, on_delete=models.CASCADE, related_name="keywords"
    )
    keyword = models.CharField(max_length=255)
    is_target = models.BooleanField(default=False)

    class Meta:
        unique_together = ("website", "keyword")

    def __str__(self):
        return f"{self.keyword} - {self.website.url}"


class Competitor(models.Model):
    """Model to store competitor information for a website"""

    website = models.ForeignKey(
        Website, on_delete=models.CASCADE, related_name="competitors"
    )
    name = models.CharField(max_length=255)
    target_url = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    rank = models.IntegerField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ("website", "target_url")

    def __str__(self):
        return f"{self.name} - competitor of {self.website.url}"


class CompetitorKeyword(models.Model):
    """Model to store top keywords for competitors"""

    competitor = models.ForeignKey(
        Competitor, on_delete=models.CASCADE, related_name="top_keywords"
    )
    keyword = models.CharField(max_length=255)

    def __str__(self):
        return f"{self.keyword} - {self.competitor.name}"


class RankingIssue(models.Model):
    """Model to store ranking issues identified for a competitor's content"""

    IMPACT_CHOICES = [
        ("high", "High"),
        ("medium", "Medium"),
        ("low", "Low"),
    ]

    competitor = models.ForeignKey(
        Competitor, on_delete=models.CASCADE, related_name="ranking_issues"
    )
    title = models.CharField(max_length=255)
    description = models.TextField()
    impact = models.CharField(max_length=10, choices=IMPACT_CHOICES, default="medium")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Issue: {self.title} - {self.competitor.name}"


class ContentRecommendation(models.Model):
    """Model to store content recommendations for a competitor"""

    IMPACT_CHOICES = [
        ("high", "High"),
        ("medium", "Medium"),
        ("low", "Low"),
    ]

    competitor = models.ForeignKey(
        Competitor, on_delete=models.CASCADE, related_name="content_recommendations"
    )
    title = models.CharField(max_length=255)
    description = models.TextField()
    impact = models.CharField(max_length=10, choices=IMPACT_CHOICES, default="medium")
    estimated_hours = models.PositiveIntegerField(default=2)
    is_opportunity = models.BooleanField(
        default=False
    )  # Flag for special opportunity recommendations
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Recommendation: {self.title} - {self.competitor.name}"


class RegionalPerformance(models.Model):
    """Model to store regional performance trends for a website"""

    website = models.ForeignKey(
        Website, on_delete=models.CASCADE, related_name="regional_performance"
    )
    month = models.CharField(max_length=20)
    year = models.IntegerField()
    organic_keywords = models.IntegerField(null=True, blank=True)
    competitors_average = models.IntegerField(null=True, blank=True)
    avg_position = models.FloatField(null=True, blank=True)
    visitors_trend = models.IntegerField(null=True, blank=True)

    class Meta:
        unique_together = ("website", "month", "year")

    def __str__(self):
        return f"{self.website.url} - {self.month} {self.year}"


class DataForSEORequest(models.Model):
    """Model to store DataForSEO API requests"""

    website = models.ForeignKey(
        Website, on_delete=models.CASCADE, related_name="dataforseo_requests"
    )
    endpoint = models.CharField(max_length=255)
    request_data = models.TextField()
    response_data = models.TextField(blank=True, null=True)
    status = models.CharField(max_length=50, default="pending")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.endpoint} request for {self.website.url}"

    def set_request_data(self, data):
        self.request_data = json.dumps(data)

    def get_request_data(self):
        return json.loads(self.request_data)

    def set_response_data(self, data):
        self.response_data = json.dumps(data)

    def get_response_data(self):
        return json.loads(self.response_data) if self.response_data else None


class UserWebsiteAnalysis(models.Model):
    """Model to track user-specific website analyses"""

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="website_analyses",
    )
    website = models.ForeignKey(
        Website, on_delete=models.CASCADE, related_name="user_analyses"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    last_viewed_at = models.DateTimeField(auto_now=True)
    notes = models.TextField(blank=True, null=True)
    is_favorite = models.BooleanField(default=False)

    class Meta:
        unique_together = ("user", "website")
        ordering = ["-last_viewed_at"]
        verbose_name = "User Website Analysis"
        verbose_name_plural = "User Website Analyses"

    def __str__(self):
        return f"{self.user.username} - {self.website.url}"


# Replacing competitor-specific models with website-specific models
class WebsiteStrategyGap(models.Model):
    """Model to store strategy gaps for a website"""

    website = models.ForeignKey(
        Website, on_delete=models.CASCADE, related_name="strategy_gaps"
    )
    text = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Strategy Gap: {self.text[:30]}... - {self.website.url}"


class WebsiteGrowthOpportunity(models.Model):
    """Model to store growth opportunities for a website"""

    website = models.ForeignKey(
        Website, on_delete=models.CASCADE, related_name="growth_opportunities"
    )
    text = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Growth Opportunity: {self.text[:30]}... - {self.website.url}"


class GoogleSearchConsoleCredential(models.Model):
    """Model to store Google Search Console credentials for a user"""

    user = models.OneToOneField(
        User, on_delete=models.CASCADE, related_name="gsc_credential"
    )
    access_token = models.TextField()
    refresh_token = models.TextField()
    token_expiry = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"GSC Credentials for {self.user.email}"

    @property
    def is_expired(self):
        """Check if the access token is expired"""
        return timezone.now() >= self.token_expiry
