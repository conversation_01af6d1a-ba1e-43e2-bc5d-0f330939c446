import logging
from datetime import datetime
from typing import Dict, Any, Optional
from django.utils import timezone
from django.utils.dateparse import parse_datetime

from .models import (
    Website,
    CoreWebVitalsAnalysis,
    PerformanceOpportunity,
    PerformanceDiagnostic,
)
from .pagespeed_service import PageSpeedInsightsService

logger = logging.getLogger(__name__)


class CoreWebVitalsService:
    """
    Service for processing and storing Core Web Vitals data
    """

    def __init__(self):
        self.pagespeed_service = PageSpeedInsightsService()

    def analyze_and_store(
        self, website_url: str, strategy: str = "mobile"
    ) -> Optional[CoreWebVitalsAnalysis]:
        """
        Analyze a website and store the Core Web Vitals data

        Args:
            website_url: URL of the website to analyze
            strategy: Analysis strategy ('mobile' or 'desktop')

        Returns:
            CoreWebVitalsAnalysis instance or None if error
        """
        try:
            # Get or create website
            website, created = Website.objects.get_or_create(
                url=website_url, defaults={"industry": "", "language": ""}
            )

            # Get Core Web Vitals data from PageSpeed API
            cwv_data = self.pagespeed_service.get_core_web_vitals(website_url, strategy)

            if "error" in cwv_data:
                logger.error(f"Error getting Core Web Vitals data: {cwv_data['error']}")
                return None

            # Process and store the data
            analysis = self._process_and_store_cwv_data(website, cwv_data, strategy)

            return analysis

        except Exception as e:
            logger.error(
                f"Error analyzing and storing Core Web Vitals for {website_url}: {str(e)}"
            )
            return None

    def analyze_multiple_strategies(
        self, website_url: str
    ) -> Dict[str, Optional[CoreWebVitalsAnalysis]]:
        """
        Analyze a website for both mobile and desktop strategies

        Args:
            website_url: URL of the website to analyze

        Returns:
            Dictionary with mobile and desktop analysis results
        """
        results = {
            "mobile": self.analyze_and_store(website_url, "mobile"),
            "desktop": self.analyze_and_store(website_url, "desktop"),
        }

        return results

    def _process_and_store_cwv_data(
        self, website: Website, cwv_data: Dict[str, Any], strategy: str
    ) -> CoreWebVitalsAnalysis:
        """
        Process and store Core Web Vitals data in the database

        Args:
            website: Website instance
            cwv_data: Raw Core Web Vitals data from PageSpeed API
            strategy: Analysis strategy

        Returns:
            CoreWebVitalsAnalysis instance
        """
        try:
            # Parse timestamp
            timestamp_str = cwv_data.get("timestamp")
            analysis_timestamp = timezone.now()
            if timestamp_str:
                try:
                    analysis_timestamp = parse_datetime(timestamp_str)
                    if analysis_timestamp is None:
                        analysis_timestamp = timezone.now()
                except Exception:
                    analysis_timestamp = timezone.now()

            # Get or update existing analysis
            analysis, created = CoreWebVitalsAnalysis.objects.update_or_create(
                website=website,
                strategy=strategy,
                defaults={
                    "analysis_timestamp": analysis_timestamp,
                    "lighthouse_version": cwv_data.get("lighthouse_version"),
                    "raw_data": cwv_data,
                    **self._extract_performance_scores(cwv_data),
                    **self._extract_core_web_vitals_metrics(cwv_data),
                    **self._extract_additional_metrics(cwv_data),
                },
            )

            # Clear existing opportunities and diagnostics for this analysis
            analysis.opportunities.all().delete()
            analysis.diagnostics.all().delete()

            # Store opportunities
            self._store_opportunities(analysis, cwv_data.get("opportunities", []))

            # Store diagnostics
            self._store_diagnostics(analysis, cwv_data.get("diagnostics", []))

            logger.info(
                f"Successfully stored Core Web Vitals analysis for {website.url} ({strategy})"
            )

            return analysis

        except Exception as e:
            logger.error(f"Error processing and storing CWV data: {str(e)}")
            raise

    def _extract_performance_scores(self, cwv_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract performance category scores"""
        categories = cwv_data.get("categories", {})

        return {
            "performance_score": categories.get("performance", {}).get("score"),
            "accessibility_score": categories.get("accessibility", {}).get("score"),
            "best_practices_score": categories.get("best-practices", {}).get("score"),
            "seo_score": categories.get("seo", {}).get("score"),
        }

    def _extract_core_web_vitals_metrics(
        self, cwv_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Extract Core Web Vitals metrics from the data"""
        core_web_vitals = cwv_data.get("core_web_vitals", {})
        field_data = cwv_data.get("field_data", {})
        lab_data = cwv_data.get("lab_data", {})

        metrics = {}

        # LCP metrics
        lcp_cwv = core_web_vitals.get("lcp", {})
        lcp_lab = lab_data.get("lcp", {})
        lcp_field = field_data.get("lcp", {})

        metrics.update(
            {
                "lcp_value": lcp_cwv.get("lab_value") or lcp_lab.get("numeric_value"),
                "lcp_score": lcp_cwv.get("lab_score") or lcp_lab.get("score"),
                "lcp_assessment": lcp_cwv.get("assessment"),
                "lcp_field_value": lcp_cwv.get("field_value")
                or lcp_field.get("percentile"),
                "lcp_field_category": lcp_cwv.get("field_category")
                or lcp_field.get("category"),
            }
        )

        # FID metrics
        fid_cwv = core_web_vitals.get("fid", {})
        fid_lab = lab_data.get("fid", {})
        fid_field = field_data.get("fid", {})

        metrics.update(
            {
                "fid_value": fid_cwv.get("lab_value") or fid_lab.get("numeric_value"),
                "fid_score": fid_cwv.get("lab_score") or fid_lab.get("score"),
                "fid_assessment": fid_cwv.get("assessment"),
                "fid_field_value": fid_cwv.get("field_value")
                or fid_field.get("percentile"),
                "fid_field_category": fid_cwv.get("field_category")
                or fid_field.get("category"),
            }
        )

        # CLS metrics
        cls_cwv = core_web_vitals.get("cls", {})
        cls_lab = lab_data.get("cls", {})
        cls_field = field_data.get("cls", {})

        metrics.update(
            {
                "cls_value": cls_cwv.get("lab_value") or cls_lab.get("numeric_value"),
                "cls_score": cls_cwv.get("lab_score") or cls_lab.get("score"),
                "cls_assessment": cls_cwv.get("assessment"),
                "cls_field_value": cls_cwv.get("field_value")
                or cls_field.get("percentile"),
                "cls_field_category": cls_cwv.get("field_category")
                or cls_field.get("category"),
            }
        )

        # FCP metrics
        fcp_cwv = core_web_vitals.get("fcp", {})
        fcp_lab = lab_data.get("fcp", {})
        fcp_field = field_data.get("fcp", {})

        metrics.update(
            {
                "fcp_value": fcp_cwv.get("lab_value") or fcp_lab.get("numeric_value"),
                "fcp_score": fcp_cwv.get("lab_score") or fcp_lab.get("score"),
                "fcp_assessment": fcp_cwv.get("assessment"),
                "fcp_field_value": fcp_cwv.get("field_value")
                or fcp_field.get("percentile"),
                "fcp_field_category": fcp_cwv.get("field_category")
                or fcp_field.get("category"),
            }
        )

        return metrics

    def _extract_additional_metrics(self, cwv_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract additional performance metrics"""
        lab_data = cwv_data.get("lab_data", {})

        metrics = {}

        # Speed Index
        speed_index = lab_data.get("speed_index", {})
        metrics.update(
            {
                "speed_index_value": speed_index.get("numeric_value"),
                "speed_index_score": speed_index.get("score"),
            }
        )

        # Time to Interactive
        tti = lab_data.get("tti", {})
        metrics.update(
            {"tti_value": tti.get("numeric_value"), "tti_score": tti.get("score")}
        )

        # Total Blocking Time
        tbt = lab_data.get("tbt", {})
        metrics.update(
            {"tbt_value": tbt.get("numeric_value"), "tbt_score": tbt.get("score")}
        )

        return metrics

    def _store_opportunities(
        self, analysis: CoreWebVitalsAnalysis, opportunities: list
    ):
        """Store performance opportunities"""
        for opp in opportunities:
            try:
                PerformanceOpportunity.objects.create(
                    analysis=analysis,
                    opportunity_id=opp.get("id", ""),
                    title=opp.get("title", ""),
                    description=opp.get("description", ""),
                    score=opp.get("score"),
                    display_value=opp.get("display_value", ""),
                    overall_savings_ms=opp.get("details", {}).get("overallSavingsMs"),
                    overall_savings_bytes=opp.get("details", {}).get(
                        "overallSavingsBytes"
                    ),
                    details=opp.get("details", {}),
                )
            except Exception as e:
                logger.warning(f"Error storing opportunity {opp.get('id')}: {str(e)}")

    def _store_diagnostics(self, analysis: CoreWebVitalsAnalysis, diagnostics: list):
        """Store performance diagnostics"""
        for diag in diagnostics:
            try:
                PerformanceDiagnostic.objects.create(
                    analysis=analysis,
                    diagnostic_id=diag.get("id", ""),
                    title=diag.get("title", ""),
                    description=diag.get("description", ""),
                    score=diag.get("score"),
                    display_value=diag.get("display_value", ""),
                    details=diag.get("details", {}),
                )
            except Exception as e:
                logger.warning(f"Error storing diagnostic {diag.get('id')}: {str(e)}")

    def get_latest_analysis(
        self, website_url: str, strategy: str = None
    ) -> Dict[str, Any]:
        """
        Get the latest Core Web Vitals analysis for a website

        Args:
            website_url: URL of the website
            strategy: Optional strategy filter ('mobile' or 'desktop')

        Returns:
            Dictionary with analysis data or empty dict if not found
        """
        try:
            website = Website.objects.get(url=website_url)

            if strategy:
                analyses = website.core_web_vitals_analyses.filter(strategy=strategy)
            else:
                analyses = website.core_web_vitals_analyses.all()

            latest_analyses = analyses.order_by("-created_at")

            if strategy:
                analysis = latest_analyses.first()
                if analysis:
                    from .serializers import CoreWebVitalsAnalysisSerializer

                    return CoreWebVitalsAnalysisSerializer(analysis).data
            else:
                # Return both mobile and desktop if available
                result = {}
                for strat in ["mobile", "desktop"]:
                    analysis = latest_analyses.filter(strategy=strat).first()
                    if analysis:
                        from .serializers import CoreWebVitalsAnalysisSerializer

                        result[strat] = CoreWebVitalsAnalysisSerializer(analysis).data
                return result

        except Website.DoesNotExist:
            logger.warning(f"Website {website_url} not found")
        except Exception as e:
            logger.error(f"Error getting latest analysis: {str(e)}")

        return {}

    def get_analysis_history(
        self, website_url: str, strategy: str = None, limit: int = 10
    ) -> list:
        """
        Get analysis history for a website

        Args:
            website_url: URL of the website
            strategy: Optional strategy filter
            limit: Maximum number of results

        Returns:
            List of analysis data
        """
        try:
            website = Website.objects.get(url=website_url)

            if strategy:
                analyses = website.core_web_vitals_analyses.filter(strategy=strategy)
            else:
                analyses = website.core_web_vitals_analyses.all()

            analyses = analyses.order_by("-created_at")[:limit]

            from .serializers import CoreWebVitalsAnalysisSerializer

            return [
                CoreWebVitalsAnalysisSerializer(analysis).data for analysis in analyses
            ]

        except Website.DoesNotExist:
            logger.warning(f"Website {website_url} not found")
        except Exception as e:
            logger.error(f"Error getting analysis history: {str(e)}")

        return []
