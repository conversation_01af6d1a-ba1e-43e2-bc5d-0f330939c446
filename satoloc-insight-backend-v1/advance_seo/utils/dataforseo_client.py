import os
import json
import base64
import requests
from django.conf import settings
import logging
import time

logger = logging.getLogger(__name__)

class DataForSEOClient:
    """
    Client for interacting with DataForSEO API
    """
    BASE_URL = "https://api.dataforseo.com/v3"
    
    def __init__(self):
        self.login = os.environ.get('DATAFORSEO_API_LOGIN')
        self.password = os.environ.get('DATAFORSEO_API_PASSWORD')
        
        if not self.login or not self.password:
            raise ValueError("DataForSEO API credentials not found in environment variables")
        
        self.auth_header = self._get_auth_header()
    
    def _get_auth_header(self):
        """
        Generate the Authorization header for DataForSEO API
        """
        credentials = f"{self.login}:{self.password}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()
        return f"Basic {encoded_credentials}"
    
    def _make_request(self, method, endpoint, data=None):
        """
        Make a request to the DataForSEO API
        
        Args:
            method (str): HTTP method (GET, POST)
            endpoint (str): API endpoint
            data (dict or list, optional): Request data for POST requests
            
        Returns:
            dict: API response
        """
        url = f"{self.BASE_URL}/{endpoint}"
        headers = {
            'Authorization': self.auth_header,
            'Content-Type': 'application/json'
        }
        
        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=headers)
            elif method.upper() == 'POST':
                payload = json.dumps(data)
                response = requests.post(url, headers=headers, data=payload)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json()
        
        except requests.exceptions.RequestException as e:
            logger.error("Error making request to DataForSEO API: %s", e)
            return {"status_code": getattr(e.response, 'status_code', 500), "error": str(e)}
    
    def get_competitors(self, target_url, limit=20, exclude_internal=True, exclude_large_domains=True, main_domain=True):
        """
        Get competitors for a target URL
        
        Args:
            target_url (str): Target URL to get competitors for
            limit (int): Maximum number of results to return
            exclude_internal (bool): Whether to exclude internal backlinks
            exclude_large_domains (bool): Whether to exclude large domains
            main_domain (bool): Whether to use the main domain
            
        Returns:
            dict: Competitors data
        """
        endpoint = "backlinks/competitors/live"
        
        # Remove trailing slash if present to ensure consistent format
        target_url = target_url.rstrip('/')
        
        data = [{
            "target": target_url,
            "limit": limit,
            "exclude_internal_backlinks": exclude_internal,
            "exclude_large_domains": exclude_large_domains,
            "main_domain": main_domain,
            "rank_scale": "one_hundred"  # Add rank_scale parameter
        }]
        
        logger.info("Making request to DataForSEO API: %s with data: %s", endpoint, data)
        response = self._make_request('POST', endpoint, data)
        
        # Log the entire response to debug
        logger.info("Received response from DataForSEO API: %s", endpoint)
        logger.info("Response status code: %s", response.get('status_code'))
        logger.info("Response status message: %s", response.get('status_message'))
        
        # Log the structure of the response
        if 'tasks' in response:
            logger.info("Tasks count: %s", len(response['tasks']))
            if response['tasks']:
                task = response['tasks'][0]
                logger.info("Task status: %s", task.get('status'))
                if 'result' in task:
                    if task['result'] is None:
                        logger.info("Task result is None")
                    else:
                        logger.info("Task result count: %s", len(task['result']))
                        if task['result']:
                            result = task['result'][0]
                            if 'items' in result:
                                logger.info("Items count: %s", len(result['items']))
                            else:
                                logger.info("No items in result")
                else:
                    logger.info("No result in task")
        
        return response
    
    def get_backlinks_summary(self, target_url, limit=100, main_domain=True):
        """
        Get backlinks summary for a target URL
        
        Args:
            target_url (str): Target URL to get backlinks summary for
            limit (int): Maximum number of results to return
            main_domain (bool): Whether to use the main domain
            
        Returns:
            dict: Backlinks summary data
        """
        endpoint = "backlinks/summary/live"
        
        # Remove trailing slash if present to ensure consistent format
        target_url = target_url.rstrip('/')
        
        data = [{
            "target": target_url,
            "limit": limit,
            "main_domain": main_domain
        }]
        
        logger.info("Making request to DataForSEO API: %s with data: %s", endpoint, data)
        return self._make_request('POST', endpoint, data)
    
    def get_domain_rating(self, target_url):
        """
        Get domain rating for a target URL using DataForSEO API's Domain Rank Overview endpoint
        
        Args:
            target_url (str): Target URL to get domain rating for
            
        Returns:
            float: Domain rating score (0-100)
        """
        # Use Domain Rank Overview endpoint
        endpoint = "dataforseo_labs/google/domain_rank_overview/live"
        
        # Remove protocol and trailing slashes from domain
        domain = target_url.replace("https://", "").replace("http://", "").rstrip("/")
        
        data = [{
            "target": domain,
            "location_code": 2840,  # United States
            "language_code": "en"
        }]
        
        logger.info("Making request to DataForSEO API: %s with data: %s", endpoint, data)
        response = self._make_request('POST', endpoint, data)
        
        if response.get('status_code') == 20000:
            try:
                result = response.get('tasks', [])[0].get('result', [])
                
                # Check if result is empty
                if not result:
                    logger.warning("No domain rank overview data found for %s", target_url)
                    return 0
                    
                result_item = result[0]
                
                # Extract metrics from the response
                items = result_item.get('items', [])
                if not items:
                    logger.warning("No domain rating data found for %s", target_url)
                    return 0
                    
                metrics = items[0].get('metrics', {})
                organic_metrics = metrics.get('organic', {})
                
                # Calculate domain rating based on multiple factors:
                # 1. Estimated traffic value (etv)
                # 2. Number of keywords in top positions
                # 3. Total keyword count
                etv = organic_metrics.get('etv', 0)
                pos_1 = organic_metrics.get('pos_1', 0)
                pos_2_3 = organic_metrics.get('pos_2_3', 0)
                pos_4_10 = organic_metrics.get('pos_4_10', 0)
                total_keywords = organic_metrics.get('count', 0)
                
                # Get backlinks data as well for a more comprehensive score
                backlinks_data = self.get_backlinks_summary(target_url)
                backlinks_count = 0
                referring_domains = 0
                
                if backlinks_data and backlinks_data.get('status_code') == 20000:
                    try:
                        backlinks_tasks = backlinks_data.get('tasks', [])
                        if backlinks_tasks and len(backlinks_tasks) > 0:
                            backlinks_result = backlinks_tasks[0].get('result', [])
                            if backlinks_result and len(backlinks_result) > 0:
                                backlinks_item = backlinks_result[0]
                                backlinks_count = backlinks_item.get('backlinks', 0)
                                referring_domains = backlinks_item.get('referring_domains', 0)
                    except (IndexError, KeyError, TypeError) as e:
                        logger.error("Error parsing backlinks data: %s", e)
                
                # Calculate domain rating components
                traffic_score = min(40, int(etv / 3000))  # Max 40 points from traffic
                
                # Calculate keyword position score
                top_positions = pos_1 + pos_2_3 + pos_4_10
                keyword_score = 0
                if total_keywords > 0:
                    keyword_ratio = top_positions / total_keywords
                    keyword_score = min(30, int(keyword_ratio * 300))  # Max 30 points from keywords
                
                # Calculate backlink score
                backlink_score = min(30, int(referring_domains / 100))  # Max 30 points from backlinks
                
                # Combine scores
                domain_rating = traffic_score + keyword_score + backlink_score
                
                logger.info("Retrieved domain rating for %s: %s (traffic: %s, keywords: %s, backlinks: %s)", target_url, domain_rating, traffic_score, keyword_score, backlink_score)
                
                return domain_rating
            except (IndexError, KeyError, TypeError) as e:
                logger.error("Error parsing domain rating data: %s", e)
                logger.exception(e)  # Log the full traceback
                return 0
        
        logger.warning("Failed to get domain rating for %s", target_url)
        return 0
    
    def get_organic_traffic(self, target_url):
        """
        Get organic traffic estimate for a target URL using DataForSEO API's Bulk Traffic Estimation endpoint
        
        Args:
            target_url (str): Target URL to get organic traffic for
            
        Returns:
            int: Organic traffic score (0-100)
        """
        # Use Bulk Traffic Estimation endpoint
        endpoint = "dataforseo_labs/google/bulk_traffic_estimation/live"
        
        # Remove protocol and trailing slashes from domain
        domain = target_url.replace("https://", "").replace("http://", "").rstrip("/")
        
        data = [{
            "targets": [domain],
            "location_code": 2840,  # United States
            "language_code": "en"
        }]
        
        logger.info("Making request to DataForSEO API: %s with data: %s", endpoint, data)
        response = self._make_request('POST', endpoint, data)
        
        if response.get('status_code') == 20000:
            try:
                result = response.get('tasks', [])[0].get('result', [])[0]
                items = result.get('items', [])
                
                if not items:
                    logger.warning("No traffic data found for %s", target_url)
                    return 0
                    
                item = items[0]
                metrics = item.get('metrics', {})
                organic = metrics.get('organic', {})
                
                # Get estimated traffic value
                etv = organic.get('etv', 0)
                
                # Normalize to 0-100 scale
                # For high-traffic sites, we need a logarithmic scale
                if etv > 0:
                    # Using log scale to handle wide range of traffic values
                    import math
                    normalized_score = min(100, int(10 * math.log10(etv + 1)))
                else:
                    normalized_score = 0
                
                logger.info("Retrieved organic traffic for %s: %s (normalized: %s)", target_url, etv, normalized_score)
                
                return normalized_score
            except (IndexError, KeyError) as e:
                logger.error("Error parsing organic traffic data: %s", e)
                return 0
        
        logger.warning("Failed to get organic traffic for %s", target_url)
        return 0
    
    def get_search_rankings(self, target_url):
        """
        Get search rankings data for a target URL using DataForSEO API's Domain Rank Overview endpoint
        
        Args:
            target_url (str): Target URL to get search rankings for
            
        Returns:
            float: Search rankings score (0-100)
        """
        # Use Domain Rank Overview endpoint
        endpoint = "dataforseo_labs/google/domain_rank_overview/live"
        
        # Remove protocol and trailing slashes from domain
        domain = target_url.replace("https://", "").replace("http://", "").rstrip("/")
        
        data = [{
            "target": domain,
            "location_code": 2840,  # United States
            "language_code": "en"
        }]
        
        logger.info("Making request to DataForSEO API: %s with data: %s", endpoint, data)
        response = self._make_request('POST', endpoint, data)
        
        if response.get('status_code') == 20000:
            try:
                result = response.get('tasks', [])[0].get('result', [])[0]
                
                # Extract metrics from the response
                items = result.get('items', [])
                if not items:
                    logger.warning("No search rankings data found for %s", target_url)
                    return 0
                    
                metrics = items[0].get('metrics', {})
                organic_metrics = metrics.get('organic', {})
                
                # Get keyword position counts with weighted importance
                pos_1 = organic_metrics.get('pos_1', 0) * 10  # Position 1 has 10x weight
                pos_2_3 = organic_metrics.get('pos_2_3', 0) * 5  # Positions 2-3 have 5x weight
                pos_4_10 = organic_metrics.get('pos_4_10', 0) * 2  # Positions 4-10 have 2x weight
                pos_11_20 = organic_metrics.get('pos_11_20', 0)  # Positions 11-20 have normal weight
                
                # Calculate weighted score
                weighted_score = pos_1 + pos_2_3 + pos_4_10 + pos_11_20
                
                # Calculate percentage of keywords in top 20 positions with weighting
                total_keywords = organic_metrics.get('count', 0)
                if total_keywords == 0:
                    return 0
                    
                # Calculate the base score
                base_score = min(100, int((weighted_score / max(1, total_keywords)) * 10))
                
                # Adjust score based on the number of top position keywords
                top10_keywords = organic_metrics.get('pos_1', 0) + organic_metrics.get('pos_2_3', 0) + organic_metrics.get('pos_4_10', 0)
                
                # If a site has a significant number of top 10 keywords, boost the score
                if top10_keywords > 1000:
                    ranking_score = min(100, base_score + 20)
                elif top10_keywords > 500:
                    ranking_score = min(100, base_score + 10)
                elif top10_keywords > 100:
                    ranking_score = min(100, base_score + 5)
                else:
                    ranking_score = base_score
                
                logger.info("Retrieved search rankings for %s: %s%% (base: %s%%, top10: %s)", target_url, ranking_score, base_score, top10_keywords)
                
                return ranking_score
            except (IndexError, KeyError) as e:
                logger.error("Error parsing search rankings data: %s", e)
                return 0
        
        logger.warning("Failed to get search rankings for %s", target_url)
        return 0
    
    def get_search_terms(self, target_url):
        """
        Get search terms count for a target URL using DataForSEO API's Domain Rank Overview endpoint
        
        Args:
            target_url (str): Target URL to get search terms for
            
        Returns:
            int: Number of search terms
        """
        # Use Domain Rank Overview endpoint
        endpoint = "dataforseo_labs/google/domain_rank_overview/live"
        
        # Remove protocol and trailing slashes from domain
        domain = target_url.replace("https://", "").replace("http://", "").rstrip("/")
        
        data = [{
            "target": domain,
            "location_code": 2840,  # United States
            "language_code": "en"
        }]
        
        logger.info("Making request to DataForSEO API: %s with data: %s", endpoint, data)
        response = self._make_request('POST', endpoint, data)
        
        if response.get('status_code') == 20000:
            try:
                result = response.get('tasks', [])[0].get('result', [])[0]
                
                # Extract metrics from the response
                items = result.get('items', [])
                if not items:
                    logger.warning("No search terms data found for %s", target_url)
                    return 0
                    
                metrics = items[0].get('metrics', {})
                organic_metrics = metrics.get('organic', {})
                
                # Get total keyword count
                total_keywords = organic_metrics.get('count', 0)
                
                logger.info("Retrieved search terms count for %s: %s", target_url, total_keywords)
                
                return total_keywords
            except (IndexError, KeyError) as e:
                logger.error("Error parsing search terms data: %s", e)
                return 0
        
        logger.warning("Failed to get search terms for %s", target_url)
        return 0
    
    def get_site_links(self, target_url):
        """
        Get site links count for a target URL using DataForSEO API
        
        Args:
            target_url (str): Target URL to get site links for
            
        Returns:
            int: Number of site links
        """
        # Use backlinks summary to get total backlinks
        summary_data = self.get_backlinks_summary(target_url)
        
        if summary_data and summary_data.get('status_code') == 20000:
            try:
                tasks = summary_data.get('tasks', [])
                if not tasks or len(tasks) == 0:
                    logger.warning("No tasks found in backlinks summary for %s", target_url)
                    return 0
                    
                result = tasks[0].get('result', [])
                if not result or len(result) == 0:
                    logger.warning("No result found in backlinks summary for %s", target_url)
                    return 0
                    
                backlinks_data = result[0]
                backlinks_count = backlinks_data.get('backlinks', 0)
                
                logger.info("Retrieved site links count for %s: %s", target_url, backlinks_count)
                
                return backlinks_count
            except (IndexError, KeyError, TypeError) as e:
                logger.error("Error parsing site links data: %s", e)
                logger.exception(e)
                return 0
        
        logger.warning("Failed to get site links for %s", target_url)
        return 0
    
    def get_content_score(self, target_url):
        """
        Get content score for a target URL using DataForSEO API's On-Page Summary endpoint
        
        Args:
            target_url (str): Target URL to get content score for
            
        Returns:
            float: Content score (0-100)
        """
        try:
            # First try to get content score from on-page analysis
            content_score = self._get_content_score_from_onpage(target_url)
            
            # If that fails, try a fallback method
            if content_score == 0:
                content_score = self._get_content_score_fallback(target_url)
            
            return content_score
        except Exception as e:
            logger.error(f"Error getting content score for {target_url}: {e}")
            logger.exception(e)
            return 0
    
    def _get_content_score_from_onpage(self, target_url):
        """
        Get content score using on-page analysis
        """
        # Use on-page summary to estimate content quality
        endpoint = "on_page/task_post"
        
        # Ensure the target URL has a protocol
        if not target_url.startswith(('http://', 'https://')):
            target_url = f"https://{target_url}"
        
        data = [{
            "target": target_url,
            "max_crawl_pages": 10,
            "load_resources": True,
            "enable_javascript": True,
            "enable_browser_rendering": True
        }]
        
        logger.info("Making request to DataForSEO API: %s with data: %s", endpoint, data)
        response = self._make_request('POST', endpoint, data)
        
        if response.get('status_code') == 20000:
            try:
                # Check if we have a task ID to fetch results
                task_id = response.get('tasks', [])[0].get('id')
                if not task_id:
                    logger.warning("No task ID found for content score analysis of %s", target_url)
                    return 0
                    
                # Wait for the task to complete
                time.sleep(10)
                
                # Fetch the summary results
                summary_endpoint = f"on_page/summary/{task_id}"
                summary_response = self._make_request('GET', summary_endpoint)
                
                if summary_response.get('status_code') == 20000:
                    task_result = summary_response.get('tasks', [])[0].get('result', [])
                    
                    # Handle the case where result is a list
                    if isinstance(task_result, list):
                        if not task_result:
                            logger.warning("Empty result list for content score analysis of %s", target_url)
                            return 0
                        
                        # If it's a list, take the first item
                        summary_result = task_result[0] if task_result else {}
                    else:
                        # If it's already a dictionary, use it directly
                        summary_result = task_result
                    
                    if not summary_result:
                        logger.warning("No summary result found for content score analysis of %s", target_url)
                        return 0
                    
                    # Extract content quality metrics
                    on_page_score = summary_result.get('on_page_score', 0)
                    content_quality = summary_result.get('content_quality', 0)
                    
                    # If content_quality is not available, use other metrics
                    if not content_quality:
                        # Calculate based on available metrics
                        total_pages = summary_result.get('total_pages', 0)
                        pages_with_duplicates = summary_result.get('pages_with_duplicates', 0)
                        broken_resources = summary_result.get('broken_resources', 0)
                        
                        # Higher score for more pages and fewer issues
                        if total_pages > 0:
                            duplicate_ratio = pages_with_duplicates / total_pages if total_pages > 0 else 1
                            resource_ratio = broken_resources / total_pages if total_pages > 0 else 1
                            content_quality = 100 * (1 - (duplicate_ratio * 0.5 + resource_ratio * 0.5))
                        else:
                            content_quality = 0
                    
                    # Combine metrics for final score
                    content_score = min(100, int((on_page_score * 0.7) + (content_quality * 0.3)))
                    
                    logger.info("Retrieved content score for %s: %s", target_url, content_score)
                    
                    return content_score
                else:
                    logger.warning("Failed to get content score summary for %s: %s", target_url, summary_response.get('status_message'))
                    return 0
            except (IndexError, KeyError, AttributeError) as e:
                logger.error("Error parsing content score data: %s", e)
                logger.exception(e)
                return 0
        
        logger.warning("Failed to get content score from on-page analysis for %s", target_url)
        return 0
    
    def _get_content_score_fallback(self, target_url):
        """
        Fallback method to estimate content score based on domain rank overview data
        """
        # Use Domain Rank Overview endpoint
        endpoint = "dataforseo_labs/google/domain_rank_overview/live"
        
        # Remove protocol and trailing slashes from domain
        domain = target_url.replace("https://", "").replace("http://", "").rstrip("/")
        
        data = [{
            "target": domain,
            "location_code": 2840,  # United States
            "language_code": "en"
        }]
        
        logger.info("Making fallback request for content score to DataForSEO API: %s with data: %s", endpoint, data)
        response = self._make_request('POST', endpoint, data)
        
        if response.get('status_code') == 20000:
            try:
                result = response.get('tasks', [])[0].get('result', [])[0]
                
                # Extract metrics from the response
                items = result.get('items', [])
                if not items:
                    logger.warning("No domain data found for content score fallback of %s", target_url)
                    return 0
                    
                metrics = items[0].get('metrics', {})
                organic_metrics = metrics.get('organic', {})
                
                # Estimate content score based on keyword positions
                # Sites with more top-ranking keywords typically have better content
                pos_1 = organic_metrics.get('pos_1', 0)
                pos_2_3 = organic_metrics.get('pos_2_3', 0)
                pos_4_10 = organic_metrics.get('pos_4_10', 0)
                total_keywords = organic_metrics.get('count', 0)
                
                if total_keywords == 0:
                    return 0
                
                # Calculate content score based on keyword distribution
                # Higher positions indicate better content
                top_positions = pos_1 + pos_2_3 + pos_4_10
                content_score = min(80, int((top_positions / max(1, total_keywords)) * 500))
                
                logger.info("Retrieved fallback content score for %s: %s", target_url, content_score)
                
                return content_score
            except (IndexError, KeyError) as e:
                logger.error("Error parsing fallback content score data: %s", e)
                return 0
        
        logger.warning("Failed to get fallback content score for %s", target_url)
        return 0
    
    def get_technical_analysis(self, target_url):
        """
        Get technical analysis data for a target URL using Google PageSpeed Insights API
        
        Args:
            target_url (str): Target URL to get technical analysis for
            
        Returns:
            dict: Technical analysis data
        """
        import requests
        from django.conf import settings
        import os
        import json
        
        technical_data = {}
        technical_issues = []
        
        # Ensure the URL has a protocol
        if not target_url.startswith(('http://', 'https://')):
            target_url = f"https://{target_url}"
        
        # Use Google PageSpeed Insights API for performance metrics
        pagespeed_api_key = os.environ.get('PAGESPEED_API_KEY')
        if not pagespeed_api_key:
            logger.warning("PAGESPEED_API_KEY not found in environment variables")
            return {
                'page_speed': 0,
                'mobile_optimization': 0,
                'core_web_vitals': 0,
                'https_security': False,
                'canonical_url': False,
                'schema_markup': False,
                'technical_issues': []
            }
            
        pagespeed_url = f"https://www.googleapis.com/pagespeedonline/v5/runPagespeed?url={target_url}&key={pagespeed_api_key}&strategy=mobile"
        
        try:
            logger.info("Making request to PageSpeed Insights API for %s", target_url)
            pagespeed_response = requests.get(pagespeed_url)
            
            if pagespeed_response.status_code != 200:
                logger.error("PageSpeed API returned status code %s: %s", pagespeed_response.status_code, pagespeed_response.text)
                return {
                    'page_speed': 0,
                    'mobile_optimization': 0,
                    'core_web_vitals': 0,
                    'https_security': False,
                    'canonical_url': False,
                    'schema_markup': False,
                    'technical_issues': ["Error fetching PageSpeed data"]
                }
                
            pagespeed_data = pagespeed_response.json()
            
            # Log the response structure for debugging
            logger.info("PageSpeed API response keys: %s", pagespeed_data.keys())
            
            # Extract performance metrics
            if 'lighthouseResult' in pagespeed_data:
                lighthouse = pagespeed_data['lighthouseResult']
                
                # Log lighthouse categories and audits for debugging
                logger.info("Lighthouse categories: %s", lighthouse.get('categories', {}).keys())
                logger.info("Lighthouse audits sample: %s", list(lighthouse.get('audits', {}).keys())[:10])
                
                # Extract page speed score
                if 'categories' in lighthouse and 'performance' in lighthouse['categories']:
                    technical_data['page_speed'] = int(lighthouse['categories']['performance']['score'] * 100)
                    logger.info("Page speed score: %s", technical_data['page_speed'])
                
                # Extract mobile optimization score
                if 'audits' in lighthouse and 'mobile-friendly' in lighthouse['audits']:
                    mobile_score = lighthouse['audits']['mobile-friendly'].get('score')
                    if mobile_score is not None:
                        technical_data['mobile_optimization'] = int(mobile_score * 100)
                    else:
                        technical_data['mobile_optimization'] = 0
                    logger.info("Mobile optimization score: %s", technical_data['mobile_optimization'])
                else:
                    # If mobile-friendly audit is not available, use viewport audit as a proxy
                    viewport_score = lighthouse['audits'].get('viewport', {}).get('score')
                    if viewport_score is not None:
                        technical_data['mobile_optimization'] = int(viewport_score * 100)
                    else:
                        technical_data['mobile_optimization'] = 0
                    logger.info("Mobile optimization score (from viewport): %s", technical_data['mobile_optimization'])
                
                # Extract core web vitals
                core_web_vitals_score = 0
                if 'audits' in lighthouse:
                    # Calculate average of LCP, FID, and CLS scores
                    lcp_score = lighthouse['audits'].get('largest-contentful-paint', {}).get('score', 0)
                    fid_score = lighthouse['audits'].get('max-potential-fid', {}).get('score', 0)
                    cls_score = lighthouse['audits'].get('cumulative-layout-shift', {}).get('score', 0)
                    
                    # Handle None values
                    lcp_score = 0 if lcp_score is None else lcp_score
                    fid_score = 0 if fid_score is None else fid_score
                    cls_score = 0 if cls_score is None else cls_score
                    
                    logger.info("LCP score: %s, FID score: %s, CLS score: %s", lcp_score, fid_score, cls_score)
                    
                    core_web_vitals_score = int(((lcp_score + fid_score + cls_score) / 3) * 100)
                
                technical_data['core_web_vitals'] = core_web_vitals_score
                logger.info("Core web vitals score: %s", technical_data['core_web_vitals'])
                
                # Extract HTTPS security
                https_score = lighthouse['audits'].get('is-on-https', {}).get('score')
                technical_data['https_security'] = https_score == 1 if https_score is not None else False
                logger.info("HTTPS security score: %s, result: %s", https_score, technical_data['https_security'])
                
                # Extract canonical URL
                canonical_score = lighthouse['audits'].get('canonical', {}).get('score')
                technical_data['canonical_url'] = canonical_score == 1 if canonical_score is not None else False
                logger.info("Canonical URL score: %s, result: %s", canonical_score, technical_data['canonical_url'])
                
                # Extract schema markup
                schema_score = lighthouse['audits'].get('structured-data', {}).get('score')
                technical_data['schema_markup'] = schema_score == 1 if schema_score is not None else False
                logger.info("Schema markup score: %s, result: %s", schema_score, technical_data['schema_markup'])
                
                # Extract technical issues
                for audit_id, audit in lighthouse['audits'].items():
                    score = audit.get('score')
                    # Only include failing audits with a score less than 0.5
                    if score is not None and score < 0.5 and 'title' in audit:
                        technical_issues.append(audit['title'])
                
                logger.info("Found %s technical issues", len(technical_issues))
            else:
                logger.warning("No lighthouseResult in PageSpeed API response")
                logger.info("PageSpeed API response: %s", json.dumps(pagespeed_data)[:500])
            
            # Add technical issues to the data
            technical_data['technical_issues'] = technical_issues
            
            return technical_data
            
        except Exception as e:
            logger.error("Error getting technical analysis for %s: %s", target_url, e)
            logger.exception(e)
            
            # Return basic data structure with zeros/empty values
            return {
                'page_speed': 0,
                'mobile_optimization': 0,
                'core_web_vitals': 0,
                'https_security': False,
                'canonical_url': False,
                'schema_markup': False,
                'technical_issues': []
            }
    
    def get_domain_keywords(self, domain, limit=20, language_code="en", location_code=2840):
        """
        Get top keywords for a domain using DataForSEO Labs API
        
        Args:
            domain (str): Domain to get keywords for (e.g., "example.com")
            limit (int): Maximum number of keywords to return
            language_code (str): Language code (default: "en" for English, also supports "tr" for Turkish)
            location_code (int): Location code (default: 2840 for United States, 2792 for Turkey)
            
        Returns:
            dict: Keywords data
        """
        endpoint = "dataforseo_labs/google/ranked_keywords/live"
        
        # Remove protocol and trailing slashes from domain
        domain = domain.replace("https://", "").replace("http://", "").rstrip("/")
        
        data = [{
            "target": domain,
            "limit": limit,
            "language_code": language_code,
            "location_code": location_code
        }]
        
        logger.info("Making request to DataForSEO API: %s with data: %s", endpoint, data)
        return self._make_request('POST', endpoint, data)
    
    def get_domain_analytics(self, target_url):
        """
        Get comprehensive domain analytics data by combining multiple API endpoints
        
        Args:
            target_url (str): Target URL to get analytics for
            
        Returns:
            dict: Comprehensive domain analytics data
        """
        # Get backlinks summary data
        backlinks_summary = self.get_backlinks_summary(target_url)
        
        # Get domain rating data
        domain_rating = self.get_domain_rating(target_url)
        
        # Get ranked keywords data (if available)
        ranked_keywords_data = self.get_domain_keywords(
            target_url.replace("https://", "").replace("http://", "").rstrip("/"),
            limit=100
        )
        
        # Combine all data into a comprehensive analytics object
        analytics = {
            'backlinks_summary': backlinks_summary,
            'domain_rating': domain_rating,
            'ranked_keywords': ranked_keywords_data
        }
        
        return analytics 