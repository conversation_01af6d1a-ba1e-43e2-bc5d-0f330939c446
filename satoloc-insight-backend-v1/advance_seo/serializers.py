from rest_framework import serializers
from .models import (
    Website,
    WebsiteLanguage,
    SEOMetrics,
    TechnicalAnalysis,
    Keyword,
    Competitor,
    CompetitorKeyword,
    RegionalPerformance,
    WebsiteStrategyGap,
    WebsiteGrowthOpportunity,
    DataForSEORequest,
    UserWebsiteAnalysis,
    RankingIssue,
    ContentRecommendation,
    CoreWebVitalsAnalysis,
    PerformanceOpportunity,
    PerformanceDiagnostic,
)


class WebsiteLanguageSerializer(serializers.ModelSerializer):
    class Meta:
        model = WebsiteLanguage
        fields = ["language"]


class KeywordSerializer(serializers.ModelSerializer):
    class Meta:
        model = Keyword
        fields = ["keyword", "is_target"]


class CompetitorKeywordSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompetitorKeyword
        fields = ["keyword"]


class RankingIssueSerializer(serializers.ModelSerializer):
    class Meta:
        model = RankingIssue
        exclude = ["competitor", "created_at", "updated_at"]


class ContentRecommendationSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContentRecommendation
        exclude = ["competitor", "created_at", "updated_at"]


class WebsiteStrategyGapSerializer(serializers.ModelSerializer):
    class Meta:
        model = WebsiteStrategyGap
        fields = ["id", "text", "created_at", "updated_at"]


class WebsiteGrowthOpportunitySerializer(serializers.ModelSerializer):
    class Meta:
        model = WebsiteGrowthOpportunity
        fields = ["id", "text", "created_at", "updated_at"]


class SEOMetricsSerializer(serializers.ModelSerializer):
    class Meta:
        model = SEOMetrics
        exclude = ["id", "website", "created_at", "updated_at"]

    def to_representation(self, instance):
        data = super().to_representation(instance)
        # Format the data to match the example JSON structure
        return {
            "domain_rating": {
                "description": "Overall strength of the website ability to rank in search engines (scale 0-100).",
                "score": instance.domain_rating_score,
                "previous_score": instance.domain_rating_previous_score,
            },
            "organic_traffic": {
                "description": "Total organic traffic from search engines (scale 0-100).",
                "score": instance.organic_traffic_score,
                "previous_score": instance.organic_traffic_previous_score,
            },
            "search_rankings": {
                "description": "percentage of your target keywords that are ranked in the top 10 search results in %.",
                "score": instance.search_rankings_score,
                "previous_score": instance.search_rankings_previous_score,
            },
            "search_terms": {
                "description": "number of different search terms that are used to find the website.",
                "score": instance.search_terms_score,
                "previous_score": instance.search_terms_previous_score,
            },
            "site_links": {
                "description": "number of links from other websites to the target website.",
                "score": instance.site_links_score,
                "previous_score": instance.site_links_previous_score,
            },
            "content_score": {
                "description": "percentage of the website that is optimized for search engines.",
                "score": instance.content_score,
                "previous_score": instance.content_score_previous_score,
            },
        }


class TechnicalAnalysisSerializer(serializers.ModelSerializer):
    class Meta:
        model = TechnicalAnalysis
        exclude = ["id", "website", "created_at", "updated_at"]

    def to_representation(self, instance):
        data = super().to_representation(instance)
        # Format the data to match the example JSON structure
        return {
            "page_speed": {
                "description": "Overall page loading speed score (scale 0-100).",
                "score": instance.page_speed,
            },
            "mobile_optimization": {
                "description": "Mobile-friendliness and responsive design score (scale 0-100).",
                "score": instance.mobile_optimization,
            },
            "core_web_vitals": {
                "description": "Core Web Vitals performance score (scale 0-100).",
                "score": instance.core_web_vitals,
            },
            "local_schema": {
                "description": "Local business schema markup implementation.",
                "implemented": instance.local_schema,
            },
            "https_security": {
                "description": "HTTPS security implementation.",
                "implemented": instance.https_security,
            },
            "canonical_url": {
                "description": "Canonical URL implementation to prevent duplicate content.",
                "implemented": instance.canonical_url,
            },
            "hreflang_implementation": {
                "description": "Hreflang tags for international SEO.",
                "implemented": instance.hreflang_implementation,
            },
            "schema_markup": {
                "description": "Structured data markup implementation.",
                "implemented": instance.schema_markup,
            },
        }


class CompetitorSerializer(serializers.ModelSerializer):
    top_keywords = CompetitorKeywordSerializer(many=True, read_only=True)
    ranking_issues = RankingIssueSerializer(many=True, read_only=True)
    content_recommendations = ContentRecommendationSerializer(many=True, read_only=True)

    class Meta:
        model = Competitor
        exclude = ["website", "created_at", "updated_at"]

    def to_representation(self, instance):
        data = super().to_representation(instance)
        # Extract keywords as a list of strings
        top_keywords = [keyword["keyword"] for keyword in data.pop("top_keywords", [])]
        data["top_keywords"] = top_keywords

        return data


class RegionalPerformanceSerializer(serializers.ModelSerializer):
    class Meta:
        model = RegionalPerformance
        exclude = ["id", "website"]


class WebsiteSerializer(serializers.ModelSerializer):
    class Meta:
        model = Website
        fields = "__all__"
        read_only_fields = ("created_at", "updated_at")


class WebsiteDetailSerializer(serializers.ModelSerializer):
    available_languages = WebsiteLanguageSerializer(many=True, read_only=True)
    keywords = KeywordSerializer(many=True, read_only=True)
    competitors = CompetitorSerializer(many=True, read_only=True)
    seo_metrics = SEOMetricsSerializer(read_only=True)
    technical_analysis = TechnicalAnalysisSerializer(read_only=True)
    regional_performance = RegionalPerformanceSerializer(many=True, read_only=True)
    strategy_gaps = WebsiteStrategyGapSerializer(many=True, read_only=True)
    growth_opportunities = WebsiteGrowthOpportunitySerializer(many=True, read_only=True)

    class Meta:
        model = Website
        fields = [
            "id",
            "url",
            "industry",
            "language",
            "available_languages",
            "keywords",
            "seo_metrics",
            "technical_analysis",
            "competitors",
            "regional_performance",
            "strategy_gaps",
            "growth_opportunities",
            "created_at",
            "updated_at",
        ]

    # Prevent automatic URL formatting
    def validate_url(self, value):
        # Just return the URL exactly as it was provided
        return value

    def to_representation(self, instance):
        data = super().to_representation(instance)

        # Format available_languages as a list of strings
        available_languages = [
            lang["language"] for lang in data.pop("available_languages", [])
        ]
        data["available_languages"] = [
            {"language": lang} for lang in available_languages
        ]

        # Keep original keywords data structure
        # We won't transform keywords so the frontend has full access to the data

        # Get backlinks count from SEO metrics if available
        if "seo_metrics" in data and data["seo_metrics"]:
            try:
                backlinks_count = instance.seo_metrics.backlinks_count or 0
                data["backlinks"] = backlinks_count
            except (AttributeError, SEOMetrics.DoesNotExist):
                data["backlinks"] = 0
        else:
            data["backlinks"] = 0

        # Format strategy gaps and growth opportunities for easier frontend handling
        strategy_gaps = [
            {"id": gap["id"], "text": gap["text"]}
            for gap in data.get("strategy_gaps", [])
        ]
        data["strategy_gaps"] = strategy_gaps

        growth_opportunities = [
            {"id": opp["id"], "text": opp["text"]}
            for opp in data.get("growth_opportunities", [])
        ]
        data["growth_opportunities"] = growth_opportunities

        return data


# Serializers for the new models
class RankingIssueSerializer(serializers.ModelSerializer):
    class Meta:
        model = RankingIssue
        exclude = ["competitor", "created_at", "updated_at"]


class ContentRecommendationSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContentRecommendation
        exclude = ["competitor", "created_at", "updated_at"]


class PerformanceOpportunitySerializer(serializers.ModelSerializer):
    class Meta:
        model = PerformanceOpportunity
        fields = [
            "opportunity_id",
            "title",
            "description",
            "score",
            "display_value",
            "overall_savings_ms",
            "overall_savings_bytes",
            "details",
        ]


class PerformanceDiagnosticSerializer(serializers.ModelSerializer):
    class Meta:
        model = PerformanceDiagnostic
        fields = [
            "diagnostic_id",
            "title",
            "description",
            "score",
            "display_value",
            "details",
        ]


class CoreWebVitalsAnalysisSerializer(serializers.ModelSerializer):
    opportunities = PerformanceOpportunitySerializer(many=True, read_only=True)
    diagnostics = PerformanceDiagnosticSerializer(many=True, read_only=True)

    class Meta:
        model = CoreWebVitalsAnalysis
        fields = [
            "strategy",
            "analysis_timestamp",
            "lighthouse_version",
            "performance_score",
            "accessibility_score",
            "best_practices_score",
            "seo_score",
            "lcp_value",
            "lcp_score",
            "lcp_assessment",
            "lcp_field_value",
            "lcp_field_category",
            "fid_value",
            "fid_score",
            "fid_assessment",
            "fid_field_value",
            "fid_field_category",
            "cls_value",
            "cls_score",
            "cls_assessment",
            "cls_field_value",
            "cls_field_category",
            "fcp_value",
            "fcp_score",
            "fcp_assessment",
            "fcp_field_value",
            "fcp_field_category",
            "speed_index_value",
            "speed_index_score",
            "tti_value",
            "tti_score",
            "tbt_value",
            "tbt_score",
            "opportunities",
            "diagnostics",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ("created_at", "updated_at")

    def to_representation(self, instance):
        data = super().to_representation(instance)

        # Structure the data to match the expected format
        structured_data = {
            "url": instance.website.url,
            "strategy": instance.strategy,
            "timestamp": instance.analysis_timestamp,
            "lighthouse_version": instance.lighthouse_version,
            "core_web_vitals": {
                "lcp": {
                    "field_value": instance.lcp_field_value,
                    "field_category": instance.lcp_field_category,
                    "lab_value": instance.lcp_value,
                    "lab_score": instance.lcp_score,
                    "assessment": instance.lcp_assessment,
                },
                "fid": {
                    "field_value": instance.fid_field_value,
                    "field_category": instance.fid_field_category,
                    "lab_value": instance.fid_value,
                    "lab_score": instance.fid_score,
                    "assessment": instance.fid_assessment,
                },
                "cls": {
                    "field_value": instance.cls_field_value,
                    "field_category": instance.cls_field_category,
                    "lab_value": instance.cls_value,
                    "lab_score": instance.cls_score,
                    "assessment": instance.cls_assessment,
                },
                "fcp": {
                    "field_value": instance.fcp_field_value,
                    "field_category": instance.fcp_field_category,
                    "lab_value": instance.fcp_value,
                    "lab_score": instance.fcp_score,
                    "assessment": instance.fcp_assessment,
                },
            },
            "performance_score": instance.performance_score,
            "categories": {
                "performance": {
                    "score": instance.performance_score,
                    "title": "Performance",
                },
                "accessibility": {
                    "score": instance.accessibility_score,
                    "title": "Accessibility",
                },
                "best-practices": {
                    "score": instance.best_practices_score,
                    "title": "Best Practices",
                },
                "seo": {"score": instance.seo_score, "title": "SEO"},
            },
            "lab_data": {
                "speed_index": {
                    "score": instance.speed_index_score,
                    "numeric_value": instance.speed_index_value,
                },
                "tti": {
                    "score": instance.tti_score,
                    "numeric_value": instance.tti_value,
                },
                "tbt": {
                    "score": instance.tbt_score,
                    "numeric_value": instance.tbt_value,
                },
            },
            "opportunities": data.get("opportunities", []),
            "diagnostics": data.get("diagnostics", []),
            "created_at": data.get("created_at"),
            "updated_at": data.get("updated_at"),
        }

        return structured_data
