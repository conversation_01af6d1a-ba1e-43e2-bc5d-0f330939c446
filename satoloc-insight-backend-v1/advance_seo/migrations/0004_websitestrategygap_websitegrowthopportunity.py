# Generated by Django 4.2.16 on 2025-05-08 14:04

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('advance_seo', '0003_alter_competitor_target_url_alter_website_url_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='WebsiteStrategyGap',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('website', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='strategy_gaps', to='advance_seo.website')),
            ],
        ),
        migrations.CreateModel(
            name='WebsiteGrowthOpportunity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('website', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='growth_opportunities', to='advance_seo.website')),
            ],
        ),
    ]
