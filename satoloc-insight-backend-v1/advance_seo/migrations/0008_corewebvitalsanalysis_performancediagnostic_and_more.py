# Generated by Django 4.2.16 on 2025-06-24 21:04

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("advance_seo", "0007_googlesearchconsolecredential"),
    ]

    operations = [
        migrations.CreateModel(
            name="CoreWebVitalsAnalysis",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "strategy",
                    models.CharField(
                        choices=[("mobile", "Mobile"), ("desktop", "Desktop")],
                        max_length=10,
                    ),
                ),
                ("analysis_timestamp", models.DateTimeField()),
                (
                    "lighthouse_version",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("performance_score", models.IntegerField(blank=True, null=True)),
                ("accessibility_score", models.IntegerField(blank=True, null=True)),
                ("best_practices_score", models.Integer<PERSON>ield(blank=True, null=True)),
                ("seo_score", models.IntegerField(blank=True, null=True)),
                ("lcp_value", models.FloatField(blank=True, null=True)),
                ("lcp_score", models.FloatField(blank=True, null=True)),
                (
                    "lcp_assessment",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("fid_value", models.FloatField(blank=True, null=True)),
                ("fid_score", models.FloatField(blank=True, null=True)),
                (
                    "fid_assessment",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("cls_value", models.FloatField(blank=True, null=True)),
                ("cls_score", models.FloatField(blank=True, null=True)),
                (
                    "cls_assessment",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("fcp_value", models.FloatField(blank=True, null=True)),
                ("fcp_score", models.FloatField(blank=True, null=True)),
                (
                    "fcp_assessment",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("lcp_field_value", models.FloatField(blank=True, null=True)),
                (
                    "lcp_field_category",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("fid_field_value", models.FloatField(blank=True, null=True)),
                (
                    "fid_field_category",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("cls_field_value", models.FloatField(blank=True, null=True)),
                (
                    "cls_field_category",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("fcp_field_value", models.FloatField(blank=True, null=True)),
                (
                    "fcp_field_category",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("speed_index_value", models.FloatField(blank=True, null=True)),
                ("speed_index_score", models.FloatField(blank=True, null=True)),
                ("tti_value", models.FloatField(blank=True, null=True)),
                ("tti_score", models.FloatField(blank=True, null=True)),
                ("tbt_value", models.FloatField(blank=True, null=True)),
                ("tbt_score", models.FloatField(blank=True, null=True)),
                ("raw_data", models.JSONField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "website",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="core_web_vitals_analyses",
                        to="advance_seo.website",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="PerformanceDiagnostic",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("diagnostic_id", models.CharField(max_length=100)),
                ("title", models.CharField(max_length=255)),
                ("description", models.TextField()),
                ("score", models.FloatField(blank=True, null=True)),
                (
                    "display_value",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("details", models.JSONField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "analysis",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="diagnostics",
                        to="advance_seo.corewebvitalsanalysis",
                    ),
                ),
            ],
            options={
                "unique_together": {("analysis", "diagnostic_id")},
            },
        ),
        migrations.CreateModel(
            name="PerformanceOpportunity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("opportunity_id", models.CharField(max_length=100)),
                ("title", models.CharField(max_length=255)),
                ("description", models.TextField()),
                ("score", models.FloatField(blank=True, null=True)),
                (
                    "display_value",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("overall_savings_ms", models.IntegerField(blank=True, null=True)),
                ("overall_savings_bytes", models.IntegerField(blank=True, null=True)),
                ("details", models.JSONField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "analysis",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="opportunities",
                        to="advance_seo.corewebvitalsanalysis",
                    ),
                ),
            ],
            options={
                "unique_together": {("analysis", "opportunity_id")},
            },
        ),
        migrations.DeleteModel(
            name="TechnicalIssue",
        ),
        migrations.AddIndex(
            model_name="corewebvitalsanalysis",
            index=models.Index(
                fields=["website", "strategy"], name="advance_seo_website_89d6d1_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="corewebvitalsanalysis",
            index=models.Index(
                fields=["created_at"], name="advance_seo_created_85c919_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="corewebvitalsanalysis",
            unique_together={("website", "strategy")},
        ),
    ]
