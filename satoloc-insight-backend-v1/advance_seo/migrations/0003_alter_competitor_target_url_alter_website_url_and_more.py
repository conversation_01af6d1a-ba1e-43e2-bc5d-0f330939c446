# Generated by Django 4.2.16 on 2025-05-08 10:31

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('advance_seo', '0002_userwebsiteanalysis'),
    ]

    operations = [
        migrations.AlterField(
            model_name='competitor',
            name='target_url',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='website',
            name='url',
            field=models.CharField(max_length=255, unique=True),
        ),
        migrations.CreateModel(
            name='CompetitorStrategyGap',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('competitor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='strategy_gaps', to='advance_seo.competitor')),
            ],
        ),
        migrations.CreateModel(
            name='CompetitorGrowthOpportunity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('competitor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='growth_opportunities', to='advance_seo.competitor')),
            ],
        ),
    ]
