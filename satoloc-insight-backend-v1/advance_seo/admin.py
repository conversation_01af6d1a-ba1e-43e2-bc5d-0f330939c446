from django.contrib import admin
from .models import (
    Website,
    WebsiteLanguage,
    SEOMetrics,
    TechnicalAnalysis,
    Keyword,
    Competitor,
    CompetitorKeyword,
    RegionalPerformance,
    DataForSEORequest,
    UserWebsiteAnalysis,
    WebsiteStrategyGap,
    WebsiteGrowthOpportunity,
    RankingIssue,
    ContentRecommendation,
    CoreWebVitalsAnalysis,
    PerformanceOpportunity,
    PerformanceDiagnostic,
)


class WebsiteLanguageInline(admin.TabularInline):
    model = WebsiteLanguage
    extra = 1


class KeywordInline(admin.TabularInline):
    model = Keyword
    extra = 1


class CompetitorInline(admin.TabularInline):
    model = Competitor
    extra = 1


class CompetitorKeywordInline(admin.TabularInline):
    model = CompetitorKeyword
    extra = 5


class WebsiteStrategyGapInline(admin.TabularInline):
    model = WebsiteStrategyGap
    extra = 1
    verbose_name_plural = "Website Strategy Gaps"


class WebsiteGrowthOpportunityInline(admin.TabularInline):
    model = WebsiteGrowthOpportunity
    extra = 1
    verbose_name_plural = "Website Growth Opportunities"


class RankingIssueInline(admin.TabularInline):
    model = RankingIssue
    extra = 1


class ContentRecommendationInline(admin.TabularInline):
    model = ContentRecommendation
    extra = 1


@admin.register(Website)
class WebsiteAdmin(admin.ModelAdmin):
    list_display = ("url", "industry", "language", "created_at", "updated_at")
    search_fields = ("url", "industry", "language")
    list_filter = ("industry", "language", "created_at")
    inlines = [
        WebsiteLanguageInline,
        KeywordInline,
        CompetitorInline,
        WebsiteStrategyGapInline,
        WebsiteGrowthOpportunityInline,
    ]


@admin.register(SEOMetrics)
class SEOMetricsAdmin(admin.ModelAdmin):
    list_display = (
        "website",
        "domain_rating_score",
        "organic_traffic_score",
        "search_rankings_score",
        "updated_at",
    )
    search_fields = ("website__url",)
    list_filter = ("created_at", "updated_at")


@admin.register(TechnicalAnalysis)
class TechnicalAnalysisAdmin(admin.ModelAdmin):
    list_display = (
        "website",
        "page_speed",
        "mobile_optimization",
        "core_web_vitals",
        "updated_at",
    )
    search_fields = ("website__url",)
    list_filter = (
        "https_security",
        "canonical_url",
        "schema_markup",
        "created_at",
        "updated_at",
    )


@admin.register(Competitor)
class CompetitorAdmin(admin.ModelAdmin):
    list_display = ("name", "website", "target_url", "rank", "updated_at")
    search_fields = ("name", "website__url", "target_url")
    list_filter = ("created_at", "updated_at")
    inlines = [CompetitorKeywordInline, RankingIssueInline, ContentRecommendationInline]


@admin.register(RegionalPerformance)
class RegionalPerformanceAdmin(admin.ModelAdmin):
    list_display = ("website", "month", "year", "organic_keywords", "avg_position")
    search_fields = ("website__url",)
    list_filter = ("month", "year")


@admin.register(DataForSEORequest)
class DataForSEORequestAdmin(admin.ModelAdmin):
    list_display = ("website", "endpoint", "status", "created_at", "updated_at")
    search_fields = ("website__url", "endpoint")
    list_filter = ("endpoint", "status", "created_at")
    readonly_fields = ("request_data", "response_data")


@admin.register(UserWebsiteAnalysis)
class UserWebsiteAnalysisAdmin(admin.ModelAdmin):
    list_display = ("user", "website", "created_at", "last_viewed_at", "is_favorite")
    list_filter = ("is_favorite", "created_at", "last_viewed_at")
    search_fields = ("user__username", "user__email", "website__url")
    raw_id_fields = ("user", "website")


@admin.register(WebsiteStrategyGap)
class WebsiteStrategyGapAdmin(admin.ModelAdmin):
    list_display = ("website", "text", "created_at", "updated_at")
    search_fields = ("website__url", "text")
    list_filter = ("created_at", "updated_at")


@admin.register(WebsiteGrowthOpportunity)
class WebsiteGrowthOpportunityAdmin(admin.ModelAdmin):
    list_display = ("website", "text", "created_at", "updated_at")
    search_fields = ("website__url", "text")
    list_filter = ("created_at", "updated_at")


@admin.register(RankingIssue)
class RankingIssueAdmin(admin.ModelAdmin):
    list_display = ("title", "competitor", "impact", "created_at")
    search_fields = (
        "title",
        "description",
        "competitor__name",
        "competitor__website__url",
    )
    list_filter = ("impact", "created_at")


@admin.register(ContentRecommendation)
class ContentRecommendationAdmin(admin.ModelAdmin):
    list_display = (
        "title",
        "competitor",
        "impact",
        "estimated_hours",
        "is_opportunity",
        "created_at",
    )
    search_fields = (
        "title",
        "description",
        "competitor__name",
        "competitor__website__url",
    )
    list_filter = ("impact", "is_opportunity", "created_at")


class PerformanceOpportunityInline(admin.TabularInline):
    model = PerformanceOpportunity
    extra = 0
    readonly_fields = (
        "opportunity_id",
        "title",
        "description",
        "score",
        "display_value",
    )


class PerformanceDiagnosticInline(admin.TabularInline):
    model = PerformanceDiagnostic
    extra = 0
    readonly_fields = (
        "diagnostic_id",
        "title",
        "description",
        "score",
        "display_value",
    )


@admin.register(CoreWebVitalsAnalysis)
class CoreWebVitalsAnalysisAdmin(admin.ModelAdmin):
    list_display = (
        "website",
        "strategy",
        "performance_score",
        "lcp_assessment",
        "fid_assessment",
        "cls_assessment",
        "analysis_timestamp",
        "created_at",
    )
    search_fields = ("website__url",)
    list_filter = (
        "strategy",
        "lcp_assessment",
        "fid_assessment",
        "cls_assessment",
        "analysis_timestamp",
        "created_at",
    )
    readonly_fields = (
        "analysis_timestamp",
        "lighthouse_version",
        "raw_data",
        "created_at",
        "updated_at",
    )
    inlines = [PerformanceOpportunityInline, PerformanceDiagnosticInline]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("website")


@admin.register(PerformanceOpportunity)
class PerformanceOpportunityAdmin(admin.ModelAdmin):
    list_display = (
        "analysis",
        "opportunity_id",
        "title",
        "score",
        "display_value",
        "overall_savings_ms",
        "overall_savings_bytes",
    )
    search_fields = ("analysis__website__url", "opportunity_id", "title")
    list_filter = ("opportunity_id", "created_at")
    readonly_fields = ("created_at",)


@admin.register(PerformanceDiagnostic)
class PerformanceDiagnosticAdmin(admin.ModelAdmin):
    list_display = (
        "analysis",
        "diagnostic_id",
        "title",
        "score",
        "display_value",
    )
    search_fields = ("analysis__website__url", "diagnostic_id", "title")
    list_filter = ("diagnostic_id", "created_at")
    readonly_fields = ("created_at",)
