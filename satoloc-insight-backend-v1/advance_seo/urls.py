from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    WebsiteViewSet,
    GSCConnectView,
    GSCDisconnectView,
    GSCStatusView,
    GSCSitesView,
    GSCSearchAnalyticsView,
    GSCUrlInspectionView,
    GSCSitemapsView,
    GSCAdvancedSearchAnalyticsView,
    GSCBulkDataView,
    GSCPerformanceDataView,
    GSCOptimizedDailyDataView,
    GSCDataAvailabilityView,
    # Core Web Vitals endpoints
    PageSpeedInsightsView,
    CoreWebVitalsView,
    CoreWebVitalsMultiStrategyView,
    CoreWebVitalsHistoryView,
    CoreWebVitalsBatchView,
    PageSpeedOpportunitiesView,
)

router = DefaultRouter()
router.register(r"websites", WebsiteViewSet, basename="website")

urlpatterns = [
    path("", include(router.urls)),
    # GSC Connection Management
    path("gsc-connect/", GSCConnectView.as_view(), name="gsc-connect"),
    path("gsc-disconnect/", GSCDisconnectView.as_view(), name="gsc-disconnect"),
    path("gsc-status/", GSCStatusView.as_view(), name="gsc-status"),
    # GSC Data Endpoints
    path("gsc-sites/", GSCSitesView.as_view(), name="gsc-sites"),
    path(
        "gsc-search-analytics/",
        GSCSearchAnalyticsView.as_view(),
        name="gsc-search-analytics",
    ),
    path(
        "gsc-url-inspection/", GSCUrlInspectionView.as_view(), name="gsc-url-inspection"
    ),
    path("gsc-sitemaps/", GSCSitemapsView.as_view(), name="gsc-sitemaps"),
    # Advanced GSC Data Endpoints
    path(
        "gsc-advanced-search-analytics/",
        GSCAdvancedSearchAnalyticsView.as_view(),
        name="gsc-advanced-search-analytics",
    ),
    path("gsc-bulk-data/", GSCBulkDataView.as_view(), name="gsc-bulk-data"),
    # New performance data endpoints
    path(
        "gsc-performance-data/",
        GSCPerformanceDataView.as_view(),
        name="gsc-performance-data",
    ),
    path(
        "gsc-optimized-daily-data/",
        GSCOptimizedDailyDataView.as_view(),
        name="gsc-optimized-daily-data",
    ),
    path(
        "gsc-data-availability/",
        GSCDataAvailabilityView.as_view(),
        name="gsc-data-availability",
    ),
    # Core Web Vitals endpoints
    path(
        "pagespeed-insights/",
        PageSpeedInsightsView.as_view(),
        name="pagespeed-insights",
    ),
    path(
        "core-web-vitals/",
        CoreWebVitalsView.as_view(),
        name="core-web-vitals",
    ),
    path(
        "core-web-vitals-multi/",
        CoreWebVitalsMultiStrategyView.as_view(),
        name="core-web-vitals-multi",
    ),
    path(
        "core-web-vitals-history/",
        CoreWebVitalsHistoryView.as_view(),
        name="core-web-vitals-history",
    ),
    path(
        "core-web-vitals-batch/",
        CoreWebVitalsBatchView.as_view(),
        name="core-web-vitals-batch",
    ),
    path(
        "pagespeed-opportunities/",
        PageSpeedOpportunitiesView.as_view(),
        name="pagespeed-opportunities",
    ),
]
