import os
import requests
import logging
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse, urljoin
from django.conf import settings

logger = logging.getLogger(__name__)


class PageSpeedInsightsService:
    """
    Service for interacting with Google PageSpeed Insights API v5 to get Core Web Vitals data
    """

    def __init__(self):
        self.api_key = os.environ.get("PAGESPEED_API_KEY")
        self.base_url = "https://www.googleapis.com/pagespeedonline/v5/runPagespeed"

        if not self.api_key:
            logger.warning("PAGESPEED_API_KEY not found in environment variables")

    def _make_request(
        self, url: str, strategy: str = "mobile", **kwargs
    ) -> Optional[Dict[str, Any]]:
        """
        Make a request to PageSpeed Insights API

        Args:
            url: Target URL to analyze
            strategy: Analysis strategy ('mobile' or 'desktop')
            **kwargs: Additional parameters

        Returns:
            API response data or None if error
        """
        if not self.api_key:
            logger.error("PageSpeed API key not configured")
            return None

        # Ensure URL has protocol
        if not url.startswith(("http://", "https://")):
            url = f"https://{url}"

        params = {
            "url": url,
            "key": self.api_key,
            "strategy": strategy,
            "category": ["performance", "accessibility", "best-practices", "seo"],
            "utm_source": "satoloc_insight",
        }

        # Add any additional parameters
        params.update(kwargs)

        try:
            logger.info(
                "Making PageSpeed Insights request for %s with strategy %s",
                url,
                strategy,
            )
            response = requests.get(self.base_url, params=params, timeout=30)

            if response.status_code == 200:
                return response.json()
            else:
                logger.error(
                    "PageSpeed API returned status %s: %s",
                    response.status_code,
                    response.text,
                )
                return None

        except requests.exceptions.RequestException as e:
            logger.error("Error making PageSpeed API request: %s", str(e))
            return None

    def get_core_web_vitals(self, url: str, strategy: str = "mobile") -> Dict[str, Any]:
        """
        Get Core Web Vitals data for a URL

        Args:
            url: Target URL to analyze
            strategy: 'mobile' or 'desktop'

        Returns:
            Dictionary containing Core Web Vitals metrics
        """
        response_data = self._make_request(url, strategy)

        if not response_data:
            return {
                "error": "Failed to fetch PageSpeed data",
                "url": url,
                "strategy": strategy,
            }

        try:
            return self._extract_core_web_vitals(response_data, url, strategy)
        except Exception as e:
            logger.error("Error extracting Core Web Vitals: %s", str(e))
            return {
                "error": "Error extracting Core Web Vitals: %s" % str(e),
                "url": url,
                "strategy": strategy,
            }

    def _extract_core_web_vitals(
        self, response_data: Dict[str, Any], url: str, strategy: str
    ) -> Dict[str, Any]:
        """
        Extract Core Web Vitals metrics from PageSpeed response

        Args:
            response_data: Raw PageSpeed API response
            url: Target URL
            strategy: Analysis strategy

        Returns:
            Processed Core Web Vitals data
        """
        result = {
            "url": url,
            "strategy": strategy,
            "timestamp": response_data.get("analysisUTCTimestamp"),
            "lighthouse_version": response_data.get("lighthouseResult", {}).get(
                "lighthouseVersion"
            ),
            "core_web_vitals": {},
            "field_data": {},
            "lab_data": {},
            "performance_score": None,
            "categories": {},
            "opportunities": [],
            "diagnostics": [],
        }

        lighthouse_result = response_data.get("lighthouseResult", {})

        # Extract performance score
        categories = lighthouse_result.get("categories", {})
        if "performance" in categories:
            result["performance_score"] = (
                int(categories["performance"]["score"] * 100)
                if categories["performance"]["score"]
                else 0
            )

        # Extract all category scores
        for category_name, category_data in categories.items():
            result["categories"][category_name] = {
                "score": (
                    int(category_data["score"] * 100) if category_data["score"] else 0
                ),
                "title": category_data.get("title", category_name),
            }

        # Extract Core Web Vitals from field data (Real User Monitoring)
        loading_experience = response_data.get("loadingExperience", {})
        if loading_experience.get("metrics"):
            result["field_data"] = self._extract_field_metrics(
                loading_experience["metrics"]
            )

        # Extract Core Web Vitals from lab data (Lighthouse)
        audits = lighthouse_result.get("audits", {})
        result["lab_data"] = self._extract_lab_metrics(audits)

        # Combine field and lab data for Core Web Vitals summary
        result["core_web_vitals"] = self._get_core_web_vitals_summary(
            result["field_data"], result["lab_data"]
        )

        # Extract opportunities and diagnostics
        result["opportunities"] = self._extract_opportunities(audits)
        result["diagnostics"] = self._extract_diagnostics(audits)

        return result

    def _extract_field_metrics(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Extract Core Web Vitals from field data (Real User Monitoring)"""
        field_data = {}

        # Core Web Vitals metrics mapping
        cwv_mapping = {
            "LARGEST_CONTENTFUL_PAINT_MS": "lcp",
            "FIRST_INPUT_DELAY_MS": "fid",
            "CUMULATIVE_LAYOUT_SHIFT_SCORE": "cls",
            "FIRST_CONTENTFUL_PAINT_MS": "fcp",
            "INTERACTION_TO_NEXT_PAINT": "inp",
        }

        for metric_key, metric_data in metrics.items():
            if metric_key in cwv_mapping:
                metric_name = cwv_mapping[metric_key]

                field_data[metric_name] = {
                    "percentile": metric_data.get("percentile"),
                    "category": metric_data.get("category"),
                    "distributions": metric_data.get("distributions", []),
                }

        return field_data

    def _extract_lab_metrics(self, audits: Dict[str, Any]) -> Dict[str, Any]:
        """Extract Core Web Vitals from lab data (Lighthouse)"""
        lab_data = {}

        # Lab metrics mapping
        lab_mapping = {
            "largest-contentful-paint": "lcp",
            "max-potential-fid": "fid",
            "cumulative-layout-shift": "cls",
            "first-contentful-paint": "fcp",
            "speed-index": "speed_index",
            "interactive": "tti",
            "total-blocking-time": "tbt",
        }

        for audit_key, metric_name in lab_mapping.items():
            if audit_key in audits:
                audit_data = audits[audit_key]

                lab_data[metric_name] = {
                    "score": audit_data.get("score"),
                    "numeric_value": audit_data.get("numericValue"),
                    "display_value": audit_data.get("displayValue"),
                    "title": audit_data.get("title"),
                    "description": audit_data.get("description"),
                }

        return lab_data

    def _get_core_web_vitals_summary(
        self, field_data: Dict[str, Any], lab_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create a summary of Core Web Vitals combining field and lab data"""
        summary = {}

        # Core Web Vitals thresholds (in milliseconds for LCP/FCP/FID, score for CLS)
        thresholds = {
            "lcp": {"good": 2500, "needs_improvement": 4000},
            "fid": {"good": 100, "needs_improvement": 300},
            "cls": {"good": 0.1, "needs_improvement": 0.25},
            "fcp": {"good": 1800, "needs_improvement": 3000},
        }

        for metric in ["lcp", "fid", "cls", "fcp"]:
            metric_summary = {
                "field_value": None,
                "field_category": None,
                "lab_value": None,
                "lab_score": None,
                "assessment": None,
            }

            # Field data (real user data)
            if metric in field_data:
                metric_summary["field_value"] = field_data[metric].get("percentile")
                metric_summary["field_category"] = field_data[metric].get("category")

            # Lab data (Lighthouse)
            if metric in lab_data:
                metric_summary["lab_value"] = lab_data[metric].get("numeric_value")
                metric_summary["lab_score"] = lab_data[metric].get("score")

            # Overall assessment
            if metric_summary["field_value"] is not None:
                metric_summary["assessment"] = self._assess_metric_value(
                    metric, metric_summary["field_value"], thresholds
                )
            elif metric_summary["lab_value"] is not None:
                metric_summary["assessment"] = self._assess_metric_value(
                    metric, metric_summary["lab_value"], thresholds
                )

            summary[metric] = metric_summary

        return summary

    def _assess_metric_value(
        self, metric: str, value: float, thresholds: Dict[str, Any]
    ) -> str:
        """Assess if a metric value is good, needs improvement, or poor"""
        if metric not in thresholds:
            return "unknown"

        threshold = thresholds[metric]

        if metric == "cls":
            # CLS is a score, lower is better
            if value <= threshold["good"]:
                return "good"
            elif value <= threshold["needs_improvement"]:
                return "needs_improvement"
            else:
                return "poor"
        else:
            # Time-based metrics, lower is better
            if value <= threshold["good"]:
                return "good"
            elif value <= threshold["needs_improvement"]:
                return "needs_improvement"
            else:
                return "poor"

    def _extract_opportunities(self, audits: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract performance opportunities from audits"""
        opportunities = []

        opportunity_audits = [
            "render-blocking-resources",
            "unused-css-rules",
            "unused-javascript",
            "modern-image-formats",
            "offscreen-images",
            "unminified-css",
            "unminified-javascript",
            "efficient-animated-content",
            "duplicated-javascript",
            "legacy-javascript",
            "preconnect",
            "server-response-time",
            "redirects",
            "image-aspect-ratio",
            "image-size-responsive",
            "preload-lcp-image",
        ]

        for audit_key in opportunity_audits:
            if audit_key in audits:
                audit = audits[audit_key]
                if audit.get("score") is not None and audit["score"] < 1:
                    opportunities.append(
                        {
                            "id": audit_key,
                            "title": audit.get("title"),
                            "description": audit.get("description"),
                            "score": audit.get("score"),
                            "display_value": audit.get("displayValue"),
                            "details": audit.get("details", {}),
                        }
                    )

        return opportunities

    def _extract_diagnostics(self, audits: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract diagnostic information from audits"""
        diagnostics = []

        diagnostic_audits = [
            "mainthread-work-breakdown",
            "bootup-time",
            "uses-long-cache-ttl",
            "total-byte-weight",
            "dom-size",
            "critical-request-chains",
            "user-timings",
            "screenshots",
            "final-screenshot",
        ]

        for audit_key in diagnostic_audits:
            if audit_key in audits:
                audit = audits[audit_key]
                diagnostics.append(
                    {
                        "id": audit_key,
                        "title": audit.get("title"),
                        "description": audit.get("description"),
                        "score": audit.get("score"),
                        "display_value": audit.get("displayValue"),
                        "details": audit.get("details", {}),
                    }
                )

        return diagnostics

    def get_multiple_strategies(self, url: str) -> Dict[str, Any]:
        """
        Get Core Web Vitals for both mobile and desktop strategies

        Args:
            url: Target URL to analyze

        Returns:
            Dictionary containing both mobile and desktop results
        """
        results = {
            "url": url,
            "mobile": self.get_core_web_vitals(url, "mobile"),
            "desktop": self.get_core_web_vitals(url, "desktop"),
        }

        return results

    def get_batch_analysis(
        self, urls: List[str], strategy: str = "mobile"
    ) -> Dict[str, Any]:
        """
        Analyze multiple URLs for Core Web Vitals

        Args:
            urls: List of URLs to analyze
            strategy: Analysis strategy

        Returns:
            Dictionary containing results for all URLs
        """
        results = {"strategy": strategy, "timestamp": None, "results": {}}

        for url in urls:
            logger.info("Analyzing %s for Core Web Vitals", url)
            results["results"][url] = self.get_core_web_vitals(url, strategy)

        return results
