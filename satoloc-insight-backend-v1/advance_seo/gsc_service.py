import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, List, Any

from django.conf import settings
from django.utils import timezone
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from .models import GoogleSearchConsoleCredential

logger = logging.getLogger(__name__)


class GSCService:
    """Service class for Google Search Console operations"""

    SCOPES = [
        "https://www.googleapis.com/auth/webmasters.readonly",
        "https://www.googleapis.com/auth/webmasters",
    ]

    def __init__(self):
        self.client_config = {
            "web": {
                "client_id": settings.GOOGLE_OAUTH2_CLIENT_ID,
                "client_secret": settings.GOOGLE_OAUTH2_CLIENT_SECRET,
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                "redirect_uris": [
                    "http://localhost:3000/gsc-callback",
                    "http://localhost:8000/gsc-callback",
                    "https://satolocinsight.com/gsc-callback",
                    "https://www.satolocinsight.com/gsc-callback",
                ],
            }
        }

    def exchange_code_for_tokens(
        self, auth_code: str, redirect_uri: str
    ) -> Dict[str, Any]:
        """
        Exchange authorization code for access and refresh tokens

        Args:
            auth_code: Authorization code from Google OAuth
            redirect_uri: The redirect URI used in the authorization request

        Returns:
            Dictionary containing token information

        Raises:
            Exception: If token exchange fails
        """
        try:
            # Try with full scopes first
            flow = Flow.from_client_config(
                self.client_config, scopes=self.SCOPES, redirect_uri=redirect_uri
            )

            try:
                # Exchange the authorization code for tokens
                flow.fetch_token(code=auth_code)
                credentials = flow.credentials
            except Exception as scope_error:
                logger.warning(
                    f"Full scope exchange failed, trying readonly only: {str(scope_error)}"
                )

                # Try with readonly scope only if full scope fails
                readonly_flow = Flow.from_client_config(
                    self.client_config,
                    scopes=["https://www.googleapis.com/auth/webmasters.readonly"],
                    redirect_uri=redirect_uri,
                )
                readonly_flow.fetch_token(code=auth_code)
                credentials = readonly_flow.credentials

            return {
                "access_token": credentials.token,
                "refresh_token": credentials.refresh_token,
                "token_expiry": credentials.expiry,
                "scopes": credentials.scopes,
            }

        except Exception as e:
            logger.error(f"Error exchanging authorization code for tokens: {str(e)}")
            raise Exception(f"Failed to exchange authorization code: {str(e)}")

    def store_credentials(
        self, user, token_data: Dict[str, Any]
    ) -> GoogleSearchConsoleCredential:
        """
        Store or update user's GSC credentials

        Args:
            user: Django user object
            token_data: Dictionary containing token information

        Returns:
            GoogleSearchConsoleCredential instance
        """
        try:
            credential, created = (
                GoogleSearchConsoleCredential.objects.update_or_create(
                    user=user,
                    defaults={
                        "access_token": token_data["access_token"],
                        "refresh_token": token_data["refresh_token"],
                        "token_expiry": token_data["token_expiry"],
                    },
                )
            )

            action = "Created" if created else "Updated"
            logger.info(f"{action} GSC credentials for user {user.email}")

            return credential

        except Exception as e:
            logger.error(
                f"Error storing GSC credentials for user {user.email}: {str(e)}"
            )
            raise Exception(f"Failed to store credentials: {str(e)}")

    def refresh_token(self, credential: GoogleSearchConsoleCredential) -> bool:
        """
        Refresh expired access token using refresh token

        Args:
            credential: GoogleSearchConsoleCredential instance

        Returns:
            Boolean indicating success
        """
        try:
            # Create credentials object from stored data
            creds = Credentials(
                token=credential.access_token,
                refresh_token=credential.refresh_token,
                token_uri="https://oauth2.googleapis.com/token",
                client_id=settings.GOOGLE_OAUTH2_CLIENT_ID,
                client_secret=settings.GOOGLE_OAUTH2_CLIENT_SECRET,
                scopes=self.SCOPES,
            )

            # Refresh the token
            creds.refresh(Request())

            # Update stored credentials
            credential.access_token = creds.token
            credential.token_expiry = creds.expiry
            credential.save()

            logger.info(
                f"Successfully refreshed GSC token for user {credential.user.email}"
            )
            return True

        except Exception as e:
            logger.error(
                f"Error refreshing GSC token for user {credential.user.email}: {str(e)}"
            )
            return False

    def get_valid_credentials(self, user) -> Optional[Credentials]:
        """
        Get valid credentials for a user, refreshing if necessary

        Args:
            user: Django user object

        Returns:
            Google Credentials object or None if not available/invalid
        """
        try:
            credential = GoogleSearchConsoleCredential.objects.get(user=user)

            # Create credentials object
            creds = Credentials(
                token=credential.access_token,
                refresh_token=credential.refresh_token,
                token_uri="https://oauth2.googleapis.com/token",
                client_id=settings.GOOGLE_OAUTH2_CLIENT_ID,
                client_secret=settings.GOOGLE_OAUTH2_CLIENT_SECRET,
                scopes=self.SCOPES,
            )

            # Check if token is expired and refresh if needed
            if credential.is_expired:
                logger.info(
                    f"Token expired for user {user.email}, attempting to refresh"
                )
                if self.refresh_token(credential):
                    # Reload credential with updated token
                    credential.refresh_from_db()
                    creds = Credentials(
                        token=credential.access_token,
                        refresh_token=credential.refresh_token,
                        token_uri="https://oauth2.googleapis.com/token",
                        client_id=settings.GOOGLE_OAUTH2_CLIENT_ID,
                        client_secret=settings.GOOGLE_OAUTH2_CLIENT_SECRET,
                        scopes=self.SCOPES,
                    )
                else:
                    logger.error(f"Failed to refresh token for user {user.email}")
                    return None

            return creds

        except GoogleSearchConsoleCredential.DoesNotExist:
            logger.info(f"No GSC credentials found for user {user.email}")
            return None
        except Exception as e:
            logger.error(
                f"Error getting valid credentials for user {user.email}: {str(e)}"
            )
            return None

    def get_gsc_service(self, user):
        """
        Get authenticated Google Search Console service

        Args:
            user: Django user object

        Returns:
            Google Search Console service object or None
        """
        credentials = self.get_valid_credentials(user)
        if not credentials:
            return None

        try:
            service = build("searchconsole", "v1", credentials=credentials)
            return service
        except Exception as e:
            logger.error(f"Error building GSC service for user {user.email}: {str(e)}")
            return None

    def get_user_sites(self, user) -> List[Dict[str, Any]]:
        """
        Get list of sites accessible to the user in GSC

        Args:
            user: Django user object

        Returns:
            List of site information dictionaries
        """
        service = self.get_gsc_service(user)
        if not service:
            return []

        try:
            sites_result = service.sites().list().execute()
            sites = sites_result.get("siteEntry", [])

            site_list = []
            for site in sites:
                site_list.append(
                    {
                        "site_url": site.get("siteUrl"),
                        "permission_level": site.get("permissionLevel"),
                    }
                )

            logger.info(f"Retrieved {len(site_list)} sites for user {user.email}")
            return site_list

        except HttpError as e:
            logger.error(f"HTTP error getting sites for user {user.email}: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"Error getting sites for user {user.email}: {str(e)}")
            return []

    def get_search_analytics(
        self,
        user,
        site_url: str,
        start_date: str,
        end_date: str,
        dimensions: List[str] = None,
        row_limit: int = 1000,
    ) -> Dict[str, Any]:
        """
        Get search analytics data from GSC

        Args:
            user: Django user object
            site_url: The site URL to get data for
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            dimensions: List of dimensions (query, page, country, device, searchAppearance)
            row_limit: Maximum number of rows to return

        Returns:
            Dictionary containing search analytics data
        """
        service = self.get_gsc_service(user)
        if not service:
            return {"error": "No valid GSC service available"}

        if dimensions is None:
            dimensions = ["query"]

        try:
            request_body = {
                "startDate": start_date,
                "endDate": end_date,
                "dimensions": dimensions,
                "rowLimit": row_limit,
            }

            result = (
                service.searchanalytics()
                .query(siteUrl=site_url, body=request_body)
                .execute()
            )

            logger.info(
                f"Retrieved search analytics for {site_url} for user {user.email}"
            )
            return result

        except HttpError as e:
            error_msg = f"HTTP error getting search analytics for {site_url}: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
        except Exception as e:
            error_msg = f"Error getting search analytics for {site_url}: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}

    def get_advanced_search_analytics(
        self,
        user,
        site_url: str,
        start_date: str,
        end_date: str,
        dimensions: List[str] = None,
        search_type: str = "web",
        row_limit: int = 1000,
        start_row: int = 0,
        aggregation_type: str = "auto",
        data_state: str = "final",
        dimension_filter_groups: List[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Get comprehensive search analytics data from GSC with all available options

        Args:
            user: Django user object
            site_url: The site URL to get data for
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            dimensions: List of dimensions (query, page, country, device, searchAppearance, date, hour)
            search_type: Type of search (web, image, video, news, discover, googleNews)
            row_limit: Maximum number of rows to return (1-25000)
            start_row: Zero-based index of first row
            aggregation_type: How data is aggregated (auto, byPage, byProperty)
            data_state: Data freshness (final, all, hourly_all)
            dimension_filter_groups: Advanced filtering groups

        Returns:
            Dictionary containing comprehensive search analytics data
        """
        service = self.get_gsc_service(user)
        if not service:
            return {"error": "No valid GSC service available"}

        if dimensions is None:
            dimensions = ["query"]

        if dimension_filter_groups is None:
            dimension_filter_groups = []

        try:
            # Build comprehensive request body
            request_body = {
                "startDate": start_date,
                "endDate": end_date,
                "dimensions": dimensions,
                "type": search_type,
                "rowLimit": min(row_limit, 25000),  # API limit
                "startRow": start_row,
                "aggregationType": aggregation_type,
                "dataState": data_state,
            }

            # Add dimension filter groups if provided
            if dimension_filter_groups:
                request_body["dimensionFilterGroups"] = dimension_filter_groups

            result = (
                service.searchanalytics()
                .query(siteUrl=site_url, body=request_body)
                .execute()
            )

            # Add metadata about the request
            result["request_metadata"] = {
                "dimensions": dimensions,
                "search_type": search_type,
                "row_limit": row_limit,
                "start_row": start_row,
                "aggregation_type": aggregation_type,
                "data_state": data_state,
                "filters_applied": len(dimension_filter_groups) > 0,
                "total_filters": sum(
                    len(group.get("filters", [])) for group in dimension_filter_groups
                ),
            }

            logger.info(
                f"Retrieved advanced search analytics for {site_url} (type: {search_type}, "
                f"dimensions: {dimensions}) for user {user.email}"
            )
            return result

        except HttpError as e:
            error_msg = (
                f"HTTP error getting advanced search analytics for {site_url}: {str(e)}"
            )
            logger.error(error_msg)
            return {"error": error_msg}
        except Exception as e:
            error_msg = (
                f"Error getting advanced search analytics for {site_url}: {str(e)}"
            )
            logger.error(error_msg)
            return {"error": error_msg}

    def get_site_inspection(
        self, user, site_url: str, inspect_url: str
    ) -> Dict[str, Any]:
        """
        Get URL inspection data from GSC

        Args:
            user: Django user object
            site_url: The site property in GSC
            inspect_url: The specific URL to inspect

        Returns:
            Dictionary containing inspection data
        """
        service = self.get_gsc_service(user)
        if not service:
            return {"error": "No valid GSC service available"}

        try:
            request_body = {"inspectionUrl": inspect_url, "siteUrl": site_url}

            result = (
                service.urlInspection().index().inspect(body=request_body).execute()
            )

            logger.info(
                f"Retrieved URL inspection for {inspect_url} for user {user.email}"
            )
            return result

        except HttpError as e:
            error_msg = f"HTTP error getting URL inspection for {inspect_url}: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
        except Exception as e:
            error_msg = f"Error getting URL inspection for {inspect_url}: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}

    def get_sitemaps(self, user, site_url: str) -> List[Dict[str, Any]]:
        """
        Get sitemap information from GSC

        Args:
            user: Django user object
            site_url: The site URL to get sitemaps for

        Returns:
            List of sitemap information dictionaries
        """
        service = self.get_gsc_service(user)
        if not service:
            return []

        try:
            result = service.sitemaps().list(siteUrl=site_url).execute()
            sitemaps = result.get("sitemap", [])

            sitemap_list = []
            for sitemap in sitemaps:
                sitemap_list.append(
                    {
                        "path": sitemap.get("path"),
                        "last_submitted": sitemap.get("lastSubmitted"),
                        "is_pending": sitemap.get("isPending"),
                        "is_sitemaps_index": sitemap.get("isSitemapsIndex"),
                        "type": sitemap.get("type"),
                        "last_downloaded": sitemap.get("lastDownloaded"),
                        "warnings": sitemap.get("warnings"),
                        "errors": sitemap.get("errors"),
                    }
                )

            logger.info(
                f"Retrieved {len(sitemap_list)} sitemaps for {site_url} for user {user.email}"
            )
            return sitemap_list

        except HttpError as e:
            logger.error(f"HTTP error getting sitemaps for {site_url}: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"Error getting sitemaps for {site_url}: {str(e)}")
            return []

    def disconnect_user(self, user) -> bool:
        """
        Disconnect user from GSC by deleting stored credentials

        Args:
            user: Django user object

        Returns:
            Boolean indicating success
        """
        try:
            GoogleSearchConsoleCredential.objects.filter(user=user).delete()
            logger.info(f"Successfully disconnected GSC for user {user.email}")
            return True
        except Exception as e:
            logger.error(f"Error disconnecting GSC for user {user.email}: {str(e)}")
            return False

    def get_performance_data_accurate(
        self,
        user,
        site_url: str,
        target_date: str,
        search_type: str = "web",
        aggregation_type: str = "byProperty",
    ) -> Dict[str, Any]:
        """
        Get accurate performance counts (without page/query dimensions)
        Following Google's recommended pattern for complete data
        """
        service = self.get_gsc_service(user)
        if not service:
            return {"error": "No valid GSC service available"}

        try:
            # For accurate counts, omit page and query dimensions
            if aggregation_type == "byPage":
                dimensions = ["country", "device"]
                request_body = {
                    "startDate": target_date,
                    "endDate": target_date,
                    "dimensions": dimensions,
                    "type": search_type,
                    "aggregationType": "byPage",
                    "rowLimit": 25000,
                }
            else:
                dimensions = ["country", "device"]
                request_body = {
                    "startDate": target_date,
                    "endDate": target_date,
                    "dimensions": dimensions,
                    "type": search_type,
                    "rowLimit": 25000,
                }

            result = (
                service.searchanalytics()
                .query(siteUrl=site_url, body=request_body)
                .execute()
            )

            logger.info(
                f"Retrieved accurate performance data for {site_url} on {target_date}"
            )
            return result

        except Exception as e:
            error_msg = f"Error getting accurate performance data: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}

    def get_performance_data_detailed(
        self,
        user,
        site_url: str,
        target_date: str,
        search_type: str = "web",
        aggregation_type: str = "byProperty",
    ) -> Dict[str, Any]:
        """
        Get detailed performance data (with page/query dimensions)
        Note: May lose some data due to privacy filtering
        """
        service = self.get_gsc_service(user)
        if not service:
            return {"error": "No valid GSC service available"}

        try:
            if aggregation_type == "byPage":
                dimensions = ["page", "query", "country", "device"]
                request_body = {
                    "startDate": target_date,
                    "endDate": target_date,
                    "dimensions": dimensions,
                    "type": search_type,
                    "rowLimit": 25000,
                }
            else:
                dimensions = ["query", "country", "device"]
                request_body = {
                    "startDate": target_date,
                    "endDate": target_date,
                    "dimensions": dimensions,
                    "type": search_type,
                    "rowLimit": 25000,
                }

            result = (
                service.searchanalytics()
                .query(siteUrl=site_url, body=request_body)
                .execute()
            )

            logger.info(
                f"Retrieved detailed performance data for {site_url} on {target_date}"
            )
            return result

        except Exception as e:
            error_msg = f"Error getting detailed performance data: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}

    def get_search_appearance_data(
        self, user, site_url: str, target_date: str, search_type: str = "web"
    ) -> Dict[str, Any]:
        """
        Get search appearance data using the two-step process
        1. Get list of search appearance types
        2. Get detailed data for each type
        """
        service = self.get_gsc_service(user)
        if not service:
            return {"error": "No valid GSC service available"}

        try:
            # Step 1: Get search appearance types
            step1_request = {
                "startDate": target_date,
                "endDate": target_date,
                "type": search_type,
                "dimensions": ["searchAppearance"],
            }

            step1_result = (
                service.searchanalytics()
                .query(siteUrl=site_url, body=step1_request)
                .execute()
            )

            search_appearance_data = {
                "overview": step1_result,
                "detailed_breakdown": {},
            }

            # Step 2: Get detailed data for each search appearance type
            if "rows" in step1_result:
                for row in step1_result["rows"]:
                    appearance_type = row["keys"][0]

                    # Get detailed breakdown for this appearance type
                    step2_request = {
                        "startDate": target_date,
                        "endDate": target_date,
                        "type": search_type,
                        "dimensions": ["device", "country"],
                        "dimensionFilterGroups": [
                            {
                                "filters": [
                                    {
                                        "dimension": "searchAppearance",
                                        "operator": "equals",
                                        "expression": appearance_type,
                                    }
                                ]
                            }
                        ],
                    }

                    try:
                        step2_result = (
                            service.searchanalytics()
                            .query(siteUrl=site_url, body=step2_request)
                            .execute()
                        )
                        search_appearance_data["detailed_breakdown"][
                            appearance_type
                        ] = step2_result
                    except Exception as e:
                        search_appearance_data["detailed_breakdown"][
                            appearance_type
                        ] = {"error": str(e)}

            logger.info(
                f"Retrieved search appearance data for {site_url} on {target_date}"
            )
            return search_appearance_data

        except Exception as e:
            error_msg = f"Error getting search appearance data: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}

    def get_daily_data_paginated(
        self,
        user,
        site_url: str,
        target_date: str,
        search_type: str = "web",
        dimensions: List[str] = None,
        aggregation_type: str = "byProperty",
        max_rows: int = 25000,
    ) -> Dict[str, Any]:
        """
        Get one day's worth of data with pagination to handle large datasets
        """
        service = self.get_gsc_service(user)
        if not service:
            return {"error": "No valid GSC service available"}

        if dimensions is None:
            dimensions = ["country", "device"]

        try:
            all_rows = []
            start_row = 0
            total_requests = 0

            while True:
                request_body = {
                    "startDate": target_date,
                    "endDate": target_date,
                    "dimensions": dimensions,
                    "type": search_type,
                    "rowLimit": max_rows,
                    "startRow": start_row,
                }

                if aggregation_type == "byPage":
                    request_body["aggregationType"] = "byPage"

                result = (
                    service.searchanalytics()
                    .query(siteUrl=site_url, body=request_body)
                    .execute()
                )

                total_requests += 1

                if "rows" not in result or len(result["rows"]) == 0:
                    break

                all_rows.extend(result["rows"])
                start_row += max_rows

                # Safety limit to prevent infinite loops
                if total_requests >= 10:
                    break

            paginated_result = {
                "rows": all_rows,
                "total_rows": len(all_rows),
                "total_requests": total_requests,
                "responseAggregationType": result.get(
                    "responseAggregationType", "unknown"
                ),
                "pagination_metadata": {
                    "target_date": target_date,
                    "search_type": search_type,
                    "dimensions": dimensions,
                    "aggregation_type": aggregation_type,
                    "max_rows_per_request": max_rows,
                },
            }

            logger.info(
                f"Retrieved {len(all_rows)} rows of daily data for {site_url} on {target_date}"
            )
            return paginated_result

        except Exception as e:
            error_msg = f"Error getting paginated daily data: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}

    def check_recent_data_availability(
        self, user, site_url: str, search_type: str = "web", days_to_check: int = 10
    ) -> Dict[str, Any]:
        """
        Check what is the most recently available data by running a simple query
        grouped by date for the past 10 days
        """
        service = self.get_gsc_service(user)
        if not service:
            return {"error": "No valid GSC service available"}

        try:
            end_date = timezone.now().date()
            start_date = end_date - timezone.timedelta(days=days_to_check)

            request_body = {
                "startDate": start_date.strftime("%Y-%m-%d"),
                "endDate": end_date.strftime("%Y-%m-%d"),
                "dimensions": ["date"],
                "type": search_type,
                "rowLimit": days_to_check,
            }

            result = (
                service.searchanalytics()
                .query(siteUrl=site_url, body=request_body)
                .execute()
            )

            # Process the results to find the most recent data
            available_dates = []
            if "rows" in result:
                for row in result["rows"]:
                    date_str = row["keys"][0]
                    available_dates.append(
                        {
                            "date": date_str,
                            "clicks": row.get("clicks", 0),
                            "impressions": row.get("impressions", 0),
                            "ctr": row.get("ctr", 0),
                            "position": row.get("position", 0),
                        }
                    )

            # Sort by date to find the most recent
            available_dates.sort(key=lambda x: x["date"], reverse=True)

            availability_info = {
                "site_url": site_url,
                "search_type": search_type,
                "check_period": f"{start_date} to {end_date}",
                "available_dates": available_dates,
                "most_recent_date": (
                    available_dates[0]["date"] if available_dates else None
                ),
                "total_days_with_data": len(available_dates),
                "recommendation": {
                    "suggested_target_date": (
                        available_dates[0]["date"] if available_dates else None
                    ),
                    "note": "Use this date for optimal data retrieval",
                },
            }

            logger.info(
                f"Checked data availability for {site_url}: {len(available_dates)} days with data"
            )
            return availability_info

        except Exception as e:
            error_msg = f"Error checking data availability: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}


# Convenience function to get service instance
def get_gsc_service():
    """Get a GSC service instance"""
    return GSCService()
