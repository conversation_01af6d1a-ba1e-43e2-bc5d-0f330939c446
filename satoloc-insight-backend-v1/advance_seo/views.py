from django.shortcuts import render, get_object_or_404
from django.db.models import Q, Count
from django.db import transaction
from django.http import HttpRequest, JsonResponse
from django.utils.decorators import method_decorator
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from rest_framework.views import APIView
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser, AllowAny
import traceback

from .models import (
    Website,
    SEOMetrics,
    TechnicalAnalysis,
    Competitor,
    RegionalPerformance,
    UserWebsiteAnalysis,
    WebsiteLanguage,
    Keyword,
    CompetitorKeyword,
    WebsiteStrategyGap,
    WebsiteGrowthOpportunity,
    RankingIssue,
    ContentRecommendation,
    GoogleSearchConsoleCredential,
)
from .serializers import (
    WebsiteSerializer,
    SEOMetricsSerializer,
    TechnicalAnalysisSerializer,
    CompetitorSerializer,
    RegionalPerformanceSerializer,
    WebsiteDetailSerializer,
)
from .services import (
    SEODataService,
    import_competitors_from_csv_file,
    import_keywords_from_csv_for_competitor,
)
from .gsc_service import get_gsc_service
from .pagespeed_service import PageSpeedInsightsService


# Create your views here.


class WebsiteViewSet(viewsets.ModelViewSet):
    """
    API endpoint for websites
    """

    queryset = Website.objects.all()
    serializer_class = WebsiteSerializer
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.action == "retrieve" or self.action == "list":
            return WebsiteDetailSerializer
        return WebsiteSerializer

    def get_permissions(self):
        """
        Admin-only access for destroy actions
        List action is now accessible to all authenticated users
        """
        if self.action in ["destroy"]:
            return [IsAuthenticated(), IsAdminUser()]
        return super().get_permissions()

    def update(self, request, *args, **kwargs):
        """
        Custom update method to ensure URL is preserved exactly as provided
        """
        partial = kwargs.pop("partial", False)
        instance = self.get_object()

        # Log the incoming data for debugging
        print(f"Update data received: {request.data}")

        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, "_prefetched_objects_cache", None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        return Response(serializer.data)

    def list(self, request, *args, **kwargs):
        """
        List all websites (accessible to all authenticated users)
        """
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def all_websites(self, request):
        """
        Get all websites in the database without user-specific data
        This is accessible to all authenticated users
        """
        queryset = Website.objects.all()
        serializer = WebsiteDetailSerializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def my_analyses(self, request):
        """
        Get all websites analyzed by the current user
        """
        user_analyses = UserWebsiteAnalysis.objects.filter(
            user=request.user
        ).select_related("website")
        websites = [analysis.website for analysis in user_analyses]

        # Add is_favorite and last_viewed_at to each website
        for i, website in enumerate(websites):
            website.is_favorite = user_analyses[i].is_favorite
            website.last_viewed_at = user_analyses[i].last_viewed_at
            website.notes = user_analyses[i].notes

        serializer = WebsiteDetailSerializer(websites, many=True)
        data = serializer.data

        # Add user-specific fields to the serialized data
        for i, item in enumerate(data):
            item["is_favorite"] = websites[i].is_favorite
            item["last_viewed_at"] = (
                websites[i].last_viewed_at.isoformat()
                if hasattr(websites[i], "last_viewed_at")
                else None
            )
            item["notes"] = websites[i].notes

        return Response(data)

    @action(detail=True, methods=["post"])
    def toggle_favorite(self, request, pk=None):
        """
        Toggle favorite status for a website
        """
        website = self.get_object()
        analysis, created = UserWebsiteAnalysis.objects.get_or_create(
            user=request.user,
            website=website,
            defaults={"last_viewed_at": timezone.now()},
        )
        # Toggle the favorite status
        analysis.is_favorite = not analysis.is_favorite
        analysis.save()
        return Response({"is_favorite": analysis.is_favorite})

    @action(detail=True, methods=["post"], url_path="competitors/(?P<competitor_pk>[^/.]+)/upload_keywords_csv")
    def upload_keywords_csv_for_competitor(self, request, pk=None, competitor_pk=None):
        """
        Upload a CSV file to import keywords for a specific competitor.
        """
        try:
            website = self.get_object()
            competitor = get_object_or_404(
                Competitor, pk=competitor_pk, website=website
            )

            if "file" not in request.FILES:
                return Response(
                    {"error": "No file provided"}, status=status.HTTP_400_BAD_REQUEST
                )

            csv_file = request.FILES["file"]

            if not csv_file.name.endswith(".csv"):
                return Response(
                    {"error": "File is not a CSV"}, status=status.HTTP_400_BAD_REQUEST
                )

            keywords_added, errors = import_keywords_from_csv_for_competitor(
                csv_file, website, competitor.id
            )

            return Response(
                {
                    "success": f"{keywords_added} keywords imported successfully for {competitor.target_url}",
                    "errors": errors,
                },
                status=status.HTTP_201_CREATED,
            )
        except Exception as e:
            traceback.print_exc()
            return Response(
                {"error": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["post"], url_path="upload_competitors_csv")
    def upload_competitors_csv(self, request, pk=None):
        """
        Upload a CSV file to import competitors for this website.
        Accepts multipart/form-data with a 'file' field.
        """
        website = self.get_object()
        file_obj = request.FILES.get("file")
        if not file_obj:
            print("[UPLOAD DEBUG] No file provided in request.FILES")
            return Response(
                {"error": "No file provided"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            added_count = import_competitors_from_csv_file(website, file_obj)
        except Exception as e:
            tb = traceback.format_exc()
            print(f"[UPLOAD DEBUG] Exception during CSV import: {e}\n{tb}")
            return Response(
                {"error": str(e), "traceback": tb}, status=status.HTTP_400_BAD_REQUEST
            )

        return Response({"message": f"Imported {added_count} competitors from CSV."})

    @action(detail=True, methods=["post"])
    def add_notes(self, request, pk=None):
        """
        Add notes to a website analysis
        """
        website = self.get_object()
        notes = request.data.get("notes", "")

        analysis, created = UserWebsiteAnalysis.objects.get_or_create(
            user=request.user,
            website=website,
            defaults={"last_viewed_at": timezone.now()},
        )

        analysis.notes = notes
        analysis.save()

        return Response({"notes": analysis.notes})

    @action(detail=False, methods=["get"])
    def by_url(self, request):
        """
        Get website data by URL
        """
        url = request.query_params.get("url")

        if not url:
            return Response(
                {"error": "URL parameter is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Keep original URL
        original_url = url

        # Clean the URL (remove protocol, www, trailing slashes) only for searching
        cleaned_url = (
            url.replace("http://", "")
            .replace("https://", "")
            .replace("www.", "")
            .rstrip("/")
        )

        # Try to find the website by cleaned URL first
        website = Website.objects.filter(url__iexact=cleaned_url).first()

        # If not found, try with the original URL
        if not website:
            website = Website.objects.filter(url__iexact=original_url).first()

        if not website:
            return Response(
                {"error": f"Website with URL {url} not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        # Create or update UserWebsiteAnalysis record
        UserWebsiteAnalysis.objects.update_or_create(
            user=request.user,
            website=website,
            defaults={"last_viewed_at": timezone.now()},
        )

        serializer = WebsiteDetailSerializer(website)
        return Response(serializer.data)

    @action(detail=False, methods=["post"])
    def process(self, request):
        """
        Process a website and fetch SEO data

        Request body:
            url (str): URL of the website to process
            industry (str, optional): Industry of the website
            language (str, optional): Language of the website (supported: 'en' for English, 'tr' for Turkish)
        """
        url = request.data.get("url")
        industry = request.data.get("industry")
        language = request.data.get("language")

        if not url:
            return Response(
                {"error": "URL is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        # Normalize language value
        if language:
            # Map full language names to ISO codes
            language_mapping = {
                "english": "en",
                "turkish": "tr",
                # Add more mappings as needed
            }

            # Convert to lowercase for case-insensitive matching
            normalized_language = language.lower()

            # If it's a full language name, convert to ISO code
            if normalized_language in language_mapping:
                language = language_mapping[normalized_language]

        # Validate language if provided
        supported_languages = ["en", "tr"]
        if language and language.lower() not in supported_languages:
            return Response(
                {
                    "error": f'Language {language} is not supported. Supported languages: {", ".join(supported_languages)}'
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        service = SEODataService()
        website = service.process_website(url, industry, language)

        # Create or update UserWebsiteAnalysis record
        UserWebsiteAnalysis.objects.update_or_create(
            user=request.user,
            website=website,
            defaults={"last_viewed_at": timezone.now()},
        )

        serializer = WebsiteDetailSerializer(website)
        return Response(serializer.data)

    @action(detail=True, methods=["get"])
    def competitors(self, request, pk=None):
        """
        Get competitors for a website
        """
        website = self.get_object()
        competitors = website.competitors.all()
        serializer = CompetitorSerializer(competitors, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=["get"])
    def seo_metrics(self, request, pk=None):
        """
        Get SEO metrics for a website
        """
        website = self.get_object()
        try:
            metrics = website.seo_metrics
            serializer = SEOMetricsSerializer(metrics)
            return Response(serializer.data)
        except SEOMetrics.DoesNotExist:
            return Response(
                {"error": "SEO metrics not found for this website"},
                status=status.HTTP_404_NOT_FOUND,
            )

    @action(detail=True, methods=["get"])
    def technical_analysis(self, request, pk=None):
        """
        Get technical analysis for a website
        """
        website = self.get_object()
        try:
            analysis = website.technical_analysis
            serializer = TechnicalAnalysisSerializer(analysis)
            return Response(serializer.data)
        except TechnicalAnalysis.DoesNotExist:
            return Response(
                {"error": "Technical analysis not found for this website"},
                status=status.HTTP_404_NOT_FOUND,
            )

    @action(detail=True, methods=["get"])
    def regional_performance(self, request, pk=None):
        """
        Get regional performance for a website
        """
        website = self.get_object()
        performance = website.regional_performance.all()
        serializer = RegionalPerformanceSerializer(performance, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def add_language(self, request, pk=None):
        """
        Add a language to a website
        """
        website = self.get_object()
        language = request.data.get("language")

        if not language:
            return Response(
                {"error": "Language is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        # Check if language already exists
        if WebsiteLanguage.objects.filter(website=website, language=language).exists():
            return Response(
                {"error": f"Language {language} already exists for this website"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Add language
        WebsiteLanguage.objects.create(website=website, language=language)

        return Response({"success": True, "language": language})

    @action(detail=True, methods=["post"])
    def remove_language(self, request, pk=None):
        """
        Remove a language from a website
        """
        website = self.get_object()
        language = request.data.get("language")

        if not language:
            return Response(
                {"error": "Language is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        # Check if language exists
        try:
            language_obj = WebsiteLanguage.objects.get(
                website=website, language=language
            )
            language_obj.delete()
            return Response({"success": True})
        except WebsiteLanguage.DoesNotExist:
            return Response(
                {"error": f"Language {language} does not exist for this website"},
                status=status.HTTP_404_NOT_FOUND,
            )

    @action(detail=True, methods=["post"])
    def add_keyword(self, request, pk=None):
        """
        Add a keyword to a website
        """
        website = self.get_object()
        keyword = request.data.get("keyword")
        is_target = request.data.get("is_target", False)

        if not keyword:
            return Response(
                {"error": "Keyword is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        # Check if keyword already exists
        if Keyword.objects.filter(website=website, keyword=keyword).exists():
            return Response(
                {"error": f"Keyword {keyword} already exists for this website"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Add keyword
        Keyword.objects.create(website=website, keyword=keyword, is_target=is_target)

        return Response({"success": True, "keyword": keyword, "is_target": is_target})

    @action(detail=True, methods=["post"])
    def remove_keyword(self, request, pk=None):
        """
        Remove a keyword from a website
        """
        website = self.get_object()
        keyword = request.data.get("keyword")

        if not keyword:
            return Response(
                {"error": "Keyword is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        # Check if keyword exists
        try:
            keyword_obj = Keyword.objects.get(website=website, keyword=keyword)
            keyword_obj.delete()
            return Response({"success": True})
        except Keyword.DoesNotExist:
            return Response(
                {"error": f"Keyword {keyword} does not exist for this website"},
                status=status.HTTP_404_NOT_FOUND,
            )

    @action(detail=True, methods=["post"])
    def update_keyword(self, request, pk=None):
        """
        Update a keyword's target status
        """
        website = self.get_object()
        keyword = request.data.get("keyword")
        is_target = request.data.get("is_target")

        if not keyword:
            return Response(
                {"error": "Keyword is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        if is_target is None:
            return Response(
                {"error": "is_target is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        # Check if keyword exists
        try:
            keyword_obj = Keyword.objects.get(website=website, keyword=keyword)
            keyword_obj.is_target = is_target
            keyword_obj.save()
            return Response(
                {"success": True, "keyword": keyword, "is_target": is_target}
            )
        except Keyword.DoesNotExist:
            return Response(
                {"error": f"Keyword {keyword} does not exist for this website"},
                status=status.HTTP_404_NOT_FOUND,
            )

    @action(detail=True, methods=["post"])
    def add_competitor(self, request, pk=None):
        """
        Add a competitor to a website
        """
        website = self.get_object()
        name = request.data.get("name")
        target_url = request.data.get("target_url")
        description = request.data.get("description", "")
        rank = request.data.get("rank")

        if not name or not target_url:
            return Response(
                {"error": "Name and target_url are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Normalize URL
        target_url = (
            target_url.replace("http://", "")
            .replace("https://", "")
            .replace("www.", "")
            .rstrip("/")
        )

        # Check if competitor already exists
        if Competitor.objects.filter(website=website, name=name).exists():
            return Response(
                {"error": f"Competitor {name} already exists for this website"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Add competitor
        competitor = Competitor.objects.create(
            website=website,
            name=name,
            target_url=target_url,
            description=description,
            rank=rank,
        )

        return Response(
            {
                "success": True,
                "competitor": {
                    "id": competitor.id,
                    "name": competitor.name,
                    "target_url": competitor.target_url,
                    "description": competitor.description,
                    "rank": competitor.rank,
                },
            }
        )

    @action(detail=True, methods=["post"])
    def remove_competitor(self, request, pk=None):
        """
        Remove a competitor from a website
        """
        website = self.get_object()
        competitor_id = request.data.get("competitor_id")

        if not competitor_id:
            return Response(
                {"error": "competitor_id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if competitor exists
        try:
            competitor = Competitor.objects.get(id=competitor_id, website=website)
            competitor.delete()
            return Response({"success": True})
        except Competitor.DoesNotExist:
            return Response(
                {
                    "error": f"Competitor with ID {competitor_id} does not exist for this website"
                },
                status=status.HTTP_404_NOT_FOUND,
            )

    @action(detail=True, methods=["post"])
    def update_competitor(self, request, pk=None):
        """
        Update a competitor's information
        """
        website = self.get_object()
        competitor_id = request.data.get("competitor_id")
        target_url = request.data.get("target_url")
        original_target_url = request.data.get(
            "original_target_url"
        )  # To identify the competitor
        name = request.data.get("name")
        description = request.data.get("description")
        rank = request.data.get("rank")

        # We can identify a competitor by either its ID or original target_url
        if not competitor_id and not original_target_url:
            return Response(
                {
                    "error": "Either competitor_id or original_target_url is required to identify the competitor"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Normalize URL if provided
        if target_url:
            target_url = (
                target_url.replace("http://", "")
                .replace("https://", "")
                .replace("www.", "")
                .rstrip("/")
            )

        if original_target_url:
            original_target_url = (
                original_target_url.replace("http://", "")
                .replace("https://", "")
                .replace("www.", "")
                .rstrip("/")
            )

        # First try by ID if provided
        if competitor_id:
            try:
                competitor = Competitor.objects.get(id=competitor_id, website=website)
            except Competitor.DoesNotExist:
                competitor = None
        else:
            competitor = None

        # If not found by ID, try by target_url
        if not competitor and original_target_url:
            try:
                competitor = Competitor.objects.get(
                    website=website, target_url=original_target_url
                )
            except Competitor.DoesNotExist:
                return Response(
                    {
                        "error": f"Competitor with target URL {original_target_url} does not exist for this website"
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )

        if not competitor:
            return Response(
                {"error": "Competitor not found"}, status=status.HTTP_404_NOT_FOUND
            )

        # Update competitor information
        changes_made = False
        if name:
            competitor.name = name
            changes_made = True

        if target_url:
            competitor.target_url = target_url
            changes_made = True

        if description is not None:  # Allow empty description
            competitor.description = description
            changes_made = True

        if rank is not None:  # Allow rank to be 0
            competitor.rank = rank
            changes_made = True

        if changes_made:
            competitor.save()

        return Response(
            {
                "success": True,
                "competitor": {
                    "id": competitor.id,
                    "name": competitor.name,
                    "target_url": competitor.target_url,
                    "description": competitor.description,
                    "rank": competitor.rank,
                },
            }
        )

    @action(
        detail=True,
        methods=["post"],
        url_path="competitors/(?P<competitor_pk>[^/.]+)/update_keywords",
    )
    def update_keywords_for_competitor(self, request, pk=None, competitor_pk=None):
        """
        Update keywords for a specific competitor.
        This action replaces all existing keywords with the new list provided.
        """
        try:
            website = self.get_object()
            competitor = get_object_or_404(
                Competitor, pk=competitor_pk, website=website
            )

            # Get keywords from request data, expecting a list of strings
            keywords_list = request.data.get("keywords", [])

            if not isinstance(keywords_list, list):
                return Response(
                    {"error": "Keywords data must be a list of strings."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            with transaction.atomic():
                # Clear existing keywords for this competitor
                CompetitorKeyword.objects.filter(competitor=competitor).delete()

                # Create new keywords
                new_keywords = []
                for keyword_text in keywords_list:
                    if keyword_text and isinstance(keyword_text, str):
                        new_keywords.append(
                            CompetitorKeyword(
                                competitor=competitor, keyword=keyword_text.strip()
                            )
                        )

                if new_keywords:
                    CompetitorKeyword.objects.bulk_create(new_keywords)

            return Response(
                {
                    "success": f"{len(new_keywords)} keywords updated for {competitor.target_url}"
                },
                status=status.HTTP_200_OK,
            )

        except Competitor.DoesNotExist:
            return Response(
                {"error": "Competitor not found."},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            traceback.print_exc()
            return Response(
                {"error": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["post"])
    def add_ranking_issue(self, request, pk=None):
        """
        Add a ranking issue to a competitor
        """
        website = self.get_object()
        competitor_id = request.data.get("competitor_id")
        title = request.data.get("title")
        description = request.data.get("description")
        impact = request.data.get("impact", "medium")

        if not competitor_id or not title or not description:
            return Response(
                {"error": "competitor_id, title, and description are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if competitor exists
        try:
            competitor = Competitor.objects.get(id=competitor_id, website=website)
        except Competitor.DoesNotExist:
            return Response(
                {
                    "error": f"Competitor with ID {competitor_id} does not exist for this website"
                },
                status=status.HTTP_404_NOT_FOUND,
            )

        # Validate impact
        valid_impacts = ["high", "medium", "low"]
        if impact not in valid_impacts:
            return Response(
                {"error": f'Impact must be one of: {", ".join(valid_impacts)}'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Create the ranking issue
        ranking_issue = RankingIssue.objects.create(
            competitor=competitor, title=title, description=description, impact=impact
        )

        return Response(
            {
                "success": True,
                "ranking_issue": {
                    "id": ranking_issue.id,
                    "title": ranking_issue.title,
                    "description": ranking_issue.description,
                    "impact": ranking_issue.impact,
                },
            }
        )

    @action(detail=True, methods=["post"])
    def remove_ranking_issue(self, request, pk=None):
        """
        Remove a ranking issue from a competitor
        """
        website = self.get_object()
        ranking_issue_id = request.data.get("ranking_issue_id")

        if not ranking_issue_id:
            return Response(
                {"error": "ranking_issue_id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if the ranking issue exists and belongs to a competitor of this website
        try:
            ranking_issue = RankingIssue.objects.get(
                id=ranking_issue_id, competitor__website=website
            )
            ranking_issue.delete()
            return Response({"success": True})
        except RankingIssue.DoesNotExist:
            return Response(
                {
                    "error": f"Ranking issue with ID {ranking_issue_id} does not exist for this website"
                },
                status=status.HTTP_404_NOT_FOUND,
            )

    @action(detail=True, methods=["post"])
    def add_content_recommendation(self, request, pk=None):
        """
        Add a content recommendation to a competitor
        """
        website = self.get_object()
        competitor_id = request.data.get("competitor_id")
        title = request.data.get("title")
        description = request.data.get("description")
        impact = request.data.get("impact", "medium")
        estimated_hours = request.data.get("estimated_hours", 2)
        is_opportunity = request.data.get("is_opportunity", False)

        if not competitor_id or not title or not description:
            return Response(
                {"error": "competitor_id, title, and description are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if competitor exists
        try:
            competitor = Competitor.objects.get(id=competitor_id, website=website)
        except Competitor.DoesNotExist:
            return Response(
                {
                    "error": f"Competitor with ID {competitor_id} does not exist for this website"
                },
                status=status.HTTP_404_NOT_FOUND,
            )

        # Validate impact
        valid_impacts = ["high", "medium", "low"]
        if impact not in valid_impacts:
            return Response(
                {"error": f'Impact must be one of: {", ".join(valid_impacts)}'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Create the content recommendation
        recommendation = ContentRecommendation.objects.create(
            competitor=competitor,
            title=title,
            description=description,
            impact=impact,
            estimated_hours=estimated_hours,
            is_opportunity=is_opportunity,
        )

        return Response(
            {
                "success": True,
                "recommendation": {
                    "id": recommendation.id,
                    "title": recommendation.title,
                    "description": recommendation.description,
                    "impact": recommendation.impact,
                    "estimated_hours": recommendation.estimated_hours,
                    "is_opportunity": recommendation.is_opportunity,
                },
            }
        )

    @action(detail=True, methods=["post"])
    def remove_content_recommendation(self, request, pk=None):
        """
        Remove a content recommendation from a competitor
        """
        website = self.get_object()
        recommendation_id = request.data.get("recommendation_id")

        if not recommendation_id:
            return Response(
                {"error": "recommendation_id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if the recommendation exists and belongs to a competitor of this website
        try:
            recommendation = ContentRecommendation.objects.get(
                id=recommendation_id, competitor__website=website
            )
            recommendation.delete()
            return Response({"success": True})
        except ContentRecommendation.DoesNotExist:
            return Response(
                {
                    "error": f"Content recommendation with ID {recommendation_id} does not exist for this website"
                },
                status=status.HTTP_404_NOT_FOUND,
            )

    @action(detail=True, methods=["patch"])
    def update_seo_metrics(self, request, pk=None):
        """
        Update SEO metrics for a website
        """
        website = self.get_object()

        try:
            metrics, created = SEOMetrics.objects.get_or_create(website=website)

            # Update fields that are provided in the request
            for field in [
                "domain_rating_score",
                "domain_rating_previous_score",
                "organic_traffic_score",
                "organic_traffic_previous_score",
                "search_rankings_score",
                "search_rankings_previous_score",
                "search_terms_score",
                "search_terms_previous_score",
                "site_links_score",
                "site_links_previous_score",
                "content_score",
                "content_score_previous_score",
                "backlinks_count",
            ]:
                if field in request.data:
                    value = request.data.get(field)
                    # Convert to None if the value is empty string or null
                    if value == "" or value is None:
                        value = None
                    setattr(metrics, field, value)

            metrics.save()
            serializer = SEOMetricsSerializer(metrics)
            return Response(serializer.data)

        except Exception as e:
            return Response(
                {"error": f"Failed to update SEO metrics: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def add_strategy_gap(self, request, pk=None):
        """
        Add a strategy gap to a website
        """
        website = self.get_object()
        text = request.data.get("text", "")

        if not text:
            return Response(
                {"error": "Text is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        # Create the strategy gap
        strategy_gap = WebsiteStrategyGap.objects.create(website=website, text=text)

        return Response(
            {"success": True, "id": strategy_gap.id, "text": strategy_gap.text}
        )

    @action(detail=True, methods=["post"])
    def remove_strategy_gap(self, request, pk=None):
        """
        Remove a strategy gap from a website
        """
        website = self.get_object()
        gap_id = request.data.get("gap_id")

        if not gap_id:
            return Response(
                {"error": "gap_id is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        # Check if strategy gap exists and delete it
        try:
            strategy_gap = WebsiteStrategyGap.objects.get(
                id=int(gap_id), website=website
            )
            strategy_gap.delete()
            return Response({"success": True})
        except WebsiteStrategyGap.DoesNotExist:
            return Response(
                {
                    "error": f"Strategy gap with ID {gap_id} does not exist for this website"
                },
                status=status.HTTP_404_NOT_FOUND,
            )
        except ValueError:
            return Response(
                {"error": "Invalid strategy gap ID format"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def add_growth_opportunity(self, request, pk=None):
        """
        Add a growth opportunity to a website
        """
        website = self.get_object()
        text = request.data.get("text", "")

        if not text:
            return Response(
                {"error": "Text is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        # Create the growth opportunity
        growth_opportunity = WebsiteGrowthOpportunity.objects.create(
            website=website, text=text
        )

        return Response(
            {
                "success": True,
                "id": growth_opportunity.id,
                "text": growth_opportunity.text,
            }
        )

    @action(detail=True, methods=["post"])
    def remove_growth_opportunity(self, request, pk=None):
        """
        Remove a growth opportunity from a website
        """
        website = self.get_object()
        opportunity_id = request.data.get("opportunity_id")

        if not opportunity_id:
            return Response(
                {"error": "opportunity_id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if growth opportunity exists and delete it
        try:
            growth_opportunity = WebsiteGrowthOpportunity.objects.get(
                id=int(opportunity_id), website=website
            )
            growth_opportunity.delete()
            return Response({"success": True})
        except WebsiteGrowthOpportunity.DoesNotExist:
            return Response(
                {
                    "error": f"Growth opportunity with ID {opportunity_id} does not exist for this website"
                },
                status=status.HTTP_404_NOT_FOUND,
            )
        except ValueError:
            return Response(
                {"error": "Invalid growth opportunity ID format"},
                status=status.HTTP_400_BAD_REQUEST,
            )


# Google Search Console Integration Views
class GSCConnectView(APIView):
    """API endpoint for connecting to Google Search Console"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Exchange auth code for tokens and store them"""
        try:
            auth_code = request.data.get("auth_code")
            redirect_uri = request.data.get("redirect_uri")

            if not auth_code:
                return Response(
                    {"error": "Auth code is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not redirect_uri:
                return Response(
                    {"error": "Redirect URI is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            user = request.user
            gsc_service = get_gsc_service()

            # Exchange authorization code for tokens
            token_data = gsc_service.exchange_code_for_tokens(auth_code, redirect_uri)

            # Store the credentials
            credential = gsc_service.store_credentials(user, token_data)

            # Get user's sites to verify connection
            sites = gsc_service.get_user_sites(user)

            return Response(
                {
                    "success": True,
                    "message": "Successfully connected to Google Search Console",
                    "sites_count": len(sites),
                    "sites": sites[:5],  # Return first 5 sites as preview
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            error_message = str(e)

            # Provide more helpful error messages for common issues
            if "invalid_grant" in error_message.lower():
                error_message = "Authorization code expired or already used. Please try connecting again."
            elif "scope" in error_message.lower():
                error_message = (
                    "Permission scope mismatch. Please grant the required permissions."
                )
            elif "redirect_uri" in error_message.lower():
                error_message = "Redirect URI mismatch. Please check your Google OAuth configuration."
            elif (
                "accessNotConfigured" in error_message
                or "API has not been used" in error_message
            ):
                error_message = "Google Search Console API is not enabled in your Google Cloud project. Please enable it in the Google Cloud Console and try again."

            return Response(
                {
                    "error": error_message,
                    "message": "Failed to connect to Google Search Console",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class GSCDisconnectView(APIView):
    """API endpoint for disconnecting from Google Search Console"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Remove stored tokens"""
        try:
            user = request.user
            gsc_service = get_gsc_service()

            # Disconnect user from GSC
            success = gsc_service.disconnect_user(user)

            if success:
                return Response(
                    {
                        "success": True,
                        "message": "Successfully disconnected from Google Search Console",
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "error": "Failed to disconnect",
                        "message": "Failed to disconnect from Google Search Console",
                    },
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        except Exception as e:
            return Response(
                {
                    "error": str(e),
                    "message": "Failed to disconnect from Google Search Console",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class GSCStatusView(APIView):
    """API endpoint for checking Google Search Console connection status"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Check if user is connected to GSC"""
        try:
            user = request.user
            gsc_service = get_gsc_service()

            # Check if user has valid GSC credentials
            try:
                credential = GoogleSearchConsoleCredential.objects.get(user=user)
                is_connected = True
                is_expired = credential.is_expired

                # Test the connection by trying to get sites
                try:
                    sites = gsc_service.get_user_sites(user)
                    connection_valid = True
                except Exception as e:
                    sites = []
                    connection_valid = False
                    # Check if it's an API not enabled error
                    if "accessNotConfigured" in str(
                        e
                    ) or "API has not been used" in str(e):
                        # Keep connection as valid but note the API issue
                        connection_valid = True

            except GoogleSearchConsoleCredential.DoesNotExist:
                is_connected = False
                is_expired = None
                sites = []

            response_data = {
                "is_connected": is_connected,
                "is_expired": is_expired if is_connected else None,
            }

            # Add sites info if connected
            if is_connected:
                response_data["sites_count"] = len(sites) if "sites" in locals() else 0

                # Add API status information
                if not connection_valid and "sites" in locals():
                    response_data["api_enabled"] = False
                    response_data["api_error"] = (
                        "Google Search Console API is not enabled in your Google Cloud project"
                    )
                else:
                    response_data["api_enabled"] = True
                    # Include site names in the response
                    if "sites" in locals() and sites:
                        response_data["sites"] = sites

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {
                    "error": str(e),
                    "message": "Failed to check Google Search Console connection status",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class GSCSitesView(APIView):
    """API endpoint for getting user's GSC sites"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get list of sites accessible to the user in GSC"""
        try:
            user = request.user
            gsc_service = get_gsc_service()

            sites = gsc_service.get_user_sites(user)

            return Response(
                {"success": True, "sites": sites, "count": len(sites)},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "error": str(e),
                    "message": "Failed to retrieve GSC sites",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class GSCSearchAnalyticsView(APIView):
    """API endpoint for getting search analytics data from GSC"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Get search analytics data for a specific site"""
        try:
            user = request.user
            gsc_service = get_gsc_service()

            # Required parameters
            site_url = request.data.get("site_url")
            start_date = request.data.get("start_date")
            end_date = request.data.get("end_date")

            # Optional parameters
            dimensions = request.data.get("dimensions", ["query"])
            row_limit = request.data.get("row_limit", 1000)

            if not all([site_url, start_date, end_date]):
                return Response(
                    {"error": "site_url, start_date, and end_date are required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get search analytics data
            analytics_data = gsc_service.get_search_analytics(
                user, site_url, start_date, end_date, dimensions, row_limit
            )

            if "error" in analytics_data:
                return Response(
                    {"error": analytics_data["error"]},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            return Response(
                {
                    "success": True,
                    "data": analytics_data,
                    "site_url": site_url,
                    "date_range": f"{start_date} to {end_date}",
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "error": str(e),
                    "message": "Failed to retrieve search analytics data",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class GSCUrlInspectionView(APIView):
    """API endpoint for URL inspection data from GSC"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Get URL inspection data for a specific URL"""
        try:
            user = request.user
            gsc_service = get_gsc_service()

            site_url = request.data.get("site_url")
            inspect_url = request.data.get("inspect_url")

            if not all([site_url, inspect_url]):
                return Response(
                    {"error": "site_url and inspect_url are required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get URL inspection data
            inspection_data = gsc_service.get_site_inspection(
                user, site_url, inspect_url
            )

            if "error" in inspection_data:
                return Response(
                    {"error": inspection_data["error"]},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            return Response(
                {
                    "success": True,
                    "data": inspection_data,
                    "site_url": site_url,
                    "inspect_url": inspect_url,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "error": str(e),
                    "message": "Failed to retrieve URL inspection data",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class GSCSitemapsView(APIView):
    """API endpoint for getting sitemap data from GSC"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Get sitemap information for a specific site"""
        try:
            user = request.user
            gsc_service = get_gsc_service()

            site_url = request.data.get("site_url")

            if not site_url:
                return Response(
                    {"error": "site_url is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get sitemaps data
            sitemaps = gsc_service.get_sitemaps(user, site_url)

            return Response(
                {
                    "success": True,
                    "sitemaps": sitemaps,
                    "count": len(sitemaps),
                    "site_url": site_url,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "error": str(e),
                    "message": "Failed to retrieve sitemaps data",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class GSCAdvancedSearchAnalyticsView(APIView):
    """API endpoint for comprehensive Google Search Console search analytics data"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Get comprehensive search analytics data with all available options"""
        try:
            user = request.user
            gsc_service = get_gsc_service()

            # Required parameters
            site_url = request.data.get("site_url")
            start_date = request.data.get("start_date")
            end_date = request.data.get("end_date")

            # Optional parameters with defaults
            dimensions = request.data.get("dimensions", ["query"])
            search_type = request.data.get(
                "type", "web"
            )  # web, image, video, news, discover, googleNews
            row_limit = request.data.get("row_limit", 1000)
            start_row = request.data.get("start_row", 0)
            aggregation_type = request.data.get(
                "aggregation_type", "auto"
            )  # auto, byPage, byProperty
            data_state = request.data.get(
                "data_state", "final"
            )  # final, all, hourly_all

            # Advanced filtering
            dimension_filter_groups = request.data.get("dimension_filter_groups", [])

            if not all([site_url, start_date, end_date]):
                return Response(
                    {"error": "site_url, start_date, and end_date are required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get comprehensive search analytics data
            analytics_data = gsc_service.get_advanced_search_analytics(
                user=user,
                site_url=site_url,
                start_date=start_date,
                end_date=end_date,
                dimensions=dimensions,
                search_type=search_type,
                row_limit=row_limit,
                start_row=start_row,
                aggregation_type=aggregation_type,
                data_state=data_state,
                dimension_filter_groups=dimension_filter_groups,
            )

            if "error" in analytics_data:
                return Response(
                    {"error": analytics_data["error"]},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            return Response(
                {
                    "success": True,
                    "data": analytics_data,
                    "parameters": {
                        "site_url": site_url,
                        "date_range": f"{start_date} to {end_date}",
                        "dimensions": dimensions,
                        "search_type": search_type,
                        "row_limit": row_limit,
                        "start_row": start_row,
                        "aggregation_type": aggregation_type,
                        "data_state": data_state,
                        "filters_applied": len(dimension_filter_groups) > 0,
                    },
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "error": str(e),
                    "message": "Failed to retrieve advanced search analytics data",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class GSCBulkDataView(APIView):
    """API endpoint for bulk GSC data retrieval with multiple data types"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Get bulk data from multiple GSC endpoints"""
        try:
            user = request.user
            gsc_service = get_gsc_service()

            site_url = request.data.get("site_url")
            start_date = request.data.get("start_date")
            end_date = request.data.get("end_date")

            # Data types to retrieve
            include_search_analytics = request.data.get(
                "include_search_analytics", True
            )
            include_url_inspection = request.data.get("include_url_inspection", False)
            include_sitemaps = request.data.get("include_sitemaps", True)

            # URLs for inspection (if requested)
            inspect_urls = request.data.get("inspect_urls", [])

            if not site_url:
                return Response(
                    {"error": "site_url is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            bulk_data = {
                "site_url": site_url,
                "timestamp": timezone.now().isoformat(),
                "data": {},
            }

            # Get search analytics data (multiple types)
            if include_search_analytics:
                search_types = ["web", "image", "video", "news", "discover"]
                dimensions_sets = [
                    ["query"],
                    ["page"],
                    ["country"],
                    ["device"],
                    ["searchAppearance"],
                    ["query", "page"],
                    ["country", "device"],
                ]

                bulk_data["data"]["search_analytics"] = {}

                for search_type in search_types:
                    bulk_data["data"]["search_analytics"][search_type] = {}

                    for dimensions in dimensions_sets:
                        try:
                            analytics_data = gsc_service.get_advanced_search_analytics(
                                user=user,
                                site_url=site_url,
                                start_date=start_date or "2024-01-01",
                                end_date=end_date or "2024-12-31",
                                dimensions=dimensions,
                                search_type=search_type,
                                row_limit=100,
                            )

                            dimension_key = "_".join(dimensions)
                            bulk_data["data"]["search_analytics"][search_type][
                                dimension_key
                            ] = analytics_data

                        except Exception as e:
                            bulk_data["data"]["search_analytics"][search_type][
                                dimension_key
                            ] = {"error": str(e)}

            # Get sitemaps data
            if include_sitemaps:
                sitemaps = gsc_service.get_sitemaps(user, site_url)
                bulk_data["data"]["sitemaps"] = sitemaps

            # Get URL inspection data
            if include_url_inspection and inspect_urls:
                bulk_data["data"]["url_inspections"] = {}

                for inspect_url in inspect_urls:
                    try:
                        inspection_data = gsc_service.get_site_inspection(
                            user, site_url, inspect_url
                        )
                        bulk_data["data"]["url_inspections"][
                            inspect_url
                        ] = inspection_data
                    except Exception as e:
                        bulk_data["data"]["url_inspections"][inspect_url] = {
                            "error": str(e)
                        }

            return Response(
                {
                    "success": True,
                    "bulk_data": bulk_data,
                    "summary": {
                        "search_analytics_included": include_search_analytics,
                        "sitemaps_included": include_sitemaps,
                        "url_inspections_included": include_url_inspection,
                        "inspected_urls_count": (
                            len(inspect_urls) if include_url_inspection else 0
                        ),
                    },
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "error": str(e),
                    "message": "Failed to retrieve bulk GSC data",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# Add this new performance data endpoint after the existing GSC views
class GSCPerformanceDataView(APIView):
    """API endpoint for retrieving actual GSC performance data with clicks, impressions, CTR, position"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Get performance data following Google's recommended patterns"""
        try:
            user = request.user
            gsc_service = get_gsc_service()

            site_url = request.data.get("site_url")
            days_back = request.data.get("days_back", 3)  # Default to 3 days ago
            search_type = request.data.get("type", "web")
            aggregation_type = request.data.get("aggregation_type", "byProperty")
            include_detailed_data = request.data.get("include_detailed_data", False)
            get_search_appearance = request.data.get("get_search_appearance", False)

            if not site_url:
                return Response(
                    {"error": "site_url is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Calculate the target date (3 days ago by default for data availability)
            target_date = (
                timezone.now() - timezone.timedelta(days=days_back)
            ).strftime("%Y-%m-%d")

            performance_data = {
                "site_url": site_url,
                "target_date": target_date,
                "search_type": search_type,
                "aggregation_type": aggregation_type,
                "data": {},
            }

            # 1. Get accurate counts (without page/query dimensions)
            accurate_counts = gsc_service.get_performance_data_accurate(
                user=user,
                site_url=site_url,
                target_date=target_date,
                search_type=search_type,
                aggregation_type=aggregation_type,
            )
            performance_data["data"]["accurate_counts"] = accurate_counts

            # 2. Get detailed data (with page/query dimensions) if requested
            if include_detailed_data:
                detailed_data = gsc_service.get_performance_data_detailed(
                    user=user,
                    site_url=site_url,
                    target_date=target_date,
                    search_type=search_type,
                    aggregation_type=aggregation_type,
                )
                performance_data["data"]["detailed_data"] = detailed_data

            # 3. Get search appearance data (two-step process) if requested
            if get_search_appearance:
                search_appearance_data = gsc_service.get_search_appearance_data(
                    user=user,
                    site_url=site_url,
                    target_date=target_date,
                    search_type=search_type,
                )
                performance_data["data"]["search_appearance"] = search_appearance_data

            return Response(
                {
                    "success": True,
                    "performance_data": performance_data,
                    "notes": {
                        "data_date": f"Data from {target_date} ({days_back} days ago)",
                        "aggregation": aggregation_type,
                        "includes_detailed": include_detailed_data,
                        "includes_search_appearance": get_search_appearance,
                    },
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "error": str(e),
                    "message": "Failed to retrieve performance data",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class GSCOptimizedDailyDataView(APIView):
    """API endpoint for daily data retrieval following Google's quota-efficient patterns"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Get one day's worth of data efficiently with pagination"""
        try:
            user = request.user
            gsc_service = get_gsc_service()

            site_url = request.data.get("site_url")
            target_date = request.data.get("target_date")
            search_type = request.data.get("type", "web")
            dimensions = request.data.get("dimensions", ["country", "device"])
            aggregation_type = request.data.get("aggregation_type", "byProperty")
            max_rows = request.data.get("max_rows", 25000)

            if not all([site_url, target_date]):
                return Response(
                    {"error": "site_url and target_date are required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get paginated daily data
            daily_data = gsc_service.get_daily_data_paginated(
                user=user,
                site_url=site_url,
                target_date=target_date,
                search_type=search_type,
                dimensions=dimensions,
                aggregation_type=aggregation_type,
                max_rows=max_rows,
            )

            return Response(
                {
                    "success": True,
                    "daily_data": daily_data,
                    "parameters": {
                        "site_url": site_url,
                        "target_date": target_date,
                        "search_type": search_type,
                        "dimensions": dimensions,
                        "aggregation_type": aggregation_type,
                    },
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "error": str(e),
                    "message": "Failed to retrieve daily data",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# Add this endpoint for checking data availability
class GSCDataAvailabilityView(APIView):
    """API endpoint for checking recent data availability"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Check what data is available in the past 10 days"""
        try:
            user = request.user
            gsc_service = get_gsc_service()

            site_url = request.data.get("site_url")
            search_type = request.data.get("type", "web")
            days_to_check = request.data.get("days_to_check", 10)

            if not site_url:
                return Response(
                    {"error": "site_url is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            availability_info = gsc_service.check_recent_data_availability(
                user=user,
                site_url=site_url,
                search_type=search_type,
                days_to_check=days_to_check,
            )

            return Response(
                {
                    "success": True,
                    "availability_info": availability_info,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "error": str(e),
                    "message": "Failed to check data availability",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# Add this new PageSpeed Insights API view
class PageSpeedInsightsView(APIView):
    """API endpoint for retrieving PageSpeed Insights data"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Get PageSpeed Insights data for a specific URL"""
        try:
            user = request.user
            pagespeed_service = PageSpeedInsightsService()

            site_url = request.data.get("site_url")

            if not site_url:
                return Response(
                    {"error": "site_url is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get PageSpeed Insights data
            insights_data = pagespeed_service.get_core_web_vitals(site_url)

            if "error" in insights_data:
                return Response(
                    {"error": insights_data["error"]},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            return Response(
                {
                    "success": True,
                    "data": insights_data,
                    "site_url": site_url,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "error": str(e),
                    "message": "Failed to retrieve PageSpeed Insights data",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class CoreWebVitalsView(APIView):
    """API endpoint for analyzing and storing Core Web Vitals data"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Analyze and store Core Web Vitals data for a specific URL"""
        try:
            from .core_web_vitals_service import CoreWebVitalsService

            cwv_service = CoreWebVitalsService()

            # Required parameters
            site_url = request.data.get("site_url")

            # Optional parameters
            strategy = request.data.get("strategy", "mobile")  # mobile or desktop

            if not site_url:
                return Response(
                    {"error": "site_url is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Validate strategy
            if strategy not in ["mobile", "desktop"]:
                return Response(
                    {"error": "strategy must be 'mobile' or 'desktop'"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Analyze and store Core Web Vitals data
            analysis = cwv_service.analyze_and_store(site_url, strategy)

            if not analysis:
                return Response(
                    {"error": "Failed to analyze website"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Serialize the stored analysis
            from .serializers import CoreWebVitalsAnalysisSerializer

            serializer = CoreWebVitalsAnalysisSerializer(analysis)

            return Response(
                {
                    "success": True,
                    "data": serializer.data,
                    "parameters": {
                        "site_url": site_url,
                        "strategy": strategy,
                    },
                    "message": f"Core Web Vitals analysis completed and stored for {site_url} ({strategy})",
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "error": str(e),
                    "message": "Failed to analyze and store Core Web Vitals data",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def get(self, request):
        """Get stored Core Web Vitals data for a website"""
        try:
            from .core_web_vitals_service import CoreWebVitalsService

            cwv_service = CoreWebVitalsService()

            # Required parameters
            site_url = request.query_params.get("site_url")

            # Optional parameters
            strategy = request.query_params.get("strategy")

            if not site_url:
                return Response(
                    {"error": "site_url is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Validate strategy if provided
            if strategy and strategy not in ["mobile", "desktop"]:
                return Response(
                    {"error": "strategy must be 'mobile' or 'desktop'"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get latest analysis data
            analysis_data = cwv_service.get_latest_analysis(site_url, strategy)

            if not analysis_data:
                return Response(
                    {"error": "No analysis data found for this website"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            return Response(
                {
                    "success": True,
                    "data": analysis_data,
                    "parameters": {
                        "site_url": site_url,
                        "strategy": strategy,
                    },
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "error": str(e),
                    "message": "Failed to retrieve stored Core Web Vitals data",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class CoreWebVitalsMultiStrategyView(APIView):
    """API endpoint for analyzing and storing Core Web Vitals data for both mobile and desktop"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Analyze and store Core Web Vitals data for both mobile and desktop strategies"""
        try:
            from .core_web_vitals_service import CoreWebVitalsService

            cwv_service = CoreWebVitalsService()

            site_url = request.data.get("site_url")

            if not site_url:
                return Response(
                    {"error": "site_url is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Analyze and store Core Web Vitals data for both strategies
            analyses = cwv_service.analyze_multiple_strategies(site_url)

            # Serialize the results
            from .serializers import CoreWebVitalsAnalysisSerializer

            serialized_data = {}
            for strategy, analysis in analyses.items():
                if analysis:
                    serialized_data[strategy] = CoreWebVitalsAnalysisSerializer(
                        analysis
                    ).data
                else:
                    serialized_data[strategy] = None

            # Check if at least one analysis was successful
            success_count = sum(
                1 for analysis in analyses.values() if analysis is not None
            )

            if success_count == 0:
                return Response(
                    {"error": "Failed to analyze website for any strategy"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            return Response(
                {
                    "success": True,
                    "data": {"url": site_url, "analyses": serialized_data},
                    "parameters": {
                        "site_url": site_url,
                        "strategies": ["mobile", "desktop"],
                    },
                    "message": f"Core Web Vitals analysis completed for {success_count}/2 strategies",
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "error": str(e),
                    "message": "Failed to analyze and store multi-strategy Core Web Vitals data",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def get(self, request):
        """Get stored Core Web Vitals data for both mobile and desktop strategies"""
        try:
            from .core_web_vitals_service import CoreWebVitalsService

            cwv_service = CoreWebVitalsService()

            site_url = request.query_params.get("site_url")

            if not site_url:
                return Response(
                    {"error": "site_url is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get latest analysis data for both strategies
            analysis_data = cwv_service.get_latest_analysis(site_url)

            if not analysis_data:
                return Response(
                    {"error": "No analysis data found for this website"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            return Response(
                {
                    "success": True,
                    "data": {"url": site_url, "analyses": analysis_data},
                    "parameters": {
                        "site_url": site_url,
                        "strategies": ["mobile", "desktop"],
                    },
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "error": str(e),
                    "message": "Failed to retrieve stored multi-strategy Core Web Vitals data",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class CoreWebVitalsBatchView(APIView):
    """API endpoint for batch Core Web Vitals analysis of multiple URLs"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Get Core Web Vitals data for multiple URLs"""
        try:
            pagespeed_service = PageSpeedInsightsService()

            # Required parameters
            urls = request.data.get("urls", [])

            # Optional parameters
            strategy = request.data.get("strategy", "mobile")

            if not urls or not isinstance(urls, list):
                return Response(
                    {"error": "urls must be a non-empty list"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if len(urls) > 10:  # Limit batch size to prevent abuse
                return Response(
                    {"error": "Maximum 10 URLs allowed per batch request"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Validate strategy
            if strategy not in ["mobile", "desktop"]:
                return Response(
                    {"error": "strategy must be 'mobile' or 'desktop'"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get batch Core Web Vitals data
            batch_data = pagespeed_service.get_batch_analysis(urls, strategy)

            return Response(
                {
                    "success": True,
                    "data": batch_data,
                    "parameters": {
                        "urls": urls,
                        "strategy": strategy,
                        "urls_count": len(urls),
                    },
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "error": str(e),
                    "message": "Failed to retrieve batch Core Web Vitals data",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class CoreWebVitalsHistoryView(APIView):
    """API endpoint for retrieving Core Web Vitals analysis history"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get Core Web Vitals analysis history for a website"""
        try:
            from .core_web_vitals_service import CoreWebVitalsService

            cwv_service = CoreWebVitalsService()

            # Required parameters
            site_url = request.query_params.get("site_url")

            # Optional parameters
            strategy = request.query_params.get("strategy")
            limit = int(request.query_params.get("limit", 10))

            if not site_url:
                return Response(
                    {"error": "site_url is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Validate strategy if provided
            if strategy and strategy not in ["mobile", "desktop"]:
                return Response(
                    {"error": "strategy must be 'mobile' or 'desktop'"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Validate limit
            if limit > 50:
                limit = 50

            # Get analysis history
            history_data = cwv_service.get_analysis_history(site_url, strategy, limit)

            return Response(
                {
                    "success": True,
                    "data": {
                        "url": site_url,
                        "history": history_data,
                        "count": len(history_data),
                    },
                    "parameters": {
                        "site_url": site_url,
                        "strategy": strategy,
                        "limit": limit,
                    },
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "error": str(e),
                    "message": "Failed to retrieve Core Web Vitals history",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class PageSpeedOpportunitiesView(APIView):
    """API endpoint for retrieving PageSpeed performance opportunities"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Get performance opportunities and diagnostics for a URL"""
        try:
            pagespeed_service = PageSpeedInsightsService()

            site_url = request.data.get("site_url")
            strategy = request.data.get("strategy", "mobile")

            if not site_url:
                return Response(
                    {"error": "site_url is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get full Core Web Vitals data which includes opportunities
            cwv_data = pagespeed_service.get_core_web_vitals(site_url, strategy)

            if "error" in cwv_data:
                return Response(
                    {"error": cwv_data["error"]},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Extract opportunities and diagnostics
            opportunities_data = {
                "url": site_url,
                "strategy": strategy,
                "timestamp": cwv_data.get("timestamp"),
                "performance_score": cwv_data.get("performance_score"),
                "opportunities": cwv_data.get("opportunities", []),
                "diagnostics": cwv_data.get("diagnostics", []),
                "categories": cwv_data.get("categories", {}),
            }

            return Response(
                {
                    "success": True,
                    "data": opportunities_data,
                    "parameters": {
                        "site_url": site_url,
                        "strategy": strategy,
                    },
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "error": str(e),
                    "message": "Failed to retrieve PageSpeed opportunities data",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
