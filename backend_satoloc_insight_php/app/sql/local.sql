-- MySQL dump 10.13  Distrib 8.0.35, for macos13 (x86_64)
--
-- Host: localhost    Database: local
-- ------------------------------------------------------
-- Server version	8.0.35

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `wp_commentmeta`
--

DROP TABLE IF EXISTS `wp_commentmeta`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wp_commentmeta` (
  `meta_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `comment_id` bigint unsigned NOT NULL DEFAULT '0',
  `meta_key` varchar(255) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `meta_value` longtext COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`meta_id`),
  KEY `comment_id` (`comment_id`),
  KEY `meta_key` (`meta_key`(191))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wp_commentmeta`
--

LOCK TABLES `wp_commentmeta` WRITE;
/*!40000 ALTER TABLE `wp_commentmeta` DISABLE KEYS */;
/*!40000 ALTER TABLE `wp_commentmeta` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wp_comments`
--

DROP TABLE IF EXISTS `wp_comments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wp_comments` (
  `comment_ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `comment_post_ID` bigint unsigned NOT NULL DEFAULT '0',
  `comment_author` tinytext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `comment_author_email` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `comment_author_url` varchar(200) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `comment_author_IP` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `comment_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `comment_date_gmt` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `comment_content` text COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `comment_karma` int NOT NULL DEFAULT '0',
  `comment_approved` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '1',
  `comment_agent` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `comment_type` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'comment',
  `comment_parent` bigint unsigned NOT NULL DEFAULT '0',
  `user_id` bigint unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`comment_ID`),
  KEY `comment_post_ID` (`comment_post_ID`),
  KEY `comment_approved_date_gmt` (`comment_approved`,`comment_date_gmt`),
  KEY `comment_date_gmt` (`comment_date_gmt`),
  KEY `comment_parent` (`comment_parent`),
  KEY `comment_author_email` (`comment_author_email`(10))
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wp_comments`
--

LOCK TABLES `wp_comments` WRITE;
/*!40000 ALTER TABLE `wp_comments` DISABLE KEYS */;
INSERT INTO `wp_comments` VALUES (1,1,'A WordPress Commenter','<EMAIL>','https://wordpress.org/','','2025-05-26 19:05:34','2025-05-26 19:05:34','Hi, this is a comment.\nTo get started with moderating, editing, and deleting comments, please visit the Comments screen in the dashboard.\nCommenter avatars come from <a href=\"https://gravatar.com/\">Gravatar</a>.',0,'1','','comment',0,0);
/*!40000 ALTER TABLE `wp_comments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wp_links`
--

DROP TABLE IF EXISTS `wp_links`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wp_links` (
  `link_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `link_url` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_name` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_image` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_target` varchar(25) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_description` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_visible` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'Y',
  `link_owner` bigint unsigned NOT NULL DEFAULT '1',
  `link_rating` int NOT NULL DEFAULT '0',
  `link_updated` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `link_rel` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_notes` mediumtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `link_rss` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`link_id`),
  KEY `link_visible` (`link_visible`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wp_links`
--

LOCK TABLES `wp_links` WRITE;
/*!40000 ALTER TABLE `wp_links` DISABLE KEYS */;
/*!40000 ALTER TABLE `wp_links` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wp_options`
--

DROP TABLE IF EXISTS `wp_options`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wp_options` (
  `option_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `option_name` varchar(191) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `option_value` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `autoload` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'yes',
  PRIMARY KEY (`option_id`),
  UNIQUE KEY `option_name` (`option_name`),
  KEY `autoload` (`autoload`)
) ENGINE=InnoDB AUTO_INCREMENT=173 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wp_options`
--

LOCK TABLES `wp_options` WRITE;
/*!40000 ALTER TABLE `wp_options` DISABLE KEYS */;
INSERT INTO `wp_options` VALUES (1,'cron','a:12:{i:1748293533;a:1:{s:16:\"wp_update_themes\";a:1:{s:32:\"40cd750bba9870f18aada2478b24840a\";a:3:{s:8:\"schedule\";s:10:\"twicedaily\";s:4:\"args\";a:0:{}s:8:\"interval\";i:43200;}}}i:1748293535;a:1:{s:34:\"wp_privacy_delete_old_export_files\";a:1:{s:32:\"40cd750bba9870f18aada2478b24840a\";a:3:{s:8:\"schedule\";s:6:\"hourly\";s:4:\"args\";a:0:{}s:8:\"interval\";i:3600;}}}i:1748329602;a:1:{s:21:\"wp_update_user_counts\";a:1:{s:32:\"40cd750bba9870f18aada2478b24840a\";a:3:{s:8:\"schedule\";s:10:\"twicedaily\";s:4:\"args\";a:0:{}s:8:\"interval\";i:43200;}}}i:**********;a:1:{s:16:\"wp_version_check\";a:1:{s:32:\"40cd750bba9870f18aada2478b24840a\";a:3:{s:8:\"schedule\";s:10:\"twicedaily\";s:4:\"args\";a:0:{}s:8:\"interval\";i:43200;}}}i:**********;a:1:{s:17:\"wp_update_plugins\";a:1:{s:32:\"40cd750bba9870f18aada2478b24840a\";a:3:{s:8:\"schedule\";s:10:\"twicedaily\";s:4:\"args\";a:0:{}s:8:\"interval\";i:43200;}}}i:**********;a:2:{s:30:\"wp_site_health_scheduled_check\";a:1:{s:32:\"40cd750bba9870f18aada2478b24840a\";a:3:{s:8:\"schedule\";s:6:\"weekly\";s:4:\"args\";a:0:{}s:8:\"interval\";i:604800;}}s:32:\"recovery_mode_clean_expired_keys\";a:1:{s:32:\"40cd750bba9870f18aada2478b24840a\";a:3:{s:8:\"schedule\";s:5:\"daily\";s:4:\"args\";a:0:{}s:8:\"interval\";i:86400;}}}i:**********;a:1:{s:19:\"wp_scheduled_delete\";a:1:{s:32:\"40cd750bba9870f18aada2478b24840a\";a:3:{s:8:\"schedule\";s:5:\"daily\";s:4:\"args\";a:0:{}s:8:\"interval\";i:86400;}}}i:**********;a:1:{s:25:\"delete_expired_transients\";a:1:{s:32:\"40cd750bba9870f18aada2478b24840a\";a:3:{s:8:\"schedule\";s:5:\"daily\";s:4:\"args\";a:0:{}s:8:\"interval\";i:86400;}}}i:**********;a:1:{s:30:\"wp_scheduled_auto_draft_delete\";a:1:{s:32:\"40cd750bba9870f18aada2478b24840a\";a:3:{s:8:\"schedule\";s:5:\"daily\";s:4:\"args\";a:0:{}s:8:\"interval\";i:86400;}}}i:**********;a:1:{s:21:\"ai1wm_storage_cleanup\";a:1:{s:32:\"40cd750bba9870f18aada2478b24840a\";a:3:{s:8:\"schedule\";s:5:\"daily\";s:4:\"args\";a:0:{}s:8:\"interval\";i:86400;}}}i:1748896630;a:1:{s:30:\"wp_delete_temp_updater_backups\";a:1:{s:32:\"40cd750bba9870f18aada2478b24840a\";a:3:{s:8:\"schedule\";s:6:\"weekly\";s:4:\"args\";a:0:{}s:8:\"interval\";i:604800;}}}s:7:\"version\";i:2;}','on');
INSERT INTO `wp_options` VALUES (2,'siteurl','http://satolocphpbackend.local','on');
INSERT INTO `wp_options` VALUES (3,'home','http://satolocphpbackend.local','on');
INSERT INTO `wp_options` VALUES (4,'blogname','satoloc_php_backend','on');
INSERT INTO `wp_options` VALUES (5,'blogdescription','','on');
INSERT INTO `wp_options` VALUES (6,'users_can_register','0','on');
INSERT INTO `wp_options` VALUES (7,'admin_email','<EMAIL>','on');
INSERT INTO `wp_options` VALUES (8,'start_of_week','1','on');
INSERT INTO `wp_options` VALUES (9,'use_balanceTags','0','on');
INSERT INTO `wp_options` VALUES (10,'use_smilies','1','on');
INSERT INTO `wp_options` VALUES (11,'require_name_email','1','on');
INSERT INTO `wp_options` VALUES (12,'comments_notify','1','on');
INSERT INTO `wp_options` VALUES (13,'posts_per_rss','10','on');
INSERT INTO `wp_options` VALUES (14,'rss_use_excerpt','0','on');
INSERT INTO `wp_options` VALUES (15,'mailserver_url','mail.example.com','on');
INSERT INTO `wp_options` VALUES (16,'mailserver_login','<EMAIL>','on');
INSERT INTO `wp_options` VALUES (17,'mailserver_pass','','on');
INSERT INTO `wp_options` VALUES (18,'mailserver_port','110','on');
INSERT INTO `wp_options` VALUES (19,'default_category','1','on');
INSERT INTO `wp_options` VALUES (20,'default_comment_status','open','on');
INSERT INTO `wp_options` VALUES (21,'default_ping_status','open','on');
INSERT INTO `wp_options` VALUES (22,'default_pingback_flag','1','on');
INSERT INTO `wp_options` VALUES (23,'posts_per_page','10','on');
INSERT INTO `wp_options` VALUES (24,'date_format','F j, Y','on');
INSERT INTO `wp_options` VALUES (25,'time_format','g:i a','on');
INSERT INTO `wp_options` VALUES (26,'links_updated_date_format','F j, Y g:i a','on');
INSERT INTO `wp_options` VALUES (27,'comment_moderation','0','on');
INSERT INTO `wp_options` VALUES (28,'moderation_notify','1','on');
INSERT INTO `wp_options` VALUES (29,'permalink_structure','/%postname%/','on');
INSERT INTO `wp_options` VALUES (30,'rewrite_rules','a:94:{s:11:\"^wp-json/?$\";s:22:\"index.php?rest_route=/\";s:14:\"^wp-json/(.*)?\";s:33:\"index.php?rest_route=/$matches[1]\";s:21:\"^index.php/wp-json/?$\";s:22:\"index.php?rest_route=/\";s:24:\"^index.php/wp-json/(.*)?\";s:33:\"index.php?rest_route=/$matches[1]\";s:17:\"^wp-sitemap\\.xml$\";s:23:\"index.php?sitemap=index\";s:17:\"^wp-sitemap\\.xsl$\";s:36:\"index.php?sitemap-stylesheet=sitemap\";s:23:\"^wp-sitemap-index\\.xsl$\";s:34:\"index.php?sitemap-stylesheet=index\";s:48:\"^wp-sitemap-([a-z]+?)-([a-z\\d_-]+?)-(\\d+?)\\.xml$\";s:75:\"index.php?sitemap=$matches[1]&sitemap-subtype=$matches[2]&paged=$matches[3]\";s:34:\"^wp-sitemap-([a-z]+?)-(\\d+?)\\.xml$\";s:47:\"index.php?sitemap=$matches[1]&paged=$matches[2]\";s:47:\"category/(.+?)/feed/(feed|rdf|rss|rss2|atom)/?$\";s:52:\"index.php?category_name=$matches[1]&feed=$matches[2]\";s:42:\"category/(.+?)/(feed|rdf|rss|rss2|atom)/?$\";s:52:\"index.php?category_name=$matches[1]&feed=$matches[2]\";s:23:\"category/(.+?)/embed/?$\";s:46:\"index.php?category_name=$matches[1]&embed=true\";s:35:\"category/(.+?)/page/?([0-9]{1,})/?$\";s:53:\"index.php?category_name=$matches[1]&paged=$matches[2]\";s:17:\"category/(.+?)/?$\";s:35:\"index.php?category_name=$matches[1]\";s:44:\"tag/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$\";s:42:\"index.php?tag=$matches[1]&feed=$matches[2]\";s:39:\"tag/([^/]+)/(feed|rdf|rss|rss2|atom)/?$\";s:42:\"index.php?tag=$matches[1]&feed=$matches[2]\";s:20:\"tag/([^/]+)/embed/?$\";s:36:\"index.php?tag=$matches[1]&embed=true\";s:32:\"tag/([^/]+)/page/?([0-9]{1,})/?$\";s:43:\"index.php?tag=$matches[1]&paged=$matches[2]\";s:14:\"tag/([^/]+)/?$\";s:25:\"index.php?tag=$matches[1]\";s:45:\"type/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$\";s:50:\"index.php?post_format=$matches[1]&feed=$matches[2]\";s:40:\"type/([^/]+)/(feed|rdf|rss|rss2|atom)/?$\";s:50:\"index.php?post_format=$matches[1]&feed=$matches[2]\";s:21:\"type/([^/]+)/embed/?$\";s:44:\"index.php?post_format=$matches[1]&embed=true\";s:33:\"type/([^/]+)/page/?([0-9]{1,})/?$\";s:51:\"index.php?post_format=$matches[1]&paged=$matches[2]\";s:15:\"type/([^/]+)/?$\";s:33:\"index.php?post_format=$matches[1]\";s:12:\"robots\\.txt$\";s:18:\"index.php?robots=1\";s:13:\"favicon\\.ico$\";s:19:\"index.php?favicon=1\";s:12:\"sitemap\\.xml\";s:24:\"index.php??sitemap=index\";s:48:\".*wp-(atom|rdf|rss|rss2|feed|commentsrss2)\\.php$\";s:18:\"index.php?feed=old\";s:20:\".*wp-app\\.php(/.*)?$\";s:19:\"index.php?error=403\";s:18:\".*wp-register.php$\";s:23:\"index.php?register=true\";s:32:\"feed/(feed|rdf|rss|rss2|atom)/?$\";s:27:\"index.php?&feed=$matches[1]\";s:27:\"(feed|rdf|rss|rss2|atom)/?$\";s:27:\"index.php?&feed=$matches[1]\";s:8:\"embed/?$\";s:21:\"index.php?&embed=true\";s:20:\"page/?([0-9]{1,})/?$\";s:28:\"index.php?&paged=$matches[1]\";s:41:\"comments/feed/(feed|rdf|rss|rss2|atom)/?$\";s:42:\"index.php?&feed=$matches[1]&withcomments=1\";s:36:\"comments/(feed|rdf|rss|rss2|atom)/?$\";s:42:\"index.php?&feed=$matches[1]&withcomments=1\";s:17:\"comments/embed/?$\";s:21:\"index.php?&embed=true\";s:44:\"search/(.+)/feed/(feed|rdf|rss|rss2|atom)/?$\";s:40:\"index.php?s=$matches[1]&feed=$matches[2]\";s:39:\"search/(.+)/(feed|rdf|rss|rss2|atom)/?$\";s:40:\"index.php?s=$matches[1]&feed=$matches[2]\";s:20:\"search/(.+)/embed/?$\";s:34:\"index.php?s=$matches[1]&embed=true\";s:32:\"search/(.+)/page/?([0-9]{1,})/?$\";s:41:\"index.php?s=$matches[1]&paged=$matches[2]\";s:14:\"search/(.+)/?$\";s:23:\"index.php?s=$matches[1]\";s:47:\"author/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$\";s:50:\"index.php?author_name=$matches[1]&feed=$matches[2]\";s:42:\"author/([^/]+)/(feed|rdf|rss|rss2|atom)/?$\";s:50:\"index.php?author_name=$matches[1]&feed=$matches[2]\";s:23:\"author/([^/]+)/embed/?$\";s:44:\"index.php?author_name=$matches[1]&embed=true\";s:35:\"author/([^/]+)/page/?([0-9]{1,})/?$\";s:51:\"index.php?author_name=$matches[1]&paged=$matches[2]\";s:17:\"author/([^/]+)/?$\";s:33:\"index.php?author_name=$matches[1]\";s:69:\"([0-9]{4})/([0-9]{1,2})/([0-9]{1,2})/feed/(feed|rdf|rss|rss2|atom)/?$\";s:80:\"index.php?year=$matches[1]&monthnum=$matches[2]&day=$matches[3]&feed=$matches[4]\";s:64:\"([0-9]{4})/([0-9]{1,2})/([0-9]{1,2})/(feed|rdf|rss|rss2|atom)/?$\";s:80:\"index.php?year=$matches[1]&monthnum=$matches[2]&day=$matches[3]&feed=$matches[4]\";s:45:\"([0-9]{4})/([0-9]{1,2})/([0-9]{1,2})/embed/?$\";s:74:\"index.php?year=$matches[1]&monthnum=$matches[2]&day=$matches[3]&embed=true\";s:57:\"([0-9]{4})/([0-9]{1,2})/([0-9]{1,2})/page/?([0-9]{1,})/?$\";s:81:\"index.php?year=$matches[1]&monthnum=$matches[2]&day=$matches[3]&paged=$matches[4]\";s:39:\"([0-9]{4})/([0-9]{1,2})/([0-9]{1,2})/?$\";s:63:\"index.php?year=$matches[1]&monthnum=$matches[2]&day=$matches[3]\";s:56:\"([0-9]{4})/([0-9]{1,2})/feed/(feed|rdf|rss|rss2|atom)/?$\";s:64:\"index.php?year=$matches[1]&monthnum=$matches[2]&feed=$matches[3]\";s:51:\"([0-9]{4})/([0-9]{1,2})/(feed|rdf|rss|rss2|atom)/?$\";s:64:\"index.php?year=$matches[1]&monthnum=$matches[2]&feed=$matches[3]\";s:32:\"([0-9]{4})/([0-9]{1,2})/embed/?$\";s:58:\"index.php?year=$matches[1]&monthnum=$matches[2]&embed=true\";s:44:\"([0-9]{4})/([0-9]{1,2})/page/?([0-9]{1,})/?$\";s:65:\"index.php?year=$matches[1]&monthnum=$matches[2]&paged=$matches[3]\";s:26:\"([0-9]{4})/([0-9]{1,2})/?$\";s:47:\"index.php?year=$matches[1]&monthnum=$matches[2]\";s:43:\"([0-9]{4})/feed/(feed|rdf|rss|rss2|atom)/?$\";s:43:\"index.php?year=$matches[1]&feed=$matches[2]\";s:38:\"([0-9]{4})/(feed|rdf|rss|rss2|atom)/?$\";s:43:\"index.php?year=$matches[1]&feed=$matches[2]\";s:19:\"([0-9]{4})/embed/?$\";s:37:\"index.php?year=$matches[1]&embed=true\";s:31:\"([0-9]{4})/page/?([0-9]{1,})/?$\";s:44:\"index.php?year=$matches[1]&paged=$matches[2]\";s:13:\"([0-9]{4})/?$\";s:26:\"index.php?year=$matches[1]\";s:27:\".?.+?/attachment/([^/]+)/?$\";s:32:\"index.php?attachment=$matches[1]\";s:37:\".?.+?/attachment/([^/]+)/trackback/?$\";s:37:\"index.php?attachment=$matches[1]&tb=1\";s:57:\".?.+?/attachment/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$\";s:49:\"index.php?attachment=$matches[1]&feed=$matches[2]\";s:52:\".?.+?/attachment/([^/]+)/(feed|rdf|rss|rss2|atom)/?$\";s:49:\"index.php?attachment=$matches[1]&feed=$matches[2]\";s:52:\".?.+?/attachment/([^/]+)/comment-page-([0-9]{1,})/?$\";s:50:\"index.php?attachment=$matches[1]&cpage=$matches[2]\";s:33:\".?.+?/attachment/([^/]+)/embed/?$\";s:43:\"index.php?attachment=$matches[1]&embed=true\";s:16:\"(.?.+?)/embed/?$\";s:41:\"index.php?pagename=$matches[1]&embed=true\";s:20:\"(.?.+?)/trackback/?$\";s:35:\"index.php?pagename=$matches[1]&tb=1\";s:40:\"(.?.+?)/feed/(feed|rdf|rss|rss2|atom)/?$\";s:47:\"index.php?pagename=$matches[1]&feed=$matches[2]\";s:35:\"(.?.+?)/(feed|rdf|rss|rss2|atom)/?$\";s:47:\"index.php?pagename=$matches[1]&feed=$matches[2]\";s:28:\"(.?.+?)/page/?([0-9]{1,})/?$\";s:48:\"index.php?pagename=$matches[1]&paged=$matches[2]\";s:35:\"(.?.+?)/comment-page-([0-9]{1,})/?$\";s:48:\"index.php?pagename=$matches[1]&cpage=$matches[2]\";s:24:\"(.?.+?)(?:/([0-9]+))?/?$\";s:47:\"index.php?pagename=$matches[1]&page=$matches[2]\";s:27:\"[^/]+/attachment/([^/]+)/?$\";s:32:\"index.php?attachment=$matches[1]\";s:37:\"[^/]+/attachment/([^/]+)/trackback/?$\";s:37:\"index.php?attachment=$matches[1]&tb=1\";s:57:\"[^/]+/attachment/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$\";s:49:\"index.php?attachment=$matches[1]&feed=$matches[2]\";s:52:\"[^/]+/attachment/([^/]+)/(feed|rdf|rss|rss2|atom)/?$\";s:49:\"index.php?attachment=$matches[1]&feed=$matches[2]\";s:52:\"[^/]+/attachment/([^/]+)/comment-page-([0-9]{1,})/?$\";s:50:\"index.php?attachment=$matches[1]&cpage=$matches[2]\";s:33:\"[^/]+/attachment/([^/]+)/embed/?$\";s:43:\"index.php?attachment=$matches[1]&embed=true\";s:16:\"([^/]+)/embed/?$\";s:37:\"index.php?name=$matches[1]&embed=true\";s:20:\"([^/]+)/trackback/?$\";s:31:\"index.php?name=$matches[1]&tb=1\";s:40:\"([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$\";s:43:\"index.php?name=$matches[1]&feed=$matches[2]\";s:35:\"([^/]+)/(feed|rdf|rss|rss2|atom)/?$\";s:43:\"index.php?name=$matches[1]&feed=$matches[2]\";s:28:\"([^/]+)/page/?([0-9]{1,})/?$\";s:44:\"index.php?name=$matches[1]&paged=$matches[2]\";s:35:\"([^/]+)/comment-page-([0-9]{1,})/?$\";s:44:\"index.php?name=$matches[1]&cpage=$matches[2]\";s:24:\"([^/]+)(?:/([0-9]+))?/?$\";s:43:\"index.php?name=$matches[1]&page=$matches[2]\";s:16:\"[^/]+/([^/]+)/?$\";s:32:\"index.php?attachment=$matches[1]\";s:26:\"[^/]+/([^/]+)/trackback/?$\";s:37:\"index.php?attachment=$matches[1]&tb=1\";s:46:\"[^/]+/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$\";s:49:\"index.php?attachment=$matches[1]&feed=$matches[2]\";s:41:\"[^/]+/([^/]+)/(feed|rdf|rss|rss2|atom)/?$\";s:49:\"index.php?attachment=$matches[1]&feed=$matches[2]\";s:41:\"[^/]+/([^/]+)/comment-page-([0-9]{1,})/?$\";s:50:\"index.php?attachment=$matches[1]&cpage=$matches[2]\";s:22:\"[^/]+/([^/]+)/embed/?$\";s:43:\"index.php?attachment=$matches[1]&embed=true\";}','on');
INSERT INTO `wp_options` VALUES (31,'hack_file','0','on');
INSERT INTO `wp_options` VALUES (32,'blog_charset','UTF-8','on');
INSERT INTO `wp_options` VALUES (33,'moderation_keys','','off');
INSERT INTO `wp_options` VALUES (34,'active_plugins','a:1:{i:0;s:51:\"all-in-one-wp-migration/all-in-one-wp-migration.php\";}','on');
INSERT INTO `wp_options` VALUES (35,'category_base','','on');
INSERT INTO `wp_options` VALUES (36,'ping_sites','http://rpc.pingomatic.com/','on');
INSERT INTO `wp_options` VALUES (37,'comment_max_links','2','on');
INSERT INTO `wp_options` VALUES (38,'gmt_offset','0','on');
INSERT INTO `wp_options` VALUES (39,'default_email_category','1','on');
INSERT INTO `wp_options` VALUES (40,'recently_edited','','off');
INSERT INTO `wp_options` VALUES (41,'template','twentytwentyfive','on');
INSERT INTO `wp_options` VALUES (42,'stylesheet','twentytwentyfive','on');
INSERT INTO `wp_options` VALUES (43,'comment_registration','0','on');
INSERT INTO `wp_options` VALUES (44,'html_type','text/html','on');
INSERT INTO `wp_options` VALUES (45,'use_trackback','0','on');
INSERT INTO `wp_options` VALUES (46,'default_role','subscriber','on');
INSERT INTO `wp_options` VALUES (47,'db_version','58975','on');
INSERT INTO `wp_options` VALUES (48,'uploads_use_yearmonth_folders','1','on');
INSERT INTO `wp_options` VALUES (49,'upload_path','','on');
INSERT INTO `wp_options` VALUES (50,'blog_public','1','on');
INSERT INTO `wp_options` VALUES (51,'default_link_category','2','on');
INSERT INTO `wp_options` VALUES (52,'show_on_front','posts','on');
INSERT INTO `wp_options` VALUES (53,'tag_base','','on');
INSERT INTO `wp_options` VALUES (54,'show_avatars','1','on');
INSERT INTO `wp_options` VALUES (55,'avatar_rating','G','on');
INSERT INTO `wp_options` VALUES (56,'upload_url_path','','on');
INSERT INTO `wp_options` VALUES (57,'thumbnail_size_w','150','on');
INSERT INTO `wp_options` VALUES (58,'thumbnail_size_h','150','on');
INSERT INTO `wp_options` VALUES (59,'thumbnail_crop','1','on');
INSERT INTO `wp_options` VALUES (60,'medium_size_w','300','on');
INSERT INTO `wp_options` VALUES (61,'medium_size_h','300','on');
INSERT INTO `wp_options` VALUES (62,'avatar_default','mystery','on');
INSERT INTO `wp_options` VALUES (63,'large_size_w','1024','on');
INSERT INTO `wp_options` VALUES (64,'large_size_h','1024','on');
INSERT INTO `wp_options` VALUES (65,'image_default_link_type','none','on');
INSERT INTO `wp_options` VALUES (66,'image_default_size','','on');
INSERT INTO `wp_options` VALUES (67,'image_default_align','','on');
INSERT INTO `wp_options` VALUES (68,'close_comments_for_old_posts','0','on');
INSERT INTO `wp_options` VALUES (69,'close_comments_days_old','14','on');
INSERT INTO `wp_options` VALUES (70,'thread_comments','1','on');
INSERT INTO `wp_options` VALUES (71,'thread_comments_depth','5','on');
INSERT INTO `wp_options` VALUES (72,'page_comments','0','on');
INSERT INTO `wp_options` VALUES (73,'comments_per_page','50','on');
INSERT INTO `wp_options` VALUES (74,'default_comments_page','newest','on');
INSERT INTO `wp_options` VALUES (75,'comment_order','asc','on');
INSERT INTO `wp_options` VALUES (76,'sticky_posts','a:0:{}','on');
INSERT INTO `wp_options` VALUES (77,'widget_categories','a:0:{}','on');
INSERT INTO `wp_options` VALUES (78,'widget_text','a:0:{}','on');
INSERT INTO `wp_options` VALUES (79,'widget_rss','a:0:{}','on');
INSERT INTO `wp_options` VALUES (80,'uninstall_plugins','a:0:{}','off');
INSERT INTO `wp_options` VALUES (81,'timezone_string','','on');
INSERT INTO `wp_options` VALUES (82,'page_for_posts','0','on');
INSERT INTO `wp_options` VALUES (83,'page_on_front','0','on');
INSERT INTO `wp_options` VALUES (84,'default_post_format','0','on');
INSERT INTO `wp_options` VALUES (85,'link_manager_enabled','0','on');
INSERT INTO `wp_options` VALUES (86,'finished_splitting_shared_terms','1','on');
INSERT INTO `wp_options` VALUES (87,'site_icon','0','on');
INSERT INTO `wp_options` VALUES (88,'medium_large_size_w','768','on');
INSERT INTO `wp_options` VALUES (89,'medium_large_size_h','0','on');
INSERT INTO `wp_options` VALUES (90,'wp_page_for_privacy_policy','3','on');
INSERT INTO `wp_options` VALUES (91,'show_comments_cookies_opt_in','1','on');
INSERT INTO `wp_options` VALUES (92,'admin_email_lifespan','1763838333','on');
INSERT INTO `wp_options` VALUES (93,'disallowed_keys','','off');
INSERT INTO `wp_options` VALUES (94,'comment_previously_approved','1','on');
INSERT INTO `wp_options` VALUES (95,'auto_plugin_theme_update_emails','a:0:{}','off');
INSERT INTO `wp_options` VALUES (96,'auto_update_core_dev','enabled','on');
INSERT INTO `wp_options` VALUES (97,'auto_update_core_minor','enabled','on');
INSERT INTO `wp_options` VALUES (98,'auto_update_core_major','enabled','on');
INSERT INTO `wp_options` VALUES (99,'wp_force_deactivated_plugins','a:0:{}','on');
INSERT INTO `wp_options` VALUES (100,'wp_attachment_pages_enabled','0','on');
INSERT INTO `wp_options` VALUES (101,'initial_db_version','58975','on');
INSERT INTO `wp_options` VALUES (102,'wp_user_roles','a:5:{s:13:\"administrator\";a:2:{s:4:\"name\";s:13:\"Administrator\";s:12:\"capabilities\";a:61:{s:13:\"switch_themes\";b:1;s:11:\"edit_themes\";b:1;s:16:\"activate_plugins\";b:1;s:12:\"edit_plugins\";b:1;s:10:\"edit_users\";b:1;s:10:\"edit_files\";b:1;s:14:\"manage_options\";b:1;s:17:\"moderate_comments\";b:1;s:17:\"manage_categories\";b:1;s:12:\"manage_links\";b:1;s:12:\"upload_files\";b:1;s:6:\"import\";b:1;s:15:\"unfiltered_html\";b:1;s:10:\"edit_posts\";b:1;s:17:\"edit_others_posts\";b:1;s:20:\"edit_published_posts\";b:1;s:13:\"publish_posts\";b:1;s:10:\"edit_pages\";b:1;s:4:\"read\";b:1;s:8:\"level_10\";b:1;s:7:\"level_9\";b:1;s:7:\"level_8\";b:1;s:7:\"level_7\";b:1;s:7:\"level_6\";b:1;s:7:\"level_5\";b:1;s:7:\"level_4\";b:1;s:7:\"level_3\";b:1;s:7:\"level_2\";b:1;s:7:\"level_1\";b:1;s:7:\"level_0\";b:1;s:17:\"edit_others_pages\";b:1;s:20:\"edit_published_pages\";b:1;s:13:\"publish_pages\";b:1;s:12:\"delete_pages\";b:1;s:19:\"delete_others_pages\";b:1;s:22:\"delete_published_pages\";b:1;s:12:\"delete_posts\";b:1;s:19:\"delete_others_posts\";b:1;s:22:\"delete_published_posts\";b:1;s:20:\"delete_private_posts\";b:1;s:18:\"edit_private_posts\";b:1;s:18:\"read_private_posts\";b:1;s:20:\"delete_private_pages\";b:1;s:18:\"edit_private_pages\";b:1;s:18:\"read_private_pages\";b:1;s:12:\"delete_users\";b:1;s:12:\"create_users\";b:1;s:17:\"unfiltered_upload\";b:1;s:14:\"edit_dashboard\";b:1;s:14:\"update_plugins\";b:1;s:14:\"delete_plugins\";b:1;s:15:\"install_plugins\";b:1;s:13:\"update_themes\";b:1;s:14:\"install_themes\";b:1;s:11:\"update_core\";b:1;s:10:\"list_users\";b:1;s:12:\"remove_users\";b:1;s:13:\"promote_users\";b:1;s:18:\"edit_theme_options\";b:1;s:13:\"delete_themes\";b:1;s:6:\"export\";b:1;}}s:6:\"editor\";a:2:{s:4:\"name\";s:6:\"Editor\";s:12:\"capabilities\";a:34:{s:17:\"moderate_comments\";b:1;s:17:\"manage_categories\";b:1;s:12:\"manage_links\";b:1;s:12:\"upload_files\";b:1;s:15:\"unfiltered_html\";b:1;s:10:\"edit_posts\";b:1;s:17:\"edit_others_posts\";b:1;s:20:\"edit_published_posts\";b:1;s:13:\"publish_posts\";b:1;s:10:\"edit_pages\";b:1;s:4:\"read\";b:1;s:7:\"level_7\";b:1;s:7:\"level_6\";b:1;s:7:\"level_5\";b:1;s:7:\"level_4\";b:1;s:7:\"level_3\";b:1;s:7:\"level_2\";b:1;s:7:\"level_1\";b:1;s:7:\"level_0\";b:1;s:17:\"edit_others_pages\";b:1;s:20:\"edit_published_pages\";b:1;s:13:\"publish_pages\";b:1;s:12:\"delete_pages\";b:1;s:19:\"delete_others_pages\";b:1;s:22:\"delete_published_pages\";b:1;s:12:\"delete_posts\";b:1;s:19:\"delete_others_posts\";b:1;s:22:\"delete_published_posts\";b:1;s:20:\"delete_private_posts\";b:1;s:18:\"edit_private_posts\";b:1;s:18:\"read_private_posts\";b:1;s:20:\"delete_private_pages\";b:1;s:18:\"edit_private_pages\";b:1;s:18:\"read_private_pages\";b:1;}}s:6:\"author\";a:2:{s:4:\"name\";s:6:\"Author\";s:12:\"capabilities\";a:10:{s:12:\"upload_files\";b:1;s:10:\"edit_posts\";b:1;s:20:\"edit_published_posts\";b:1;s:13:\"publish_posts\";b:1;s:4:\"read\";b:1;s:7:\"level_2\";b:1;s:7:\"level_1\";b:1;s:7:\"level_0\";b:1;s:12:\"delete_posts\";b:1;s:22:\"delete_published_posts\";b:1;}}s:11:\"contributor\";a:2:{s:4:\"name\";s:11:\"Contributor\";s:12:\"capabilities\";a:5:{s:10:\"edit_posts\";b:1;s:4:\"read\";b:1;s:7:\"level_1\";b:1;s:7:\"level_0\";b:1;s:12:\"delete_posts\";b:1;}}s:10:\"subscriber\";a:2:{s:4:\"name\";s:10:\"Subscriber\";s:12:\"capabilities\";a:2:{s:4:\"read\";b:1;s:7:\"level_0\";b:1;}}}','on');
INSERT INTO `wp_options` VALUES (103,'fresh_site','1','off');
INSERT INTO `wp_options` VALUES (104,'user_count','1','off');
INSERT INTO `wp_options` VALUES (105,'widget_block','a:6:{i:2;a:1:{s:7:\"content\";s:19:\"<!-- wp:search /-->\";}i:3;a:1:{s:7:\"content\";s:154:\"<!-- wp:group --><div class=\"wp-block-group\"><!-- wp:heading --><h2>Recent Posts</h2><!-- /wp:heading --><!-- wp:latest-posts /--></div><!-- /wp:group -->\";}i:4;a:1:{s:7:\"content\";s:227:\"<!-- wp:group --><div class=\"wp-block-group\"><!-- wp:heading --><h2>Recent Comments</h2><!-- /wp:heading --><!-- wp:latest-comments {\"displayAvatar\":false,\"displayDate\":false,\"displayExcerpt\":false} /--></div><!-- /wp:group -->\";}i:5;a:1:{s:7:\"content\";s:146:\"<!-- wp:group --><div class=\"wp-block-group\"><!-- wp:heading --><h2>Archives</h2><!-- /wp:heading --><!-- wp:archives /--></div><!-- /wp:group -->\";}i:6;a:1:{s:7:\"content\";s:150:\"<!-- wp:group --><div class=\"wp-block-group\"><!-- wp:heading --><h2>Categories</h2><!-- /wp:heading --><!-- wp:categories /--></div><!-- /wp:group -->\";}s:12:\"_multiwidget\";i:1;}','auto');
INSERT INTO `wp_options` VALUES (106,'sidebars_widgets','a:4:{s:19:\"wp_inactive_widgets\";a:0:{}s:9:\"sidebar-1\";a:3:{i:0;s:7:\"block-2\";i:1;s:7:\"block-3\";i:2;s:7:\"block-4\";}s:9:\"sidebar-2\";a:2:{i:0;s:7:\"block-5\";i:1;s:7:\"block-6\";}s:13:\"array_version\";i:3;}','auto');
INSERT INTO `wp_options` VALUES (107,'widget_pages','a:1:{s:12:\"_multiwidget\";i:1;}','auto');
INSERT INTO `wp_options` VALUES (108,'widget_calendar','a:1:{s:12:\"_multiwidget\";i:1;}','auto');
INSERT INTO `wp_options` VALUES (109,'widget_archives','a:1:{s:12:\"_multiwidget\";i:1;}','auto');
INSERT INTO `wp_options` VALUES (110,'widget_media_audio','a:1:{s:12:\"_multiwidget\";i:1;}','auto');
INSERT INTO `wp_options` VALUES (111,'widget_media_image','a:1:{s:12:\"_multiwidget\";i:1;}','auto');
INSERT INTO `wp_options` VALUES (112,'widget_media_gallery','a:1:{s:12:\"_multiwidget\";i:1;}','auto');
INSERT INTO `wp_options` VALUES (113,'widget_media_video','a:1:{s:12:\"_multiwidget\";i:1;}','auto');
INSERT INTO `wp_options` VALUES (114,'widget_meta','a:1:{s:12:\"_multiwidget\";i:1;}','auto');
INSERT INTO `wp_options` VALUES (115,'widget_search','a:1:{s:12:\"_multiwidget\";i:1;}','auto');
INSERT INTO `wp_options` VALUES (116,'widget_recent-posts','a:1:{s:12:\"_multiwidget\";i:1;}','auto');
INSERT INTO `wp_options` VALUES (117,'widget_recent-comments','a:1:{s:12:\"_multiwidget\";i:1;}','auto');
INSERT INTO `wp_options` VALUES (118,'widget_tag_cloud','a:1:{s:12:\"_multiwidget\";i:1;}','auto');
INSERT INTO `wp_options` VALUES (119,'widget_nav_menu','a:1:{s:12:\"_multiwidget\";i:1;}','auto');
INSERT INTO `wp_options` VALUES (120,'widget_custom_html','a:1:{s:12:\"_multiwidget\";i:1;}','auto');
INSERT INTO `wp_options` VALUES (121,'_transient_wp_core_block_css_files','a:2:{s:7:\"version\";s:5:\"6.8.1\";s:5:\"files\";a:536:{i:0;s:23:\"archives/editor-rtl.css\";i:1;s:27:\"archives/editor-rtl.min.css\";i:2;s:19:\"archives/editor.css\";i:3;s:23:\"archives/editor.min.css\";i:4;s:22:\"archives/style-rtl.css\";i:5;s:26:\"archives/style-rtl.min.css\";i:6;s:18:\"archives/style.css\";i:7;s:22:\"archives/style.min.css\";i:8;s:20:\"audio/editor-rtl.css\";i:9;s:24:\"audio/editor-rtl.min.css\";i:10;s:16:\"audio/editor.css\";i:11;s:20:\"audio/editor.min.css\";i:12;s:19:\"audio/style-rtl.css\";i:13;s:23:\"audio/style-rtl.min.css\";i:14;s:15:\"audio/style.css\";i:15;s:19:\"audio/style.min.css\";i:16;s:19:\"audio/theme-rtl.css\";i:17;s:23:\"audio/theme-rtl.min.css\";i:18;s:15:\"audio/theme.css\";i:19;s:19:\"audio/theme.min.css\";i:20;s:21:\"avatar/editor-rtl.css\";i:21;s:25:\"avatar/editor-rtl.min.css\";i:22;s:17:\"avatar/editor.css\";i:23;s:21:\"avatar/editor.min.css\";i:24;s:20:\"avatar/style-rtl.css\";i:25;s:24:\"avatar/style-rtl.min.css\";i:26;s:16:\"avatar/style.css\";i:27;s:20:\"avatar/style.min.css\";i:28;s:21:\"button/editor-rtl.css\";i:29;s:25:\"button/editor-rtl.min.css\";i:30;s:17:\"button/editor.css\";i:31;s:21:\"button/editor.min.css\";i:32;s:20:\"button/style-rtl.css\";i:33;s:24:\"button/style-rtl.min.css\";i:34;s:16:\"button/style.css\";i:35;s:20:\"button/style.min.css\";i:36;s:22:\"buttons/editor-rtl.css\";i:37;s:26:\"buttons/editor-rtl.min.css\";i:38;s:18:\"buttons/editor.css\";i:39;s:22:\"buttons/editor.min.css\";i:40;s:21:\"buttons/style-rtl.css\";i:41;s:25:\"buttons/style-rtl.min.css\";i:42;s:17:\"buttons/style.css\";i:43;s:21:\"buttons/style.min.css\";i:44;s:22:\"calendar/style-rtl.css\";i:45;s:26:\"calendar/style-rtl.min.css\";i:46;s:18:\"calendar/style.css\";i:47;s:22:\"calendar/style.min.css\";i:48;s:25:\"categories/editor-rtl.css\";i:49;s:29:\"categories/editor-rtl.min.css\";i:50;s:21:\"categories/editor.css\";i:51;s:25:\"categories/editor.min.css\";i:52;s:24:\"categories/style-rtl.css\";i:53;s:28:\"categories/style-rtl.min.css\";i:54;s:20:\"categories/style.css\";i:55;s:24:\"categories/style.min.css\";i:56;s:19:\"code/editor-rtl.css\";i:57;s:23:\"code/editor-rtl.min.css\";i:58;s:15:\"code/editor.css\";i:59;s:19:\"code/editor.min.css\";i:60;s:18:\"code/style-rtl.css\";i:61;s:22:\"code/style-rtl.min.css\";i:62;s:14:\"code/style.css\";i:63;s:18:\"code/style.min.css\";i:64;s:18:\"code/theme-rtl.css\";i:65;s:22:\"code/theme-rtl.min.css\";i:66;s:14:\"code/theme.css\";i:67;s:18:\"code/theme.min.css\";i:68;s:22:\"columns/editor-rtl.css\";i:69;s:26:\"columns/editor-rtl.min.css\";i:70;s:18:\"columns/editor.css\";i:71;s:22:\"columns/editor.min.css\";i:72;s:21:\"columns/style-rtl.css\";i:73;s:25:\"columns/style-rtl.min.css\";i:74;s:17:\"columns/style.css\";i:75;s:21:\"columns/style.min.css\";i:76;s:33:\"comment-author-name/style-rtl.css\";i:77;s:37:\"comment-author-name/style-rtl.min.css\";i:78;s:29:\"comment-author-name/style.css\";i:79;s:33:\"comment-author-name/style.min.css\";i:80;s:29:\"comment-content/style-rtl.css\";i:81;s:33:\"comment-content/style-rtl.min.css\";i:82;s:25:\"comment-content/style.css\";i:83;s:29:\"comment-content/style.min.css\";i:84;s:26:\"comment-date/style-rtl.css\";i:85;s:30:\"comment-date/style-rtl.min.css\";i:86;s:22:\"comment-date/style.css\";i:87;s:26:\"comment-date/style.min.css\";i:88;s:31:\"comment-edit-link/style-rtl.css\";i:89;s:35:\"comment-edit-link/style-rtl.min.css\";i:90;s:27:\"comment-edit-link/style.css\";i:91;s:31:\"comment-edit-link/style.min.css\";i:92;s:32:\"comment-reply-link/style-rtl.css\";i:93;s:36:\"comment-reply-link/style-rtl.min.css\";i:94;s:28:\"comment-reply-link/style.css\";i:95;s:32:\"comment-reply-link/style.min.css\";i:96;s:30:\"comment-template/style-rtl.css\";i:97;s:34:\"comment-template/style-rtl.min.css\";i:98;s:26:\"comment-template/style.css\";i:99;s:30:\"comment-template/style.min.css\";i:100;s:42:\"comments-pagination-numbers/editor-rtl.css\";i:101;s:46:\"comments-pagination-numbers/editor-rtl.min.css\";i:102;s:38:\"comments-pagination-numbers/editor.css\";i:103;s:42:\"comments-pagination-numbers/editor.min.css\";i:104;s:34:\"comments-pagination/editor-rtl.css\";i:105;s:38:\"comments-pagination/editor-rtl.min.css\";i:106;s:30:\"comments-pagination/editor.css\";i:107;s:34:\"comments-pagination/editor.min.css\";i:108;s:33:\"comments-pagination/style-rtl.css\";i:109;s:37:\"comments-pagination/style-rtl.min.css\";i:110;s:29:\"comments-pagination/style.css\";i:111;s:33:\"comments-pagination/style.min.css\";i:112;s:29:\"comments-title/editor-rtl.css\";i:113;s:33:\"comments-title/editor-rtl.min.css\";i:114;s:25:\"comments-title/editor.css\";i:115;s:29:\"comments-title/editor.min.css\";i:116;s:23:\"comments/editor-rtl.css\";i:117;s:27:\"comments/editor-rtl.min.css\";i:118;s:19:\"comments/editor.css\";i:119;s:23:\"comments/editor.min.css\";i:120;s:22:\"comments/style-rtl.css\";i:121;s:26:\"comments/style-rtl.min.css\";i:122;s:18:\"comments/style.css\";i:123;s:22:\"comments/style.min.css\";i:124;s:20:\"cover/editor-rtl.css\";i:125;s:24:\"cover/editor-rtl.min.css\";i:126;s:16:\"cover/editor.css\";i:127;s:20:\"cover/editor.min.css\";i:128;s:19:\"cover/style-rtl.css\";i:129;s:23:\"cover/style-rtl.min.css\";i:130;s:15:\"cover/style.css\";i:131;s:19:\"cover/style.min.css\";i:132;s:22:\"details/editor-rtl.css\";i:133;s:26:\"details/editor-rtl.min.css\";i:134;s:18:\"details/editor.css\";i:135;s:22:\"details/editor.min.css\";i:136;s:21:\"details/style-rtl.css\";i:137;s:25:\"details/style-rtl.min.css\";i:138;s:17:\"details/style.css\";i:139;s:21:\"details/style.min.css\";i:140;s:20:\"embed/editor-rtl.css\";i:141;s:24:\"embed/editor-rtl.min.css\";i:142;s:16:\"embed/editor.css\";i:143;s:20:\"embed/editor.min.css\";i:144;s:19:\"embed/style-rtl.css\";i:145;s:23:\"embed/style-rtl.min.css\";i:146;s:15:\"embed/style.css\";i:147;s:19:\"embed/style.min.css\";i:148;s:19:\"embed/theme-rtl.css\";i:149;s:23:\"embed/theme-rtl.min.css\";i:150;s:15:\"embed/theme.css\";i:151;s:19:\"embed/theme.min.css\";i:152;s:19:\"file/editor-rtl.css\";i:153;s:23:\"file/editor-rtl.min.css\";i:154;s:15:\"file/editor.css\";i:155;s:19:\"file/editor.min.css\";i:156;s:18:\"file/style-rtl.css\";i:157;s:22:\"file/style-rtl.min.css\";i:158;s:14:\"file/style.css\";i:159;s:18:\"file/style.min.css\";i:160;s:23:\"footnotes/style-rtl.css\";i:161;s:27:\"footnotes/style-rtl.min.css\";i:162;s:19:\"footnotes/style.css\";i:163;s:23:\"footnotes/style.min.css\";i:164;s:23:\"freeform/editor-rtl.css\";i:165;s:27:\"freeform/editor-rtl.min.css\";i:166;s:19:\"freeform/editor.css\";i:167;s:23:\"freeform/editor.min.css\";i:168;s:22:\"gallery/editor-rtl.css\";i:169;s:26:\"gallery/editor-rtl.min.css\";i:170;s:18:\"gallery/editor.css\";i:171;s:22:\"gallery/editor.min.css\";i:172;s:21:\"gallery/style-rtl.css\";i:173;s:25:\"gallery/style-rtl.min.css\";i:174;s:17:\"gallery/style.css\";i:175;s:21:\"gallery/style.min.css\";i:176;s:21:\"gallery/theme-rtl.css\";i:177;s:25:\"gallery/theme-rtl.min.css\";i:178;s:17:\"gallery/theme.css\";i:179;s:21:\"gallery/theme.min.css\";i:180;s:20:\"group/editor-rtl.css\";i:181;s:24:\"group/editor-rtl.min.css\";i:182;s:16:\"group/editor.css\";i:183;s:20:\"group/editor.min.css\";i:184;s:19:\"group/style-rtl.css\";i:185;s:23:\"group/style-rtl.min.css\";i:186;s:15:\"group/style.css\";i:187;s:19:\"group/style.min.css\";i:188;s:19:\"group/theme-rtl.css\";i:189;s:23:\"group/theme-rtl.min.css\";i:190;s:15:\"group/theme.css\";i:191;s:19:\"group/theme.min.css\";i:192;s:21:\"heading/style-rtl.css\";i:193;s:25:\"heading/style-rtl.min.css\";i:194;s:17:\"heading/style.css\";i:195;s:21:\"heading/style.min.css\";i:196;s:19:\"html/editor-rtl.css\";i:197;s:23:\"html/editor-rtl.min.css\";i:198;s:15:\"html/editor.css\";i:199;s:19:\"html/editor.min.css\";i:200;s:20:\"image/editor-rtl.css\";i:201;s:24:\"image/editor-rtl.min.css\";i:202;s:16:\"image/editor.css\";i:203;s:20:\"image/editor.min.css\";i:204;s:19:\"image/style-rtl.css\";i:205;s:23:\"image/style-rtl.min.css\";i:206;s:15:\"image/style.css\";i:207;s:19:\"image/style.min.css\";i:208;s:19:\"image/theme-rtl.css\";i:209;s:23:\"image/theme-rtl.min.css\";i:210;s:15:\"image/theme.css\";i:211;s:19:\"image/theme.min.css\";i:212;s:29:\"latest-comments/style-rtl.css\";i:213;s:33:\"latest-comments/style-rtl.min.css\";i:214;s:25:\"latest-comments/style.css\";i:215;s:29:\"latest-comments/style.min.css\";i:216;s:27:\"latest-posts/editor-rtl.css\";i:217;s:31:\"latest-posts/editor-rtl.min.css\";i:218;s:23:\"latest-posts/editor.css\";i:219;s:27:\"latest-posts/editor.min.css\";i:220;s:26:\"latest-posts/style-rtl.css\";i:221;s:30:\"latest-posts/style-rtl.min.css\";i:222;s:22:\"latest-posts/style.css\";i:223;s:26:\"latest-posts/style.min.css\";i:224;s:18:\"list/style-rtl.css\";i:225;s:22:\"list/style-rtl.min.css\";i:226;s:14:\"list/style.css\";i:227;s:18:\"list/style.min.css\";i:228;s:22:\"loginout/style-rtl.css\";i:229;s:26:\"loginout/style-rtl.min.css\";i:230;s:18:\"loginout/style.css\";i:231;s:22:\"loginout/style.min.css\";i:232;s:25:\"media-text/editor-rtl.css\";i:233;s:29:\"media-text/editor-rtl.min.css\";i:234;s:21:\"media-text/editor.css\";i:235;s:25:\"media-text/editor.min.css\";i:236;s:24:\"media-text/style-rtl.css\";i:237;s:28:\"media-text/style-rtl.min.css\";i:238;s:20:\"media-text/style.css\";i:239;s:24:\"media-text/style.min.css\";i:240;s:19:\"more/editor-rtl.css\";i:241;s:23:\"more/editor-rtl.min.css\";i:242;s:15:\"more/editor.css\";i:243;s:19:\"more/editor.min.css\";i:244;s:30:\"navigation-link/editor-rtl.css\";i:245;s:34:\"navigation-link/editor-rtl.min.css\";i:246;s:26:\"navigation-link/editor.css\";i:247;s:30:\"navigation-link/editor.min.css\";i:248;s:29:\"navigation-link/style-rtl.css\";i:249;s:33:\"navigation-link/style-rtl.min.css\";i:250;s:25:\"navigation-link/style.css\";i:251;s:29:\"navigation-link/style.min.css\";i:252;s:33:\"navigation-submenu/editor-rtl.css\";i:253;s:37:\"navigation-submenu/editor-rtl.min.css\";i:254;s:29:\"navigation-submenu/editor.css\";i:255;s:33:\"navigation-submenu/editor.min.css\";i:256;s:25:\"navigation/editor-rtl.css\";i:257;s:29:\"navigation/editor-rtl.min.css\";i:258;s:21:\"navigation/editor.css\";i:259;s:25:\"navigation/editor.min.css\";i:260;s:24:\"navigation/style-rtl.css\";i:261;s:28:\"navigation/style-rtl.min.css\";i:262;s:20:\"navigation/style.css\";i:263;s:24:\"navigation/style.min.css\";i:264;s:23:\"nextpage/editor-rtl.css\";i:265;s:27:\"nextpage/editor-rtl.min.css\";i:266;s:19:\"nextpage/editor.css\";i:267;s:23:\"nextpage/editor.min.css\";i:268;s:24:\"page-list/editor-rtl.css\";i:269;s:28:\"page-list/editor-rtl.min.css\";i:270;s:20:\"page-list/editor.css\";i:271;s:24:\"page-list/editor.min.css\";i:272;s:23:\"page-list/style-rtl.css\";i:273;s:27:\"page-list/style-rtl.min.css\";i:274;s:19:\"page-list/style.css\";i:275;s:23:\"page-list/style.min.css\";i:276;s:24:\"paragraph/editor-rtl.css\";i:277;s:28:\"paragraph/editor-rtl.min.css\";i:278;s:20:\"paragraph/editor.css\";i:279;s:24:\"paragraph/editor.min.css\";i:280;s:23:\"paragraph/style-rtl.css\";i:281;s:27:\"paragraph/style-rtl.min.css\";i:282;s:19:\"paragraph/style.css\";i:283;s:23:\"paragraph/style.min.css\";i:284;s:35:\"post-author-biography/style-rtl.css\";i:285;s:39:\"post-author-biography/style-rtl.min.css\";i:286;s:31:\"post-author-biography/style.css\";i:287;s:35:\"post-author-biography/style.min.css\";i:288;s:30:\"post-author-name/style-rtl.css\";i:289;s:34:\"post-author-name/style-rtl.min.css\";i:290;s:26:\"post-author-name/style.css\";i:291;s:30:\"post-author-name/style.min.css\";i:292;s:26:\"post-author/editor-rtl.css\";i:293;s:30:\"post-author/editor-rtl.min.css\";i:294;s:22:\"post-author/editor.css\";i:295;s:26:\"post-author/editor.min.css\";i:296;s:25:\"post-author/style-rtl.css\";i:297;s:29:\"post-author/style-rtl.min.css\";i:298;s:21:\"post-author/style.css\";i:299;s:25:\"post-author/style.min.css\";i:300;s:33:\"post-comments-form/editor-rtl.css\";i:301;s:37:\"post-comments-form/editor-rtl.min.css\";i:302;s:29:\"post-comments-form/editor.css\";i:303;s:33:\"post-comments-form/editor.min.css\";i:304;s:32:\"post-comments-form/style-rtl.css\";i:305;s:36:\"post-comments-form/style-rtl.min.css\";i:306;s:28:\"post-comments-form/style.css\";i:307;s:32:\"post-comments-form/style.min.css\";i:308;s:26:\"post-content/style-rtl.css\";i:309;s:30:\"post-content/style-rtl.min.css\";i:310;s:22:\"post-content/style.css\";i:311;s:26:\"post-content/style.min.css\";i:312;s:23:\"post-date/style-rtl.css\";i:313;s:27:\"post-date/style-rtl.min.css\";i:314;s:19:\"post-date/style.css\";i:315;s:23:\"post-date/style.min.css\";i:316;s:27:\"post-excerpt/editor-rtl.css\";i:317;s:31:\"post-excerpt/editor-rtl.min.css\";i:318;s:23:\"post-excerpt/editor.css\";i:319;s:27:\"post-excerpt/editor.min.css\";i:320;s:26:\"post-excerpt/style-rtl.css\";i:321;s:30:\"post-excerpt/style-rtl.min.css\";i:322;s:22:\"post-excerpt/style.css\";i:323;s:26:\"post-excerpt/style.min.css\";i:324;s:34:\"post-featured-image/editor-rtl.css\";i:325;s:38:\"post-featured-image/editor-rtl.min.css\";i:326;s:30:\"post-featured-image/editor.css\";i:327;s:34:\"post-featured-image/editor.min.css\";i:328;s:33:\"post-featured-image/style-rtl.css\";i:329;s:37:\"post-featured-image/style-rtl.min.css\";i:330;s:29:\"post-featured-image/style.css\";i:331;s:33:\"post-featured-image/style.min.css\";i:332;s:34:\"post-navigation-link/style-rtl.css\";i:333;s:38:\"post-navigation-link/style-rtl.min.css\";i:334;s:30:\"post-navigation-link/style.css\";i:335;s:34:\"post-navigation-link/style.min.css\";i:336;s:27:\"post-template/style-rtl.css\";i:337;s:31:\"post-template/style-rtl.min.css\";i:338;s:23:\"post-template/style.css\";i:339;s:27:\"post-template/style.min.css\";i:340;s:24:\"post-terms/style-rtl.css\";i:341;s:28:\"post-terms/style-rtl.min.css\";i:342;s:20:\"post-terms/style.css\";i:343;s:24:\"post-terms/style.min.css\";i:344;s:24:\"post-title/style-rtl.css\";i:345;s:28:\"post-title/style-rtl.min.css\";i:346;s:20:\"post-title/style.css\";i:347;s:24:\"post-title/style.min.css\";i:348;s:26:\"preformatted/style-rtl.css\";i:349;s:30:\"preformatted/style-rtl.min.css\";i:350;s:22:\"preformatted/style.css\";i:351;s:26:\"preformatted/style.min.css\";i:352;s:24:\"pullquote/editor-rtl.css\";i:353;s:28:\"pullquote/editor-rtl.min.css\";i:354;s:20:\"pullquote/editor.css\";i:355;s:24:\"pullquote/editor.min.css\";i:356;s:23:\"pullquote/style-rtl.css\";i:357;s:27:\"pullquote/style-rtl.min.css\";i:358;s:19:\"pullquote/style.css\";i:359;s:23:\"pullquote/style.min.css\";i:360;s:23:\"pullquote/theme-rtl.css\";i:361;s:27:\"pullquote/theme-rtl.min.css\";i:362;s:19:\"pullquote/theme.css\";i:363;s:23:\"pullquote/theme.min.css\";i:364;s:39:\"query-pagination-numbers/editor-rtl.css\";i:365;s:43:\"query-pagination-numbers/editor-rtl.min.css\";i:366;s:35:\"query-pagination-numbers/editor.css\";i:367;s:39:\"query-pagination-numbers/editor.min.css\";i:368;s:31:\"query-pagination/editor-rtl.css\";i:369;s:35:\"query-pagination/editor-rtl.min.css\";i:370;s:27:\"query-pagination/editor.css\";i:371;s:31:\"query-pagination/editor.min.css\";i:372;s:30:\"query-pagination/style-rtl.css\";i:373;s:34:\"query-pagination/style-rtl.min.css\";i:374;s:26:\"query-pagination/style.css\";i:375;s:30:\"query-pagination/style.min.css\";i:376;s:25:\"query-title/style-rtl.css\";i:377;s:29:\"query-title/style-rtl.min.css\";i:378;s:21:\"query-title/style.css\";i:379;s:25:\"query-title/style.min.css\";i:380;s:25:\"query-total/style-rtl.css\";i:381;s:29:\"query-total/style-rtl.min.css\";i:382;s:21:\"query-total/style.css\";i:383;s:25:\"query-total/style.min.css\";i:384;s:20:\"query/editor-rtl.css\";i:385;s:24:\"query/editor-rtl.min.css\";i:386;s:16:\"query/editor.css\";i:387;s:20:\"query/editor.min.css\";i:388;s:19:\"quote/style-rtl.css\";i:389;s:23:\"quote/style-rtl.min.css\";i:390;s:15:\"quote/style.css\";i:391;s:19:\"quote/style.min.css\";i:392;s:19:\"quote/theme-rtl.css\";i:393;s:23:\"quote/theme-rtl.min.css\";i:394;s:15:\"quote/theme.css\";i:395;s:19:\"quote/theme.min.css\";i:396;s:23:\"read-more/style-rtl.css\";i:397;s:27:\"read-more/style-rtl.min.css\";i:398;s:19:\"read-more/style.css\";i:399;s:23:\"read-more/style.min.css\";i:400;s:18:\"rss/editor-rtl.css\";i:401;s:22:\"rss/editor-rtl.min.css\";i:402;s:14:\"rss/editor.css\";i:403;s:18:\"rss/editor.min.css\";i:404;s:17:\"rss/style-rtl.css\";i:405;s:21:\"rss/style-rtl.min.css\";i:406;s:13:\"rss/style.css\";i:407;s:17:\"rss/style.min.css\";i:408;s:21:\"search/editor-rtl.css\";i:409;s:25:\"search/editor-rtl.min.css\";i:410;s:17:\"search/editor.css\";i:411;s:21:\"search/editor.min.css\";i:412;s:20:\"search/style-rtl.css\";i:413;s:24:\"search/style-rtl.min.css\";i:414;s:16:\"search/style.css\";i:415;s:20:\"search/style.min.css\";i:416;s:20:\"search/theme-rtl.css\";i:417;s:24:\"search/theme-rtl.min.css\";i:418;s:16:\"search/theme.css\";i:419;s:20:\"search/theme.min.css\";i:420;s:24:\"separator/editor-rtl.css\";i:421;s:28:\"separator/editor-rtl.min.css\";i:422;s:20:\"separator/editor.css\";i:423;s:24:\"separator/editor.min.css\";i:424;s:23:\"separator/style-rtl.css\";i:425;s:27:\"separator/style-rtl.min.css\";i:426;s:19:\"separator/style.css\";i:427;s:23:\"separator/style.min.css\";i:428;s:23:\"separator/theme-rtl.css\";i:429;s:27:\"separator/theme-rtl.min.css\";i:430;s:19:\"separator/theme.css\";i:431;s:23:\"separator/theme.min.css\";i:432;s:24:\"shortcode/editor-rtl.css\";i:433;s:28:\"shortcode/editor-rtl.min.css\";i:434;s:20:\"shortcode/editor.css\";i:435;s:24:\"shortcode/editor.min.css\";i:436;s:24:\"site-logo/editor-rtl.css\";i:437;s:28:\"site-logo/editor-rtl.min.css\";i:438;s:20:\"site-logo/editor.css\";i:439;s:24:\"site-logo/editor.min.css\";i:440;s:23:\"site-logo/style-rtl.css\";i:441;s:27:\"site-logo/style-rtl.min.css\";i:442;s:19:\"site-logo/style.css\";i:443;s:23:\"site-logo/style.min.css\";i:444;s:27:\"site-tagline/editor-rtl.css\";i:445;s:31:\"site-tagline/editor-rtl.min.css\";i:446;s:23:\"site-tagline/editor.css\";i:447;s:27:\"site-tagline/editor.min.css\";i:448;s:26:\"site-tagline/style-rtl.css\";i:449;s:30:\"site-tagline/style-rtl.min.css\";i:450;s:22:\"site-tagline/style.css\";i:451;s:26:\"site-tagline/style.min.css\";i:452;s:25:\"site-title/editor-rtl.css\";i:453;s:29:\"site-title/editor-rtl.min.css\";i:454;s:21:\"site-title/editor.css\";i:455;s:25:\"site-title/editor.min.css\";i:456;s:24:\"site-title/style-rtl.css\";i:457;s:28:\"site-title/style-rtl.min.css\";i:458;s:20:\"site-title/style.css\";i:459;s:24:\"site-title/style.min.css\";i:460;s:26:\"social-link/editor-rtl.css\";i:461;s:30:\"social-link/editor-rtl.min.css\";i:462;s:22:\"social-link/editor.css\";i:463;s:26:\"social-link/editor.min.css\";i:464;s:27:\"social-links/editor-rtl.css\";i:465;s:31:\"social-links/editor-rtl.min.css\";i:466;s:23:\"social-links/editor.css\";i:467;s:27:\"social-links/editor.min.css\";i:468;s:26:\"social-links/style-rtl.css\";i:469;s:30:\"social-links/style-rtl.min.css\";i:470;s:22:\"social-links/style.css\";i:471;s:26:\"social-links/style.min.css\";i:472;s:21:\"spacer/editor-rtl.css\";i:473;s:25:\"spacer/editor-rtl.min.css\";i:474;s:17:\"spacer/editor.css\";i:475;s:21:\"spacer/editor.min.css\";i:476;s:20:\"spacer/style-rtl.css\";i:477;s:24:\"spacer/style-rtl.min.css\";i:478;s:16:\"spacer/style.css\";i:479;s:20:\"spacer/style.min.css\";i:480;s:20:\"table/editor-rtl.css\";i:481;s:24:\"table/editor-rtl.min.css\";i:482;s:16:\"table/editor.css\";i:483;s:20:\"table/editor.min.css\";i:484;s:19:\"table/style-rtl.css\";i:485;s:23:\"table/style-rtl.min.css\";i:486;s:15:\"table/style.css\";i:487;s:19:\"table/style.min.css\";i:488;s:19:\"table/theme-rtl.css\";i:489;s:23:\"table/theme-rtl.min.css\";i:490;s:15:\"table/theme.css\";i:491;s:19:\"table/theme.min.css\";i:492;s:24:\"tag-cloud/editor-rtl.css\";i:493;s:28:\"tag-cloud/editor-rtl.min.css\";i:494;s:20:\"tag-cloud/editor.css\";i:495;s:24:\"tag-cloud/editor.min.css\";i:496;s:23:\"tag-cloud/style-rtl.css\";i:497;s:27:\"tag-cloud/style-rtl.min.css\";i:498;s:19:\"tag-cloud/style.css\";i:499;s:23:\"tag-cloud/style.min.css\";i:500;s:28:\"template-part/editor-rtl.css\";i:501;s:32:\"template-part/editor-rtl.min.css\";i:502;s:24:\"template-part/editor.css\";i:503;s:28:\"template-part/editor.min.css\";i:504;s:27:\"template-part/theme-rtl.css\";i:505;s:31:\"template-part/theme-rtl.min.css\";i:506;s:23:\"template-part/theme.css\";i:507;s:27:\"template-part/theme.min.css\";i:508;s:30:\"term-description/style-rtl.css\";i:509;s:34:\"term-description/style-rtl.min.css\";i:510;s:26:\"term-description/style.css\";i:511;s:30:\"term-description/style.min.css\";i:512;s:27:\"text-columns/editor-rtl.css\";i:513;s:31:\"text-columns/editor-rtl.min.css\";i:514;s:23:\"text-columns/editor.css\";i:515;s:27:\"text-columns/editor.min.css\";i:516;s:26:\"text-columns/style-rtl.css\";i:517;s:30:\"text-columns/style-rtl.min.css\";i:518;s:22:\"text-columns/style.css\";i:519;s:26:\"text-columns/style.min.css\";i:520;s:19:\"verse/style-rtl.css\";i:521;s:23:\"verse/style-rtl.min.css\";i:522;s:15:\"verse/style.css\";i:523;s:19:\"verse/style.min.css\";i:524;s:20:\"video/editor-rtl.css\";i:525;s:24:\"video/editor-rtl.min.css\";i:526;s:16:\"video/editor.css\";i:527;s:20:\"video/editor.min.css\";i:528;s:19:\"video/style-rtl.css\";i:529;s:23:\"video/style-rtl.min.css\";i:530;s:15:\"video/style.css\";i:531;s:19:\"video/style.min.css\";i:532;s:19:\"video/theme-rtl.css\";i:533;s:23:\"video/theme-rtl.min.css\";i:534;s:15:\"video/theme.css\";i:535;s:19:\"video/theme.min.css\";}}','on');
INSERT INTO `wp_options` VALUES (125,'recovery_keys','a:0:{}','off');
INSERT INTO `wp_options` VALUES (126,'WPLANG','','auto');
INSERT INTO `wp_options` VALUES (127,'_site_transient_update_core','O:8:\"stdClass\":4:{s:7:\"updates\";a:1:{i:0;O:8:\"stdClass\":10:{s:8:\"response\";s:6:\"latest\";s:8:\"download\";s:59:\"https://downloads.wordpress.org/release/wordpress-6.8.1.zip\";s:6:\"locale\";s:5:\"en_US\";s:8:\"packages\";O:8:\"stdClass\":5:{s:4:\"full\";s:59:\"https://downloads.wordpress.org/release/wordpress-6.8.1.zip\";s:10:\"no_content\";s:70:\"https://downloads.wordpress.org/release/wordpress-6.8.1-no-content.zip\";s:11:\"new_bundled\";s:71:\"https://downloads.wordpress.org/release/wordpress-6.8.1-new-bundled.zip\";s:7:\"partial\";s:0:\"\";s:8:\"rollback\";s:0:\"\";}s:7:\"current\";s:5:\"6.8.1\";s:7:\"version\";s:5:\"6.8.1\";s:11:\"php_version\";s:6:\"7.2.24\";s:13:\"mysql_version\";s:5:\"5.5.5\";s:11:\"new_bundled\";s:3:\"6.7\";s:15:\"partial_version\";s:0:\"\";}}s:12:\"last_checked\";i:1748291833;s:15:\"version_checked\";s:5:\"6.8.1\";s:12:\"translations\";a:0:{}}','off');
INSERT INTO `wp_options` VALUES (131,'_site_transient_update_themes','O:8:\"stdClass\":5:{s:12:\"last_checked\";i:1748291834;s:7:\"checked\";a:3:{s:16:\"twentytwentyfive\";s:3:\"1.2\";s:16:\"twentytwentyfour\";s:3:\"1.3\";s:17:\"twentytwentythree\";s:3:\"1.6\";}s:8:\"response\";a:0:{}s:9:\"no_update\";a:3:{s:16:\"twentytwentyfive\";a:6:{s:5:\"theme\";s:16:\"twentytwentyfive\";s:11:\"new_version\";s:3:\"1.2\";s:3:\"url\";s:46:\"https://wordpress.org/themes/twentytwentyfive/\";s:7:\"package\";s:62:\"https://downloads.wordpress.org/theme/twentytwentyfive.1.2.zip\";s:8:\"requires\";s:3:\"6.7\";s:12:\"requires_php\";s:3:\"7.2\";}s:16:\"twentytwentyfour\";a:6:{s:5:\"theme\";s:16:\"twentytwentyfour\";s:11:\"new_version\";s:3:\"1.3\";s:3:\"url\";s:46:\"https://wordpress.org/themes/twentytwentyfour/\";s:7:\"package\";s:62:\"https://downloads.wordpress.org/theme/twentytwentyfour.1.3.zip\";s:8:\"requires\";s:3:\"6.4\";s:12:\"requires_php\";s:3:\"7.0\";}s:17:\"twentytwentythree\";a:6:{s:5:\"theme\";s:17:\"twentytwentythree\";s:11:\"new_version\";s:3:\"1.6\";s:3:\"url\";s:47:\"https://wordpress.org/themes/twentytwentythree/\";s:7:\"package\";s:63:\"https://downloads.wordpress.org/theme/twentytwentythree.1.6.zip\";s:8:\"requires\";s:3:\"6.1\";s:12:\"requires_php\";s:3:\"5.6\";}}s:12:\"translations\";a:0:{}}','off');
INSERT INTO `wp_options` VALUES (132,'_site_transient_timeout_browser_4e53babdc7983733fc0c4b1895b00caf','1748891203','off');
INSERT INTO `wp_options` VALUES (133,'_site_transient_browser_4e53babdc7983733fc0c4b1895b00caf','a:10:{s:4:\"name\";s:6:\"Safari\";s:7:\"version\";s:6:\"18.3.1\";s:8:\"platform\";s:9:\"Macintosh\";s:10:\"update_url\";s:29:\"https://www.apple.com/safari/\";s:7:\"img_src\";s:43:\"http://s.w.org/images/browsers/safari.png?1\";s:11:\"img_src_ssl\";s:44:\"https://s.w.org/images/browsers/safari.png?1\";s:15:\"current_version\";s:2:\"11\";s:7:\"upgrade\";b:0;s:8:\"insecure\";b:0;s:6:\"mobile\";b:0;}','off');
INSERT INTO `wp_options` VALUES (134,'_site_transient_timeout_php_check_a34f4a08303dd29cee70e79d780daa9d','1748891204','off');
INSERT INTO `wp_options` VALUES (135,'_site_transient_php_check_a34f4a08303dd29cee70e79d780daa9d','a:5:{s:19:\"recommended_version\";s:3:\"7.4\";s:15:\"minimum_version\";s:6:\"7.2.24\";s:12:\"is_supported\";b:1;s:9:\"is_secure\";b:1;s:13:\"is_acceptable\";b:1;}','off');
INSERT INTO `wp_options` VALUES (137,'_site_transient_timeout_community-events-d41d8cd98f00b204e9800998ecf8427e','1748334566','off');
INSERT INTO `wp_options` VALUES (138,'_site_transient_community-events-d41d8cd98f00b204e9800998ecf8427e','a:4:{s:9:\"sandboxed\";b:0;s:5:\"error\";N;s:8:\"location\";a:1:{s:2:\"ip\";b:0;}s:6:\"events\";a:0:{}}','off');
INSERT INTO `wp_options` VALUES (139,'can_compress_scripts','0','on');
INSERT INTO `wp_options` VALUES (140,'_transient_timeout_feed_9bbd59226dc36b9b26cd43f15694c5c3','1748329605','off');
INSERT INTO `wp_options` VALUES (141,'_transient_feed_9bbd59226dc36b9b26cd43f15694c5c3','a:6:{s:5:\"child\";a:1:{s:0:\"\";a:1:{s:3:\"rss\";a:1:{i:0;a:6:{s:4:\"data\";s:3:\"\n\n\n\";s:7:\"attribs\";a:1:{s:0:\"\";a:1:{s:7:\"version\";s:3:\"2.0\";}}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";s:5:\"child\";a:1:{s:0:\"\";a:1:{s:7:\"channel\";a:1:{i:0;a:6:{s:4:\"data\";s:52:\"\n	\n	\n	\n	\n	\n	\n	\n	\n	\n\n \n	\n		\n		\n		\n		\n		\n		\n		\n		\n		\n	\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";s:5:\"child\";a:4:{s:0:\"\";a:8:{s:5:\"title\";a:1:{i:0;a:5:{s:4:\"data\";s:14:\"WordPress News\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"link\";a:1:{i:0;a:5:{s:4:\"data\";s:26:\"https://wordpress.org/news\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:11:\"description\";a:1:{i:0;a:5:{s:4:\"data\";s:59:\"The latest news about WordPress and the WordPress community\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:13:\"lastBuildDate\";a:1:{i:0;a:5:{s:4:\"data\";s:31:\"Wed, 07 May 2025 14:01:21 +0000\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:8:\"language\";a:1:{i:0;a:5:{s:4:\"data\";s:5:\"en-US\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:9:\"generator\";a:1:{i:0;a:5:{s:4:\"data\";s:40:\"https://wordpress.org/?v=6.9-alpha-60251\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:5:\"image\";a:1:{i:0;a:6:{s:4:\"data\";s:11:\"\n	\n	\n	\n	\n	\n\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";s:5:\"child\";a:1:{s:0:\"\";a:5:{s:3:\"url\";a:1:{i:0;a:5:{s:4:\"data\";s:29:\"https://s.w.org/favicon.ico?2\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:5:\"title\";a:1:{i:0;a:5:{s:4:\"data\";s:14:\"WordPress News\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"link\";a:1:{i:0;a:5:{s:4:\"data\";s:26:\"https://wordpress.org/news\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:5:\"width\";a:1:{i:0;a:5:{s:4:\"data\";s:2:\"32\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:6:\"height\";a:1:{i:0;a:5:{s:4:\"data\";s:2:\"32\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}}}}s:4:\"item\";a:10:{i:0;a:6:{s:4:\"data\";s:60:\"\n		\n		\n		\n		\n		\n				\n		\n		\n\n					\n										\n					\n		\n		\n			\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";s:5:\"child\";a:4:{s:0:\"\";a:6:{s:5:\"title\";a:1:{i:0;a:5:{s:4:\"data\";s:32:\"WordPress Campus Connect Expands\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"link\";a:1:{i:0;a:5:{s:4:\"data\";s:68:\"https://wordpress.org/news/2025/05/wordpress-campus-connect-expands/\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:7:\"pubDate\";a:1:{i:0;a:5:{s:4:\"data\";s:31:\"Wed, 07 May 2025 12:40:19 +0000\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:8:\"category\";a:2:{i:0;a:5:{s:4:\"data\";s:9:\"Community\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:1;a:5:{s:4:\"data\";s:6:\"Events\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"guid\";a:1:{i:0;a:5:{s:4:\"data\";s:35:\"https://wordpress.org/news/?p=18726\";s:7:\"attribs\";a:1:{s:0:\"\";a:1:{s:11:\"isPermaLink\";s:5:\"false\";}}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:11:\"description\";a:1:{i:0;a:5:{s:4:\"data\";s:391:\"WordPress Campus Connect, initially launched in October 2024 as a pilot program, has now been formally established as an official event series due to its resounding success. The inaugural program, spearheaded by myself, Anand Upadhyay, garnered immense enthusiasm from 400 Indian students who were eager to engage in hands-on WordPress training. WordPress Campus Connect transcends [&#8230;]\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:32:\"http://purl.org/dc/elements/1.1/\";a:1:{s:7:\"creator\";a:1:{i:0;a:5:{s:4:\"data\";s:14:\"Anand Upadhyay\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:40:\"http://purl.org/rss/1.0/modules/content/\";a:1:{s:7:\"encoded\";a:1:{i:0;a:5:{s:4:\"data\";s:27763:\"\n<p>WordPress Campus Connect, <a href=\"https://central.wordcamp.org/news/2024/12/wp-campus-connect-recap-a-journey-of-empowering-students-and-building-community/\">initially launched in October 2024 as a pilot program</a>, has now been formally established as an official event series due to its resounding success. The inaugural program, spearheaded by myself, <a href=\"https://profiles.wordpress.org/anandau14/\">Anand Upadhyay</a>, garnered immense enthusiasm from 400 Indian students who were eager to engage in hands-on WordPress training.</p>\n\n\n\n<figure class=\"wp-block-gallery has-nested-images columns-default is-cropped has-border-color has-darker-grey-background-color has-background wp-block-gallery-1 is-layout-flex wp-block-gallery-is-layout-flex\" style=\"border-color:#1c2024;border-width:20px;border-radius:2px\">\n<figure data-wp-context=\"{&quot;imageId&quot;:&quot;6834b9fe3717e&quot;}\" data-wp-interactive=\"core/image\" class=\"wp-block-image size-large wp-lightbox-container\"><img data-recalc-dims=\"1\" fetchpriority=\"high\" decoding=\"async\" width=\"1024\" height=\"683\" data-wp-class--hide=\"state.isContentHidden\" data-wp-class--show=\"state.isContentVisible\" data-wp-init=\"callbacks.setButtonStyles\" data-wp-on-async--click=\"actions.showLightbox\" data-wp-on-async--load=\"callbacks.setButtonStyles\" data-wp-on-async-window--resize=\"callbacks.setButtonStyles\" data-id=\"18733\" src=\"https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A6596.jpg?resize=1024%2C683&#038;ssl=1\" alt=\"\" class=\"wp-image-18733\" srcset=\"https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A6596-scaled.jpg?resize=1024%2C683&amp;ssl=1 1024w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A6596-scaled.jpg?resize=300%2C200&amp;ssl=1 300w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A6596-scaled.jpg?resize=768%2C512&amp;ssl=1 768w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A6596-scaled.jpg?resize=1536%2C1024&amp;ssl=1 1536w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A6596-scaled.jpg?resize=2048%2C1365&amp;ssl=1 2048w\" sizes=\"(max-width: 1000px) 100vw, 1000px\" /><button\n			class=\"lightbox-trigger\"\n			type=\"button\"\n			aria-haspopup=\"dialog\"\n			aria-label=\"Enlarge\"\n			data-wp-init=\"callbacks.initTriggerButton\"\n			data-wp-on-async--click=\"actions.showLightbox\"\n			data-wp-style--right=\"state.imageButtonRight\"\n			data-wp-style--top=\"state.imageButtonTop\"\n		>\n			<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"12\" height=\"12\" fill=\"none\" viewBox=\"0 0 12 12\">\n				<path fill=\"#fff\" d=\"M2 0a2 2 0 0 0-2 2v2h1.5V2a.5.5 0 0 1 .5-.5h2V0H2Zm2 10.5H2a.5.5 0 0 1-.5-.5V8H0v2a2 2 0 0 0 2 2h2v-1.5ZM8 12v-1.5h2a.5.5 0 0 0 .5-.5V8H12v2a2 2 0 0 1-2 2H8Zm2-12a2 2 0 0 1 2 2v2h-1.5V2a.5.5 0 0 0-.5-.5H8V0h2Z\" />\n			</svg>\n		</button></figure>\n\n\n\n<figure data-wp-context=\"{&quot;imageId&quot;:&quot;6834b9fe375a9&quot;}\" data-wp-interactive=\"core/image\" class=\"wp-block-image size-large wp-lightbox-container\"><img data-recalc-dims=\"1\" decoding=\"async\" width=\"1024\" height=\"683\" data-wp-class--hide=\"state.isContentHidden\" data-wp-class--show=\"state.isContentVisible\" data-wp-init=\"callbacks.setButtonStyles\" data-wp-on-async--click=\"actions.showLightbox\" data-wp-on-async--load=\"callbacks.setButtonStyles\" data-wp-on-async-window--resize=\"callbacks.setButtonStyles\" data-id=\"18735\" src=\"https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A8902.jpg?resize=1024%2C683&#038;ssl=1\" alt=\"\" class=\"wp-image-18735\" srcset=\"https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A8902-scaled.jpg?resize=1024%2C683&amp;ssl=1 1024w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A8902-scaled.jpg?resize=300%2C200&amp;ssl=1 300w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A8902-scaled.jpg?resize=768%2C512&amp;ssl=1 768w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A8902-scaled.jpg?resize=1536%2C1024&amp;ssl=1 1536w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A8902-scaled.jpg?resize=2048%2C1365&amp;ssl=1 2048w\" sizes=\"(max-width: 1000px) 100vw, 1000px\" /><button\n			class=\"lightbox-trigger\"\n			type=\"button\"\n			aria-haspopup=\"dialog\"\n			aria-label=\"Enlarge\"\n			data-wp-init=\"callbacks.initTriggerButton\"\n			data-wp-on-async--click=\"actions.showLightbox\"\n			data-wp-style--right=\"state.imageButtonRight\"\n			data-wp-style--top=\"state.imageButtonTop\"\n		>\n			<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"12\" height=\"12\" fill=\"none\" viewBox=\"0 0 12 12\">\n				<path fill=\"#fff\" d=\"M2 0a2 2 0 0 0-2 2v2h1.5V2a.5.5 0 0 1 .5-.5h2V0H2Zm2 10.5H2a.5.5 0 0 1-.5-.5V8H0v2a2 2 0 0 0 2 2h2v-1.5ZM8 12v-1.5h2a.5.5 0 0 0 .5-.5V8H12v2a2 2 0 0 1-2 2H8Zm2-12a2 2 0 0 1 2 2v2h-1.5V2a.5.5 0 0 0-.5-.5H8V0h2Z\" />\n			</svg>\n		</button></figure>\n\n\n\n<figure data-wp-context=\"{&quot;imageId&quot;:&quot;6834b9fe3797f&quot;}\" data-wp-interactive=\"core/image\" class=\"wp-block-image size-large wp-lightbox-container\"><img data-recalc-dims=\"1\" decoding=\"async\" width=\"1024\" height=\"683\" data-wp-class--hide=\"state.isContentHidden\" data-wp-class--show=\"state.isContentVisible\" data-wp-init=\"callbacks.setButtonStyles\" data-wp-on-async--click=\"actions.showLightbox\" data-wp-on-async--load=\"callbacks.setButtonStyles\" data-wp-on-async-window--resize=\"callbacks.setButtonStyles\" data-id=\"18732\" src=\"https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A6763.jpg?resize=1024%2C683&#038;ssl=1\" alt=\"\" class=\"wp-image-18732\" srcset=\"https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A6763-scaled.jpg?resize=1024%2C683&amp;ssl=1 1024w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A6763-scaled.jpg?resize=300%2C200&amp;ssl=1 300w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A6763-scaled.jpg?resize=768%2C512&amp;ssl=1 768w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A6763-scaled.jpg?resize=1536%2C1024&amp;ssl=1 1536w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A6763-scaled.jpg?resize=2048%2C1365&amp;ssl=1 2048w\" sizes=\"(max-width: 1000px) 100vw, 1000px\" /><button\n			class=\"lightbox-trigger\"\n			type=\"button\"\n			aria-haspopup=\"dialog\"\n			aria-label=\"Enlarge\"\n			data-wp-init=\"callbacks.initTriggerButton\"\n			data-wp-on-async--click=\"actions.showLightbox\"\n			data-wp-style--right=\"state.imageButtonRight\"\n			data-wp-style--top=\"state.imageButtonTop\"\n		>\n			<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"12\" height=\"12\" fill=\"none\" viewBox=\"0 0 12 12\">\n				<path fill=\"#fff\" d=\"M2 0a2 2 0 0 0-2 2v2h1.5V2a.5.5 0 0 1 .5-.5h2V0H2Zm2 10.5H2a.5.5 0 0 1-.5-.5V8H0v2a2 2 0 0 0 2 2h2v-1.5ZM8 12v-1.5h2a.5.5 0 0 0 .5-.5V8H12v2a2 2 0 0 1-2 2H8Zm2-12a2 2 0 0 1 2 2v2h-1.5V2a.5.5 0 0 0-.5-.5H8V0h2Z\" />\n			</svg>\n		</button></figure>\n\n\n\n<figure data-wp-context=\"{&quot;imageId&quot;:&quot;6834b9fe37d37&quot;}\" data-wp-interactive=\"core/image\" class=\"wp-block-image size-large wp-lightbox-container\"><img data-recalc-dims=\"1\" loading=\"lazy\" decoding=\"async\" width=\"1024\" height=\"682\" data-wp-class--hide=\"state.isContentHidden\" data-wp-class--show=\"state.isContentVisible\" data-wp-init=\"callbacks.setButtonStyles\" data-wp-on-async--click=\"actions.showLightbox\" data-wp-on-async--load=\"callbacks.setButtonStyles\" data-wp-on-async-window--resize=\"callbacks.setButtonStyles\" data-id=\"18728\" src=\"https://i0.wp.com/wordpress.org/news/files/2025/05/image-3.jpeg?resize=1024%2C682&#038;ssl=1\" alt=\"\" class=\"wp-image-18728\" srcset=\"https://i0.wp.com/wordpress.org/news/files/2025/05/image-3.jpeg?resize=1024%2C682&amp;ssl=1 1024w, https://i0.wp.com/wordpress.org/news/files/2025/05/image-3.jpeg?resize=300%2C200&amp;ssl=1 300w, https://i0.wp.com/wordpress.org/news/files/2025/05/image-3.jpeg?resize=768%2C512&amp;ssl=1 768w, https://i0.wp.com/wordpress.org/news/files/2025/05/image-3.jpeg?resize=1536%2C1023&amp;ssl=1 1536w, https://i0.wp.com/wordpress.org/news/files/2025/05/image-3.jpeg?w=1600&amp;ssl=1 1600w\" sizes=\"auto, (max-width: 1000px) 100vw, 1000px\" /><button\n			class=\"lightbox-trigger\"\n			type=\"button\"\n			aria-haspopup=\"dialog\"\n			aria-label=\"Enlarge\"\n			data-wp-init=\"callbacks.initTriggerButton\"\n			data-wp-on-async--click=\"actions.showLightbox\"\n			data-wp-style--right=\"state.imageButtonRight\"\n			data-wp-style--top=\"state.imageButtonTop\"\n		>\n			<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"12\" height=\"12\" fill=\"none\" viewBox=\"0 0 12 12\">\n				<path fill=\"#fff\" d=\"M2 0a2 2 0 0 0-2 2v2h1.5V2a.5.5 0 0 1 .5-.5h2V0H2Zm2 10.5H2a.5.5 0 0 1-.5-.5V8H0v2a2 2 0 0 0 2 2h2v-1.5ZM8 12v-1.5h2a.5.5 0 0 0 .5-.5V8H12v2a2 2 0 0 1-2 2H8Zm2-12a2 2 0 0 1 2 2v2h-1.5V2a.5.5 0 0 0-.5-.5H8V0h2Z\" />\n			</svg>\n		</button></figure>\n\n\n\n<figure data-wp-context=\"{&quot;imageId&quot;:&quot;6834b9fe380f9&quot;}\" data-wp-interactive=\"core/image\" class=\"wp-block-image size-large wp-lightbox-container\"><img data-recalc-dims=\"1\" loading=\"lazy\" decoding=\"async\" width=\"1024\" height=\"682\" data-wp-class--hide=\"state.isContentHidden\" data-wp-class--show=\"state.isContentVisible\" data-wp-init=\"callbacks.setButtonStyles\" data-wp-on-async--click=\"actions.showLightbox\" data-wp-on-async--load=\"callbacks.setButtonStyles\" data-wp-on-async-window--resize=\"callbacks.setButtonStyles\" data-id=\"18729\" src=\"https://i0.wp.com/wordpress.org/news/files/2025/05/image.jpeg?resize=1024%2C682&#038;ssl=1\" alt=\"\" class=\"wp-image-18729\" srcset=\"https://i0.wp.com/wordpress.org/news/files/2025/05/image.jpeg?resize=1024%2C682&amp;ssl=1 1024w, https://i0.wp.com/wordpress.org/news/files/2025/05/image.jpeg?resize=300%2C200&amp;ssl=1 300w, https://i0.wp.com/wordpress.org/news/files/2025/05/image.jpeg?resize=768%2C512&amp;ssl=1 768w, https://i0.wp.com/wordpress.org/news/files/2025/05/image.jpeg?resize=1536%2C1023&amp;ssl=1 1536w, https://i0.wp.com/wordpress.org/news/files/2025/05/image.jpeg?w=1600&amp;ssl=1 1600w\" sizes=\"auto, (max-width: 1000px) 100vw, 1000px\" /><button\n			class=\"lightbox-trigger\"\n			type=\"button\"\n			aria-haspopup=\"dialog\"\n			aria-label=\"Enlarge\"\n			data-wp-init=\"callbacks.initTriggerButton\"\n			data-wp-on-async--click=\"actions.showLightbox\"\n			data-wp-style--right=\"state.imageButtonRight\"\n			data-wp-style--top=\"state.imageButtonTop\"\n		>\n			<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"12\" height=\"12\" fill=\"none\" viewBox=\"0 0 12 12\">\n				<path fill=\"#fff\" d=\"M2 0a2 2 0 0 0-2 2v2h1.5V2a.5.5 0 0 1 .5-.5h2V0H2Zm2 10.5H2a.5.5 0 0 1-.5-.5V8H0v2a2 2 0 0 0 2 2h2v-1.5ZM8 12v-1.5h2a.5.5 0 0 0 .5-.5V8H12v2a2 2 0 0 1-2 2H8Zm2-12a2 2 0 0 1 2 2v2h-1.5V2a.5.5 0 0 0-.5-.5H8V0h2Z\" />\n			</svg>\n		</button></figure>\n\n\n\n<figure data-wp-context=\"{&quot;imageId&quot;:&quot;6834b9fe384ac&quot;}\" data-wp-interactive=\"core/image\" class=\"wp-block-image size-large wp-lightbox-container\"><img data-recalc-dims=\"1\" loading=\"lazy\" decoding=\"async\" width=\"1024\" height=\"682\" data-wp-class--hide=\"state.isContentHidden\" data-wp-class--show=\"state.isContentVisible\" data-wp-init=\"callbacks.setButtonStyles\" data-wp-on-async--click=\"actions.showLightbox\" data-wp-on-async--load=\"callbacks.setButtonStyles\" data-wp-on-async-window--resize=\"callbacks.setButtonStyles\" data-id=\"18730\" src=\"https://i0.wp.com/wordpress.org/news/files/2025/05/image-1.jpeg?resize=1024%2C682&#038;ssl=1\" alt=\"\" class=\"wp-image-18730\" srcset=\"https://i0.wp.com/wordpress.org/news/files/2025/05/image-1.jpeg?resize=1024%2C682&amp;ssl=1 1024w, https://i0.wp.com/wordpress.org/news/files/2025/05/image-1.jpeg?resize=300%2C200&amp;ssl=1 300w, https://i0.wp.com/wordpress.org/news/files/2025/05/image-1.jpeg?resize=768%2C512&amp;ssl=1 768w, https://i0.wp.com/wordpress.org/news/files/2025/05/image-1.jpeg?resize=1536%2C1023&amp;ssl=1 1536w, https://i0.wp.com/wordpress.org/news/files/2025/05/image-1.jpeg?w=1600&amp;ssl=1 1600w\" sizes=\"auto, (max-width: 1000px) 100vw, 1000px\" /><button\n			class=\"lightbox-trigger\"\n			type=\"button\"\n			aria-haspopup=\"dialog\"\n			aria-label=\"Enlarge\"\n			data-wp-init=\"callbacks.initTriggerButton\"\n			data-wp-on-async--click=\"actions.showLightbox\"\n			data-wp-style--right=\"state.imageButtonRight\"\n			data-wp-style--top=\"state.imageButtonTop\"\n		>\n			<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"12\" height=\"12\" fill=\"none\" viewBox=\"0 0 12 12\">\n				<path fill=\"#fff\" d=\"M2 0a2 2 0 0 0-2 2v2h1.5V2a.5.5 0 0 1 .5-.5h2V0H2Zm2 10.5H2a.5.5 0 0 1-.5-.5V8H0v2a2 2 0 0 0 2 2h2v-1.5ZM8 12v-1.5h2a.5.5 0 0 0 .5-.5V8H12v2a2 2 0 0 1-2 2H8Zm2-12a2 2 0 0 1 2 2v2h-1.5V2a.5.5 0 0 0-.5-.5H8V0h2Z\" />\n			</svg>\n		</button></figure>\n\n\n\n<figure data-wp-context=\"{&quot;imageId&quot;:&quot;6834b9fe3885f&quot;}\" data-wp-interactive=\"core/image\" class=\"wp-block-image size-large wp-lightbox-container\"><img data-recalc-dims=\"1\" loading=\"lazy\" decoding=\"async\" width=\"1024\" height=\"683\" data-wp-class--hide=\"state.isContentHidden\" data-wp-class--show=\"state.isContentVisible\" data-wp-init=\"callbacks.setButtonStyles\" data-wp-on-async--click=\"actions.showLightbox\" data-wp-on-async--load=\"callbacks.setButtonStyles\" data-wp-on-async-window--resize=\"callbacks.setButtonStyles\" data-id=\"18737\" src=\"https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A8923.jpg?resize=1024%2C683&#038;ssl=1\" alt=\"\" class=\"wp-image-18737\" srcset=\"https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A8923-scaled.jpg?resize=1024%2C683&amp;ssl=1 1024w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A8923-scaled.jpg?resize=300%2C200&amp;ssl=1 300w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A8923-scaled.jpg?resize=768%2C512&amp;ssl=1 768w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A8923-scaled.jpg?resize=1536%2C1024&amp;ssl=1 1536w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A8923-scaled.jpg?resize=2048%2C1365&amp;ssl=1 2048w\" sizes=\"auto, (max-width: 1000px) 100vw, 1000px\" /><button\n			class=\"lightbox-trigger\"\n			type=\"button\"\n			aria-haspopup=\"dialog\"\n			aria-label=\"Enlarge\"\n			data-wp-init=\"callbacks.initTriggerButton\"\n			data-wp-on-async--click=\"actions.showLightbox\"\n			data-wp-style--right=\"state.imageButtonRight\"\n			data-wp-style--top=\"state.imageButtonTop\"\n		>\n			<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"12\" height=\"12\" fill=\"none\" viewBox=\"0 0 12 12\">\n				<path fill=\"#fff\" d=\"M2 0a2 2 0 0 0-2 2v2h1.5V2a.5.5 0 0 1 .5-.5h2V0H2Zm2 10.5H2a.5.5 0 0 1-.5-.5V8H0v2a2 2 0 0 0 2 2h2v-1.5ZM8 12v-1.5h2a.5.5 0 0 0 .5-.5V8H12v2a2 2 0 0 1-2 2H8Zm2-12a2 2 0 0 1 2 2v2h-1.5V2a.5.5 0 0 0-.5-.5H8V0h2Z\" />\n			</svg>\n		</button></figure>\n\n\n\n<figure data-wp-context=\"{&quot;imageId&quot;:&quot;6834b9fe38c0c&quot;}\" data-wp-interactive=\"core/image\" class=\"wp-block-image size-large wp-lightbox-container\"><img data-recalc-dims=\"1\" loading=\"lazy\" decoding=\"async\" width=\"1024\" height=\"682\" data-wp-class--hide=\"state.isContentHidden\" data-wp-class--show=\"state.isContentVisible\" data-wp-init=\"callbacks.setButtonStyles\" data-wp-on-async--click=\"actions.showLightbox\" data-wp-on-async--load=\"callbacks.setButtonStyles\" data-wp-on-async-window--resize=\"callbacks.setButtonStyles\" data-id=\"18731\" src=\"https://i0.wp.com/wordpress.org/news/files/2025/05/image-2.jpeg?resize=1024%2C682&#038;ssl=1\" alt=\"\" class=\"wp-image-18731\" srcset=\"https://i0.wp.com/wordpress.org/news/files/2025/05/image-2.jpeg?resize=1024%2C682&amp;ssl=1 1024w, https://i0.wp.com/wordpress.org/news/files/2025/05/image-2.jpeg?resize=300%2C200&amp;ssl=1 300w, https://i0.wp.com/wordpress.org/news/files/2025/05/image-2.jpeg?resize=768%2C512&amp;ssl=1 768w, https://i0.wp.com/wordpress.org/news/files/2025/05/image-2.jpeg?resize=1536%2C1023&amp;ssl=1 1536w, https://i0.wp.com/wordpress.org/news/files/2025/05/image-2.jpeg?w=1600&amp;ssl=1 1600w\" sizes=\"auto, (max-width: 1000px) 100vw, 1000px\" /><button\n			class=\"lightbox-trigger\"\n			type=\"button\"\n			aria-haspopup=\"dialog\"\n			aria-label=\"Enlarge\"\n			data-wp-init=\"callbacks.initTriggerButton\"\n			data-wp-on-async--click=\"actions.showLightbox\"\n			data-wp-style--right=\"state.imageButtonRight\"\n			data-wp-style--top=\"state.imageButtonTop\"\n		>\n			<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"12\" height=\"12\" fill=\"none\" viewBox=\"0 0 12 12\">\n				<path fill=\"#fff\" d=\"M2 0a2 2 0 0 0-2 2v2h1.5V2a.5.5 0 0 1 .5-.5h2V0H2Zm2 10.5H2a.5.5 0 0 1-.5-.5V8H0v2a2 2 0 0 0 2 2h2v-1.5ZM8 12v-1.5h2a.5.5 0 0 0 .5-.5V8H12v2a2 2 0 0 1-2 2H8Zm2-12a2 2 0 0 1 2 2v2h-1.5V2a.5.5 0 0 0-.5-.5H8V0h2Z\" />\n			</svg>\n		</button></figure>\n\n\n\n<figure data-wp-context=\"{&quot;imageId&quot;:&quot;6834b9fe38fc4&quot;}\" data-wp-interactive=\"core/image\" class=\"wp-block-image size-large wp-lightbox-container\"><img data-recalc-dims=\"1\" loading=\"lazy\" decoding=\"async\" width=\"1024\" height=\"683\" data-wp-class--hide=\"state.isContentHidden\" data-wp-class--show=\"state.isContentVisible\" data-wp-init=\"callbacks.setButtonStyles\" data-wp-on-async--click=\"actions.showLightbox\" data-wp-on-async--load=\"callbacks.setButtonStyles\" data-wp-on-async-window--resize=\"callbacks.setButtonStyles\" data-id=\"18739\" src=\"https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A8821.jpg?resize=1024%2C683&#038;ssl=1\" alt=\"\" class=\"wp-image-18739\" srcset=\"https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A8821-scaled.jpg?resize=1024%2C683&amp;ssl=1 1024w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A8821-scaled.jpg?resize=300%2C200&amp;ssl=1 300w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A8821-scaled.jpg?resize=768%2C512&amp;ssl=1 768w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A8821-scaled.jpg?resize=1536%2C1024&amp;ssl=1 1536w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A8821-scaled.jpg?resize=2048%2C1365&amp;ssl=1 2048w\" sizes=\"auto, (max-width: 1000px) 100vw, 1000px\" /><button\n			class=\"lightbox-trigger\"\n			type=\"button\"\n			aria-haspopup=\"dialog\"\n			aria-label=\"Enlarge\"\n			data-wp-init=\"callbacks.initTriggerButton\"\n			data-wp-on-async--click=\"actions.showLightbox\"\n			data-wp-style--right=\"state.imageButtonRight\"\n			data-wp-style--top=\"state.imageButtonTop\"\n		>\n			<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"12\" height=\"12\" fill=\"none\" viewBox=\"0 0 12 12\">\n				<path fill=\"#fff\" d=\"M2 0a2 2 0 0 0-2 2v2h1.5V2a.5.5 0 0 1 .5-.5h2V0H2Zm2 10.5H2a.5.5 0 0 1-.5-.5V8H0v2a2 2 0 0 0 2 2h2v-1.5ZM8 12v-1.5h2a.5.5 0 0 0 .5-.5V8H12v2a2 2 0 0 1-2 2H8Zm2-12a2 2 0 0 1 2 2v2h-1.5V2a.5.5 0 0 0-.5-.5H8V0h2Z\" />\n			</svg>\n		</button></figure>\n\n\n\n<figure data-wp-context=\"{&quot;imageId&quot;:&quot;6834b9fe3937f&quot;}\" data-wp-interactive=\"core/image\" class=\"wp-block-image size-large wp-lightbox-container\"><img data-recalc-dims=\"1\" loading=\"lazy\" decoding=\"async\" width=\"1024\" height=\"683\" data-wp-class--hide=\"state.isContentHidden\" data-wp-class--show=\"state.isContentVisible\" data-wp-init=\"callbacks.setButtonStyles\" data-wp-on-async--click=\"actions.showLightbox\" data-wp-on-async--load=\"callbacks.setButtonStyles\" data-wp-on-async-window--resize=\"callbacks.setButtonStyles\" data-id=\"18734\" src=\"https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A6765.jpg?resize=1024%2C683&#038;ssl=1\" alt=\"\" class=\"wp-image-18734\" srcset=\"https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A6765-scaled.jpg?resize=1024%2C683&amp;ssl=1 1024w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A6765-scaled.jpg?resize=300%2C200&amp;ssl=1 300w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A6765-scaled.jpg?resize=768%2C512&amp;ssl=1 768w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A6765-scaled.jpg?resize=1536%2C1024&amp;ssl=1 1536w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A6765-scaled.jpg?resize=2048%2C1365&amp;ssl=1 2048w\" sizes=\"auto, (max-width: 1000px) 100vw, 1000px\" /><button\n			class=\"lightbox-trigger\"\n			type=\"button\"\n			aria-haspopup=\"dialog\"\n			aria-label=\"Enlarge\"\n			data-wp-init=\"callbacks.initTriggerButton\"\n			data-wp-on-async--click=\"actions.showLightbox\"\n			data-wp-style--right=\"state.imageButtonRight\"\n			data-wp-style--top=\"state.imageButtonTop\"\n		>\n			<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"12\" height=\"12\" fill=\"none\" viewBox=\"0 0 12 12\">\n				<path fill=\"#fff\" d=\"M2 0a2 2 0 0 0-2 2v2h1.5V2a.5.5 0 0 1 .5-.5h2V0H2Zm2 10.5H2a.5.5 0 0 1-.5-.5V8H0v2a2 2 0 0 0 2 2h2v-1.5ZM8 12v-1.5h2a.5.5 0 0 0 .5-.5V8H12v2a2 2 0 0 1-2 2H8Zm2-12a2 2 0 0 1 2 2v2h-1.5V2a.5.5 0 0 0-.5-.5H8V0h2Z\" />\n			</svg>\n		</button></figure>\n\n\n\n<figure data-wp-context=\"{&quot;imageId&quot;:&quot;6834b9fe3972f&quot;}\" data-wp-interactive=\"core/image\" class=\"wp-block-image size-large wp-lightbox-container\"><img data-recalc-dims=\"1\" loading=\"lazy\" decoding=\"async\" width=\"1024\" height=\"683\" data-wp-class--hide=\"state.isContentHidden\" data-wp-class--show=\"state.isContentVisible\" data-wp-init=\"callbacks.setButtonStyles\" data-wp-on-async--click=\"actions.showLightbox\" data-wp-on-async--load=\"callbacks.setButtonStyles\" data-wp-on-async-window--resize=\"callbacks.setButtonStyles\" data-id=\"18741\" src=\"https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A9926.jpg?resize=1024%2C683&#038;ssl=1\" alt=\"\" class=\"wp-image-18741\" srcset=\"https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A9926-scaled.jpg?resize=1024%2C683&amp;ssl=1 1024w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A9926-scaled.jpg?resize=300%2C200&amp;ssl=1 300w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A9926-scaled.jpg?resize=768%2C512&amp;ssl=1 768w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A9926-scaled.jpg?resize=1536%2C1024&amp;ssl=1 1536w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A9926-scaled.jpg?resize=2048%2C1365&amp;ssl=1 2048w\" sizes=\"auto, (max-width: 1000px) 100vw, 1000px\" /><button\n			class=\"lightbox-trigger\"\n			type=\"button\"\n			aria-haspopup=\"dialog\"\n			aria-label=\"Enlarge\"\n			data-wp-init=\"callbacks.initTriggerButton\"\n			data-wp-on-async--click=\"actions.showLightbox\"\n			data-wp-style--right=\"state.imageButtonRight\"\n			data-wp-style--top=\"state.imageButtonTop\"\n		>\n			<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"12\" height=\"12\" fill=\"none\" viewBox=\"0 0 12 12\">\n				<path fill=\"#fff\" d=\"M2 0a2 2 0 0 0-2 2v2h1.5V2a.5.5 0 0 1 .5-.5h2V0H2Zm2 10.5H2a.5.5 0 0 1-.5-.5V8H0v2a2 2 0 0 0 2 2h2v-1.5ZM8 12v-1.5h2a.5.5 0 0 0 .5-.5V8H12v2a2 2 0 0 1-2 2H8Zm2-12a2 2 0 0 1 2 2v2h-1.5V2a.5.5 0 0 0-.5-.5H8V0h2Z\" />\n			</svg>\n		</button></figure>\n\n\n\n<figure data-wp-context=\"{&quot;imageId&quot;:&quot;6834b9fe39ae5&quot;}\" data-wp-interactive=\"core/image\" class=\"wp-block-image size-large wp-lightbox-container\"><img data-recalc-dims=\"1\" loading=\"lazy\" decoding=\"async\" width=\"1024\" height=\"683\" data-wp-class--hide=\"state.isContentHidden\" data-wp-class--show=\"state.isContentVisible\" data-wp-init=\"callbacks.setButtonStyles\" data-wp-on-async--click=\"actions.showLightbox\" data-wp-on-async--load=\"callbacks.setButtonStyles\" data-wp-on-async-window--resize=\"callbacks.setButtonStyles\" data-id=\"18740\" src=\"https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A9269.jpg?resize=1024%2C683&#038;ssl=1\" alt=\"\" class=\"wp-image-18740\" srcset=\"https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A9269-scaled.jpg?resize=1024%2C683&amp;ssl=1 1024w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A9269-scaled.jpg?resize=300%2C200&amp;ssl=1 300w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A9269-scaled.jpg?resize=768%2C512&amp;ssl=1 768w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A9269-scaled.jpg?resize=1536%2C1024&amp;ssl=1 1536w, https://i0.wp.com/wordpress.org/news/files/2025/05/1H4A9269-scaled.jpg?resize=2048%2C1365&amp;ssl=1 2048w\" sizes=\"auto, (max-width: 1000px) 100vw, 1000px\" /><button\n			class=\"lightbox-trigger\"\n			type=\"button\"\n			aria-haspopup=\"dialog\"\n			aria-label=\"Enlarge\"\n			data-wp-init=\"callbacks.initTriggerButton\"\n			data-wp-on-async--click=\"actions.showLightbox\"\n			data-wp-style--right=\"state.imageButtonRight\"\n			data-wp-style--top=\"state.imageButtonTop\"\n		>\n			<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"12\" height=\"12\" fill=\"none\" viewBox=\"0 0 12 12\">\n				<path fill=\"#fff\" d=\"M2 0a2 2 0 0 0-2 2v2h1.5V2a.5.5 0 0 1 .5-.5h2V0H2Zm2 10.5H2a.5.5 0 0 1-.5-.5V8H0v2a2 2 0 0 0 2 2h2v-1.5ZM8 12v-1.5h2a.5.5 0 0 0 .5-.5V8H12v2a2 2 0 0 1-2 2H8Zm2-12a2 2 0 0 1 2 2v2h-1.5V2a.5.5 0 0 0-.5-.5H8V0h2Z\" />\n			</svg>\n		</button></figure>\n</figure>\n\n\n\n<p>WordPress Campus Connect transcends the conventional workshop model by fostering a holistic learning community. It couples on-campus event learning with a diverse range of post-event activities, including meetups, website challenges, scholarships, and volunteering opportunities, all geared towards nurturing student development. The program&#8217;s efficacy has prompted other organizations in India to express interest in replicating its structure.</p>\n\n\n\n<p>Looking ahead, multiple local WordPress communities in India aim to reach more students in India through WordPress Campus Connect events. The curriculum will include beginner content, delve into more advanced WordPress concepts, and feature specialized sessions tailored for students with prior WordPress experience.&nbsp;</p>\n\n\n\n<div class=\"wp-block-group is-layout-constrained wp-container-core-group-is-layout-8a400663 wp-block-group-is-layout-constrained\">\n<figure class=\"wp-block-image size-large\"><img data-recalc-dims=\"1\" loading=\"lazy\" decoding=\"async\" width=\"1024\" height=\"351\" src=\"https://i0.wp.com/wordpress.org/news/files/2025/05/wpcc-logo-transparent.png?resize=1024%2C351&#038;ssl=1\" alt=\"\" class=\"wp-image-18743\" style=\"object-fit:cover\" srcset=\"https://i0.wp.com/wordpress.org/news/files/2025/05/wpcc-logo-transparent-scaled.png?resize=1024%2C351&amp;ssl=1 1024w, https://i0.wp.com/wordpress.org/news/files/2025/05/wpcc-logo-transparent-scaled.png?resize=300%2C103&amp;ssl=1 300w, https://i0.wp.com/wordpress.org/news/files/2025/05/wpcc-logo-transparent-scaled.png?resize=768%2C263&amp;ssl=1 768w, https://i0.wp.com/wordpress.org/news/files/2025/05/wpcc-logo-transparent-scaled.png?resize=1536%2C527&amp;ssl=1 1536w, https://i0.wp.com/wordpress.org/news/files/2025/05/wpcc-logo-transparent-scaled.png?resize=2048%2C702&amp;ssl=1 2048w\" sizes=\"auto, (max-width: 1000px) 100vw, 1000px\" /></figure>\n</div>\n\n\n\n<p>The official recognition of WordPress Campus Connect as an event series paves the way for further expansion, giving the series similar support and standing as WordCamps but with a student education-first goal and focus. Future plans include organizing large-scale student events, establishing WordPress clubs on college campuses, and facilitating mentorship connections for students.</p>\n\n\n\n<p>To support these ambitious goals, volunteers identified several key next steps:</p>\n\n\n\n<ul class=\"wp-block-list\">\n<li><strong>Volunteer Handbook Development:</strong> Creating a comprehensive guidebook to equip volunteers with the necessary resources and information.</li>\n\n\n\n<li><strong>GatherPress Integration:</strong> Exploring the feasibility of integrating GatherPress as a tool for student groups.</li>\n\n\n\n<li><strong>Volunteer Recruitment:</strong> Actively seeking and onboarding volunteers to support WordPress Campus Connect initiatives through activities such as:\n<ol class=\"wp-block-list\">\n<li>Creating a workflow and guidelines for processing Student Club applications</li>\n\n\n\n<li>On-site facilitation or assistance for WordPress Campus Connect events</li>\n</ol>\n</li>\n\n\n\n<li><strong>Landing Page Creation</strong>: Creating a landing page describing what WordPress Campus Connect is all about</li>\n\n\n\n<li><strong>Student Groups: </strong>Drafting a framework for students to create their own groups for hosting WordPress events and activities.&nbsp;</li>\n</ul>\n\n\n\n<p>The overwhelming success of WordPress Campus Connect and the enthusiasm it has generated serve as a testament to the transformative power of passion and dedication. As WordPress Campus Connect continues to evolve and expand, it holds the promise of shaping the future of WordPress education and community engagement.</p>\n\n\n\n<p class=\"has-off-white-background-color has-background\">If you’re interested in helping shape the future of education with WordPress, <a href=\"https://wordpress.slack.com/archives/C08QK4V94AF\">join us in the #campusconnect Make Slack channel</a> today!</p>\n\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:30:\"com-wordpress:feed-additions:1\";a:1:{s:7:\"post-id\";a:1:{i:0;a:5:{s:4:\"data\";s:5:\"18726\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}}}i:1;a:6:{s:4:\"data\";s:57:\"\n		\n		\n		\n		\n		\n				\n		\n\n					\n										\n					\n		\n		\n			\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";s:5:\"child\";a:4:{s:0:\"\";a:6:{s:5:\"title\";a:1:{i:0;a:5:{s:4:\"data\";s:35:\"WordPress 6.8.1 Maintenance Release\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"link\";a:1:{i:0;a:5:{s:4:\"data\";s:71:\"https://wordpress.org/news/2025/04/wordpress-6-8-1-maintenance-release/\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:7:\"pubDate\";a:1:{i:0;a:5:{s:4:\"data\";s:31:\"Wed, 30 Apr 2025 17:17:09 +0000\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:8:\"category\";a:1:{i:0;a:5:{s:4:\"data\";s:8:\"Releases\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"guid\";a:1:{i:0;a:5:{s:4:\"data\";s:35:\"https://wordpress.org/news/?p=18721\";s:7:\"attribs\";a:1:{s:0:\"\";a:1:{s:11:\"isPermaLink\";s:5:\"false\";}}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:11:\"description\";a:1:{i:0;a:5:{s:4:\"data\";s:371:\"WordPress 6.8.1 is now available! This minor release includes fixes for 15 bugs throughout Core and the Block Editor addressing issues affecting multiple areas of WordPress including the block editor, multisite, and REST API. For a full list of bug fixes, please refer to the release candidate announcement. WordPress 6.8.1 is a short-cycle maintenance release. [&#8230;]\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:32:\"http://purl.org/dc/elements/1.1/\";a:1:{s:7:\"creator\";a:1:{i:0;a:5:{s:4:\"data\";s:12:\"Aaron Jorbin\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:40:\"http://purl.org/rss/1.0/modules/content/\";a:1:{s:7:\"encoded\";a:1:{i:0;a:5:{s:4:\"data\";s:6967:\"\n<h2 class=\"wp-block-heading\">WordPress 6.8.1 is now available!</h2>\n\n\n\n<p>This minor release includes fixes for 15 bugs <a href=\"https://core.trac.wordpress.org/query?resolution=fixed&amp;milestone=6.8.1&amp;group=component&amp;col=id&amp;col=summary&amp;col=milestone&amp;col=owner&amp;col=type&amp;col=status&amp;col=priority&amp;order=priority\">throughout Core</a> and <a href=\"https://github.com/WordPress/wordpress-develop/pull/8752\">the Block Editor</a> addressing issues affecting multiple areas of WordPress including the block editor, multisite, and REST API. For a full list of bug fixes, please refer to the <a href=\"https://make.wordpress.org/core/2025/04/28/wordpress-6-8-1-rc1-is-now-available/\">release candidate announcement.</a></p>\n\n\n\n<p>WordPress 6.8.1 is a short-cycle maintenance release. More maintenance releases will be made available throughout 2025.</p>\n\n\n\n<p>If you have sites that support automatic background updates, the update process will begin automatically.</p>\n\n\n\n<p>You can <a href=\"https://wordpress.org/wordpress-6.8.1.zip\">download WordPress 6.8.1 from WordPress.org</a>, or visit your WordPress Dashboard, click “Updates”, and then click “Update Now”. For more information on this release, please <a href=\"https://wordpress.org/support/wordpress-version/version-6-8-1\">visit the HelpHub site</a>.</p>\n\n\n\n<h2 class=\"wp-block-heading\">Thank you to these WordPress contributors</h2>\n\n\n\n<p>This release was led by <a href=\"https://profiles.wordpress.org/jorbin/\">Aaron Jorbin</a>.</p>\n\n\n\n<p>WordPress 6.8.1 would not have been possible without the contributions of the following people. Their asynchronous coordination to deliver maintenance fixes into a stable release is a testament to the power and capability of the WordPress community.</p>\n\n\n\n<p class=\"is-style-wporg-props-medium\"><a href=\"https://profiles.wordpress.org/jorbin\">Aaron Jorbin</a>, <a href=\"https://profiles.wordpress.org/adamsilverstein\">Adam Silverstein</a>, <a href=\"https://profiles.wordpress.org/wildworks\">Aki Hamano</a>, <a href=\"https://profiles.wordpress.org/ankitmaru\">Ankit Panchal</a>, <a href=\"https://profiles.wordpress.org/bernhard-reiter\">bernhard-reiter</a>, <a href=\"https://profiles.wordpress.org/poena\">Carolina Nymark</a>, <a href=\"https://profiles.wordpress.org/codeamp\">Code Amp</a>, <a href=\"https://profiles.wordpress.org/talldanwp\">Daniel Richards</a>, <a href=\"https://profiles.wordpress.org/davidbaumwald\">David Baumwald</a>, <a href=\"https://profiles.wordpress.org/justlevine\">David Levine</a>, <a href=\"https://profiles.wordpress.org/dilipbheda\">Dilip Bheda</a>, <a href=\"https://profiles.wordpress.org/dd32\">Dion Hulse</a>, <a href=\"https://profiles.wordpress.org/dsawyers\">dsawyers</a>, <a href=\"https://profiles.wordpress.org/eduwass\">eduwass</a>, <a href=\"https://profiles.wordpress.org/ethitter\">Erick Hitter</a>, <a href=\"https://profiles.wordpress.org/estelaris/\">Estela Rueda</a>, <a href=\"https://profiles.wordpress.org/fabiankaegy\">Fabian Kägy</a>, <a href=\"https://profiles.wordpress.org/mamaduka\">George Mamadashvili</a>, <a href=\"https://profiles.wordpress.org/gziolo\">Greg Ziółkowski</a>, <a href=\"https://profiles.wordpress.org/kabir93\">H. Kabir</a>, <a href=\"https://profiles.wordpress.org/hideishi\">hideishi</a>, <a href=\"https://profiles.wordpress.org/abcd95\">Himanshu Pathak</a>, <a href=\"https://profiles.wordpress.org/jarekmorawski\">jarekmorawski</a>, <a href=\"https://profiles.wordpress.org/audrasjb\">Jb Audras</a>, <a href=\"https://profiles.wordpress.org/JeffPaul\">Jeffrey Paul</a>, <a href=\"https://profiles.wordpress.org/jeffr0\">Jeffro</a>, <a href=\"https://profiles.wordpress.org/jeremyfelt\">Jeremy Felt</a>, <a href=\"https://profiles.wordpress.org/joedolson\">Joe Dolson</a>, <a href=\"https://profiles.wordpress.org/joemcgill\">Joe McGill</a>, <a href=\"https://profiles.wordpress.org/joen\">Joen A.</a>, <a href=\"https://profiles.wordpress.org/johnjamesjacoby\">John James Jacoby</a>, <a href=\"https://profiles.wordpress.org/desrosj\">Jonathan Desrosiers</a>, <a href=\"https://profiles.wordpress.org/spacedmonkey\">Jonny Harris</a>, <a href=\"https://profiles.wordpress.org/verygoode\">Joshua Goode</a>, <a href=\"https://profiles.wordpress.org/karthikeya01\">Karthikeya Bethu</a>, <a href=\"https://profiles.wordpress.org/iamkingsleyf\">Kingsley Felix</a>, <a href=\"https://profiles.wordpress.org/obenland\">Konstantin Obenland</a>, <a href=\"https://profiles.wordpress.org/0mirka00\">Lena Morita</a>, <a href=\"https://profiles.wordpress.org/lilgames\">LilGames</a>, <a href=\"https://profiles.wordpress.org/megane9988\">megane9988</a>, <a href=\"https://profiles.wordpress.org/marktimemedia\">Michelle Schulp Hunt</a>, <a href=\"https://profiles.wordpress.org/presstoke\">Mitchell Austin</a>, <a href=\"https://profiles.wordpress.org/mukesh27\">Mukesh Panchal</a>, <a href=\"https://profiles.wordpress.org/nickwilmot\">nickwilmot</a>, <a href=\"https://profiles.wordpress.org/nikunj8866\">Nikunj Hatkar</a>, <a href=\"https://profiles.wordpress.org/swissspidy\">Pascal Birchler</a>, <a href=\"https://profiles.wordpress.org/pbiron\">Paul Biron</a>, <a href=\"https://profiles.wordpress.org/peterwilsoncc\">Peter Wilson</a>, <a href=\"https://profiles.wordpress.org/pratiklondhe\">Pratik Londhe</a>, <a href=\"https://profiles.wordpress.org/presskopp\">Presskopp</a>, <a href=\"https://profiles.wordpress.org/sainathpoojary\">Sainath Poojary</a>, <a href=\"https://profiles.wordpress.org/sc0ttkclark\">Scott Kingsley Clark</a>, <a href=\"https://profiles.wordpress.org/coffee2code/\">Scott Reilly</a>, <a href=\"https://profiles.wordpress.org/SergeyBiryukov\">Sergey Biryukov</a>, <a href=\"https://profiles.wordpress.org/SirLouen\">SirLouen</a>, <a href=\"https://profiles.wordpress.org/soean\">Sören Wünsch</a>, <a href=\"https://profiles.wordpress.org/sourav08\">Sourav Pahwa</a>, <a href=\"https://profiles.wordpress.org/sabernhardt\">Stephen Bernhardt</a>, <a href=\"https://profiles.wordpress.org/takuword\">takuword</a>, <a href=\"https://profiles.wordpress.org/tusharaddweb\">Tushar Patel</a>, <a href=\"https://profiles.wordpress.org/westonruter\">Weston Ruter</a>, <a href=\"https://profiles.wordpress.org/yogeshbhutkar\">Yogesh Bhutkar</a></p>\n\n\n\n<h2 class=\"wp-block-heading\">How to contribute</h2>\n\n\n\n<p>To get involved in WordPress core development, head over to Trac, <a href=\"https://core.trac.wordpress.org/report/6\">pick a ticket</a>, and join the conversation in the <a href=\"https://wordpress.slack.com/archives/C02RQBWTW\">#core</a> and <a href=\"https://wordpress.slack.com/archives/C080HLPP2Q6\">#6-8-release-leads</a> channels. Need help? Check out the <a href=\"https://make.wordpress.org/core/handbook/\">Core Contributor Handbook</a>.</p>\n\n\n\n<p><em>Props to <a href=\'https://profiles.wordpress.org/estelaris/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>estelaris</a> and <a href=\'https://profiles.wordpress.org/joedolson/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>joedolson</a> for proofreading.</em></p>\n\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:30:\"com-wordpress:feed-additions:1\";a:1:{s:7:\"post-id\";a:1:{i:0;a:5:{s:4:\"data\";s:5:\"18721\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}}}i:2;a:6:{s:4:\"data\";s:57:\"\n		\n		\n		\n		\n		\n				\n		\n\n					\n										\n					\n		\n		\n			\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";s:5:\"child\";a:4:{s:0:\"\";a:6:{s:5:\"title\";a:1:{i:0;a:5:{s:4:\"data\";s:17:\"WordPress Jubilee\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"link\";a:1:{i:0;a:5:{s:4:\"data\";s:43:\"https://wordpress.org/news/2025/04/jubilee/\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:7:\"pubDate\";a:1:{i:0;a:5:{s:4:\"data\";s:31:\"Tue, 22 Apr 2025 02:07:36 +0000\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:8:\"category\";a:1:{i:0;a:5:{s:4:\"data\";s:7:\"General\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"guid\";a:1:{i:0;a:5:{s:4:\"data\";s:35:\"https://wordpress.org/news/?p=18716\";s:7:\"attribs\";a:1:{s:0:\"\";a:1:{s:11:\"isPermaLink\";s:5:\"false\";}}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:11:\"description\";a:1:{i:0;a:5:{s:4:\"data\";s:289:\"As I said, we&#8217;re dropping all the human blocks. Community guidelines, directory guidelines, and such will need to be followed going forward, but whatever blocks were in place before are now cleared. It may take a few days, but any pre-existing blocks are considered bugs to be fixed.\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:32:\"http://purl.org/dc/elements/1.1/\";a:1:{s:7:\"creator\";a:1:{i:0;a:5:{s:4:\"data\";s:14:\"Matt Mullenweg\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:40:\"http://purl.org/rss/1.0/modules/content/\";a:1:{s:7:\"encoded\";a:1:{i:0;a:5:{s:4:\"data\";s:298:\"\n<p>As I said, we&#8217;re dropping all the human blocks. Community guidelines, directory guidelines, and such will need to be followed going forward, but whatever blocks were in place before are now cleared. It may take a few days, but any pre-existing blocks are considered bugs to be fixed.</p>\n\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:30:\"com-wordpress:feed-additions:1\";a:1:{s:7:\"post-id\";a:1:{i:0;a:5:{s:4:\"data\";s:5:\"18716\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}}}i:3;a:6:{s:4:\"data\";s:66:\"\n		\n		\n		\n		\n		\n				\n		\n		\n		\n		\n\n					\n										\n					\n		\n		\n			\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";s:5:\"child\";a:4:{s:0:\"\";a:6:{s:5:\"title\";a:1:{i:0;a:5:{s:4:\"data\";s:25:\"WordPress 6.8 “Cecil”\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"link\";a:1:{i:0;a:5:{s:4:\"data\";s:41:\"https://wordpress.org/news/2025/04/cecil/\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:7:\"pubDate\";a:1:{i:0;a:5:{s:4:\"data\";s:31:\"Tue, 15 Apr 2025 17:00:45 +0000\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:8:\"category\";a:4:{i:0;a:5:{s:4:\"data\";s:7:\"General\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:1;a:5:{s:4:\"data\";s:8:\"Releases\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:2;a:5:{s:4:\"data\";s:3:\"6.8\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:3;a:5:{s:4:\"data\";s:8:\"releases\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"guid\";a:1:{i:0;a:5:{s:4:\"data\";s:35:\"https://wordpress.org/news/?p=18701\";s:7:\"attribs\";a:1:{s:0:\"\";a:1:{s:11:\"isPermaLink\";s:5:\"false\";}}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:11:\"description\";a:1:{i:0;a:5:{s:4:\"data\";s:395:\"WordPress 6.8, code-named “Cecil,” refines your creative workflow with a streamlined Style Book, now available in select Classic themes. Enjoy faster navigation via speculative loading, stronger security with bcrypt password hashing, and over 100 accessibility improvements. With thoughtful editor upgrades and behind-the-scenes performance boosts, 6.8 delivers polish, power, and precision.\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:32:\"http://purl.org/dc/elements/1.1/\";a:1:{s:7:\"creator\";a:1:{i:0;a:5:{s:4:\"data\";s:12:\"Jeffrey Paul\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:40:\"http://purl.org/rss/1.0/modules/content/\";a:1:{s:7:\"encoded\";a:1:{i:0;a:5:{s:4:\"data\";s:80563:\"\n<figure class=\"wp-block-image size-large\"><img data-recalc-dims=\"1\" loading=\"lazy\" decoding=\"async\" width=\"1024\" height=\"535\" src=\"https://i0.wp.com/wordpress.org/news/files/2025/04/WordPress-6-8-Cecil.png?resize=1024%2C535&#038;ssl=1\" alt=\"Simulated record album featuring a grid of brightly-colored duotone portraits of Cecil Taylor\" class=\"wp-image-18708\" srcset=\"https://i0.wp.com/wordpress.org/news/files/2025/04/WordPress-6-8-Cecil.png?resize=1024%2C535&amp;ssl=1 1024w, https://i0.wp.com/wordpress.org/news/files/2025/04/WordPress-6-8-Cecil.png?resize=300%2C157&amp;ssl=1 300w, https://i0.wp.com/wordpress.org/news/files/2025/04/WordPress-6-8-Cecil.png?resize=768%2C401&amp;ssl=1 768w, https://i0.wp.com/wordpress.org/news/files/2025/04/WordPress-6-8-Cecil.png?resize=1536%2C802&amp;ssl=1 1536w, https://i0.wp.com/wordpress.org/news/files/2025/04/WordPress-6-8-Cecil.png?resize=2048%2C1070&amp;ssl=1 2048w, https://i0.wp.com/wordpress.org/news/files/2025/04/WordPress-6-8-Cecil.png?w=3000&amp;ssl=1 3000w\" sizes=\"auto, (max-width: 1000px) 100vw, 1000px\" /></figure>\n\n\n\n<p>Each WordPress release celebrates an artist who has left an indelible mark on music.&nbsp; WordPress 6.8, code-named “Cecil,” honors the legendary pianist and jazz pioneer Cecil Taylor.</p>\n\n\n\n<p>Classically trained yet relentlessly unconventional, Taylor reimagined the piano as a percussive instrument—layering tone clusters, polyphony, and rhythm into a sound both chaotic and precise. His music defied expectation, finding form in disorder and harmony in dissonance.</p>\n\n\n\n<p>That same spirit drives WordPress 6.8. Embrace its bold new features with the same curiosity and experimentation that defined Cecil’s sound.</p>\n\n\n\n<h2 class=\"wp-block-heading\">Welcome to WordPress 6.8!</h2>\n\n\n\n<p>WordPress 6.8 polishes and refines the tools that you use every day, making your site faster, more secure, and easier to manage.&nbsp; The Style Book now has a structured layout and works with Classic themes, giving you more control over global styles. Speculative loading speeds up navigation by preloading links before users navigate to them, bcrypt hashing strengthens password security automatically, and database optimizations improve performance.</p>\n\n\n\n<p><a href=\"https://wordpress.org/wordpress-6.8.zip\">Download WordPress 6.8 “Cecil”</a></p>\n\n\n\n<h2 class=\"wp-block-heading\">A release polished to a high sheen.</h2>\n\n\n\n<h3 class=\"wp-block-heading\">The Style Book gets a cleaner look—and a few new tricks.</h3>\n\n\n\n<p>The Style Book has a new, structured layout and clearer labels, to make it even easier to edit colors, typography—almost all your site styles—in one place.</p>\n\n\n\n<p>Plus, now you can see it in Classic themes that have editor-styles or a theme.json file.&nbsp; Find the Style Book under Appearance &gt; Design and use it to preview your theme’s evolution, as you edit CSS or make changes in the Customizer.</p>\n\n\n\n<figure class=\"wp-block-image size-full\"><img data-recalc-dims=\"1\" loading=\"lazy\" decoding=\"async\" width=\"900\" height=\"900\" src=\"https://i0.wp.com/wordpress.org/news/files/2025/04/6-8-style-book.png?resize=900%2C900&#038;ssl=1\" alt=\"\" class=\"wp-image-18703\" srcset=\"https://i0.wp.com/wordpress.org/news/files/2025/04/6-8-style-book.png?w=900&amp;ssl=1 900w, https://i0.wp.com/wordpress.org/news/files/2025/04/6-8-style-book.png?resize=300%2C300&amp;ssl=1 300w, https://i0.wp.com/wordpress.org/news/files/2025/04/6-8-style-book.png?resize=150%2C150&amp;ssl=1 150w, https://i0.wp.com/wordpress.org/news/files/2025/04/6-8-style-book.png?resize=768%2C768&amp;ssl=1 768w\" sizes=\"auto, (max-width: 900px) 100vw, 900px\" /></figure>\n\n\n\n<h3 class=\"wp-block-heading\">Editor improvements</h3>\n\n\n\n<p>Easier ways to see your options in Data Views, and you can exclude sticky posts from the Query Loop.&nbsp; Plus, you’ll find lots of little improvements in the editor that smooth your way through everything you build.</p>\n\n\n\n<figure class=\"wp-block-image size-full\"><img data-recalc-dims=\"1\" loading=\"lazy\" decoding=\"async\" width=\"900\" height=\"900\" src=\"https://i0.wp.com/wordpress.org/news/files/2025/04/6-8-editor-improvements.png?resize=900%2C900&#038;ssl=1\" alt=\"\" class=\"wp-image-18705\" srcset=\"https://i0.wp.com/wordpress.org/news/files/2025/04/6-8-editor-improvements.png?w=900&amp;ssl=1 900w, https://i0.wp.com/wordpress.org/news/files/2025/04/6-8-editor-improvements.png?resize=300%2C300&amp;ssl=1 300w, https://i0.wp.com/wordpress.org/news/files/2025/04/6-8-editor-improvements.png?resize=150%2C150&amp;ssl=1 150w, https://i0.wp.com/wordpress.org/news/files/2025/04/6-8-editor-improvements.png?resize=768%2C768&amp;ssl=1 768w\" sizes=\"auto, (max-width: 900px) 100vw, 900px\" /></figure>\n\n\n\n<h3 class=\"wp-block-heading\">Near-instant page loads, thanks to Speculative Loading</h3>\n\n\n\n<p>In WordPress 6.8, pages load faster than ever.&nbsp; When you or your user hovers over or clicks a link, WordPress may preload the next page, for a smoother, near-instant experience. The system balances speed and efficiency, and you can control how it works, with a plugin or your own code.&nbsp; This feature only works in modern browsers—older ones will simply ignore it without any impact.</p>\n\n\n\n<figure class=\"wp-block-image size-full\"><img data-recalc-dims=\"1\" loading=\"lazy\" decoding=\"async\" width=\"900\" height=\"900\" src=\"https://i0.wp.com/wordpress.org/news/files/2025/04/6-8-speculative-loading-1.png?resize=900%2C900&#038;ssl=1\" alt=\"\" class=\"wp-image-18706\" srcset=\"https://i0.wp.com/wordpress.org/news/files/2025/04/6-8-speculative-loading-1.png?w=900&amp;ssl=1 900w, https://i0.wp.com/wordpress.org/news/files/2025/04/6-8-speculative-loading-1.png?resize=300%2C300&amp;ssl=1 300w, https://i0.wp.com/wordpress.org/news/files/2025/04/6-8-speculative-loading-1.png?resize=150%2C150&amp;ssl=1 150w, https://i0.wp.com/wordpress.org/news/files/2025/04/6-8-speculative-loading-1.png?resize=768%2C768&amp;ssl=1 768w\" sizes=\"auto, (max-width: 900px) 100vw, 900px\" /></figure>\n\n\n\n<h3 class=\"wp-block-heading\">Stronger password security with bcrypt</h3>\n\n\n\n<p>Now passwords are harder to crack with bcrypt hashing, which takes a lot more computing power to break.&nbsp; This strengthens overall security, as do other encryption improvements across WordPress.&nbsp; You don’t need to do anything—everything updates automatically.</p>\n\n\n\n<figure class=\"wp-block-image size-full\"><img data-recalc-dims=\"1\" loading=\"lazy\" decoding=\"async\" width=\"900\" height=\"900\" src=\"https://i0.wp.com/wordpress.org/news/files/2025/04/6-8-stronger-password-security-bcrypt.png?resize=900%2C900&#038;ssl=1\" alt=\"\" class=\"wp-image-18707\" srcset=\"https://i0.wp.com/wordpress.org/news/files/2025/04/6-8-stronger-password-security-bcrypt.png?w=900&amp;ssl=1 900w, https://i0.wp.com/wordpress.org/news/files/2025/04/6-8-stronger-password-security-bcrypt.png?resize=300%2C300&amp;ssl=1 300w, https://i0.wp.com/wordpress.org/news/files/2025/04/6-8-stronger-password-security-bcrypt.png?resize=150%2C150&amp;ssl=1 150w, https://i0.wp.com/wordpress.org/news/files/2025/04/6-8-stronger-password-security-bcrypt.png?resize=768%2C768&amp;ssl=1 768w\" sizes=\"auto, (max-width: 900px) 100vw, 900px\" /></figure>\n\n\n\n<h3 class=\"wp-block-heading\">Accessibility improvements</h3>\n\n\n\n<p>100+ accessibility fixes and enhancements touch a broad spectrum of the WordPress experience.&nbsp; This release includes fixes to every bundled theme, improvements to the navigation menu management, the customizer, and simplified labeling.&nbsp; The Block Editor has over 70 improvements to blocks, DataViews, and to its overall user experience.</p>\n\n\n\n<h3 class=\"wp-block-heading\">Performance updates</h3>\n\n\n\n<p>WordPress 6.8 packs a wide range of performance fixes and enhancements to speed up everything from editing to browsing.&nbsp; Beyond speculative loading, WordPress 6.8 pays special attention to the block editor, block type registration, and query caching.&nbsp; Plus, imagine never waiting longer than 50 milliseconds—for any interaction.&nbsp; In WordPress 6.8, the Interactivity API takes a first step toward that goal.</p>\n\n\n\n<h3 class=\"wp-block-heading\">And much more</h3>\n\n\n\n<p>For a comprehensive overview of all the new features and enhancements in WordPress 6.8, please visit the feature-showcase website.</p>\n\n\n\n<p><a href=\"https://wordpress.org/download/releases/6-8/\">Check out what’s new</a></p>\n\n\n\n<h2 class=\"wp-block-heading\">Learn more about WordPress 6.8</h2>\n\n\n\n<p><a href=\"https://learn.wordpress.org/\">Learn WordPress</a> is a free resource for new and experienced WordPress users.&nbsp; Learn is stocked with how-to videos on using various features in WordPress, <a href=\"https://learn.wordpress.org/social-learning/\">interactive workshops</a> for exploring topics in-depth, and lesson plans for diving deep into specific areas of WordPress.</p>\n\n\n\n<p>Read the <a href=\"https://wordpress.org/documentation/wordpress-version/version-6.8\">WordPress 6.8 Release Notes</a> for information on installation, enhancements, fixed issues, release contributors, learning resources, and the list of file changes.</p>\n\n\n\n<p>Explore the <a href=\"https://make.wordpress.org/core/2025/03/28/wordpress-6-8-field-guide/\">WordPress 6.8 Field Guide</a>.&nbsp; Learn about the changes in this release with detailed developer notes to help you build with WordPress.</p>\n\n\n\n<h2 class=\"wp-block-heading\">The 6.8 release squad</h2>\n\n\n\n<p>Every release comes to you from a dedicated team of enthusiastic contributors who help keep things on track and moving smoothly.&nbsp; The team that has led 6.8 is a cross-functional group of contributors who are always ready to champion ideas, remove blockers, and resolve issues.</p>\n\n\n\n<ul class=\"wp-block-list\">\n<li>Release Lead: <a href=\"https://profiles.wordpress.org/matt/\">Matt Mullenweg</a></li>\n\n\n\n<li>Release Coordination: <a href=\"https://profiles.wordpress.org/jeffpaul/\">Jeffrey Paul</a> and <a href=\"https://profiles.wordpress.org/michelleames/\">Michelle Frechette</a></li>\n\n\n\n<li>Tech Leads: <a href=\"https://profiles.wordpress.org/joemcgill/\">Joe McGill</a>, <a href=\"https://profiles.wordpress.org/desrosj/\">Jonathan Desrosiers</a>, and <a href=\"https://profiles.wordpress.org/mamaduka/\">George Mamadashvili</a></li>\n\n\n\n<li>Triage Lead: <a href=\"https://profiles.wordpress.org/audrasjb/\">Jb Audras</a></li>\n\n\n\n<li>Design Lead: <a href=\"https://profiles.wordpress.org/karmatosed/\">Tammie Lister</a></li>\n\n\n\n<li>Performance Lead: <a href=\"https://profiles.wordpress.org/flixos90/\">Felix Arntz</a></li>\n\n\n\n<li>Test Lead: <a href=\"https://profiles.wordpress.org/krupajnanda/\">Krupa Nanda</a></li>\n</ul>\n\n\n\n<h2 class=\"wp-block-heading\"><strong>Thank you, contributors</strong></h2>\n\n\n\n<p>The mission of WordPress is to <a href=\"https://wordpress.org/about/\">democratize publishing</a> and embody the <a href=\"https://opensource.org/osd-annotated\">freedoms that come with open source</a>.&nbsp; A global and diverse community of people collaborating to strengthen the software supports this effort.</p>\n\n\n\n<p>WordPress 6.8 reflects the tireless efforts and passion of more than 900 contributors in more than 60 countries all over the world. This release also welcomed over 250 first-time contributors!&nbsp;</p>\n\n\n\n<p>Their collaboration delivered more than 320 enhancements and fixes, ensuring a stable release for all—a testament to the power and capability of the WordPress open source community.</p>\n\n\n<p class=\"has-small-font-size\"><a href=\"https://profiles.wordpress.org/jorbin/\">Aaron Jorbin</a> · <a href=\"https://profiles.wordpress.org/aaronrobertshaw/\">Aaron Robertshaw</a> · <a href=\"https://profiles.wordpress.org/thehercules/\">Aashish Sharma</a> · <a href=\"https://profiles.wordpress.org/webcommsat/\">Abha Thakor</a> · <a href=\"https://profiles.wordpress.org/hiabhaykulkarni/\">Abhay Kulkarni</a> · <a href=\"https://profiles.wordpress.org/abhi3315/\">Abhishek Sharma</a> · <a href=\"https://profiles.wordpress.org/adakaleh/\">AdaKaleh</a> · <a href=\"https://profiles.wordpress.org/adamsilverstein/\">Adam Silverstein</a> · <a href=\"https://profiles.wordpress.org/zieladam/\">Adam Zieliński</a> · <a href=\"https://profiles.wordpress.org/adhun/\">Adhun Anand</a> · <a href=\"https://profiles.wordpress.org/adityab98/\">Aditya Bansode</a> · <a href=\"https://profiles.wordpress.org/stromhalm/\">Adrian Jagusch</a> · <a href=\"https://profiles.wordpress.org/aduth/\">aduth</a> · <a href=\"https://profiles.wordpress.org/hemant-ahir/\">Ahir Hemant</a> · <a href=\"https://profiles.wordpress.org/ahmarzaidi/\">Ahmar Zaidi</a> · <a href=\"https://profiles.wordpress.org/chaion07/\">Ahmed Kabir Chaion</a> · <a href=\"https://profiles.wordpress.org/engahmeds3ed/\">Ahmed Saeed</a> · <a href=\"https://profiles.wordpress.org/ahsankhan316/\">Ahsan Khan</a> · <a href=\"https://profiles.wordpress.org/aishwarryapande/\">Aishwarrya Pande</a> · <a href=\"https://profiles.wordpress.org/ajayghaghretiya1/\">Ajay Ghaghretiya</a> · <a href=\"https://profiles.wordpress.org/ajmaurya/\">Ajay Maurya</a> · <a href=\"https://profiles.wordpress.org/ajitbohra/\">Ajit Bohra</a> · <a href=\"https://profiles.wordpress.org/akanshusingh/\">Akanshu Singh</a> · <a href=\"https://profiles.wordpress.org/wildworks/\">Aki Hamano</a> · <a href=\"https://profiles.wordpress.org/atachibana/\">Akira Tachibana</a> · <a href=\"https://profiles.wordpress.org/aks30498/\">aks30498</a> · <a href=\"https://profiles.wordpress.org/akshat2802/\">Akshat Kakkad</a> · <a href=\"https://profiles.wordpress.org/akshaydhere/\">Akshay Dhere</a> · <a href=\"https://profiles.wordpress.org/aljullu/\">Albert Juh&#233; Lluveras</a> · <a href=\"https://profiles.wordpress.org/ralessio/\">Alessio</a> · <a href=\"https://profiles.wordpress.org/xknown/\">Alex Concha</a> · <a href=\"https://profiles.wordpress.org/alexflorisca/\">Alex Florisca</a> · <a href=\"https://profiles.wordpress.org/ajlende/\">Alex Lende</a> · <a href=\"https://profiles.wordpress.org/alexstine/\">Alex Stine</a> · <a href=\"https://profiles.wordpress.org/albigdd/\">Alexander Bigga</a> · <a href=\"https://profiles.wordpress.org/alexandrebuffet/\">Alexandre Buffet</a> · <a href=\"https://profiles.wordpress.org/alexus450/\">Alexei Samarschi</a> · <a href=\"https://profiles.wordpress.org/alexschmitz/\">alexschmitz</a> · <a href=\"https://profiles.wordpress.org/alireyad/\">Ali Akbar Reyad</a> · <a href=\"https://profiles.wordpress.org/firewatch/\">allilevine</a> · <a href=\"https://profiles.wordpress.org/alpipego/\">alpipego</a> · <a href=\"https://profiles.wordpress.org/mrfoxtalbot/\">Alvaro G&#243;mez</a> · <a href=\"https://profiles.wordpress.org/akrocks/\">Amaan Khan</a> · <a href=\"https://profiles.wordpress.org/alh0319/\">Amber Hinds</a> · <a href=\"https://profiles.wordpress.org/amimulihsanmahdi/\">Amimul Ihsan</a> · <a href=\"https://profiles.wordpress.org/amin7/\">Amin</a> · <a href=\"https://profiles.wordpress.org/amincharoliya/\">Amin Charoliya</a> · <a href=\"https://profiles.wordpress.org/amitbhosale/\">Amit Bhosale</a> · <a href=\"https://profiles.wordpress.org/amitraj2203/\">Amit Raj</a> · <a href=\"https://profiles.wordpress.org/ammarmassoud/\">Ammar Mohamed Fath Allah Abd-Elaaty Massoud</a> · <a href=\"https://profiles.wordpress.org/amolebonde/\">amolebonde</a> · <a href=\"https://profiles.wordpress.org/anandraj346/\">Anand Raj</a> · <a href=\"https://profiles.wordpress.org/anlino/\">Anders Nor&#233;n</a> · <a href=\"https://profiles.wordpress.org/afercia/\">Andrea Fercia</a> · <a href=\"https://profiles.wordpress.org/awetz583/\">Andrea Roenning</a> · <a href=\"https://profiles.wordpress.org/bowedk/\">Andreas Pedersen</a> · <a href=\"https://profiles.wordpress.org/andraganescu/\">Andrei Draganescu</a> · <a href=\"https://profiles.wordpress.org/euthelup/\">Andrei Lupu</a> · <a href=\"https://profiles.wordpress.org/nacin/\">Andrew Nacin</a> · <a href=\"https://profiles.wordpress.org/azaozz/\">Andrew Ozz</a> · <a href=\"https://profiles.wordpress.org/andrewserong/\">Andrew Serong</a> · <a href=\"https://profiles.wordpress.org/andrewssanya/\">Andrew Ssanya</a> · <a href=\"https://profiles.wordpress.org/rarst/\">Andrey \"Rarst\" Savchenko</a> · <a href=\"https://profiles.wordpress.org/oandregal/\">André Maneiro</a> · <a href=\"https://profiles.wordpress.org/afragen/\">Andy Fragen</a> · <a href=\"https://profiles.wordpress.org/rilwis/\">Anh Tran</a> · <a href=\"https://profiles.wordpress.org/wpgurudev/\">Ankit Gade</a> · <a href=\"https://profiles.wordpress.org/ankit-k-gupta/\">Ankit K Gupta</a> · <a href=\"https://profiles.wordpress.org/ankitkumarshah/\">Ankit Kumar Shah</a> · <a href=\"https://profiles.wordpress.org/ankitmaru/\">Ankit Panchal</a> · <a href=\"https://profiles.wordpress.org/ankitpatel1578/\">Ankit Patel</a> · <a href=\"https://profiles.wordpress.org/anmolverma404/\">Anmol Verma</a> · <a href=\"https://profiles.wordpress.org/annchichi/\">annchichi</a> · <a href=\"https://profiles.wordpress.org/annebovelett/\">Anne-Mieke Bovelett</a> · <a href=\"https://profiles.wordpress.org/annezazu/\">annezazu</a> · <a href=\"https://profiles.wordpress.org/antpb/\">Anthony Burchell</a> · <a href=\"https://profiles.wordpress.org/ahortin/\">Anthony Hortin</a> · <a href=\"https://profiles.wordpress.org/antonvlasenko/\">Anton Vlasenko</a> · <a href=\"https://profiles.wordpress.org/anujrathore24/\">Anuj Singh</a> · <a href=\"https://profiles.wordpress.org/anveshika/\">Anveshika Srivastava</a> · <a href=\"https://profiles.wordpress.org/aristath/\">Ari Stathopoulos</a> · <a href=\"https://profiles.wordpress.org/arielmaidana/\">Ariel Maidana</a> · <a href=\"https://profiles.wordpress.org/arnaudbroes/\">arnaudbroes</a> · <a href=\"https://profiles.wordpress.org/arnoutblueshell/\">arnoutblueshell</a> · <a href=\"https://profiles.wordpress.org/artemiosans/\">Artemio Morales</a> · <a href=\"https://profiles.wordpress.org/mrashishjain/\">Ashish Jain</a> · <a href=\"https://profiles.wordpress.org/mohonchandra/\">Asish Chandra Mohon</a> · <a href=\"https://profiles.wordpress.org/auareyou/\">Au</a> · <a href=\"https://profiles.wordpress.org/autotutorial/\">autotutorial</a> · <a href=\"https://profiles.wordpress.org/ayeshrajans/\">Ayesh Karunaratne</a> · <a href=\"https://profiles.wordpress.org/azharderaiya/\">Azhar Deraiya</a> · <a href=\"https://profiles.wordpress.org/beryldlg/\">B&#233;ryl de La Grandi&#232;re</a> · <a href=\"https://profiles.wordpress.org/back2backdvm/\">back2backdvm</a> · <a href=\"https://profiles.wordpress.org/neo2k23/\">BackuPs</a> · <a href=\"https://profiles.wordpress.org/balub/\">Balu B</a> · <a href=\"https://profiles.wordpress.org/barry/\">Barry</a> · <a href=\"https://profiles.wordpress.org/bayejid00/\">Bayejid Ahmed</a> · <a href=\"https://profiles.wordpress.org/beafialho/\">Beatriz Fialho</a> · <a href=\"https://profiles.wordpress.org/benharri/\">ben</a> · <a href=\"https://profiles.wordpress.org/scruffian/\">Ben Dwyer</a> · <a href=\"https://profiles.wordpress.org/bsutcliffe/\">Ben Sutcliffe</a> · <a href=\"https://profiles.wordpress.org/benazeer/\">Benazeer</a> · <a href=\"https://profiles.wordpress.org/benniledl/\">Benedikt Ledl</a> · <a href=\"https://profiles.wordpress.org/benjamin_zekavica/\">Benjamin Zekavica</a> · <a href=\"https://profiles.wordpress.org/benoitchantre/\">Benoit Chantre</a> · <a href=\"https://profiles.wordpress.org/bernhard-reiter/\">bernhard-reiter</a> · <a href=\"https://profiles.wordpress.org/berislavgrgicak/\">Bero</a> · <a href=\"https://profiles.wordpress.org/bgermann/\">bgermann</a> · <a href=\"https://profiles.wordpress.org/albatross10/\">Bhavik Kalpesh</a> · <a href=\"https://profiles.wordpress.org/bijit027/\">Bijit Deb</a> · <a href=\"https://profiles.wordpress.org/birgire/\">Birgir Erlendsson (birgire)</a> · <a href=\"https://profiles.wordpress.org/bph/\">Birgit Pauli-Haack</a> · <a href=\"https://profiles.wordpress.org/rainynewt/\">Bishal Shrestha</a> · <a href=\"https://profiles.wordpress.org/bjornw/\">BjornW</a> · <a href=\"https://profiles.wordpress.org/bluantinoo/\">bluantinoo</a> · <a href=\"https://profiles.wordpress.org/bogdanungureanu/\">BogdanUngureanu</a> · <a href=\"https://profiles.wordpress.org/bor0/\">Boro Sitnikovski</a> · <a href=\"https://profiles.wordpress.org/veryard/\">Brad</a> · <a href=\"https://profiles.wordpress.org/bjorsch/\">Brad Jorsch</a> · <a href=\"https://profiles.wordpress.org/bradleyt/\">Bradley Taylor</a> · <a href=\"https://profiles.wordpress.org/bhubbard/\">Brandon Hubbard</a> · <a href=\"https://profiles.wordpress.org/bpayton/\">Brandon Payton</a> · <a href=\"https://profiles.wordpress.org/brettsmason/\">brettsmason</a> · <a href=\"https://profiles.wordpress.org/brevilo/\">brevilo</a> · <a href=\"https://profiles.wordpress.org/ironprogrammer/\">Brian Alexander</a> · <a href=\"https://profiles.wordpress.org/bacoords/\">Brian Coords</a> · <a href=\"https://profiles.wordpress.org/masteradhoc/\">Brian Haas</a> · <a href=\"https://profiles.wordpress.org/bronsonquick/\">Bronson Quick</a> · <a href=\"https://profiles.wordpress.org/bschneidewind/\">Bryan Schneidewind</a> · <a href=\"https://profiles.wordpress.org/burakkaptan/\">burak</a> · <a href=\"https://profiles.wordpress.org/burnuser/\">burnuser</a> · <a href=\"https://profiles.wordpress.org/snicco/\">Calvin Alkan</a> · <a href=\"https://profiles.wordpress.org/calvinalkan/\">Calvin Alkan</a> · <a href=\"https://profiles.wordpress.org/cbravobernal/\">Carlos Bravo</a> · <a href=\"https://profiles.wordpress.org/poena/\">Carolina Nymark</a> · <a href=\"https://profiles.wordpress.org/cbirdsong/\">cbirdsong</a> · <a href=\"https://profiles.wordpress.org/cgastrell/\">cgastrell</a> · <a href=\"https://profiles.wordpress.org/charleslf/\">charleslf</a> · <a href=\"https://profiles.wordpress.org/mel_cha/\">chiilog (Chiaki Okamoto)</a> · <a href=\"https://profiles.wordpress.org/chintanmachhi207/\">chintanmachhi207</a> · <a href=\"https://profiles.wordpress.org/chrico/\">ChriCo</a> · <a href=\"https://profiles.wordpress.org/chriscct7/\">chriscct7</a> · <a href=\"https://profiles.wordpress.org/chrisdotdotdot/\">chrisdotdotdot</a> · <a href=\"https://profiles.wordpress.org/apermo/\">Christoph Daum</a> · <a href=\"https://profiles.wordpress.org/claimableperch/\">claimableperch</a> · <a href=\"https://profiles.wordpress.org/claudiulodro/\">Claudiu Lodromanean</a> · <a href=\"https://profiles.wordpress.org/codeamp/\">Code Amp</a> · <a href=\"https://profiles.wordpress.org/codebymikey/\">codebymikey</a> · <a href=\"https://profiles.wordpress.org/codersantosh/\">codersantosh</a> · <a href=\"https://profiles.wordpress.org/costdev/\">Colin Stewart</a> · <a href=\"https://profiles.wordpress.org/colind/\">ColinD</a> · <a href=\"https://profiles.wordpress.org/colinleroy/\">colinleroy</a> · <a href=\"https://profiles.wordpress.org/coreyw/\">Corey Worrell</a> · <a href=\"https://profiles.wordpress.org/courane01/\">Courtney Robertson</a> · <a href=\"https://profiles.wordpress.org/cbusquets1989/\">Cris Busquets</a> · <a href=\"https://profiles.wordpress.org/mitogh/\">Crisoforo Gaspar</a> · <a href=\"https://profiles.wordpress.org/ctienshi/\">ctienshi</a> · <a href=\"https://profiles.wordpress.org/cweiske/\">cweiske</a> · <a href=\"https://profiles.wordpress.org/didierjm/\">cybeardjm</a> · <a href=\"https://profiles.wordpress.org/coquardcyr/\">Cyrille</a> · <a href=\"https://profiles.wordpress.org/css31/\">Cyrille Sanson</a> · <a href=\"https://profiles.wordpress.org/daedalon/\">Daedalon</a> · <a href=\"https://profiles.wordpress.org/acketon/\">Dakota Chichester</a> · <a href=\"https://profiles.wordpress.org/dalleyne/\">Damien Alleyne</a> · <a href=\"https://profiles.wordpress.org/colorful-tones/\">Damon Cook</a> · <a href=\"https://profiles.wordpress.org/dpknauss/\">Dan Knauss</a> · <a href=\"https://profiles.wordpress.org/danht/\">danht</a> · <a href=\"https://profiles.wordpress.org/daniguardiola/\">Dani Guardiola</a> · <a href=\"https://profiles.wordpress.org/danielbachhuber/\">Daniel Bachhuber</a> · <a href=\"https://profiles.wordpress.org/danielpost/\">Daniel Post</a> · <a href=\"https://profiles.wordpress.org/talldanwp/\">Daniel Richards</a> · <a href=\"https://profiles.wordpress.org/mte90/\">Daniele Scasciafratte</a> · <a href=\"https://profiles.wordpress.org/danielpataki/\">danielpataki</a> · <a href=\"https://profiles.wordpress.org/danielvann/\">danielvann</a> · <a href=\"https://profiles.wordpress.org/darerodz/\">darerodz</a> · <a href=\"https://profiles.wordpress.org/dkotter/\">Darin Kotter</a> · <a href=\"https://profiles.wordpress.org/dhewercorus/\">Darren Hewer</a> · <a href=\"https://profiles.wordpress.org/davelo/\">Dave Loodts</a> · <a href=\"https://profiles.wordpress.org/davilera/\">David Aguilera</a> · <a href=\"https://profiles.wordpress.org/darnado/\">David Arnado</a> · <a href=\"https://profiles.wordpress.org/davidbaumwald/\">David Baumwald</a> · <a href=\"https://profiles.wordpress.org/davidbinda/\">David Biňovec</a> · <a href=\"https://profiles.wordpress.org/davidabowman/\">David Bowman</a> · <a href=\"https://profiles.wordpress.org/dpcalhoun/\">David Calhoun</a> · <a href=\"https://profiles.wordpress.org/dafargier/\">David FARGIER</a> · <a href=\"https://profiles.wordpress.org/dlh/\">David Herrera</a> · <a href=\"https://profiles.wordpress.org/david-innes/\">David Innes</a> · <a href=\"https://profiles.wordpress.org/justlevine/\">David Levine</a> · <a href=\"https://profiles.wordpress.org/daviedr/\">David Rozando</a> · <a href=\"https://profiles.wordpress.org/dshanske/\">David Shanske</a> · <a href=\"https://profiles.wordpress.org/get_dave/\">David Smith</a> · <a href=\"https://profiles.wordpress.org/daymobrew/\">daymobrew</a> · <a href=\"https://profiles.wordpress.org/deadduck169/\">deadduck169</a> · <a href=\"https://profiles.wordpress.org/dsas/\">Dean Sas</a> · <a href=\"https://profiles.wordpress.org/dkarfa/\">Debabrata Karfa</a> · <a href=\"https://profiles.wordpress.org/debarghyabanerjee/\">DEBARGHYA BANERJEE</a> · <a href=\"https://profiles.wordpress.org/deeppatel8950/\">Deep Patel</a> · <a href=\"https://profiles.wordpress.org/deepakrohilla/\">Deepak Rohilla</a> · <a href=\"https://profiles.wordpress.org/realloc/\">Dennis Ploetner</a> · <a href=\"https://profiles.wordpress.org/dmsnell/\">Dennis Snell</a> · <a href=\"https://profiles.wordpress.org/densinakum/\">Densi Nakum</a> · <a href=\"https://profiles.wordpress.org/designsimply/\">designsimply</a> · <a href=\"https://profiles.wordpress.org/devansh2002/\">Devansh Chaudhary</a> · <a href=\"https://profiles.wordpress.org/dhananjaykuber/\">Dhananjay Kuber</a> · <a href=\"https://profiles.wordpress.org/dharm1025/\">Dharmesh Patel</a> · <a href=\"https://profiles.wordpress.org/dhavalkapadane/\">Dhaval Kapadane</a> · <a href=\"https://profiles.wordpress.org/dheeraj2995/\">Dheeraj Bhosale</a> · <a href=\"https://profiles.wordpress.org/dhrumilk/\">Dhrumil Kumbhani</a> · <a href=\"https://profiles.wordpress.org/dhruvang21/\">Dhruvang21</a> · <a href=\"https://profiles.wordpress.org/dhruvishah2203/\">Dhruvi Shah</a> · <a href=\"https://profiles.wordpress.org/dhruvik18/\">Dhruvik Malaviya</a> · <a href=\"https://profiles.wordpress.org/diguj/\">Digvijay Zite</a> · <a href=\"https://profiles.wordpress.org/dilipom13/\">dilip</a> · <a href=\"https://profiles.wordpress.org/dilipbheda/\">Dilip Bheda</a> · <a href=\"https://profiles.wordpress.org/diliphingarajiya/\">Dilip Hingarajiya</a> · <a href=\"https://profiles.wordpress.org/dilip2615/\">Dilip Modhavadiya</a> · <a href=\"https://profiles.wordpress.org/dingguodong/\">DingGuodong</a> · <a href=\"https://profiles.wordpress.org/dd32/\">Dion Hulse</a> · <a href=\"https://profiles.wordpress.org/divyeshk71/\">Divyesh_kakrecha</a> · <a href=\"https://profiles.wordpress.org/djcowan/\">dj.cowan</a> · <a href=\"https://profiles.wordpress.org/mediaformat/\">Django</a> · <a href=\"https://profiles.wordpress.org/dnnsjsk/\">dnnsjsk</a> · <a href=\"https://profiles.wordpress.org/doekenorg/\">Doeke Norg</a> · <a href=\"https://profiles.wordpress.org/dogee/\">dogee</a> · <a href=\"https://profiles.wordpress.org/domenicdenicola/\">domenicdenicola</a> · <a href=\"https://profiles.wordpress.org/ocean90/\">Dominik Schilling</a> · <a href=\"https://profiles.wordpress.org/donalirl/\">donalirl</a> · <a href=\"https://profiles.wordpress.org/dooperweb/\">dooperweb</a> · <a href=\"https://profiles.wordpress.org/dougwollison/\">Doug Wollison</a> · <a href=\"https://profiles.wordpress.org/dretzlaff/\">dretzlaff</a> · <a href=\"https://profiles.wordpress.org/drewapicture/\">Drew Jaynes</a> · <a href=\"https://profiles.wordpress.org/drivingralle/\">Drivingralle</a> · <a href=\"https://profiles.wordpress.org/dsawyers/\">dsawyers</a> · <a href=\"https://profiles.wordpress.org/dwainm/\">Dwain Maralack</a> · <a href=\"https://profiles.wordpress.org/earthman100/\">Earthman Media</a> · <a href=\"https://profiles.wordpress.org/eclev91/\">eclev91</a> · <a href=\"https://profiles.wordpress.org/eddystile/\">Eddy</a> · <a href=\"https://profiles.wordpress.org/eduwass/\">eduwass</a> · <a href=\"https://profiles.wordpress.org/cais/\">Edward Caissie</a> · <a href=\"https://profiles.wordpress.org/eceleste/\">efc</a> · <a href=\"https://profiles.wordpress.org/talksina/\">Elena Brescacin</a> · <a href=\"https://profiles.wordpress.org/elizaan36/\">elizaan36</a> · <a href=\"https://profiles.wordpress.org/ellatrix/\">Ella van Durpe</a> · <a href=\"https://profiles.wordpress.org/codex-m/\">Emerson Maningo</a> · <a href=\"https://profiles.wordpress.org/emlebrun/\">Emilie LEBRUN</a> · <a href=\"https://profiles.wordpress.org/eatse/\">Emmanuel Ats&#233;</a> · <a href=\"https://profiles.wordpress.org/lopo/\">Enrico Battocchi</a> · <a href=\"https://profiles.wordpress.org/ericdye/\">Eric Dye</a> · <a href=\"https://profiles.wordpress.org/ethitter/\">Erick Hitter</a> · <a href=\"https://profiles.wordpress.org/codekraft/\">Erik</a> · <a href=\"https://profiles.wordpress.org/kebbet/\">Erik</a> · <a href=\"https://profiles.wordpress.org/im3dabasia1/\">Eshaan Dabasiya</a> · <a href=\"https://profiles.wordpress.org/estelaris/\">Estela Rueda</a> · <a href=\"https://profiles.wordpress.org/eherman24/\">Evan Herman</a> · <a href=\"https://profiles.wordpress.org/etobiesen/\">Even Tobiesen</a> · <a href=\"https://profiles.wordpress.org/fabiankaegy/\">Fabian Kägy</a> · <a href=\"https://profiles.wordpress.org/gaambo/\">Fabian Todt</a> · <a href=\"https://profiles.wordpress.org/faisalahammad/\">Faisal Ahammad</a> · <a href=\"https://profiles.wordpress.org/faisal03/\">Faisal Alvi</a> · <a href=\"https://profiles.wordpress.org/fazyshah/\">Faizan Nabi</a> · <a href=\"https://profiles.wordpress.org/flixos90/\">Felix Arntz</a> · <a href=\"https://profiles.wordpress.org/ffffelix/\">Felix Renicks</a> · <a href=\"https://profiles.wordpress.org/finntown/\">finntown</a> · <a href=\"https://profiles.wordpress.org/firoz2456/\">Firoz Sabaliya</a> · <a href=\"https://profiles.wordpress.org/francina/\">Francesca Marano</a> · <a href=\"https://profiles.wordpress.org/franciscabusas22/\">Francis Cabusas</a> · <a href=\"https://profiles.wordpress.org/fcoveram/\">Francisco</a> · <a href=\"https://profiles.wordpress.org/frankbiganski/\">Frank B.</a> · <a href=\"https://profiles.wordpress.org/frank-klein/\">Frank Klein</a> · <a href=\"https://profiles.wordpress.org/franz00/\">Fransisca H</a> · <a href=\"https://profiles.wordpress.org/frkly/\">frkly</a> · <a href=\"https://profiles.wordpress.org/fushar/\">fushar</a> · <a href=\"https://profiles.wordpress.org/fxbenard/\">FX B&#233;nard</a> · <a href=\"https://profiles.wordpress.org/gaellebesson/\">gaellebesson</a> · <a href=\"https://profiles.wordpress.org/gajendrasingh/\">Gajendra Singh</a> · <a href=\"https://profiles.wordpress.org/galibh/\">Galib Hayder</a> · <a href=\"https://profiles.wordpress.org/ecgan/\">Gan Eng Chin</a> · <a href=\"https://profiles.wordpress.org/garrett-eclipse/\">Garrett Hyder</a> · <a href=\"https://profiles.wordpress.org/garyj/\">Gary Jones</a> · <a href=\"https://profiles.wordpress.org/pento/\">Gary Pendergast</a> · <a href=\"https://profiles.wordpress.org/soulseekah/\">Gennady Kovshenin</a> · <a href=\"https://profiles.wordpress.org/mamaduka/\">George Mamadashvili</a> · <a href=\"https://profiles.wordpress.org/geriux/\">Gerardo Pacheco</a> · <a href=\"https://profiles.wordpress.org/ghinamt/\">ghinamt</a> · <a href=\"https://profiles.wordpress.org/girishpanchal/\">Girish Panchal</a> · <a href=\"https://profiles.wordpress.org/giuriani/\">giuriani</a> · <a href=\"https://profiles.wordpress.org/glendaviesnz/\">Glen Davies</a> · <a href=\"https://profiles.wordpress.org/glynnquelch/\">Glynn Quelch</a> · <a href=\"https://profiles.wordpress.org/ingeniumed/\">Gopal Krishnan</a> · <a href=\"https://profiles.wordpress.org/grantmkin/\">Grant M. Kinney</a> · <a href=\"https://profiles.wordpress.org/gziolo/\">Greg Ziółkowski</a> · <a href=\"https://profiles.wordpress.org/gregbenz/\">gregbenz</a> · <a href=\"https://profiles.wordpress.org/grgarside/\">grgarside</a> · <a href=\"https://profiles.wordpress.org/growwbuddy/\">GrowwBuddy</a> · <a href=\"https://profiles.wordpress.org/guido07111975/\">Guido</a> · <a href=\"https://profiles.wordpress.org/guillaumeturpin/\">Guillaume TURPIN</a> · <a href=\"https://profiles.wordpress.org/gulamdastgir04/\">Gulamdastgir Momin</a> · <a href=\"https://profiles.wordpress.org/priethor/\">H&#233;ctor Prieto</a> · <a href=\"https://profiles.wordpress.org/kabir93/\">H. Kabir</a> · <a href=\"https://profiles.wordpress.org/gencmedya/\">Halil Kaya</a> · <a href=\"https://profiles.wordpress.org/yahaly/\">Halyna Yampolska</a> · <a href=\"https://profiles.wordpress.org/hameem1102/\">Hameem Mahdad Kader</a> · <a href=\"https://profiles.wordpress.org/hanneslsm/\">Hannes Leismann</a> · <a href=\"https://profiles.wordpress.org/hage/\">Hans-Gerd Gerhards</a> · <a href=\"https://profiles.wordpress.org/hardipparmar/\">Hardip Parmar</a> · <a href=\"https://profiles.wordpress.org/hareesh-pillai/\">Hareesh S</a> · <a href=\"https://profiles.wordpress.org/harishanker/\">Hari Shanker R</a> · <a href=\"https://profiles.wordpress.org/harrym/\">harrym</a> · <a href=\"https://profiles.wordpress.org/harshalkadu/\">Harshal Kadu</a> · <a href=\"https://profiles.wordpress.org/hazdiego/\">Haz</a> · <a href=\"https://profiles.wordpress.org/hekuranredev/\">hekuranredev</a> · <a href=\"https://profiles.wordpress.org/helen/\">Helen Hou-Sandi</a> · <a href=\"https://profiles.wordpress.org/hdkothari81/\">Hemant Kothari</a> · <a href=\"https://profiles.wordpress.org/iamarinoh/\">Henrique Iamarino</a> · <a href=\"https://profiles.wordpress.org/henrywright/\">Henry Wright</a> · <a href=\"https://profiles.wordpress.org/rvouill/\">Herve THOMAS</a> · <a href=\"https://profiles.wordpress.org/hideishi/\">hideishi</a> · <a href=\"https://profiles.wordpress.org/kurudrive/\">Hidekazu Ishikawa</a> · <a href=\"https://profiles.wordpress.org/hilayt24/\">Hilay Trivedi</a> · <a href=\"https://profiles.wordpress.org/panchalhimani711/\">Himani Panchal</a> · <a href=\"https://profiles.wordpress.org/abcd95/\">Himanshu Pathak</a> · <a href=\"https://profiles.wordpress.org/hbhalodia/\">Hit Bhalodia</a> · <a href=\"https://profiles.wordpress.org/hitendra-chopda/\">Hitendra Chopda</a> · <a href=\"https://profiles.wordpress.org/hiteshhuptechweb/\">Hitesh Dhokai</a> · <a href=\"https://profiles.wordpress.org/hovhanneshovakimyan/\">Hovhannes Hovakimyan</a> · <a href=\"https://profiles.wordpress.org/hueitan/\">hueitan</a> · <a href=\"https://profiles.wordpress.org/humanify/\">humanify</a> · <a href=\"https://profiles.wordpress.org/hussain896/\">hussain896</a> · <a href=\"https://profiles.wordpress.org/huubl/\">huubl</a> · <a href=\"https://profiles.wordpress.org/huzaifaalmesbah/\">Huzaifa Al Mesbah</a> · <a href=\"https://profiles.wordpress.org/iandunn/\">Ian Dunn</a> · <a href=\"https://profiles.wordpress.org/ilovecats7/\">ilovecats7</a> · <a href=\"https://profiles.wordpress.org/imranhasanraaz/\">Imran</a> · <a href=\"https://profiles.wordpress.org/imranh920/\">Imran Hossain (a11n)</a> · <a href=\"https://profiles.wordpress.org/indirabiswas27/\">Indira Biswas</a> · <a href=\"https://profiles.wordpress.org/indithemes/\">indithemes</a> · <a href=\"https://profiles.wordpress.org/isabel_brison/\">Isabel Brison</a> · <a href=\"https://profiles.wordpress.org/iseulde/\">iseulde</a> · <a href=\"https://profiles.wordpress.org/isuke01/\">Isu</a> · <a href=\"https://profiles.wordpress.org/ivankristianto/\">Ivan Kristianto</a> · <a href=\"https://profiles.wordpress.org/moxie/\">Jack</a> · <a href=\"https://profiles.wordpress.org/jagirbahesh/\">Jagir Bahesh</a> · <a href=\"https://profiles.wordpress.org/jainil07/\">Jainil Shah</a> · <a href=\"https://profiles.wordpress.org/whyisjake/\">Jake Spurlock</a> · <a href=\"https://profiles.wordpress.org/jameskoster/\">James Koster</a> · <a href=\"https://profiles.wordpress.org/jhmonroe/\">James Monroe</a> · <a href=\"https://profiles.wordpress.org/jopdop30/\">James Payne</a> · <a href=\"https://profiles.wordpress.org/imaginarymedia/\">James Robinson</a> · <a href=\"https://profiles.wordpress.org/q0rban/\">James Sansbury</a> · <a href=\"https://profiles.wordpress.org/jamieblomerus/\">Jamie Blomerus</a> · <a href=\"https://profiles.wordpress.org/jammycakes/\">jammycakes</a> · <a href=\"https://profiles.wordpress.org/lysyjan87/\">Jan Lys&#253;</a> · <a href=\"https://profiles.wordpress.org/janak007/\">janak Kaneriya</a> · <a href=\"https://profiles.wordpress.org/janusdev/\">janusdev</a> · <a href=\"https://profiles.wordpress.org/jsnajdr/\">Jarda Snajdr</a> · <a href=\"https://profiles.wordpress.org/jarekmorawski/\">jarekmorawski</a> · <a href=\"https://profiles.wordpress.org/boogah/\">Jason Cosper</a> · <a href=\"https://profiles.wordpress.org/madtownlems/\">Jason LeMahieu (MadtownLems)</a> · <a href=\"https://profiles.wordpress.org/jasonsa19/\">Jason Sauerwald</a> · <a href=\"https://profiles.wordpress.org/javiercasares/\">Javier Casares</a> · <a href=\"https://profiles.wordpress.org/shovan_jaya/\">Jayaram</a> · <a href=\"https://profiles.wordpress.org/jdahir0789/\">Jaydip Ahir</a> · <a href=\"https://profiles.wordpress.org/jdnd/\">jdnd</a> · <a href=\"https://profiles.wordpress.org/audrasjb/\">Jean-Baptiste Audras</a> · <a href=\"https://profiles.wordpress.org/krokodok/\">Jeff Chi</a> · <a href=\"https://profiles.wordpress.org/maverick3x6/\">Jeff Golenski</a> · <a href=\"https://profiles.wordpress.org/jffng/\">Jeff Ong</a> · <a href=\"https://profiles.wordpress.org/jeffpaul/\">Jeff Paul</a> · <a href=\"https://profiles.wordpress.org/jeffr0/\">Jeffro</a> · <a href=\"https://profiles.wordpress.org/jeflopodev/\">jeflopo</a> · <a href=\"https://profiles.wordpress.org/jdy68/\">Jenny Dupuy</a> · <a href=\"https://profiles.wordpress.org/jepperask/\">jepperask</a> · <a href=\"https://profiles.wordpress.org/jeremyfelt/\">Jeremy Felt</a> · <a href=\"https://profiles.wordpress.org/jeremy80/\">jeremy80</a> · <a href=\"https://profiles.wordpress.org/jeremyroman/\">jeremyroman</a> · <a href=\"https://profiles.wordpress.org/jeryj/\">jeryj</a> · <a href=\"https://profiles.wordpress.org/jessedyck/\">jessedyck</a> · <a href=\"https://profiles.wordpress.org/luminuu/\">Jessica Lyschik</a> · <a href=\"https://profiles.wordpress.org/jigar-bhanushali/\">Jigar Bhanushali</a> · <a href=\"https://profiles.wordpress.org/jigar9998/\">Jigar Panchal</a> · <a href=\"https://profiles.wordpress.org/jnweaver/\">jnweaver</a> · <a href=\"https://profiles.wordpress.org/jartes/\">Joan Artes</a> · <a href=\"https://profiles.wordpress.org/jodamo5/\">jodamo5</a> · <a href=\"https://profiles.wordpress.org/joedolson/\">Joe Dolson</a> · <a href=\"https://profiles.wordpress.org/joehoyle/\">Joe Hoyle</a> · <a href=\"https://profiles.wordpress.org/joemcgill/\">Joe McGill</a> · <a href=\"https://profiles.wordpress.org/joecsmalley/\">joecsmalley</a> · <a href=\"https://profiles.wordpress.org/joen/\">Joen Asmussen</a> · <a href=\"https://profiles.wordpress.org/timse201/\">Johannes J&#252;lg</a> · <a href=\"https://profiles.wordpress.org/johnbillion/\">John Blackbourn</a> · <a href=\"https://profiles.wordpress.org/johnjamesjacoby/\">John James Jacoby</a> · <a href=\"https://profiles.wordpress.org/johnregan3/\">John Regan</a> · <a href=\"https://profiles.wordpress.org/arrcorona/\">Jomar Jay Corona</a> · <a href=\"https://profiles.wordpress.org/jomonthomaslobo1/\">Jomon Thomas Lobo</a> · <a href=\"https://profiles.wordpress.org/jonsurrell/\">Jon Surrell</a> · <a href=\"https://profiles.wordpress.org/psykro/\">Jonathan Bossenger</a> · <a href=\"https://profiles.wordpress.org/desrosj/\">Jonathan Desrosiers</a> · <a href=\"https://profiles.wordpress.org/jongycastillo/\">Jong</a> · <a href=\"https://profiles.wordpress.org/n2erjo00/\">Joni Erkkil&#228;</a> · <a href=\"https://profiles.wordpress.org/spacedmonkey/\">Jonny Harris</a> · <a href=\"https://profiles.wordpress.org/jonnyauk/\">jonnyauk</a> · <a href=\"https://profiles.wordpress.org/joomskys/\">joomskys</a> · <a href=\"https://profiles.wordpress.org/joostdevalk/\">Joost de Valk</a> · <a href=\"https://profiles.wordpress.org/jordesign/\">jordesign</a> · <a href=\"https://profiles.wordpress.org/jorgefilipecosta/\">Jorge Costa</a> · <a href=\"https://profiles.wordpress.org/josvelasco/\">Jos Velasco</a> · <a href=\"https://profiles.wordpress.org/josevarghese/\">Jose Varghese</a> · <a href=\"https://profiles.wordpress.org/blogitsolutions/\">Josef Seidl</a> · <a href=\"https://profiles.wordpress.org/josephscott/\">Joseph Scott</a> · <a href=\"https://profiles.wordpress.org/verygoode/\">Joshua Goode</a> · <a href=\"https://profiles.wordpress.org/joshuawold/\">Joshua Wold</a> · <a href=\"https://profiles.wordpress.org/jottevanger/\">jottevanger</a> · <a href=\"https://profiles.wordpress.org/jokanane/\">Jouni Kananen</a> · <a href=\"https://profiles.wordpress.org/joyously/\">Joy</a> · <a href=\"https://profiles.wordpress.org/juanfra/\">Juan Aldasoro</a> · <a href=\"https://profiles.wordpress.org/juanbuis/\">juanbuis</a> · <a href=\"https://profiles.wordpress.org/juanmaguitar/\">JuanMa Garrido</a> · <a href=\"https://profiles.wordpress.org/juanwp22/\">juanwp22</a> · <a href=\"https://profiles.wordpress.org/jules-colle/\">Jules Colle</a> · <a href=\"https://profiles.wordpress.org/julianmar/\">julianmar</a> · <a href=\"https://profiles.wordpress.org/juliemoynat/\">Julie Moynat</a> · <a href=\"https://profiles.wordpress.org/jrf/\">Juliette Reinders Folmer</a> · <a href=\"https://profiles.wordpress.org/juliobox/\">Julio Potier</a> · <a href=\"https://profiles.wordpress.org/greenshady/\">Justin Tadlock</a> · <a href=\"https://profiles.wordpress.org/juzar/\">Juzar</a> · <a href=\"https://profiles.wordpress.org/kadamwhite/\">K. Adam White</a> · <a href=\"https://profiles.wordpress.org/kafleg/\">KafleG</a> · <a href=\"https://profiles.wordpress.org/kevin940726/\">Kai Hao</a> · <a href=\"https://profiles.wordpress.org/kaitohm/\">Kaito Hanamori</a> · <a href=\"https://profiles.wordpress.org/ikamal/\">Kamal Hosen</a> · <a href=\"https://profiles.wordpress.org/kapasias/\">KAP ASIAs</a> · <a href=\"https://profiles.wordpress.org/kapilpaul/\">Kapil Paul</a> · <a href=\"https://profiles.wordpress.org/karlgroves/\">karlgroves</a> · <a href=\"https://profiles.wordpress.org/karolmanijak/\">Karol Manijak</a> · <a href=\"https://profiles.wordpress.org/karthickmurugan/\">Karthick Murugan</a> · <a href=\"https://profiles.wordpress.org/thekt12/\">Karthik Thayyil</a> · <a href=\"https://profiles.wordpress.org/karthikeya01/\">Karthikeya Bethu</a> · <a href=\"https://profiles.wordpress.org/kasparsd/\">Kaspars</a> · <a href=\"https://profiles.wordpress.org/zoonini/\">Kathryn Presner</a> · <a href=\"https://profiles.wordpress.org/kausaralm/\">Kausar Alam</a> · <a href=\"https://profiles.wordpress.org/keithdevon/\">keithdevon</a> · <a href=\"https://profiles.wordpress.org/kel-dc/\">Kel Santiago-Pilarski</a> · <a href=\"https://profiles.wordpress.org/ryelle/\">Kelly Choyce-Dwan</a> · <a href=\"https://profiles.wordpress.org/tinypixel/\">Kelly Mears</a> · <a href=\"https://profiles.wordpress.org/keoshi/\">keoshi</a> · <a href=\"https://profiles.wordpress.org/ketanniruke/\">Ketan Niruke</a> · <a href=\"https://profiles.wordpress.org/patelketan/\">Ketan Patel</a> · <a href=\"https://profiles.wordpress.org/kevinlearynet/\">Kevin Leary</a> · <a href=\"https://profiles.wordpress.org/kharisblank/\">Kharis Sulistiyono</a> · <a href=\"https://profiles.wordpress.org/iamkingsleyf/\">Kingsley Felix</a> · <a href=\"https://profiles.wordpress.org/kirasong/\">Kira Schroder</a> · <a href=\"https://profiles.wordpress.org/kjroelke/\">KJ Roelke</a> · <a href=\"https://profiles.wordpress.org/kkmuffme/\">kkmuffme</a> · <a href=\"https://profiles.wordpress.org/knutsp/\">Knut Sparhell</a> · <a href=\"https://profiles.wordpress.org/kohheepeace/\">kohheepeace</a> · <a href=\"https://profiles.wordpress.org/obenland/\">Konstantin Obenland</a> · <a href=\"https://profiles.wordpress.org/kmgalanakis/\">Konstantinos Galanakis</a> · <a href=\"https://profiles.wordpress.org/xkon/\">Konstantinos Xenos</a> · <a href=\"https://profiles.wordpress.org/kristastevens/\">kristastevens</a> · <a href=\"https://profiles.wordpress.org/krupajnanda/\">Krupa Nanda</a> · <a href=\"https://profiles.wordpress.org/krupaly2k/\">Krupal Lakhia</a> · <a href=\"https://profiles.wordpress.org/laurelfulford/\">laurelfulford</a> · <a href=\"https://profiles.wordpress.org/lakshmananphp/\">Lax Mariappan</a> · <a href=\"https://profiles.wordpress.org/laxman-prajapati/\">Laxman Prajapati</a> · <a href=\"https://profiles.wordpress.org/lcarevic/\">lcarevic</a> · <a href=\"https://profiles.wordpress.org/leecollings/\">Lee</a> · <a href=\"https://profiles.wordpress.org/leewillis77/\">Lee Willis</a> · <a href=\"https://profiles.wordpress.org/leedxw/\">leedxw</a> · <a href=\"https://profiles.wordpress.org/0mirka00/\">Lena Morita</a> · <a href=\"https://profiles.wordpress.org/lenasterg/\">lenasterg</a> · <a href=\"https://profiles.wordpress.org/leszeks/\">leszeks</a> · <a href=\"https://profiles.wordpress.org/leup/\">leup</a> · <a href=\"https://profiles.wordpress.org/levskipg/\">levskipg</a> · <a href=\"https://profiles.wordpress.org/lgladdy/\">Liam Gladdy</a> · <a href=\"https://profiles.wordpress.org/lilgames/\">LilGames</a> · <a href=\"https://profiles.wordpress.org/louislaugesen/\">Louis</a> · <a href=\"https://profiles.wordpress.org/louwie17/\">Lourens</a> · <a href=\"https://profiles.wordpress.org/lovesoni1999/\">Love Soni</a> · <a href=\"https://profiles.wordpress.org/thelovekesh/\">Lovekesh Kumar</a> · <a href=\"https://profiles.wordpress.org/lovewpmu/\">lovewpmu</a> · <a href=\"https://profiles.wordpress.org/lovor/\">Lovro Hrust</a> · <a href=\"https://profiles.wordpress.org/dreadlox/\">Lox</a> · <a href=\"https://profiles.wordpress.org/ltrihan/\">ltrihan</a> · <a href=\"https://profiles.wordpress.org/gigitux/\">Luigi Teschio</a> · <a href=\"https://profiles.wordpress.org/zaguiini/\">Luis Felipe Zaguini</a> · <a href=\"https://profiles.wordpress.org/luisherranz/\">luisherranz</a> · <a href=\"https://profiles.wordpress.org/wpfed/\">Lukasz</a> · <a href=\"https://profiles.wordpress.org/lukecavanagh/\">Luke Cavanagh</a> · <a href=\"https://profiles.wordpress.org/gadelhas/\">M&#225;rio Santos</a> · <a href=\"https://profiles.wordpress.org/maciejmackowiak/\">maciejmackowiak</a> · <a href=\"https://profiles.wordpress.org/madhudollu/\">Madhu Dollu</a> · <a href=\"https://profiles.wordpress.org/madpeter/\">madpeter</a> · <a href=\"https://profiles.wordpress.org/magdarogier/\">Magda Rogier</a> · <a href=\"https://profiles.wordpress.org/onemaggie/\">Maggie Cabrera</a> · <a href=\"https://profiles.wordpress.org/mp518/\">Mahesh Prajapati</a> · <a href=\"https://profiles.wordpress.org/mai21/\">Mai</a> · <a href=\"https://profiles.wordpress.org/maikelraow/\">maikelraow</a> · <a href=\"https://profiles.wordpress.org/travel_girl/\">Maja Benke</a> · <a href=\"https://profiles.wordpress.org/mkrndmane/\">Makarand G. Mane</a> · <a href=\"https://profiles.wordpress.org/tomdxw/\">mallorydxw-old</a> · <a href=\"https://profiles.wordpress.org/manfcarlo/\">manfcarlo</a> · <a href=\"https://profiles.wordpress.org/manojmaharrshi/\">Manoj Maharrshi</a> · <a href=\"https://profiles.wordpress.org/manzoorwanijk/\">Manzoor Wani</a> · <a href=\"https://profiles.wordpress.org/mdxfr/\">Marc</a> · <a href=\"https://profiles.wordpress.org/fullofcaffeine/\">Marcelo de Moraes Serpa</a> · <a href=\"https://profiles.wordpress.org/marchalyoan/\">marchalyoan</a> · <a href=\"https://profiles.wordpress.org/afmarchetti/\">Marchetti Design</a> · <a href=\"https://profiles.wordpress.org/mciampini/\">Marco Ciampini</a> · <a href=\"https://profiles.wordpress.org/mariayohana/\">Maria Yohana</a> · <a href=\"https://profiles.wordpress.org/marian1/\">marian1</a> · <a href=\"https://profiles.wordpress.org/nuryko/\">Marianna</a> · <a href=\"https://profiles.wordpress.org/plari/\">Marie</a> · <a href=\"https://profiles.wordpress.org/tyxla/\">Marin Atanasov</a> · <a href=\"https://profiles.wordpress.org/marineevain/\">Marine EVAIN</a> · <a href=\"https://profiles.wordpress.org/nofearinc/\">Mario Peshev</a> · <a href=\"https://profiles.wordpress.org/santosguillamot/\">Mario Santos</a> · <a href=\"https://profiles.wordpress.org/clorith/\">Marius L. J.</a> · <a href=\"https://profiles.wordpress.org/markhowellsmead/\">Mark Howells-Mead</a> · <a href=\"https://profiles.wordpress.org/mapk/\">Mark Uraine</a> · <a href=\"https://profiles.wordpress.org/mark-k/\">Mark-k</a> · <a href=\"https://profiles.wordpress.org/martinkrcho/\">martin.krcho</a> · <a href=\"https://profiles.wordpress.org/marybaum/\">Mary Baum</a> · <a href=\"https://profiles.wordpress.org/4thhubbard/\">Mary Hubbard</a> · <a href=\"https://profiles.wordpress.org/immasud/\">Masud Rana</a> · <a href=\"https://profiles.wordpress.org/paapst/\">Mathieu Paapst</a> · <a href=\"https://profiles.wordpress.org/mmaattiiaass/\">Matias Benedetto</a> · <a href=\"https://profiles.wordpress.org/matveb/\">Matias Ventura</a> · <a href=\"https://profiles.wordpress.org/iammattthomas/\">Matt (Thomas) Miklic</a> · <a href=\"https://profiles.wordpress.org/matt/\">Matt Mullenweg</a> · <a href=\"https://profiles.wordpress.org/mattyrob/\">Matt Robinson</a> · <a href=\"https://profiles.wordpress.org/mattwest/\">Matt West</a> · <a href=\"https://profiles.wordpress.org/matteoenna/\">Matteo Enna</a> · <a href=\"https://profiles.wordpress.org/kinggmobb/\">Matthew</a> · <a href=\"https://profiles.wordpress.org/mboynes/\">Matthew Boynes</a> · <a href=\"https://profiles.wordpress.org/mattryanwalker/\">mattryanwalker</a> · <a href=\"https://profiles.wordpress.org/azouamauriac/\">Mauriac AZOUA</a> · <a href=\"https://profiles.wordpress.org/maximemeganck/\">Maxime Meganck</a> · <a href=\"https://profiles.wordpress.org/maximebj/\">maximebj</a> · <a href=\"https://profiles.wordpress.org/mayanktripathi32/\">Mayank Tripathi</a> · <a href=\"https://profiles.wordpress.org/mayur8991/\">Mayur Prajapati</a> · <a href=\"https://profiles.wordpress.org/faithcoder/\">Md Abdullah Al Arif</a> · <a href=\"https://profiles.wordpress.org/hmbashar/\">Md Abul Bashar</a> · <a href=\"https://profiles.wordpress.org/itsmhrahman/\">Md Hafijur Rahman</a> · <a href=\"https://profiles.wordpress.org/kawsar007/\">MD Kawsar Chowdhury</a> · <a href=\"https://profiles.wordpress.org/mehdi01/\">Md Mahdi Hasan</a> · <a href=\"https://profiles.wordpress.org/mdibrahimk48/\">Md. Ibrahim Khalil</a> · <a href=\"https://profiles.wordpress.org/najmulsaju/\">Md. Najmul Islam</a> · <a href=\"https://profiles.wordpress.org/mehedi_csit/\">Md.Mehedi Hasan</a> · <a href=\"https://profiles.wordpress.org/mdviralsampat/\">mdviralsampat</a> · <a href=\"https://profiles.wordpress.org/megane9988/\">megane9988</a> · <a href=\"https://profiles.wordpress.org/leeseoftbc/\">MelissaH</a> · <a href=\"https://profiles.wordpress.org/metropolis_john/\">metropolis_john</a> · <a href=\"https://profiles.wordpress.org/mdawaffe/\">Michael Adams (mdawaffe)</a> · <a href=\"https://profiles.wordpress.org/mburridge/\">Michael Burridge</a> · <a href=\"https://profiles.wordpress.org/websiteredev/\">Michael Willman</a> · <a href=\"https://profiles.wordpress.org/czapla/\">Michal Czaplinski</a> · <a href=\"https://profiles.wordpress.org/michelleames/\">Michelle Frechette</a> · <a href=\"https://profiles.wordpress.org/marktimemedia/\">Michelle Schulp Hunt</a> · <a href=\"https://profiles.wordpress.org/micromadness/\">micromadness</a> · <a href=\"https://profiles.wordpress.org/mcsf/\">Miguel Fonseca</a> · <a href=\"https://profiles.wordpress.org/mmtr86/\">Miguel Torres</a> · <a href=\"https://profiles.wordpress.org/mijotj/\">Mijo T J</a> · <a href=\"https://profiles.wordpress.org/simison/\">Mikael Korpela</a> · <a href=\"https://profiles.wordpress.org/mbijon/\">Mike Bijon</a> · <a href=\"https://profiles.wordpress.org/ritterml/\">Mike Ritter</a> · <a href=\"https://profiles.wordpress.org/mikinc860/\">Mikin Chauhan</a> · <a href=\"https://profiles.wordpress.org/milana_cap/\">Milana Cap</a> · <a href=\"https://profiles.wordpress.org/milindmore22/\">Milind More</a> · <a href=\"https://profiles.wordpress.org/mimitips/\">mimi</a> · <a href=\"https://profiles.wordpress.org/369work/\">miroku</a> · <a href=\"https://profiles.wordpress.org/presstoke/\">Mitchell Austin</a> · <a href=\"https://profiles.wordpress.org/modi2918/\">Modi Sahil</a> · <a href=\"https://profiles.wordpress.org/antwortzeit/\">modulbuero</a> · <a href=\"https://profiles.wordpress.org/aboelfotoh/\">Mohamed Aboelfotoh Mohamed</a> · <a href=\"https://profiles.wordpress.org/mohitdadhich10/\">Mohit Dadhich</a> · <a href=\"https://profiles.wordpress.org/mojorob/\">mojorob</a> · <a href=\"https://profiles.wordpress.org/jucaduca/\">Morais Junior</a> · <a href=\"https://profiles.wordpress.org/mosescursor/\">Moses Cursor Ssebunya</a> · <a href=\"https://profiles.wordpress.org/mosne/\">Mosne / Paolo Tesei</a> · <a href=\"https://profiles.wordpress.org/mr660/\">mr660</a> · <a href=\"https://profiles.wordpress.org/mreishus/\">mreishus</a> · <a href=\"https://profiles.wordpress.org/mrunalkulkarni/\">mrunalkulkarni</a> · <a href=\"https://profiles.wordpress.org/mrwweb/\">mrwweb</a> · <a href=\"https://profiles.wordpress.org/mtdkei/\">mt</a> · <a href=\"https://profiles.wordpress.org/muddassirnasim/\">Muddassir Rahman Nasim</a> · <a href=\"https://profiles.wordpress.org/devmuhib/\">Muhibul Haque</a> · <a href=\"https://profiles.wordpress.org/mujuonly/\">mujuonly</a> · <a href=\"https://profiles.wordpress.org/mukesh27/\">Mukesh Panchal</a> · <a href=\"https://profiles.wordpress.org/mukulsingh27/\">Mukul Singh</a> · <a href=\"https://profiles.wordpress.org/my1xt/\">My1</a> · <a href=\"https://profiles.wordpress.org/n8finch/\">n8finch</a> · <a href=\"https://profiles.wordpress.org/assassinateur/\">Nadir Seghir</a> · <a href=\"https://profiles.wordpress.org/nandow/\">nandow</a> · <a href=\"https://profiles.wordpress.org/nanisamireddy/\">NANI SAMIREDDY</a> · <a href=\"https://profiles.wordpress.org/narenin/\">Narendra Sishodiya</a> · <a href=\"https://profiles.wordpress.org/nareshbheda/\">Naresh Bheda</a> · <a href=\"https://profiles.wordpress.org/nataliat2004/\">Natalia T</a> · <a href=\"https://profiles.wordpress.org/nathanatmoz/\">Nathan Johnson</a> · <a href=\"https://profiles.wordpress.org/navneet/\">navneet</a> · <a href=\"https://profiles.wordpress.org/navi161/\">Navneet Kaur</a> · <a href=\"https://profiles.wordpress.org/nhrrob/\">Nazmul Hasan Robin</a> · <a href=\"https://profiles.wordpress.org/greatislander/\">Ned Zimmerman</a> · <a href=\"https://profiles.wordpress.org/neotrope/\">neotrope</a> · <a href=\"https://profiles.wordpress.org/mcqueen22/\">NerQuiles</a> · <a href=\"https://profiles.wordpress.org/krstarica/\">net</a> · <a href=\"https://profiles.wordpress.org/ndiego/\">Nick Diego</a> · <a href=\"https://profiles.wordpress.org/nickgalvez/\">nickgalvez</a> · <a href=\"https://profiles.wordpress.org/nickwilmot/\">nickwilmot</a> · <a href=\"https://profiles.wordpress.org/undefinedfr/\">Nicolas RIVIERE</a> · <a href=\"https://profiles.wordpress.org/nicolasleroy/\">nicolasleroy</a> · <a href=\"https://profiles.wordpress.org/nidhidhandhukiya/\">nidhidhandhukiya</a> · <a href=\"https://profiles.wordpress.org/ntsekouras/\">Nik Tsekouras</a> · <a href=\"https://profiles.wordpress.org/smushytaco/\">Nikan Radan</a> · <a href=\"https://profiles.wordpress.org/nikitasolanki1812/\">Nikita Solanki</a> · <a href=\"https://profiles.wordpress.org/nikunj8866/\">Nikunj Hatkar</a> · <a href=\"https://profiles.wordpress.org/snilesh/\">Nilesh Shiragave</a> · <a href=\"https://profiles.wordpress.org/nirajgirixd/\">Niraj Giri</a> · <a href=\"https://profiles.wordpress.org/nithins53/\">Nithin SreeRaj</a> · <a href=\"https://profiles.wordpress.org/mrtortai/\">Noam Eppel</a> · <a href=\"https://profiles.wordpress.org/othernoel/\">Noel Santos</a> · <a href=\"https://profiles.wordpress.org/noruzzaman/\">Noruzzaman</a> · <a href=\"https://profiles.wordpress.org/nosilver4u/\">nosilver4u</a> · <a href=\"https://profiles.wordpress.org/oceantober/\">oceantober</a> · <a href=\"https://profiles.wordpress.org/finalwebsites/\">Olaf Lederer</a> · <a href=\"https://profiles.wordpress.org/oglekler/\">Olga Gleckler</a> · <a href=\"https://profiles.wordpress.org/domainsupport/\">Oliver Campion</a> · <a href=\"https://profiles.wordpress.org/jbkkd/\">Omer Korner</a> · <a href=\"https://profiles.wordpress.org/arzola/\">Oscar Arzola</a> · <a href=\"https://profiles.wordpress.org/groenroos/\">Oskari Groenroos</a> · <a href=\"https://profiles.wordpress.org/ov3rfly/\">Ov3rfly</a> · <a href=\"https://profiles.wordpress.org/paaljoachim/\">Paal Joachim Romdahl</a> · <a href=\"https://profiles.wordpress.org/pampfelimetten/\">pampfelimetten</a> · <a href=\"https://profiles.wordpress.org/parinpanjari/\">Parin Panjari</a> · <a href=\"https://profiles.wordpress.org/dparthj/\">Parth Dodiya</a> · <a href=\"https://profiles.wordpress.org/parthvataliya/\">Parth vataliya</a> · <a href=\"https://profiles.wordpress.org/swissspidy/\">Pascal Birchler</a> · <a href=\"https://profiles.wordpress.org/casiepa/\">Pascal Casier</a> · <a href=\"https://profiles.wordpress.org/pateljaymin/\">Patel Jaymin</a> · <a href=\"https://profiles.wordpress.org/patricia70/\">Patricia BT</a> · <a href=\"https://profiles.wordpress.org/lumiblog/\">Patrick Lumumba</a> · <a href=\"https://profiles.wordpress.org/eartboard/\">Paul</a> · <a href=\"https://profiles.wordpress.org/pbearne/\">Paul Bearne</a> · <a href=\"https://profiles.wordpress.org/pbiron/\">Paul Biron</a> · <a href=\"https://profiles.wordpress.org/bbpaule/\">Paul English</a> · <a href=\"https://profiles.wordpress.org/paulkevan/\">Paul Kevan</a> · <a href=\"https://profiles.wordpress.org/paullb/\">paullb</a> · <a href=\"https://profiles.wordpress.org/psrpinto/\">Paulo Pinto</a> · <a href=\"https://profiles.wordpress.org/paulopmt1/\">Paulo Trentin</a> · <a href=\"https://profiles.wordpress.org/pavanpatil1/\">Pavan Patil</a> · <a href=\"https://profiles.wordpress.org/creadordev/\">Pawan Kumar</a> · <a href=\"https://profiles.wordpress.org/pedromendonca/\">Pedro Mendon&#231;a</a> · <a href=\"https://profiles.wordpress.org/provenself/\">Peter Rubin</a> · <a href=\"https://profiles.wordpress.org/westi/\">Peter Westwood</a> · <a href=\"https://profiles.wordpress.org/peterwilsoncc/\">Peter Wilson</a> · <a href=\"https://profiles.wordpress.org/peter8nss/\">peter8nss</a> · <a href=\"https://profiles.wordpress.org/peterdavehello/\">peterdavehello</a> · <a href=\"https://profiles.wordpress.org/petitphp/\">petitphp</a> · <a href=\"https://profiles.wordpress.org/philliproth/\">philliproth</a> · <a href=\"https://profiles.wordpress.org/philwebs/\">philwebs</a> · <a href=\"https://profiles.wordpress.org/pitamdey/\">Pitam Dey</a> · <a href=\"https://profiles.wordpress.org/pixlpirate/\">pixlpirate</a> · <a href=\"https://profiles.wordpress.org/podpirate/\">podpirate</a> · <a href=\"https://profiles.wordpress.org/poojabhimani/\">Pooja Bhimani</a> · <a href=\"https://profiles.wordpress.org/pooja1210/\">Pooja Killekar (Muchandikar)</a> · <a href=\"https://profiles.wordpress.org/pooja9712/\">pooja9712</a> · <a href=\"https://profiles.wordpress.org/praful2111/\">Praful Patel</a> · <a href=\"https://profiles.wordpress.org/prasadkarmalkar/\">Prasad Karmalkar</a> · <a href=\"https://profiles.wordpress.org/prashant/\">prashant</a> · <a href=\"https://profiles.wordpress.org/pmbaldha/\">Prashant Baldha</a> · <a href=\"https://profiles.wordpress.org/contactprashantpp/\">Prashant Patil</a> · <a href=\"https://profiles.wordpress.org/prathamesh24/\">Prathamesh Shirke</a> · <a href=\"https://profiles.wordpress.org/prathameshbhagat1511/\">prathameshbhagat1511</a> · <a href=\"https://profiles.wordpress.org/pkbhatt/\">Pratik Bhatt</a> · <a href=\"https://profiles.wordpress.org/pratikgandhi/\">Pratik Gandhi</a> · <a href=\"https://profiles.wordpress.org/pratiklondhe/\">Pratik Londhe</a> · <a href=\"https://profiles.wordpress.org/presskopp/\">Presskopp</a> · <a href=\"https://profiles.wordpress.org/digitalpritam/\">Pritam Sonone</a> · <a href=\"https://profiles.wordpress.org/priyank9033/\">Priyank Vadhavana</a> · <a href=\"https://profiles.wordpress.org/pushpenderindia/\">Pushpender Singh</a> · <a href=\"https://profiles.wordpress.org/itsjonq/\">Q</a> · <a href=\"https://profiles.wordpress.org/qhaensler/\">qhaensler</a> · <a href=\"https://profiles.wordpress.org/rfischmann/\">Rafael Fischmann</a> · <a href=\"https://profiles.wordpress.org/rahulsprajapati/\">Rahul Prajapati</a> · <a href=\"https://profiles.wordpress.org/imrraaj/\">Raj Patel</a> · <a href=\"https://profiles.wordpress.org/rajat1192/\">Rajat Patel</a> · <a href=\"https://profiles.wordpress.org/raj198/\">Rajendra Patel</a> · <a href=\"https://profiles.wordpress.org/rajendrapatilraj/\">Rajendra Patil</a> · <a href=\"https://profiles.wordpress.org/rkradadiya/\">Rajesh Radadiya</a> · <a href=\"https://profiles.wordpress.org/rajeshrathodwp/\">Rajesh Rathod</a> · <a href=\"https://profiles.wordpress.org/rajinsharwar/\">Rajin Sharwar</a> · <a href=\"https://profiles.wordpress.org/rahmohn/\">Ramon Ahnert</a> · <a href=\"https://profiles.wordpress.org/rcorrales/\">Ramon Corrales</a> · <a href=\"https://profiles.wordpress.org/ramonopoly/\">Ramon James</a> · <a href=\"https://profiles.wordpress.org/ravigadhiyawp/\">Ravi Gadhiya</a> · <a href=\"https://profiles.wordpress.org/redkite/\">redkite</a> · <a href=\"https://profiles.wordpress.org/rehanali/\">Rehan Ali</a> · <a href=\"https://profiles.wordpress.org/rejaulalomkhan/\">Rejaul Alom Khan</a> · <a href=\"https://profiles.wordpress.org/rembem/\">Remco</a> · <a href=\"https://profiles.wordpress.org/renathoc/\">Renatho</a> · <a href=\"https://profiles.wordpress.org/ooraiser69/\">Renz Jay Sanchez</a> · <a href=\"https://profiles.wordpress.org/youknowriad/\">Riad Benguella</a> · <a href=\"https://profiles.wordpress.org/richtabor/\">Rich Tabor</a> · <a href=\"https://profiles.wordpress.org/riddhidave/\">Riddhi Dave</a> · <a href=\"https://profiles.wordpress.org/rinkalpagdar/\">Rinkal Pagdar</a> · <a href=\"https://profiles.wordpress.org/rishavdutta/\">Rishav Dutta</a> · <a href=\"https://profiles.wordpress.org/rcreators/\">Rishi Mehta</a> · <a href=\"https://profiles.wordpress.org/rishishah/\">Rishi Shah</a> · <a href=\"https://profiles.wordpress.org/rishit30g/\">Rishit Gupta</a> · <a href=\"https://profiles.wordpress.org/rkyburz/\">rkyburz</a> · <a href=\"https://profiles.wordpress.org/noisysocks/\">Robert Anderson</a> · <a href=\"https://profiles.wordpress.org/miqrogroove/\">Robert Chapin</a> · <a href=\"https://profiles.wordpress.org/robertghetau/\">Robert Ghetau</a> · <a href=\"https://profiles.wordpress.org/harmr/\">Robert Seyfriedsberger</a> · <a href=\"https://profiles.wordpress.org/robertstaddon/\">robertstaddon</a> · <a href=\"https://profiles.wordpress.org/robinmartijn/\">Robin Martijn</a> · <a href=\"https://profiles.wordpress.org/kreppar/\">Rodrigo Arias</a> · <a href=\"https://profiles.wordpress.org/rodrigosprimo/\">Rodrigo Primo</a> · <a href=\"https://profiles.wordpress.org/rogierlankhorst/\">Rogier Lankhorst</a> · <a href=\"https://profiles.wordpress.org/geekofshire/\">Rohan Jha</a> · <a href=\"https://profiles.wordpress.org/rohitmathur7/\">rohitmathur7</a> · <a href=\"https://profiles.wordpress.org/rohjay/\">rohjay</a> · <a href=\"https://profiles.wordpress.org/room34/\">room34</a> · <a href=\"https://profiles.wordpress.org/rotemg/\">Rotem Gelbart</a> · <a href=\"https://profiles.wordpress.org/lev0/\">Roy Orbitson</a> · <a href=\"https://profiles.wordpress.org/roytanck/\">Roy Tanck</a> · <a href=\"https://profiles.wordpress.org/roybellingan/\">roybellingan</a> · <a href=\"https://profiles.wordpress.org/ruchirj/\">Ruchir Goswami</a> · <a href=\"https://profiles.wordpress.org/rudrakshigupta/\">Rudrakshi Gupta</a> · <a href=\"https://profiles.wordpress.org/rupesh5438/\">Rupesh Patil</a> · <a href=\"https://profiles.wordpress.org/rupw/\">rupw</a> · <a href=\"https://profiles.wordpress.org/ruturajraval2305/\">Ruturaj Raval</a> · <a href=\"https://profiles.wordpress.org/rvoigt/\">rvoigt</a> · <a href=\"https://profiles.wordpress.org/bookdude13/\">Ryan Fredlund</a> · <a href=\"https://profiles.wordpress.org/ryanhellyer/\">Ryan Hellyer</a> · <a href=\"https://profiles.wordpress.org/rmccue/\">Ryan McCue</a> · <a href=\"https://profiles.wordpress.org/welcher/\">Ryan Welcher</a> · <a href=\"https://profiles.wordpress.org/looswebstudio/\">Ryo</a> · <a href=\"https://profiles.wordpress.org/sppramodh/\">S P Pramodh</a> · <a href=\"https://profiles.wordpress.org/sebastienserre/\">S&#233;bastien SERRE</a> · <a href=\"https://profiles.wordpress.org/sergiomdgomes/\">S&#233;rgio Gomes</a> · <a href=\"https://profiles.wordpress.org/soean/\">S&#246;ren W&#252;nsch</a> · <a href=\"https://profiles.wordpress.org/sabbir1991/\">Sabbir Ahmed</a> · <a href=\"https://profiles.wordpress.org/sabrineg/\">sabrineg</a> · <a href=\"https://profiles.wordpress.org/isaeedam/\">Saeed Piri</a> · <a href=\"https://profiles.wordpress.org/sagarlakhani/\">Sagar Lakhani</a> · <a href=\"https://profiles.wordpress.org/sagarprajapati/\">Sagar Prajapati</a> · <a href=\"https://profiles.wordpress.org/mi5t4n/\">Sagar Tamang</a> · <a href=\"https://profiles.wordpress.org/sailpete/\">sailpete</a> · <a href=\"https://profiles.wordpress.org/sainathpoojary/\">Sainath Poojary</a> · <a href=\"https://profiles.wordpress.org/sajjad67/\">Sajjad Hossain Sagor</a> · <a href=\"https://profiles.wordpress.org/sallyruchman/\">sally</a> · <a href=\"https://profiles.wordpress.org/samiamnot/\">samiamnot</a> · <a href=\"https://profiles.wordpress.org/viralsampat/\">Sampat Viral</a> · <a href=\"https://profiles.wordpress.org/ranafge/\">Samsul Islam Rana</a> · <a href=\"https://profiles.wordpress.org/otto42/\">Samuel Wood (Otto)</a> · <a href=\"https://profiles.wordpress.org/codexdemon/\">Sanyogg Shelar</a> · <a href=\"https://profiles.wordpress.org/mikachan/\">Sarah Norris</a> · <a href=\"https://profiles.wordpress.org/sarathar/\">Sarath AR</a> · <a href=\"https://profiles.wordpress.org/sarthaknagoshe2002/\">Sarthak Nagoshe</a> · <a href=\"https://profiles.wordpress.org/sathyapulse/\">Sathiya Venkatesan</a> · <a href=\"https://profiles.wordpress.org/sfougnier/\">Saul Fougnier</a> · <a href=\"https://profiles.wordpress.org/saurabhdhariwal/\">saurabh.dhariwal</a> · <a href=\"https://profiles.wordpress.org/saxonafletcher/\">Saxon Fletcher</a> · <a href=\"https://profiles.wordpress.org/infosatech/\">Sayan Datta</a> · <a href=\"https://profiles.wordpress.org/sayedulsayem/\">Sayedul Sayem</a> · <a href=\"https://profiles.wordpress.org/sbathompson/\">sbathompson</a> · <a href=\"https://profiles.wordpress.org/paragoninitiativeenterprises/\">Scott Arciszewski</a> · <a href=\"https://profiles.wordpress.org/sc0ttkclark/\">Scott Kingsley Clark</a> · <a href=\"https://profiles.wordpress.org/coffee2code/\">Scott Reilly</a> · <a href=\"https://profiles.wordpress.org/scribu/\">scribu</a> · <a href=\"https://profiles.wordpress.org/sean212/\">Sean Fisher</a> · <a href=\"https://profiles.wordpress.org/seanlanglands/\">seanlanglands</a> · <a href=\"https://profiles.wordpress.org/sebastianpisula/\">Sebastian Pisula</a> · <a href=\"https://profiles.wordpress.org/seifradwane/\">Seif Radwane</a> · <a href=\"https://profiles.wordpress.org/sergeybiryukov/\">Sergey Biryukov</a> · <a href=\"https://profiles.wordpress.org/sergiogutierrez/\">sergiogutierrez</a> · <a href=\"https://profiles.wordpress.org/smrubenstein/\">Seth Rubenstein</a> · <a href=\"https://profiles.wordpress.org/severinepozzo/\">Severine Pozzo</a> · <a href=\"https://profiles.wordpress.org/shadialaghbari/\">Shadi G شادي جـ</a> · <a href=\"https://profiles.wordpress.org/shailu25/\">Shail Mehta</a> · <a href=\"https://profiles.wordpress.org/sh4lin/\">Shalin Shah</a> · <a href=\"https://profiles.wordpress.org/shanemuir/\">Shane Muirhead</a> · <a href=\"https://profiles.wordpress.org/spdft/\">Shoe</a> · <a href=\"https://profiles.wordpress.org/shraddhagore/\">Shraddha Gore</a> · <a href=\"https://profiles.wordpress.org/shreyashd21/\">shreyashd21</a> · <a href=\"https://profiles.wordpress.org/shub07/\">Shubham Kumar Bansal</a> · <a href=\"https://profiles.wordpress.org/iamshubhamsp/\">Shubham Patil</a> · <a href=\"https://profiles.wordpress.org/shulard/\">shulard</a> · <a href=\"https://profiles.wordpress.org/shyamkariya/\">Shyam Kariya</a> · <a href=\"https://profiles.wordpress.org/nomnom99/\">Siddharth Thevaril</a> · <a href=\"https://profiles.wordpress.org/siliconforks/\">siliconforks</a> · <a href=\"https://profiles.wordpress.org/simo_m/\">Simone Maranzana</a> · <a href=\"https://profiles.wordpress.org/sirlouen/\">SirLouen</a> · <a href=\"https://profiles.wordpress.org/sjefen6/\">sjefen6</a> · <a href=\"https://profiles.wordpress.org/slaffik/\">Slava Abakumov</a> · <a href=\"https://profiles.wordpress.org/mslavco/\">Slavco Mihajloski</a> · <a href=\"https://profiles.wordpress.org/smerriman/\">smerriman</a> · <a href=\"https://profiles.wordpress.org/snehapatil02/\">Sneha Patil</a> · <a href=\"https://profiles.wordpress.org/snehal5774/\">snehal5774</a> · <a href=\"https://profiles.wordpress.org/sohamjoshi8275/\">Soham sham joshi</a> · <a href=\"https://profiles.wordpress.org/sourabhjain/\">Sourabh Jain</a> · <a href=\"https://profiles.wordpress.org/sourav08/\">Sourav Pahwa</a> · <a href=\"https://profiles.wordpress.org/soyebsalar/\">soyeb salar</a> · <a href=\"https://profiles.wordpress.org/spencerfinnell/\">Spencer Finnell</a> · <a href=\"https://profiles.wordpress.org/spenserhale/\">Spenser Hale</a> · <a href=\"https://profiles.wordpress.org/spmultidots/\">spmultidots</a> · <a href=\"https://profiles.wordpress.org/spncr/\">spncr</a> · <a href=\"https://profiles.wordpress.org/srikat/\">Sridhar Katakam</a> · <a href=\"https://profiles.wordpress.org/stefahn/\">Stefan Seidner-Britting</a> · <a href=\"https://profiles.wordpress.org/ryokuhi/\">Stefano Minoia</a> · <a href=\"https://profiles.wordpress.org/stein2nd/\">stein2nd</a> · <a href=\"https://profiles.wordpress.org/sabernhardt/\">Stephen Bernhardt</a> · <a href=\"https://profiles.wordpress.org/netweb/\">Stephen Edgar</a> · <a href=\"https://profiles.wordpress.org/dufresnesteven/\">Steve Dufresne</a> · <a href=\"https://profiles.wordpress.org/stimul/\">stimul</a> · <a href=\"https://profiles.wordpress.org/strarsis/\">strarsis</a> · <a href=\"https://profiles.wordpress.org/stuartmcalpine/\">Stuart McAlpine</a> · <a href=\"https://profiles.wordpress.org/subodhrajpopat/\">Subodh Rajpopat</a> · <a href=\"https://profiles.wordpress.org/subrataemfluence/\">Subrata Sarkar</a> · <a href=\"https://profiles.wordpress.org/sketchboy/\">SUHAS SUTAR</a> · <a href=\"https://profiles.wordpress.org/sukhendu2002/\">Sukhendu Sekhar Guria</a> · <a href=\"https://profiles.wordpress.org/sumitbagthariya16/\">Sumit Bagthariya</a> · <a href=\"https://profiles.wordpress.org/sumitsingh/\">Sumit Singh</a> · <a href=\"https://profiles.wordpress.org/csesumonpro/\">Sumon Sarker</a> · <a href=\"https://profiles.wordpress.org/sunil25393/\">SunilPrajapati</a> · <a href=\"https://profiles.wordpress.org/surajswalstar/\">Suraj Sutar</a> · <a href=\"https://profiles.wordpress.org/swalkinshaw/\">swalkinshaw</a> · <a href=\"https://profiles.wordpress.org/cybr/\">Sybre Waaijer</a> · <a href=\"https://profiles.wordpress.org/synchro/\">Synchro</a> · <a href=\"https://profiles.wordpress.org/tschwarz-1/\">t.schwarz</a> · <a href=\"https://profiles.wordpress.org/tacoverdo/\">Taco Verdonschot</a> · <a href=\"https://profiles.wordpress.org/gonom9/\">Taegon Kim</a> · <a href=\"https://profiles.wordpress.org/iamtakashi/\">Takashi Irie</a> · <a href=\"https://profiles.wordpress.org/inc2734/\">Takashi Kitajima</a> · <a href=\"https://profiles.wordpress.org/takayukister/\">Takayuki Miyoshi</a> · <a href=\"https://profiles.wordpress.org/takuword/\">Takuro</a> · <a href=\"https://profiles.wordpress.org/karmatosed/\">Tammie Lister</a> · <a href=\"https://profiles.wordpress.org/devtanbir/\">Tanbir Ahmod</a> · <a href=\"https://profiles.wordpress.org/voboghure/\">Tapan Kumer Das</a> · <a href=\"https://profiles.wordpress.org/th23/\">th23</a> · <a href=\"https://profiles.wordpress.org/thakordarshil/\">Thakor Darshil</a> · <a href=\"https://profiles.wordpress.org/thelmachido/\">thelmachido a11n</a> · <a href=\"https://profiles.wordpress.org/themebeans/\">ThemeBeans</a> · <a href=\"https://profiles.wordpress.org/thomaswm/\">thomaswm</a> · <a href=\"https://profiles.wordpress.org/threadi/\">threadi</a> · <a href=\"https://profiles.wordpress.org/thrijith/\">Thrijith Thankachan</a> · <a href=\"https://profiles.wordpress.org/tiagogoncalves/\">tiago</a> · <a href=\"https://profiles.wordpress.org/tigriweb/\">TigriWeb</a> · <a href=\"https://profiles.wordpress.org/tillkruess/\">Till Kr&#252;ss</a> · <a href=\"https://profiles.wordpress.org/tb1909/\">Tim Brath&#228;rig</a> · <a href=\"https://profiles.wordpress.org/dontfeedthecode/\">Tim Sheehan</a> · <a href=\"https://profiles.wordpress.org/timwhitlock/\">Tim W</a> · <a href=\"https://profiles.wordpress.org/sippis/\">Timi Wahalahti</a> · <a href=\"https://profiles.wordpress.org/spaceshipone/\">Timoth&#233;e Brosille</a> · <a href=\"https://profiles.wordpress.org/timothyblynjacobs/\">Timothy Jacobs</a> · <a href=\"https://profiles.wordpress.org/tkama/\">Timur Kamaev</a> · <a href=\"https://profiles.wordpress.org/tirth03/\">Tirth Doshi</a> · <a href=\"https://profiles.wordpress.org/tjarrett/\">TJarrett</a> · <a href=\"https://profiles.wordpress.org/tobiasbg/\">Tobias B&#228;thge</a> · <a href=\"https://profiles.wordpress.org/tobifjellner/\">tobifjellner (Tor-Bjorn &#8220;Tobi&#8221; Fjellner)</a> · <a href=\"https://profiles.wordpress.org/tgsrvrs/\">Tom Gugel</a> · <a href=\"https://profiles.wordpress.org/tommusrhodus/\">Tom Rhodes</a> · <a href=\"https://profiles.wordpress.org/shimotomoki/\">Tomoki Shimomura</a> · <a href=\"https://profiles.wordpress.org/skithund/\">Toni Viemer&#246;</a> · <a href=\"https://profiles.wordpress.org/hellofromtonya/\">Tonya Mork</a> · <a href=\"https://profiles.wordpress.org/toro_unit/\">Toro_Unit (Hiroshi Urabe)</a> · <a href=\"https://profiles.wordpress.org/zodiac1978/\">Torsten Landsiedel</a> · <a href=\"https://profiles.wordpress.org/tropicalista/\">tropicalista</a> · <a href=\"https://profiles.wordpress.org/areziaal/\">Troy Chaplin</a> · <a href=\"https://profiles.wordpress.org/truptikanzariya/\">Trupti Kanzariya</a> · <a href=\"https://profiles.wordpress.org/trushaamin/\">Trusha</a> · <a href=\"https://profiles.wordpress.org/tunetheweb/\">tunetheweb</a> · <a href=\"https://profiles.wordpress.org/dinhtungdu/\">Tung Du</a> · <a href=\"https://profiles.wordpress.org/tusharaddweb/\">Tushar Patel</a> · <a href=\"https://profiles.wordpress.org/tyb/\">TyB</a> · <a href=\"https://profiles.wordpress.org/tysonlmao/\">tysonlmao</a> · <a href=\"https://profiles.wordpress.org/ugyensupport/\">Ugyen Dorji</a> · <a href=\"https://profiles.wordpress.org/umeshsinghin/\">Umesh Singh</a> · <a href=\"https://profiles.wordpress.org/unsalkorkmaz/\">Unsal Korkmaz</a> · <a href=\"https://profiles.wordpress.org/up1512001/\">up1512001</a> · <a href=\"https://profiles.wordpress.org/upadalavipul/\">upadalavipul</a> · <a href=\"https://profiles.wordpress.org/utrenkner/\">utrenkner</a> · <a href=\"https://profiles.wordpress.org/eboxnet/\">Vagelis</a> · <a href=\"https://profiles.wordpress.org/vaibhavsweb/\">Vaibhav Singh Web</a> · <a href=\"https://profiles.wordpress.org/vaibhav2527/\">Vaibhav Tukaram Nawale</a> · <a href=\"https://profiles.wordpress.org/valer1e/\">Val&#233;rie Galassi</a> · <a href=\"https://profiles.wordpress.org/twvania/\">Vania</a> · <a href=\"https://profiles.wordpress.org/okvee/\">vee</a> · <a href=\"https://profiles.wordpress.org/vevas/\">Vegard S.</a> · <a href=\"https://profiles.wordpress.org/vgnavada/\">vgnavada</a> · <a href=\"https://profiles.wordpress.org/vcanales/\">Vicente Canales</a> · <a href=\"https://profiles.wordpress.org/fylgjur/\">Victoria - a11n</a> · <a href=\"https://profiles.wordpress.org/gvgvgvijayan/\">Vijayan</a> · <a href=\"https://profiles.wordpress.org/vijaysinh9094/\">Vijaysinh Zala</a> · <a href=\"https://profiles.wordpress.org/vikrampm/\">Vikram</a> · <a href=\"https://profiles.wordpress.org/szepeviktor/\">Viktor Sz&#233;pe</a> · <a href=\"https://profiles.wordpress.org/vineet2003/\">Vinit</a> · <a href=\"https://profiles.wordpress.org/vipulgupta003/\">Vipul Gupta</a> · <a href=\"https://profiles.wordpress.org/vipulpatil/\">Vipul Patil</a> · <a href=\"https://profiles.wordpress.org/virgildia/\">Virgildia</a> · <a href=\"https://profiles.wordpress.org/virgar/\">Virginie Garnier</a> · <a href=\"https://profiles.wordpress.org/virginienacci/\">virginienacci</a> · <a href=\"https://profiles.wordpress.org/vishy-moghan/\">Vishy Moghan</a> · <a href=\"https://profiles.wordpress.org/vivekawsm/\">vivekawsm</a> · <a href=\"https://profiles.wordpress.org/vrishabhsk/\">Vrishabh Jasani</a> · <a href=\"https://profiles.wordpress.org/vykesmac/\">vykesmac</a> · <a href=\"https://profiles.wordpress.org/wadowad/\">wadowad</a> · <a href=\"https://profiles.wordpress.org/pctevree/\">Web-Pepper</a> · <a href=\"https://profiles.wordpress.org/webmandesign/\">WebMan Design &#124; Oliver Juhas</a> · <a href=\"https://profiles.wordpress.org/west7/\">Wes Theron</a> · <a href=\"https://profiles.wordpress.org/westonruter/\">Weston Ruter</a> · <a href=\"https://profiles.wordpress.org/whaze/\">whaze</a> · <a href=\"https://profiles.wordpress.org/skorasaurus/\">Will Skora</a> · <a href=\"https://profiles.wordpress.org/williampatton/\">williampatton</a> · <a href=\"https://profiles.wordpress.org/wongjn/\">wongjn</a> · <a href=\"https://profiles.wordpress.org/worldweb/\">World Web Technology</a> · <a href=\"https://profiles.wordpress.org/wpgerd/\">wpgerd</a> · <a href=\"https://profiles.wordpress.org/wplmillet/\">wplmillet</a> · <a href=\"https://profiles.wordpress.org/wprockstar2/\">wprockstar2</a> · <a href=\"https://profiles.wordpress.org/wwdes/\">wwdes</a> · <a href=\"https://profiles.wordpress.org/xate/\">xate</a> · <a href=\"https://profiles.wordpress.org/xavilc/\">xavilc</a> · <a href=\"https://profiles.wordpress.org/xpurichan/\">xpurichan</a> · <a href=\"https://profiles.wordpress.org/yahil/\">Yahil Madakiya</a> · <a href=\"https://profiles.wordpress.org/yaniiliev/\">Yani</a> · <a href=\"https://profiles.wordpress.org/yashrocz/\">Yash Kukreja</a> · <a href=\"https://profiles.wordpress.org/yellowafterlife/\">yellowafterlife</a> · <a href=\"https://profiles.wordpress.org/yogeshbhutkar/\">Yogesh Bhutkar</a> · <a href=\"https://profiles.wordpress.org/yudhisthirnahar/\">yudhisthirnahar</a> · <a href=\"https://profiles.wordpress.org/fierevere/\">Yui</a> · <a href=\"https://profiles.wordpress.org/yuliyan/\">Yuliyan Slavchev</a> · <a href=\"https://profiles.wordpress.org/omaeyusuke/\">yuu</a> · <a href=\"https://profiles.wordpress.org/zaoyao/\">zaoyao</a> · <a href=\"https://profiles.wordpress.org/zargarov/\">Zargarov</a> · <a href=\"https://profiles.wordpress.org/zebulan/\">Zebulan Stanphill</a> · <a href=\"https://profiles.wordpress.org/zeelthakkar/\">zeelthakkar</a> · <a href=\"https://profiles.wordpress.org/zunaid321/\">Zunaid Amin</a> · <a href=\"https://profiles.wordpress.org/haozi/\">耗子</a></p>\n\n\n\n<p>More than <a href=\"https://translate.wordpress.org/stats/\">60 locales have fully translated</a> WordPress 6.8 into their language making this one of the most translated releases ever on day one.&nbsp; Community translators are working hard to ensure more translations are on their way.&nbsp; Thank you to everyone who helps make WordPress available in 200 languages.</p>\n\n\n\n<p>Last but not least, thanks to the volunteers who contribute to the <a href=\"https://wordpress.org/support/\">support forums</a> by answering questions from WordPress users worldwide.</p>\n\n\n\n<h2 class=\"wp-block-heading\"><strong>Get involved</strong></h2>\n\n\n\n<p>Participation in WordPress goes far beyond coding. And learning more and getting involved is easy.&nbsp; Discover the teams that come together to <a href=\"https://make.wordpress.org/\">Make WordPress</a> and use <a href=\"https://make.wordpress.org/contribute/\">this interactive tool</a> to help you decide which is right for you.</p>\n\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:30:\"com-wordpress:feed-additions:1\";a:1:{s:7:\"post-id\";a:1:{i:0;a:5:{s:4:\"data\";s:5:\"18701\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}}}i:4;a:6:{s:4:\"data\";s:66:\"\n		\n		\n		\n		\n		\n				\n		\n		\n		\n		\n\n					\n										\n					\n		\n		\n			\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";s:5:\"child\";a:4:{s:0:\"\";a:6:{s:5:\"title\";a:1:{i:0;a:5:{s:4:\"data\";s:34:\"WordCamp Europe 2025: Sali, Basel!\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"link\";a:1:{i:0;a:5:{s:4:\"data\";s:67:\"https://wordpress.org/news/2025/04/wordcamp-europe-2025-sali-basel/\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:7:\"pubDate\";a:1:{i:0;a:5:{s:4:\"data\";s:31:\"Mon, 14 Apr 2025 19:16:10 +0000\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:8:\"category\";a:4:{i:0;a:5:{s:4:\"data\";s:6:\"Events\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:1;a:5:{s:4:\"data\";s:8:\"WordCamp\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:2;a:5:{s:4:\"data\";s:4:\"WCEU\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:3;a:5:{s:4:\"data\";s:9:\"WordCamps\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"guid\";a:1:{i:0;a:5:{s:4:\"data\";s:35:\"https://wordpress.org/news/?p=18683\";s:7:\"attribs\";a:1:{s:0:\"\";a:1:{s:11:\"isPermaLink\";s:5:\"false\";}}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:11:\"description\";a:1:{i:0;a:5:{s:4:\"data\";s:411:\"WordCamp Europe 2025 will be held in gorgeous Basel, Switzerland, from June 5 to 7. It will bring together open source enthusiasts, developers, and WordPress professionals from across the region—and the world! This year’s event offers fresh perspectives, engaging conversations, and countless opportunities to connect with the WordPress community, agencies, and innovators over three action-packed [&#8230;]\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:32:\"http://purl.org/dc/elements/1.1/\";a:1:{s:7:\"creator\";a:1:{i:0;a:5:{s:4:\"data\";s:14:\"Brett McSherry\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:40:\"http://purl.org/rss/1.0/modules/content/\";a:1:{s:7:\"encoded\";a:1:{i:0;a:5:{s:4:\"data\";s:6552:\"\n<figure class=\"wp-block-image size-large\"><img data-recalc-dims=\"1\" loading=\"lazy\" decoding=\"async\" width=\"1024\" height=\"390\" src=\"https://i0.wp.com/wordpress.org/news/files/2025/04/473144529_985418666952827_8265761567676487048_n.png?resize=1024%2C390&#038;ssl=1\" alt=\"\" class=\"wp-image-18690\" srcset=\"https://i0.wp.com/wordpress.org/news/files/2025/04/473144529_985418666952827_8265761567676487048_n.png?resize=1024%2C390&amp;ssl=1 1024w, https://i0.wp.com/wordpress.org/news/files/2025/04/473144529_985418666952827_8265761567676487048_n.png?resize=300%2C114&amp;ssl=1 300w, https://i0.wp.com/wordpress.org/news/files/2025/04/473144529_985418666952827_8265761567676487048_n.png?resize=768%2C292&amp;ssl=1 768w, https://i0.wp.com/wordpress.org/news/files/2025/04/473144529_985418666952827_8265761567676487048_n.png?w=1230&amp;ssl=1 1230w\" sizes=\"auto, (max-width: 1000px) 100vw, 1000px\" /></figure>\n\n\n\n<p>WordCamp Europe 2025 will be held in gorgeous Basel, Switzerland, from June 5 to 7. It will bring together open source enthusiasts, developers, and WordPress professionals from across the region—and the world!</p>\n\n\n\n<p>This year’s event offers fresh perspectives, engaging conversations, and countless opportunities to connect with the WordPress community, agencies, and innovators over three action-packed days of learning, networking, and collaboration.</p>\n\n\n\n<div class=\"wp-block-group has-off-white-background-color has-background is-layout-constrained wp-container-core-group-is-layout-8503c47b wp-block-group-is-layout-constrained\">\n<div style=\"height:50px\" aria-hidden=\"true\" class=\"wp-block-spacer\"></div>\n\n\n\n<p class=\"has-text-align-center has-large-font-size\"><img src=\"https://s.w.org/images/core/emoji/16.0.1/72x72/1f39f.png\" alt=\"🎟\" class=\"wp-smiley\" style=\"height: 1em; max-height: 1em;\" /> <strong>Tickets are limited—secure yours today!</strong></p>\n\n\n\n<div style=\"height:25px\" aria-hidden=\"true\" class=\"wp-block-spacer\"></div>\n\n\n\n<div class=\"wp-block-buttons is-content-justification-center is-layout-flex wp-container-core-buttons-is-layout-a89b3969 wp-block-buttons-is-layout-flex\">\n<div class=\"wp-block-button\"><a class=\"wp-block-button__link has-text-align-center wp-element-button\" href=\"https://europe.wordcamp.org/2025/tickets/\">Get your ticket now</a></div>\n</div>\n\n\n\n<div style=\"height:50px\" aria-hidden=\"true\" class=\"wp-block-spacer\"></div>\n</div>\n\n\n\n<h2 class=\"wp-block-heading\">Keynotes, panels, and deep dives</h2>\n\n\n\n<p>The main conference, which will be held on June 6th and 7th, will feature <a href=\"https://europe.wordcamp.org/2025/schedule/\">a lineup of notable keynote sessions</a>, including diverse sessions on business strategy and building innovative modern features, open-source advocacy, accessibility, and more. For those looking to sharpen their skills, presentations will dive deep into topics like SEO for WordPress, performance optimization, speed-builds, and leveraging AI.</p>\n\n\n\n<h2 class=\"wp-block-heading\">Q&amp;A Fireside Chat</h2>\n\n\n\n<p>You can join Mary Hubbard, the WordPress Executive Director, and Matt Mullenweg, co-founder of the WordPress project on June 7th to discuss WordPress and its community, followed by an in-person Q&amp;A session here in Basel.</p>\n\n\n\n<h2 class=\"wp-block-heading\">Explore Basel</h2>\n\n\n\n<p>Basel, the third-largest city in Switzerland, <a href=\"https://europe.wordcamp.org/2025/location/\">is filled with historical buildings, a cathedral, museums, and hidden gems</a>! You can get around Basel and experience it yourself using your BaselCard, which allows you to hop on and off all public transportation without a worry. A walk down Basel’s river Rhine could be the perfect evening during your visit to WordCamp Europe!</p>\n\n\n\n<p><img src=\"https://s.w.org/images/core/emoji/16.0.1/72x72/1f6cf.png\" alt=\"🛏\" class=\"wp-smiley\" style=\"height: 1em; max-height: 1em;\" /><a href=\"https://europe.wordcamp.org/2025/location/accommodation/\"> Find accommodations in Basel<br></a><img src=\"https://s.w.org/images/core/emoji/16.0.1/72x72/1f68c.png\" alt=\"🚌\" class=\"wp-smiley\" style=\"height: 1em; max-height: 1em;\" /><a href=\"https://europe.wordcamp.org/2025/location/getting-around-in-basel/\"> Learn about BaselCard benefits<br></a><img src=\"https://s.w.org/images/core/emoji/16.0.1/72x72/1f4c4.png\" alt=\"📄\" class=\"wp-smiley\" style=\"height: 1em; max-height: 1em;\" /><a href=\"https://europe.wordcamp.org/2025/location/visa/\"> Need a visa? Apply by May 10, 2025</a></p>\n\n\n\n<p><img src=\"https://s.w.org/images/core/emoji/16.0.1/72x72/1f4c5.png\" alt=\"📅\" class=\"wp-smiley\" style=\"height: 1em; max-height: 1em;\" /> <strong>Plan Your WordCamp Europe Experience</strong> See the <a href=\"https://europe.wordcamp.org/2025/schedule/\">full schedule</a>, plan your sessions, and maximize your time in Basel.</p>\n\n\n\n<h2 class=\"wp-block-heading\">Get WordCamp-ready</h2>\n\n\n\n<p><img src=\"https://s.w.org/images/core/emoji/16.0.1/72x72/1f39f.png\" alt=\"🎟\" class=\"wp-smiley\" style=\"height: 1em; max-height: 1em;\" /> <strong>Make sure to secure your <a href=\"https://europe.wordcamp.org/2025/tickets/\">WordCamp Europe 2025 tickets</a></strong> (if you haven’t already!)</p>\n\n\n\n<p>Follow WordCamp Europe news on <a href=\"https://bsky.app/profile/wordpress.org\">Bluesky</a>, <a href=\"https://instagram.com/wordpress/\">Instagram</a>, <a href=\"https://www.facebook.com/WordPress\">Facebook</a>, <a href=\"https://www.linkedin.com/company/wordpress/\">LinkedIn</a>, <a href=\"https://www.threads.net/@wordpress\">Threads</a>, <a href=\"https://mastodon.world/@wordpress\">Mastodon</a>, and <a href=\"https://twitter.com/WordPress\">X/Twitter</a> for real-time updates!</p>\n\n\n\n<p>You can also help us spread the word about WordCamp Europe this year! Post about your attendance <a href=\"https://europe.wordcamp.org/2025/press/spread-the-word/\">using our ready-made texts</a> or we also encourage you to use your own voice – you know your community best.</p>\n\n\n\n<p>And as always, be part of the conversation! Whether you’re attending in-person in Basel or following along online, share your experiences using the tags #WCEU and #WordPress on all social media networks!</p>\n\n\n\n<p>Basel is calling—see you at WordCamp Europe 2025! <img src=\"https://s.w.org/images/core/emoji/16.0.1/72x72/1f1e8-1f1ed.png\" alt=\"🇨🇭\" class=\"wp-smiley\" style=\"height: 1em; max-height: 1em;\" /></p>\n\n\n\n<hr class=\"wp-block-separator has-alpha-channel-opacity\" />\n\n\n\n<p>Thank you to <a href=\"https://profiles.wordpress.org/dolgelukkig/\">@dolgelukkig</a> for drafting this copy.</p>\n\n\n\n<p></p>\n\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:30:\"com-wordpress:feed-additions:1\";a:1:{s:7:\"post-id\";a:1:{i:0;a:5:{s:4:\"data\";s:5:\"18683\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}}}i:5;a:6:{s:4:\"data\";s:69:\"\n		\n		\n		\n		\n		\n				\n		\n		\n		\n		\n		\n\n					\n										\n					\n		\n		\n			\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";s:5:\"child\";a:4:{s:0:\"\";a:6:{s:5:\"title\";a:1:{i:0;a:5:{s:4:\"data\";s:33:\"WordPress 6.8 Release Candidate 3\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"link\";a:1:{i:0;a:5:{s:4:\"data\";s:69:\"https://wordpress.org/news/2025/04/wordpress-6-8-release-candidate-3/\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:7:\"pubDate\";a:1:{i:0;a:5:{s:4:\"data\";s:31:\"Tue, 08 Apr 2025 15:54:49 +0000\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:8:\"category\";a:5:{i:0;a:5:{s:4:\"data\";s:11:\"Development\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:1;a:5:{s:4:\"data\";s:8:\"Releases\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:2;a:5:{s:4:\"data\";s:3:\"6-8\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:3;a:5:{s:4:\"data\";s:11:\"development\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:4;a:5:{s:4:\"data\";s:8:\"releases\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"guid\";a:1:{i:0;a:5:{s:4:\"data\";s:35:\"https://wordpress.org/news/?p=18673\";s:7:\"attribs\";a:1:{s:0:\"\";a:1:{s:11:\"isPermaLink\";s:5:\"false\";}}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:11:\"description\";a:1:{i:0;a:5:{s:4:\"data\";s:260:\"WordPress 6.8 RC 3 is ready for download and testing! The scheduled final release date for WordPress 6.8 is April 15, 2025. Your help testing over the next week is vital to ensuring the final release is everything it should be: stable, powerful, and intuitive.\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:32:\"http://purl.org/dc/elements/1.1/\";a:1:{s:7:\"creator\";a:1:{i:0;a:5:{s:4:\"data\";s:12:\"Jeffrey Paul\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:40:\"http://purl.org/rss/1.0/modules/content/\";a:1:{s:7:\"encoded\";a:1:{i:0;a:5:{s:4:\"data\";s:8765:\"\n<p>The third release candidate (“RC3”) for WordPress 6.8 is ready for download and testing!</p>\n\n\n\n<p><strong>This version of the WordPress software is under development</strong>.<strong>&nbsp; Please do not install, run, or test this version of WordPress on production or mission-critical websites.</strong>&nbsp; Instead, it’s recommended that you evaluate RC3 on a test server and site.</p>\n\n\n\n<p>Reaching this phase of the release cycle is an important milestone.&nbsp; While release candidates are considered ready for release, testing remains crucial to ensure that everything in WordPress 6.8 is the best it can be.</p>\n\n\n\n<p>You can test WordPress 6.8 RC3 in four ways:</p>\n\n\n\n<figure class=\"wp-block-table\"><table class=\"has-fixed-layout\"><tbody><tr><td><strong>Plugin</strong></td><td>Install and activate the <a href=\"https://wordpress.org/plugins/wordpress-beta-tester/\">WordPress Beta Tester</a> plugin on a WordPress install.&nbsp; (Select the “Bleeding edge” channel and “Beta/RC Only” stream).</td></tr><tr><td><strong>Direct Download</strong></td><td>Download the <a href=\"https://wordpress.org/wordpress-6.8-RC3.zip\">RC3 version (zip)</a> and install it on a WordPress website.</td></tr><tr><td><strong>Command Line</strong></td><td>Use the following <a href=\"https://make.wordpress.org/cli/\">WP-CLI</a> command: <code>wp core update --version=6.8-RC3</code></td></tr><tr><td><strong>WordPress Playground</strong></td><td>Use the <a href=\"https://playground.wordpress.net/?php=8.0&amp;wp=beta&amp;networking=no&amp;language=&amp;multisite=no&amp;random=y4q1rn88xn\">6.8 RC3 WordPress Playground instance</a> (available within 35 minutes after the release is ready) to test the software directly in your browser without the need for a separate site or setup.</td></tr></tbody></table></figure>\n\n\n\n<p>The current target for the WordPress 6.8 release is <strong>April 15, 2025</strong>. Get an overview of the <a href=\"https://make.wordpress.org/core/6-8/\">6.8 release cycle</a>, and check the <a href=\"https://make.wordpress.org/core/\">Make WordPress Core blog</a> for <a href=\"https://make.wordpress.org/core/tag/6-8/\">6.8-related posts</a> leading up to next week’s release for further details.</p>\n\n\n\n<h2 class=\"wp-block-heading\">What’s in WordPress 6.8 RC3?</h2>\n\n\n\n<p>Get a recap of WordPress 6.8’s highlighted features in the <a href=\"https://wordpress.org/news/2025/03/wordpress-6-8-beta-1/\">Beta 1 announcement</a>. For more technical information related to issues addressed since <a href=\"https://wordpress.org/news/2025/04/wordpress-6-8-release-candidate-2/\">RC2</a>, you can browse the following links:</p>\n\n\n\n<ul class=\"wp-block-list\">\n<li><a href=\"https://github.com/WordPress/gutenberg/commits/wp/6.8?since=2025-04-02&amp;until=2025-04-08\">GitHub commits for 6.8</a> since April 1</li>\n\n\n\n<li><a href=\"https://core.trac.wordpress.org/log/branches/6.8?action=stop_on_copy&amp;mode=stop_on_copy&amp;rev=60145&amp;stop_rev=60125&amp;limit=100&amp;sfp_email=&amp;sfph_mail=\">Changes committed</a> since April 1</li>\n</ul>\n\n\n\n<h2 class=\"wp-block-heading\">How you can contribute</h2>\n\n\n\n<p>WordPress is open source software made possible by a passionate community that collaborates and contributes to its development. The resources below outline various ways you can help the world’s most popular open source web platform, regardless of your technical expertise.</p>\n\n\n\n<h3 class=\"wp-block-heading\">Get involved in testing</h3>\n\n\n\n<p>Testing for issues is critical to ensuring WordPress is performant and stable. It’s also a meaningful way for anyone to contribute. <a href=\"https://make.wordpress.org/test/2025/03/04/help-test-wordpress-6-8/\">This detailed guide</a> will walk you through testing features in WordPress 6.8.&nbsp; For those new to testing, follow <a href=\"https://make.wordpress.org/test/handbook/get-setup-for-testing/\">this general testing guide</a> for more details on getting set up.</p>\n\n\n\n<p>If you encounter an issue, please report it to the <a href=\"https://wordpress.org/support/forum/alphabeta/\">Alpha/Beta area</a> of the support forums or directly to <a href=\"https://core.trac.wordpress.org/newticket\">WordPress Trac</a> if you are comfortable writing a reproducible bug report. You can also check your issue against a list of <a href=\"https://core.trac.wordpress.org/tickets/major\">known bugs</a>.</p>\n\n\n\n<p>Curious about testing releases in general?&nbsp; Follow along with the <a href=\"https://make.wordpress.org/test/\">testing initiatives in Make Core</a> and join the <a href=\"https://wordpress.slack.com/messages/core-test/\">#core-test channel</a> on <a href=\"https://wordpress.slack.com/\">Making WordPress Slack</a>.</p>\n\n\n\n<h3 class=\"wp-block-heading\">Search for vulnerabilities</h3>\n\n\n\n<p>From now until the final release of WordPress 6.8 (scheduled for April 15, 2025), the <a href=\"https://make.wordpress.org/core/handbook/testing/reporting-security-vulnerabilities/#enhanced-bounty-rewards-during-beta-and-release-candidate-phases\">monetary reward for reporting new, unreleased security vulnerabilities</a> is doubled.&nbsp; Please follow responsible disclosure practices as detailed in the project’s security practices and policies outlined on the <a href=\"https://hackerone.com/wordpress\">HackerOne page</a> and in the <a href=\"https://wordpress.org/about/security/\">security white paper</a>.</p>\n\n\n\n<h3 class=\"wp-block-heading\">Update your theme or plugin</h3>\n\n\n\n<p>For plugin and theme authors, your products play an integral role in extending the functionality and value of WordPress for all users.&nbsp; For more details on developer-related changes in 6.8, please review the <a href=\"https://make.wordpress.org/core/2025/03/28/wordpress-6-8-field-guide/\">WordPress 6.8 Field Guide</a>.</p>\n\n\n\n<p>Thanks for continuing to test your themes and plugins with the WordPress 6.8 beta releases.&nbsp; With RC3, you’ll want to conclude your testing and update the <em>“Tested up to”</em> version in your <a href=\"https://developer.wordpress.org/plugins/wordpress-org/how-your-readme-txt-works/\">plugin’s readme file</a> to 6.8.</p>\n\n\n\n<p>If you find compatibility issues, please post detailed information to the <a href=\"https://wordpress.org/support/forum/alphabeta/\">support forum</a>.</p>\n\n\n\n<h3 class=\"wp-block-heading\">Help translate WordPress</h3>\n\n\n\n<p>Do you speak a language other than English?  <bdo lang=\"es\" dir=\"ltr\">¿Español?</bdo>  <bdo lang=\"fr\" dir=\"ltr\">Français?</bdo>  <bdo lang=\"ru\" dir=\"ltr\">Русский?</bdo>  <bdo lang=\"ja\" dir=\"ltr\">日本語?</bdo> <bdo lang=\"hi\" dir=\"ltr\">हिन्दी?</bdo> <bdo lang=\"mr\" dir=\"ltr\">मराठी?</bdo> <bdo lang=\"bn\" dir=\"ltr\">বাংলা?</bdo>  You can <a href=\"https://translate.wordpress.org/projects/wp/dev/\">help translate WordPress into more than 100 languages</a>.</p>\n\n\n\n<h2 class=\"wp-block-heading\">An RC3 haiku</h2>\n\n\n\n<p><em>The launch draws closer,</em><em><br></em><em>Six-eight sings through RC3,</em><em><br></em><em>Almost time to shine.</em></p>\n\n\n\n<p class=\"has-text-align-right\"><em>Thank you to the following contributors for collaborating on this post: <a href=\'https://profiles.wordpress.org/audrasjb/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>audrasjb</a>, <a href=\'https://profiles.wordpress.org/mamaduka/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>mamaduka</a>, <a href=\'https://profiles.wordpress.org/krupajnanda/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>krupajnanda</a>, <a href=\'https://profiles.wordpress.org/benjamin_zekavica/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>benjamin_zekavica</a>, <a href=\'https://profiles.wordpress.org/narenin/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>narenin</a>, <a href=\'https://profiles.wordpress.org/joedolson/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>joedolson</a>, <a href=\'https://profiles.wordpress.org/courane01/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>courane01</a>, <a href=\'https://profiles.wordpress.org/joemcgill/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>joemcgill</a>, <a href=\'https://profiles.wordpress.org/marybaum/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>marybaum</a>, <a href=\'https://profiles.wordpress.org/kmgalanakis/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>kmgalanakis</a>, <a href=\'https://profiles.wordpress.org/umeshsinghin/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>umeshsinghin</a>, <a href=\'https://profiles.wordpress.org/wildworks/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>wildworks</a>, <a href=\'https://profiles.wordpress.org/mkrndmane/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>mkrndmane</a>.</em></p>\n\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:30:\"com-wordpress:feed-additions:1\";a:1:{s:7:\"post-id\";a:1:{i:0;a:5:{s:4:\"data\";s:5:\"18673\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}}}i:6;a:6:{s:4:\"data\";s:69:\"\n		\n		\n		\n		\n		\n				\n		\n		\n		\n		\n		\n\n					\n										\n					\n		\n		\n			\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";s:5:\"child\";a:4:{s:0:\"\";a:6:{s:5:\"title\";a:1:{i:0;a:5:{s:4:\"data\";s:33:\"WordPress 6.8 Release Candidate 2\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"link\";a:1:{i:0;a:5:{s:4:\"data\";s:69:\"https://wordpress.org/news/2025/04/wordpress-6-8-release-candidate-2/\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:7:\"pubDate\";a:1:{i:0;a:5:{s:4:\"data\";s:31:\"Tue, 01 Apr 2025 15:53:20 +0000\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:8:\"category\";a:5:{i:0;a:5:{s:4:\"data\";s:11:\"Development\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:1;a:5:{s:4:\"data\";s:8:\"Releases\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:2;a:5:{s:4:\"data\";s:3:\"6.8\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:3;a:5:{s:4:\"data\";s:11:\"development\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:4;a:5:{s:4:\"data\";s:8:\"releases\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"guid\";a:1:{i:0;a:5:{s:4:\"data\";s:35:\"https://wordpress.org/news/?p=18662\";s:7:\"attribs\";a:1:{s:0:\"\";a:1:{s:11:\"isPermaLink\";s:5:\"false\";}}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:11:\"description\";a:1:{i:0;a:5:{s:4:\"data\";s:367:\"The second Release Candidate (“RC2”) for WordPress 6.8 is ready for download and testing! This version of the WordPress software is under development. Please do not install, run, or test this version of WordPress on production or mission-critical websites. Instead, it’s recommended that you evaluate RC2 on a test server and site. Reaching this phase [&#8230;]\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:32:\"http://purl.org/dc/elements/1.1/\";a:1:{s:7:\"creator\";a:1:{i:0;a:5:{s:4:\"data\";s:19:\"Jonathan Desrosiers\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:40:\"http://purl.org/rss/1.0/modules/content/\";a:1:{s:7:\"encoded\";a:1:{i:0;a:5:{s:4:\"data\";s:8741:\"\n<p>The second Release Candidate (“RC2”) for WordPress 6.8 is ready for download and testing!</p>\n\n\n\n<p><strong>This version of the WordPress software is under development</strong>.<strong> Please do not install, run, or test this version of WordPress on production or mission-critical websites.</strong> Instead, it’s recommended that you evaluate RC2 on a test server and site.</p>\n\n\n\n<p>Reaching this phase of the release cycle is an important milestone. While release candidates are considered ready for release, testing remains crucial to ensure that everything in WordPress 6.8 is the best it can be.</p>\n\n\n\n<p>You can test WordPress 6.8 RC2 in four ways:</p>\n\n\n\n<figure class=\"wp-block-table\"><table class=\"has-fixed-layout\"><tbody><tr><td><strong>Plugin</strong></td><td>Install and activate the<a href=\"https://wordpress.org/plugins/wordpress-beta-tester/\"> WordPress Beta Tester</a> plugin on a WordPress install. (Select the “Bleeding edge” channel and “Beta/RC Only” stream).</td></tr><tr><td><strong>Direct Download</strong></td><td>Download the<a href=\"https://wordpress.org/wordpress-6.8-RC2.zip\"> RC2 version (zip)</a> and install it on a WordPress website.</td></tr><tr><td><strong>Command Line</strong></td><td>Use the following<a href=\"https://make.wordpress.org/cli/\"> WP-CLI</a> command: wp core update &#8211;version=6.8-RC2</td></tr><tr><td><strong>WordPress Playground</strong></td><td>Use the<a href=\"https://playground.wordpress.net/?php=8.0&amp;wp=beta&amp;networking=no&amp;language=&amp;multisite=no&amp;random=y4q1rn88xn\"> 6.8 RC2 WordPress Playground instance</a> (available within 35 minutes after the release is ready) to test the software directly in your browser without the need for a separate site or setup.</td></tr></tbody></table></figure>\n\n\n\n<p>The current target for the WordPress 6.8 release is <strong>April 15, 2025</strong>.&nbsp; Get an overview of the<a href=\"https://make.wordpress.org/core/6-8/\"> 6.8 release cycle</a>, and check the<a href=\"https://make.wordpress.org/core/\"> Make WordPress Core blog</a> for<a href=\"https://make.wordpress.org/core/tag/6-8/\"> 6.8-related posts</a> in the coming weeks for further details.</p>\n\n\n\n<h2 class=\"wp-block-heading\">What’s in WordPress 6.8 RC2?</h2>\n\n\n\n<p>Get a recap of WordPress 6.8’s highlighted features in the<a href=\"https://wordpress.org/news/2025/03/wordpress-6-8-beta-1/\"> Beta 1 announcement</a>. For more technical information related to issues addressed since RC1, you can browse the following links:</p>\n\n\n\n<ul class=\"wp-block-list\">\n<li><a href=\"https://github.com/WordPress/gutenberg/commits/wp/6.8?since=2025-03-26&amp;until=2025-04-01\">GitHub commits for 6.8</a> since March 25</li>\n\n\n\n<li><a href=\"https://core.trac.wordpress.org/query?id=63175%2C62887%2C63193%2C61219&amp;milestone=6.8&amp;group=component&amp;col=id&amp;col=summary&amp;col=milestone&amp;col=owner&amp;col=type&amp;col=status&amp;col=priority&amp;order=priority\">Closed Trac tickets</a> since March 25</li>\n</ul>\n\n\n\n<p>Want to look deeper into the details and technical notes for this release? These recent posts cover some of the latest updates:</p>\n\n\n\n<ul class=\"wp-block-list\">\n<li><a href=\"https://make.wordpress.org/core/2025/03/06/speculative-loading-in-6-8/\">Speculative Loading in 6.8</a></li>\n\n\n\n<li><a href=\"https://make.wordpress.org/core/2025/02/17/wordpress-6-8-will-use-bcrypt-for-password-hashing/\">WordPress 6.8 will use bcrypt for password hashing</a></li>\n\n\n\n<li><a href=\"https://make.wordpress.org/core/2025/03/12/roster-of-design-tools-per-block-wordpress-6-8-edition/\">Roster of design tools per block (WordPress 6.8 edition)</a></li>\n\n\n\n<li><a href=\"https://make.wordpress.org/core/2025/03/13/more-efficient-block-type-registration-in-6-8/\">More efficient block type registration in 6.8</a></li>\n\n\n\n<li><a href=\"https://make.wordpress.org/core/2025/03/25/updates-to-user-interface-components-in-wordpress-6-8/\">Updates to user-interface components in WordPress 6.8</a></li>\n\n\n\n<li><a href=\"https://make.wordpress.org/core/2025/03/24/interactivity-api-best-practices-in-6-8/\">Interactivity API best practices in 6.8</a></li>\n\n\n\n<li><a href=\"https://make.wordpress.org/core/2025/03/12/i18n-improvements-6-8/\">Internationalization improvements in 6.8</a></li>\n</ul>\n\n\n\n<h2 class=\"wp-block-heading\">How you can contribute</h2>\n\n\n\n<p>WordPress is open source software made possible by a passionate community of people collaborating on and contributing to its development. The resources below outline various ways you can help the world’s most popular open source web platform, regardless of your technical expertise.</p>\n\n\n\n<h3 class=\"wp-block-heading\">Get involved in testing</h3>\n\n\n\n<p>Testing for issues is critical to ensuring WordPress is performant and stable.&nbsp; It’s also a meaningful way for anyone to contribute. <a href=\"https://make.wordpress.org/test/2025/03/04/help-test-wordpress-6-8/\">&nbsp;This detailed guide</a> will walk you through testing features in WordPress 6.8.&nbsp; For those new to testing, follow<a href=\"https://make.wordpress.org/test/handbook/get-setup-for-testing/\"> this general testing guide</a> for more details on getting set up.</p>\n\n\n\n<p>If you encounter an issue, please report it to the<a href=\"https://wordpress.org/support/forum/alphabeta/\"> Alpha/Beta area</a> of the support forums or directly to<a href=\"https://core.trac.wordpress.org/newticket\"> WordPress Trac</a> if you are comfortable writing a reproducible bug report.&nbsp; You can also check your issue against a list of<a href=\"https://core.trac.wordpress.org/tickets/major\"> known bugs</a>.</p>\n\n\n\n<p>Curious about testing releases in general?&nbsp; Follow along with the<a href=\"https://make.wordpress.org/test/\"> testing initiatives in Make Core</a> and join the<a href=\"https://wordpress.slack.com/messages/core-test/\"> #core-test channel</a> on<a href=\"https://wordpress.slack.com/\"> Making WordPress Slack</a>.</p>\n\n\n\n<h3 class=\"wp-block-heading\">Search for vulnerabilities</h3>\n\n\n\n<p>From now until the final release of WordPress 6.8 (scheduled for April 15, 2025), the<a href=\"https://make.wordpress.org/core/handbook/testing/reporting-security-vulnerabilities/#enhanced-bounty-rewards-during-beta-and-release-candidate-phases\"> monetary reward for reporting new, unreleased security vulnerabilities</a> is doubled. Please follow responsible disclosure practices as detailed in the project’s security practices and policies outlined on the<a href=\"https://hackerone.com/wordpress\"> HackerOne page</a> and in the<a href=\"https://wordpress.org/about/security/\"> security white paper</a>.</p>\n\n\n\n<h3 class=\"wp-block-heading\">Update your theme or plugin</h3>\n\n\n\n<p>For plugin and theme authors, your products play an integral role in extending the functionality and value of WordPress for all users.</p>\n\n\n\n<p>Thanks for continuing to test your themes and plugins with the WordPress 6.8 beta releases. With RC2, you’ll want to conclude your testing and update the <em>“Tested up to”</em> version in your<a href=\"https://developer.wordpress.org/plugins/wordpress-org/how-your-readme-txt-works/\"> plugin’s readme file</a> to 6.8.</p>\n\n\n\n<p>If you find compatibility issues, please post detailed information to the<a href=\"https://wordpress.org/support/forum/alphabeta/\"> support forum</a>.</p>\n\n\n\n<h3 class=\"wp-block-heading\">Help translate WordPress</h3>\n\n\n\n<p>Do you speak a language other than English?  ¿Español?  Français? Русский? 日本語?  हिन्दी?  বাংলা?  मराठी?  ಕನ್ನಡ?  You can<a href=\"https://translate.wordpress.org/projects/wp/dev/\"> help translate WordPress into more than 100 languages</a>. This release milestone (RC2) also marks the<a href=\"https://make.wordpress.org/polyglots/handbook/glossary/#hard-freeze\"> hard string freeze</a> point of the 6.8 release cycle.</p>\n\n\n\n<h2 class=\"wp-block-heading\">An RC2 haiku</h2>\n\n\n\n<p><em>Testing, 1, 2, 3</em><br><em>It’s almost April fifteenth</em><br><em>Squashing all the bugs</em></p>\n\n\n\n<p><em>Thank you to the following contributors for collaborating on this post: <a href=\'https://profiles.wordpress.org/michelleames/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>michelleames</a>, <a href=\'https://profiles.wordpress.org/tacoverdo/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>tacoverdo</a>, <a href=\'https://profiles.wordpress.org/jopdop30/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>jopdop30</a>, <a href=\'https://profiles.wordpress.org/vgnavada/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>vgnavada</a>, <a href=\'https://profiles.wordpress.org/jeffpaul/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>jeffpaul</a>.</em></p>\n\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:30:\"com-wordpress:feed-additions:1\";a:1:{s:7:\"post-id\";a:1:{i:0;a:5:{s:4:\"data\";s:5:\"18662\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}}}i:7;a:6:{s:4:\"data\";s:69:\"\n		\n		\n		\n		\n		\n				\n		\n		\n		\n		\n		\n\n					\n										\n					\n		\n		\n			\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";s:5:\"child\";a:4:{s:0:\"\";a:6:{s:5:\"title\";a:1:{i:0;a:5:{s:4:\"data\";s:33:\"WordPress 6.8 Release Candidate 1\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"link\";a:1:{i:0;a:5:{s:4:\"data\";s:69:\"https://wordpress.org/news/2025/03/wordpress-6-8-release-candidate-1/\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:7:\"pubDate\";a:1:{i:0;a:5:{s:4:\"data\";s:31:\"Tue, 25 Mar 2025 16:19:41 +0000\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:8:\"category\";a:5:{i:0;a:5:{s:4:\"data\";s:11:\"Development\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:1;a:5:{s:4:\"data\";s:8:\"Releases\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:2;a:5:{s:4:\"data\";s:3:\"6.8\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:3;a:5:{s:4:\"data\";s:11:\"development\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:4;a:5:{s:4:\"data\";s:8:\"releases\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"guid\";a:1:{i:0;a:5:{s:4:\"data\";s:35:\"https://wordpress.org/news/?p=18639\";s:7:\"attribs\";a:1:{s:0:\"\";a:1:{s:11:\"isPermaLink\";s:5:\"false\";}}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:11:\"description\";a:1:{i:0;a:5:{s:4:\"data\";s:267:\"WordPress 6.8 RC 1 is ready for download and testing! The scheduled final release date for WordPress 6.8 is April 15, 2025. Your help testing over the next three weeks is vital to ensuring the final release is everything it should be: stable, powerful, and intuitive.\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:32:\"http://purl.org/dc/elements/1.1/\";a:1:{s:7:\"creator\";a:1:{i:0;a:5:{s:4:\"data\";s:12:\"Jeffrey Paul\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:40:\"http://purl.org/rss/1.0/modules/content/\";a:1:{s:7:\"encoded\";a:1:{i:0;a:5:{s:4:\"data\";s:9475:\"\n<p>The first Release Candidate (“RC1”) for WordPress 6.8 is ready for download and testing!</p>\n\n\n\n<p><strong>This version of the WordPress software is under development</strong>.<strong>&nbsp; Please do not install, run, or test this version of WordPress on production or mission-critical websites.</strong>&nbsp; Instead, it’s recommended that you evaluate RC1 on a test server and site.</p>\n\n\n\n<p>Reaching this phase of the release cycle is an important milestone.&nbsp; While release candidates are considered ready for release, testing remains crucial to ensure that everything in WordPress 6.8 is the best it can be.</p>\n\n\n\n<p>You can test WordPress 6.8 RC1 in four ways:</p>\n\n\n\n<figure class=\"wp-block-table\"><table class=\"has-fixed-layout\"><tbody><tr><td><strong>Plugin</strong></td><td>Install and activate the<a href=\"https://wordpress.org/plugins/wordpress-beta-tester/\"> WordPress Beta Tester</a> plugin on a WordPress install.&nbsp; (Select the “Bleeding edge” channel and “Beta/RC Only” stream).</td></tr><tr><td><strong>Direct Download</strong></td><td>Download the <a href=\"https://wordpress.org/wordpress-6.8-RC1.zip\">RC1 version (zip)</a> and install it on a WordPress website.</td></tr><tr><td><strong>Command Line</strong></td><td>Use the following<a href=\"https://make.wordpress.org/cli/\"> WP-CLI</a> command: <code>wp core update --version=6.8-RC1</code></td></tr><tr><td><strong>WordPress Playground</strong></td><td>Use the <a href=\"https://playground.wordpress.net/?php=8.0&amp;wp=beta&amp;networking=no&amp;language=&amp;multisite=no&amp;random=y4q1rn88xn\">6.8 RC1 WordPress Playground instance</a> (available within 35 minutes after the release is ready) to test the software directly in your browser without the need for a separate site or setup.</td></tr></tbody></table></figure>\n\n\n\n<p>The current target for the WordPress 6.8 release is <strong>April 15, 2025</strong>.&nbsp; Get an overview of the <a href=\"https://make.wordpress.org/core/6-8/\">6.8 release cycle</a>, and check the<a href=\"https://make.wordpress.org/core/\"> Make WordPress Core blog</a> for <a href=\"https://make.wordpress.org/core/tag/6-8/\">6.8-related posts</a> in the coming weeks for further details.</p>\n\n\n\n<h2 class=\"wp-block-heading\">What’s in WordPress 6.8 RC1?</h2>\n\n\n\n<p>Get a recap of WordPress 6.8’s highlighted features in the <a href=\"https://wordpress.org/news/2025/03/wordpress-6-8-beta-1/\">Beta 1 announcement</a>.&nbsp; For more technical information related to issues addressed since Beta 3, you can browse the following links:</p>\n\n\n\n<ul class=\"wp-block-list\">\n<li><a href=\"https://github.com/WordPress/gutenberg/commits/wp/6.8?since=2025-03-18&amp;until=2025-03-25\">GitHub commits for 6.8</a> since March 18</li>\n\n\n\n<li><a href=\"https://core.trac.wordpress.org/query?status=closed&amp;changetime=03%2F18%2F2025..03%2F25%2F2025&amp;milestone=6.8&amp;col=id&amp;col=milestone&amp;col=owner&amp;col=type&amp;col=priority&amp;order=id\">Closed Trac tickets</a> since March 18</li>\n</ul>\n\n\n\n<p>Want to look deeper into the details and technical notes for this release? These recent posts cover some of the latest updates:</p>\n\n\n\n<ul class=\"wp-block-list\">\n<li><a href=\"https://make.wordpress.org/core/2025/03/06/speculative-loading-in-6-8/\">Speculative Loading in 6.8</a></li>\n\n\n\n<li><a href=\"https://make.wordpress.org/core/2025/02/17/wordpress-6-8-will-use-bcrypt-for-password-hashing/\">WordPress 6.8 will use bcrypt for password hashing</a></li>\n\n\n\n<li><a href=\"https://make.wordpress.org/core/2025/03/12/roster-of-design-tools-per-block-wordpress-6-8-edition/\">Roster of design tools per block (WordPress 6.8 edition)</a></li>\n\n\n\n<li><a href=\"https://make.wordpress.org/core/2025/03/13/more-efficient-block-type-registration-in-6-8/\">More efficient block type registration in 6.8</a></li>\n\n\n\n<li><a href=\"https://make.wordpress.org/core/2025/03/25/updates-to-user-interface-components-in-wordpress-6-8/\">Updates to user-interface components in WordPress 6.8</a></li>\n\n\n\n<li><a href=\"https://make.wordpress.org/core/2025/03/24/interactivity-api-best-practices-in-6-8/\">Interactivity API best practices in 6.8</a></li>\n\n\n\n<li><a href=\"https://make.wordpress.org/core/2025/03/12/i18n-improvements-6-8/\">Internationalization improvements in 6.8</a></li>\n</ul>\n\n\n\n<h2 class=\"wp-block-heading\">How you can contribute</h2>\n\n\n\n<p>WordPress is open source software made possible by a passionate community of people collaborating on and contributing to its development.&nbsp; The resources below outline various ways you can help the world’s most popular open source web platform, regardless of your technical expertise.</p>\n\n\n\n<h3 class=\"wp-block-heading\">Get involved in testing</h3>\n\n\n\n<p>Testing for issues is critical to ensuring WordPress is performant and stable.&nbsp; It’s also a meaningful way for anyone to contribute.&nbsp; <a href=\"https://make.wordpress.org/test/2025/03/04/help-test-wordpress-6-8/\">This detailed guide</a> will walk you through testing features in WordPress 6.8.&nbsp; For those new to testing, follow <a href=\"https://make.wordpress.org/test/handbook/get-setup-for-testing/\">this general testing guide</a> for more details on getting set up.</p>\n\n\n\n<p>If you encounter an issue, please report it to the<a href=\"https://wordpress.org/support/forum/alphabeta/\"> Alpha/Beta area</a> of the support forums or directly to<a href=\"https://core.trac.wordpress.org/newticket\"> WordPress Trac</a> if you are comfortable writing a reproducible bug report.&nbsp; You can also check your issue against a list of<a href=\"https://core.trac.wordpress.org/tickets/major\"> known bugs</a>.</p>\n\n\n\n<p>Curious about testing releases in general?&nbsp; Follow along with the<a href=\"https://make.wordpress.org/test/\"> testing initiatives in Make Core</a> and join the<a href=\"https://wordpress.slack.com/messages/core-test/\"> #core-test channel</a> on<a href=\"https://wordpress.slack.com/\"> Making WordPress Slack</a>.</p>\n\n\n\n<h3 class=\"wp-block-heading\">Search for vulnerabilities</h3>\n\n\n\n<p>From now until the final release of WordPress 6.8 (scheduled for April 15, 2025), the <a href=\"https://make.wordpress.org/core/handbook/testing/reporting-security-vulnerabilities/#enhanced-bounty-rewards-during-beta-and-release-candidate-phases\">monetary reward for reporting new, unreleased security vulnerabilities</a> is doubled.&nbsp; Please follow responsible disclosure practices as detailed in the project’s security practices and policies outlined on the<a href=\"https://hackerone.com/wordpress\"> HackerOne page</a> and in the<a href=\"https://wordpress.org/about/security/\"> security white paper</a>.</p>\n\n\n\n<h3 class=\"wp-block-heading\">Update your theme or plugin</h3>\n\n\n\n<p>For plugin and theme authors, your products play an integral role in extending the functionality and value of WordPress for all users.</p>\n\n\n\n<p>Thanks for continuing to test your themes and plugins with the WordPress 6.8 beta releases.&nbsp; With RC1, you’ll want to conclude your testing and update the <em>“Tested up to”</em> version in your<a href=\"https://developer.wordpress.org/plugins/wordpress-org/how-your-readme-txt-works/\"> plugin’s readme file</a> to 6.8.</p>\n\n\n\n<p>If you find compatibility issues, please post detailed information to the<a href=\"https://wordpress.org/support/forum/alphabeta/\"> support forum</a>.</p>\n\n\n\n<h3 class=\"wp-block-heading\">Help translate WordPress</h3>\n\n\n\n<p>Do you speak a language other than English?  <bdo lang=\"es\" dir=\"ltr\">¿Español?</bdo>  <bdo lang=\"fr\" dir=\"ltr\">Français?</bdo>  <bdo lang=\"ru\" dir=\"ltr\">Русский?</bdo>  <bdo lang=\"jp\" dir=\"ltr\">日本語?</bdo> <bdo lang=\"hi\" dir=\"ltr\">हिन्दी?</bdo>  <bdo lang=\"bn\" dir=\"ltr\">বাংলা?</bdo>  <bdo lang=\"mr\" dir=\"ltr\">मराठी?</bdo>  You can<a href=\"https://translate.wordpress.org/projects/wp/dev/\"> help translate WordPress into more than 100 languages</a>.  This release milestone (RC1) also marks the<a href=\"https://make.wordpress.org/polyglots/handbook/glossary/#hard-freeze\"> hard string freeze</a> point of the 6.8 release cycle.</p>\n\n\n\n<h2 class=\"wp-block-heading\">An RC1 haiku</h2>\n\n\n\n<p>March fades, nearly there,<br>Six-eight hums—a steady beat,<br>RC greets the world.</p>\n\n\n\n<p class=\"has-text-align-right\"><em>Thank you to the following contributors for collaborating on this post: <a href=\'https://profiles.wordpress.org/joemcgill/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>joemcgill</a> <a href=\'https://profiles.wordpress.org/benjamin_zekavica/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>benjamin_zekavica</a> <a href=\'https://profiles.wordpress.org/courane01/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>courane01</a> <a href=\'https://profiles.wordpress.org/mkrndmane/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>mkrndmane</a> <a href=\'https://profiles.wordpress.org/audrasjb/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>audrasjb</a> <a href=\'https://profiles.wordpress.org/areziaal/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>areziaal</a> <a href=\'https://profiles.wordpress.org/ankit-k-gupta/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>ankit-k-gupta</a> <a href=\'https://profiles.wordpress.org/krupajnanda/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>krupajnanda</a> <a href=\'https://profiles.wordpress.org/bph/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>bph</a>.</em></p>\n\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:30:\"com-wordpress:feed-additions:1\";a:1:{s:7:\"post-id\";a:1:{i:0;a:5:{s:4:\"data\";s:5:\"18639\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}}}i:8;a:6:{s:4:\"data\";s:69:\"\n		\n		\n		\n		\n		\n				\n		\n		\n		\n		\n		\n\n					\n										\n					\n		\n		\n			\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";s:5:\"child\";a:4:{s:0:\"\";a:6:{s:5:\"title\";a:1:{i:0;a:5:{s:4:\"data\";s:20:\"WordPress 6.8 Beta 3\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"link\";a:1:{i:0;a:5:{s:4:\"data\";s:56:\"https://wordpress.org/news/2025/03/wordpress-6-8-beta-3/\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:7:\"pubDate\";a:1:{i:0;a:5:{s:4:\"data\";s:31:\"Tue, 18 Mar 2025 15:35:14 +0000\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:8:\"category\";a:5:{i:0;a:5:{s:4:\"data\";s:11:\"Development\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:1;a:5:{s:4:\"data\";s:8:\"Releases\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:2;a:5:{s:4:\"data\";s:3:\"6.8\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:3;a:5:{s:4:\"data\";s:11:\"development\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:4;a:5:{s:4:\"data\";s:8:\"releases\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"guid\";a:1:{i:0;a:5:{s:4:\"data\";s:35:\"https://wordpress.org/news/?p=18634\";s:7:\"attribs\";a:1:{s:0:\"\";a:1:{s:11:\"isPermaLink\";s:5:\"false\";}}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:11:\"description\";a:1:{i:0;a:5:{s:4:\"data\";s:289:\"WordPress 6.8 Beta 3 is ready for download and testing! The scheduled final release date for WordPress 6.8 is April 15, 2025. Your help testing Beta and RC versions over the next four weeks is vital to ensuring the final release is everything it should be: stable, powerful, and intuitive.\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:32:\"http://purl.org/dc/elements/1.1/\";a:1:{s:7:\"creator\";a:1:{i:0;a:5:{s:4:\"data\";s:12:\"Jeffrey Paul\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:40:\"http://purl.org/rss/1.0/modules/content/\";a:1:{s:7:\"encoded\";a:1:{i:0;a:5:{s:4:\"data\";s:5627:\"\n<p>WordPress 6.8 Beta 3 is now ready for testing!</p>\n\n\n\n<p><strong>This beta version of the WordPress software is under development</strong>. Please do not install, run, or test this version of WordPress on production or mission-critical websites. Instead, it is recommended you evaluate Beta 3 on a test server and site.</p>\n\n\n\n<p>You can test WordPress 6.8 Beta 3 in four ways:</p>\n\n\n\n<figure class=\"wp-block-table\"><table class=\"has-fixed-layout\"><tbody><tr><td><strong>Plugin</strong></td><td>Install and activate the<a href=\"https://wordpress.org/plugins/wordpress-beta-tester/\"> WordPress Beta Tester</a> plugin on a WordPress install.&nbsp; (Select the “Bleeding edge” channel and “Beta/RC Only” stream).</td></tr><tr><td><strong>Direct Download</strong></td><td>Download the <a href=\"https://wordpress.org/wordpress-6.8-beta3.zip\">Beta 3 version (zip)</a> and install it on a WordPress website.</td></tr><tr><td><strong>Command Line</strong></td><td>Use the following<a href=\"https://make.wordpress.org/cli/\"> WP-CLI</a> command: <code>wp core update --version=6.8-beta3</code></td></tr><tr><td><strong>WordPress Playground</strong></td><td>Use the <a href=\"https://playground.wordpress.net/?php=8.0&amp;wp=beta&amp;networking=no&amp;language=&amp;multisite=no&amp;random=y4q1rn88xn\">6.8 Beta 3 WordPress Playground instance</a> to test the software directly in your browser without the need for a separate site or setup.</td></tr></tbody></table></figure>\n\n\n\n<p>The current target date for the final release of WordPress 6.8 is <strong>April 15, 2025</strong>. Get an overview of the <a href=\"https://make.wordpress.org/core/6-8/\">6.8 release cycle</a>, and check the<a href=\"https://make.wordpress.org/core/\"> Make WordPress Core blog</a> for <a href=\"https://make.wordpress.org/core/tag/6-8/\">6.8-related posts</a> in the coming weeks for more information.</p>\n\n\n\n<p><strong>Catch up on what’s new in WordPress 6.8</strong>: Read the <a href=\"https://wordpress.org/news/2025/03/wordpress-6-8-beta-1/\">Beta 1</a> and <a href=\"https://wordpress.org/news/2025/03/wordpress-6-8-beta-2/\">Beta 2</a> announcements for details and highlights.</p>\n\n\n\n<h2 class=\"wp-block-heading\">How to test this release</h2>\n\n\n\n<p>Your help testing the WordPress 6.8 Beta 3 version is key to ensuring everything in the release is the best it can be. While testing the upgrade process is essential, trying out new features is equally important. <a href=\"https://make.wordpress.org/test/2025/03/04/help-test-wordpress-6-8/\">This detailed guide</a> will walk you through testing features in WordPress 6.8.</p>\n\n\n\n<p>If you encounter an issue, please report it to the Alpha/Beta area of the support forums or directly to<a href=\"https://core.trac.wordpress.org/newticket\"> WordPress Trac</a> if you are comfortable writing a reproducible bug report. You can also check your issue against a list of<a href=\"https://core.trac.wordpress.org/tickets/major\"> known bugs</a>.</p>\n\n\n\n<p>Curious about testing releases in general?&nbsp; Follow along with the<a href=\"https://make.wordpress.org/test/\"> testing initiatives in Make Core</a> and join the<a href=\"https://wordpress.slack.com/messages/core-test/\"> #core-test channel</a> on<a href=\"https://wordpress.slack.com/\"> Making WordPress Slack</a>.</p>\n\n\n\n<h2 class=\"wp-block-heading\">Vulnerability bounty doubles during Beta/RC</h2>\n\n\n\n<p>Between Beta 1, released on March 4, 2025, and the final Release Candidate (RC) scheduled for April 8, 2025, the <a href=\"https://make.wordpress.org/core/handbook/testing/reporting-security-vulnerabilities/#enhanced-bounty-rewards-during-beta-and-release-candidate-phases\">monetary reward for reporting new, unreleased security vulnerabilities</a> is doubled. Please follow responsible disclosure practices as detailed in the project’s security practices and policies outlined on the<a href=\"https://hackerone.com/wordpress\"> HackerOne page</a> and in the<a href=\"https://wordpress.org/about/security/\"> security white paper</a>.</p>\n\n\n\n<h2 class=\"wp-block-heading\">Beta 3 updates and highlights</h2>\n\n\n\n<p>WordPress 6.8 Beta 3 contains more than 3 Editor updates and fixes since the Beta 2 release, including 16 tickets for WordPress core.</p>\n\n\n\n<p>Each beta cycle focuses on bug fixes; more are on the way with your help through testing. You can browse the technical details for all issues addressed since Beta 3 using these links:</p>\n\n\n\n<ul class=\"wp-block-list\">\n<li><a href=\"https://github.com/WordPress/gutenberg/commits/wp/6.8?since=2025-03-12&amp;until=2025-03-17\">GitHub commits for 6.8</a> since March 12, 2025</li>\n\n\n\n<li><a href=\"https://core.trac.wordpress.org/query?status=closed&amp;changetime=03%2F12%2F2025..03%2F17%2F2025&amp;milestone=6.8&amp;col=id&amp;col=milestone&amp;col=owner&amp;col=type&amp;col=priority&amp;order=id\">Closed Trac tickets</a> since March 12, 2025</li>\n</ul>\n\n\n\n<h2 class=\"wp-block-heading\">A Beta 3 haiku</h2>\n\n\n\n<p>Beta three refines,<br>WordPress shapes with steady hands,<br>Code grows into form.</p>\n\n\n\n<p class=\"has-text-align-right\"><em>Props to <a href=\'https://profiles.wordpress.org/benjamin_zekavica/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>benjamin_zekavica</a> <a href=\'https://profiles.wordpress.org/krupajnanda/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>krupajnanda</a> <a href=\'https://profiles.wordpress.org/ankit-k-gupta/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>ankit-k-gupta</a> <a href=\'https://profiles.wordpress.org/joemcgill/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>joemcgill</a> for proofreading and review.</em></p>\n\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:30:\"com-wordpress:feed-additions:1\";a:1:{s:7:\"post-id\";a:1:{i:0;a:5:{s:4:\"data\";s:5:\"18634\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}}}i:9;a:6:{s:4:\"data\";s:69:\"\n		\n		\n		\n		\n		\n				\n		\n		\n		\n		\n		\n\n					\n										\n					\n		\n		\n			\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";s:5:\"child\";a:4:{s:0:\"\";a:6:{s:5:\"title\";a:1:{i:0;a:5:{s:4:\"data\";s:20:\"WordPress 6.8 Beta 2\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"link\";a:1:{i:0;a:5:{s:4:\"data\";s:56:\"https://wordpress.org/news/2025/03/wordpress-6-8-beta-2/\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:7:\"pubDate\";a:1:{i:0;a:5:{s:4:\"data\";s:31:\"Tue, 11 Mar 2025 15:46:13 +0000\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:8:\"category\";a:5:{i:0;a:5:{s:4:\"data\";s:11:\"Development\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:1;a:5:{s:4:\"data\";s:8:\"Releases\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:2;a:5:{s:4:\"data\";s:3:\"6.8\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:3;a:5:{s:4:\"data\";s:11:\"development\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}i:4;a:5:{s:4:\"data\";s:8:\"releases\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:4:\"guid\";a:1:{i:0;a:5:{s:4:\"data\";s:35:\"https://wordpress.org/news/?p=18619\";s:7:\"attribs\";a:1:{s:0:\"\";a:1:{s:11:\"isPermaLink\";s:5:\"false\";}}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:11:\"description\";a:1:{i:0;a:5:{s:4:\"data\";s:289:\"WordPress 6.8 Beta 2 is ready for download and testing! The scheduled final release date for WordPress 6.8 is April 15, 2025. Your help testing Beta and RC versions over the next five weeks is vital to ensuring the final release is everything it should be: stable, powerful, and intuitive.\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:32:\"http://purl.org/dc/elements/1.1/\";a:1:{s:7:\"creator\";a:1:{i:0;a:5:{s:4:\"data\";s:12:\"Jeffrey Paul\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:40:\"http://purl.org/rss/1.0/modules/content/\";a:1:{s:7:\"encoded\";a:1:{i:0;a:5:{s:4:\"data\";s:5940:\"\n<p>WordPress 6.8 Beta 2 is now ready for testing!</p>\n\n\n\n<p><strong>This beta version of the WordPress software is under development</strong>. Please do not install, run, or test this version of WordPress on production or mission-critical websites.&nbsp; Instead, you should evaluate Beta 2 on a test server and site.</p>\n\n\n\n<p>You can test WordPress 6.8 Beta 2 in four ways:</p>\n\n\n\n<figure class=\"wp-block-table\"><table class=\"has-fixed-layout\"><tbody><tr><td><strong>Plugin</strong></td><td>Install and activate the<a href=\"https://wordpress.org/plugins/wordpress-beta-tester/\"> WordPress Beta Tester</a> plugin on a WordPress install. (Select the “Bleeding edge” channel and “Beta/RC Only” stream.)</td></tr><tr><td><strong>Direct Download</strong></td><td>Download the <a href=\"https://wordpress.org/wordpress-6.8-beta2.zip\">Beta 2 version (zip)</a> and install it on a WordPress website.</td></tr><tr><td><strong>Command Line</strong></td><td>Use this<a href=\"https://make.wordpress.org/cli/\"> WP-CLI</a> command: <code>wp core update --version=6.8-beta2</code></td></tr><tr><td><strong>WordPress Playground</strong></td><td>Use the <a href=\"https://playground.wordpress.net/?php=8.0&amp;wp=beta&amp;networking=no&amp;language=&amp;multisite=no&amp;random=y4q1rn88xn\">6.8 Beta 2 WordPress Playground instance</a> to test the software directly in your browser.&nbsp; No setup is required–just click and go!&nbsp;</td></tr></tbody></table></figure>\n\n\n\n<p>The current target date for the final release of WordPress 6.8 is <strong>April 15, 2025</strong>. Get an overview of the <a href=\"https://make.wordpress.org/core/6-8/\">6.8 release cycle</a>, and check the<a href=\"https://make.wordpress.org/core/\"> Make WordPress Core blog</a> for <a href=\"https://make.wordpress.org/core/tag/6-8/\">6.8-related posts</a> in the coming weeks for more information.</p>\n\n\n\n<p><strong>Catch up on what’s new in WordPress 6.8</strong>: <a href=\"https://wordpress.org/news/2025/03/wordpress-6-8-beta-1/\">Read the Beta 1 announcement</a> for details and highlights.</p>\n\n\n\n<h2 class=\"wp-block-heading\">How to test this release</h2>\n\n\n\n<p>Your help testing the WordPress 6.8 Beta 2 version is key to ensuring everything in the release is the best it can be. While testing the upgrade process is essential, trying out new features is equally important.&nbsp; <a href=\"https://make.wordpress.org/test/2025/03/04/help-test-wordpress-6-8/\">This detailed guide</a> will walk you through testing features in WordPress 6.8.</p>\n\n\n\n<p>If you encounter an issue, please report it to the<a href=\"https://wordpress.org/support/forum/alphabeta/\"> Alpha/Beta area</a> of the support forums or directly to<a href=\"https://core.trac.wordpress.org/newticket\"> WordPress Trac</a> if you are comfortable writing a reproducible bug report. You can also check your issue against a list of<a href=\"https://core.trac.wordpress.org/tickets/major\"> known bugs</a>.</p>\n\n\n\n<p>Curious about testing releases in general? Follow along with the<a href=\"https://make.wordpress.org/test/\"> testing initiatives in Make Core</a> and join the<a href=\"https://wordpress.slack.com/messages/core-test/\"> #core-test channel</a> on<a href=\"https://wordpress.slack.com/\"> Making WordPress Slack</a>.</p>\n\n\n\n<h2 class=\"wp-block-heading\">Vulnerability bounty doubles during Beta/RC</h2>\n\n\n\n<p>Between Beta 1, released on March 4, 2025, and the final Release Candidate (RC) scheduled for April 8, 2025, the<a href=\"https://make.wordpress.org/core/handbook/testing/reporting-security-vulnerabilities/#enhanced-bounty-rewards-during-beta-and-release-candidate-phases\"> monetary reward for reporting new, unreleased security vulnerabilities</a> is doubled. Please follow responsible disclosure practices as detailed in the project’s security practices and policies outlined on the<a href=\"https://hackerone.com/wordpress\"> HackerOne page</a> and in the<a href=\"https://wordpress.org/about/security/\"> security white paper</a>.</p>\n\n\n\n<h2 class=\"wp-block-heading\">Beta 2 updates and highlights</h2>\n\n\n\n<p>WordPress 6.8 Beta 2 contains more than 14 Editor updates and fixes since the Beta 1 release, including 21 tickets for WordPress core.</p>\n\n\n\n<p>Each beta cycle focuses on bug fixes; more are on the way with your help through testing. You can browse the technical details for all issues addressed since Beta 1 using these links:</p>\n\n\n\n<ul class=\"wp-block-list\">\n<li><a href=\"https://github.com/WordPress/gutenberg/commits/wp/6.8?since=2025-03-05&amp;until=2025-03-10\">GitHub commits for 6.8</a> since March 4, 2025</li>\n\n\n\n<li><a href=\"https://core.trac.wordpress.org/query?status=closed&amp;changetime=03%2F05%2F2025..03%2F10%2F2025&amp;milestone=6.8&amp;col=id&amp;col=milestone&amp;col=owner&amp;col=type&amp;col=priority&amp;order=id\">Closed Trac tickets</a> since March 4, 2025</li>\n</ul>\n\n\n\n<h2 class=\"wp-block-heading\">A Beta 2 haiku</h2>\n\n\n\n<p>Second wave refines,<br>Lines of code like rivers flow,<br>WordPress finds its form.</p>\n\n\n\n<p class=\"has-text-align-right\"><em>Props to <a href=\'https://profiles.wordpress.org/ankitkumarshah/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>ankitkumarshah</a> <a href=\'https://profiles.wordpress.org/vgnavada/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>vgnavada</a> <a href=\'https://profiles.wordpress.org/krupajnanda/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>krupajnanda</a> <a href=\'https://profiles.wordpress.org/michelleames/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>michelleames</a> <a href=\'https://profiles.wordpress.org/audrasjb/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>audrasjb</a> <a href=\'https://profiles.wordpress.org/marybaum/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>marybaum</a> <a href=\'https://profiles.wordpress.org/ecgan/\' class=\'mention\'><span class=\'mentions-prefix\'>@</span>ecgan</a></em> <em>for proofreading and review.</em></p>\n\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:30:\"com-wordpress:feed-additions:1\";a:1:{s:7:\"post-id\";a:1:{i:0;a:5:{s:4:\"data\";s:5:\"18619\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}}}}}s:27:\"http://www.w3.org/2005/Atom\";a:1:{s:4:\"link\";a:1:{i:0;a:5:{s:4:\"data\";s:0:\"\";s:7:\"attribs\";a:1:{s:0:\"\";a:3:{s:4:\"href\";s:32:\"https://wordpress.org/news/feed/\";s:3:\"rel\";s:4:\"self\";s:4:\"type\";s:19:\"application/rss+xml\";}}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:44:\"http://purl.org/rss/1.0/modules/syndication/\";a:2:{s:12:\"updatePeriod\";a:1:{i:0;a:5:{s:4:\"data\";s:9:\"\n	hourly	\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}s:15:\"updateFrequency\";a:1:{i:0;a:5:{s:4:\"data\";s:4:\"\n	1	\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}s:30:\"com-wordpress:feed-additions:1\";a:1:{s:4:\"site\";a:1:{i:0;a:5:{s:4:\"data\";s:8:\"14607090\";s:7:\"attribs\";a:0:{}s:8:\"xml_base\";s:0:\"\";s:17:\"xml_base_explicit\";b:0;s:8:\"xml_lang\";s:0:\"\";}}}}}}}}}}}}s:4:\"type\";i:128;s:7:\"headers\";O:48:\"WpOrg\\Requests\\Utility\\CaseInsensitiveDictionary\":1:{s:7:\"\0*\0data\";a:12:{s:6:\"server\";s:5:\"nginx\";s:4:\"date\";s:29:\"Mon, 26 May 2025 19:06:45 GMT\";s:12:\"content-type\";s:34:\"application/rss+xml; charset=UTF-8\";s:4:\"vary\";s:37:\"Accept-Encoding, accept, content-type\";s:25:\"strict-transport-security\";s:12:\"max-age=3600\";s:6:\"x-olaf\";s:3:\"⛄\";s:13:\"last-modified\";s:29:\"Wed, 07 May 2025 14:01:21 GMT\";s:4:\"link\";s:63:\"<https://wordpress.org/news/wp-json/>; rel=\"https://api.w.org/\"\";s:15:\"x-frame-options\";s:10:\"SAMEORIGIN\";s:16:\"content-encoding\";s:4:\"gzip\";s:7:\"alt-svc\";s:19:\"h3=\":443\"; ma=86400\";s:4:\"x-nc\";s:9:\"HIT ord 1\";}}s:5:\"build\";i:1727736619;s:21:\"cache_expiration_time\";i:1748329605;s:23:\"__cache_expiration_time\";i:1748329605;}','off');
INSERT INTO `wp_options` VALUES (142,'_transient_timeout_feed_mod_9bbd59226dc36b9b26cd43f15694c5c3','1748329605','off');
INSERT INTO `wp_options` VALUES (143,'_transient_feed_mod_9bbd59226dc36b9b26cd43f15694c5c3','1748286405','off');
INSERT INTO `wp_options` VALUES (144,'_transient_timeout_dash_v2_88ae138922fe95674369b1cb3d215a2b','1748329606','off');
INSERT INTO `wp_options` VALUES (145,'_transient_dash_v2_88ae138922fe95674369b1cb3d215a2b','<div class=\"rss-widget\"><ul><li><a class=\'rsswidget\' href=\'https://wordpress.org/news/2025/05/wordpress-campus-connect-expands/\'>WordPress Campus Connect Expands</a></li><li><a class=\'rsswidget\' href=\'https://wordpress.org/news/2025/04/wordpress-6-8-1-maintenance-release/\'>WordPress 6.8.1 Maintenance Release</a></li></ul></div><div class=\"rss-widget\"><p><strong>RSS Error:</strong> https://planet.wordpress.org/feed/ is invalid XML, likely due to invalid characters. XML error: &gt; required at line 6, column 24</p></div>','off');
INSERT INTO `wp_options` VALUES (148,'finished_updating_comment_type','1','auto');
INSERT INTO `wp_options` VALUES (157,'_site_transient_wp_plugin_dependencies_plugin_data','a:0:{}','off');
INSERT INTO `wp_options` VALUES (158,'recently_activated','a:0:{}','off');
INSERT INTO `wp_options` VALUES (159,'_site_transient_timeout_poptags_40cd750bba9870f18aada2478b24840a','1748302502','off');
INSERT INTO `wp_options` VALUES (160,'_site_transient_poptags_40cd750bba9870f18aada2478b24840a','O:8:\"stdClass\":100:{s:11:\"woocommerce\";a:3:{s:4:\"name\";s:11:\"woocommerce\";s:4:\"slug\";s:11:\"woocommerce\";s:5:\"count\";i:7066;}s:6:\"widget\";a:3:{s:4:\"name\";s:6:\"widget\";s:4:\"slug\";s:6:\"widget\";s:5:\"count\";i:4889;}s:4:\"post\";a:3:{s:4:\"name\";s:4:\"post\";s:4:\"slug\";s:4:\"post\";s:5:\"count\";i:2799;}s:5:\"admin\";a:3:{s:4:\"name\";s:5:\"admin\";s:4:\"slug\";s:5:\"admin\";s:5:\"count\";i:2711;}s:5:\"posts\";a:3:{s:4:\"name\";s:5:\"posts\";s:4:\"slug\";s:5:\"posts\";s:5:\"count\";i:2114;}s:9:\"shortcode\";a:3:{s:4:\"name\";s:9:\"shortcode\";s:4:\"slug\";s:9:\"shortcode\";s:5:\"count\";i:2038;}s:8:\"comments\";a:3:{s:4:\"name\";s:8:\"comments\";s:4:\"slug\";s:8:\"comments\";s:5:\"count\";i:1950;}s:3:\"seo\";a:3:{s:4:\"name\";s:3:\"seo\";s:4:\"slug\";s:3:\"seo\";s:5:\"count\";i:1903;}s:6:\"images\";a:3:{s:4:\"name\";s:6:\"images\";s:4:\"slug\";s:6:\"images\";s:5:\"count\";i:1610;}s:9:\"ecommerce\";a:3:{s:4:\"name\";s:9:\"ecommerce\";s:4:\"slug\";s:9:\"ecommerce\";s:5:\"count\";i:1609;}s:5:\"image\";a:3:{s:4:\"name\";s:5:\"image\";s:4:\"slug\";s:5:\"image\";s:5:\"count\";i:1602;}s:6:\"google\";a:3:{s:4:\"name\";s:6:\"google\";s:4:\"slug\";s:6:\"google\";s:5:\"count\";i:1555;}s:7:\"twitter\";a:3:{s:4:\"name\";s:7:\"twitter\";s:4:\"slug\";s:7:\"twitter\";s:5:\"count\";i:1513;}s:8:\"facebook\";a:3:{s:4:\"name\";s:8:\"facebook\";s:4:\"slug\";s:8:\"facebook\";s:5:\"count\";i:1507;}s:5:\"email\";a:3:{s:4:\"name\";s:5:\"email\";s:4:\"slug\";s:5:\"email\";s:5:\"count\";i:1468;}s:7:\"gallery\";a:3:{s:4:\"name\";s:7:\"gallery\";s:4:\"slug\";s:7:\"gallery\";s:5:\"count\";i:1335;}s:7:\"sidebar\";a:3:{s:4:\"name\";s:7:\"sidebar\";s:4:\"slug\";s:7:\"sidebar\";s:5:\"count\";i:1317;}s:8:\"security\";a:3:{s:4:\"name\";s:8:\"security\";s:4:\"slug\";s:8:\"security\";s:5:\"count\";i:1239;}s:5:\"login\";a:3:{s:4:\"name\";s:5:\"login\";s:4:\"slug\";s:5:\"login\";s:5:\"count\";i:1226;}s:6:\"social\";a:3:{s:4:\"name\";s:6:\"social\";s:4:\"slug\";s:6:\"social\";s:5:\"count\";i:1175;}s:4:\"page\";a:3:{s:4:\"name\";s:4:\"page\";s:4:\"slug\";s:4:\"page\";s:5:\"count\";i:1160;}s:10:\"e-commerce\";a:3:{s:4:\"name\";s:10:\"e-commerce\";s:4:\"slug\";s:10:\"e-commerce\";s:5:\"count\";i:1041;}s:5:\"video\";a:3:{s:4:\"name\";s:5:\"video\";s:4:\"slug\";s:5:\"video\";s:5:\"count\";i:1039;}s:7:\"payment\";a:3:{s:4:\"name\";s:7:\"payment\";s:4:\"slug\";s:7:\"payment\";s:5:\"count\";i:1035;}s:5:\"block\";a:3:{s:4:\"name\";s:5:\"block\";s:4:\"slug\";s:5:\"block\";s:5:\"count\";i:1006;}s:9:\"analytics\";a:3:{s:4:\"name\";s:9:\"analytics\";s:4:\"slug\";s:9:\"analytics\";s:5:\"count\";i:999;}s:7:\"widgets\";a:3:{s:4:\"name\";s:7:\"widgets\";s:4:\"slug\";s:7:\"widgets\";s:5:\"count\";i:999;}s:9:\"gutenberg\";a:3:{s:4:\"name\";s:9:\"gutenberg\";s:4:\"slug\";s:9:\"gutenberg\";s:5:\"count\";i:972;}s:6:\"slider\";a:3:{s:4:\"name\";s:6:\"slider\";s:4:\"slug\";s:6:\"slider\";s:5:\"count\";i:930;}s:7:\"content\";a:3:{s:4:\"name\";s:7:\"content\";s:4:\"slug\";s:7:\"content\";s:5:\"count\";i:926;}s:5:\"links\";a:3:{s:4:\"name\";s:5:\"links\";s:4:\"slug\";s:5:\"links\";s:5:\"count\";i:921;}s:15:\"payment-gateway\";a:3:{s:4:\"name\";s:15:\"payment gateway\";s:4:\"slug\";s:15:\"payment-gateway\";s:5:\"count\";i:908;}s:4:\"spam\";a:3:{s:4:\"name\";s:4:\"spam\";s:4:\"slug\";s:4:\"spam\";s:5:\"count\";i:907;}s:4:\"form\";a:3:{s:4:\"name\";s:4:\"form\";s:4:\"slug\";s:4:\"form\";s:5:\"count\";i:903;}s:9:\"elementor\";a:3:{s:4:\"name\";s:9:\"elementor\";s:4:\"slug\";s:9:\"elementor\";s:5:\"count\";i:902;}s:5:\"media\";a:3:{s:4:\"name\";s:5:\"media\";s:4:\"slug\";s:5:\"media\";s:5:\"count\";i:852;}s:6:\"search\";a:3:{s:4:\"name\";s:6:\"search\";s:4:\"slug\";s:6:\"search\";s:5:\"count\";i:833;}s:6:\"editor\";a:3:{s:4:\"name\";s:6:\"editor\";s:4:\"slug\";s:6:\"editor\";s:5:\"count\";i:802;}s:10:\"buddypress\";a:3:{s:4:\"name\";s:10:\"buddypress\";s:4:\"slug\";s:10:\"buddypress\";s:5:\"count\";i:782;}s:12:\"contact-form\";a:3:{s:4:\"name\";s:12:\"contact form\";s:4:\"slug\";s:12:\"contact-form\";s:5:\"count\";i:771;}s:4:\"menu\";a:3:{s:4:\"name\";s:4:\"menu\";s:4:\"slug\";s:4:\"menu\";s:5:\"count\";i:763;}s:3:\"rss\";a:3:{s:4:\"name\";s:3:\"rss\";s:4:\"slug\";s:3:\"rss\";s:5:\"count\";i:758;}s:5:\"pages\";a:3:{s:4:\"name\";s:5:\"pages\";s:4:\"slug\";s:5:\"pages\";s:5:\"count\";i:748;}s:5:\"embed\";a:3:{s:4:\"name\";s:5:\"embed\";s:4:\"slug\";s:5:\"embed\";s:5:\"count\";i:737;}s:4:\"feed\";a:3:{s:4:\"name\";s:4:\"feed\";s:4:\"slug\";s:4:\"feed\";s:5:\"count\";i:711;}s:8:\"category\";a:3:{s:4:\"name\";s:8:\"category\";s:4:\"slug\";s:8:\"category\";s:5:\"count\";i:705;}s:4:\"ajax\";a:3:{s:4:\"name\";s:4:\"ajax\";s:4:\"slug\";s:4:\"ajax\";s:5:\"count\";i:679;}s:7:\"youtube\";a:3:{s:4:\"name\";s:7:\"youtube\";s:4:\"slug\";s:7:\"youtube\";s:5:\"count\";i:672;}s:6:\"jquery\";a:3:{s:4:\"name\";s:6:\"jquery\";s:4:\"slug\";s:6:\"jquery\";s:5:\"count\";i:665;}s:3:\"api\";a:3:{s:4:\"name\";s:3:\"api\";s:4:\"slug\";s:3:\"api\";s:5:\"count\";i:657;}s:8:\"shipping\";a:3:{s:4:\"name\";s:8:\"shipping\";s:4:\"slug\";s:8:\"shipping\";s:5:\"count\";i:641;}s:9:\"affiliate\";a:3:{s:4:\"name\";s:9:\"affiliate\";s:4:\"slug\";s:9:\"affiliate\";s:5:\"count\";i:634;}s:3:\"css\";a:3:{s:4:\"name\";s:3:\"css\";s:4:\"slug\";s:3:\"css\";s:5:\"count\";i:633;}s:4:\"chat\";a:3:{s:4:\"name\";s:4:\"chat\";s:4:\"slug\";s:4:\"chat\";s:5:\"count\";i:629;}s:9:\"marketing\";a:3:{s:4:\"name\";s:9:\"marketing\";s:4:\"slug\";s:9:\"marketing\";s:5:\"count\";i:625;}s:11:\"performance\";a:3:{s:4:\"name\";s:11:\"performance\";s:4:\"slug\";s:11:\"performance\";s:5:\"count\";i:623;}s:9:\"dashboard\";a:3:{s:4:\"name\";s:9:\"dashboard\";s:4:\"slug\";s:9:\"dashboard\";s:5:\"count\";i:623;}s:4:\"link\";a:3:{s:4:\"name\";s:4:\"link\";s:4:\"slug\";s:4:\"link\";s:5:\"count\";i:618;}s:10:\"responsive\";a:3:{s:4:\"name\";s:10:\"responsive\";s:4:\"slug\";s:10:\"responsive\";s:5:\"count\";i:616;}s:10:\"javascript\";a:3:{s:4:\"name\";s:10:\"javascript\";s:4:\"slug\";s:10:\"javascript\";s:5:\"count\";i:614;}s:5:\"share\";a:3:{s:4:\"name\";s:5:\"share\";s:4:\"slug\";s:5:\"share\";s:5:\"count\";i:609;}s:14:\"contact-form-7\";a:3:{s:4:\"name\";s:14:\"contact form 7\";s:4:\"slug\";s:14:\"contact-form-7\";s:5:\"count\";i:606;}s:7:\"contact\";a:3:{s:4:\"name\";s:7:\"contact\";s:4:\"slug\";s:7:\"contact\";s:5:\"count\";i:595;}s:6:\"events\";a:3:{s:4:\"name\";s:6:\"events\";s:4:\"slug\";s:6:\"events\";s:5:\"count\";i:587;}s:3:\"ads\";a:3:{s:4:\"name\";s:3:\"ads\";s:4:\"slug\";s:3:\"ads\";s:5:\"count\";i:585;}s:5:\"forms\";a:3:{s:4:\"name\";s:5:\"forms\";s:4:\"slug\";s:5:\"forms\";s:5:\"count\";i:583;}s:6:\"custom\";a:3:{s:4:\"name\";s:6:\"custom\";s:4:\"slug\";s:6:\"custom\";s:5:\"count\";i:578;}s:7:\"comment\";a:3:{s:4:\"name\";s:7:\"comment\";s:4:\"slug\";s:7:\"comment\";s:5:\"count\";i:577;}s:5:\"theme\";a:3:{s:4:\"name\";s:5:\"theme\";s:4:\"slug\";s:5:\"theme\";s:5:\"count\";i:574;}s:6:\"blocks\";a:3:{s:4:\"name\";s:6:\"blocks\";s:4:\"slug\";s:6:\"blocks\";s:5:\"count\";i:573;}s:10:\"categories\";a:3:{s:4:\"name\";s:10:\"categories\";s:4:\"slug\";s:10:\"categories\";s:5:\"count\";i:566;}s:5:\"popup\";a:3:{s:4:\"name\";s:5:\"popup\";s:4:\"slug\";s:5:\"popup\";s:5:\"count\";i:558;}s:8:\"calendar\";a:3:{s:4:\"name\";s:8:\"calendar\";s:4:\"slug\";s:8:\"calendar\";s:5:\"count\";i:551;}s:6:\"button\";a:3:{s:4:\"name\";s:6:\"button\";s:4:\"slug\";s:6:\"button\";s:5:\"count\";i:547;}s:8:\"payments\";a:3:{s:4:\"name\";s:8:\"payments\";s:4:\"slug\";s:8:\"payments\";s:5:\"count\";i:536;}s:4:\"user\";a:3:{s:4:\"name\";s:4:\"user\";s:4:\"slug\";s:4:\"user\";s:5:\"count\";i:531;}s:4:\"tags\";a:3:{s:4:\"name\";s:4:\"tags\";s:4:\"slug\";s:4:\"tags\";s:5:\"count\";i:526;}s:5:\"users\";a:3:{s:4:\"name\";s:5:\"users\";s:4:\"slug\";s:5:\"users\";s:5:\"count\";i:523;}s:10:\"newsletter\";a:3:{s:4:\"name\";s:10:\"newsletter\";s:4:\"slug\";s:10:\"newsletter\";s:5:\"count\";i:510;}s:6:\"mobile\";a:3:{s:4:\"name\";s:6:\"mobile\";s:4:\"slug\";s:6:\"mobile\";s:5:\"count\";i:507;}s:10:\"navigation\";a:3:{s:4:\"name\";s:10:\"navigation\";s:4:\"slug\";s:10:\"navigation\";s:5:\"count\";i:501;}s:2:\"ai\";a:3:{s:4:\"name\";s:2:\"AI\";s:4:\"slug\";s:2:\"ai\";s:5:\"count\";i:500;}s:12:\"social-media\";a:3:{s:4:\"name\";s:12:\"social media\";s:4:\"slug\";s:12:\"social-media\";s:5:\"count\";i:486;}s:8:\"tracking\";a:3:{s:4:\"name\";s:8:\"tracking\";s:4:\"slug\";s:8:\"tracking\";s:5:\"count\";i:478;}s:5:\"stats\";a:3:{s:4:\"name\";s:5:\"stats\";s:4:\"slug\";s:5:\"stats\";s:5:\"count\";i:468;}s:8:\"checkout\";a:3:{s:4:\"name\";s:8:\"checkout\";s:4:\"slug\";s:8:\"checkout\";s:5:\"count\";i:463;}s:12:\"notification\";a:3:{s:4:\"name\";s:12:\"notification\";s:4:\"slug\";s:12:\"notification\";s:5:\"count\";i:461;}s:7:\"gateway\";a:3:{s:4:\"name\";s:7:\"gateway\";s:4:\"slug\";s:7:\"gateway\";s:5:\"count\";i:461;}s:10:\"statistics\";a:3:{s:4:\"name\";s:10:\"statistics\";s:4:\"slug\";s:10:\"statistics\";s:5:\"count\";i:460;}s:9:\"slideshow\";a:3:{s:4:\"name\";s:9:\"slideshow\";s:4:\"slug\";s:9:\"slideshow\";s:5:\"count\";i:457;}s:8:\"redirect\";a:3:{s:4:\"name\";s:8:\"redirect\";s:4:\"slug\";s:8:\"redirect\";s:5:\"count\";i:457;}s:5:\"photo\";a:3:{s:4:\"name\";s:5:\"photo\";s:4:\"slug\";s:5:\"photo\";s:5:\"count\";i:455;}s:6:\"import\";a:3:{s:4:\"name\";s:6:\"import\";s:4:\"slug\";s:6:\"import\";s:5:\"count\";i:449;}s:4:\"news\";a:3:{s:4:\"name\";s:4:\"news\";s:4:\"slug\";s:4:\"news\";s:5:\"count\";i:433;}s:5:\"cache\";a:3:{s:4:\"name\";s:5:\"cache\";s:4:\"slug\";s:5:\"cache\";s:5:\"count\";i:430;}s:6:\"photos\";a:3:{s:4:\"name\";s:6:\"photos\";s:4:\"slug\";s:6:\"photos\";s:5:\"count\";i:430;}s:4:\"code\";a:3:{s:4:\"name\";s:4:\"code\";s:4:\"slug\";s:4:\"code\";s:5:\"count\";i:420;}s:10:\"shortcodes\";a:3:{s:4:\"name\";s:10:\"shortcodes\";s:4:\"slug\";s:10:\"shortcodes\";s:5:\"count\";i:419;}s:7:\"plugins\";a:3:{s:4:\"name\";s:7:\"plugins\";s:4:\"slug\";s:7:\"plugins\";s:5:\"count\";i:411;}s:9:\"multisite\";a:3:{s:4:\"name\";s:9:\"multisite\";s:4:\"slug\";s:9:\"multisite\";s:5:\"count\";i:409;}}','off');
INSERT INTO `wp_options` VALUES (163,'_site_transient_timeout_theme_roots','1748293633','off');
INSERT INTO `wp_options` VALUES (164,'_site_transient_theme_roots','a:3:{s:16:\"twentytwentyfive\";s:7:\"/themes\";s:16:\"twentytwentyfour\";s:7:\"/themes\";s:17:\"twentytwentythree\";s:7:\"/themes\";}','off');
INSERT INTO `wp_options` VALUES (166,'_site_transient_update_plugins','O:8:\"stdClass\":5:{s:12:\"last_checked\";i:1748291852;s:8:\"response\";a:0:{}s:12:\"translations\";a:0:{}s:9:\"no_update\";a:1:{s:51:\"all-in-one-wp-migration/all-in-one-wp-migration.php\";O:8:\"stdClass\":10:{s:2:\"id\";s:37:\"w.org/plugins/all-in-one-wp-migration\";s:4:\"slug\";s:23:\"all-in-one-wp-migration\";s:6:\"plugin\";s:51:\"all-in-one-wp-migration/all-in-one-wp-migration.php\";s:11:\"new_version\";s:4:\"7.94\";s:3:\"url\";s:54:\"https://wordpress.org/plugins/all-in-one-wp-migration/\";s:7:\"package\";s:71:\"https://downloads.wordpress.org/plugin/all-in-one-wp-migration.7.94.zip\";s:5:\"icons\";a:2:{s:2:\"2x\";s:76:\"https://ps.w.org/all-in-one-wp-migration/assets/icon-256x256.png?rev=2458334\";s:2:\"1x\";s:76:\"https://ps.w.org/all-in-one-wp-migration/assets/icon-128x128.png?rev=2458334\";}s:7:\"banners\";a:2:{s:2:\"2x\";s:79:\"https://ps.w.org/all-in-one-wp-migration/assets/banner-1544x500.png?rev=3264554\";s:2:\"1x\";s:78:\"https://ps.w.org/all-in-one-wp-migration/assets/banner-772x250.png?rev=3264554\";}s:11:\"banners_rtl\";a:0:{}s:8:\"requires\";s:3:\"3.3\";}}s:7:\"checked\";a:1:{s:51:\"all-in-one-wp-migration/all-in-one-wp-migration.php\";s:4:\"7.94\";}}','off');
INSERT INTO `wp_options` VALUES (167,'ai1wm_secret_key','z19mguztw8NI','auto');
INSERT INTO `wp_options` VALUES (168,'theme_mods_twentytwentyfive','a:1:{s:18:\"custom_css_post_id\";i:-1;}','auto');
INSERT INTO `wp_options` VALUES (169,'_transient_wp_styles_for_blocks','a:2:{s:4:\"hash\";s:32:\"995febb4f4fef6e5500e6199916c745b\";s:6:\"blocks\";a:52:{s:11:\"core/button\";s:0:\"\";s:14:\"core/site-logo\";s:0:\"\";s:18:\"core/post-template\";s:0:\"\";s:12:\"core/columns\";s:769:\":root :where(.wp-block-columns-is-layout-flow) > :first-child{margin-block-start: 0;}:root :where(.wp-block-columns-is-layout-flow) > :last-child{margin-block-end: 0;}:root :where(.wp-block-columns-is-layout-flow) > *{margin-block-start: var(--wp--preset--spacing--50);margin-block-end: 0;}:root :where(.wp-block-columns-is-layout-constrained) > :first-child{margin-block-start: 0;}:root :where(.wp-block-columns-is-layout-constrained) > :last-child{margin-block-end: 0;}:root :where(.wp-block-columns-is-layout-constrained) > *{margin-block-start: var(--wp--preset--spacing--50);margin-block-end: 0;}:root :where(.wp-block-columns-is-layout-flex){gap: var(--wp--preset--spacing--50);}:root :where(.wp-block-columns-is-layout-grid){gap: var(--wp--preset--spacing--50);}\";s:14:\"core/pullquote\";s:306:\":root :where(.wp-block-pullquote){font-size: var(--wp--preset--font-size--xx-large);font-weight: 300;line-height: 1.2;padding-top: var(--wp--preset--spacing--30);padding-bottom: var(--wp--preset--spacing--30);}:root :where(.wp-block-pullquote p:last-of-type){margin-bottom: var(--wp--preset--spacing--30);}\";s:32:\"c48738dcb285a3f6ab83acff204fc486\";s:106:\":root :where(.wp-block-pullquote cite){font-size: var(--wp--preset--font-size--small);font-style: normal;}\";s:11:\"core/avatar\";s:57:\":root :where(.wp-block-avatar img){border-radius: 100px;}\";s:12:\"core/buttons\";s:665:\":root :where(.wp-block-buttons-is-layout-flow) > :first-child{margin-block-start: 0;}:root :where(.wp-block-buttons-is-layout-flow) > :last-child{margin-block-end: 0;}:root :where(.wp-block-buttons-is-layout-flow) > *{margin-block-start: 16px;margin-block-end: 0;}:root :where(.wp-block-buttons-is-layout-constrained) > :first-child{margin-block-start: 0;}:root :where(.wp-block-buttons-is-layout-constrained) > :last-child{margin-block-end: 0;}:root :where(.wp-block-buttons-is-layout-constrained) > *{margin-block-start: 16px;margin-block-end: 0;}:root :where(.wp-block-buttons-is-layout-flex){gap: 16px;}:root :where(.wp-block-buttons-is-layout-grid){gap: 16px;}\";s:9:\"core/code\";s:427:\":root :where(.wp-block-code){background-color: var(--wp--preset--color--accent-5);color: var(--wp--preset--color--contrast);font-family: var(--wp--preset--font-family--fira-code);font-size: var(--wp--preset--font-size--medium);font-weight: 300;padding-top: var(--wp--preset--spacing--40);padding-right: var(--wp--preset--spacing--40);padding-bottom: var(--wp--preset--spacing--40);padding-left: var(--wp--preset--spacing--40);}\";s:24:\"core/comment-author-name\";s:169:\":root :where(.wp-block-comment-author-name){color: var(--wp--preset--color--accent-4);font-size: var(--wp--preset--font-size--small);margin-top: 5px;margin-bottom: 0px;}\";s:32:\"c0002c260f8238c4212f3e4c369fc4f7\";s:143:\":root :where(.wp-block-comment-author-name a:where(:not(.wp-element-button))){color: var(--wp--preset--color--accent-4);text-decoration: none;}\";s:32:\"1e7c38b45537b325dbbbaec17a301676\";s:112:\":root :where(.wp-block-comment-author-name a:where(:not(.wp-element-button)):hover){text-decoration: underline;}\";s:20:\"core/comment-content\";s:178:\":root :where(.wp-block-comment-content){font-size: var(--wp--preset--font-size--medium);margin-top: var(--wp--preset--spacing--30);margin-bottom: var(--wp--preset--spacing--30);}\";s:17:\"core/comment-date\";s:127:\":root :where(.wp-block-comment-date){color: var(--wp--preset--color--contrast);font-size: var(--wp--preset--font-size--small);}\";s:32:\"c83ca7b3e52884c70f7830c54f99b318\";s:114:\":root :where(.wp-block-comment-date a:where(:not(.wp-element-button))){color: var(--wp--preset--color--contrast);}\";s:22:\"core/comment-edit-link\";s:90:\":root :where(.wp-block-comment-edit-link){font-size: var(--wp--preset--font-size--small);}\";s:32:\"41d70710612536a90e368c12bcb0efea\";s:119:\":root :where(.wp-block-comment-edit-link a:where(:not(.wp-element-button))){color: var(--wp--preset--color--contrast);}\";s:23:\"core/comment-reply-link\";s:91:\":root :where(.wp-block-comment-reply-link){font-size: var(--wp--preset--font-size--small);}\";s:32:\"13c96340dbf37700add1f4c5cae19f3e\";s:120:\":root :where(.wp-block-comment-reply-link a:where(:not(.wp-element-button))){color: var(--wp--preset--color--contrast);}\";s:23:\"core/post-comments-form\";s:565:\":root :where(.wp-block-post-comments-form){font-size: var(--wp--preset--font-size--medium);padding-top: var(--wp--preset--spacing--40);padding-bottom: var(--wp--preset--spacing--40);}:root :where(.wp-block-post-comments-form textarea, .wp-block-post-comments-form input:not([type=submit])){border-radius:.25rem; border-color: var(--wp--preset--color--accent-6) !important;}:root :where(.wp-block-post-comments-form input[type=checkbox]){margin:0 .2rem 0 0 !important;}:root :where(.wp-block-post-comments-form label){font-size: var(--wp--preset--font-size--small);}\";s:24:\"core/comments-pagination\";s:182:\":root :where(.wp-block-comments-pagination){font-size: var(--wp--preset--font-size--medium);margin-top: var(--wp--preset--spacing--40);margin-bottom: var(--wp--preset--spacing--40);}\";s:29:\"core/comments-pagination-next\";s:98:\":root :where(.wp-block-comments-pagination-next){font-size: var(--wp--preset--font-size--medium);}\";s:32:\"core/comments-pagination-numbers\";s:101:\":root :where(.wp-block-comments-pagination-numbers){font-size: var(--wp--preset--font-size--medium);}\";s:33:\"core/comments-pagination-previous\";s:102:\":root :where(.wp-block-comments-pagination-previous){font-size: var(--wp--preset--font-size--medium);}\";s:14:\"core/post-date\";s:124:\":root :where(.wp-block-post-date){color: var(--wp--preset--color--accent-4);font-size: var(--wp--preset--font-size--small);}\";s:32:\"ac0d4e00f5ec22d14451759983e5bd43\";s:133:\":root :where(.wp-block-post-date a:where(:not(.wp-element-button))){color: var(--wp--preset--color--accent-4);text-decoration: none;}\";s:32:\"0ae6ffd1b886044c2da62d75d05ab13d\";s:102:\":root :where(.wp-block-post-date a:where(:not(.wp-element-button)):hover){text-decoration: underline;}\";s:25:\"core/post-navigation-link\";s:94:\":root :where(.wp-block-post-navigation-link){font-size: var(--wp--preset--font-size--medium);}\";s:15:\"core/post-terms\";s:158:\":root :where(.wp-block-post-terms){font-size: var(--wp--preset--font-size--small);font-weight: 600;}:root :where(.wp-block-post-terms a){white-space: nowrap;}\";s:15:\"core/post-title\";s:0:\"\";s:32:\"bb496d3fcd9be3502ce57ff8281e5687\";s:92:\":root :where(.wp-block-post-title a:where(:not(.wp-element-button))){text-decoration: none;}\";s:32:\"12380ab98fdc81351bb32a39bbfc9249\";s:103:\":root :where(.wp-block-post-title a:where(:not(.wp-element-button)):hover){text-decoration: underline;}\";s:10:\"core/quote\";s:1315:\":root :where(.wp-block-quote){border-color: currentColor;border-width: 0 0 0 2px;border-style: solid;font-size: var(--wp--preset--font-size--large);font-weight: 300;margin-right: 0;margin-left: 0;padding-top: var(--wp--preset--spacing--30);padding-right: var(--wp--preset--spacing--40);padding-bottom: var(--wp--preset--spacing--30);padding-left: var(--wp--preset--spacing--40);}:root :where(.wp-block-quote-is-layout-flow) > :first-child{margin-block-start: 0;}:root :where(.wp-block-quote-is-layout-flow) > :last-child{margin-block-end: 0;}:root :where(.wp-block-quote-is-layout-flow) > *{margin-block-start: var(--wp--preset--spacing--30);margin-block-end: 0;}:root :where(.wp-block-quote-is-layout-constrained) > :first-child{margin-block-start: 0;}:root :where(.wp-block-quote-is-layout-constrained) > :last-child{margin-block-end: 0;}:root :where(.wp-block-quote-is-layout-constrained) > *{margin-block-start: var(--wp--preset--spacing--30);margin-block-end: 0;}:root :where(.wp-block-quote-is-layout-flex){gap: var(--wp--preset--spacing--30);}:root :where(.wp-block-quote-is-layout-grid){gap: var(--wp--preset--spacing--30);}:root :where(.wp-block-quote.has-text-align-right ){border-width: 0 2px 0 0;}:root :where(.wp-block-quote.has-text-align-center ){border-width: 0;border-inline: 0; padding-inline: 0;}\";s:32:\"1de7a22e22013106efc5be82788cb6c0\";s:176:\":root :where(.wp-block-quote cite){font-size: var(--wp--preset--font-size--small);font-style: normal;font-weight: 300;}:root :where(.wp-block-quote cite sub){font-size: 0.65em}\";s:21:\"core/query-pagination\";s:107:\":root :where(.wp-block-query-pagination){font-size: var(--wp--preset--font-size--medium);font-weight: 500;}\";s:11:\"core/search\";s:380:\":root :where(.wp-block-search .wp-block-search__label, .wp-block-search .wp-block-search__input, .wp-block-search .wp-block-search__button){font-size: var(--wp--preset--font-size--medium);line-height: 1.6;}:root :where(.wp-block-search .wp-block-search__input){border-radius:3.125rem;padding-left:1.5625rem;padding-right:1.5625rem;border-color:var(--wp--preset--color--accent-6);}\";s:32:\"14fa6a3d0cfbde171cbc0fb04aa8a6cf\";s:138:\":root :where(.wp-block-search .wp-element-button,.wp-block-search  .wp-block-button__link){border-radius: 3.125rem;margin-left: 1.125rem;}\";s:32:\"05993ee2f3de94b5d1350998a7e9b6b0\";s:130:\":root :where(.wp-block-search .wp-element-button:hover,.wp-block-search  .wp-block-button__link:hover){border-color: transparent;}\";s:14:\"core/separator\";s:148:\":root :where(.wp-block-separator){border-color: currentColor;border-width: 0 0 1px 0;border-style: solid;color: var(--wp--preset--color--accent-6);}\";s:17:\"core/site-tagline\";s:86:\":root :where(.wp-block-site-tagline){font-size: var(--wp--preset--font-size--medium);}\";s:15:\"core/site-title\";s:75:\":root :where(.wp-block-site-title){font-weight: 700;letter-spacing: -.5px;}\";s:32:\"f513d889cf971b13995cc3fffed2f39b\";s:92:\":root :where(.wp-block-site-title a:where(:not(.wp-element-button))){text-decoration: none;}\";s:32:\"22c37a317cc0ebd50155b5ad78564f37\";s:103:\":root :where(.wp-block-site-title a:where(:not(.wp-element-button)):hover){text-decoration: underline;}\";s:21:\"core/term-description\";s:90:\":root :where(.wp-block-term-description){font-size: var(--wp--preset--font-size--medium);}\";s:15:\"core/navigation\";s:84:\":root :where(.wp-block-navigation){font-size: var(--wp--preset--font-size--medium);}\";s:32:\"25289a01850f5a0264ddb79a9a3baf3d\";s:92:\":root :where(.wp-block-navigation a:where(:not(.wp-element-button))){text-decoration: none;}\";s:32:\"026c04da08398d655a95047f1f235d97\";s:103:\":root :where(.wp-block-navigation a:where(:not(.wp-element-button)):hover){text-decoration: underline;}\";s:9:\"core/list\";s:52:\":root :where(.wp-block-list li){margin-top: 0.5rem;}\";s:12:\"core/heading\";s:0:\"\";s:14:\"core/paragraph\";s:0:\"\";s:10:\"core/group\";s:0:\"\";s:11:\"core/column\";s:0:\"\";}}','on');
INSERT INTO `wp_options` VALUES (170,'ai1wm_status','a:2:{s:4:\"type\";s:8:\"download\";s:7:\"message\";s:387:\"<a href=\"http://satolocphpbackend.local/wp-content/ai1wm-backups/satolocphpbackend-local-20250526-203744-51hl22ofe9gn.wpress\" class=\"ai1wm-button-green ai1wm-emphasize ai1wm-button-download\" title=\"satolocphpbackend.local\" download=\"satolocphpbackend-local-20250526-203744-51hl22ofe9gn.wpress\">\n							<span>Download satolocphpbackend.local</span>\n							<em>Size: 16 MB</em>\n							</a>\";}','auto');
INSERT INTO `wp_options` VALUES (171,'_site_transient_timeout_wp_theme_files_patterns-44cec885efaa77d1bb65cbdf0aa133e3','1748293667','off');
INSERT INTO `wp_options` VALUES (172,'_site_transient_wp_theme_files_patterns-44cec885efaa77d1bb65cbdf0aa133e3','a:2:{s:7:\"version\";s:3:\"1.2\";s:8:\"patterns\";a:98:{s:21:\"banner-about-book.php\";a:4:{s:5:\"title\";s:28:\"Banner with book description\";s:4:\"slug\";s:34:\"twentytwentyfive/banner-about-book\";s:11:\"description\";s:66:\"Banner with book description and accompanying image for promotion.\";s:10:\"categories\";a:1:{i:0;s:6:\"banner\";}}s:28:\"banner-cover-big-heading.php\";a:4:{s:5:\"title\";s:22:\"Cover with big heading\";s:4:\"slug\";s:41:\"twentytwentyfive/banner-cover-big-heading\";s:11:\"description\";s:82:\"A full-width cover section with a large background image and an oversized heading.\";s:10:\"categories\";a:3:{i:0;s:6:\"banner\";i:1;s:5:\"about\";i:2;s:8:\"featured\";}}s:22:\"banner-intro-image.php\";a:4:{s:5:\"title\";s:49:\"Short heading and paragraph and image on the left\";s:4:\"slug\";s:35:\"twentytwentyfive/banner-intro-image\";s:11:\"description\";s:68:\"A Intro pattern with Short heading, paragraph and image on the left.\";s:10:\"categories\";a:2:{i:0;s:6:\"banner\";i:1;s:8:\"featured\";}}s:16:\"banner-intro.php\";a:4:{s:5:\"title\";s:35:\"Intro with left-aligned description\";s:4:\"slug\";s:29:\"twentytwentyfive/banner-intro\";s:11:\"description\";s:66:\"A large left-aligned heading with a brand name emphasized in bold.\";s:10:\"categories\";a:1:{i:0;s:6:\"banner\";}}s:17:\"banner-poster.php\";a:4:{s:5:\"title\";s:19:\"Poster-like section\";s:4:\"slug\";s:30:\"twentytwentyfive/banner-poster\";s:11:\"description\";s:78:\"A section that can be used as a banner or a landing page to announce an event.\";s:10:\"categories\";a:2:{i:0;s:6:\"banner\";i:1;s:5:\"media\";}}s:43:\"banner-with-description-and-images-grid.php\";a:4:{s:5:\"title\";s:39:\"Banner with description and images grid\";s:4:\"slug\";s:47:\"twentytwentyfive/banner-description-images-grid\";s:11:\"description\";s:75:\"A banner with a short paragraph, and two images displayed in a grid layout.\";s:10:\"categories\";a:2:{i:0;s:6:\"banner\";i:1;s:8:\"featured\";}}s:18:\"binding-format.php\";a:4:{s:5:\"title\";s:16:\"Post format name\";s:4:\"slug\";s:31:\"twentytwentyfive/binding-format\";s:11:\"description\";s:75:\"Prints the name of the post format with the help of the Block Bindings API.\";s:10:\"categories\";a:1:{i:0;s:28:\"twentytwentyfive_post-format\";}}s:12:\"comments.php\";a:5:{s:5:\"title\";s:8:\"Comments\";s:4:\"slug\";s:25:\"twentytwentyfive/comments\";s:11:\"description\";s:63:\"Comments area with comments list, pagination, and comment form.\";s:10:\"categories\";a:1:{i:0;s:4:\"text\";}s:10:\"blockTypes\";a:1:{i:0;s:13:\"core/comments\";}}s:32:\"contact-centered-social-link.php\";a:5:{s:5:\"title\";s:30:\"Centered link and social links\";s:4:\"slug\";s:45:\"twentytwentyfive/contact-centered-social-link\";s:11:\"description\";s:73:\"Centered contact section with a prominent message and social media links.\";s:10:\"categories\";a:1:{i:0;s:7:\"contact\";}s:8:\"keywords\";a:3:{i:0;s:7:\"contact\";i:1;s:3:\"faq\";i:2;s:9:\"questions\";}}s:26:\"contact-info-locations.php\";a:6:{s:5:\"title\";s:27:\"Contact, info and locations\";s:4:\"slug\";s:39:\"twentytwentyfive/contact-info-locations\";s:11:\"description\";s:78:\"Contact section with social media links, email, and multiple location details.\";s:13:\"viewportWidth\";i:1400;s:10:\"categories\";a:1:{i:0;s:7:\"contact\";}s:8:\"keywords\";a:2:{i:0;s:7:\"contact\";i:1;s:8:\"location\";}}s:29:\"contact-location-and-link.php\";a:4:{s:5:\"title\";s:25:\"Contact location and link\";s:4:\"slug\";s:42:\"twentytwentyfive/contact-location-and-link\";s:11:\"description\";s:89:\"Contact section with a location address, a directions link, and an image of the location.\";s:10:\"categories\";a:2:{i:0;s:7:\"contact\";i:1;s:8:\"featured\";}}s:18:\"cta-book-links.php\";a:4:{s:5:\"title\";s:30:\"Call to action with book links\";s:4:\"slug\";s:31:\"twentytwentyfive/cta-book-links\";s:11:\"description\";s:74:\"A call to action section with links to get the book in different websites.\";s:10:\"categories\";a:1:{i:0;s:14:\"call-to-action\";}}s:22:\"cta-book-locations.php\";a:4:{s:5:\"title\";s:29:\"Call to action with locations\";s:4:\"slug\";s:35:\"twentytwentyfive/cta-book-locations\";s:11:\"description\";s:82:\"A call to action section with links to get the book in the most popular locations.\";s:10:\"categories\";a:1:{i:0;s:14:\"call-to-action\";}}s:24:\"cta-centered-heading.php\";a:4:{s:5:\"title\";s:16:\"Centered heading\";s:4:\"slug\";s:37:\"twentytwentyfive/cta-centered-heading\";s:11:\"description\";s:53:\"A hero with a centered heading, paragraph and button.\";s:10:\"categories\";a:1:{i:0;s:14:\"call-to-action\";}}s:19:\"cta-events-list.php\";a:4:{s:5:\"title\";s:11:\"Events list\";s:4:\"slug\";s:32:\"twentytwentyfive/cta-events-list\";s:11:\"description\";s:37:\"A list of events with call to action.\";s:10:\"categories\";a:1:{i:0;s:14:\"call-to-action\";}}s:26:\"cta-grid-products-link.php\";a:5:{s:5:\"title\";s:54:\"Call to action with grid layout with products and link\";s:4:\"slug\";s:39:\"twentytwentyfive/cta-grid-products-link\";s:11:\"description\";s:42:\"A call to action featuring product images.\";s:13:\"viewportWidth\";i:1400;s:10:\"categories\";a:2:{i:0;s:14:\"call-to-action\";i:1;s:8:\"featured\";}}s:22:\"cta-heading-search.php\";a:4:{s:5:\"title\";s:23:\"Heading and search form\";s:4:\"slug\";s:35:\"twentytwentyfive/cta-heading-search\";s:11:\"description\";s:54:\"Large heading with a search form for quick navigation.\";s:10:\"categories\";a:1:{i:0;s:14:\"call-to-action\";}}s:18:\"cta-newsletter.php\";a:5:{s:5:\"title\";s:18:\"Newsletter sign-up\";s:4:\"slug\";s:31:\"twentytwentyfive/cta-newsletter\";s:11:\"description\";s:0:\"\";s:10:\"categories\";a:1:{i:0;s:14:\"call-to-action\";}s:8:\"keywords\";a:2:{i:0;s:14:\"call-to-action\";i:1;s:10:\"newsletter\";}}s:15:\"event-3-col.php\";a:5:{s:5:\"title\";s:46:\"Events, 3 columns with event images and titles\";s:4:\"slug\";s:28:\"twentytwentyfive/event-3-col\";s:11:\"description\";s:95:\"A header with title and text and three columns that show 3 events with their images and titles.\";s:10:\"categories\";a:1:{i:0;s:6:\"banner\";}s:8:\"keywords\";a:3:{i:0;s:6:\"events\";i:1;s:7:\"columns\";i:2;s:6:\"images\";}}s:14:\"event-rsvp.php\";a:7:{s:5:\"title\";s:10:\"Event RSVP\";s:4:\"slug\";s:27:\"twentytwentyfive/event-rsvp\";s:11:\"description\";s:64:\"RSVP for an upcoming event with a cover image and event details.\";s:13:\"viewportWidth\";i:1400;s:10:\"categories\";a:1:{i:0;s:14:\"call-to-action\";}s:8:\"keywords\";a:3:{i:0;s:14:\"call-to-action\";i:1;s:4:\"rsvp\";i:2;s:5:\"event\";}s:10:\"blockTypes\";a:1:{i:0;s:17:\"core/post-content\";}}s:18:\"event-schedule.php\";a:5:{s:5:\"title\";s:14:\"Event schedule\";s:4:\"slug\";s:31:\"twentytwentyfive/event-schedule\";s:11:\"description\";s:54:\"A section with specified dates and times for an event.\";s:10:\"categories\";a:1:{i:0;s:5:\"about\";}s:8:\"keywords\";a:4:{i:0;s:6:\"events\";i:1;s:6:\"agenda\";i:2;s:8:\"schedule\";i:3;s:8:\"lectures\";}}s:19:\"footer-centered.php\";a:5:{s:5:\"title\";s:15:\"Centered footer\";s:4:\"slug\";s:32:\"twentytwentyfive/footer-centered\";s:11:\"description\";s:44:\"Footer with centered site title and tagline.\";s:10:\"categories\";a:1:{i:0;s:6:\"footer\";}s:10:\"blockTypes\";a:1:{i:0;s:25:\"core/template-part/footer\";}}s:18:\"footer-columns.php\";a:5:{s:5:\"title\";s:19:\"Footer with columns\";s:4:\"slug\";s:31:\"twentytwentyfive/footer-columns\";s:11:\"description\";s:45:\"Footer columns with title, tagline and links.\";s:10:\"categories\";a:1:{i:0;s:6:\"footer\";}s:10:\"blockTypes\";a:1:{i:0;s:25:\"core/template-part/footer\";}}s:21:\"footer-newsletter.php\";a:5:{s:5:\"title\";s:29:\"Footer with newsletter signup\";s:4:\"slug\";s:34:\"twentytwentyfive/footer-newsletter\";s:11:\"description\";s:51:\"Footer with large site title and newsletter signup.\";s:10:\"categories\";a:1:{i:0;s:6:\"footer\";}s:10:\"blockTypes\";a:1:{i:0;s:25:\"core/template-part/footer\";}}s:17:\"footer-social.php\";a:5:{s:5:\"title\";s:33:\"Centered footer with social links\";s:4:\"slug\";s:30:\"twentytwentyfive/footer-social\";s:11:\"description\";s:49:\"Footer with centered site title and social links.\";s:10:\"categories\";a:1:{i:0;s:6:\"footer\";}s:10:\"blockTypes\";a:1:{i:0;s:25:\"core/template-part/footer\";}}s:10:\"footer.php\";a:5:{s:5:\"title\";s:6:\"Footer\";s:4:\"slug\";s:23:\"twentytwentyfive/footer\";s:11:\"description\";s:51:\"Footer columns with logo, title, tagline and links.\";s:10:\"categories\";a:1:{i:0;s:6:\"footer\";}s:10:\"blockTypes\";a:1:{i:0;s:25:\"core/template-part/footer\";}}s:16:\"format-audio.php\";a:4:{s:5:\"title\";s:12:\"Audio format\";s:4:\"slug\";s:29:\"twentytwentyfive/format-audio\";s:11:\"description\";s:73:\"An audio post format with an image, title, audio player, and description.\";s:10:\"categories\";a:1:{i:0;s:28:\"twentytwentyfive_post-format\";}}s:15:\"format-link.php\";a:4:{s:5:\"title\";s:11:\"Link format\";s:4:\"slug\";s:28:\"twentytwentyfive/format-link\";s:11:\"description\";s:77:\"A link post format with a description and an emphasized link for key content.\";s:10:\"categories\";a:1:{i:0;s:28:\"twentytwentyfive_post-format\";}}s:15:\"grid-videos.php\";a:4:{s:5:\"title\";s:16:\"Grid with videos\";s:4:\"slug\";s:28:\"twentytwentyfive/grid-videos\";s:11:\"description\";s:19:\"A grid with videos.\";s:10:\"categories\";a:1:{i:0;s:5:\"about\";}}s:24:\"grid-with-categories.php\";a:5:{s:5:\"title\";s:20:\"Grid with categories\";s:4:\"slug\";s:37:\"twentytwentyfive/grid-with-categories\";s:11:\"description\";s:41:\"A grid section with different categories.\";s:13:\"viewportWidth\";i:1400;s:10:\"categories\";a:1:{i:0;s:6:\"banner\";}}s:19:\"header-centered.php\";a:5:{s:5:\"title\";s:20:\"Centered site header\";s:4:\"slug\";s:32:\"twentytwentyfive/header-centered\";s:11:\"description\";s:52:\"Site header with centered site title and navigation.\";s:10:\"categories\";a:1:{i:0;s:6:\"header\";}s:10:\"blockTypes\";a:1:{i:0;s:25:\"core/template-part/header\";}}s:18:\"header-columns.php\";a:5:{s:5:\"title\";s:19:\"Header with columns\";s:4:\"slug\";s:31:\"twentytwentyfive/header-columns\";s:11:\"description\";s:54:\"Site header with site title and navigation in columns.\";s:10:\"categories\";a:1:{i:0;s:6:\"header\";}s:10:\"blockTypes\";a:1:{i:0;s:25:\"core/template-part/header\";}}s:22:\"header-large-title.php\";a:5:{s:5:\"title\";s:23:\"Header with large title\";s:4:\"slug\";s:35:\"twentytwentyfive/header-large-title\";s:11:\"description\";s:63:\"Site header with large site title and right-aligned navigation.\";s:10:\"categories\";a:1:{i:0;s:6:\"header\";}s:10:\"blockTypes\";a:1:{i:0;s:25:\"core/template-part/header\";}}s:10:\"header.php\";a:5:{s:5:\"title\";s:6:\"Header\";s:4:\"slug\";s:23:\"twentytwentyfive/header\";s:11:\"description\";s:43:\"Site header with site title and navigation.\";s:10:\"categories\";a:1:{i:0;s:6:\"header\";}s:10:\"blockTypes\";a:1:{i:0;s:25:\"core/template-part/header\";}}s:36:\"heading-and-paragraph-with-image.php\";a:4:{s:5:\"title\";s:45:\"Heading and paragraph with image on the right\";s:4:\"slug\";s:49:\"twentytwentyfive/heading-and-paragraph-with-image\";s:11:\"description\";s:89:\"A two-column section with a heading and paragraph on the left, and an image on the right.\";s:10:\"categories\";a:1:{i:0;s:5:\"about\";}}s:13:\"hero-book.php\";a:5:{s:5:\"title\";s:9:\"Hero book\";s:4:\"slug\";s:26:\"twentytwentyfive/hero-book\";s:11:\"description\";s:66:\"A hero section for the book with a description and pre-order link.\";s:10:\"categories\";a:1:{i:0;s:6:\"banner\";}s:8:\"keywords\";a:3:{i:0;s:7:\"podcast\";i:1;s:4:\"hero\";i:2;s:7:\"stories\";}}s:25:\"hero-full-width-image.php\";a:4:{s:5:\"title\";s:22:\"Hero, full width image\";s:4:\"slug\";s:38:\"twentytwentyfive/hero-full-width-image\";s:11:\"description\";s:68:\"A hero with a full width image, heading, short paragraph and button.\";s:10:\"categories\";a:1:{i:0;s:6:\"banner\";}}s:41:\"hero-overlapped-book-cover-with-links.php\";a:4:{s:5:\"title\";s:38:\"Hero, overlapped book cover with links\";s:4:\"slug\";s:54:\"twentytwentyfive/hero-overlapped-book-cover-with-links\";s:11:\"description\";s:47:\"A hero with an overlapped book cover and links.\";s:10:\"categories\";a:1:{i:0;s:6:\"banner\";}}s:16:\"hero-podcast.php\";a:5:{s:5:\"title\";s:12:\"Hero podcast\";s:4:\"slug\";s:29:\"twentytwentyfive/hero-podcast\";s:11:\"description\";s:0:\"\";s:10:\"categories\";a:1:{i:0;s:6:\"banner\";}s:8:\"keywords\";a:3:{i:0;s:7:\"podcast\";i:1;s:4:\"hero\";i:2;s:7:\"stories\";}}s:14:\"hidden-404.php\";a:4:{s:5:\"title\";s:3:\"404\";s:4:\"slug\";s:27:\"twentytwentyfive/hidden-404\";s:11:\"description\";s:0:\"\";s:8:\"inserter\";b:0;}s:23:\"hidden-blog-heading.php\";a:4:{s:5:\"title\";s:19:\"Hidden blog heading\";s:4:\"slug\";s:36:\"twentytwentyfive/hidden-blog-heading\";s:11:\"description\";s:52:\"Hidden heading for the home page and index template.\";s:8:\"inserter\";b:0;}s:17:\"hidden-search.php\";a:4:{s:5:\"title\";s:6:\"Search\";s:4:\"slug\";s:30:\"twentytwentyfive/hidden-search\";s:11:\"description\";s:0:\"\";s:8:\"inserter\";b:0;}s:18:\"hidden-sidebar.php\";a:4:{s:5:\"title\";s:7:\"Sidebar\";s:4:\"slug\";s:31:\"twentytwentyfive/hidden-sidebar\";s:11:\"description\";s:0:\"\";s:8:\"inserter\";b:0;}s:21:\"hidden-written-by.php\";a:4:{s:5:\"title\";s:10:\"Written by\";s:4:\"slug\";s:34:\"twentytwentyfive/hidden-written-by\";s:11:\"description\";s:0:\"\";s:8:\"inserter\";b:0;}s:9:\"logos.php\";a:4:{s:5:\"title\";s:5:\"Logos\";s:4:\"slug\";s:22:\"twentytwentyfive/logos\";s:11:\"description\";s:77:\"Showcasing the podcast\'s clients with a heading and a series of client logos.\";s:10:\"categories\";a:1:{i:0;s:6:\"banner\";}}s:24:\"media-instagram-grid.php\";a:5:{s:5:\"title\";s:14:\"Instagram grid\";s:4:\"slug\";s:37:\"twentytwentyfive/media-instagram-grid\";s:11:\"description\";s:62:\"A grid section with photos and a link to an Instagram profile.\";s:13:\"viewportWidth\";i:1440;s:10:\"categories\";a:3:{i:0;s:5:\"media\";i:1;s:7:\"gallery\";i:2;s:8:\"featured\";}}s:14:\"more-posts.php\";a:5:{s:5:\"title\";s:10:\"More posts\";s:4:\"slug\";s:27:\"twentytwentyfive/more-posts\";s:11:\"description\";s:45:\"Displays a list of posts with title and date.\";s:10:\"categories\";a:1:{i:0;s:5:\"query\";}s:10:\"blockTypes\";a:1:{i:0;s:10:\"core/query\";}}s:21:\"overlapped-images.php\";a:4:{s:5:\"title\";s:41:\"Overlapping images and paragraph on right\";s:4:\"slug\";s:34:\"twentytwentyfive/overlapped-images\";s:11:\"description\";s:53:\"A section with overlapping images, and a description.\";s:10:\"categories\";a:2:{i:0;s:5:\"about\";i:1;s:8:\"featured\";}}s:22:\"page-business-home.php\";a:8:{s:5:\"title\";s:17:\"Business homepage\";s:4:\"slug\";s:35:\"twentytwentyfive/page-business-home\";s:11:\"description\";s:28:\"A business homepage pattern.\";s:13:\"viewportWidth\";i:1400;s:10:\"categories\";a:2:{i:0;s:21:\"twentytwentyfive_page\";i:1;s:8:\"featured\";}s:8:\"keywords\";a:1:{i:0;s:7:\"starter\";}s:10:\"blockTypes\";a:1:{i:0;s:17:\"core/post-content\";}s:9:\"postTypes\";a:2:{i:0;s:4:\"page\";i:1;s:11:\"wp_template\";}}s:20:\"page-coming-soon.php\";a:8:{s:5:\"title\";s:11:\"Coming soon\";s:4:\"slug\";s:33:\"twentytwentyfive/page-coming-soon\";s:11:\"description\";s:96:\"A full-width cover banner that can be applied to a page or it can work as a single landing page.\";s:13:\"viewportWidth\";i:1400;s:10:\"categories\";a:2:{i:0;s:21:\"twentytwentyfive_page\";i:1;s:8:\"featured\";}s:8:\"keywords\";a:1:{i:0;s:7:\"starter\";}s:10:\"blockTypes\";a:1:{i:0;s:17:\"core/post-content\";}s:9:\"postTypes\";a:2:{i:0;s:4:\"page\";i:1;s:11:\"wp_template\";}}s:15:\"page-cv-bio.php\";a:7:{s:5:\"title\";s:6:\"CV/bio\";s:4:\"slug\";s:28:\"twentytwentyfive/page-cv-bio\";s:11:\"description\";s:36:\"A pattern for a CV/Bio landing page.\";s:13:\"viewportWidth\";i:1400;s:10:\"categories\";a:3:{i:0;s:21:\"twentytwentyfive_page\";i:1;s:5:\"about\";i:2;s:8:\"featured\";}s:8:\"keywords\";a:1:{i:0;s:7:\"starter\";}s:10:\"blockTypes\";a:1:{i:0;s:17:\"core/post-content\";}}s:21:\"page-landing-book.php\";a:8:{s:5:\"title\";s:21:\"Landing page for book\";s:4:\"slug\";s:34:\"twentytwentyfive/page-landing-book\";s:11:\"description\";s:104:\"A landing page for the book with a hero section, pre-order links, locations, FAQs and newsletter signup.\";s:13:\"viewportWidth\";i:1400;s:10:\"categories\";a:2:{i:0;s:21:\"twentytwentyfive_page\";i:1;s:8:\"featured\";}s:8:\"keywords\";a:1:{i:0;s:7:\"starter\";}s:10:\"blockTypes\";a:1:{i:0;s:17:\"core/post-content\";}s:9:\"postTypes\";a:2:{i:0;s:4:\"page\";i:1;s:11:\"wp_template\";}}s:22:\"page-landing-event.php\";a:8:{s:5:\"title\";s:22:\"Landing page for event\";s:4:\"slug\";s:35:\"twentytwentyfive/page-landing-event\";s:11:\"description\";s:87:\"A landing page for the event with a hero section, description, FAQs and call to action.\";s:13:\"viewportWidth\";i:1400;s:10:\"categories\";a:2:{i:0;s:21:\"twentytwentyfive_page\";i:1;s:8:\"featured\";}s:8:\"keywords\";a:1:{i:0;s:7:\"starter\";}s:10:\"blockTypes\";a:1:{i:0;s:17:\"core/post-content\";}s:9:\"postTypes\";a:2:{i:0;s:4:\"page\";i:1;s:11:\"wp_template\";}}s:24:\"page-landing-podcast.php\";a:8:{s:5:\"title\";s:24:\"Landing page for podcast\";s:4:\"slug\";s:37:\"twentytwentyfive/page-landing-podcast\";s:11:\"description\";s:111:\"A landing page for the podcast with a hero section, description, logos, grid with videos and newsletter signup.\";s:13:\"viewportWidth\";i:1400;s:10:\"categories\";a:2:{i:0;s:21:\"twentytwentyfive_page\";i:1;s:8:\"featured\";}s:8:\"keywords\";a:1:{i:0;s:7:\"starter\";}s:10:\"blockTypes\";a:1:{i:0;s:17:\"core/post-content\";}s:9:\"postTypes\";a:2:{i:0;s:4:\"page\";i:1;s:11:\"wp_template\";}}s:50:\"page-link-in-bio-heading-paragraph-links-image.php\";a:7:{s:5:\"title\";s:59:\"Link in bio heading, paragraph, links and full-height image\";s:4:\"slug\";s:63:\"twentytwentyfive/page-link-in-bio-heading-paragraph-links-image\";s:11:\"description\";s:84:\"A link in bio landing page with a heading, paragraph, links and a full height image.\";s:13:\"viewportWidth\";i:1400;s:10:\"categories\";a:3:{i:0;s:21:\"twentytwentyfive_page\";i:1;s:6:\"banner\";i:2;s:8:\"featured\";}s:8:\"keywords\";a:1:{i:0;s:7:\"starter\";}s:10:\"blockTypes\";a:1:{i:0;s:17:\"core/post-content\";}}s:33:\"page-link-in-bio-wide-margins.php\";a:7:{s:5:\"title\";s:48:\"Link in bio with profile, links and wide margins\";s:4:\"slug\";s:46:\"twentytwentyfive/page-link-in-bio-wide-margins\";s:11:\"description\";s:86:\"A link in bio landing page with social links, a profile photo and a brief description.\";s:13:\"viewportWidth\";i:1400;s:10:\"categories\";a:3:{i:0;s:21:\"twentytwentyfive_page\";i:1;s:6:\"banner\";i:2;s:8:\"featured\";}s:8:\"keywords\";a:1:{i:0;s:7:\"starter\";}s:10:\"blockTypes\";a:1:{i:0;s:17:\"core/post-content\";}}s:39:\"page-link-in-bio-with-tight-margins.php\";a:8:{s:5:\"title\";s:30:\"Link in bio with tight margins\";s:4:\"slug\";s:52:\"twentytwentyfive/page-link-in-bio-with-tight-margins\";s:11:\"description\";s:90:\"A full-width, full-height link in bio section with an image, a paragraph and social links.\";s:13:\"viewportWidth\";i:1400;s:10:\"categories\";a:2:{i:0;s:21:\"twentytwentyfive_page\";i:1;s:6:\"banner\";}s:8:\"keywords\";a:1:{i:0;s:7:\"starter\";}s:10:\"blockTypes\";a:1:{i:0;s:17:\"core/post-content\";}s:9:\"postTypes\";a:2:{i:0;s:4:\"page\";i:1;s:11:\"wp_template\";}}s:23:\"page-portfolio-home.php\";a:8:{s:5:\"title\";s:18:\"Portfolio homepage\";s:4:\"slug\";s:36:\"twentytwentyfive/page-portfolio-home\";s:11:\"description\";s:29:\"A portfolio homepage pattern.\";s:13:\"viewportWidth\";i:1400;s:10:\"categories\";a:2:{i:0;s:21:\"twentytwentyfive_page\";i:1;s:5:\"posts\";}s:8:\"keywords\";a:1:{i:0;s:7:\"starter\";}s:10:\"blockTypes\";a:1:{i:0;s:17:\"core/post-content\";}s:9:\"postTypes\";a:2:{i:0;s:4:\"page\";i:1;s:11:\"wp_template\";}}s:18:\"page-shop-home.php\";a:8:{s:5:\"title\";s:13:\"Shop homepage\";s:4:\"slug\";s:31:\"twentytwentyfive/page-shop-home\";s:11:\"description\";s:24:\"A shop homepage pattern.\";s:13:\"viewportWidth\";i:1400;s:10:\"categories\";a:1:{i:0;s:21:\"twentytwentyfive_page\";}s:8:\"keywords\";a:1:{i:0;s:7:\"starter\";}s:10:\"blockTypes\";a:1:{i:0;s:17:\"core/post-content\";}s:9:\"postTypes\";a:2:{i:0;s:4:\"page\";i:1;s:11:\"wp_template\";}}s:19:\"post-navigation.php\";a:5:{s:5:\"title\";s:15:\"Post navigation\";s:4:\"slug\";s:32:\"twentytwentyfive/post-navigation\";s:11:\"description\";s:29:\"Next and previous post links.\";s:10:\"categories\";a:1:{i:0;s:4:\"text\";}s:10:\"blockTypes\";a:1:{i:0;s:25:\"core/post-navigation-link\";}}s:17:\"pricing-2-col.php\";a:5:{s:5:\"title\";s:18:\"Pricing, 2 columns\";s:4:\"slug\";s:30:\"twentytwentyfive/pricing-2-col\";s:11:\"description\";s:88:\"Pricing section with two columns, pricing plan, description, and call-to-action buttons.\";s:13:\"viewportWidth\";i:1400;s:10:\"categories\";a:1:{i:0;s:14:\"call-to-action\";}}s:17:\"pricing-3-col.php\";a:4:{s:5:\"title\";s:18:\"Pricing, 3 columns\";s:4:\"slug\";s:30:\"twentytwentyfive/pricing-3-col\";s:11:\"description\";s:100:\"A three-column boxed pricing table designed to showcase services, descriptions, and pricing options.\";s:10:\"categories\";a:3:{i:0;s:14:\"call-to-action\";i:1;s:6:\"banner\";i:2;s:8:\"services\";}}s:18:\"services-3-col.php\";a:4:{s:5:\"title\";s:19:\"Services, 3 columns\";s:4:\"slug\";s:31:\"twentytwentyfive/services-3-col\";s:11:\"description\";s:56:\"Three columns with images and text to showcase services.\";s:10:\"categories\";a:3:{i:0;s:14:\"call-to-action\";i:1;s:6:\"banner\";i:2;s:8:\"services\";}}s:36:\"services-subscriber-only-section.php\";a:4:{s:5:\"title\";s:33:\"Services, subscriber only section\";s:4:\"slug\";s:49:\"twentytwentyfive/services-subscriber-only-section\";s:11:\"description\";s:72:\"A subscriber-only section highlighting exclusive services and offerings.\";s:10:\"categories\";a:2:{i:0;s:14:\"call-to-action\";i:1;s:8:\"services\";}}s:24:\"services-team-photos.php\";a:4:{s:5:\"title\";s:21:\"Services, team photos\";s:4:\"slug\";s:37:\"twentytwentyfive/services-team-photos\";s:11:\"description\";s:59:\"Display team photos in a services section with grid layout.\";s:10:\"categories\";a:3:{i:0;s:6:\"banner\";i:1;s:14:\"call-to-action\";i:2;s:8:\"featured\";}}s:37:\"template-404-vertical-header-blog.php\";a:5:{s:5:\"title\";s:17:\"Right-aligned 404\";s:4:\"slug\";s:50:\"twentytwentyfive/template-404-vertical-header-blog\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:13:\"templateTypes\";a:1:{i:0;s:3:\"404\";}}s:30:\"template-archive-news-blog.php\";a:6:{s:5:\"title\";s:17:\"News blog archive\";s:4:\"slug\";s:43:\"twentytwentyfive/template-archive-news-blog\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:8:\"inserter\";b:0;s:13:\"templateTypes\";a:1:{i:0;s:7:\"archive\";}}s:31:\"template-archive-photo-blog.php\";a:6:{s:5:\"title\";s:18:\"Photo blog archive\";s:4:\"slug\";s:44:\"twentytwentyfive/template-archive-photo-blog\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:8:\"inserter\";b:0;s:13:\"templateTypes\";a:1:{i:0;s:7:\"archive\";}}s:30:\"template-archive-text-blog.php\";a:6:{s:5:\"title\";s:17:\"Text blog archive\";s:4:\"slug\";s:43:\"twentytwentyfive/template-archive-text-blog\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:8:\"inserter\";b:0;s:13:\"templateTypes\";a:1:{i:0;s:7:\"archive\";}}s:41:\"template-archive-vertical-header-blog.php\";a:6:{s:5:\"title\";s:21:\"Right-aligned archive\";s:4:\"slug\";s:54:\"twentytwentyfive/template-archive-vertical-header-blog\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:8:\"inserter\";b:0;s:13:\"templateTypes\";a:1:{i:0;s:7:\"archive\";}}s:27:\"template-home-news-blog.php\";a:6:{s:5:\"title\";s:14:\"News blog home\";s:4:\"slug\";s:40:\"twentytwentyfive/template-home-news-blog\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:8:\"inserter\";b:0;s:13:\"templateTypes\";a:3:{i:0;s:10:\"front-page\";i:1;s:5:\"index\";i:2;s:4:\"home\";}}s:28:\"template-home-photo-blog.php\";a:6:{s:5:\"title\";s:15:\"Photo blog home\";s:4:\"slug\";s:41:\"twentytwentyfive/template-home-photo-blog\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:8:\"inserter\";b:0;s:13:\"templateTypes\";a:3:{i:0;s:10:\"front-page\";i:1;s:5:\"index\";i:2;s:4:\"home\";}}s:38:\"template-home-posts-grid-news-blog.php\";a:5:{s:5:\"title\";s:34:\"News blog with featured posts grid\";s:4:\"slug\";s:51:\"twentytwentyfive/template-home-posts-grid-news-blog\";s:11:\"description\";s:0:\"\";s:8:\"inserter\";b:0;s:13:\"templateTypes\";a:3:{i:0;s:10:\"front-page\";i:1;s:5:\"index\";i:2;s:4:\"home\";}}s:27:\"template-home-text-blog.php\";a:6:{s:5:\"title\";s:14:\"Text blog home\";s:4:\"slug\";s:40:\"twentytwentyfive/template-home-text-blog\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:8:\"inserter\";b:0;s:13:\"templateTypes\";a:2:{i:0;s:10:\"front-page\";i:1;s:4:\"home\";}}s:38:\"template-home-vertical-header-blog.php\";a:6:{s:5:\"title\";s:18:\"Right-aligned home\";s:4:\"slug\";s:51:\"twentytwentyfive/template-home-vertical-header-blog\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:8:\"inserter\";b:0;s:13:\"templateTypes\";a:3:{i:0;s:10:\"front-page\";i:1;s:5:\"index\";i:2;s:4:\"home\";}}s:40:\"template-home-with-sidebar-news-blog.php\";a:6:{s:5:\"title\";s:22:\"News blog with sidebar\";s:4:\"slug\";s:53:\"twentytwentyfive/template-home-with-sidebar-news-blog\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:8:\"inserter\";b:0;s:13:\"templateTypes\";a:3:{i:0;s:10:\"front-page\";i:1;s:5:\"index\";i:2;s:4:\"home\";}}s:28:\"template-page-photo-blog.php\";a:5:{s:5:\"title\";s:15:\"Photo blog page\";s:4:\"slug\";s:41:\"twentytwentyfive/template-page-photo-blog\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:13:\"templateTypes\";a:1:{i:0;s:4:\"page\";}}s:38:\"template-page-vertical-header-blog.php\";a:5:{s:5:\"title\";s:18:\"Right-aligned page\";s:4:\"slug\";s:51:\"twentytwentyfive/template-page-vertical-header-blog\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:13:\"templateTypes\";a:1:{i:0;s:4:\"page\";}}s:33:\"template-query-loop-news-blog.php\";a:4:{s:5:\"title\";s:20:\"News blog query loop\";s:4:\"slug\";s:46:\"twentytwentyfive/template-query-loop-news-blog\";s:11:\"description\";s:0:\"\";s:8:\"inserter\";b:0;}s:34:\"template-query-loop-photo-blog.php\";a:6:{s:5:\"title\";s:16:\"Photo blog posts\";s:4:\"slug\";s:47:\"twentytwentyfive/template-query-loop-photo-blog\";s:11:\"description\";s:54:\"A list of posts, 3 columns, with only featured images.\";s:13:\"viewportWidth\";i:1400;s:10:\"categories\";a:1:{i:0;s:5:\"query\";}s:10:\"blockTypes\";a:1:{i:0;s:10:\"core/query\";}}s:33:\"template-query-loop-text-blog.php\";a:4:{s:5:\"title\";s:20:\"Text blog query loop\";s:4:\"slug\";s:46:\"twentytwentyfive/template-query-loop-text-blog\";s:11:\"description\";s:0:\"\";s:8:\"inserter\";b:0;}s:44:\"template-query-loop-vertical-header-blog.php\";a:4:{s:5:\"title\";s:24:\"Right-aligned query loop\";s:4:\"slug\";s:57:\"twentytwentyfive/template-query-loop-vertical-header-blog\";s:11:\"description\";s:0:\"\";s:8:\"inserter\";b:0;}s:23:\"template-query-loop.php\";a:5:{s:5:\"title\";s:23:\"List of posts, 1 column\";s:4:\"slug\";s:36:\"twentytwentyfive/template-query-loop\";s:11:\"description\";s:61:\"A list of posts, 1 column, with featured image and post date.\";s:10:\"categories\";a:1:{i:0;s:5:\"query\";}s:10:\"blockTypes\";a:1:{i:0;s:10:\"core/query\";}}s:29:\"template-search-news-blog.php\";a:6:{s:5:\"title\";s:24:\"News blog search results\";s:4:\"slug\";s:42:\"twentytwentyfive/template-search-news-blog\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:8:\"inserter\";b:0;s:13:\"templateTypes\";a:1:{i:0;s:6:\"search\";}}s:30:\"template-search-photo-blog.php\";a:6:{s:5:\"title\";s:25:\"Photo blog search results\";s:4:\"slug\";s:43:\"twentytwentyfive/template-search-photo-blog\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:8:\"inserter\";b:0;s:13:\"templateTypes\";a:1:{i:0;s:6:\"search\";}}s:29:\"template-search-text-blog.php\";a:6:{s:5:\"title\";s:24:\"Text blog search results\";s:4:\"slug\";s:42:\"twentytwentyfive/template-search-text-blog\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:8:\"inserter\";b:0;s:13:\"templateTypes\";a:1:{i:0;s:6:\"search\";}}s:40:\"template-search-vertical-header-blog.php\";a:6:{s:5:\"title\";s:26:\"Right-aligned blog, search\";s:4:\"slug\";s:53:\"twentytwentyfive/template-search-vertical-header-blog\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:8:\"inserter\";b:0;s:13:\"templateTypes\";a:1:{i:0;s:6:\"search\";}}s:40:\"template-single-left-aligned-content.php\";a:6:{s:5:\"title\";s:30:\"Post with left-aligned content\";s:4:\"slug\";s:47:\"twentytwentyfive/post-with-left-aligned-content\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:8:\"inserter\";b:0;s:13:\"templateTypes\";a:2:{i:0;s:5:\"posts\";i:1;s:6:\"single\";}}s:29:\"template-single-news-blog.php\";a:6:{s:5:\"title\";s:34:\"News blog single post with sidebar\";s:4:\"slug\";s:42:\"twentytwentyfive/template-single-news-blog\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:8:\"inserter\";b:0;s:13:\"templateTypes\";a:2:{i:0;s:5:\"posts\";i:1;s:6:\"single\";}}s:26:\"template-single-offset.php\";a:6:{s:5:\"title\";s:34:\"Offset post without featured image\";s:4:\"slug\";s:39:\"twentytwentyfive/template-single-offset\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:8:\"inserter\";b:0;s:13:\"templateTypes\";a:2:{i:0;s:5:\"posts\";i:1;s:6:\"single\";}}s:30:\"template-single-photo-blog.php\";a:6:{s:5:\"title\";s:22:\"Photo blog single post\";s:4:\"slug\";s:43:\"twentytwentyfive/template-single-photo-blog\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:8:\"inserter\";b:0;s:13:\"templateTypes\";a:2:{i:0;s:5:\"posts\";i:1;s:6:\"single\";}}s:29:\"template-single-text-blog.php\";a:6:{s:5:\"title\";s:21:\"Text blog single post\";s:4:\"slug\";s:42:\"twentytwentyfive/template-single-text-blog\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:8:\"inserter\";b:0;s:13:\"templateTypes\";a:2:{i:0;s:5:\"posts\";i:1;s:6:\"single\";}}s:40:\"template-single-vertical-header-blog.php\";a:6:{s:5:\"title\";s:25:\"Right-aligned single post\";s:4:\"slug\";s:53:\"twentytwentyfive/template-single-vertical-header-blog\";s:11:\"description\";s:0:\"\";s:13:\"viewportWidth\";i:1400;s:8:\"inserter\";b:0;s:13:\"templateTypes\";a:2:{i:0;s:5:\"posts\";i:1;s:6:\"single\";}}s:22:\"testimonials-2-col.php\";a:5:{s:5:\"title\";s:21:\"2 columns with avatar\";s:4:\"slug\";s:35:\"twentytwentyfive/testimonials-2-col\";s:11:\"description\";s:42:\"Two columns with testimonials and avatars.\";s:10:\"categories\";a:1:{i:0;s:12:\"testimonials\";}s:8:\"keywords\";a:1:{i:0;s:11:\"testimonial\";}}s:22:\"testimonials-6-col.php\";a:5:{s:5:\"title\";s:35:\"3 column layout with 6 testimonials\";s:4:\"slug\";s:35:\"twentytwentyfive/testimonials-6-col\";s:11:\"description\";s:86:\"A section with three columns and two rows, each containing a testimonial and citation.\";s:10:\"categories\";a:1:{i:0;s:12:\"testimonials\";}s:8:\"keywords\";a:1:{i:0;s:11:\"testimonial\";}}s:22:\"testimonials-large.php\";a:5:{s:5:\"title\";s:32:\"Review with large image on right\";s:4:\"slug\";s:35:\"twentytwentyfive/testimonials-large\";s:11:\"description\";s:46:\"A testimonial with a large image on the right.\";s:10:\"categories\";a:1:{i:0;s:12:\"testimonials\";}s:8:\"keywords\";a:1:{i:0;s:11:\"testimonial\";}}s:13:\"text-faqs.php\";a:6:{s:5:\"title\";s:4:\"FAQs\";s:4:\"slug\";s:26:\"twentytwentyfive/text-faqs\";s:11:\"description\";s:68:\"A FAQs section with a FAQ heading and list of questions and answers.\";s:13:\"viewportWidth\";i:1400;s:10:\"categories\";a:2:{i:0;s:4:\"text\";i:1;s:5:\"about\";}s:8:\"keywords\";a:5:{i:0;s:3:\"faq\";i:1;s:5:\"about\";i:2;s:10:\"frequently\";i:3;s:5:\"asked\";i:4;s:9:\"questions\";}}s:19:\"vertical-header.php\";a:6:{s:5:\"title\";s:20:\"Vertical site header\";s:4:\"slug\";s:32:\"twentytwentyfive/vertical-header\";s:11:\"description\";s:52:\"Vertical site header with site title and navigation.\";s:13:\"viewportWidth\";i:300;s:10:\"categories\";a:1:{i:0;s:6:\"header\";}s:10:\"blockTypes\";a:1:{i:0;s:34:\"core/template-part/vertical-header\";}}}}','off');
/*!40000 ALTER TABLE `wp_options` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wp_postmeta`
--

DROP TABLE IF EXISTS `wp_postmeta`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wp_postmeta` (
  `meta_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `post_id` bigint unsigned NOT NULL DEFAULT '0',
  `meta_key` varchar(255) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `meta_value` longtext COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`meta_id`),
  KEY `post_id` (`post_id`),
  KEY `meta_key` (`meta_key`(191))
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wp_postmeta`
--

LOCK TABLES `wp_postmeta` WRITE;
/*!40000 ALTER TABLE `wp_postmeta` DISABLE KEYS */;
INSERT INTO `wp_postmeta` VALUES (1,2,'_wp_page_template','default');
INSERT INTO `wp_postmeta` VALUES (2,3,'_wp_page_template','default');
/*!40000 ALTER TABLE `wp_postmeta` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wp_posts`
--

DROP TABLE IF EXISTS `wp_posts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wp_posts` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `post_author` bigint unsigned NOT NULL DEFAULT '0',
  `post_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `post_date_gmt` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `post_content` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `post_title` text COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `post_excerpt` text COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `post_status` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'publish',
  `comment_status` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'open',
  `ping_status` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'open',
  `post_password` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `post_name` varchar(200) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `to_ping` text COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `pinged` text COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `post_modified` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `post_modified_gmt` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `post_content_filtered` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `post_parent` bigint unsigned NOT NULL DEFAULT '0',
  `guid` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `menu_order` int NOT NULL DEFAULT '0',
  `post_type` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'post',
  `post_mime_type` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `comment_count` bigint NOT NULL DEFAULT '0',
  PRIMARY KEY (`ID`),
  KEY `post_name` (`post_name`(191)),
  KEY `type_status_date` (`post_type`,`post_status`,`post_date`,`ID`),
  KEY `post_parent` (`post_parent`),
  KEY `post_author` (`post_author`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wp_posts`
--

LOCK TABLES `wp_posts` WRITE;
/*!40000 ALTER TABLE `wp_posts` DISABLE KEYS */;
INSERT INTO `wp_posts` VALUES (1,1,'2025-05-26 19:05:34','2025-05-26 19:05:34','<!-- wp:paragraph -->\n<p>Welcome to WordPress. This is your first post. Edit or delete it, then start writing!</p>\n<!-- /wp:paragraph -->','Hello world!','','publish','open','open','','hello-world','','','2025-05-26 19:05:34','2025-05-26 19:05:34','',0,'http://satolocphpbackend.local/?p=1',0,'post','',1);
INSERT INTO `wp_posts` VALUES (2,1,'2025-05-26 19:05:34','2025-05-26 19:05:34','<!-- wp:paragraph -->\n<p>This is an example page. It\'s different from a blog post because it will stay in one place and will show up in your site navigation (in most themes). Most people start with an About page that introduces them to potential site visitors. It might say something like this:</p>\n<!-- /wp:paragraph -->\n\n<!-- wp:quote -->\n<blockquote class=\"wp-block-quote\"><p>Hi there! I\'m a bike messenger by day, aspiring actor by night, and this is my website. I live in Los Angeles, have a great dog named Jack, and I like pi&#241;a coladas. (And gettin\' caught in the rain.)</p></blockquote>\n<!-- /wp:quote -->\n\n<!-- wp:paragraph -->\n<p>...or something like this:</p>\n<!-- /wp:paragraph -->\n\n<!-- wp:quote -->\n<blockquote class=\"wp-block-quote\"><p>The XYZ Doohickey Company was founded in 1971, and has been providing quality doohickeys to the public ever since. Located in Gotham City, XYZ employs over 2,000 people and does all kinds of awesome things for the Gotham community.</p></blockquote>\n<!-- /wp:quote -->\n\n<!-- wp:paragraph -->\n<p>As a new WordPress user, you should go to <a href=\"http://satolocphpbackend.local/wp-admin/\">your dashboard</a> to delete this page and create new pages for your content. Have fun!</p>\n<!-- /wp:paragraph -->','Sample Page','','publish','closed','open','','sample-page','','','2025-05-26 19:05:34','2025-05-26 19:05:34','',0,'http://satolocphpbackend.local/?page_id=2',0,'page','',0);
INSERT INTO `wp_posts` VALUES (3,1,'2025-05-26 19:05:34','2025-05-26 19:05:34','<!-- wp:heading -->\n<h2 class=\"wp-block-heading\">Who we are</h2>\n<!-- /wp:heading -->\n<!-- wp:paragraph -->\n<p><strong class=\"privacy-policy-tutorial\">Suggested text: </strong>Our website address is: http://satolocphpbackend.local.</p>\n<!-- /wp:paragraph -->\n<!-- wp:heading -->\n<h2 class=\"wp-block-heading\">Comments</h2>\n<!-- /wp:heading -->\n<!-- wp:paragraph -->\n<p><strong class=\"privacy-policy-tutorial\">Suggested text: </strong>When visitors leave comments on the site we collect the data shown in the comments form, and also the visitor&#8217;s IP address and browser user agent string to help spam detection.</p>\n<!-- /wp:paragraph -->\n<!-- wp:paragraph -->\n<p>An anonymized string created from your email address (also called a hash) may be provided to the Gravatar service to see if you are using it. The Gravatar service privacy policy is available here: https://automattic.com/privacy/. After approval of your comment, your profile picture is visible to the public in the context of your comment.</p>\n<!-- /wp:paragraph -->\n<!-- wp:heading -->\n<h2 class=\"wp-block-heading\">Media</h2>\n<!-- /wp:heading -->\n<!-- wp:paragraph -->\n<p><strong class=\"privacy-policy-tutorial\">Suggested text: </strong>If you upload images to the website, you should avoid uploading images with embedded location data (EXIF GPS) included. Visitors to the website can download and extract any location data from images on the website.</p>\n<!-- /wp:paragraph -->\n<!-- wp:heading -->\n<h2 class=\"wp-block-heading\">Cookies</h2>\n<!-- /wp:heading -->\n<!-- wp:paragraph -->\n<p><strong class=\"privacy-policy-tutorial\">Suggested text: </strong>If you leave a comment on our site you may opt-in to saving your name, email address and website in cookies. These are for your convenience so that you do not have to fill in your details again when you leave another comment. These cookies will last for one year.</p>\n<!-- /wp:paragraph -->\n<!-- wp:paragraph -->\n<p>If you visit our login page, we will set a temporary cookie to determine if your browser accepts cookies. This cookie contains no personal data and is discarded when you close your browser.</p>\n<!-- /wp:paragraph -->\n<!-- wp:paragraph -->\n<p>When you log in, we will also set up several cookies to save your login information and your screen display choices. Login cookies last for two days, and screen options cookies last for a year. If you select &quot;Remember Me&quot;, your login will persist for two weeks. If you log out of your account, the login cookies will be removed.</p>\n<!-- /wp:paragraph -->\n<!-- wp:paragraph -->\n<p>If you edit or publish an article, an additional cookie will be saved in your browser. This cookie includes no personal data and simply indicates the post ID of the article you just edited. It expires after 1 day.</p>\n<!-- /wp:paragraph -->\n<!-- wp:heading -->\n<h2 class=\"wp-block-heading\">Embedded content from other websites</h2>\n<!-- /wp:heading -->\n<!-- wp:paragraph -->\n<p><strong class=\"privacy-policy-tutorial\">Suggested text: </strong>Articles on this site may include embedded content (e.g. videos, images, articles, etc.). Embedded content from other websites behaves in the exact same way as if the visitor has visited the other website.</p>\n<!-- /wp:paragraph -->\n<!-- wp:paragraph -->\n<p>These websites may collect data about you, use cookies, embed additional third-party tracking, and monitor your interaction with that embedded content, including tracking your interaction with the embedded content if you have an account and are logged in to that website.</p>\n<!-- /wp:paragraph -->\n<!-- wp:heading -->\n<h2 class=\"wp-block-heading\">Who we share your data with</h2>\n<!-- /wp:heading -->\n<!-- wp:paragraph -->\n<p><strong class=\"privacy-policy-tutorial\">Suggested text: </strong>If you request a password reset, your IP address will be included in the reset email.</p>\n<!-- /wp:paragraph -->\n<!-- wp:heading -->\n<h2 class=\"wp-block-heading\">How long we retain your data</h2>\n<!-- /wp:heading -->\n<!-- wp:paragraph -->\n<p><strong class=\"privacy-policy-tutorial\">Suggested text: </strong>If you leave a comment, the comment and its metadata are retained indefinitely. This is so we can recognize and approve any follow-up comments automatically instead of holding them in a moderation queue.</p>\n<!-- /wp:paragraph -->\n<!-- wp:paragraph -->\n<p>For users that register on our website (if any), we also store the personal information they provide in their user profile. All users can see, edit, or delete their personal information at any time (except they cannot change their username). Website administrators can also see and edit that information.</p>\n<!-- /wp:paragraph -->\n<!-- wp:heading -->\n<h2 class=\"wp-block-heading\">What rights you have over your data</h2>\n<!-- /wp:heading -->\n<!-- wp:paragraph -->\n<p><strong class=\"privacy-policy-tutorial\">Suggested text: </strong>If you have an account on this site, or have left comments, you can request to receive an exported file of the personal data we hold about you, including any data you have provided to us. You can also request that we erase any personal data we hold about you. This does not include any data we are obliged to keep for administrative, legal, or security purposes.</p>\n<!-- /wp:paragraph -->\n<!-- wp:heading -->\n<h2 class=\"wp-block-heading\">Where your data is sent</h2>\n<!-- /wp:heading -->\n<!-- wp:paragraph -->\n<p><strong class=\"privacy-policy-tutorial\">Suggested text: </strong>Visitor comments may be checked through an automated spam detection service.</p>\n<!-- /wp:paragraph -->\n','Privacy Policy','','draft','closed','open','','privacy-policy','','','2025-05-26 19:05:34','2025-05-26 19:05:34','',0,'http://satolocphpbackend.local/?page_id=3',0,'page','',0);
INSERT INTO `wp_posts` VALUES (4,1,'2025-05-26 19:06:44','0000-00-00 00:00:00','','Auto Draft','','auto-draft','open','open','','','','','2025-05-26 19:06:44','0000-00-00 00:00:00','',0,'http://satolocphpbackend.local/?p=4',0,'post','',0);
INSERT INTO `wp_posts` VALUES (5,0,'2025-05-26 20:37:32','2025-05-26 20:37:32','<!-- wp:page-list /-->','Navigation','','publish','closed','closed','','navigation','','','2025-05-26 20:37:32','2025-05-26 20:37:32','',0,'http://satolocphpbackend.local/navigation/',0,'wp_navigation','',0);
/*!40000 ALTER TABLE `wp_posts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wp_term_relationships`
--

DROP TABLE IF EXISTS `wp_term_relationships`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wp_term_relationships` (
  `object_id` bigint unsigned NOT NULL DEFAULT '0',
  `term_taxonomy_id` bigint unsigned NOT NULL DEFAULT '0',
  `term_order` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`object_id`,`term_taxonomy_id`),
  KEY `term_taxonomy_id` (`term_taxonomy_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wp_term_relationships`
--

LOCK TABLES `wp_term_relationships` WRITE;
/*!40000 ALTER TABLE `wp_term_relationships` DISABLE KEYS */;
INSERT INTO `wp_term_relationships` VALUES (1,1,0);
/*!40000 ALTER TABLE `wp_term_relationships` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wp_term_taxonomy`
--

DROP TABLE IF EXISTS `wp_term_taxonomy`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wp_term_taxonomy` (
  `term_taxonomy_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `term_id` bigint unsigned NOT NULL DEFAULT '0',
  `taxonomy` varchar(32) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `parent` bigint unsigned NOT NULL DEFAULT '0',
  `count` bigint NOT NULL DEFAULT '0',
  PRIMARY KEY (`term_taxonomy_id`),
  UNIQUE KEY `term_id_taxonomy` (`term_id`,`taxonomy`),
  KEY `taxonomy` (`taxonomy`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wp_term_taxonomy`
--

LOCK TABLES `wp_term_taxonomy` WRITE;
/*!40000 ALTER TABLE `wp_term_taxonomy` DISABLE KEYS */;
INSERT INTO `wp_term_taxonomy` VALUES (1,1,'category','',0,1);
/*!40000 ALTER TABLE `wp_term_taxonomy` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wp_termmeta`
--

DROP TABLE IF EXISTS `wp_termmeta`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wp_termmeta` (
  `meta_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `term_id` bigint unsigned NOT NULL DEFAULT '0',
  `meta_key` varchar(255) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `meta_value` longtext COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`meta_id`),
  KEY `term_id` (`term_id`),
  KEY `meta_key` (`meta_key`(191))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wp_termmeta`
--

LOCK TABLES `wp_termmeta` WRITE;
/*!40000 ALTER TABLE `wp_termmeta` DISABLE KEYS */;
/*!40000 ALTER TABLE `wp_termmeta` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wp_terms`
--

DROP TABLE IF EXISTS `wp_terms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wp_terms` (
  `term_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(200) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `slug` varchar(200) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `term_group` bigint NOT NULL DEFAULT '0',
  PRIMARY KEY (`term_id`),
  KEY `slug` (`slug`(191)),
  KEY `name` (`name`(191))
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wp_terms`
--

LOCK TABLES `wp_terms` WRITE;
/*!40000 ALTER TABLE `wp_terms` DISABLE KEYS */;
INSERT INTO `wp_terms` VALUES (1,'Uncategorized','uncategorized',0);
/*!40000 ALTER TABLE `wp_terms` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wp_usermeta`
--

DROP TABLE IF EXISTS `wp_usermeta`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wp_usermeta` (
  `umeta_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL DEFAULT '0',
  `meta_key` varchar(255) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `meta_value` longtext COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`umeta_id`),
  KEY `user_id` (`user_id`),
  KEY `meta_key` (`meta_key`(191))
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wp_usermeta`
--

LOCK TABLES `wp_usermeta` WRITE;
/*!40000 ALTER TABLE `wp_usermeta` DISABLE KEYS */;
INSERT INTO `wp_usermeta` VALUES (1,1,'nickname','satolocinsight');
INSERT INTO `wp_usermeta` VALUES (2,1,'first_name','');
INSERT INTO `wp_usermeta` VALUES (3,1,'last_name','');
INSERT INTO `wp_usermeta` VALUES (4,1,'description','');
INSERT INTO `wp_usermeta` VALUES (5,1,'rich_editing','true');
INSERT INTO `wp_usermeta` VALUES (6,1,'syntax_highlighting','true');
INSERT INTO `wp_usermeta` VALUES (7,1,'comment_shortcuts','false');
INSERT INTO `wp_usermeta` VALUES (8,1,'admin_color','fresh');
INSERT INTO `wp_usermeta` VALUES (9,1,'use_ssl','0');
INSERT INTO `wp_usermeta` VALUES (10,1,'show_admin_bar_front','true');
INSERT INTO `wp_usermeta` VALUES (11,1,'locale','');
INSERT INTO `wp_usermeta` VALUES (12,1,'wp_capabilities','a:1:{s:13:\"administrator\";b:1;}');
INSERT INTO `wp_usermeta` VALUES (13,1,'wp_user_level','10');
INSERT INTO `wp_usermeta` VALUES (14,1,'dismissed_wp_pointers','');
INSERT INTO `wp_usermeta` VALUES (15,1,'show_welcome_panel','1');
INSERT INTO `wp_usermeta` VALUES (16,1,'session_tokens','a:1:{s:64:\"29c3a07b7e1f196e70615646d3c2fa1de029a4ff104a4a2946bec782471c320e\";a:4:{s:10:\"expiration\";i:1749496001;s:2:\"ip\";s:9:\"127.0.0.1\";s:2:\"ua\";s:119:\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15\";s:5:\"login\";i:1748286401;}}');
INSERT INTO `wp_usermeta` VALUES (17,1,'wp_dashboard_quick_press_last_post_id','4');
/*!40000 ALTER TABLE `wp_usermeta` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wp_users`
--

DROP TABLE IF EXISTS `wp_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wp_users` (
  `ID` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_login` varchar(60) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_pass` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_nicename` varchar(50) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_email` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_url` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_registered` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `user_activation_key` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_status` int NOT NULL DEFAULT '0',
  `display_name` varchar(250) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`ID`),
  KEY `user_login_key` (`user_login`),
  KEY `user_nicename` (`user_nicename`),
  KEY `user_email` (`user_email`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wp_users`
--

LOCK TABLES `wp_users` WRITE;
/*!40000 ALTER TABLE `wp_users` DISABLE KEYS */;
INSERT INTO `wp_users` VALUES (1,'satolocinsight','$wp$2y$10$4OOjBjHjRyPnZHZ2g5Q1huUJBndNze/k60RMm2zk4QVX.tl7anrOy','satolocinsight','<EMAIL>','http://satolocphpbackend.local','2025-05-26 19:05:34','',0,'satolocinsight');
/*!40000 ALTER TABLE `wp_users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-25 19:26:03
