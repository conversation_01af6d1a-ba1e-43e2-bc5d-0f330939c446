<?php
/**
 * Plugin Name:       <PERSON>loc Insight Connector
 * Plugin URI:        #
 * Description:       Provides secure REST API endpoints and settings page for content synchronization.
 * Version:           1.1.0
 * Author:            Satoloc Insight
 * Author URI:        https://satolocinsight.com
 * License:           GPL v2 or later
 * License URI:       https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain:       satoloc-insight-connector
 * Domain Path:       /languages
 * Requires PHP:      7.4
 * Requires at least: 5.8
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
    die;
}

// Define plugin constants with consistent prefix
define( 'SATOLOC_INSIGHT_CONNECTOR_VERSION', '1.1.3' ); // <-- Updated version
define( 'SATOLOC_INSIGHT_CONNECTOR_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'SATOLOC_INSIGHT_CONNECTOR_API_KEY_OPTION', 'satoloc_insight_connector_api_key' );
define( 'SATOLOC_INSIGHT_CONNECTOR_API_NAMESPACE', 'satoloc-insight-connector/v1' );
// *** IMPORTANT: Define the EXACT header name your frontend will send ***
define( 'SATOLOC_INSIGHT_CONNECTOR_API_KEY_HEADER', 'X-Satoloc-Insight-Key' ); // Use this one consistently
define( 'SATOLOC_INSIGHT_CONNECTOR_SETTINGS_SLUG', 'satoloc-insight-connector-settings');

/**
 * Activation hook: Generate API Key and flush rewrite rules.
 * MODIFIED: Generate alphanumeric key only.
 */
function satoloc_insight_connector_activate() {
    // Generate key if it doesn't exist
    if ( ! get_option( SATOLOC_INSIGHT_CONNECTOR_API_KEY_OPTION ) ) {
        // Generate a 64-character key using ONLY letters and numbers
        $api_key = wp_generate_password( 64, false, false );
        update_option( SATOLOC_INSIGHT_CONNECTOR_API_KEY_OPTION, $api_key );
    }
    flush_rewrite_rules();
}
register_activation_hook( __FILE__, 'satoloc_insight_connector_activate' );

/**
 * Deactivation hook: Flush rewrite rules.
 */
function satoloc_insight_connector_deactivate() {
    flush_rewrite_rules();
}
register_deactivation_hook( __FILE__, 'satoloc_insight_connector_deactivate' );

/**
 * Initialize the plugin: Load API class and register hooks.
 */
function satoloc_insight_connector_init() {
    $api_class_file = SATOLOC_INSIGHT_CONNECTOR_PLUGIN_DIR . 'includes/class-satoloc-insight-connector-api.php';
    if ( file_exists( $api_class_file ) ) {
        require_once $api_class_file;
        if ( class_exists( 'Satoloc_Insight_Connector_API' ) ) {
            $api = new Satoloc_Insight_Connector_API();
            $api->register_hooks();
        } else { error_log('Satoloc Insight Connector Error: API Class not found in ' . $api_class_file); }
    } else { error_log('Satoloc Insight Connector Error: API Class file not found at ' . $api_class_file); }
}
add_action( 'plugins_loaded', 'satoloc_insight_connector_init' );

// --- Admin Dashboard Code ---

/**
 * Add the plugin settings page to the admin menu.
 */
function satoloc_insight_connector_add_admin_menu() {
    add_options_page(
        __( 'Satoloc Insight Connector Settings', 'satoloc-insight-connector' ),
        __( 'Satoloc Connector', 'satoloc-insight-connector' ),
        'manage_options',
        SATOLOC_INSIGHT_CONNECTOR_SETTINGS_SLUG,
        'satoloc_insight_connector_options_page_html'
    );
}
add_action( 'admin_menu', 'satoloc_insight_connector_add_admin_menu' );

/**
 * Render the HTML for the plugin's options page.
 */
function satoloc_insight_connector_options_page_html() {
    if ( ! current_user_can( 'manage_options' ) ) {
        wp_die( __( 'You do not have sufficient permissions to access this page.', 'satoloc-insight-connector' ) );
    }

    $api_key = get_option( SATOLOC_INSIGHT_CONNECTOR_API_KEY_OPTION );
    $api_namespace_url = rest_url( SATOLOC_INSIGHT_CONNECTOR_API_NAMESPACE );
    $api_key_header = SATOLOC_INSIGHT_CONNECTOR_API_KEY_HEADER; // Get header name from constant
    $settings_slug = SATOLOC_INSIGHT_CONNECTOR_SETTINGS_SLUG;

    ?>
    <style>
        .sic-card { background: #fff; border: 1px solid #c3c4c7; box-shadow: 0 1px 1px rgba(0,0,0,.04); padding: 20px; margin-bottom: 20px; max-width: 800px; }
        .sic-card h2 { font-size: 1.3em; margin: 0 0 1em 0; padding: 0 0 .5em 0; border-bottom: 1px solid #eee; }
        .sic-field-group { margin-bottom: 1.5em; padding-bottom: 1.5em; border-bottom: 1px solid #f0f0f0; }
        .sic-field-group:last-child { margin-bottom: 0; padding-bottom: 0; border-bottom: none; }
        .sic-field-group label { display: block; font-weight: 600; margin-bottom: 5px; }
        .sic-api-key-display { font-family: monospace; background-color: #f0f0f1; padding: 8px 12px; border: 1px solid #ddd; border-radius: 3px; word-break: break-all; display: inline-block; margin-right: 5px; max-width: calc(100% - 100px); vertical-align: middle; cursor: text; }
        .sic-field-group .button-small { vertical-align: middle; }
        .sic-field-group .description { color: #646970; font-size: .9em; margin-top: 5px; }
        .sic-field-group code { background: #eee; padding: 2px 5px; border: 1px solid #ddd; border-radius: 3px; }
        .sic-regenerate-form .button.delete { background: #d63638; border-color: #b02a2c #9e2628 #9e2628; box-shadow: inset 0 1px 0 #e47476; color: #fff; text-decoration: none; text-shadow: 0 -1px 1px #b02a2c,1px 0 1px #b02a2c,0 1px 1px #b02a2c,-1px 0 1px #b02a2c; }
        .sic-regenerate-form .button.delete:hover { background: #c23032; border-color: #9e2628; color: #fff; }
        .sic-instructions ol { margin-left: 20px; list-style: decimal; }
        .sic-instructions li { margin-bottom: .5em; }
    </style>

    <div class="wrap">
        <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>
        <?php settings_errors( 'satoloc_insight_connector_messages' ); ?>
        <p><?php esc_html_e( 'Configure settings and view API details for the Satoloc Insight Connector plugin.', 'satoloc-insight-connector' ); ?></p>

        <div class="sic-card">
            <h2><?php esc_html_e( 'API Connection Details', 'satoloc-insight-connector' ); ?></h2>
            <div class="sic-field-group">
                <label for="sic-api-key-display"><?php esc_html_e( 'API Key', 'satoloc-insight-connector' ); ?></label>
                <?php if ( $api_key ) : ?>
                    <span id="sic-api-key-display" class="sic-api-key-display" title="<?php esc_attr_e('Click to select text', 'satoloc-insight-connector'); ?>"><?php echo esc_html( $api_key ); ?></span>
                    <button type="button" class="button button-small" id="sic-copy-key-button"><?php esc_html_e( 'Copy Key', 'satoloc-insight-connector' ); ?></button>
                    <p class="description"><?php esc_html_e( 'Use this key in the required HTTP header:', 'satoloc-insight-connector' ); ?> <code><?php echo esc_html( $api_key_header ); ?></code></p>
                <?php else : ?>
                    <p class="notice notice-error inline"><?php esc_html_e( 'API Key not generated. Please deactivate and reactivate the plugin.', 'satoloc-insight-connector' ); ?></p>
                <?php endif; ?>
            </div>
            <div class="sic-field-group">
                 <label><?php esc_html_e( 'API Base URL', 'satoloc-insight-connector' ); ?></label>
                 <?php if ( $api_namespace_url ) : ?>
                     <code><?php echo esc_url( $api_namespace_url ); ?></code>
                     <p class="description"><?php esc_html_e( 'The base endpoint for your custom API.', 'satoloc-insight-connector' ); ?></p>
                 <?php else : ?>
                      <p class="notice notice-warning inline"><?php esc_html_e( 'Could not determine API Base URL. Ensure permalinks are not set to Plain.', 'satoloc-insight-connector' ); ?></p>
                 <?php endif; ?>
            </div>
        </div>

        <div class="sic-card">
             <h2><?php esc_html_e( 'Security', 'satoloc-insight-connector' ); ?></h2>
             <div class="sic-field-group sic-regenerate-form">
                 <label><?php esc_html_e( 'Regenerate API Key', 'satoloc-insight-connector' ); ?></label>
                 <form method="post" action="<?php echo esc_url( admin_url( 'admin-post.php' ) ); ?>" onsubmit="return confirm('<?php esc_attr_e('Are you sure you want to regenerate the API Key? The old key will stop working immediately.', 'satoloc-insight-connector'); ?>');">
                     <input type="hidden" name="action" value="satoloc_insight_connector_regenerate_key">
                     <?php wp_nonce_field( 'satoloc_insight_connector_regenerate_key_nonce', '_wpnonce_regenerate' ); ?>
                     <?php submit_button( __( 'Regenerate Key', 'satoloc-insight-connector' ), 'delete', 'submit-regenerate', false ); ?>
                 </form>
                 <p class="description"><?php esc_html_e( 'If you suspect your key is compromised, regenerate it. You will need to update any connected applications with the new key.', 'satoloc-insight-connector' ); ?></p>
             </div>
        </div>

        <div class="sic-card sic-instructions">
            <h2><?php esc_html_e( 'Instructions', 'satoloc-insight-connector' ); ?></h2>
             <p><?php esc_html_e( 'To connect your external application (e.g., Next.js frontend):', 'satoloc-insight-connector' ); ?></p>
             <ol>
                 <li><?php esc_html_e( 'Copy the API Key shown above.', 'satoloc-insight-connector' ); ?></li>
                 <li><?php esc_html_e( 'Copy the API Base URL shown above.', 'satoloc-insight-connector' ); ?></li>
                 <li><?php esc_html_e( 'In your application, provide the Base URL when connecting.', 'satoloc-insight-connector' ); ?></li>
                 <li><?php printf( esc_html__( 'When making requests to the API Base URL, include the API Key in the %s HTTP header.', 'satoloc-insight-connector' ), '<code>' . esc_html( $api_key_header ) . '</code>' ); ?></li>
             </ol>
              <p><strong><?php esc_html_e( 'Important:', 'satoloc-insight-connector' ); ?></strong> <?php esc_html_e( 'Always use HTTPS to protect your API Key during transmission.', 'satoloc-insight-connector' ); ?></p>
        </div>
    </div><!-- .wrap -->

    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.getElementById('sic-copy-key-button');
            const apiKeyField = document.getElementById('sic-api-key-display');

            // Check if both elements exist before adding listener
            if (copyButton && apiKeyField) {
                copyButton.addEventListener('click', function() {
                    const apiKey = apiKeyField.textContent?.trim() || apiKeyField.innerText?.trim(); // Get text content reliably

                    if (!apiKey) {
                        console.error('API Key field is empty.');
                        alert('<?php echo esc_js(__('Could not find API Key text.', 'satoloc-insight-connector')); ?>');
                        return;
                    }

                    // Check if clipboard API is available
                    if (!navigator.clipboard) {
                        // Use execCommand as fallback
                        try {
                            selectElementText(apiKeyField);
                            const successful = document.execCommand('copy');
                            if (successful) {
                                const originalText = copyButton.textContent;
                                copyButton.textContent = 'Copied!';
                                copyButton.disabled = true;
                                setTimeout(function() {
                                    copyButton.textContent = originalText;
                                    copyButton.disabled = false;
                                }, 1500);
                            } else {
                                alert('Copy failed. Please select and copy manually.');
                            }
                        } catch (err) {
                            console.error('Failed to copy: ', err);
                            alert('Failed to copy key. Please copy manually.');
                            selectElementText(apiKeyField);
                        }
                        return;
                    }

                    navigator.clipboard.writeText(apiKey).then(function() {
                        // Success feedback
                        console.log('API Key copied successfully!');
                        const originalText = copyButton.textContent;
                        copyButton.textContent = '<?php echo esc_js(__('Copied!', 'satoloc-insight-connector')); ?>';
                        copyButton.disabled = true;
                        setTimeout(function() {
                            copyButton.textContent = originalText;
                            copyButton.disabled = false;
                        }, 1500);
                    }).catch(function(err) {
                        // Error feedback
                        console.error('Failed to copy API key: ', err);
                        alert('<?php echo esc_js(__('Failed to copy key. Please copy manually.', 'satoloc-insight-connector')); ?>');
                        // Optionally, try to select the text for manual copy here
                         selectElementText(apiKeyField);
                    });
                });

                // Allow selecting text in the span for manual copy
                apiKeyField.addEventListener('click', function() {
                    selectElementText(apiKeyField);
                });

            } else {
                 console.warn('Copy button or API Key field not found on the page.');
            }

            // Helper function to select text within an element
            function selectElementText(el) {
                let range, selection;
                if (document.body.createTextRange) { // IE
                    range = document.body.createTextRange();
                    range.moveToElementText(el);
                    range.select();
                } else if (window.getSelection) { // Standard
                    selection = window.getSelection();
                    range = document.createRange();
                    range.selectNodeContents(el);
                    selection.removeAllRanges();
                    selection.addRange(range);
                }
            }
        });
    </script>
    <?php
}

/**
 * Handle the regenerate key action submitted via admin-post.php
 */
function satoloc_insight_connector_handle_regenerate_action() {
     if ( ! isset( $_POST['_wpnonce_regenerate'] ) || ! wp_verify_nonce( $_POST['_wpnonce_regenerate'], 'satoloc_insight_connector_regenerate_key_nonce' ) ) { wp_die(__( 'Security check failed.', 'satoloc-insight-connector' )); }
     if ( ! current_user_can( 'manage_options' ) ) { wp_die(__( 'You do not have permission.', 'satoloc-insight-connector' )); }
     
     // Generate a 64-character key using ONLY letters and numbers
     $new_api_key = wp_generate_password( 64, false, false );
     
     $updated = update_option( SATOLOC_INSIGHT_CONNECTOR_API_KEY_OPTION, $new_api_key );
     $message = $updated ? __( 'API Key successfully regenerated (Alphanumeric).', 'satoloc-insight-connector' ) : __( 'Failed to regenerate API Key.', 'satoloc-insight-connector' );
     $type = $updated ? 'success' : 'error';
     add_settings_error('satoloc_insight_connector_messages', 'api_key_action', $message, $type);
     set_transient( 'settings_errors', get_settings_errors(), 30 );
     wp_safe_redirect( admin_url( 'options-general.php?page=' . SATOLOC_INSIGHT_CONNECTOR_SETTINGS_SLUG . '&settings-updated=true' ) );
     exit;
}
add_action( 'admin_post_satoloc_insight_connector_regenerate_key', 'satoloc_insight_connector_handle_regenerate_action' );
// --- End Admin Dashboard Code ---


// --- START: CORS Handling Code ---

/**
 * Add necessary CORS headers for the custom API.
 */
function satoloc_insight_connector_add_cors_support() {
    add_filter( 'rest_allowed_cors_headers', 'satoloc_insight_connector_allow_custom_headers' );
}
add_action( 'rest_api_init', 'satoloc_insight_connector_add_cors_support', 15 );

/**
 * Callback function to add custom headers to the allowed list for CORS.
 */
function satoloc_insight_connector_allow_custom_headers( $allowed_headers ) {
    // Get the header name from the constant
    $custom_header = SATOLOC_INSIGHT_CONNECTOR_API_KEY_HEADER; // e.g., 'X-Satoloc-Insight-Key'

    // Ensure the header from the constant is allowed
    if ( is_string($custom_header) && ! empty($custom_header) && ! in_array( $custom_header, $allowed_headers ) ) {
        $allowed_headers[] = $custom_header;
        // Optional: Log that the header was added for debugging
        // error_log("Satoloc Connector: Added CORS allowed header: " . $custom_header);
    }

    // Ensure Content-Type is allowed for POST/PUT with JSON
    if ( ! in_array( 'Content-Type', $allowed_headers ) ) {
         $allowed_headers[] = 'Content-Type';
    }

    // Add other common headers just in case
    $standard_headers = ['Authorization', 'X-Requested-With', 'Accept'];
    foreach ($standard_headers as $header) {
        if ( ! in_array( $header, $allowed_headers ) ) {
            $allowed_headers[] = $header;
        }
    }

    return $allowed_headers;
}
// --- END: CORS Handling Code ---

?>