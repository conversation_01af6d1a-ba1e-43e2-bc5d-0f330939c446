<?php
/**
 * Satoloc Insight Connector API Class
 * Handles the registration and callbacks for the custom REST API endpoints.
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
    die;
}

// Use the renamed class name
class Satoloc_Insight_Connector_API {

    // --- Properties and Constructor (Keep as before) ---
    protected $namespace;
    protected $api_key_header;

    public function __construct() {
        $this->namespace = SATOLOC_INSIGHT_CONNECTOR_API_NAMESPACE;
        $this->api_key_header = SATOLOC_INSIGHT_CONNECTOR_API_KEY_HEADER;
    }

    // --- register_hooks, register_routes, check_api_key_permission, get_categories (Keep as before) ---
    public function register_hooks() {
        add_action( 'rest_api_init', [ $this, 'register_routes' ] );
    }

    public function register_routes() {
        // Route for /content GET, POST
        register_rest_route( $this->namespace, '/content', [
            [
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [ $this, 'get_items' ],
                'permission_callback' => [ $this, 'check_api_key_permission' ],
                'args'                => $this->get_collection_params(),
            ],
            [
                'methods'             => WP_REST_Server::CREATABLE, // POST
                'callback'            => [ $this, 'create_item' ],
                'permission_callback' => [ $this, 'check_api_key_permission' ],
                'args'                => $this->get_endpoint_args_for_item_schema( true ), // Updated args
            ],
            'schema' => [ $this, 'get_public_item_schema' ], // Updated schema
        ]);

        // Route for /content/{id} GET, POST/PUT/PATCH, DELETE
        register_rest_route( $this->namespace, '/content/(?P<id>[\d]+)', [
            'args' => [
                'id' => [
                    'description' => __( 'Unique identifier for the content item.', 'satoloc-insight-connector' ),
                    'type'        => 'integer',
                    'required'    => true,
                 ],
            ],
            [
                'methods'             => WP_REST_Server::READABLE, // GET
                'callback'            => [ $this, 'get_item' ],
                'permission_callback' => [ $this, 'check_api_key_permission' ],
            ],
            [
                'methods'             => WP_REST_Server::EDITABLE, // POST, PUT, PATCH
                'callback'            => [ $this, 'update_item' ],
                'permission_callback' => [ $this, 'check_api_key_permission' ],
                'args'                => $this->get_endpoint_args_for_item_schema( false ),
            ],
            [
                'methods'             => WP_REST_Server::DELETABLE, // DELETE
                'callback'            => [ $this, 'delete_item' ],
                'permission_callback' => [ $this, 'check_api_key_permission' ],
                'args'                => [
                    'force' => [
                         'description' => __( 'Whether to bypass trash and force deletion.', 'satoloc-insight-connector' ),
                         'type'        => 'boolean',
                         'default'     => false,
                    ],
                ],
            ],
            'schema' => [ $this, 'get_public_item_schema' ],
        ]);

        // Route for /categories GET
        register_rest_route( $this->namespace, '/categories', [
            [
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [ $this, 'get_categories' ],
                'permission_callback' => [ $this, 'check_api_key_permission' ],
                'args'                => [
                    'hide_empty' => [
                        'description' => __('Whether to hide terms not assigned to any posts.', 'satoloc-insight-connector'),
                        'type'        => 'boolean',
                        'default'     => false,
                        'sanitize_callback' => 'rest_sanitize_boolean',
                    ],
                    'context' => $this->get_context_param( [ 'default' => 'view' ] ),
                ],
            ],
            'schema' => [ $this, 'get_public_category_schema' ],
        ]);
    }

    public function check_api_key_permission( $request ) {
        $header_key = 'HTTP_' . str_replace( '-', '_', strtoupper( $this->api_key_header ) );
        $provided_key = isset( $_SERVER[$header_key] ) ? sanitize_text_field( wp_unslash( $_SERVER[$header_key] ) ) : null;
        $stored_key = get_option( SATOLOC_INSIGHT_CONNECTOR_API_KEY_OPTION );

        if ( ! $stored_key ) {
            return new WP_Error('rest_api_key_not_configured', __('API Key not configured.', 'satoloc-insight-connector'), ['status' => 500]);
        }
        if ( empty( $provided_key ) ) {
             return new WP_Error('rest_api_key_missing', __('API Key missing in header:', 'satoloc-insight-connector') . ' ' . $this->api_key_header, ['status' => 401]);
        }
        if ( ! hash_equals( $stored_key, $provided_key ) ) {
            return new WP_Error('rest_api_key_invalid', __('Invalid API Key provided.', 'satoloc-insight-connector'), ['status' => 403]);
        }
        return true;
    }

    public function get_categories( $request ) {
        $params = $request->get_params();
        $args = [
            'taxonomy'   => 'category',
            'hide_empty' => isset($params['hide_empty']) ? rest_sanitize_boolean($params['hide_empty']) : false,
            'orderby'    => 'name',
            'order'      => 'ASC',
        ];

        $terms = get_terms( $args );

        if ( is_wp_error( $terms ) ) {
            error_log('Satoloc Connector: Error fetching categories - ' . $terms->get_error_message());
            return new WP_Error( 'rest_term_error', __( 'Error fetching categories.', 'satoloc-insight-connector' ), [ 'status' => 500, 'details' => $terms->get_error_message() ] );
        }

        $categories_data = [];
        if (is_array($terms)) { // Ensure $terms is an array before looping
            foreach ( $terms as $term ) {
                 if ($term instanceof WP_Term) { // Check if it's a WP_Term object
                     $categories_data[] = $this->prepare_category_for_response($term, $request);
                 } else {
                     error_log('Satoloc Connector: Encountered non-WP_Term item in get_terms result.');
                 }
            }
        } else {
             error_log('Satoloc Connector: get_terms did not return an array for categories.');
        }

        return new WP_REST_Response( $categories_data, 200 );
    }

    // --- get_items (Added Logging) ---
    public function get_items( $request ) {
        // (Keep argument parsing as before)
        $params = $request->get_params();
        $args = [
            'post_type'      => isset($params['post_type']) ? array_map('sanitize_key', explode(',', $params['post_type'])) : ['post', 'page'],
            'post_status'    => isset($params['post_status']) ? array_map('sanitize_key', explode(',', $params['post_status'])) : ['publish', 'draft', 'pending', 'future', 'private'],
            'posts_per_page' => isset($params['per_page']) ? (int) $params['per_page'] : 100,
            'paged'          => isset($params['page']) ? (int) $params['page'] : 1,
            'orderby'        => isset($params['orderby']) ? sanitize_key($params['orderby']) : 'modified',
            'order'          => isset($params['order']) && in_array(strtoupper($params['order']), ['ASC', 'DESC']) ? strtoupper($params['order']) : 'DESC',
        ];
        $allowed_types = $this->get_allowed_post_types();
        $args['post_type'] = array_intersect($args['post_type'], $allowed_types);
        if (empty($args['post_type'])) { $args['post_type'] = $allowed_types; }
        if (isset($params['lang']) && !empty($params['lang'])) {
             if ( function_exists('pll_the_languages') || defined('ICL_SITEPRESS_VERSION') ) {
                 $args['lang'] = sanitize_key($params['lang']);
             }
        }

        $posts_query = new WP_Query( $args );
        $posts_data = [];

        if ( $posts_query->have_posts() ) {
            error_log("Satoloc Connector: Processing " . count($posts_query->posts) . " posts for page " . $args['paged']); // Log count
            while ( $posts_query->have_posts() ) {
                $posts_query->the_post();
                $post_id = get_the_ID();
                $post_object = get_post( $post_id );

                if ($post_object) {
                    error_log("Satoloc Connector: Preparing item for post ID: " . $post_id); // Log before preparing
                    try {
                        $response_item = $this->prepare_item_for_response( $post_object, $request );

                        // Check if prepare_item_for_response returned an error
                        if (is_wp_error($response_item)) {
                             error_log("Satoloc Connector: Error preparing post ID " . $post_id . ": " . $response_item->get_error_message());
                             // Optionally skip this item or handle differently
                             continue;
                        }

                        // Check if the response is a WP_REST_Response object before preparing for collection
                        if ($response_item instanceof WP_REST_Response) {
                            $posts_data[] = $this->prepare_response_for_collection( $response_item );
                        } else {
                             error_log("Satoloc Connector: prepare_item_for_response did not return WP_REST_Response for post ID " . $post_id);
                             // Handle case where it might return raw data or something else if logic changes
                        }

                    } catch (Exception $e) {
                         // Catch potential fatal errors during preparation (though PHP might not always allow catching fatals)
                         error_log("Satoloc Connector: Exception preparing post ID " . $post_id . ": " . $e->getMessage());
                         // Decide how to handle - skip item? return error?
                         continue; // Skip this item on exception
                    }
                } else {
                     error_log("Satoloc Connector: Failed to get post object for ID: " . $post_id);
                }
            }
            wp_reset_postdata();
        } else {
             error_log("Satoloc Connector: No posts found for page " . $args['paged']);
        }

        // (Keep response creation and header setting as before)
        $response = new WP_REST_Response( $posts_data, 200 );
        $total_posts = $posts_query->found_posts; $max_pages = $posts_query->max_num_pages;
        $response->header( 'X-WP-Total', (int) $total_posts ); $response->header( 'X-WP-TotalPages', (int) $max_pages );
        $base_url = rest_url( sprintf( '%s/%s', $this->namespace, 'content' ) ); $query_params = $request->get_query_params();
        if ( $args['paged'] > 1 ) { $prev_page = $args['paged'] - 1; $prev_link = add_query_arg( array_merge($query_params, ['page' => $prev_page]), $base_url ); $response->add_link( 'prev', $prev_link ); }
        if ( $args['paged'] < $max_pages ) { $next_page = $args['paged'] + 1; $next_link = add_query_arg( array_merge($query_params, ['page' => $next_page]), $base_url ); $response->add_link( 'next', $next_link ); }

        error_log("Satoloc Connector: Finished processing page " . $args['paged'] . ". Returning " . count($posts_data) . " items."); // Log finish
        return $response;
    }

    // --- get_item (Keep as before, logging added inside) ---
     public function get_item( $request ) {
        $id = (int) $request['id']; $post = get_post( $id );
        if ( empty( $post ) || ! in_array( $post->post_type, $this->get_allowed_post_types() ) ) {
            return new WP_Error( 'rest_post_invalid_id', __( 'Invalid content ID or type.', 'satoloc-insight-connector' ), [ 'status' => 404 ] );
        }
        $response = $this->prepare_item_for_response( $post, $request );
        if (is_wp_error($response)) {
             error_log("Satoloc Connector: Error preparing single item response for post ID " . $id . ": " . $response->get_error_message());
        } elseif ($response instanceof WP_REST_Response) {
            $data = $response->get_data();
            if (!isset($data['categories'])) {
                 error_log("Satoloc Connector: Categories field missing from prepared response for post ID: " . $id);
            }
        }
        return $response;
    }

    // --- Theme Builder Detection and Handling ---
    
    /**
     * Detect active page builders on the site
     * @return array List of detected page builders
     */
    private function detect_active_page_builders() {
        $builders = [];
        
        // Check for AVADA/Fusion Builder
        if ( defined('AVADA_VERSION') || class_exists('FusionBuilder') || function_exists('fusion_builder_map') ) {
            $builders[] = 'avada';
        }
        
        // Check for Elementor
        if ( defined('ELEMENTOR_VERSION') || class_exists('\Elementor\Plugin') ) {
            $builders[] = 'elementor';
        }
        
        // Check for Divi Builder
        if ( defined('ET_BUILDER_VERSION') || function_exists('et_divi_builder_init') ) {
            $builders[] = 'divi';
        }
        
        // Check for Beaver Builder
        if ( class_exists('FLBuilder') || defined('FL_BUILDER_VERSION') ) {
            $builders[] = 'beaver';
        }
        
        // Check for WPBakery Page Builder
        if ( defined('WPB_VC_VERSION') || class_exists('Vc_Manager') ) {
            $builders[] = 'wpbakery';
        }
        
        // Check for Gutenberg Block Editor (WordPress 5.0+)
        if ( function_exists('has_blocks') ) {
            $builders[] = 'gutenberg';
        }
        
        return $builders;
    }
    
    /**
     * Check if content contains page builder shortcodes/elements
     * @param string $content The post content
     * @return string|null The detected page builder type, or null if none detected
     */
    private function detect_page_builder_in_content($content) {
        if (empty($content)) {
            return null;
        }
        
        // Check for AVADA/Fusion Builder shortcodes
        if (preg_match('/\[fusion_/', $content)) {
            return 'avada';
        }
        
        // Check for Elementor data
        if (strpos($content, 'elementor') !== false || strpos($content, 'data-elementor') !== false) {
            return 'elementor';
        }
        
        // Check for Divi shortcodes
        if (preg_match('/\[et_pb_/', $content)) {
            return 'divi';
        }
        
        // Check for Beaver Builder shortcodes
        if (preg_match('/\[fl_builder_/', $content)) {
            return 'beaver';
        }
        
        // Check for WPBakery shortcodes
        if (preg_match('/\[vc_/', $content)) {
            return 'wpbakery';
        }
        
        return null;
    }
    
    /**
     * Apply page builder specific meta fields to enable builder functionality
     * @param int $post_id The post ID
     * @param string $builder The page builder type
     * @param string $content The post content
     */
    private function apply_page_builder_meta($post_id, $builder, $content = '') {
        switch ($builder) {
            case 'avada':
                // Enable Fusion Builder for this post
                update_post_meta($post_id, '_fusion_builder_status', 'active');
                
                // Force Fusion Builder to be the default editor (bypasses auto-activation setting)
                update_post_meta($post_id, '_fusion_builder_default_editor', 'fusion_builder');
                
                // Set the builder to be used (alternative approach)
                update_post_meta($post_id, '_fusion_builder_use_builder', 'yes');
                
                // Set editor preference to force Fusion Builder
                update_post_meta($post_id, '_fusion_builder_editor_preference', 'fusion_builder');
                
                // Force auto-activation for this specific post (overrides global setting)
                update_post_meta($post_id, '_fusion_builder_auto_activate', 'on');
                
                // Alternative meta field for auto-activation
                update_post_meta($post_id, '_fusion_builder_enabled', '1');
                
                // Set admin editor preference (checked when loading edit page)
                update_post_meta($post_id, '_fusion_builder_admin_editor', 'fusion_builder');
                
                // Force builder mode flag
                update_post_meta($post_id, '_fusion_builder_force_mode', 'builder');
                
                // Store the original shortcode content for Fusion Builder
                if (!empty($content)) {
                    update_post_meta($post_id, '_fusion_builder_content', $content);
                }
                
                // Ensure the post is marked as using Fusion Builder layout
                update_post_meta($post_id, '_fusion_builder_converted', 'yes');
                
                // Set page template to use Fusion Builder (if it's a page)
                $post_type = get_post_type($post_id);
                if ($post_type === 'page') {
                    update_post_meta($post_id, '_wp_page_template', 'default');
                }
                
                // Trigger AVADA to process the content (if function exists)
                if (function_exists('fusion_builder_convert_to_builder')) {
                    fusion_builder_convert_to_builder($post_id);
                    error_log("Satoloc Connector: Triggered AVADA content conversion for post {$post_id}");
                }
                
                // Alternative: Use WordPress action to notify AVADA
                do_action('fusion_builder_post_created', $post_id, $content);
                
                // Add filter to force auto-activation for this specific post
                add_filter('fusion_builder_auto_activate', function($auto_activate, $post_id_check) use ($post_id) {
                    if ($post_id_check == $post_id) {
                        return true; // Force auto-activation for this post
                    }
                    return $auto_activate;
                }, 10, 2);
                
                // Add admin hook to redirect to builder mode when editing this post
                add_action('admin_init', function() use ($post_id) {
                    if (isset($_GET['post']) && $_GET['post'] == $post_id && isset($_GET['action']) && $_GET['action'] == 'edit') {
                        // Check if we're not already in builder mode
                        if (!isset($_GET['fb-edit'])) {
                            // Redirect to builder mode
                            $builder_url = admin_url('post.php?post=' . $post_id . '&action=edit&fb-edit=1');
                            wp_redirect($builder_url);
                            exit;
                        }
                    }
                });
                
                error_log("Satoloc Connector: Applied comprehensive AVADA meta fields to post {$post_id}");
                break;
                
            case 'elementor':
                // Enable Elementor for this post
                update_post_meta($post_id, '_elementor_edit_mode', 'builder');
                update_post_meta($post_id, '_elementor_template_type', 'wp-post');
                error_log("Satoloc Connector: Applied Elementor meta fields to post {$post_id}");
                break;
                
            case 'divi':
                // Enable Divi Builder for this post
                update_post_meta($post_id, '_et_pb_use_builder', 'on');
                update_post_meta($post_id, '_et_pb_old_content', $content);
                error_log("Satoloc Connector: Applied Divi meta fields to post {$post_id}");
                break;
                
            case 'beaver':
                // Enable Beaver Builder for this post
                update_post_meta($post_id, '_fl_builder_enabled', '1');
                error_log("Satoloc Connector: Applied Beaver Builder meta fields to post {$post_id}");
                break;
                
            case 'wpbakery':
                // Enable WPBakery Page Builder for this post
                update_post_meta($post_id, '_wpb_vc_js_status', 'true');
                error_log("Satoloc Connector: Applied WPBakery meta fields to post {$post_id}");
                break;
                
            default:
                error_log("Satoloc Connector: Unknown page builder type: {$builder}");
                break;
        }
    }
    
    /**
     * Handle page builder integration for created/updated posts
     * @param int $post_id The post ID
     * @param string $content The post content
     */
    private function handle_page_builder_integration($post_id, $content) {
        // First, detect what page builders are active on this site
        $active_builders = $this->detect_active_page_builders();
        
        if (empty($active_builders)) {
            error_log("Satoloc Connector: No page builders detected on this site");
            return;
        }
        
        // Then, check if the content contains page builder elements
        $content_builder = $this->detect_page_builder_in_content($content);
        
        if ($content_builder && in_array($content_builder, $active_builders)) {
            // Content contains page builder elements and the builder is active
            $this->apply_page_builder_meta($post_id, $content_builder, $content);
            error_log("Satoloc Connector: Detected and applied {$content_builder} integration for post {$post_id}");
        } else {
            error_log("Satoloc Connector: No page builder content detected or builder not active. Active builders: " . implode(', ', $active_builders));
        }
    }
    
    // --- create_item, update_item, delete_item (Keep as before) ---
    public function create_item( $request ) {
        $params = $request->get_params();
        if ( empty( $params['title'] ) ) return new WP_Error( 'rest_missing_param', __( 'Missing required parameter(s): title', 'satoloc-insight-connector' ), [ 'status' => 400 ] );
        if ( empty( $params['post_type'] ) || ! in_array( $params['post_type'], $this->get_allowed_post_types() ) ) return new WP_Error( 'rest_invalid_param', __( 'Invalid parameter(s): post_type', 'satoloc-insight-connector' ), [ 'status' => 400 ] );
        if ( empty( $params['status'] ) || ! in_array( $params['status'], $this->get_allowed_post_statuses() ) ) return new WP_Error( 'rest_invalid_param', __( 'Invalid parameter(s): status', 'satoloc-insight-connector' ), [ 'status' => 400 ] );

        $post_data = [
            'post_title'   => sanitize_text_field( $params['title'] ),
            'post_content' => isset($params['content']) ? wp_kses_post( $params['content'] ) : '',
            'post_type'    => sanitize_key( $params['post_type'] ),
            'post_status'  => sanitize_key( $params['status'] ),
            'post_author'  => $this->get_author_id_for_sync(),
            'post_name'    => isset($params['slug']) ? sanitize_title($params['slug']) : null,
        ];

        $valid_category_ids = $this->validate_and_get_category_ids($params);
        if ( empty( $valid_category_ids ) ) {
            $default_cat_id = (int) get_option( 'default_category' );
            if ( $default_cat_id > 0 && term_exists($default_cat_id, 'category') ) {
                $valid_category_ids[] = $default_cat_id;
            }
        }
        if ( $post_data['post_type'] === 'post' && !empty($valid_category_ids) ) {
             $post_data['post_category'] = $valid_category_ids;
        }

        $post_id = wp_insert_post( $post_data, true );
        if ( is_wp_error( $post_id ) ) { $post_id->add_data( [ 'status' => 500 ] ); return $post_id; }

        if ( $post_data['post_type'] === 'post' && !empty($valid_category_ids) ) {
             wp_set_post_terms( $post_id, $valid_category_ids, 'category', false );
        }

        if ( isset( $params['language_code'] ) ) { $this->handle_language_assignment($post_id, $params['language_code']); }
        if ( isset( $params['meta_input'] ) && is_array($params['meta_input'])) { $this->handle_meta_input($post_id, $params['meta_input']); }
        if (isset($params['translation_of']) && is_numeric($params['translation_of']) && isset($params['language_code'])) { $this->handle_translation_linking($post_id, $params['translation_of'], $params['language_code']); }

        // Handle page builder integration
        $content = isset($params['content']) ? $params['content'] : '';
        $this->handle_page_builder_integration($post_id, $content);

        $post = get_post( $post_id );
        if ( ! $post ) return new WP_Error( 'rest_post_creation_failed', __( 'Failed post retrieval after creation.', 'satoloc-insight-connector' ), [ 'status' => 500 ] );

        $response = $this->prepare_item_for_response( $post, $request );
        $response->set_status( 201 );
        $response->header( 'Location', rest_url( sprintf( '%s/%s/%d', $this->namespace, 'content', $post_id ) ) );
        return $response;
    }

    public function update_item( $request ) {
        $id = (int) $request['id']; $post = get_post( $id );
        if ( empty( $post ) || ! in_array( $post->post_type, $this->get_allowed_post_types() ) ) return new WP_Error( 'rest_post_invalid_id', __( 'Invalid content ID or type provided for update.', 'satoloc-insight-connector' ), [ 'status' => 404 ] );

        $params = $request->get_params(); $post_data = [ 'ID' => $id ];

        if ( isset( $params['title'] ) ) $post_data['post_title'] = sanitize_text_field( $params['title'] );
        if ( isset( $params['content'] ) ) $post_data['post_content'] = wp_kses_post( $params['content'] );
        if ( isset( $params['status'] ) ) {
            if ( ! in_array( $params['status'], $this->get_allowed_post_statuses() ) ) return new WP_Error( 'rest_invalid_param', __( 'Invalid parameter(s): status', 'satoloc-insight-connector' ), [ 'status' => 400 ] );
            $post_data['post_status'] = sanitize_key( $params['status'] );
        }
        if ( isset( $params['slug'] ) ) $post_data['post_name'] = sanitize_title( $params['slug'] );

        $update_categories = array_key_exists('category_id', $params) || array_key_exists('categories', $params);
        $valid_category_ids = $this->validate_and_get_category_ids($params);

        if ( $update_categories && empty($valid_category_ids) ) {
             $default_cat_id = (int) get_option( 'default_category' );
             if ( $default_cat_id > 0 && term_exists($default_cat_id, 'category') ) {
                 $valid_category_ids[] = $default_cat_id;
             }
        }

        $update_meta = isset( $params['meta_input'] ) && is_array($params['meta_input']);
        $has_core_updates = count( $post_data ) > 1;

        if ( !$has_core_updates && !$update_categories && !$update_meta ) return new WP_Error( 'rest_no_update_data_provided', __( 'No valid fields provided for update.', 'satoloc-insight-connector' ), [ 'status' => 400 ] );

        if ($has_core_updates) {
            $result = wp_update_post( $post_data, true );
            if ( is_wp_error( $result ) ) { $result->add_data( [ 'status' => 500 ] ); return $result; }
        }

        if ( $post->post_type === 'post' && $update_categories ) {
             wp_set_post_terms( $id, $valid_category_ids, 'category', false );
        }

        if ($update_meta) { $this->handle_meta_input($id, $params['meta_input']); }

    // Handle page builder integration for updated content
    if (isset($params['content'])) {
        $this->handle_page_builder_integration($id, $params['content']);
    }

    $updated_post = get_post( $id );
        if ( ! $updated_post ) return new WP_Error( 'rest_post_update_failed', __( 'Failed post retrieval after update.', 'satoloc-insight-connector' ), [ 'status' => 500 ] );
        return $this->prepare_item_for_response( $updated_post, $request );
    }

     public function delete_item( $request ) {
        $id = (int) $request['id']; $force = isset( $request['force'] ) ? rest_sanitize_boolean( $request['force'] ) : false;
        $post = get_post( $id );
        if ( empty( $post ) || ! in_array( $post->post_type, $this->get_allowed_post_types() ) ) return new WP_Error( 'rest_post_invalid_id', __( 'The requested content item cannot be found or deleted.', 'satoloc-insight-connector' ), [ 'status' => 404 ] );

        $response_data = null;
        // Wrap prepare_item_for_response in try-catch in case it errors
        try {
             $previous_response = $this->prepare_item_for_response( $post, $request );
             if ($previous_response instanceof WP_REST_Response) {
                  $response_data = $previous_response->get_data();
             } else {
                  // Handle case where prepare failed (e.g., returned WP_Error)
                  error_log("Satoloc Connector: Failed to prepare previous data for post ID " . $id . " before deletion.");
                  $response_data = ['id' => $id, 'title' => $post->post_title]; // Basic fallback data
             }
        } catch (Exception $e) {
             error_log("Satoloc Connector: Exception preparing previous data for post ID " . $id . " before deletion: " . $e->getMessage());
             $response_data = ['id' => $id, 'title' => $post->post_title]; // Basic fallback data
        }


        $result = wp_delete_post( $id, $force );
        if ( ! $result ) return new WP_Error( 'rest_cannot_delete', __( 'The content item could not be deleted.', 'satoloc-insight-connector' ), [ 'status' => 500 ] );

        $final_response_data = [ 'deleted'  => true, 'previous' => $response_data ];
        return new WP_REST_Response( $final_response_data, 200 );
    }

    // --- prepare_item_for_response (More Robust) ---
    public function prepare_item_for_response( $item, $request ) {
        if ( ! $item instanceof WP_Post ) {
             if ( is_numeric($item) ) $item = get_post( $item );
             if ( ! $item instanceof WP_Post ) {
                  error_log("Satoloc Connector: Invalid item passed to prepare_item_for_response.");
                  return new WP_Error('invalid_item_for_response', 'Invalid item provided.', ['status' => 500]);
             }
        }

        $data = [];
        $schema = $this->get_public_item_schema();
        $properties = $schema['properties'] ?? [];

        // --- Basic Properties ---
        if ( isset( $properties['id'] ) ) $data['id'] = $item->ID;
        if ( isset( $properties['title'] ) ) $data['title'] = $item->post_title;
        if ( isset( $properties['content'] ) ) $data['content'] = $item->post_content;
        if ( isset( $properties['status'] ) ) $data['status'] = $item->post_status;
        if ( isset( $properties['post_type'] ) ) $data['post_type'] = $item->post_type;
        if ( isset( $properties['date_created_gmt'] ) ) $data['date_created_gmt'] = mysql_to_rfc3339( $item->post_date_gmt );
        if ( isset( $properties['date_modified_gmt'] ) ) $data['date_modified_gmt'] = mysql_to_rfc3339( $item->post_modified_gmt );
        if ( isset( $properties['link'] ) ) $data['link'] = get_permalink( $item->ID );
        if ( isset( $properties['featured_image_url'] ) ) { $furl = get_the_post_thumbnail_url( $item->ID, 'full' ); $data['featured_image_url'] = $furl ?: null; }
        if ( isset( $properties['slug'] ) ) $data['slug'] = $item->post_name;

        // --- Language Info ---
        if (isset($properties['language_code']) || isset($properties['language_name']) || isset($properties['translations'])) {
            $language_info = $this->get_language_information($item->ID, $item->post_type);
            if ( isset( $properties['language_code'] ) ) $data['language_code'] = $language_info['code'];
            if ( isset( $properties['language_name'] ) ) $data['language_name'] = $language_info['name'];
            if ( isset( $properties['translations'] ) ) $data['translations'] = $language_info['translations'];
        }

        // --- Category Info (More Defensive) ---
        $category_data = []; // Initialize as empty array
        if ( isset( $properties['categories'] ) && $item->post_type === 'post' ) {
            error_log("Satoloc Connector DEBUG (Post ID: " . $item->ID . "): Attempting to get categories.");
             $post_categories = wp_get_post_terms( $item->ID, 'category', ['fields' => 'all'] );

             if (is_wp_error($post_categories)) {
                error_log("Satoloc Connector DEBUG (Post ID: " . $item->ID . "): WP_Error getting categories: " . $post_categories->get_error_message());
             } elseif (empty($post_categories)) {
                  error_log("Satoloc Connector DEBUG (Post ID: " . $item->ID . "): wp_get_post_terms returned empty array.");
             } else {
                 error_log("Satoloc Connector DEBUG (Post ID: " . $item->ID . "): Raw wp_get_post_terms result: " . print_r($post_categories, true)); // Log raw result
             }

             if (is_wp_error($post_categories)) {
                error_log("Satoloc Connector DEBUG (Post ID: " . $item->ID . "): WP_Error getting categories: " . $post_categories->get_error_message());
                  // Decide: return error or empty array? Empty array might be safer for client.
                  // $category_data = []; // Already initialized
             } elseif (is_array($post_categories)) { // Ensure it's an array before looping
                 foreach ($post_categories as $category) {
                     // Use is_a for specific type check, more reliable than is_object
                     if ( $category instanceof WP_Term ) {
                         error_log("Satoloc Connector DEBUG (Post ID: " . $item->ID . "): Processing valid category: ID=" . $category->term_id . ", Name=" . $category->name); // <-- ADD LOG
                         $category_data[] = [
                             'id'   => $category->term_id,
                             'name' => $category->name,
                             'slug' => $category->slug,
                         ];
                     } else {
                          error_log("Satoloc Connector: Item in wp_get_post_terms result is not a WP_Term object for post ID: " . $item->ID);
                     }
                 }
             } else {
                  error_log("Satoloc Connector: wp_get_post_terms did not return an array or WP_Error for post ID: " . $item->ID);
             }
        } else if ($item->post_type !== 'post') {
             error_log("Satoloc Connector DEBUG (Post ID: " . $item->ID . "): Skipping category fetch, post_type is '" . $item->post_type . "'"); // <-- ADD LOG
        }
        // Always assign the categories key, even if empty or not a 'post'
        error_log("Satoloc Connector DEBUG (Post ID: " . $item->ID . "): Final category_data being assigned: " . print_r($category_data, true)); // <-- ADD LOG
        $data['categories'] = $category_data;


        // --- Create and Return Response ---
        $response = new WP_REST_Response();
        $response->set_data($data);
        $response = $this->add_links_to_response($response, $item); // Add links after setting data

        return $response;
    }


    // --- prepare_category_for_response (Keep as before) ---
    protected function prepare_category_for_response($term, $request) {
        $data = [];
        $schema = $this->get_public_category_schema();
        if (isset($schema['properties']['id'])) $data['id'] = (int) $term->term_id;
        if (isset($schema['properties']['name'])) $data['name'] = $term->name;
        if (isset($schema['properties']['slug'])) $data['slug'] = $term->slug;
        if (isset($schema['properties']['count'])) $data['count'] = (int) $term->count;
        return $data;
    }

    // --- prepare_response_for_collection (Keep as before) ---
    protected function prepare_response_for_collection( $response ) {
        if ( ! ( $response instanceof WP_REST_Response ) ) {
            error_log("Satoloc Connector: Non-WP_REST_Response passed to prepare_response_for_collection.");
            // If it's a WP_Error, maybe extract the error message?
            if (is_wp_error($response)) {
                 return ['error' => $response->get_error_message(), 'error_code' => $response->get_error_code()];
            }
            return $response; // Return as-is or handle error
        }
        $data = (array) $response->get_data();
        $links = $response->get_links();
        if ( ! empty( $links ) ) { $data['_links'] = $links; }
        return $data;
    }

    // --- add_links_to_response (Keep as before) ---
     protected function add_links_to_response($response, $item) {
         if ( ! $item instanceof WP_Post || empty($item->ID) ) {
              error_log("Satoloc Connector: Invalid item passed to add_links_to_response.");
              return $response;
         }
        $base = sprintf( '%s/%s', $this->namespace, 'content' );
        $response->add_link( 'self', rest_url( trailingslashit( $base ) . $item->ID ) );
        $response->add_link( 'collection', rest_url( $base ) );
        return $response;
    }

    // --- get_collection_params, get_public_item_schema, get_public_category_schema, get_endpoint_args_for_item_schema (Keep as before) ---
     public function get_collection_params() {
        $params = [
            'context' => $this->get_context_param( [ 'default' => 'view' ] ),
            'page' => [ 'description' => __( 'Current page of the collection.', 'satoloc-insight-connector' ), 'type' => 'integer', 'default' => 1, 'minimum' => 1, 'validate_callback' => 'rest_validate_request_arg' ],
            'per_page' => [ 'description' => __( 'Maximum number of items to be returned in result set.', 'satoloc-insight-connector' ), 'type' => 'integer', 'default' => 100, 'minimum' => 1, 'maximum' => 100, 'validate_callback' => 'rest_validate_request_arg' ],
            'post_type' => [ 'description' => __( 'Limit results to one or more post types (comma-separated).', 'satoloc-insight-connector' ), 'type' => 'string', 'default' => 'post,page', 'validate_callback' => 'rest_validate_request_arg' ],
            'post_status' => [ 'description' => __( 'Limit results to one or more post statuses (comma-separated).', 'satoloc-insight-connector' ), 'type' => 'string', 'default' => 'publish,draft,pending,future,private', 'validate_callback' => 'rest_validate_request_arg' ],
            'orderby' => [ 'description' => __( 'Sort collection by object attribute.', 'satoloc-insight-connector' ), 'type' => 'string', 'default' => 'date', 'enum' => ['date', 'modified', 'id', 'title', 'slug'], 'validate_callback' => 'rest_validate_request_arg' ],
            'order' => [ 'description' => __( 'Order sort attribute ascending or descending.', 'satoloc-insight-connector' ), 'type' => 'string', 'default' => 'desc', 'enum' => ['asc', 'desc'], 'validate_callback' => 'rest_validate_request_arg' ],
        ];
        if ( function_exists('pll_the_languages') || defined('ICL_SITEPRESS_VERSION') ) {
             $params['lang'] = [ 'description' => __( 'Limit results by language code.', 'satoloc-insight-connector' ), 'type' => 'string', 'validate_callback' => 'rest_validate_request_arg' ];
        }
        return $params;
    }
     public function get_public_item_schema() {
        static $schema;
        if ( $schema ) { return $schema; }
        $schema = [
            '$schema'    => 'http://json-schema.org/draft-04/schema#',
            'title'      => 'satoloc_insight_content_item',
            'type'       => 'object',
            'properties' => [
                'id' => [ 'description' => __( 'Unique identifier for the item.', 'satoloc-insight-connector' ), 'type' => 'integer', 'context' => ['view', 'edit', 'embed'], 'readonly' => true ],
                'title' => [ 'description' => __( 'The title for the item.', 'satoloc-insight-connector' ), 'type' => 'string', 'context' => ['view', 'edit', 'embed'], 'required' => true ],
                'content' => [ 'description' => __( 'The HTML content for the item.', 'satoloc-insight-connector' ), 'type' => 'string', 'context' => ['view', 'edit'] ],
                'status' => [ 'description' => __( 'A named status for the item.', 'satoloc-insight-connector' ), 'type' => 'string', 'enum' => $this->get_allowed_post_statuses(), 'context' => ['view', 'edit'], 'required' => true ],
                'post_type' => [ 'description' => __( 'The type of the item.', 'satoloc-insight-connector' ), 'type' => 'string', 'enum' => $this->get_allowed_post_types(), 'context' => ['view', 'edit'], 'required' => true ],
                'date_created_gmt' => [ 'description' => __( 'The date the item was published, in the site\'s timezone (GMT).', 'satoloc-insight-connector' ), 'type' => ['string', 'null'], 'format' => 'date-time', 'context' => ['view', 'edit', 'embed'], 'readonly' => true ],
                'date_modified_gmt' => [ 'description' => __( 'The date the item was last modified, in the site\'s timezone (GMT).', 'satoloc-insight-connector' ), 'type' => ['string', 'null'], 'format' => 'date-time', 'context' => ['view', 'edit', 'embed'], 'readonly' => true ],
                'link' => [ 'description' => __( 'URL to the item.', 'satoloc-insight-connector' ), 'type' => 'string', 'format' => 'uri', 'context' => ['view', 'embed'], 'readonly' => true ],
                'featured_image_url' => [ 'description' => __( 'URL of the full-sized featured image.', 'satoloc-insight-connector' ), 'type' => ['string', 'null'], 'format' => 'uri', 'context' => ['view', 'edit', 'embed'], 'readonly' => true ],
                'slug' => [ 'description' => __( 'An alphanumeric identifier for the item unique to its type.', 'satoloc-insight-connector'), 'type' => 'string', 'context' => ['view', 'edit', 'embed'] ],
                'language_code' => [ 'description' => __( 'The language code for the item (e.g., en, fr).', 'satoloc-insight-connector' ), 'type' => ['string', 'null'], 'context' => ['view', 'edit', 'embed'], 'readonly' => true ],
                'language_name' => [ 'description' => __( 'The human-readable language name.', 'satoloc-insight-connector' ), 'type' => ['string', 'null'], 'context' => ['view', 'embed'], 'readonly' => true ],
                'translations' => [ 'description' => __( 'Map of language codes to translated post IDs.', 'satoloc-insight-connector' ), 'type' => ['object', 'null'], 'properties' => [], 'additionalProperties' => ['type' => 'integer'], 'context' => ['view', 'edit', 'embed'], 'readonly' => true ],
                'categories' => [
                    'description' => __( 'The categories assigned to the item (array of objects).', 'satoloc-insight-connector' ),
                    'type'        => ['array', 'null'],
                    'context'     => ['view', 'edit', 'embed'], // Keep edit context here
                    'readonly'    => true,
                    'items'       => [
                        'type'       => 'object',
                        'properties' => [
                            'id'   => ['description' => __('Category ID.', 'satoloc-insight-connector'), 'type' => 'integer', 'readonly' => true],
                            'name' => ['description' => __('Category Name.', 'satoloc-insight-connector'), 'type' => 'string', 'readonly' => true],
                            'slug' => ['description' => __('Category Slug.', 'satoloc-insight-connector'), 'type' => 'string', 'readonly' => true],
                        ],
                    ],
                ],
            ],
        ];
        return $schema;
    }
     public function get_public_category_schema() {
        static $schema;
        if ($schema) { return $schema; }
        $schema = [
            '$schema'    => 'http://json-schema.org/draft-04/schema#',
            'title'      => 'satoloc_insight_category_item',
            'type'       => 'object',
            'properties' => [
                'id' => [ 'description' => __( 'Unique identifier for the category.', 'satoloc-insight-connector' ), 'type' => 'integer', 'context' => ['view', 'embed'], 'readonly' => true ],
                'name' => [ 'description' => __( 'HTML title for the category.', 'satoloc-insight-connector' ), 'type' => 'string', 'context' => ['view', 'embed'], 'readonly' => true ],
                'slug' => [ 'description' => __( 'An alphanumeric identifier for the category unique to its type.', 'satoloc-insight-connector' ), 'type' => 'string', 'context' => ['view', 'embed'], 'readonly' => true ],
                'count' => [ 'description' => __( 'Number of published posts for the category.', 'satoloc-insight-connector' ), 'type' => 'integer', 'context' => ['view', 'embed'], 'readonly' => true ],
            ],
        ];
        return $schema;
    }
     public function get_endpoint_args_for_item_schema( $is_creating = true ) {
        $schema = $this->get_public_item_schema(); $properties = $schema['properties'] ?? []; $endpoint_args = [];
        foreach ( $properties as $field_id => $params ) {
             if ( ! empty( $params['readonly'] ) ) continue;
             if ( empty($params['context']) || ! in_array( 'edit', $params['context'] ) ) {
                  if (!($field_id === 'slug' && $is_creating)) continue;
             }
            $arg_data = [
                 'description' => $params['description'] ?? '',
                 'type' => $params['type'] ?? 'string',
                 'enum' => $params['enum'] ?? null,
                 'required' => $is_creating && ! empty( $params['required'] ),
            ];
            if (is_array($arg_data['type'])) {
                $arg_data['type'] = array_values(array_diff($arg_data['type'], ['null']))[0] ?? 'string';
            }
            if ( is_null( $arg_data['enum'] ) ) unset( $arg_data['enum'] );
            $endpoint_args[ $field_id ] = $arg_data;
        }
         if ($is_creating) {
             $endpoint_args['language_code'] = [ 'description' => __('Set language code for the new post (requires multilingual plugin).', 'satoloc-insight-connector'), 'type' => 'string', 'required' => false, ];
             $endpoint_args['translation_of'] = [ 'description' => __('The ID of the original post this is a translation of.', 'satoloc-insight-connector'), 'type' => 'integer', 'required' => false, ];
         }
         $endpoint_args['meta_input'] = [ 'description' => __('Associative array of meta keys and values to set.', 'satoloc-insight-connector'), 'type' => 'object', 'required' => false, ];
         $endpoint_args['category_id'] = [ 'description' => __('Assign a single category ID. Overwrites existing categories.', 'satoloc-insight-connector'), 'type' => 'integer', 'required' => false, ];
         $endpoint_args['categories'] = [ 'description' => __('Assign multiple category IDs (array of integers). Overwrites existing categories.', 'satoloc-insight-connector'), 'type' => 'array', 'items' => [ 'type' => 'integer' ], 'required' => false, ];
        return $endpoint_args;
    }

    // --- validate_and_get_category_ids, handle_language_assignment, handle_meta_input, handle_translation_linking (Keep as before) ---
    private function validate_and_get_category_ids($params) {
        $category_ids = [];
        if ( isset( $params['categories'] ) && is_array( $params['categories'] ) ) {
            $raw_ids = array_map( 'intval', $params['categories'] );
            foreach ($raw_ids as $cat_id) { if ($cat_id > 0) $category_ids[] = $cat_id; }
        } elseif ( isset( $params['category_id'] ) && is_numeric( $params['category_id'] ) ) {
            $cat_id = (int) $params['category_id'];
            if ($cat_id > 0) $category_ids[] = $cat_id;
        }
        $valid_category_ids = [];
        if (!empty($category_ids)) {
            foreach ($category_ids as $cat_id) {
                $term = term_exists($cat_id, 'category');
                if ( $term !== 0 && $term !== null ) { $valid_category_ids[] = $cat_id; }
                else { error_log("Satoloc Insight Connector: Invalid category ID {$cat_id} provided or term does not exist."); }
            }
        }
        return array_unique($valid_category_ids);
    }
    private function handle_language_assignment($post_id, $language_code) {
        $sanitized_code = sanitize_key($language_code);
        if (function_exists('pll_set_post_language')) { pll_set_post_language($post_id, $sanitized_code); }
    }
    private function handle_meta_input($post_id, $meta_input) {
        if (!is_array($meta_input)) return;
        foreach ($meta_input as $meta_key => $meta_value) {
            $sanitized_key = sanitize_key($meta_key);
            if (empty($sanitized_key)) continue;
            if (is_array($meta_value) || is_object($meta_value)) { $sanitized_value = $meta_value; }
            elseif (is_numeric($meta_value)) { $sanitized_value = $meta_value; }
            else { $sanitized_value = sanitize_text_field($meta_value); }
            update_post_meta($post_id, $sanitized_key, $sanitized_value);
        }
    }
     private function handle_translation_linking($post_id, $original_post_id, $language_code) {
         $original_id = (int)$original_post_id; $lang_code = sanitize_key($language_code); $post_type = get_post_type($post_id);
         if ($original_id <= 0 || empty($lang_code) || !$post_type) { error_log("Satoloc Connector: Invalid data for translation linking. Post ID: {$post_id}, Original ID: {$original_post_id}, Lang: {$language_code}"); return; }
         if (function_exists('pll_save_post_translations') && function_exists('pll_get_post_translations') && function_exists('pll_get_post') ) {
             $original_post_lang = pll_get_post_language($original_id, 'slug');
             if (!$original_post_lang) { error_log("Satoloc Connector: Could not get language for original post ID {$original_id} for linking."); return; }
             $translations = pll_get_post_translations($original_id);
             if (!is_array($translations)) { $translations = []; }
             if (!isset($translations[$original_post_lang])) { $translations[$original_post_lang] = $original_id; }
             $translations[$lang_code] = $post_id;
             pll_save_post_translations($translations);
             error_log("Satoloc Connector: Linked post {$post_id} ({$lang_code}) to original {$original_id} ({$original_post_lang}). Translations: " . print_r($translations, true));
         } else { update_post_meta($post_id, '_satoloc_translation_of', $original_id); update_post_meta($post_id, '_satoloc_language_code', $lang_code); error_log("Satoloc Connector: Stored translation relationship in meta for post {$post_id}."); }
     }

    // --- get_language_information (Keep as before) ---
    private function get_language_information($post_id, $post_type = 'post') {
        $info = ['code' => null, 'name' => null, 'translations' => null];
        if ( function_exists( 'pll_get_post_language' ) && function_exists('pll_get_post_translations') ) {
            $lang_slug = pll_get_post_language( $post_id, 'slug' );
            if ( $lang_slug ) {
                $info['code'] = $lang_slug; $info['name'] = pll_get_post_language( $post_id, 'name' );
                $translations = pll_get_post_translations( $post_id );
                if ( is_array( $translations ) && ! empty( $translations ) ) {
                     $valid_translations = array_filter($translations, 'is_int');
                     if (!empty($valid_translations)) { $info['translations'] = $valid_translations; }
                }
            }
        } elseif ( defined( 'ICL_SITEPRESS_VERSION' ) && class_exists('WPML_Post_Element') ) {
             try {
                 $wpml_element_type = 'post_' . $post_type; global $sitepress;
                 if (isset($sitepress) && $sitepress->is_translated_post_type($wpml_element_type)) {
                     $post_element = new WPML_Post_Element( $post_id, $wpml_element_type ); $language_info = $post_element->get_language_details();
                     if ( $language_info && isset( $language_info->code ) ) {
                         $info['code'] = $language_info->code; $active_languages = $sitepress->get_active_languages();
                         if ( isset( $active_languages[ $language_info->code ] ) ) { $info['name'] = $active_languages[ $language_info->code ]['display_name']; }
                         $trid = $post_element->get_trid();
                         if ($trid) {
                             $translations_wpml = $sitepress->get_element_translations($trid, $wpml_element_type, true);
                             if (!empty($translations_wpml)) {
                                 $translations_output = [];
                                 foreach ($translations_wpml as $lang_code => $translation) { if (isset($translation->element_id)) { $translations_output[$lang_code] = (int) $translation->element_id; } }
                                 $original_lang = $post_element->get_source_language_code();
                                 if ($original_lang && !isset($translations_output[$original_lang])) { $translations_output[$info['code']] = $post_id; }
                                 elseif (!$original_lang) { $translations_output[$info['code']] = $post_id; }
                                 if (!empty($translations_output)) { $info['translations'] = $translations_output; }
                             } else { $info['translations'] = [$info['code'] => $post_id]; }
                         }
                     }
                 }
             } catch (Exception $e) { error_log("Satoloc Connector: WPML Error fetching language for post {$post_id}: " . $e->getMessage()); }
        }
        if (is_null($info['code'])) {
             $meta_lang_code = get_post_meta($post_id, '_satoloc_language_code', true);
             if (!empty($meta_lang_code)) { $info['code'] = $meta_lang_code; $info['name'] = $meta_lang_code; }
             $meta_translation_of = get_post_meta($post_id, '_satoloc_translation_of', true);
             if (!empty($meta_translation_of)) { $info['translations'] = ['_source_id' => (int)$meta_translation_of]; }
        }
        return $info;
    }

    // --- get_allowed_post_types, get_allowed_post_statuses, get_author_id_for_sync, get_context_param (Keep as before) ---
    private function get_allowed_post_types() { return apply_filters('satoloc_insight_connector_allowed_post_types', ['post', 'page']); }
    private function get_allowed_post_statuses() { return apply_filters('satoloc_insight_connector_allowed_post_statuses', ['publish', 'draft', 'pending', 'private', 'future']); }
    private function get_author_id_for_sync() { $admin_users = get_users(['role' => 'administrator', 'number' => 1, 'orderby' => 'ID', 'order' => 'ASC']); if (!empty($admin_users)) return $admin_users[0]->ID; return 1; }
    protected function get_context_param( $args = array() ) { $param_details = array( 'description' => __( 'Scope under which the request is made; determines fields present in response.', 'satoloc-insight-connector' ), 'type' => 'string', 'sanitize_callback' => 'sanitize_key', 'validate_callback' => 'rest_validate_request_arg', ); $param_details['enum'] = array( 'view', 'embed', 'edit' ); return array_merge( $param_details, $args ); }

} // End class Satoloc_Insight_Connector_API
?>