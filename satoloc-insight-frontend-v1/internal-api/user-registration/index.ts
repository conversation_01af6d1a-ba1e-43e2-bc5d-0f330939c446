// internal-api/user-registration/index.ts
import { useMutation } from "@tanstack/react-query";
import axios from "axios";

interface RegistrationFormData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  subscriptionPlan?: string;
  subscriptionPrice?: string;
}

interface RegistrationResponse {
  message: string;
  user: {
    id: string;
    username: string;
    email: string;
    subscriptionPlan?: string;
    subscriptionPrice?: string;
  };
}

export const useRegistration = () => {
  return useMutation<RegistrationResponse, Error, RegistrationFormData>({
    mutationFn: async (data) => {
      const response = await axios.post<RegistrationResponse>(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/register/`,
        data
      );
      return response.data;
    },
    onError: (error: any) => {
      // Transform error response into more readable format
      if (error.response?.data?.details) {
        throw new Error(JSON.stringify(error.response.data.details));
      }
      throw error;
    },
  });
};
