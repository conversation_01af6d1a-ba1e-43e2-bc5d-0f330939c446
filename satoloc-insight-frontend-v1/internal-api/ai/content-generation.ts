import { useMutation } from "@tanstack/react-query";
import apiClient from "@/lib/apiClient";

interface GenerateContentParams {
  keyword: string;
  contentType: string;
  prompt: string;
}

interface GenerateContentResponse {
  content: string;
}

export const useGenerateContent = () => {
  return useMutation<GenerateContentResponse, Error, GenerateContentParams>({
    mutationFn: async (params) => {
      try {
        const { data } = await apiClient.post<GenerateContentResponse>(
          "/ai/generate-content/",
          params
        );
        return data;
      } catch (error: any) {
        console.error("Error in useGenerateContent:", error);

        if (error.response?.data?.error) {
          throw new Error(error.response.data.error);
        }
        throw error;
      }
    },
  });
};
