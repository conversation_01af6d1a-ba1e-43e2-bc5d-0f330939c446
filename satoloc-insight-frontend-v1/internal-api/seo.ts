import { useMutation, useQuery } from "@tanstack/react-query";
import axios from "axios";

export interface CrawlRequest {
  url: string;
  language?: string;
  industry?: string;
  max_depth?: number;
  max_pages?: number;
  rate_limit?: number;
  gsc_api_key?: string | null;
  follow_robots?: boolean;
}

export interface CrawlResponse {
  id: number;
  url: string;
  start_time: string;
  end_time: string;
  status: string;
  error_message: string | null;
  max_depth: number;
  max_pages: number | null;
  rate_limit: number;
  gsc_refresh_token: string | null;
  has_gsc_data?: boolean;
  gsc_data?: any; // GSC data object
  pages: any[]; // We'll type this more specifically once we see the actual data structure
}

export interface CrawlResponseWrapper {
  json: CrawlResponse[];
}

export interface MetricsResponse {
  domain_rating: {
    score: number;
    change: number;
  };
  organic_traffic: {
    score: number;
    change: number;
  };
  search_terms: {
    total: number;
    new: number;
  };
  links: {
    internal: number;
    external: number;
    unique_domains: number;
  };
  content_score: {
    score: number;
    change: number;
  };
  gsc_data?: {
    search_analytics?: {
      average_position: number;
      total_impressions: number;
      total_clicks: number;
      average_ctr: number;
      top_queries: Array<{
        query: string;
        impressions: number;
        clicks: number;
        position: number;
        ctr: number;
      }>;
    };
  };
}

export interface TechnicalMetric {
  label: string;
  value: number;
  regionalAverage: number;
  status: "success" | "warning" | "error" | "missing";
}

export interface TechnicalMetricsResponse {
  metrics: TechnicalMetric[];
}

export interface RegionalTrendsResponse {
  labels: string[];
  datasets: {
    organic_keywords: number[];
    competitor_keywords: number[];
    avg_position: number[];
    visibility: number[];
  };
}

export interface SeoOptimizationChecklist {
  progress: number;
  sections: {
    title: string;
    icon: "warning" | "trending" | "technical";
    subtitle?: string;
    items: {
      text: string;
      trafficPotential?: string;
    }[];
  }[];
}

export interface GscDataResponse {
  search_analytics?: {
    total_impressions: number;
    total_clicks: number;
    average_position: number;
    average_ctr: number;
    top_queries: Array<{
      query: string;
      impressions: number;
      clicks: number;
      position: number;
      ctr: number;
    }>;
    top_pages: Array<{
      page: string;
      impressions: number;
      clicks: number;
      position: number;
      ctr: number;
    }>;
    performance_by_device: {
      desktop: {
        impressions: number;
        clicks: number;
        position: number;
        ctr: number;
      };
      mobile: {
        impressions: number;
        clicks: number;
        position: number;
        ctr: number;
      };
      tablet: {
        impressions: number;
        clicks: number;
        position: number;
        ctr: number;
      };
    };
  };
  core_web_vitals?: Array<{
    metric: string;
    status: string;
    value: number;
    threshold: number;
  }>;
  coverage_state?: string;
  error?: string;
}

export const useCrawlWebsite = (options?: {
  onSuccess?: (data: CrawlResponse) => void;
}) => {
  return useMutation<CrawlResponse, Error, CrawlRequest>({
    mutationFn: async (data) => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/seo/crawls/`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              url: data.url,
              language: data.language || "en",
              industry: data.industry || "general",
              max_depth: data.max_depth || 1,
              max_pages: data.max_pages || 100,
              rate_limit: data.rate_limit || 2,
              follow_robots: data.follow_robots ?? true,
              ...(data.gsc_api_key && { gsc_refresh_token: data.gsc_api_key }),
            }),
            credentials: "include",
          }
        );

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({
            message: `HTTP error ${response.status}: ${response.statusText}`,
          }));
          throw new Error(
            errorData?.message ||
              `Failed to crawl website: ${response.status} ${response.statusText}`
          );
        }

        const result = await response.json();

        // Validate the response structure
        if (!result || typeof result !== "object") {
          console.error("Invalid crawl response:", result);
          throw new Error("Invalid response from server");
        }

        return result;
      } catch (error) {
        console.error("Crawl error:", error);
        throw error;
      }
    },
    onError: (error) => {
      console.error("Mutation error:", error);
    },
    ...options,
  });
};

export const useGetLatestCrawl = () => {
  return useQuery<CrawlResponse | null>(
    ["latestCrawl"],
    async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/seo/crawls/latest/`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
            credentials: "include",
          }
        );

        if (response.status === 404) {
          // Return null for no crawls instead of throwing
          console.debug("No latest crawl found (404)");
          return null;
        }

        if (!response.ok) {
          console.debug(`Failed to fetch latest crawl: ${response.status}`);
          return null; // Return null instead of throwing
        }

        const data = await response.json();

        // Handle the data structure correctly - the API returns a wrapper with json array
        const crawlData = data.json?.[0] || data;

        return crawlData;
      } catch (error) {
        // Silently handle errors and return null instead of throwing
        console.debug("Error fetching latest crawl:", error);
        return null;
      }
    },
    {
      staleTime: 30000, // Consider data fresh for 30 seconds
      retry: false, // Don't retry on error
      refetchOnWindowFocus: false, // Don't refetch when window gains focus
      onError: () => {
        // Suppress error reporting to the console
      },
    }
  );
};

export const useGetCrawlResults = (crawlId?: number) => {
  return useQuery<CrawlResponse>(
    ["crawlResults", crawlId],
    async () => {
      if (!crawlId) return null;

      // Skip API call for temporary crawl IDs
      if (crawlId === -1) {
        return {
          id: -1,
          url: "",
          start_time: new Date().toISOString(),
          end_time: "",
          status: "pending",
          error_message: "Waiting for crawl to start...",
          max_depth: 0,
          max_pages: 0,
          rate_limit: 0,
          gsc_refresh_token: null,
          pages: [],
        };
      }

      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/seo/crawls/${crawlId}/`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
            credentials: "include",
          }
        );

        if (response.status === 404) {
          console.debug(`Crawl not found: ${crawlId}`);
          return {
            id: crawlId,
            url: "",
            start_time: new Date().toISOString(),
            end_time: "",
            status: "failed",
            error_message: "Crawl not found",
            max_depth: 0,
            max_pages: 0,
            rate_limit: 0,
            gsc_refresh_token: null,
            pages: [],
          };
        }

        if (!response.ok) {
          console.debug("Failed to fetch crawl results:", response.statusText);
          return {
            id: crawlId,
            url: "",
            start_time: new Date().toISOString(),
            end_time: "",
            status: "failed",
            error_message: `HTTP error: ${response.status}`,
            max_depth: 0,
            max_pages: 0,
            rate_limit: 0,
            gsc_refresh_token: null,
            pages: [],
          };
        }

        const data = await response.json();

        // Handle the data structure correctly - the API returns a wrapper with json array
        const crawlData = data.json?.[0] || data;

        return crawlData;
      } catch (error) {
        console.debug("Error fetching crawl results:", error);
        return {
          id: crawlId,
          url: "",
          start_time: new Date().toISOString(),
          end_time: "",
          status: "failed",
          error_message:
            error instanceof Error ? error.message : "Unknown error",
          max_depth: 0,
          max_pages: 0,
          rate_limit: 0,
          gsc_refresh_token: null,
          pages: [],
        };
      }
    },
    {
      enabled: !!crawlId,
      staleTime: 30000,
      retry: false, // Don't retry on error
      refetchOnWindowFocus: false, // Don't refetch when window gains focus
      onError: () => {
        // Suppress error reporting to the console
      },
    }
  );
};

export const useGetCrawlMetrics = (crawlId?: number) => {
  return useQuery<MetricsResponse>(
    ["crawlMetrics", crawlId],
    async () => {
      if (!crawlId) return null;

      // Skip API call for temporary crawl IDs
      if (crawlId === -1) {
        return {
          domain_rating: {
            score: 0,
            change: 0,
          },
          organic_traffic: {
            score: 0,
            change: 0,
          },
          search_terms: {
            total: 0,
            new: 0,
          },
          links: {
            internal: 0,
            external: 0,
            unique_domains: 0,
          },
          content_score: {
            score: 0,
            change: 0,
          },
        };
      }

      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/seo/crawls/${crawlId}/calculate_metrics/`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
            credentials: "include",
          }
        );

        if (!response.ok) {
          console.debug(
            `Failed to fetch metrics: ${response.status} ${response.statusText}`
          );
          return {
            domain_rating: {
              score: 0,
              change: 0,
            },
            organic_traffic: {
              score: 0,
              change: 0,
            },
            search_terms: {
              total: 0,
              new: 0,
            },
            links: {
              internal: 0,
              external: 0,
              unique_domains: 0,
            },
            content_score: {
              score: 0,
              change: 0,
            },
          };
        }

        const data = await response.json();
        // Handle the nested json structure if present
        return data.json || data;
      } catch (error) {
        console.debug("Error fetching crawl metrics:", error);
        return {
          domain_rating: {
            score: 0,
            change: 0,
          },
          organic_traffic: {
            score: 0,
            change: 0,
          },
          search_terms: {
            total: 0,
            new: 0,
          },
          links: {
            internal: 0,
            external: 0,
            unique_domains: 0,
          },
          content_score: {
            score: 0,
            change: 0,
          },
        };
      }
    },
    {
      enabled: !!crawlId && crawlId !== -1, // Disable the query when crawlId is -1
      staleTime: 30000,
      retry: false, // Don't retry on error
      refetchOnWindowFocus: false, // Don't refetch when window gains focus
      onError: () => {
        // Suppress error reporting to the console
      },
    }
  );
};

export const useGetTechnicalMetrics = (crawlId?: number) => {
  return useQuery<TechnicalMetricsResponse>(
    ["technicalMetrics", crawlId],
    async () => {
      if (!crawlId) return null;

      // Skip API call for temporary crawl IDs
      if (crawlId === -1) {
        return { metrics: [] };
      }

      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/seo/crawls/${crawlId}/technical_metrics/`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
            credentials: "include",
          }
        );

        if (!response.ok) {
          console.debug(
            "Failed to fetch technical metrics:",
            response.statusText
          );
          return { metrics: [] };
        }

        const responseData = await response.json();
        // Handle the nested json structure if present
        const data = responseData.json || responseData;

        // Validate the response structure
        if (!data || !data.metrics || !Array.isArray(data.metrics)) {
          console.debug("Invalid technical metrics data:", data);
          return { metrics: [] };
        }

        // Ensure all metrics have proper status values
        const processedMetrics = data.metrics.map((metric: any) => {
          if (!metric.status) {
            // Determine status based on value compared to regional average
            if (metric.value >= metric.regionalAverage + 10) {
              metric.status = "success";
            } else if (metric.value >= metric.regionalAverage - 10) {
              metric.status = "warning";
            } else {
              metric.status = "error";
            }
          }
          return metric;
        });

        return { ...data, metrics: processedMetrics };
      } catch (error) {
        console.debug("Error fetching technical metrics:", error);
        return { metrics: [] };
      }
    },
    {
      enabled: !!crawlId && crawlId !== -1, // Disable the query when crawlId is -1
      staleTime: 30000,
      retry: false, // Don't retry on error
      refetchOnWindowFocus: false, // Don't refetch when window gains focus
      onError: () => {
        // Suppress error reporting to the console
      },
    }
  );
};

export const useGetRegionalTrends = (crawlId?: number) => {
  return useQuery<RegionalTrendsResponse>(
    ["regionalTrends", crawlId],
    async () => {
      if (!crawlId) return null;

      // Skip API call for temporary crawl IDs
      if (crawlId === -1) {
        return {
          labels: [],
          datasets: {
            organic_keywords: [],
            competitor_keywords: [],
            avg_position: [],
            visibility: [],
          },
        };
      }

      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/seo/crawls/${crawlId}/regional_trends/`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
            credentials: "include",
          }
        );

        if (!response.ok) {
          console.debug(
            "Failed to fetch regional trends:",
            response.statusText
          );
          // Return default data structure instead of null
          return {
            labels: [],
            datasets: {
              organic_keywords: [],
              competitor_keywords: [],
              avg_position: [],
              visibility: [],
            },
          };
        }

        const responseData = await response.json();
        // Handle the nested json structure if present
        const data = responseData.json || responseData;

        // Validate the response structure
        if (!data || !data.labels || !data.datasets) {
          console.debug("Invalid regional trends data:", data);
          return {
            labels: [],
            datasets: {
              organic_keywords: [],
              competitor_keywords: [],
              avg_position: [],
              visibility: [],
            },
          };
        }

        return data;
      } catch (error) {
        console.debug("Error fetching regional trends:", error);
        return {
          labels: [],
          datasets: {
            organic_keywords: [],
            competitor_keywords: [],
            avg_position: [],
            visibility: [],
          },
        };
      }
    },
    {
      enabled: !!crawlId && crawlId !== -1, // Disable the query when crawlId is -1
      staleTime: 300000, // Consider data fresh for 5 minutes
      retry: false, // Don't retry on error
      refetchOnWindowFocus: false, // Don't refetch when window gains focus
      onError: () => {
        // Suppress error reporting to the console
      },
    }
  );
};

export const useGetOptimizationChecklist = (crawlId?: number) => {
  return useQuery<SeoOptimizationChecklist>(
    ["optimizationChecklist", crawlId],
    async () => {
      if (!crawlId) return null;

      // Skip API call for temporary crawl IDs
      if (crawlId === -1) {
        return {
          progress: 0,
          sections: [],
        };
      }

      try {
        const response = await axios
          .get(
            `${process.env.NEXT_PUBLIC_API_BASE_URL}/seo/crawls/${crawlId}/optimization_checklist/`,
            {
              withCredentials: true,
              // Add timeout to prevent long-running requests
              timeout: 10000,
            }
          )
          .catch((error) => {
            // Handle axios errors
            console.debug(
              "Optimization checklist request failed:",
              error.message
            );
            return {
              data: {
                progress: 0,
                sections: [],
              },
            };
          });

        // Validate the response structure
        if (!response.data) {
          console.debug("Empty optimization checklist data");
          return {
            progress: 0,
            sections: [],
          };
        }

        // Handle the nested json structure if present
        const data = response.data.json || response.data;

        if (!data.sections || !Array.isArray(data.sections)) {
          console.debug("Invalid optimization checklist data structure:", data);
          return {
            progress: data.progress || 0,
            sections: [],
          };
        }

        return data;
      } catch (error) {
        console.debug("Failed to fetch optimization checklist:", error);
        return {
          progress: 0,
          sections: [],
        };
      }
    },
    {
      enabled: !!crawlId && crawlId !== -1, // Disable the query when crawlId is -1
      staleTime: 5 * 60 * 1000, // Cache for 5 minutes
      retry: false, // Don't retry on error
      refetchOnWindowFocus: false, // Don't refetch when window gains focus
      onError: () => {
        // Suppress error reporting to the console
      },
    }
  );
};

export const useGetAllCrawls = () => {
  return useQuery<CrawlResponse[]>(
    ["allCrawls"],
    async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/seo/crawls/`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
            credentials: "include",
          }
        );

        if (!response.ok) {
          console.error("Failed to fetch all crawls:", response.statusText);
          return [];
        }

        const data = await response.json();

        // Check if the data is wrapped in a json array
        const crawlsData = data.json || data;

        // Validate the response structure
        if (!crawlsData || !Array.isArray(crawlsData)) {
          console.error("Invalid crawls data:", data);
          return [];
        }

        // Sort crawls by date (newest first)
        return crawlsData.sort(
          (a, b) =>
            new Date(b.start_time).getTime() - new Date(a.start_time).getTime()
        );
      } catch (error) {
        console.error("Error fetching all crawls:", error);
        return [];
      }
    },
    {
      staleTime: 60000, // Consider data fresh for 1 minute
      retry: 1,
    }
  );
};

export const useGetGscData = (crawlId?: number) => {
  return useQuery<GscDataResponse>(
    ["gscData", crawlId],
    async () => {
      if (!crawlId) return null;

      // Skip API call for temporary crawl IDs
      if (crawlId === -1) {
        return {
          error: "Waiting for crawl to start...",
        };
      }

      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/seo/crawls/${crawlId}/gsc_data/`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
            credentials: "include",
          }
        );

        if (!response.ok) {
          console.debug("Failed to fetch GSC data:", response.statusText);
          return {
            error: `Failed to fetch GSC data: ${response.status} ${response.statusText}`,
          };
        }

        const data = await response.json();

        // Validate the response structure
        if (!data || typeof data !== "object") {
          console.debug("Invalid GSC data:", data);
          return {
            error: "Invalid response from server",
          };
        }

        return data;
      } catch (error) {
        console.debug("Error fetching GSC data:", error);
        return {
          error: error instanceof Error ? error.message : "Unknown error",
        };
      }
    },
    {
      enabled: !!crawlId && crawlId !== -1, // Disable the query when crawlId is -1
      staleTime: 300000, // Consider data fresh for 5 minutes
      retry: false, // Don't retry on error
      refetchOnWindowFocus: false, // Don't refetch when window gains focus
      onError: () => {
        // Suppress error reporting to the console
      },
    }
  );
};

/**
 * Hook to track the status of a crawl in real-time
 */
export const useTrackCrawlStatus = (
  crawlId?: number,
  options?: {
    enabled?: boolean;
    pollingInterval?: number;
    onComplete?: (data: CrawlResponse) => void;
  }
) => {
  const { enabled = true, pollingInterval = 1000, onComplete } = options || {};

  return useQuery<CrawlResponse, Error>(
    ["crawlStatus", crawlId],
    async () => {
      // Handle temporary crawl ID (-1) or no crawl ID
      if (!crawlId || crawlId === -1) {
        // Return a default response instead of throwing an error
        return {
          id: crawlId || 0,
          url: "",
          start_time: new Date().toISOString(),
          end_time: "",
          status: "pending",
          error_message:
            crawlId === -1
              ? "Waiting for crawl to start..."
              : "No crawl ID provided",
          max_depth: 0,
          max_pages: 0,
          rate_limit: 0,
          gsc_refresh_token: null,
          pages: [],
        };
      }

      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/seo/crawls/${crawlId}/`,
          {
            credentials: "include",
          }
        );

        if (response.status === 404) {
          // Return a default response instead of throwing an error
          // This prevents constant retries for non-existent crawls
          return {
            id: crawlId,
            url: "",
            start_time: new Date().toISOString(),
            end_time: "",
            status: "pending",
            error_message: "Crawl not found",
            max_depth: 0,
            max_pages: 0,
            rate_limit: 0,
            gsc_refresh_token: null,
            pages: [],
          };
        }

        if (!response.ok) {
          console.debug(`Failed to fetch crawl status: ${response.status}`);
          // Return a default response instead of throwing
          return {
            id: crawlId,
            url: "",
            start_time: new Date().toISOString(),
            end_time: "",
            status: "pending",
            error_message: `HTTP error: ${response.status}`,
            max_depth: 0,
            max_pages: 0,
            rate_limit: 0,
            gsc_refresh_token: null,
            pages: [],
          };
        }

        const data = await response.json();

        // Handle the nested json structure if present
        return data.json?.[0] || data;
      } catch (error) {
        console.debug("Error fetching crawl status:", error);
        // Return a default response instead of throwing
        return {
          id: crawlId || 0,
          url: "",
          start_time: new Date().toISOString(),
          end_time: "",
          status: "pending",
          error_message:
            error instanceof Error ? error.message : "Unknown error",
          max_depth: 0,
          max_pages: 0,
          rate_limit: 0,
          gsc_refresh_token: null,
          pages: [],
        };
      }
    },
    {
      enabled: enabled && crawlId !== -1, // Disable the query when crawlId is -1
      refetchInterval: (data) => {
        // Stop polling when crawl is complete or failed
        if (data?.status === "completed" || data?.status === "failed") {
          if (onComplete && data?.status === "completed") {
            onComplete(data);
          }
          return false;
        }
        return pollingInterval;
      },
      refetchIntervalInBackground: true,
      staleTime: 0,
      retry: false, // Don't retry on error
      onError: () => {
        // Suppress error reporting to the console
      },
    }
  );
};
