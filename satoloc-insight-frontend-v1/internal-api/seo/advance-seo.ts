import { useMutation, useQuery } from "@tanstack/react-query";
import apiClient from "@/lib/apiClient";

/**
 * Upload competitors CSV for a website
 */
export const uploadCompetitorsCsv = async ({
  websiteId,
  file,
}: {
  websiteId: string;
  file: File;
}) => {
  const formData = new FormData();
  formData.append("file", file);
  const response = await apiClient.post(
    `/advance-seo/websites/${websiteId}/upload_competitors_csv/`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
  return response.data;
};

export const useUploadCompetitorsCsv = (options = {}) => {
  return useMutation({
    mutationFn: uploadCompetitorsCsv,
    ...options,
  });
};

/**
 * Upload keywords CSV for a specific competitor
 */
export const uploadCompetitorKeywordsCsv = async ({
  websiteId,
  competitorId,
  file,
}: {
  websiteId: string;
  competitorId: number;
  file: File;
}) => {
  const formData = new FormData();
  formData.append("file", file);
  const response = await apiClient.post(
    `/advance-seo/websites/${websiteId}/competitors/${competitorId}/upload_keywords_csv/`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
  return response.data;
};

export const useUploadCompetitorKeywordsCsv = (options = {}) => {
  return useMutation({
    mutationFn: uploadCompetitorKeywordsCsv,
    ...options,
  });
};

/**
 * Fetch SEO data for a website by URL
 */
const fetchWebsiteByUrl = async (url: string) => {
  // Validate URL is not empty
  if (!url) {
    console.error("URL is undefined or null");
    throw new Error("URL cannot be empty");
  }

  if (typeof url !== "string") {
    console.error("URL is not a string:", url);
    throw new Error("URL must be a string");
  }

  if (url.trim() === "") {
    console.error("URL is empty after trimming");
    throw new Error("URL cannot be empty");
  }

  try {
    const response = await apiClient.get(
      `/advance-seo/websites/by_url/?url=${encodeURIComponent(url.trim())}`
    );
    return response.data;
  } catch (error) {
    console.error("Error in fetchWebsiteByUrl:", error);
    throw error;
  }
};

/**
 * Fetch user's website analyses
 */
const fetchMyAnalyses = async () => {
  try {
    const response = await apiClient.get("/advance-seo/websites/my_analyses/");
    return response.data;
  } catch (error) {
    console.error("Error fetching user's analyses:", error);
    throw error;
  }
};

/**
 * Fetch all websites (admin only)
 */
const fetchAllWebsites = async () => {
  try {
    const response = await apiClient.get("/advance-seo/websites/");
    return response.data;
  } catch (error) {
    console.error("Error fetching all websites:", error);
    throw error;
  }
};

/**
 * Process a website to fetch SEO data
 */
const processWebsite = async ({
  url,
  industry,
  language,
}: {
  url: string;
  industry: string;
  language: string;
}) => {
  // Validate URL is not empty
  if (!url || url.trim() === "") {
    console.error("URL is empty in processWebsite");
    throw new Error("URL cannot be empty");
  }

  // Normalize language to ISO code if needed
  let normalizedLanguage = language;
  if (language) {
    // Map full language names to ISO codes
    const languageMapping: Record<string, string> = {
      English: "en",
      Turkish: "tr",
      // Add more mappings as needed
    };

    // If it's a full language name, convert to ISO code
    if (language in languageMapping) {
      normalizedLanguage = languageMapping[language];
    }
  }

  try {
    const response = await apiClient.post("/advance-seo/websites/process/", {
      url,
      industry,
      language: normalizedLanguage,
    });
    return response.data;
  } catch (error) {
    console.error("Error in processWebsite:", error);
    throw error;
  }
};

/**
 * Fetch website data by ID
 */
const fetchWebsiteById = async (id: string) => {
  try {
    const response = await apiClient.get(`/advance-seo/websites/${id}/`);
    return response.data;
  } catch (error) {
    console.error("Error in fetchWebsiteById:", error);
    throw error;
  }
};

/**
 * React Query hook to fetch website data by URL
 */
export const useWebsiteByUrl = (url: string, options = {}) => {
  return useQuery({
    queryKey: ["website", url],
    queryFn: () => fetchWebsiteByUrl(url),
    enabled: !!url && url.trim() !== "" && (options as any).enabled !== false,
    ...options,
  });
};

/**
 * React Query hook to fetch user's website analyses
 */
export const useMyAnalyses = (options = {}) => {
  return useQuery({
    queryKey: ["my_analyses"],
    queryFn: fetchMyAnalyses,
    ...options,
  });
};

/**
 * React Query hook to fetch all websites (admin only)
 */
export const useAllWebsites = (options = {}) => {
  return useQuery({
    queryKey: ["all_websites"],
    queryFn: fetchAllWebsites,
    ...options,
  });
};

/**
 * React Query hook to process a website and fetch SEO data
 */
export const useProcessWebsite = (options = {}) => {
  return useMutation({
    mutationFn: processWebsite,
    ...options,
  });
};

/**
 * React Query hook to fetch website data by ID
 */
export const useWebsiteById = (id: string, options = {}) => {
  return useQuery({
    queryKey: ["website_by_id", id],
    queryFn: () => fetchWebsiteById(id),
    enabled: !!id, // Only enable if ID is provided
    ...options,
  });
};

/**
 * Update website basic info
 */
const updateWebsiteBasicInfo = async ({
  id,
  data,
}: {
  id: string;
  data: { url?: string; industry?: string; language?: string };
}) => {
  try {
    const response = await apiClient.patch(
      `/advance-seo/websites/${id}/`,
      data
    );
    return response.data;
  } catch (error) {
    console.error("Error updating website basic info:", error);
    throw error;
  }
};

/**
 * Add a language to a website
 */
const addWebsiteLanguage = async ({
  websiteId,
  language,
}: {
  websiteId: string;
  language: string;
}) => {
  try {
    const response = await apiClient.post(
      `/advance-seo/websites/${websiteId}/add_language/`,
      {
        language,
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error adding website language:", error);
    throw error;
  }
};

/**
 * Remove a language from a website
 */
const removeWebsiteLanguage = async ({
  websiteId,
  language,
}: {
  websiteId: string;
  language: string;
}) => {
  try {
    const response = await apiClient.post(
      `/advance-seo/websites/${websiteId}/remove_language/`,
      {
        language,
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error removing website language:", error);
    throw error;
  }
};

/**
 * Add a keyword to a website
 */
const addWebsiteKeyword = async ({
  websiteId,
  keyword,
  isTarget,
}: {
  websiteId: string;
  keyword: string;
  isTarget: boolean;
}) => {
  try {
    const response = await apiClient.post(
      `/advance-seo/websites/${websiteId}/add_keyword/`,
      {
        keyword,
        is_target: isTarget,
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error adding website keyword:", error);
    throw error;
  }
};

/**
 * Remove a keyword from a website
 */
const removeWebsiteKeyword = async ({
  websiteId,
  keyword,
}: {
  websiteId: string;
  keyword: string;
}) => {
  try {
    const response = await apiClient.post(
      `/advance-seo/websites/${websiteId}/remove_keyword/`,
      {
        keyword,
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error removing website keyword:", error);
    throw error;
  }
};

/**
 * Update a keyword's target status
 */
const updateWebsiteKeyword = async ({
  websiteId,
  keyword,
  isTarget,
}: {
  websiteId: string;
  keyword: string;
  isTarget: boolean;
}) => {
  try {
    const response = await apiClient.post(
      `/advance-seo/websites/${websiteId}/update_keyword/`,
      {
        keyword,
        is_target: isTarget,
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error updating website keyword:", error);
    throw error;
  }
};

/**
 * Add a competitor to a website
 */
const addWebsiteCompetitor = async ({
  websiteId,
  name,
  targetUrl,
  description,
  rank,
}: {
  websiteId: string;
  name: string;
  targetUrl: string;
  description?: string;
  rank?: number;
}) => {
  try {
    const response = await apiClient.post(
      `/advance-seo/websites/${websiteId}/add_competitor/`,
      {
        name,
        target_url: targetUrl,
        description,
        rank,
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error adding website competitor:", error);
    throw error;
  }
};

/**
 * Remove a competitor from a website
 */
const removeWebsiteCompetitor = async ({
  websiteId,
  competitorId,
}: {
  websiteId: string;
  competitorId: number;
}) => {
  try {
    const response = await apiClient.post(
      `/advance-seo/websites/${websiteId}/remove_competitor/`,
      {
        competitor_id: competitorId,
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error removing website competitor:", error);
    throw error;
  }
};

/**
 * Update a competitor for a website
 */
const updateWebsiteCompetitor = async ({
  websiteId,
  competitorId,
  originalTargetUrl,
  name,
  targetUrl,
  description,
  rank,
}: {
  websiteId: string;
  competitorId?: number;
  originalTargetUrl?: string;
  name?: string;
  targetUrl?: string;
  description?: string;
  rank?: number;
}) => {
  try {
    // Build the request data with all available fields
    const requestData: any = {
      name,
      target_url: targetUrl,
      description,
      rank,
    };

    // Add identifier fields - either competitor_id or original_target_url
    if (competitorId) {
      requestData.competitor_id = competitorId;
    }

    if (originalTargetUrl) {
      requestData.original_target_url = originalTargetUrl;
    }

    const response = await apiClient.post(
      `/advance-seo/websites/${websiteId}/update_competitor/`,
      requestData
    );
    return response.data;
  } catch (error) {
    console.error("Error updating website competitor:", error);
    throw error;
  }
};

/**
 * React Query hook to update website basic info
 */
export const useUpdateWebsiteBasicInfo = (options = {}) => {
  return useMutation({
    mutationFn: updateWebsiteBasicInfo,
    ...options,
  });
};

/**
 * React Query hook to add a language to a website
 */
export const useAddWebsiteLanguage = (options = {}) => {
  return useMutation({
    mutationFn: addWebsiteLanguage,
    ...options,
  });
};

/**
 * React Query hook to remove a language from a website
 */
export const useRemoveWebsiteLanguage = (options = {}) => {
  return useMutation({
    mutationFn: removeWebsiteLanguage,
    ...options,
  });
};

/**
 * React Query hook to add a keyword to a website
 */
export const useAddWebsiteKeyword = (options = {}) => {
  return useMutation({
    mutationFn: addWebsiteKeyword,
    ...options,
  });
};

/**
 * React Query hook to remove a keyword from a website
 */
export const useRemoveWebsiteKeyword = (options = {}) => {
  return useMutation({
    mutationFn: removeWebsiteKeyword,
    ...options,
  });
};

/**
 * React Query hook to update a keyword's target status
 */
export const useUpdateWebsiteKeyword = (options = {}) => {
  return useMutation({
    mutationFn: updateWebsiteKeyword,
    ...options,
  });
};

/**
 * React Query hook to add a competitor to a website
 */
export const useAddWebsiteCompetitor = (options = {}) => {
  return useMutation({
    mutationFn: addWebsiteCompetitor,
    ...options,
  });
};

/**
 * React Query hook to remove a competitor from a website
 */
export const useRemoveWebsiteCompetitor = (options = {}) => {
  return useMutation({
    mutationFn: removeWebsiteCompetitor,
    ...options,
  });
};

/**
 * React Query hook to update a competitor for a website
 */
export const useUpdateWebsiteCompetitor = (options = {}) => {
  return useMutation({
    mutationFn: updateWebsiteCompetitor,
    ...options,
  });
};

// Add a keyword to a competitor
const addCompetitorKeyword = async ({
  websiteId,
  competitorId,
  keyword,
}: {
  websiteId: string;
  competitorId: number;
  keyword: string;
}) => {
  try {
    const response = await apiClient.post(
      `/advance-seo/websites/${websiteId}/competitors/${competitorId}/add_keyword/`,
      {
        keyword,
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error adding competitor keyword:", error);
    throw error;
  }
};

// Remove a keyword from a competitor
const removeCompetitorKeyword = async ({
  websiteId,
  competitorId,
  keyword,
}: {
  websiteId: string;
  competitorId: number;
  keyword: string;
}) => {
  try {
    const response = await apiClient.post(
      `/advance-seo/websites/${websiteId}/competitors/${competitorId}/remove_keyword/`,
      {
        keyword,
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error removing competitor keyword:", error);
    throw error;
  }
};

// Update a competitor's keywords list entirely
const updateCompetitorKeywords = async ({
  websiteId,
  competitorId,
  keywords,
}: {
  websiteId: string;
  competitorId: number;
  keywords: string[];
}) => {
  try {
    const response = await apiClient.post(
      `/advance-seo/websites/${websiteId}/competitors/${competitorId}/update_keywords/`,
      {
        keywords,
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error updating competitor keywords:", error);
    throw error;
  }
};

/**
 * React Query hook to add a keyword to a competitor
 */
export const useAddCompetitorKeyword = (options = {}) => {
  return useMutation({
    mutationFn: addCompetitorKeyword,
    ...options,
  });
};

/**
 * React Query hook to remove a keyword from a competitor
 */
export const useRemoveCompetitorKeyword = (options = {}) => {
  return useMutation({
    mutationFn: removeCompetitorKeyword,
    ...options,
  });
};

/**
 * React Query hook to update all keywords for a competitor
 */
export const useUpdateCompetitorKeywords = (options = {}) => {
  return useMutation({
    mutationFn: updateCompetitorKeywords,
    ...options,
  });
};

/**
 * Add a strategy gap to a competitor
 */
const addCompetitorStrategyGap = async ({
  websiteId,
  competitorId,
  text,
}: {
  websiteId: string;
  competitorId: number;
  text: string;
}) => {
  try {
    const response = await apiClient.post(
      `/advance-seo/websites/${websiteId}/competitors/${competitorId}/add_strategy_gap/`,
      { text }
    );
    return response.data;
  } catch (error) {
    console.error("Error adding competitor strategy gap:", error);
    throw error;
  }
};

/**
 * Remove a strategy gap from a competitor
 */
const removeCompetitorStrategyGap = async ({
  websiteId,
  competitorId,
  gapId,
}: {
  websiteId: string;
  competitorId: number;
  gapId: number;
}) => {
  try {
    const url = `/advance-seo/websites/${websiteId}/competitors/${competitorId}/remove_strategy_gap/`;

    const response = await apiClient.post(url, { gap_id: gapId });
    return response.data;
  } catch (error) {
    console.error("Error removing competitor strategy gap:", error);
    console.error(
      "Request URL:",
      `/advance-seo/websites/${websiteId}/competitors/${competitorId}/remove_strategy_gap/`
    );
    console.error("Request payload:", { gap_id: gapId });
    throw error;
  }
};

/**
 * Update a strategy gap for a competitor
 */
const updateCompetitorStrategyGap = async ({
  websiteId,
  competitorId,
  gapId,
  text,
}: {
  websiteId: string;
  competitorId: number;
  gapId: number;
  text: string;
}) => {
  try {
    const response = await apiClient.patch(
      `/advance-seo/websites/${websiteId}/competitors/${competitorId}/strategy_gaps/${gapId}/`,
      { text }
    );
    return response.data;
  } catch (error) {
    console.error("Error updating competitor strategy gap:", error);
    throw error;
  }
};

/**
 * Add a growth opportunity to a competitor
 */
const addCompetitorGrowthOpportunity = async ({
  websiteId,
  competitorId,
  text,
}: {
  websiteId: string;
  competitorId: number;
  text: string;
}) => {
  try {
    const response = await apiClient.post(
      `/advance-seo/websites/${websiteId}/competitors/${competitorId}/add_growth_opportunity/`,
      { text }
    );
    return response.data;
  } catch (error) {
    console.error("Error adding competitor growth opportunity:", error);
    throw error;
  }
};

/**
 * Remove a growth opportunity from a competitor
 */
const removeCompetitorGrowthOpportunity = async ({
  websiteId,
  competitorId,
  opportunityId,
}: {
  websiteId: string;
  competitorId: number;
  opportunityId: number;
}) => {
  try {
    const url = `/advance-seo/websites/${websiteId}/competitors/${competitorId}/remove_growth_opportunity/`;

    const response = await apiClient.post(url, {
      opportunity_id: opportunityId,
    });
    return response.data;
  } catch (error) {
    console.error("Error removing competitor growth opportunity:", error);
    console.error(
      "Request URL:",
      `/advance-seo/websites/${websiteId}/competitors/${competitorId}/remove_growth_opportunity/`
    );
    console.error("Request payload:", { opportunity_id: opportunityId });
    throw error;
  }
};

/**
 * Update a growth opportunity for a competitor
 */
const updateCompetitorGrowthOpportunity = async ({
  websiteId,
  competitorId,
  opportunityId,
  text,
}: {
  websiteId: string;
  competitorId: number;
  opportunityId: number;
  text: string;
}) => {
  try {
    const response = await apiClient.patch(
      `/advance-seo/websites/${websiteId}/competitors/${competitorId}/growth_opportunities/${opportunityId}/`,
      { text }
    );
    return response.data;
  } catch (error) {
    console.error("Error updating competitor growth opportunity:", error);
    throw error;
  }
};

// React Query hooks for strategy gaps
export const useAddCompetitorStrategyGap = (options = {}) => {
  return useMutation({
    mutationFn: addCompetitorStrategyGap,
    ...options,
  });
};

export const useRemoveCompetitorStrategyGap = (options = {}) => {
  return useMutation({
    mutationFn: removeCompetitorStrategyGap,
    ...options,
  });
};

export const useUpdateCompetitorStrategyGap = (options = {}) => {
  return useMutation({
    mutationFn: updateCompetitorStrategyGap,
    ...options,
  });
};

// React Query hooks for growth opportunities
export const useAddCompetitorGrowthOpportunity = (options = {}) => {
  return useMutation({
    mutationFn: addCompetitorGrowthOpportunity,
    ...options,
  });
};

export const useRemoveCompetitorGrowthOpportunity = (options = {}) => {
  return useMutation({
    mutationFn: removeCompetitorGrowthOpportunity,
    ...options,
  });
};

export const useUpdateCompetitorGrowthOpportunity = (options = {}) => {
  return useMutation({
    mutationFn: updateCompetitorGrowthOpportunity,
    ...options,
  });
};

/**
 * Update SEO metrics for a website
 */
const updateSEOMetrics = async ({
  websiteId,
  data,
}: {
  websiteId: string;
  data: any;
}) => {
  try {
    const response = await apiClient.patch(
      `/advance-seo/websites/${websiteId}/update_seo_metrics/`,
      data
    );
    return response.data;
  } catch (error) {
    console.error("Error updating SEO metrics:", error);
    throw error;
  }
};

/**
 * React Query hook to update SEO metrics
 */
export const useUpdateSEOMetrics = (options = {}) => {
  return useMutation({
    mutationFn: updateSEOMetrics,
    ...options,
  });
};

/**
 * Add a strategy gap to a website
 */
const addWebsiteStrategyGap = async ({
  websiteId,
  text,
}: {
  websiteId: string;
  text: string;
}) => {
  try {
    const response = await apiClient.post(
      `/advance-seo/websites/${websiteId}/add_strategy_gap/`,
      { text }
    );
    return response.data;
  } catch (error) {
    console.error("Error adding website strategy gap:", error);
    throw error;
  }
};

/**
 * Remove a strategy gap from a website
 */
const removeWebsiteStrategyGap = async ({
  websiteId,
  gapId,
}: {
  websiteId: string;
  gapId: number;
}) => {
  try {
    const url = `/advance-seo/websites/${websiteId}/remove_strategy_gap/`;

    const response = await apiClient.post(url, { gap_id: gapId });
    return response.data;
  } catch (error) {
    console.error("Error removing website strategy gap:", error);
    console.error(
      "Request URL:",
      `/advance-seo/websites/${websiteId}/remove_strategy_gap/`
    );
    console.error("Request payload:", { gap_id: gapId });
    throw error;
  }
};

/**
 * Add a growth opportunity to a website
 */
const addWebsiteGrowthOpportunity = async ({
  websiteId,
  text,
}: {
  websiteId: string;
  text: string;
}) => {
  try {
    const response = await apiClient.post(
      `/advance-seo/websites/${websiteId}/add_growth_opportunity/`,
      { text }
    );
    return response.data;
  } catch (error) {
    console.error("Error adding website growth opportunity:", error);
    throw error;
  }
};

/**
 * Remove a growth opportunity from a website
 */
const removeWebsiteGrowthOpportunity = async ({
  websiteId,
  opportunityId,
}: {
  websiteId: string;
  opportunityId: number;
}) => {
  try {
    const url = `/advance-seo/websites/${websiteId}/remove_growth_opportunity/`;

    const response = await apiClient.post(url, {
      opportunity_id: opportunityId,
    });
    return response.data;
  } catch (error) {
    console.error("Error removing website growth opportunity:", error);
    console.error(
      "Request URL:",
      `/advance-seo/websites/${websiteId}/remove_growth_opportunity/`
    );
    console.error("Request payload:", { opportunity_id: opportunityId });
    throw error;
  }
};

// React Query hooks for website strategy gaps
export const useAddWebsiteStrategyGap = (options = {}) => {
  return useMutation({
    mutationFn: addWebsiteStrategyGap,
    ...options,
  });
};

export const useRemoveWebsiteStrategyGap = (options = {}) => {
  return useMutation({
    mutationFn: removeWebsiteStrategyGap,
    ...options,
  });
};

// React Query hooks for website growth opportunities
export const useAddWebsiteGrowthOpportunity = (options = {}) => {
  return useMutation({
    mutationFn: addWebsiteGrowthOpportunity,
    ...options,
  });
};

export const useRemoveWebsiteGrowthOpportunity = (options = {}) => {
  return useMutation({
    mutationFn: removeWebsiteGrowthOpportunity,
    ...options,
  });
};

/**
 * Add a ranking issue to a competitor
 */
const addRankingIssue = async ({
  websiteId,
  competitorId,
  title,
  description,
  impact = "medium",
}: {
  websiteId: string;
  competitorId: number;
  title: string;
  description: string;
  impact?: "high" | "medium" | "low";
}) => {
  try {
    const response = await apiClient.post(
      `/advance-seo/websites/${websiteId}/add_ranking_issue/`,
      { competitor_id: competitorId, title, description, impact }
    );
    return response.data;
  } catch (error) {
    console.error("Error adding ranking issue:", error);
    throw error;
  }
};

/**
 * Remove a ranking issue
 */
const removeRankingIssue = async ({
  websiteId,
  rankingIssueId,
}: {
  websiteId: string;
  rankingIssueId: number;
}) => {
  try {
    const response = await apiClient.post(
      `/advance-seo/websites/${websiteId}/remove_ranking_issue/`,
      { ranking_issue_id: rankingIssueId }
    );
    return response.data;
  } catch (error) {
    console.error("Error removing ranking issue:", error);
    throw error;
  }
};

/**
 * Add a content recommendation to a competitor
 */
const addContentRecommendation = async ({
  websiteId,
  competitorId,
  title,
  description,
  impact = "medium",
  estimatedHours = 2,
  isOpportunity = false,
}: {
  websiteId: string;
  competitorId: number;
  title: string;
  description: string;
  impact?: "high" | "medium" | "low";
  estimatedHours?: number;
  isOpportunity?: boolean;
}) => {
  try {
    const response = await apiClient.post(
      `/advance-seo/websites/${websiteId}/add_content_recommendation/`,
      {
        competitor_id: competitorId,
        title,
        description,
        impact,
        estimated_hours: estimatedHours,
        is_opportunity: isOpportunity,
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error adding content recommendation:", error);
    throw error;
  }
};

/**
 * Remove a content recommendation
 */
const removeContentRecommendation = async ({
  websiteId,
  recommendationId,
}: {
  websiteId: string;
  recommendationId: number;
}) => {
  try {
    const response = await apiClient.post(
      `/advance-seo/websites/${websiteId}/remove_content_recommendation/`,
      { recommendation_id: recommendationId }
    );
    return response.data;
  } catch (error) {
    console.error("Error removing content recommendation:", error);
    throw error;
  }
};

// React Query hooks for ranking issues
export const useAddRankingIssue = (options = {}) => {
  return useMutation({
    mutationFn: addRankingIssue,
    ...options,
  });
};

export const useRemoveRankingIssue = (options = {}) => {
  return useMutation({
    mutationFn: removeRankingIssue,
    ...options,
  });
};

// React Query hooks for content recommendations
export const useAddContentRecommendation = (options = {}) => {
  return useMutation({
    mutationFn: addContentRecommendation,
    ...options,
  });
};

export const useRemoveContentRecommendation = (options = {}) => {
  return useMutation({
    mutationFn: removeContentRecommendation,
    ...options,
  });
};

/**
 * Core Web Vitals API Functions
 */

/**
 * Analyze Core Web Vitals for a website URL (single strategy)
 */
const analyzeCoreWebVitals = async ({
  url,
  strategy = "mobile",
}: {
  url: string;
  strategy?: "mobile" | "desktop";
}) => {
  try {
    const response = await apiClient.post("/advance-seo/core-web-vitals/", {
      site_url: url,
      strategy,
    });
    return response.data;
  } catch (error) {
    console.error("Error analyzing Core Web Vitals:", error);
    throw error;
  }
};

/**
 * Analyze Core Web Vitals for both mobile and desktop
 */
const analyzeCoreWebVitalsMultiStrategy = async ({ url }: { url: string }) => {
  try {
    const response = await apiClient.post(
      "/advance-seo/core-web-vitals-multi/",
      {
        site_url: url,
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error analyzing Core Web Vitals (multi-strategy):", error);
    throw error;
  }
};

/**
 * Get Core Web Vitals analysis for a website
 */
const getCoreWebVitals = async ({
  url,
  strategy,
}: {
  url: string;
  strategy?: "mobile" | "desktop";
}) => {
  try {
    const params = new URLSearchParams({ site_url: url });
    if (strategy) {
      params.append("strategy", strategy);
    }
    const response = await apiClient.get(
      `/advance-seo/core-web-vitals/?${params.toString()}`
    );
    return response.data;
  } catch (error: any) {
    // If 404, it means no data exists yet - return null instead of throwing
    if (error?.response?.status === 404) {
      return null;
    }
    console.error("Error fetching Core Web Vitals:", error);
    throw error;
  }
};

/**
 * Get Core Web Vitals analysis history for a website
 */
const getCoreWebVitalsHistory = async ({
  url,
  strategy,
}: {
  url: string;
  strategy?: "mobile" | "desktop";
}) => {
  try {
    const params = new URLSearchParams({ site_url: url });
    if (strategy) {
      params.append("strategy", strategy);
    }
    const response = await apiClient.get(
      `/advance-seo/core-web-vitals-history/?${params.toString()}`
    );
    return response.data;
  } catch (error: any) {
    // If 404, it means no history exists yet - return null instead of throwing
    if (error?.response?.status === 404) {
      return null;
    }
    console.error("Error fetching Core Web Vitals history:", error);
    throw error;
  }
};

// React Query hooks for Core Web Vitals
export const useAnalyzeCoreWebVitals = (options = {}) => {
  return useMutation({
    mutationFn: analyzeCoreWebVitals,
    ...options,
  });
};

export const useAnalyzeCoreWebVitalsMultiStrategy = (options = {}) => {
  return useMutation({
    mutationFn: analyzeCoreWebVitalsMultiStrategy,
    ...options,
  });
};

export const useCoreWebVitals = (
  url: string,
  strategy?: "mobile" | "desktop",
  options = {}
) => {
  return useQuery({
    queryKey: ["core-web-vitals", url, strategy],
    queryFn: () => getCoreWebVitals({ url, strategy }),
    enabled: !!url,
    ...options,
  });
};

export const useCoreWebVitalsHistory = (
  url: string,
  strategy?: "mobile" | "desktop",
  options = {}
) => {
  return useQuery({
    queryKey: ["core-web-vitals-history", url, strategy],
    queryFn: () => getCoreWebVitalsHistory({ url, strategy }),
    enabled: !!url,
    ...options,
  });
};
