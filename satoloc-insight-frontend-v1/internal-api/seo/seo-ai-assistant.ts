import React from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import apiClient from "@/lib/apiClient";

// Types for SEO AI Assistant
export interface SEOAIInsight {
  type: "opportunity" | "warning" | "info" | "success";
  title: string;
  description: string;
  priority: "high" | "medium" | "low";
  actionable: boolean;
  metrics: string[];
}

export interface SEOAIResponse {
  success: boolean;
  insights?: SEOAIInsight[];
  website_url?: string;
  component_type?: string;
  user_id?: number;
  timestamp?: string;
  error?: string;
}

export interface SEOAIRequest {
  component_type:
    | "performance-chart"
    | "core-web-vitals"
    | "gsc-data"
    | "competitor-analysis"
    | "technical-analysis";
  website_url: string;
  additional_data?: Record<string, any>;
}

export interface SEOAIHealthCheck {
  success: boolean;
  message?: string;
  openai_configured?: boolean;
  timestamp?: string;
  error?: string;
}

export interface SEOAIHistory {
  success: boolean;
  website_url?: string;
  data_available?: {
    seo_metrics: boolean;
    technical_analysis: boolean;
    core_web_vitals: boolean;
    competitors: number;
    keywords: number;
  };
  last_analysis?: string;
  timestamp?: string;
  error?: string;
}

// Utility function to sanitize data for JSON serialization
const sanitizeDataForAPI = (data: any): any => {
  if (data === null || data === undefined) {
    return data;
  }

  if (typeof data === "function" || typeof data === "symbol") {
    return undefined;
  }

  if (React.isValidElement(data)) {
    return undefined; // Remove React elements
  }

  if (Array.isArray(data)) {
    return data.map(sanitizeDataForAPI).filter((item) => item !== undefined);
  }

  if (typeof data === "object") {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(data)) {
      // Skip React-specific properties and functions
      if (
        key.startsWith("_") ||
        key.includes("react") ||
        key.includes("fiber") ||
        key.includes("React") ||
        key === "icon" || // Skip icon properties which contain React elements
        key === "component" ||
        typeof value === "function"
      ) {
        continue;
      }

      const sanitizedValue = sanitizeDataForAPI(value);
      if (sanitizedValue !== undefined) {
        sanitized[key] = sanitizedValue;
      }
    }
    return sanitized;
  }

  return data;
};

// Extract essential data for specific component types
export const extractEssentialData = (
  componentType: string,
  componentData: any,
  metrics: any
) => {
  const sanitizedComponentData = sanitizeDataForAPI(componentData);
  const sanitizedMetrics = sanitizeDataForAPI(metrics);

  // Component-specific data extraction
  switch (componentType) {
    case "performance-chart":
      return {
        componentData: Array.isArray(sanitizedComponentData)
          ? sanitizedComponentData.map((item) => ({
              date: item?.date,
              clicks: item?.clicks,
              impressions: item?.impressions,
              ctr: item?.ctr,
              position: item?.position,
            }))
          : sanitizedComponentData,
        metrics: Array.isArray(sanitizedMetrics)
          ? sanitizedMetrics.map((metric) => ({
              key: metric?.key,
              label: metric?.label,
              color: metric?.color,
              enabled: metric?.enabled,
            }))
          : sanitizedMetrics,
      };

    case "core-web-vitals":
      return {
        componentData: sanitizedComponentData
          ? {
              performance_score: sanitizedComponentData.performance_score,
              core_web_vitals: sanitizedComponentData.core_web_vitals,
              categories: sanitizedComponentData.categories,
              opportunities: sanitizedComponentData.opportunities,
              diagnostics: sanitizedComponentData.diagnostics,
              timestamp: sanitizedComponentData.timestamp,
              strategy: sanitizedComponentData.strategy,
            }
          : null,
        metrics: sanitizedMetrics,
      };

    default:
      return {
        componentData: sanitizedComponentData,
        metrics: sanitizedMetrics,
      };
  }
};

// API Functions
export const generateSEOAIInsights = async (
  data: SEOAIRequest
): Promise<SEOAIResponse> => {
  try {
    // Sanitize the data to remove circular references and React elements
    const sanitizedData = {
      ...data,
      additional_data: data.additional_data
        ? sanitizeDataForAPI(data.additional_data)
        : undefined,
    };

    const response = await apiClient.post(
      "/ai-assistant/seo/insights/",
      sanitizedData
    );
    return response.data;
  } catch (error: any) {
    console.error("Error generating SEO AI insights:", error);

    // Handle different error types
    if (error.response?.status === 400) {
      return {
        success: false,
        error: error.response.data?.error || "Invalid request parameters",
      };
    } else if (error.response?.status === 401) {
      return {
        success: false,
        error: "Authentication required",
      };
    } else if (error.response?.status === 503) {
      return {
        success: false,
        error: "SEO AI service is currently unavailable",
      };
    }

    return {
      success: false,
      error: "Failed to generate AI insights. Please try again later.",
    };
  }
};

export const checkSEOAIHealth = async (): Promise<SEOAIHealthCheck> => {
  try {
    const response = await apiClient.get("/ai-assistant/seo/health/");
    return response.data;
  } catch (error: any) {
    console.error("Error checking SEO AI health:", error);
    return {
      success: false,
      error: error.response?.data?.error || "Health check failed",
    };
  }
};

export const getSEOAIHistory = async (
  websiteUrl: string
): Promise<SEOAIHistory> => {
  try {
    const response = await apiClient.get("/ai-assistant/seo/history/", {
      params: { website_url: websiteUrl },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error getting SEO AI history:", error);
    return {
      success: false,
      error:
        error.response?.data?.error || "Failed to retrieve analysis history",
    };
  }
};

// React Query Hooks
export const useGenerateSEOAIInsights = (options?: {
  onSuccess?: (data: SEOAIResponse) => void;
  onError?: (error: any) => void;
}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: generateSEOAIInsights,
    onSuccess: (data) => {
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: ["seo-ai-history"],
      });

      if (options?.onSuccess) {
        options.onSuccess(data);
      }
    },
    onError: (error) => {
      console.error("SEO AI insights generation failed:", error);
      if (options?.onError) {
        options.onError(error);
      }
    },
  });
};

export const useSEOAIHealth = () => {
  return useQuery({
    queryKey: ["seo-ai-health"],
    queryFn: checkSEOAIHealth,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

export const useSEOAIHistory = (
  websiteUrl: string,
  options?: {
    enabled?: boolean;
  }
) => {
  return useQuery({
    queryKey: ["seo-ai-history", websiteUrl],
    queryFn: () => getSEOAIHistory(websiteUrl),
    enabled: !!websiteUrl && options?.enabled !== false,
    staleTime: 30 * 1000, // 30 seconds
  });
};

// Utility functions
export const formatInsightsByPriority = (
  insights: SEOAIInsight[]
): {
  high: SEOAIInsight[];
  medium: SEOAIInsight[];
  low: SEOAIInsight[];
} => {
  return {
    high: insights.filter((insight) => insight.priority === "high"),
    medium: insights.filter((insight) => insight.priority === "medium"),
    low: insights.filter((insight) => insight.priority === "low"),
  };
};

export const getInsightsByType = (
  insights: SEOAIInsight[],
  type: SEOAIInsight["type"]
): SEOAIInsight[] => {
  return insights.filter((insight) => insight.type === type);
};

export const getActionableInsights = (
  insights: SEOAIInsight[]
): SEOAIInsight[] => {
  return insights.filter((insight) => insight.actionable);
};

export const getInsightsByMetric = (
  insights: SEOAIInsight[],
  metric: string
): SEOAIInsight[] => {
  return insights.filter((insight) => insight.metrics.includes(metric));
};

// Constants
export const COMPONENT_TYPES = [
  "performance-chart",
  "core-web-vitals",
  "gsc-data",
  "competitor-analysis",
  "technical-analysis",
] as const;

export const INSIGHT_TYPES = [
  "opportunity",
  "warning",
  "info",
  "success",
] as const;

export const INSIGHT_PRIORITIES = ["high", "medium", "low"] as const;
