import { useState, useCallback } from "react";
import apiClient from "@/lib/apiClient";

export interface CustomContent {
  id: number;
  title: string;
  prompt: string;
  keywords: string;
  content: string;
  language: string;
  created_at: string;
  updated_at: string;
}

interface GenerateContentParams {
  title: string;
  prompt: string;
  language: string;
  keywords?: string;
}

interface ImprovePromptParams {
  prompt: string;
  title?: string;
  keywords?: string;
  tone?: string;
}

interface ImproveContentParams {
  content: string;
  element_type?: string;
}

export const useCustomContent = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [contents, setContents] = useState<CustomContent[]>([]);
  const [currentContent, setCurrentContent] = useState<CustomContent | null>(
    null
  );

  const fetchContents = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await apiClient.get("/custom-content/");
      setContents(response.data);
      return response.data;
    } catch (err: any) {
      const errorMessage =
        err.response?.data?.error || err.message || "Failed to fetch content";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getContent = useCallback(async (id: number) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await apiClient.get(`/custom-content/${id}/`);
      setCurrentContent(response.data);
      return response.data;
    } catch (err: any) {
      const errorMessage =
        err.response?.data?.error || err.message || "Failed to fetch content";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const generateContent = useCallback(
    async (params: GenerateContentParams) => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await apiClient.post(
          "/custom-content/generate/",
          params
        );
        setCurrentContent(response.data);
        await fetchContents(); // Refresh the list after generating new content
        return response.data;
      } catch (err: any) {
        const errorMessage =
          err.response?.data?.error ||
          err.message ||
          "Failed to generate content";
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    },
    [fetchContents]
  );

  const createContent = useCallback(
    async (params: Omit<CustomContent, "id" | "created_at" | "updated_at">) => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await apiClient.post("/custom-content/", params);
        await fetchContents(); // Refresh the list after creating new content
        return response.data;
      } catch (err: any) {
        const errorMessage =
          err.response?.data?.error ||
          err.message ||
          "Failed to create content";
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    },
    [fetchContents]
  );

  const updateContent = useCallback(
    async (
      id: number,
      params: Omit<CustomContent, "id" | "created_at" | "updated_at">
    ) => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await apiClient.put(`/custom-content/${id}/`, params);
        await fetchContents(); // Refresh the list after updating content
        return response.data;
      } catch (err: any) {
        const errorMessage =
          err.response?.data?.error ||
          err.message ||
          "Failed to update content";
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    },
    [fetchContents]
  );

  const saveContent = useCallback(
    async (
      params: Omit<CustomContent, "id" | "created_at" | "updated_at">,
      contentId?: number
    ) => {
      if (contentId) {
        // Update existing content
        return await updateContent(contentId, params);
      } else {
        // Create new content
        return await createContent(params);
      }
    },
    [createContent, updateContent]
  );

  const deleteContent = useCallback(
    async (id: number) => {
      setIsLoading(true);
      setError(null);
      try {
        await apiClient.delete(`/custom-content/${id}/`);

        // Optimistically update the local state instead of refetching
        setContents((prevContents) =>
          prevContents.filter((content) => content.id !== id)
        );

        // Clear current content if it's the one being deleted
        if (currentContent?.id === id) {
          setCurrentContent(null);
        }
      } catch (err: any) {
        const errorMessage =
          err.response?.data?.error ||
          err.message ||
          "Failed to delete content";
        setError(errorMessage);

        // If delete failed, refresh the list to ensure consistency
        try {
          await fetchContents();
        } catch (fetchErr) {
          console.error(
            "Failed to refresh content list after delete error:",
            fetchErr
          );
        }

        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    },
    [currentContent, fetchContents]
  );

  const improvePrompt = useCallback(async (params: ImprovePromptParams) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await apiClient.post(
        "/custom-content/improve_prompt/",
        params
      );
      return response.data;
    } catch (err: any) {
      const errorMessage =
        err.response?.data?.error || err.message || "Failed to improve prompt";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const improveContent = useCallback(async (params: ImproveContentParams) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await apiClient.post(
        "/custom-content/improve_content/",
        params
      );
      return response.data;
    } catch (err: any) {
      const errorMessage =
        err.response?.data?.error || err.message || "Failed to improve content";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    isLoading,
    error,
    contents,
    currentContent,
    fetchContents,
    getContent,
    generateContent,
    createContent,
    updateContent,
    saveContent,
    deleteContent,
    improvePrompt,
    improveContent,
    setCurrentContent,
  };
};
