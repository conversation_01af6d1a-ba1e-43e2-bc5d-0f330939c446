# WordPress Blog Integration

This module provides React hooks and utilities for integrating WordPress as a headless CMS with your Next.js application.

## Setup

### 1. Environment Variables

Add the following to your `.env.local` file:

```bash
# WordPress API URL (defaults to localhost:8080 for development)
NEXT_PUBLIC_WP_API_BASE_URL=http://localhost:8080/wp-json/wp/v2
```

For production, update this to your production WordPress URL:

```bash
NEXT_PUBLIC_WP_API_BASE_URL=https://your-wordpress-site.com/wp-json/wp/v2
```

### 2. WordPress REST API

Make sure your WordPress installation has the REST API enabled (it's enabled by default in WordPress 4.7+).

Test the API by visiting: `http://localhost:8080/wp-json/wp/v2/posts`

## Available Hooks

### `useWordPressPosts(params?)`

Fetches a list of WordPress posts with optional filtering.

```tsx
import { useWordPressPosts } from "@/internal-api/wordpress-blog";

const {
  data: posts,
  isLoading,
  error,
} = useWordPressPosts({
  per_page: 10,
  orderby: "date",
  order: "desc",
  search: "search term",
  categories: [1, 2],
  tags: [3, 4],
});
```

### `useWordPressPost(id, enabled?)`

Fetches a single WordPress post by ID.

```tsx
import { useWordPressPost } from "@/internal-api/wordpress-blog";

const { data: post, isLoading, error } = useWordPressPost(123);
```

### `useWordPressPostBySlug(slug, enabled?)`

Fetches a single WordPress post by slug.

```tsx
import { useWordPressPostBySlug } from "@/internal-api/wordpress-blog";

const { data: post, isLoading, error } = useWordPressPostBySlug("my-blog-post");
```

### `useWordPressCategories()`

Fetches all WordPress categories.

```tsx
import { useWordPressCategories } from "@/internal-api/wordpress-blog";

const { data: categories, isLoading, error } = useWordPressCategories();
```

### `useWordPressTags()`

Fetches all WordPress tags.

```tsx
import { useWordPressTags } from "@/internal-api/wordpress-blog";

const { data: tags, isLoading, error } = useWordPressTags();
```

### `useWordPressMedia(id, enabled?)`

Fetches WordPress media (images, etc.) by ID.

```tsx
import { useWordPressMedia } from "@/internal-api/wordpress-blog";

const { data: media, isLoading, error } = useWordPressMedia(456);
```

### `useWordPressPostsInfinite(params?)`

For pagination with infinite scroll.

```tsx
import { useWordPressPostsInfinite } from "@/internal-api/wordpress-blog";

const { data, fetchNextPage, hasNextPage, isFetchingNextPage } =
  useWordPressPostsInfinite({
    per_page: 6,
  });
```

## Utility Functions

### `getFeaturedImageUrl(post, media?, size?)`

Gets the featured image URL for a post.

```tsx
import { getFeaturedImageUrl } from "@/internal-api/wordpress-blog";

const imageUrl = getFeaturedImageUrl(post, media, "medium");
```

### `getExcerpt(post, length?)`

Gets a clean excerpt from a post (strips HTML).

```tsx
import { getExcerpt } from "@/internal-api/wordpress-blog";

const excerpt = getExcerpt(post, 150); // 150 characters
```

### `stripHtml(html)`

Removes HTML tags from content.

```tsx
import { stripHtml } from "@/internal-api/wordpress-blog";

const plainText = stripHtml(post.content.rendered);
```

## Query Parameters

The `WordPressQueryParams` interface supports:

- `page?: number` - Page number for pagination
- `per_page?: number` - Number of posts per page (max 100)
- `search?: string` - Search term
- `author?: number` - Author ID
- `categories?: number[]` - Category IDs
- `tags?: number[]` - Tag IDs
- `status?: 'publish' | 'draft' | 'private'` - Post status
- `order?: 'asc' | 'desc'` - Sort order
- `orderby?: 'date' | 'title' | 'slug' | 'modified'` - Sort field
- `_fields?: string[]` - Specific fields to return

## Example Usage

### Basic Blog List

```tsx
import { useWordPressPosts } from "@/internal-api/wordpress-blog";

export const BlogList = () => {
  const {
    data: posts,
    isLoading,
    error,
  } = useWordPressPosts({
    per_page: 6,
    orderby: "date",
    order: "desc",
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {posts?.map((post) => <BlogPostCard key={post.id} post={post} />)}
    </div>
  );
};
```

### Blog Post Detail

```tsx
import { useWordPressPostBySlug } from "@/internal-api/wordpress-blog";

export const BlogPost = ({ slug }: { slug: string }) => {
  const { data: post, isLoading, error } = useWordPressPostBySlug(slug);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  if (!post) return <div>Post not found</div>;

  return (
    <article>
      <h1>{post.title.rendered}</h1>
      <div dangerouslySetInnerHTML={{ __html: post.content.rendered }} />
    </article>
  );
};
```

## Types

All TypeScript interfaces are exported from the module:

- `WordPressPost` - Blog post data
- `WordPressCategory` - Category data
- `WordPressTag` - Tag data
- `WordPressMedia` - Media/image data
- `WordPressQueryParams` - Query parameters

## Caching

All hooks use React Query for caching:

- Posts: 5 minutes
- Single post: 10 minutes
- Categories/Tags: 30 minutes
- Media: 1 hour

## Error Handling

All hooks return standard React Query error states. Handle them in your components:

```tsx
const { data, isLoading, error } = useWordPressPosts();

if (error) {
  console.error("WordPress API error:", error.message);
  return <div>Failed to load posts</div>;
}
```

## Production Notes

1. **Performance**: Use `_fields` parameter to only fetch needed data
2. **SEO**: WordPress content includes proper meta data for SEO
3. **Images**: WordPress provides multiple image sizes - use appropriate size for your layout
4. **Caching**: Consider adding CDN for WordPress media files
5. **Security**: Ensure your WordPress REST API is properly secured in production
