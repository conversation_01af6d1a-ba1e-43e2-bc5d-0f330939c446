import axios from "axios";
import { useQuery, useInfiniteQuery } from "@tanstack/react-query";

// WordPress API Base URL - using environment variable with fallback to development
const WP_API_BASE_URL = process.env.NEXT_PUBLIC_WP_API_BASE_URL;

// WordPress Post Interface
export interface WordPressPost {
  id: number;
  date: string;
  date_gmt: string;
  modified: string;
  modified_gmt: string;
  slug: string;
  status: "publish" | "draft" | "private" | "pending";
  type: string;
  link: string;
  title: {
    rendered: string;
  };
  content: {
    rendered: string;
    protected: boolean;
  };
  excerpt: {
    rendered: string;
    protected: boolean;
  };
  author: number;
  featured_media: number;
  comment_status: "open" | "closed";
  ping_status: "open" | "closed";
  sticky: boolean;
  template: string;
  format: string;
  meta: Record<string, any>;
  categories: number[];
  tags: number[];
  _links: Record<string, any>;
}

// WordPress Category Interface
export interface WordPressCategory {
  id: number;
  count: number;
  description: string;
  link: string;
  name: string;
  slug: string;
  taxonomy: string;
  parent: number;
}

// WordPress Tag Interface
export interface WordPressTag {
  id: number;
  count: number;
  description: string;
  link: string;
  name: string;
  slug: string;
  taxonomy: string;
}

// WordPress Media Interface
export interface WordPressMedia {
  id: number;
  date: string;
  slug: string;
  type: string;
  link: string;
  title: {
    rendered: string;
  };
  author: number;
  media_type: "image" | "video" | "audio" | "file";
  mime_type: string;
  media_details: {
    width: number;
    height: number;
    file: string;
    sizes: Record<
      string,
      {
        file: string;
        width: number;
        height: number;
        mime_type: string;
        source_url: string;
      }
    >;
  };
  source_url: string;
  alt_text: string;
}

// Query Parameters Interface
export interface WordPressQueryParams {
  page?: number;
  per_page?: number;
  search?: string;
  author?: number;
  categories?: number[];
  tags?: number[];
  status?: "publish" | "draft" | "private";
  order?: "asc" | "desc";
  orderby?: "date" | "title" | "slug" | "modified";
  _fields?: string[];
}

// Homepage Content Interface
export interface HomepageContent {
  id: number;
  title: string;
  header: {
    title: string | null;
    description: string | null;
  };
  features: {
    localization: {
      title: string | null;
      description: string | null;
    };
    optimization: {
      title: string | null;
      description: string | null;
    };
    analytics: {
      title: string | null;
      description: string | null;
    };
    content_creation: {
      title: string | null;
      description: string | null;
    };
    management: {
      title: string | null;
      description: string | null;
    };
    autolqa: {
      title: string | null;
      description: string | null;
    };
  };
  strategy: {
    title: string | null;
    description: string | null;
    locate: {
      title: string | null;
      description: string | null;
    };
    details: {
      title: string | null;
      description: string | null;
    };
    insight: {
      title: string | null;
      description: string | null;
    };
    action: {
      title: string | null;
      description: string | null;
    };
  };
  awards: {
    title: string | null;
    description: string | null;
  };
  roadmap: {
    title: string | null;
    description: string | null;
  };
  pricing: {
    title: string | null;
    description: string | null;
    plans: {
      freemium?: {
        name: string | null;
        description: string | null;
        price: number | null;
        annual_price: number | null;
        discount: number | null;
        audience: string | null;
        translation_limit: number | null;
        content_limit: number | null;
        seo_analysis: string | null;
        team_members: number | null;
        features: Array<{
          name: string;
          included: boolean;
        }>;
        button_text: string | null;
        button_variant: string | null;
        is_coming: boolean;
      };
      pro?: {
        name: string | null;
        description: string | null;
        price: number | null;
        annual_price: number | null;
        discount: number | null;
        audience: string | null;
        translation_limit: number | null;
        content_limit: number | null;
        seo_analysis: string | null;
        team_members: number | null;
        features: Array<{
          name: string;
          included: boolean;
        }>;
        button_text: string | null;
        button_variant: string | null;
        is_coming: boolean;
      };
      premium?: {
        name: string | null;
        description: string | null;
        price: number | null;
        annual_price: number | null;
        discount: number | null;
        audience: string | null;
        translation_limit: number | null;
        content_limit: number | null;
        seo_analysis: string | null;
        team_members: number | null;
        features: Array<{
          name: string;
          included: boolean;
        }>;
        button_text: string | null;
        button_variant: string | null;
        is_coming: boolean;
      };
    };
  };
  auto_lqa: {
    title: string | null;
    description: string | null;
  };
  faq: {
    title: string | null;
    description: string | null;
    items: Array<{
      question: string;
      answer: string;
    }>;
  };
  updated_at: string;
  partners: {
    logo: {
      id: number;
      url: string;
      alt: string;
    }[];
  };
}

// API Functions
export const fetchWordPressPostsWithPagination = async (
  params: WordPressQueryParams = {}
): Promise<{
  posts: WordPressPost[];
  pagination: {
    page: number;
    per_page: number;
    total: number;
    total_pages: number;
  };
}> => {
  const searchParams = new URLSearchParams();

  // Default parameters
  searchParams.append("per_page", String(params.per_page || 10));
  searchParams.append("page", String(params.page || 1));
  searchParams.append("status", params.status || "publish");
  searchParams.append("order", params.order || "desc");
  searchParams.append("orderby", params.orderby || "date");

  // Optional parameters
  if (params.search) searchParams.append("search", params.search);
  if (params.author) searchParams.append("author", String(params.author));
  if (params.categories?.length)
    searchParams.append("categories", params.categories.join(","));
  if (params.tags?.length) searchParams.append("tags", params.tags.join(","));
  if (params._fields?.length)
    searchParams.append("_fields", params._fields.join(","));

  const response = await axios.get<WordPressPost[]>(
    `${WP_API_BASE_URL}/posts?${searchParams.toString()}`
  );

  // Extract pagination info from headers
  const totalPosts = parseInt(response.headers["x-wp-total"] || "0");
  const totalPages = parseInt(response.headers["x-wp-totalpages"] || "1");
  const currentPage = params.page || 1;
  const perPage = params.per_page || 10;

  return {
    posts: response.data,
    pagination: {
      page: currentPage,
      per_page: perPage,
      total: totalPosts,
      total_pages: totalPages,
    },
  };
};

export const fetchWordPressPosts = async (
  params: WordPressQueryParams = {}
): Promise<WordPressPost[]> => {
  const searchParams = new URLSearchParams();

  // Default parameters
  searchParams.append("per_page", String(params.per_page || 10));
  searchParams.append("page", String(params.page || 1));
  searchParams.append("status", params.status || "publish");
  searchParams.append("order", params.order || "desc");
  searchParams.append("orderby", params.orderby || "date");

  // Optional parameters
  if (params.search) searchParams.append("search", params.search);
  if (params.author) searchParams.append("author", String(params.author));
  if (params.categories?.length)
    searchParams.append("categories", params.categories.join(","));
  if (params.tags?.length) searchParams.append("tags", params.tags.join(","));
  if (params._fields?.length)
    searchParams.append("_fields", params._fields.join(","));

  const response = await axios.get<WordPressPost[]>(
    `${WP_API_BASE_URL}/posts?${searchParams.toString()}`
  );

  return response.data;
};

export const fetchWordPressPost = async (
  id: number | string
): Promise<WordPressPost> => {
  const response = await axios.get<WordPressPost>(
    `${WP_API_BASE_URL}/posts/${id}`
  );

  return response.data;
};

export const fetchWordPressPostBySlug = async (
  slug: string
): Promise<WordPressPost> => {
  const response = await axios.get<WordPressPost[]>(
    `${WP_API_BASE_URL}/posts?slug=${slug}&status=publish`
  );

  if (response.data.length === 0) {
    throw new Error(`Post with slug "${slug}" not found`);
  }

  return response.data[0];
};

export const fetchWordPressCategories = async (): Promise<
  WordPressCategory[]
> => {
  const response = await axios.get<WordPressCategory[]>(
    `${WP_API_BASE_URL}/categories?per_page=100`
  );

  return response.data;
};

export const fetchWordPressTags = async (): Promise<WordPressTag[]> => {
  const response = await axios.get<WordPressTag[]>(
    `${WP_API_BASE_URL}/tags?per_page=100`
  );

  return response.data;
};

export const fetchWordPressMedia = async (
  id: number
): Promise<WordPressMedia> => {
  const response = await axios.get<WordPressMedia>(
    `${WP_API_BASE_URL}/media/${id}`
  );

  return response.data;
};

// React Query Hooks
export const useWordPressPostsWithPagination = (
  params: WordPressQueryParams = {}
) => {
  return useQuery<
    {
      posts: WordPressPost[];
      pagination: {
        page: number;
        per_page: number;
        total: number;
        total_pages: number;
      };
    },
    Error
  >({
    queryKey: ["wordpressPostsWithPagination", params],
    queryFn: () => fetchWordPressPostsWithPagination(params),
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    retry: 2,
  });
};

export const useWordPressPosts = (params: WordPressQueryParams = {}) => {
  return useQuery<WordPressPost[], Error>({
    queryKey: ["wordpressPosts", params],
    queryFn: () => fetchWordPressPosts(params),
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    retry: 2,
  });
};

export const useWordPressPost = (
  id: number | string,
  enabled: boolean = true
) => {
  return useQuery<WordPressPost, Error>({
    queryKey: ["wordpressPost", id],
    queryFn: () => fetchWordPressPost(id),
    enabled: enabled && !!id,
    staleTime: 10 * 60 * 1000, // Cache for 10 minutes
    retry: 2,
  });
};

export const useWordPressPostBySlug = (
  slug: string,
  enabled: boolean = true
) => {
  return useQuery<WordPressPost, Error>({
    queryKey: ["wordpressPostSlug", slug],
    queryFn: () => fetchWordPressPostBySlug(slug),
    enabled: enabled && !!slug,
    staleTime: 10 * 60 * 1000, // Cache for 10 minutes
    retry: 2,
  });
};

export const useWordPressCategories = () => {
  return useQuery<WordPressCategory[], Error>({
    queryKey: ["wordpressCategories"],
    queryFn: fetchWordPressCategories,
    staleTime: 30 * 60 * 1000, // Cache for 30 minutes
    retry: 2,
  });
};

export const useWordPressTags = () => {
  return useQuery<WordPressTag[], Error>({
    queryKey: ["wordpressTags"],
    queryFn: fetchWordPressTags,
    staleTime: 30 * 60 * 1000, // Cache for 30 minutes
    retry: 2,
  });
};

export const useWordPressMedia = (id: number, enabled: boolean = true) => {
  return useQuery<WordPressMedia, Error>({
    queryKey: ["wordpressMedia", id],
    queryFn: () => fetchWordPressMedia(id),
    enabled: enabled && !!id,
    staleTime: 60 * 60 * 1000, // Cache for 1 hour
    retry: 2,
  });
};

// Infinite Query for pagination
export const useWordPressPostsInfinite = (
  params: Omit<WordPressQueryParams, "page"> = {}
) => {
  return useInfiniteQuery<WordPressPost[], Error>({
    queryKey: ["wordpressPostsInfinite", params],
    queryFn: ({ pageParam = 1 }) =>
      fetchWordPressPosts({ ...params, page: pageParam as number }),
    getNextPageParam: (lastPage, allPages) => {
      // WordPress returns fewer posts than requested when we've reached the end
      const expectedPostsPerPage = params.per_page || 10;
      return lastPage.length === expectedPostsPerPage
        ? allPages.length + 1
        : undefined;
    },
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    retry: 2,
  });
};

// Utility function to get featured image URL
export const getFeaturedImageUrl = (
  post: WordPressPost,
  media?: WordPressMedia,
  size: string = "medium"
): string | null => {
  if (!media || !media.media_details?.sizes) {
    return media?.source_url || null;
  }

  const sizeData = media.media_details.sizes[size];
  return sizeData ? sizeData.source_url : media.source_url;
};

// Utility function to strip HTML from content
export const stripHtml = (html: string): string => {
  if (typeof window !== "undefined") {
    const temp = document.createElement("div");
    temp.innerHTML = html;
    return temp.textContent || temp.innerText || "";
  }
  // Fallback for server-side rendering
  return html.replace(/<[^>]*>/g, "");
};

// Utility function to get excerpt
export const getExcerpt = (
  post: WordPressPost,
  length: number = 150
): string => {
  const excerpt = post.excerpt.rendered || post.content.rendered;
  const plainText = stripHtml(excerpt);

  if (plainText.length <= length) {
    return plainText;
  }

  return plainText.substring(0, length).trim() + "...";
};

export const fetchHomepageContent = async (): Promise<HomepageContent> => {
  const response = await axios.get<HomepageContent>(
    `${WP_API_BASE_URL?.replace("/wp/v2", "")}/satoloc/v1/homepage-content`
  );

  return response.data;
};

export const useHomepageContent = () => {
  return useQuery<HomepageContent, Error>({
    queryKey: ["homepageContent"],
    queryFn: fetchHomepageContent,
    staleTime: 15 * 60 * 1000, // Cache for 15 minutes
    retry: 2,
  });
};
