// internal-api/admin-user/index.ts

import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import axios from "axios";
import { getSession } from "next-auth/react";

/**
 * Fetch all users
 */
export const fetchAllUsers = async () => {
  const session = await getSession();
  if (!session || !session.accessToken) {
    throw new Error("No authentication token available");
  }

  const response = await axios.get(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/admin/users/`,
    {
      headers: {
        Authorization: `Bearer ${session.accessToken}`,
      },
    }
  );

  return response.data;
};

/**
 * Hook to fetch all users
 */
export const useFetchAllUsers = () => {
  return useQuery(["adminUsers"], fetchAllUsers);
};

/**
 * Add a new user
 */
interface AddUserData {
  username: string;
  email: string;
  password: string;
  role: string;
}

export const addUser = async (userData: AddUserData) => {
  const session = await getSession();
  if (!session || !session.accessToken) {
    throw new Error("No authentication token available");
  }

  const response = await axios.post("/api/auth/admin/users", userData, {
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${session.accessToken}`,
    },
  });

  return response.data;
};

/**
 * Hook to add a new user
 */
export const useAddUser = () => {
  const queryClient = useQueryClient();

  return useMutation(addUser, {
    onSuccess: () => {
      queryClient.invalidateQueries(["adminUsers"]);
    },
    onError: (error: any) => {
      console.error("User addition failed:", error);
      throw error;
    },
  });
};

/**
 * Update an existing user
 */
interface UpdateUserData {
  first_name?: string;
  last_name?: string;
  email?: string;
  roles?: string[] | string;
}

export const updateUser = async (userId: number, userData: UpdateUserData) => {
  const session = await getSession();
  if (!session || !session.accessToken) {
    throw new Error("No authentication token available");
  }

  // Using the correct endpoint that matches your Django URL patterns
  const response = await axios.put(
    `/api/admin/users/${userId}/`, // Changed to match your routing structure
    userData,
    {
      headers: {
        Authorization: `Bearer ${session.accessToken}`,
      },
    }
  );

  return response.data;
};

/**
 * Hook to update a user
 */
export const useUpdateUser = () => {
  const queryClient = useQueryClient();

  return useMutation(
    ({ userId, userData }: { userId: number; userData: UpdateUserData }) =>
      updateUser(userId, userData),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(["adminUsers"]);
      },
      onError: (error: any) => {
        console.error("User update failed:", error);
        throw error;
      },
    }
  );
};

/**
 * Delete a user
 */
export const deleteUser = async (userId: number) => {
  const session = await getSession();
  if (!session || !session.accessToken) {
    throw new Error("No authentication token available");
  }

  const response = await axios.delete(`/api/admin/users/${userId}/`, {
    headers: {
      Authorization: `Bearer ${session.accessToken}`,
    },
  });

  return response.data;
};

/**
 * Hook to delete a user
 */
export const useDeleteUser = () => {
  const queryClient = useQueryClient();

  return useMutation((userId: number) => deleteUser(userId), {
    onSuccess: () => {
      queryClient.invalidateQueries(["adminUsers"]);
    },
    onError: (error: any) => {
      console.error("User deletion failed:", error);
      throw error;
    },
  });
};

/**
 * Fetch a single user by ID
 */
export const fetchUserById = async (userId: number) => {
  const session = await getSession();
  if (!session || !session.accessToken) {
    throw new Error("No authentication token available");
  }

  // Using the correct endpoint from your Django backend
  const response = await axios.get(
    `/api/admin/users/${userId}/`, // Changed to match your routing structure
    {
      headers: {
        Authorization: `Bearer ${session.accessToken}`,
      },
    }
  );

  return response.data;
};

/**
 * Hook to fetch a single user by ID
 */
export const useFetchUserById = (userId: number) => {
  return useQuery(["adminUser", userId], () => fetchUserById(userId));
};
