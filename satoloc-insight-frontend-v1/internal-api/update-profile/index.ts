// internal-api/update-profile/index.ts
import { useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { getSession } from "next-auth/react";

interface UpdateProfileData {
  firstname: string;
  lastname: string;
  company_name: string;
  website: string;
  industry: string;
}

export const useUpdateProfile = () => {
  const queryClient = useQueryClient();

  return useMutation(
    async (data: UpdateProfileData) => {
      const session = await getSession();

      if (!session || !session.accessToken) {
        throw new Error("No authentication token available");
      }

      const response = await axios.patch("/api/auth/update-profile", data, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session.accessToken}`,
        },
      });
      return response.data;
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(["userData"]);
        queryClient.invalidateQueries(["profile"]);
      },
      onError: (error: any) => {
        console.error("Profile update failed:", error);
        if (axios.isAxiosError(error) && error.response) {
          const backendError =
            error.response.data?.error ||
            error.response.data?.message ||
            JSON.stringify(error.response.data);
          throw new Error(`Update failed: ${backendError}`);
        }
        throw new Error(
          error.message || "An unexpected error occurred while updating profile."
        );
      },
    }
  );
};
