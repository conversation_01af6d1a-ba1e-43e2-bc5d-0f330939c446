// internal-api/get-user-data/index.ts
import axios from "axios";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";

export interface UserData {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  company_name: string;
  website: string;
  industry: string;
  avatar?: string;
  registration_date: string;
  role?: string;
  name: string;
  created: string;
  subscription_plan?: string;
  subscription_price?: string;
  subscription_start_date?: string;
  // Sub-admin related fields
  created_by?: number | null;
  created_by_username?: string | null;
  created_users_count?: number;
  remaining_user_slots?: number | null;
  can_create_more_users?: boolean;
  created_users?: Array<{
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
    is_active: boolean;
    registration_date: string;
  }>;
}

export const fetchUserData = async (userToken: string | undefined) => {
  if (!userToken) {
    throw new Error("No authentication token available");
  }

  const response = await axios.get<UserData>(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/profile/`,
    {
      headers: {
        Authorization: `Bearer ${userToken}`,
      },
    }
  );

  return response.data;
};

export const useUserData = () => {
  const { data: session } = useSession();

  return useQuery<UserData, Error>({
    queryKey: ["userData"],
    queryFn: () => fetchUserData(session?.accessToken),
    enabled: !!session?.accessToken,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    retry: 2,
  });
};
