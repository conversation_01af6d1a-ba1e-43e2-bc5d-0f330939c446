import { useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import apiClient from "@/lib/apiClient";

interface CheckoutSessionData {
  price_id: string;
  success_url: string;
  cancel_url: string;
  trial_days?: number;
}

interface CheckoutSessionResponse {
  session_id: string;
  url: string;
  customer_id: string;
}

interface UseStripeCheckoutReturn {
  createCheckoutSession: (data: CheckoutSessionData) => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

export const useStripeCheckout = (): UseStripeCheckoutReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const createCheckoutSession = async (data: CheckoutSessionData) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiClient.post(
        "/payments/create-checkout-session/",
        data
      );

      const result: CheckoutSessionResponse = response.data;

      // Redirect to Stripe Checkout
      window.location.href = result.url;
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An unexpected error occurred";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    createCheckoutSession,
    isLoading,
    error,
  };
};
