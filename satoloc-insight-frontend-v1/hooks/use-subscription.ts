import { useState, useEffect } from "react";
import { toast } from "sonner";
import apiClient from "@/lib/apiClient";

interface SubscriptionData {
  id: string;
  stripe_subscription_id: string;
  status: string;
  plan_type: string;
  current_period_start: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  canceled_at: string | null;
  trial_end: string | null;
  created_at: string;
  updated_at: string;
}

interface BillingPortalData {
  return_url: string;
}

interface BillingPortalResponse {
  url: string;
}

interface UseSubscriptionReturn {
  subscription: SubscriptionData | null;
  isLoading: boolean;
  error: string | null;
  fetchSubscription: () => Promise<void>;
  createBillingPortalSession: (returnUrl: string) => Promise<void>;
  cancelSubscription: (
    subscriptionId: string,
    atPeriodEnd?: boolean
  ) => Promise<void>;
  isSubscriptionActive: boolean;
  planType: string | null;
}

export const useSubscription = (): UseSubscriptionReturn => {
  const [subscription, setSubscription] = useState<SubscriptionData | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSubscription = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiClient.get("/payments/subscription/");
      const result: SubscriptionData = response.data;
      setSubscription(result);
    } catch (err: any) {
      if (err.response?.status === 404) {
        // No subscription found
        setSubscription(null);
        return;
      }
      const errorMessage =
        err.response?.data?.error ||
        err.message ||
        "An unexpected error occurred";
      setError(errorMessage);
      console.error("Error fetching subscription:", errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const createBillingPortalSession = async (returnUrl: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiClient.post(
        "/payments/create-billing-portal-session/",
        { return_url: returnUrl }
      );

      const result: BillingPortalResponse = response.data;

      // Redirect to Stripe billing portal
      window.location.href = result.url;
    } catch (err: any) {
      const errorMessage =
        err.response?.data?.error ||
        err.message ||
        "An unexpected error occurred";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const cancelSubscription = async (
    subscriptionId: string,
    atPeriodEnd: boolean = true
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      await apiClient.post("/payments/cancel-subscription/", {
        subscription_id: subscriptionId,
        at_period_end: atPeriodEnd,
      });

      toast.success("Subscription canceled successfully");

      // Refresh subscription data
      await fetchSubscription();
    } catch (err: any) {
      const errorMessage =
        err.response?.data?.error ||
        err.message ||
        "An unexpected error occurred";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-fetch subscription on mount
  useEffect(() => {
    fetchSubscription();
  }, []);

  const isSubscriptionActive =
    subscription?.status === "active" || subscription?.status === "trialing";
  const planType = subscription?.plan_type || null;

  return {
    subscription,
    isLoading,
    error,
    fetchSubscription,
    createBillingPortalSession,
    cancelSubscription,
    isSubscriptionActive,
    planType,
  };
};
