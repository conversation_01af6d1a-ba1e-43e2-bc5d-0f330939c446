import { useState, useEffect } from "react";
import apiClient from "@/lib/apiClient";

interface PriceData {
  id: string;
  stripe_price_id: string;
  amount: string;
  currency: string;
  billing_interval: string;
  is_active: boolean;
  product_name: string;
  product_plan_type: string;
  created_at: string;
  updated_at: string;
}

interface ProductData {
  id: string;
  stripe_product_id: string;
  name: string;
  description: string;
  plan_type: string;
  is_active: boolean;
  prices: PriceData[];
}

interface UsePricingReturn {
  products: ProductData[];
  isLoading: boolean;
  error: string | null;
  fetchPricing: () => Promise<void>;
  getProductByPlanType: (planType: string) => ProductData | null;
  getPriceById: (priceId: string) => PriceData | null;
}

export const usePricing = (): UsePricingReturn => {
  const [products, setProducts] = useState<ProductData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPricing = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiClient.get("/payments/products/");
      const result: ProductData[] = response.data;
      setProducts(result);
    } catch (err: any) {
      const errorMessage =
        err.response?.data?.error ||
        err.message ||
        "An unexpected error occurred";
      setError(errorMessage);
      console.error("Error fetching pricing:", errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const getProductByPlanType = (planType: string): ProductData | null => {
    return products.find((product) => product.plan_type === planType) || null;
  };

  const getPriceById = (priceId: string): PriceData | null => {
    for (const product of products) {
      const price = product.prices.find((p) => p.stripe_price_id === priceId);
      if (price) return price;
    }
    return null;
  };

  // Auto-fetch pricing on mount
  useEffect(() => {
    fetchPricing();
  }, []);

  return {
    products,
    isLoading,
    error,
    fetchPricing,
    getProductByPlanType,
    getPriceById,
  };
};
