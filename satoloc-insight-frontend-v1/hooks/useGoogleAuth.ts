import { useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { signIn } from "next-auth/react";

interface GoogleAuthResponse {
  access_token: string;
  refresh_token?: string;
  user: {
    id: string;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
    google_id: string;
  };
  is_new_registration: boolean;
}

interface GoogleAuthUrlResponse {
  authorization_url: string;
  state: string;
  redirect_uri: string;
}

export const useGoogleAuth = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const getGoogleAuthUrl = useCallback(
    async (redirectUri: string): Promise<string | null> => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/google/url/?redirect_uri=${encodeURIComponent(redirectUri)}`
        );

        if (!response.ok) {
          throw new Error("Failed to get Google authorization URL");
        }

        const data = await response.json();

        // Store state for validation
        localStorage.setItem("google_oauth_state", data.state);
        localStorage.setItem("google_oauth_redirect_uri", redirectUri);

        return data.authorization_url;
      } catch (err: any) {
        setError(err.message || "Failed to get Google authorization URL");
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const handleGoogleCallback = useCallback(
    async (
      code: string,
      state: string,
      redirectUri: string
    ): Promise<GoogleAuthResponse | null> => {
      try {
        setIsLoading(true);
        setError(null);

        // Check if this combination has already been processed
        const processingKey = `google_callback_${code.slice(-10)}_${state.slice(-10)}`;
        const resultKey = `google_callback_result_${code.slice(-10)}_${state.slice(-10)}`;
        const alreadyProcessing = localStorage.getItem(processingKey);

        if (alreadyProcessing) {
          // Instead of throwing an error, wait for the other process to complete

          // Wait for up to 10 seconds for the other process to complete
          for (let i = 0; i < 50; i++) {
            await new Promise((resolve) => setTimeout(resolve, 200));

            // Check if the other process completed and stored a result
            const storedResult = localStorage.getItem(resultKey);
            if (storedResult) {
              try {
                const result = JSON.parse(storedResult);
                return result;
              } catch (e) {
                console.error("Failed to parse stored result:", e);
              }
            }

            // Check if the other process completed (no longer processing)
            if (!localStorage.getItem(processingKey)) {
              break;
            }

            // If we've waited too long, proceed anyway
            if (i === 49) {
              localStorage.removeItem(processingKey);
              break;
            }
          }

          // If we get here and there's no stored result, the first process might have failed
          // Return null to avoid duplicate API calls
          const storedResult = localStorage.getItem(resultKey);
          if (!storedResult) {
            return null;
          }
        }

        // Mark as processing (or re-mark if we took over)
        localStorage.setItem(processingKey, "true");

        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/google/callback/`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              code,
              state,
              redirect_uri: redirectUri,
            }),
          }
        );

        if (!response.ok) {
          const errorData = await response.json();
          // Clean up processing flag on error
          localStorage.removeItem(processingKey);
          throw new Error(errorData.error || "Google authentication failed");
        }

        const data: GoogleAuthResponse = await response.json();

        // Store the result for other processes that might be waiting
        localStorage.setItem(resultKey, JSON.stringify(data));

        // Instead of storing tokens manually, use NextAuth signIn
        const result = await signIn("credentials", {
          username: data.user.username,
          password: "google_oauth_bypass", // Special flag for Google users
          isGoogleAuth: "true",
          googleAccessToken: data.access_token,
          redirect: false,
        });

        // Clean up processing flag
        localStorage.removeItem(processingKey);

        // Clean up result after a delay (in case other processes need it)
        setTimeout(() => {
          localStorage.removeItem(resultKey);
        }, 5000);

        // Check if NextAuth signIn was successful
        if (result?.ok) {
          // Success - return the data
          return data;
        } else if (result?.error) {
          // Only throw error if NextAuth actually failed
          console.error("NextAuth signIn error:", result.error);
          throw new Error(`Authentication failed: ${result.error}`);
        } else {
          // Fallback - assume success if no explicit error
          console.warn("NextAuth signIn returned unexpected result:", result);
          return data;
        }
      } catch (err: any) {
        console.error("Google callback error:", err);
        setError(err.message || "Google authentication failed");
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const handleGoogleSignIn = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    const redirectUri = `${window.location.origin}/auth/google/callback`;
    const authUrl = await getGoogleAuthUrl(redirectUri);

    if (authUrl) {
      const popup = window.open(
        authUrl,
        "google-signin",
        "width=600,height=700"
      );

      if (!popup) {
        setError("Popup blocked. Please allow popups and try again.");
        setIsLoading(false);
      }
    } else {
      setError("Could not get Google authentication URL.");
      setIsLoading(false);
    }
  }, [getGoogleAuthUrl]);

  return {
    getGoogleAuthUrl,
    handleGoogleCallback,
    handleGoogleSignIn,
    isLoading,
    error,
  };
};
