"use client";

import {
  useState,
  useEffect,
  useCallback,
  createContext,
  useContext,
  useMemo,
} from "react";
import { verifyWpConnection, fetchWpConnections } from "@/lib/apiClient";
import { WpConnectionDetails } from "@/types";
import { getSession } from "next-auth/react";

interface WpConnectionContextType {
  connection: WpConnectionDetails | null;
  isConnected: boolean;
  isLoading: boolean; // Loading state for checking storage/verification
  error: string | null;
  connect: (details: WpConnectionDetails) => Promise<boolean>;
  disconnect: () => void;
}

const WpConnectionContext = createContext<WpConnectionContextType | undefined>(
  undefined
);

const STORAGE_KEY = "wpConnection";
const LAST_CONNECTION_ID_KEY = "lastWpConnectionId";

export const WpConnectionProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [connection, setConnection] = useState<WpConnectionDetails | null>(
    null
  );
  const [isLoading, setIsLoading] = useState<boolean>(true); // Start loading
  const [error, setError] = useState<string | null>(null);
  const [initialized, setInitialized] = useState<boolean>(false);

  // Load connection from backend first, then fallback to localStorage
  useEffect(() => {
    let isMounted = true;
    if (initialized) return;

    const loadConnection = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Check if user is authenticated
        const session = await getSession();
        const isAuthenticated = !!session?.accessToken;

        // Try to get the last used connection ID from localStorage
        const lastConnectionId = localStorage.getItem(LAST_CONNECTION_ID_KEY);

        if (isAuthenticated) {
          // Try to get connections from backend first
          const backendConnections = await fetchWpConnections();

          if (
            Array.isArray(backendConnections) &&
            backendConnections.length > 0
          ) {
            // Try to find the last used connection first (if we saved the ID)
            let activeBackendConn = null;

            if (lastConnectionId) {
              activeBackendConn = backendConnections.find(
                (conn) => conn.id === parseInt(lastConnectionId, 10)
              );
            }

            // If no last connection found, try to find an active one
            if (!activeBackendConn) {
              activeBackendConn = backendConnections.find(
                (conn) => conn.is_active
              );
            }

            // If still no connection, just use the first one
            if (!activeBackendConn && backendConnections.length > 0) {
              activeBackendConn = backendConnections[0];
            }

            if (activeBackendConn) {
              // Map to expected format
              const mappedConnection = {
                baseUrl: activeBackendConn.site_url,
                apiKey: activeBackendConn.api_key,
                id: activeBackendConn.id,
                isActive: activeBackendConn.is_active,
              };

              // Verify it works

              const isValid = await verifyWpConnection(mappedConnection);

              if (isValid && isMounted) {
                setConnection(mappedConnection);

                // Save the connection ID for future use
                localStorage.setItem(
                  LAST_CONNECTION_ID_KEY,
                  String(mappedConnection.id)
                );

                // Also update localStorage for backup
                localStorage.setItem(
                  STORAGE_KEY,
                  JSON.stringify(mappedConnection)
                );
                setIsLoading(false);
                setInitialized(true);
                return; // Stop here if we have a valid backend connection
              } else {
                console.warn("Connection verification failed");
              }
            } else {
              console.warn("No connections found in backend");
            }
          } else {
            console.warn("No connections returned from backend");
          }
        } else {
          console.warn("User not authenticated, can't fetch from backend");
        }

        // Fallback: try localStorage if backend failed or had no active connections

        const storedConnectionJson = localStorage.getItem(STORAGE_KEY);
        if (storedConnectionJson) {
          try {
            const storedConnection = JSON.parse(
              storedConnectionJson
            ) as WpConnectionDetails;

            if (storedConnection.baseUrl && storedConnection.apiKey) {
              // Verify the stored connection silently
              const isValid = await verifyWpConnection(storedConnection);

              if (isMounted) {
                if (isValid) {
                  setConnection(storedConnection);

                  // If connection has ID, save it
                  if (storedConnection.id) {
                    localStorage.setItem(
                      LAST_CONNECTION_ID_KEY,
                      String(storedConnection.id)
                    );
                  }

                  setIsLoading(false);
                  setInitialized(true);
                } else {
                  console.warn("Stored connection verification failed");
                  localStorage.removeItem(STORAGE_KEY);
                  setError(
                    "Stored connection details are invalid. Please reconnect."
                  );
                  setIsLoading(false);
                  setInitialized(true);
                }
              }
            } else {
              console.warn(
                "Invalid stored connection (missing URL or API key)"
              );
              // Invalid data structure
              localStorage.removeItem(STORAGE_KEY);
              if (isMounted) {
                setIsLoading(false);
                setInitialized(true);
              }
            }
          } catch (e) {
            console.error("Failed to parse connection from storage:", e);
            localStorage.removeItem(STORAGE_KEY); // Clear corrupt data
            if (isMounted) {
              setIsLoading(false);
              setInitialized(true);
            }
          }
        } else {
          // No stored connection, just set loading to false
          if (isMounted) {
            setIsLoading(false);
            setInitialized(true);
          }
        }
      } catch (error) {
        console.error("Error loading connection:", error);
        if (isMounted) {
          setError("Failed to load connection");
          setIsLoading(false);
          setInitialized(true);
        }
      }
    };

    loadConnection();

    return () => {
      isMounted = false;
    }; // Cleanup on unmount
  }, [initialized]); // Only run when initialized state changes

  const connect = useCallback(
    async (details: WpConnectionDetails): Promise<boolean> => {
      setIsLoading(true);
      setError(null);
      setConnection(null); // Clear previous connection attempt

      try {
        // Normalize URL slightly
        const normalizedDetails = {
          ...details,
          baseUrl: details.baseUrl.trim().replace(/\/+$/, ""), // Remove trailing slashes
        };

        const isValid = await verifyWpConnection(normalizedDetails);
        if (isValid) {
          setConnection(normalizedDetails);

          // If the connection has an ID, save it
          if (normalizedDetails.id) {
            localStorage.setItem(
              LAST_CONNECTION_ID_KEY,
              String(normalizedDetails.id)
            );
          }

          // Also update localStorage
          localStorage.setItem(STORAGE_KEY, JSON.stringify(normalizedDetails));

          setIsLoading(false);
          return true;
        } else {
          setError(
            "Connection failed. Please check WordPress URL, API Key, and ensure the plugin is active."
          );
          setIsLoading(false);
          return false;
        }
      } catch (err) {
        setError(
          `Connection error: ${
            err instanceof Error ? err.message : "Unknown error"
          }`
        );
        setIsLoading(false);
        return false;
      }
    },
    []
  );

  const disconnect = useCallback(() => {
    setConnection(null);
    setError(null);
    localStorage.removeItem(STORAGE_KEY);
    localStorage.removeItem(LAST_CONNECTION_ID_KEY);
    // Optionally clear React Query cache related to the disconnected site
  }, []);

  const isConnected = useMemo(
    () => !!connection && !isLoading,
    [connection, isLoading]
  );

  const value = useMemo(
    () => ({
      connection,
      isConnected,
      isLoading,
      error,
      connect,
      disconnect,
    }),
    [connection, isConnected, isLoading, error, connect, disconnect]
  );

  return (
    <WpConnectionContext.Provider value={value}>
      {children}
    </WpConnectionContext.Provider>
  );
};

export const useWpConnection = (): WpConnectionContextType => {
  const context = useContext(WpConnectionContext);
  if (context === undefined) {
    throw new Error(
      "useWpConnection must be used within a WpConnectionProvider"
    );
  }
  return context;
};
