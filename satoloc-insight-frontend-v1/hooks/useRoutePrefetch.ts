import { useCallback, useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { prefetchData } from "@/lib/optimizedApiClient";

// Define route-to-API mappings - using endpoints that actually exist
const ROUTE_API_MAPPINGS: Record<string, string[]> = {
  "/dashboard": ["/scraping/projects/"],
  "/seo-insights": ["/advance-seo/websites/my_analyses/"],
  "/content": ["/wp-content-sync/connections/"],
  "/custom-content": ["/custom-content/"],
  "/admin-dashboard": ["/advance-seo/websites/my_analyses/"],
  "/user-settings": ["/get-user-data/"],
  "/insights": ["/advance-seo/websites/my_analyses/"],
};

export const useRoutePrefetch = () => {
  const queryClient = useQueryClient();

  // Prefetch data for a specific route
  const prefetchRoute = useCallback(async (route: string) => {
    const apiEndpoints = ROUTE_API_MAPPINGS[route];

    if (!apiEndpoints || apiEndpoints.length === 0) {
      return;
    }

    try {
      // Prefetch API data
      await prefetchData(apiEndpoints);

      // Also prefetch the route component (Next.js will handle this internally)
      if (typeof window !== "undefined" && "requestIdleCallback" in window) {
        window.requestIdleCallback(() => {
          // This triggers Next.js prefetching
          const link = document.createElement("link");
          link.rel = "prefetch";
          link.href = route;
          document.head.appendChild(link);

          // Remove after a short delay to clean up
          setTimeout(() => {
            if (link.parentNode) {
              link.parentNode.removeChild(link);
            }
          }, 5000);
        });
      }
    } catch (error) {
      // Silently handle prefetch errors
      console.debug(`Prefetch failed for route ${route}:`, error);
    }
  }, []);

  // Prefetch multiple routes with priority
  const prefetchRoutes = useCallback(
    async (routes: string[], priority: "high" | "low" = "low") => {
      if (priority === "high") {
        // Prefetch immediately for high priority
        await Promise.allSettled(routes.map((route) => prefetchRoute(route)));
      } else {
        // Use requestIdleCallback for low priority prefetching
        if (typeof window !== "undefined" && "requestIdleCallback" in window) {
          window.requestIdleCallback(() => {
            routes.forEach((route) => prefetchRoute(route));
          });
        } else {
          // Fallback for browsers without requestIdleCallback
          setTimeout(() => {
            routes.forEach((route) => prefetchRoute(route));
          }, 100);
        }
      }
    },
    [prefetchRoute]
  );

  // Auto-prefetch common routes when component mounts
  useEffect(() => {
    const commonRoutes = ["/dashboard", "/seo-insights", "/content"];
    prefetchRoutes(commonRoutes, "low");
  }, [prefetchRoutes]);

  return {
    prefetchRoute,
    prefetchRoutes,
  };
};

// Hook for sidebar hover prefetching
export const useSidebarPrefetch = () => {
  const { prefetchRoute } = useRoutePrefetch();

  const handleHover = useCallback(
    (route: string) => {
      // Debounce hover events
      const timeoutId = setTimeout(() => {
        prefetchRoute(route);
      }, 300); // Wait 300ms before prefetching

      return () => clearTimeout(timeoutId);
    },
    [prefetchRoute]
  );

  return { handleHover };
};
