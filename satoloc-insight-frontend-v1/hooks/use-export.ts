/**
 * useExport Hook
 * Provides reusable export functionality with state management
 */

import { useState, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { 
  ExportContent, 
  ExportConfig, 
  ExportFormat 
} from '@/lib/exportUtils';
import { 
  exportContent, 
  previewContent, 
  copyContentToClipboard,
  batchExport 
} from '@/lib/exportServices';

interface UseExportOptions {
  onExportSuccess?: (format: ExportFormat) => void;
  onExportError?: (error: Error, format: ExportFormat) => void;
  onPreviewSuccess?: (url: string, format: ExportFormat) => void;
  onPreviewError?: (error: Error, format: ExportFormat) => void;
}

interface UseExportReturn {
  // State
  isExporting: boolean;
  isPreviewing: boolean;
  isCopying: boolean;
  currentFormat: ExportFormat | null;
  lastExportedFormat: ExportFormat | null;
  
  // Actions
  exportSingle: (content: ExportContent, config: ExportConfig) => Promise<void>;
  exportMultiple: (content: ExportContent, formats: ExportFormat[], baseConfig?: Omit<ExportConfig, 'format'>) => Promise<{ success: ExportFormat[]; failed: { format: ExportFormat; error: string }[] }>;
  preview: (content: ExportContent, config: ExportConfig) => Promise<string | null>;
  copyToClipboard: (content: ExportContent, format?: ExportFormat) => Promise<boolean>;
  
  // Quick export methods
  exportAsPDF: (content: ExportContent, config?: Omit<ExportConfig, 'format'>) => Promise<void>;
  exportAsHTML: (content: ExportContent, config?: Omit<ExportConfig, 'format'>) => Promise<void>;
  exportAsMarkdown: (content: ExportContent, config?: Omit<ExportConfig, 'format'>) => Promise<void>;
  exportAsText: (content: ExportContent, config?: Omit<ExportConfig, 'format'>) => Promise<void>;
  exportAsJSON: (content: ExportContent, config?: Omit<ExportConfig, 'format'>) => Promise<void>;
}

export function useExport(options: UseExportOptions = {}): UseExportReturn {
  const [isExporting, setIsExporting] = useState(false);
  const [isPreviewing, setIsPreviewing] = useState(false);
  const [isCopying, setIsCopying] = useState(false);
  const [currentFormat, setCurrentFormat] = useState<ExportFormat | null>(null);
  const [lastExportedFormat, setLastExportedFormat] = useState<ExportFormat | null>(null);
  
  const { toast } = useToast();
  
  const {
    onExportSuccess,
    onExportError,
    onPreviewSuccess,
    onPreviewError,
  } = options;

  const exportSingle = useCallback(async (
    content: ExportContent,
    config: ExportConfig
  ): Promise<void> => {
    if (!content.content.trim()) {
      const error = new Error('No content to export. Please generate or add content first.');
      toast({
        title: "Export Failed",
        description: error.message,
        variant: "destructive",
      });
      onExportError?.(error, config.format);
      return;
    }

    setIsExporting(true);
    setCurrentFormat(config.format);
    
    try {
      await exportContent(content, config);
      
      setLastExportedFormat(config.format);
      
      toast({
        title: "Export Successful",
        description: `Content exported as ${config.format.toUpperCase()} successfully.`,
        variant: "default",
      });
      
      onExportSuccess?.(config.format);
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Export failed');
      console.error('Export failed:', err);
      
      toast({
        title: "Export Failed",
        description: err.message,
        variant: "destructive",
      });
      
      onExportError?.(err, config.format);
    } finally {
      setIsExporting(false);
      setCurrentFormat(null);
    }
  }, [toast, onExportSuccess, onExportError]);

  const exportMultiple = useCallback(async (
    content: ExportContent,
    formats: ExportFormat[],
    baseConfig: Omit<ExportConfig, 'format'> = {}
  ) => {
    if (!content.content.trim()) {
      const error = new Error('No content to export. Please generate or add content first.');
      toast({
        title: "Batch Export Failed",
        description: error.message,
        variant: "destructive",
      });
      return { success: [], failed: formats.map(format => ({ format, error: error.message })) };
    }

    setIsExporting(true);
    
    try {
      const result = await batchExport(content, formats, baseConfig);
      
      if (result.success.length > 0) {
        toast({
          title: "Batch Export Completed",
          description: `Successfully exported ${result.success.length} format(s). ${result.failed.length > 0 ? `${result.failed.length} failed.` : ''}`,
          variant: result.failed.length === 0 ? "default" : "destructive",
        });
      } else {
        toast({
          title: "Batch Export Failed",
          description: "All exports failed. Please check your content and try again.",
          variant: "destructive",
        });
      }
      
      return result;
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Batch export failed');
      console.error('Batch export failed:', err);
      
      toast({
        title: "Batch Export Failed",
        description: err.message,
        variant: "destructive",
      });
      
      return { 
        success: [], 
        failed: formats.map(format => ({ format, error: err.message })) 
      };
    } finally {
      setIsExporting(false);
    }
  }, [toast]);

  const preview = useCallback(async (
    content: ExportContent,
    config: ExportConfig
  ): Promise<string | null> => {
    if (!content.content.trim()) {
      const error = new Error('No content to preview.');
      toast({
        title: "Preview Failed",
        description: error.message,
        variant: "destructive",
      });
      onPreviewError?.(error, config.format);
      return null;
    }

    setIsPreviewing(true);
    setCurrentFormat(config.format);
    
    try {
      const url = await previewContent(content, config);
      
      if (url) {
        onPreviewSuccess?.(url, config.format);
        return url;
      } else {
        toast({
          title: "Preview Not Available",
          description: `Preview is not supported for ${config.format.toUpperCase()} format.`,
          variant: "default",
        });
        return null;
      }
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Preview failed');
      console.error('Preview failed:', err);
      
      toast({
        title: "Preview Failed",
        description: err.message,
        variant: "destructive",
      });
      
      onPreviewError?.(err, config.format);
      return null;
    } finally {
      setIsPreviewing(false);
      setCurrentFormat(null);
    }
  }, [toast, onPreviewSuccess, onPreviewError]);

  const copyToClipboard = useCallback(async (
    content: ExportContent,
    format: ExportFormat = 'txt'
  ): Promise<boolean> => {
    setIsCopying(true);
    
    try {
      const success = await copyContentToClipboard(content, format);
      
      if (success) {
        toast({
          title: "Copied to Clipboard",
          description: "Content copied to clipboard successfully.",
          variant: "default",
        });
      } else {
        throw new Error('Failed to copy to clipboard');
      }
      
      return success;
    } catch (error) {
      console.error('Copy to clipboard failed:', error);
      toast({
        title: "Copy Failed",
        description: "Failed to copy content to clipboard.",
        variant: "destructive",
      });
      return false;
    } finally {
      setIsCopying(false);
    }
  }, [toast]);

  // Quick export methods
  const exportAsPDF = useCallback(async (
    content: ExportContent,
    config: Omit<ExportConfig, 'format'> = {}
  ) => {
    await exportSingle(content, { ...config, format: 'pdf' });
  }, [exportSingle]);

  const exportAsHTML = useCallback(async (
    content: ExportContent,
    config: Omit<ExportConfig, 'format'> = {}
  ) => {
    await exportSingle(content, { ...config, format: 'html' });
  }, [exportSingle]);

  const exportAsMarkdown = useCallback(async (
    content: ExportContent,
    config: Omit<ExportConfig, 'format'> = {}
  ) => {
    await exportSingle(content, { ...config, format: 'markdown' });
  }, [exportSingle]);

  const exportAsText = useCallback(async (
    content: ExportContent,
    config: Omit<ExportConfig, 'format'> = {}
  ) => {
    await exportSingle(content, { ...config, format: 'txt' });
  }, [exportSingle]);

  const exportAsJSON = useCallback(async (
    content: ExportContent,
    config: Omit<ExportConfig, 'format'> = {}
  ) => {
    await exportSingle(content, { ...config, format: 'json' });
  }, [exportSingle]);

  return {
    // State
    isExporting,
    isPreviewing,
    isCopying,
    currentFormat,
    lastExportedFormat,
    
    // Actions
    exportSingle,
    exportMultiple,
    preview,
    copyToClipboard,
    
    // Quick export methods
    exportAsPDF,
    exportAsHTML,
    exportAsMarkdown,
    exportAsText,
    exportAsJSON,
  };
}
