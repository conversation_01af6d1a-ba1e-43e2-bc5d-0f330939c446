{"name": "satoloc-insight-frontend-v1", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.1", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.11.0", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.9.1", "@mui/icons-material": "^5.14.18", "@mui/material": "^5.14.18", "@mui/system": "^5.14.18", "@mui/x-date-pickers": "^7.11.1", "@playwright/test": "^1.48.1", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.2.7", "@react-pdf/renderer": "^4.3.0", "@stripe/stripe-js": "^7.3.1", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^4.19.1", "@tanstack/react-query-devtools": "^4.19.1", "@tiptap/extension-color": "^3.0.1", "@tiptap/extension-font-family": "^3.0.1", "@tiptap/extension-highlight": "^3.0.1", "@tiptap/extension-image": "^3.0.1", "@tiptap/extension-link": "^3.0.1", "@tiptap/extension-placeholder": "^3.0.1", "@tiptap/extension-text-align": "^3.0.1", "@tiptap/extension-text-style": "^3.0.1", "@tiptap/extension-underline": "^3.0.1", "@tiptap/pm": "^3.0.1", "@tiptap/react": "^3.0.1", "@tiptap/starter-kit": "^3.0.1", "@types/jspdf": "^1.3.3", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-resizable": "^3.0.8", "@wordpress/api-fetch": "^7.24.0", "ace-builds": "^1.43.1", "axios": "^1.6.2", "axios-extensions": "^3.1.6", "axios-retry": "^4.5.0", "chart.js": "^4.4.3", "chartjs-plugin-datalabels": "^2.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "dayjs": "^1.11.12", "file-saver": "^2.0.5", "framer-motion": "^11.11.9", "html-parser": "^0.11.0", "html-react-parser": "^5.0.6", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.2", "http-proxy-middleware": "^3.0.0", "isomorphic-dompurify": "^2.25.0", "js-cookie": "^3.0.5", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "lru-cache": "^11.0.1", "lucide-react": "^0.453.0", "next": "14.2.14", "next-auth": "^4.24.7", "next-themes": "^0.3.0", "nodemailer": "^6.9.16", "openai": "^4.53.2", "pdfmake": "^0.2.18", "prettier": "^3.1.0", "react": "^18", "react-ace": "^14.0.1", "react-chartjs-2": "^5.2.0", "react-datepicker": "^7.3.0", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-gauge-chart": "^0.5.1", "react-hook-form": "^7.53.2", "react-icons": "^4.12.0", "react-intersection-observer": "^9.13.1", "react-markdown": "^10.1.0", "react-minimal-pie-chart": "^8.4.0", "react-query": "^3.39.3", "react-resizable": "^3.0.5", "react-resizable-panels": "^3.0.3", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.4", "sanitize-html": "^2.13.1", "sharp": "^0.33.4", "sonner": "^1.7.2", "stripe": "^18.2.1", "swr": "^2.2.5", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "turndown": "^7.2.0", "vaul": "^1.1.2", "wordpress-rest-api-oauth-1": "^1.1.7", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/pdfmake": "^0.2.11", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-gauge-chart": "^0.4.3", "@types/sanitize-html": "^2.13.0", "@types/turndown": "^5.0.5", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "typescript": "^5"}}