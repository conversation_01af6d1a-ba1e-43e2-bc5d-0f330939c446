// types/index.ts

// Matches the updated Django model
export interface SyncedContent {
  id: number; // Local Django DB ID
  wp_site_url: string; // Source WP site URL
  wp_id: number; // WordPress Post ID
  title: string;
  content: string; // Raw HTML content
  post_type: "post" | "page" | string;
  status: "publish" | "draft" | "pending" | "private" | "future" | string;
  date_created_gmt: string | null; // ISO date string
  date_modified_gmt: string | null; // ISO date string
  wp_link: string | null;
  featured_image_url: string | null;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
  last_synced_at: string | null; // ISO date string

  // --- Added Language Fields ---
  language_code?: string | null; // e.g., 'en', 'fr'
  language_name?: string | null; // e.g., 'English', 'Français'
  translations?: Record<string, number> | null; // e.g., { en: 123, fr: 456 }
  categories?: WpCategory[] | null; // Array of categories
  // --- End Added Language Fields ---
}

export interface WpCategory {
  id: number;
  name: string;
  slug: string;
  parent: number | null;
  count: number;
}

// For storing connection details client-side
export interface WpConnectionDetails {
  baseUrl: string; // The base URL of the WP site (e.g., https://example.com)
  apiKey: string;
  id?: number; // ID in the backend database (if saved)
  isActive?: boolean; // Whether the connection is active
}

// For API responses from Django actions
export interface SyncApiResponse {
  message: string;
  target_site?: string;
  total_fetched_from_wp?: number;
  created_locally?: number;
  updated_locally?: number;
  removed_locally?: number;
  skipped?: number;
  local_id_deleted?: number;
  wp_response?: any; // Response from WP delete action
  error?: string;
  wp_error?: string; // Specific WP error message
}

// For data passed to WP update endpoint
export interface WpUpdatePayload {
  title: string;
  content: string;
  status: "publish" | "draft" | "pending"; // Match allowed statuses
}
