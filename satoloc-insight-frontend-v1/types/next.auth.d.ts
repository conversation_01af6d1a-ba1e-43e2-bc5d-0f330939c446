// types/next-auth.d.ts
import { DefaultSession } from "next-auth";

declare module "next-auth" {
  interface Session {
    accessToken?: string;
    refreshToken?: string;
    user: {
      id: string;
      role?: string;
      isAdmin?: boolean;
      subscription_plan?: string;
      subscription_price?: string;
      subscription_start_date?: string;
    } & DefaultSession["user"];
  }

  interface User {
    id: string;
    role?: string;
    isAdmin?: boolean;
    accessToken?: string;
    refreshToken?: string;
    subscription_plan?: string;
    subscription_price?: string;
    subscription_start_date?: string;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    accessToken?: string;
    refreshToken?: string;
    role?: string;
    isAdmin?: boolean;
    subscription_plan?: string;
    subscription_price?: string;
    subscription_start_date?: string;
  }
}
