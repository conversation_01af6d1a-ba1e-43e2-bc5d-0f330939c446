export interface WordPressContent {
  id: number;
  title: string;
  content: string;
  translated_title?: string;
  translated_content?: string;
  status: string;
  created_at: string;
  updated_at: string;
}

// Wordpress connection details
export interface WpConnectionDetails {
  baseUrl: string;
  apiKey: string;
  id?: number;
  isActive?: boolean;
  _silentMode?: boolean; // Optional flag for silent mode during reconnection
}
