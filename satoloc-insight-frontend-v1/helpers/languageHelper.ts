/**
 * Language Helper Functions
 * Provides language code mapping and utilities for content management
 */

// Common language name to ISO code mapping
const LANGUAGE_CODE_MAP: Record<string, string> = {
  // English variants
  'english': 'en',
  'anglais': 'en',
  
  // Spanish variants
  'spanish': 'es',
  'español': 'es',
  'espanol': 'es',
  'castellano': 'es',
  
  // French variants
  'french': 'fr',
  'français': 'fr',
  'francais': 'fr',
  
  // Portuguese variants
  'portuguese': 'pt',
  'português': 'pt',
  'portugues': 'pt',
  
  // German variants
  'german': 'de',
  'deutsch': 'de',
  'allemand': 'de',
  
  // Italian variants
  'italian': 'it',
  'italiano': 'it',
  'italien': 'it',
  
  // Dutch variants
  'dutch': 'nl',
  'nederlands': 'nl',
  'néerlandais': 'nl',
  
  // Turkish variants
  'turkish': 'tr',
  'türkçe': 'tr',
  'turkce': 'tr',
  
  // Chinese variants
  'chinese': 'zh',
  'chinese (simplified)': 'zh-cn',
  'chinese (traditional)': 'zh-tw',
  '中文': 'zh',
  '中文 (中国)': 'zh-cn',
  '中文 (台灣)': 'zh-tw',
  'mandarin': 'zh',
  
  // Japanese variants
  'japanese': 'ja',
  '日本語': 'ja',
  'nihongo': 'ja',
  
  // Korean variants
  'korean': 'ko',
  '한국어': 'ko',
  'hangul': 'ko',
  
  // Arabic variants
  'arabic': 'ar',
  'العربية': 'ar',
  'arabe': 'ar',
  
  // Russian variants
  'russian': 'ru',
  'русский': 'ru',
  'russe': 'ru',
  
  // Hindi variants
  'hindi': 'hi',
  'हिन्दी': 'hi',
  
  // Other common languages
  'swedish': 'sv',
  'norwegian': 'no',
  'danish': 'da',
  'finnish': 'fi',
  'polish': 'pl',
  'czech': 'cs',
  'hungarian': 'hu',
  'romanian': 'ro',
  'bulgarian': 'bg',
  'greek': 'el',
  'hebrew': 'he',
  'thai': 'th',
  'vietnamese': 'vi',
  'indonesian': 'id',
  'malay': 'ms',
  'tagalog': 'tl',
  'swahili': 'sw',
  'afrikaans': 'af',
  'catalan': 'ca',
  'basque': 'eu',
  'galician': 'gl',
  'ukrainian': 'uk',
  'croatian': 'hr',
  'serbian': 'sr',
  'slovenian': 'sl',
  'slovak': 'sk',
  'lithuanian': 'lt',
  'latvian': 'lv',
  'estonian': 'et',
  'maltese': 'mt',
  'irish': 'ga',
  'welsh': 'cy',
  'icelandic': 'is',
};

/**
 * Get language code from language name
 * @param languageName - The language name (case insensitive)
 * @returns The ISO language code or 'unknown' if not found
 */
export function getLanguageCode(languageName: string): string {
  if (!languageName || typeof languageName !== 'string') {
    return 'unknown';
  }
  
  // Normalize the language name: lowercase and trim
  const normalizedName = languageName.toLowerCase().trim();
  
  // Direct lookup
  if (LANGUAGE_CODE_MAP[normalizedName]) {
    return LANGUAGE_CODE_MAP[normalizedName];
  }
  
  // Try to find partial matches (for cases like "English (US)")
  for (const [key, code] of Object.entries(LANGUAGE_CODE_MAP)) {
    if (normalizedName.includes(key) || key.includes(normalizedName)) {
      return code;
    }
  }
  
  // If no match found, return 'unknown'
  return 'unknown';
}

/**
 * Get language name from language code
 * @param languageCode - The ISO language code
 * @returns The language name or the original code if not found
 */
export function getLanguageName(languageCode: string): string {
  if (!languageCode || typeof languageCode !== 'string') {
    return 'Unknown';
  }
  
  const codeToNameMap: Record<string, string> = {
    'en': 'English',
    'es': 'Spanish',
    'fr': 'French',
    'pt': 'Portuguese',
    'de': 'German',
    'it': 'Italian',
    'nl': 'Dutch',
    'tr': 'Turkish',
    'zh': 'Chinese',
    'zh-cn': 'Chinese (Simplified)',
    'zh-tw': 'Chinese (Traditional)',
    'ja': 'Japanese',
    'ko': 'Korean',
    'ar': 'Arabic',
    'ru': 'Russian',
    'hi': 'Hindi',
    'sv': 'Swedish',
    'no': 'Norwegian',
    'da': 'Danish',
    'fi': 'Finnish',
    'pl': 'Polish',
    'cs': 'Czech',
    'hu': 'Hungarian',
    'ro': 'Romanian',
    'bg': 'Bulgarian',
    'el': 'Greek',
    'he': 'Hebrew',
    'th': 'Thai',
    'vi': 'Vietnamese',
    'id': 'Indonesian',
    'ms': 'Malay',
    'tl': 'Tagalog',
    'sw': 'Swahili',
    'af': 'Afrikaans',
    'ca': 'Catalan',
    'eu': 'Basque',
    'gl': 'Galician',
    'uk': 'Ukrainian',
    'hr': 'Croatian',
    'sr': 'Serbian',
    'sl': 'Slovenian',
    'sk': 'Slovak',
    'lt': 'Lithuanian',
    'lv': 'Latvian',
    'et': 'Estonian',
    'mt': 'Maltese',
    'ga': 'Irish',
    'cy': 'Welsh',
    'is': 'Icelandic',
  };
  
  const normalizedCode = languageCode.toLowerCase().trim();
  return codeToNameMap[normalizedCode] || languageCode;
}

/**
 * Format language display string with name and code
 * @param languageName - The language name
 * @param languageCode - The language code (optional)
 * @returns Formatted string like "English (en)" or "French (fr)"
 */
export function formatLanguageDisplay(languageName?: string, languageCode?: string): string {
  if (!languageName && !languageCode) {
    return 'Unknown (unknown)';
  }
  
  // If we have a name but no code, try to get the code
  let finalName = languageName || getLanguageName(languageCode || '');
  let finalCode = languageCode;
  
  // If code is missing or 'unknown', try to get it from the name
  if (!finalCode || finalCode === 'unknown') {
    finalCode = getLanguageCode(finalName);
  }
  
  // If name is missing, try to get it from the code
  if (!finalName || finalName === 'unknown') {
    finalName = getLanguageName(finalCode);
  }
  
  // Capitalize the first letter of the name
  finalName = finalName.charAt(0).toUpperCase() + finalName.slice(1);
  
  return `${finalName} (${finalCode})`;
}

/**
 * Check if a language code is valid (not 'unknown')
 * @param languageCode - The language code to check
 * @returns True if the code is valid, false otherwise
 */
export function isValidLanguageCode(languageCode: string): boolean {
  return Boolean(languageCode && languageCode !== 'unknown');
}

/**
 * Get all supported language codes
 * @returns Array of all supported language codes
 */
export function getSupportedLanguageCodes(): string[] {
  return Array.from(new Set(Object.values(LANGUAGE_CODE_MAP)));
}

/**
 * Get all supported language names
 * @returns Array of all supported language names
 */
export function getSupportedLanguageNames(): string[] {
  return Object.keys(LANGUAGE_CODE_MAP).map(name => 
    name.charAt(0).toUpperCase() + name.slice(1)
  );
}
