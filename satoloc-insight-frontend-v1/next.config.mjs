/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  trailingSlash: true,
  output: "standalone",

  images: {
    remotePatterns: [
      {
        protocol: "http",
        hostname: "localhost",
        port: "8080",
        pathname: "/wp-content/uploads/**",
      },
      {
        protocol: "https",
        hostname: "cms.satolocinsight.com",
        pathname: "/wp-content/uploads/**",
      },
    ],
  },

  async rewrites() {
    return [
      {
        source: "/api/image-proxy/:path*",
        destination: "https://oaidalleapiprodscus.blob.core.windows.net/:path*",
      },
    ];
  },
};

export default nextConfig;
