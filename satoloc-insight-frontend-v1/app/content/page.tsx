// src/app/content/page.tsx

"use client";

import React, { useState, useMemo, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getSession } from "next-auth/react";
import {
  fetchContentItems,
  syncAllFromWordPress,
  deleteContentFromWordPress,
  deleteContentLocally,
  verifyWpConnection,
  saveWpConnection,
  fetchWpConnections,
  updateWpConnectionLastSync,
  deactivateWpConnection,
} from "../../lib/apiClient";
import { useWpConnection } from "../../hooks/use-wp-connection";
import { useToast } from "../../hooks/use-toast";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";

import {
  AlertTriangle,
  RefreshCw,
  Loader2,
  Globe,
  Search,
  X,
} from "lucide-react";
import { SyncedContent } from "@/types";
import { ConnectForm } from "@/components/ConnectForm";
import { ConnectionFormData } from "@/lib/validators";
import PageWrapper from "@/components/PageWrapper";
import { ConfirmationModal } from "@/components/ConfirmationModal";
import ContentCard from "@/components/ContentComponents/ContentCard";
import ContentFilter from "@/components/ContentComponents/ContentFilter";
import { formatLanguageDisplay } from "@/helpers/languageHelper";

// Storage key for connection
const CONNECTION_STORAGE_KEY = "wpConnection";

export default function ContentListPage() {
  // ...existing hooks
  const [confirmModal, setConfirmModal] = useState<{
    isOpen: boolean;
    title: string;
    description: string;
    variant?: "default" | "destructive";
    onConfirm: () => void;
    onClose: () => void;
  } | null>(null);
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { connection, isConnected, connect, disconnect } = useWpConnection();
  const [isConnecting, setIsConnecting] = useState(false);

  // Simplified state for managing a single connection
  const [isLoadingConnection, setIsLoadingConnection] = useState(true);

  // Add state for sync options
  const [syncOptions, setSyncOptions] = useState({
    cleanup_missing: true,
    keep_wp_generated_translations: true,
  });

  // Add state for search functionality
  const [searchTerm, setSearchTerm] = useState("");

  // Add state for active tab
  const [activeTab, setActiveTab] = useState("posts");

  // Add state for language filtering
  const [selectedLanguages, setSelectedLanguages] = useState<Set<string>>(
    new Set()
  );
  const [showLanguageFilter, setShowLanguageFilter] = useState(false);

  // Load saved connection from backend on component mount
  useEffect(() => {
    const loadConnection = async () => {
      try {
        setIsLoadingConnection(true);
        const session = await getSession();
        const isAuthenticated = !!session?.accessToken;

        if (isAuthenticated) {
          try {
            const backendConnections = await fetchWpConnections();

            if (
              Array.isArray(backendConnections) &&
              backendConnections.length > 0
            ) {
              // Use the first connection (or active one if available)
              const activeConn =
                backendConnections.find((conn) => conn.is_active) ||
                backendConnections[0];
              const connectionDetails = {
                baseUrl: activeConn.site_url,
                apiKey: activeConn.api_key,
                id: activeConn.id,
                isActive: activeConn.is_active,
              };

              // Connect with this connection
              if (connectionDetails.baseUrl && connectionDetails.apiKey) {
                await connect(connectionDetails);
              }
            } else {
              disconnect();
            }
          } catch (err) {
            console.error("Error loading from backend:", err);
            toast({
              title: "Connection Load Failed",
              description: err instanceof Error ? err.message : "Unknown error",
              variant: "destructive",
            });
          }
        } else {
        }
      } catch (error) {
        console.error("Error:", error);
        toast({
          title: "Error",
          description:
            error instanceof Error ? error.message : "Unknown error occurred",
          variant: "destructive",
        });
      } finally {
        setIsLoadingConnection(false);
      }
    };

    loadConnection();
  }, [connect, disconnect, toast]);

  // Handle connection submission
  const handleConnect = async (formData: ConnectionFormData) => {
    try {
      setIsConnecting(true);

      const session = await getSession();
      const isAuthenticated = !!session?.accessToken;
      if (formData.connectionOnly) {
        await connect({
          baseUrl: formData.baseUrl,
          apiKey: formData.apiKey,
        });

        toast({
          title: "Connection Successful",
          description: `Connected to ${formData.baseUrl}`,
        });
        return;
      }

      // Verify connection before saving/connecting
      const isValid = await verifyWpConnection({
        baseUrl: formData.baseUrl,
        apiKey: formData.apiKey,
      });

      if (!isValid) {
        toast({
          title: "Connection Failed",
          description:
            "Could not connect to the WordPress site. Please check URL and API key.",
          variant: "destructive",
        });
        return;
      }

      // If authenticated, save to backend
      if (isAuthenticated) {
        try {
          const savedConnection = await saveWpConnection({
            baseUrl: formData.baseUrl,
            apiKey: formData.apiKey,
          });

          // Check if the connection already existed
          if (savedConnection.already_exists) {
            // Connect with the existing connection
            const connectionDetails = {
              baseUrl: formData.baseUrl,
              apiKey: formData.apiKey,
              id: savedConnection.id,
              isActive: true,
            };

            await connect(connectionDetails);

            toast({
              title: "Connection Successful",
              description: `Connected to existing site ${formData.baseUrl}`,
            });
            return;
          }

          if (savedConnection && savedConnection.id) {
            // Connect with the new connection
            const connectionDetails = {
              baseUrl: formData.baseUrl,
              apiKey: formData.apiKey,
              id: savedConnection.id,
              isActive: true,
            };

            await connect(connectionDetails);

            toast({
              title: "Connection Successful",
              description: `Connected to ${formData.baseUrl}`,
            });
          }
        } catch (error) {
          console.error("Error saving connection to backend:", error);

          // Check if this is a duplicate connection error
          const errorMessage =
            error instanceof Error ? error.message : String(error);
          if (
            errorMessage.includes("duplicate key") ||
            errorMessage.includes("already exists") ||
            errorMessage.includes("Validation error: site_url") ||
            errorMessage.includes("already connected")
          ) {
            // Try to connect anyway if it already exists
            try {
              // Get existing connection from backend
              const connections = await fetchWpConnections();
              const existingConnection = connections.find(
                (conn) => conn.site_url === formData.baseUrl
              );

              if (existingConnection) {
                await connect({
                  baseUrl: formData.baseUrl,
                  apiKey: formData.apiKey,
                  id: existingConnection.id,
                  isActive: existingConnection.is_active,
                });

                toast({
                  title: "Connected to Existing Site",
                  description: `Successfully connected to ${formData.baseUrl}`,
                });
                return;
              }
            } catch (connectError) {
              console.error(
                "Failed to connect to existing site:",
                connectError
              );
            }
          }

          // Rethrow the original error if we couldn't handle it
          throw error;
        }
      } else {
        // Not authenticated, just connect locally
        await connect({
          baseUrl: formData.baseUrl,
          apiKey: formData.apiKey,
        });

        toast({
          title: "Connection Successful",
          description: `Connected to ${formData.baseUrl}`,
        });
      }
    } catch (error) {
      console.error("Connection error:", error);
      toast({
        title: "Connection Failed",
        description:
          error instanceof Error ? error.message : "Unknown connection error",
        variant: "destructive",
      });

      // Rethrow for the ConnectForm component to handle
      throw error;
    } finally {
      setIsConnecting(false);
    }
  };

  // Handle disconnection
  const handleDisconnect = async () => {
    try {
      // If there's a connection with an ID, attempt to deactivate in backend
      if (connection?.id) {
        const session = await getSession();
        if (session?.accessToken) {
          try {
            // Use deactivate endpoint instead of delete
            await deactivateWpConnection(connection.id);
          } catch (error) {
            console.error("Error deactivating connection in backend:", error);
            toast({
              title: "Backend Deactivation Failed",
              description: `Could not deactivate connection: ${error instanceof Error ? error.message : "Unknown error"}`,
              variant: "destructive",
            });
          }
        }
      }

      // Disconnect locally
      disconnect();
      toast({
        title: "Disconnected",
        description: connection?.baseUrl
          ? `Disconnected from ${connection.baseUrl}`
          : "Disconnected",
      });
    } catch (error) {
      toast({
        title: "Disconnect Failed",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      });
    }
  };

  // Derive query key based on connection from context
  const siteUrl = connection?.baseUrl;
  const queryKey = useMemo(() => ["localContent", siteUrl], [siteUrl]);

  // Fetch local content from Django API, filtered by the connected site URL
  const {
    data: content = [],
    isLoading: isLoadingContent,
    error,
    refetch,
    isFetching, // Use isFetching for background refresh indicators
  } = useQuery<SyncedContent[], Error>({
    queryKey: queryKey,
    queryFn: () => {
      if (!siteUrl) {
        console.warn("No site URL, skipping fetch");
        return Promise.resolve([]);
      }
      return fetchContentItems(siteUrl);
    },
    enabled: !!siteUrl && isConnected,
    retry: 1,
    refetchOnWindowFocus: true,
  });

  // Effect to refetch or clear content when connection changes
  useEffect(() => {
    if (siteUrl && isConnected) {
      queryClient.invalidateQueries({ queryKey: queryKey });
    } else {
      queryClient.setQueryData(queryKey, []);
    }
  }, [siteUrl, isConnected, queryKey, queryClient]); // queryClient added

  // --- Mutations ---
  const syncMutation = useMutation({
    mutationFn: () => {
      if (!connection) throw new Error("Not connected");
      if (!connection.apiKey || connection.apiKey === "REDACTED_IN_STORAGE")
        throw new Error("API Key missing");
      const detailsToSync = {
        baseUrl: connection.baseUrl,
        apiKey: connection.apiKey,
        id: connection.id,
      };
      return syncAllFromWordPress(detailsToSync, syncOptions);
    },
    onSuccess: (data) => {
      const removedMsg = data.removed_locally
        ? `, Removed: ${data.removed_locally}`
        : "";
      toast({
        title: "Sync Complete",
        description: `Created: ${data.created_locally}, Updated: ${data.updated_locally}${removedMsg}, Skipped: ${data.skipped}.`,
        duration: 6000,
      });
      queryClient.invalidateQueries({ queryKey: queryKey });
      if (connection?.id)
        updateWpConnectionLastSync(connection.id).catch(console.error);
    },
    onError: (error: Error) =>
      toast({
        title: "Sync Failed",
        description: error.message,
        variant: "destructive",
      }),
  });

  const deleteWpMutation = useMutation({
    // Replace window.confirm with modal state
    mutationFn: async (localId: number) => {
      if (
        !connection ||
        !connection.apiKey ||
        connection.apiKey === "REDACTED_IN_STORAGE"
      )
        throw new Error("Connection/API Key missing");
      const confirmed = await new Promise<boolean>((resolve) => {
        setConfirmModal({
          isOpen: true,
          title: "Delete from WordPress & locally?",
          description: `Item ID: ${localId}\nSite: ${connection.baseUrl}\nCANNOT be undone.`,
          variant: "destructive",
          onConfirm: () => resolve(true),
          onClose: () => resolve(false),
        });
      });
      if (!confirmed) throw new Error("Deletion cancelled");
      return deleteContentFromWordPress(localId, connection.apiKey, false);
    },
    onSuccess: (data, localId) => {
      toast({
        title: "Deletion Successful",
        description: data.message || `Item ${localId} deleted.`,
      });
      queryClient.invalidateQueries({ queryKey: queryKey });
    },
    onError: (error: Error) => {
      if (error.message !== "Deletion cancelled")
        toast({
          title: "Deletion Failed",
          description: error.message,
          variant: "destructive",
        });
    },
  });

  const deleteLocalOnlyMutation = useMutation({
    // Replace window.confirm with modal state
    mutationFn: async (localId: number) => {
      const confirmed = await new Promise<boolean>((resolve) => {
        setConfirmModal({
          isOpen: true,
          title: "Delete locally only?",
          description: `Item ID: ${localId}\nWon't affect WordPress. Cannot be undone.`,
          variant: "destructive",
          onConfirm: () => resolve(true),
          onClose: () => resolve(false),
        });
      });
      if (!confirmed) throw new Error("Local deletion cancelled");
      return deleteContentLocally(localId);
    },
    onSuccess: (data, localId) => {
      toast({
        title: "Local Deletion Successful",
        description: data.message || `Item ${localId} deleted locally.`,
      });
      queryClient.invalidateQueries({ queryKey: queryKey });
    },
    onError: (error: Error) => {
      if (error.message !== "Local deletion cancelled")
        toast({
          title: "Local Deletion Failed",
          description: error.message,
          variant: "destructive",
        });
    },
  });

  const handleSync = () => {
    if (connection) syncMutation.mutate();
  };
  const handleDelete = (localId: number) => {
    if (connection) deleteWpMutation.mutate(localId);
  };
  const handleDeleteLocally = (localId: number) => {
    deleteLocalOnlyMutation.mutate(localId);
  };
  const toggleSyncOption = (option: keyof typeof syncOptions) =>
    setSyncOptions((prev) => ({ ...prev, [option]: !prev[option] }));

  // Language filter handlers
  const handleLanguageToggle = (languageKey: string) => {
    setSelectedLanguages((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(languageKey)) {
        newSet.delete(languageKey);
      } else {
        newSet.add(languageKey);
      }
      return newSet;
    });
  };

  const handleClearAllLanguages = () => {
    setSelectedLanguages(new Set());
  };

  // Helper functions to count posts and pages
  const getPostsCount = useMemo(() => {
    return content.filter((item) => item.post_type === "post").length;
  }, [content]);

  const getPagesCount = useMemo(() => {
    return content.filter((item) => item.post_type === "page").length;
  }, [content]);

  // --- Available Languages (for filter component) ---
  const availableLanguages = useMemo(() => {
    const languageCounts = new Map<string, number>();

    // Get all content for current tab (posts or pages)
    const tabContent = content.filter((item) => {
      return activeTab === "posts"
        ? item.post_type === "post"
        : item.post_type === "page";
    });

    tabContent.forEach((item) => {
      const langKey = formatLanguageDisplay(
        item.language_name || undefined,
        item.language_code || undefined
      );

      languageCounts.set(langKey, (languageCounts.get(langKey) || 0) + 1);
    });

    return languageCounts;
  }, [content, activeTab]);

  // --- Grouping Logic with Search Filtering, Post Type, and Language Filtering ---
  const contentByLanguage = useMemo(() => {
    const languages = new Map<string, SyncedContent[]>();
    const languageOrder: { [key: string]: number } = {
      English: 1,
      Spanish: 2,
      French: 3,
    }; // Example priority

    // Filter content based on search term, post type, and selected languages
    const filteredContent = content.filter((item) => {
      // Filter by post type
      const postTypeFilter =
        activeTab === "posts"
          ? item.post_type === "post"
          : item.post_type === "page";

      // Filter by search term
      const searchFilter =
        !searchTerm.trim() ||
        (item.title || "").toLowerCase().includes(searchTerm.toLowerCase());

      // Filter by selected languages (if any are selected)
      const langKey = formatLanguageDisplay(
        item.language_name || undefined,
        item.language_code || undefined
      );
      const languageFilter =
        selectedLanguages.size === 0 || selectedLanguages.has(langKey);

      return postTypeFilter && searchFilter && languageFilter;
    });

    filteredContent.forEach((item) => {
      const langKey = formatLanguageDisplay(
        item.language_name || undefined,
        item.language_code || undefined
      );

      if (!languages.has(langKey)) languages.set(langKey, []);
      languages.get(langKey)?.push(item);
    });

    // Sort items within each group by date descending
    languages.forEach((items) =>
      items.sort(
        (a, b) =>
          new Date(b.date_modified_gmt || 0).getTime() -
          new Date(a.date_modified_gmt || 0).getTime()
      )
    );

    // Sort language groups by custom order then alphabetically
    const sortedEntries = Array.from(languages.entries()).sort(
      ([keyA], [keyB]) => {
        const nameA = keyA.split(" ")[0];
        const nameB = keyB.split(" ")[0];
        const orderA = languageOrder[nameA] ?? 999;
        const orderB = languageOrder[nameB] ?? 999;
        return orderA !== orderB ? orderA - orderB : keyA.localeCompare(keyB);
      }
    );
    return new Map(sortedEntries);
  }, [content, searchTerm, activeTab, selectedLanguages]);

  // --- Render Card Logic (Using ContentCard Component) ---
  const renderContentCard = (item: SyncedContent) => {
    const isDeletingWp =
      deleteWpMutation.isLoading && deleteWpMutation.variables === item.id;
    const isDeletingLocal =
      deleteLocalOnlyMutation.isLoading &&
      deleteLocalOnlyMutation.variables === item.id;

    return (
      <ContentCard
        key={item.id}
        item={item}
        onDelete={handleDelete}
        onDeleteLocally={handleDeleteLocally}
        isDeletingWp={isDeletingWp}
        isDeletingLocal={isDeletingLocal}
      />
    );
  };

  return (
    <PageWrapper>
      <div className="flex flex-col gap-4">
        {/* Conditionally render ConnectForm if not connected */}
        {!isConnected && (
          <ConnectForm
            onSubmit={handleConnect}
            isLoading={isConnecting || isLoadingConnection}
          />
        )}

        {/* Content Card */}
        {isConnected && (
          <Card className="shadow-sm p-0 overflow-hidden">
            <CardHeader className="flex flex-col md:flex-row items-start md:items-center justify-between gap-2 py-3 px-4 border-b">
              {/* Left side: Title and Connection Info */}
              <div className="flex-grow">
                <div className="flex items-center gap-3 ">
                  <Globe className="h-5 w-5 text-[#1279b4] flex-shrink-0 " />
                  <CardTitle className="text-lg font-semibold text-foreground text-[#1279b4]">
                    Content Management
                  </CardTitle>
                </div>

                <div className="flex items-center gap-2 mt-1.5">
                  <span className="text-xs text-muted-foreground flex-shrink-0">
                    Connected to:
                  </span>
                  <div className="px-2 py-1 text-xs rounded-md bg-accent/50 text-accent-foreground truncate max-w-[250px] sm:max-w-[350px]">
                    {connection?.baseUrl.replace(/^https?:\/\//, "")}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-7 px-2 py-0 text-xs text-destructive border border-destructive"
                    onClick={handleDisconnect}
                  >
                    Disconnect
                  </Button>
                </div>
              </div>

              {/* Right side: Action Buttons */}
              <div className="flex gap-2 flex-wrap items-center mt-2 md:mt-0 flex-shrink-0">
                <Button
                  onClick={() => refetch()}
                  variant="outline"
                  size="sm"
                  className="h-8"
                  disabled={
                    isLoadingContent || isFetching || syncMutation.isLoading
                  }
                  title="Refresh content list for the selected site"
                >
                  <RefreshCw
                    className={`mr-1.5 h-3.5 w-3.5 ${
                      isFetching && !isLoadingContent ? "animate-spin" : ""
                    }`}
                  />
                  Refresh
                </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      className="h-8 bg-[#1279b4] text-white hover:bg-[#00497A]"
                      disabled={syncMutation.isLoading || isLoadingContent}
                      size="sm"
                      title="Sync content from the selected WordPress site"
                    >
                      {syncMutation.isLoading ? (
                        <Loader2 className="mr-1.5 h-3.5 w-3.5 animate-spin" />
                      ) : (
                        <RefreshCw className="mr-1.5 h-3.5 w-3.5" />
                      )}
                      {syncMutation.isLoading
                        ? "Syncing..."
                        : "Sync All from WP"}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <div className="p-2">
                      <div className="mb-2 text-sm font-medium">
                        Sync Options:
                      </div>
                      <div className="space-y-2">
                        <label className="flex items-center gap-2 text-sm cursor-pointer">
                          <input
                            type="checkbox"
                            checked={syncOptions.cleanup_missing}
                            onChange={() => toggleSyncOption("cleanup_missing")}
                            className="h-4 w-4 accent-primary"
                          />
                          Remove local posts missing from WP
                        </label>
                        <label className="flex items-center gap-2 text-sm cursor-pointer">
                          <input
                            type="checkbox"
                            checked={syncOptions.keep_wp_generated_translations}
                            onChange={() =>
                              toggleSyncOption("keep_wp_generated_translations")
                            }
                            className="h-4 w-4 accent-primary"
                          />
                          Keep locally saved translations
                        </label>
                      </div>
                      <Button
                        className="w-full mt-3 bg-[#1279b4]"
                        onClick={handleSync}
                        disabled={syncMutation.isLoading}
                        size="sm"
                      >
                        {syncMutation.isLoading ? (
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        ) : (
                          <RefreshCw className="mr-2 h-4 w-4" />
                        )}
                        {syncMutation.isLoading ? "Syncing..." : "Run Sync"}
                      </Button>
                    </div>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>

            {/* Search Section */}
            {isConnected && content.length > 0 && (
              <div className="px-4 py-3 border-b bg-muted/30">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <input
                    type="text"
                    placeholder="Search posts and pages by title..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 text-sm border border-input bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                  />
                  {searchTerm && (
                    <button
                      onClick={() => setSearchTerm("")}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground hover:text-foreground transition-colors"
                      title="Clear search"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  )}
                </div>
                {searchTerm && (
                  <div className="mt-2 text-xs text-muted-foreground">
                    {(() => {
                      const totalFiltered = Array.from(
                        contentByLanguage.values()
                      ).reduce((sum, items) => sum + items.length, 0);
                      return `Showing ${totalFiltered} of ${content.length} items matching "${searchTerm}"`;
                    })()}
                  </div>
                )}
              </div>
            )}

            <CardContent className="p-4">
              {isLoadingConnection ? (
                <div className="text-center p-6">
                  <Loader2 className="h-6 w-6 animate-spin text-primary inline-block" />
                  <p className="mt-2 text-muted-foreground">
                    Loading connection...
                  </p>
                </div>
              ) : !isConnected ? (
                <div className="text-center p-6 text-muted-foreground">
                  No connection available. Please add a connection above.
                </div>
              ) : isLoadingContent ? (
                <div className="text-center p-6">
                  <Loader2 className="h-6 w-6 animate-spin text-primary inline-block" />
                  <p className="mt-2 text-muted-foreground">
                    Loading content...
                  </p>
                </div>
              ) : error ? (
                <div className="text-destructive p-4 border border-destructive bg-destructive/10 rounded-md flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" /> Error loading content:{" "}
                  {error.message}
                </div>
              ) : content.length === 0 ? (
                <div className="text-center p-6 text-muted-foreground text-sm">
                  No content synced for this site yet. Try the Sync button.
                </div>
              ) : (
                // Tabs Layout for Posts and Pages
                <Tabs
                  value={activeTab}
                  onValueChange={setActiveTab}
                  className="w-full"
                >
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger
                      value="posts"
                      className="flex items-center gap-2"
                    >
                      Posts
                      {getPostsCount > 0 && (
                        <span className="text-xs bg-muted-foreground/20 px-1.5 py-0.5 rounded-full">
                          {getPostsCount}
                        </span>
                      )}
                    </TabsTrigger>
                    <TabsTrigger
                      value="pages"
                      className="flex items-center gap-2"
                    >
                      Pages
                      {getPagesCount > 0 && (
                        <span className="text-xs bg-muted-foreground/20 px-1.5 py-0.5 rounded-full">
                          {getPagesCount}
                        </span>
                      )}
                    </TabsTrigger>
                  </TabsList>

                  {/* Language Filter */}
                  {availableLanguages.size > 1 && (
                    <div className="mt-4">
                      <ContentFilter
                        availableLanguages={availableLanguages}
                        selectedLanguages={selectedLanguages}
                        onLanguageToggle={handleLanguageToggle}
                        onClearAll={handleClearAllLanguages}
                        totalItems={Array.from(
                          availableLanguages.values()
                        ).reduce((sum, count) => sum + count, 0)}
                        filteredItems={Array.from(
                          contentByLanguage.values()
                        ).reduce((sum, items) => sum + items.length, 0)}
                      />
                    </div>
                  )}

                  <TabsContent value="posts" className="mt-4">
                    {Array.from(contentByLanguage.values()).reduce(
                      (sum, items) => sum + items.length,
                      0
                    ) === 0 ? (
                      <div className="text-center p-6 text-muted-foreground text-sm">
                        {searchTerm ? (
                          <>
                            <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                            <p>No posts found matching "{searchTerm}"</p>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setSearchTerm("")}
                              className="mt-2 text-xs"
                            >
                              Clear search
                            </Button>
                          </>
                        ) : (
                          <p>No posts available.</p>
                        )}
                      </div>
                    ) : (
                      // Posts Grid Layout
                      <div
                        className={`grid gap-4 ${(() => {
                          const languageCount = contentByLanguage.size;
                          if (languageCount <= 1)
                            return "grid-cols-1 md:grid-cols-1 lg:grid-cols-1";
                          else if (languageCount === 2)
                            return "grid-cols-1 md:grid-cols-2 lg:grid-cols-2";
                          else if (languageCount === 3)
                            return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3";
                          else if (languageCount === 4)
                            return "grid-cols-1 md:grid-cols-2 lg:grid-cols-4";
                          else
                            return "grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5";
                        })()}`}
                      >
                        {Array.from(contentByLanguage.entries()).map(
                          ([languageKey, items]) => (
                            <div
                              key={languageKey}
                              className="flex flex-col border rounded-md"
                            >
                              <div className="font-medium text-[#1279b4] dark:text-foreground px-4 pt-2 flex items-center gap-2">
                                <Globe className="h-4 w-4 text-muted-foreground" />
                                {languageKey}{" "}
                                <span className="text-xs text-muted-foreground">
                                  ({items.length})
                                </span>
                              </div>
                              <ScrollArea className="h-[500px] w-full rounded-md p-4">
                                {items.length > 0 ? (
                                  <div className="space-y-4">
                                    {items.map((item) =>
                                      renderContentCard(item)
                                    )}
                                  </div>
                                ) : (
                                  <div className="text-center p-4 text-muted-foreground italic text-sm">
                                    No posts in this language
                                  </div>
                                )}
                              </ScrollArea>
                            </div>
                          )
                        )}
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="pages" className="mt-4">
                    {Array.from(contentByLanguage.values()).reduce(
                      (sum, items) => sum + items.length,
                      0
                    ) === 0 ? (
                      <div className="text-center p-6 text-muted-foreground text-sm">
                        {searchTerm ? (
                          <>
                            <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                            <p>No pages found matching "{searchTerm}"</p>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setSearchTerm("")}
                              className="mt-2 text-xs"
                            >
                              Clear search
                            </Button>
                          </>
                        ) : (
                          <p>No pages available.</p>
                        )}
                      </div>
                    ) : (
                      // Pages Grid Layout
                      <div
                        className={`grid gap-4 ${(() => {
                          const languageCount = contentByLanguage.size;
                          if (languageCount <= 1)
                            return "grid-cols-1 md:grid-cols-1 lg:grid-cols-1";
                          else if (languageCount === 2)
                            return "grid-cols-1 md:grid-cols-2 lg:grid-cols-2";
                          else if (languageCount === 3)
                            return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3";
                          else if (languageCount === 4)
                            return "grid-cols-1 md:grid-cols-2 lg:grid-cols-4";
                          else
                            return "grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5";
                        })()}`}
                      >
                        {Array.from(contentByLanguage.entries()).map(
                          ([languageKey, items]) => (
                            <div
                              key={languageKey}
                              className="flex flex-col border rounded-md"
                            >
                              <div className="font-medium text-sm text-[#1279b4] dark:text-foreground flex items-center gap-2 px-4 pt-2">
                                <Globe className="h-4 w-4 text-muted-foreground" />
                                {languageKey}{" "}
                                <span className="text-xs text-muted-foreground">
                                  ({items.length})
                                </span>
                              </div>
                              <ScrollArea className="h-[500px] w-full rounded-md p-4">
                                {items.length > 0 ? (
                                  <div className="space-y-4">
                                    {items.map((item) =>
                                      renderContentCard(item)
                                    )}
                                  </div>
                                ) : (
                                  <div className="text-center p-4 text-muted-foreground italic text-sm">
                                    No pages in this language
                                  </div>
                                )}
                              </ScrollArea>
                            </div>
                          )
                        )}
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              )}
            </CardContent>
          </Card>
        )}
      </div>
      {/* Confirmation Modal for destructive actions */}
      {confirmModal && (
        <ConfirmationModal
          isOpen={confirmModal.isOpen}
          title={confirmModal.title}
          description={confirmModal.description}
          variant={confirmModal.variant}
          onConfirm={() => {
            confirmModal.onConfirm();
            setConfirmModal(null);
          }}
          onClose={() => {
            confirmModal.onClose();
            setConfirmModal(null);
          }}
        />
      )}
    </PageWrapper>
  );
}
