"use client";

import React, { useState, useEffect, useMemo } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import {
  fetchContentItem,
  updateContentItem,
  pushContentToWordPress,
  getTranslationPreview,
  createTranslation,
  getMarkdownTranslationPreview,
  fetchWpCategoriesForConnection,
} from "@/lib/apiClient";
import {
  convertHtmlToMarkdown,
  convertMarkdownToHtml,
  cleanHtmlContent,
  cleanHtmlContentEnhanced,
  detectAvadaContent,
  reconstructAvadaContent,
} from "@/lib/markdownUtils";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft, Loader2, XCircle } from "lucide-react";
import { useWpConnection } from "@/hooks/use-wp-connection";
import { SyncedContent, WpCategory } from "@/types";
import { contentEditSchema, ContentEditFormData } from "@/lib/validators";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import PageWrapper from "@/components/PageWrapper";
import TranslationColumn from "@/components/ContentComponents/TranslationColumn";
import SourceContentColumn from "@/components/ContentComponents/SourceContentColumn";

export default function EditContentPage() {
  const params = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { connection, isConnected } = useWpConnection();

  const idParam = params?.id;
  //const idParam = params && params.id ? params.id : "0";
  const localId = Array.isArray(idParam)
    ? parseInt(idParam[0], 10)
    : parseInt(idParam || "0", 10);

  const queryKey = ["localContentItem", localId];

  // State for translation preview
  const [targetLanguage, setTargetLanguage] = useState<string>("");
  const [translatedTitle, setTranslatedTitle] = useState<string | null>(null);
  const [translatedContent, setTranslatedContent] = useState<string | null>(
    null
  );
  const [isPreviewLoading, setIsPreviewLoading] = useState<boolean>(false);
  const [previewError, setPreviewError] = useState<string | null>(null);
  const [isVisualMode, setIsVisualMode] = useState<boolean>(true); // For content preview

  // --- State for categories ---
  const [availableCategories, setAvailableCategories] = useState<WpCategory[]>(
    []
  );
  const [isLoadingCategories, setIsLoadingCategories] =
    useState<boolean>(false);
  const [selectedTranslationCategoryIds, setSelectedTranslationCategoryIds] =
    useState<string[]>([]); // Store IDs as strings for MultiSelect

  // --- Fetch Source Content Query ---
  const {
    data: contentItem,
    isLoading: isLoadingItem,
    error,
    isError,
  } = useQuery<SyncedContent, Error>({
    queryKey: queryKey,
    queryFn: () => fetchContentItem(localId),
    enabled: !isNaN(localId) && localId > 0,
    onSuccess: (data) => {
      // Content loaded successfully
    },
  });

  // --- Fetch Available Categories Query ---
  const { data: fetchedCategories } = useQuery<WpCategory[], Error>({
    queryKey: ["wpCategories", connection?.id], // Depends on connection ID
    queryFn: async () => {
      if (!connection?.id) return [];
      setIsLoadingCategories(true);
      const cats = await fetchWpCategoriesForConnection(connection.id);
      setIsLoadingCategories(false);
      return cats;
    },
    enabled: !!connection?.id && isConnected, // Only run when connected and have an ID
    onSuccess: (data) => {
      setAvailableCategories(data || []); // Update state on success
    },
    onError: (error) => {
      console.error("Failed to fetch categories:", error);
      toast({
        title: "Failed to load categories",
        description: error.message,
        variant: "destructive",
      });
      setAvailableCategories([]); // Clear on error
    },
    refetchOnWindowFocus: false, // Categories usually don't change often
  });

  // Map categories for MultiSelect component
  const categoryOptions = useMemo(() => {
    return availableCategories
      .map((cat) => ({
        value: cat.id.toString(), // Value must be string
        label: cat.name,
      }))
      .sort((a, b) => a.label.localeCompare(b.label)); // Sort alphabetically
  }, [availableCategories]);

  // --- Form for editing the SOURCE content ---
  const sourceForm = useForm<ContentEditFormData>({
    resolver: zodResolver(contentEditSchema),
  });

  // Effect to set default values for source form and initialize translation categories
  useEffect(() => {
    if (contentItem) {
      sourceForm.reset({
        title: contentItem.title || "",
        content: contentItem.content || "",
        status: ["publish", "draft", "pending"].includes(contentItem.status)
          ? (contentItem.status as "publish" | "draft" | "pending")
          : "draft",
      });
      // --- Initialize translation categories based on source's current categories ---
      setSelectedTranslationCategoryIds(
        contentItem.categories?.map((cat) => cat.id.toString()) || []
      );
    }
  }, [contentItem, sourceForm]);

  // --- Load/Save Translation Preview from Session Storage ---
  // Load
  useEffect(() => {
    if (typeof window !== "undefined" && localId) {
      try {
        const storageKey = `translation_preview_${localId}`;
        const savedPreview = sessionStorage.getItem(storageKey);
        if (savedPreview) {
          const parsed = JSON.parse(savedPreview);
          if (parsed.targetLanguage) setTargetLanguage(parsed.targetLanguage);
          if (parsed.translatedTitle)
            setTranslatedTitle(parsed.translatedTitle);
          if (parsed.translatedContent)
            setTranslatedContent(parsed.translatedContent);
          // Also load selected categories for translation if saved
          if (parsed.selectedCategories)
            setSelectedTranslationCategoryIds(parsed.selectedCategories);
        }
      } catch (e) {
        console.error("Failed to load translation preview", e);
      }
    }
  }, [localId]);

  // Save
  useEffect(() => {
    if (typeof window !== "undefined" && localId && targetLanguage) {
      try {
        const storageKey = `translation_preview_${localId}`;
        const previewData = {
          targetLanguage,
          translatedTitle,
          translatedContent,
          selectedCategories: selectedTranslationCategoryIds, // Save selected cats too
        };
        sessionStorage.setItem(storageKey, JSON.stringify(previewData));
      } catch (e) {
        console.error("Failed to save translation preview", e);
      }
    }
  }, [
    localId,
    targetLanguage,
    translatedTitle,
    translatedContent,
    selectedTranslationCategoryIds,
  ]);

  // --- Helper Functions ---
  const getHtmlContent = (): string => {
    if (!translatedContent) return "";
    return cleanHtmlContentEnhanced(translatedContent);
  };

  const handleContentChange = (newContent: string) => {
    setTranslatedContent(newContent);
  };

  const clearStoredTranslationPreview = () => {
    if (typeof window !== "undefined" && localId) {
      try {
        sessionStorage.removeItem(`translation_preview_${localId}`);
        sessionStorage.removeItem(`translation_markdown_${localId}`); // Clear markdown too
      } catch (e) {
        console.error("Error clearing storage", e);
      }
    }
  };

  // --- Mutations ---
  // Local Save (Source Content)
  const updateLocalMutation = useMutation({
    mutationFn: (formData: ContentEditFormData) => {
      if (isNaN(localId)) throw new Error("Invalid ID");
      // Note: This only updates title, content, status locally
      return updateContentItem(localId, formData);
    },
    onSuccess: (updatedItem) => {
      toast({ title: "Saved Locally" });
      queryClient.setQueryData(queryKey, updatedItem);
      if (updatedItem.wp_site_url) {
        queryClient.invalidateQueries({
          queryKey: ["localContent", updatedItem.wp_site_url],
        });
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Local Save Failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Push Source Content to WP
  const pushToWpMutation = useMutation({
    mutationFn: (originalContent?: string) => {
      if (!connection?.apiKey) throw new Error("WordPress API Key is missing.");
      if (isNaN(localId)) throw new Error("Invalid ID");
      // Push current state saved in DB (including its categories)
      // Does NOT push selectedTranslationCategoryIds - those are for new translations
      const categoryIdsToPush =
        contentItem?.categories?.map((cat) => cat.id) || [];
      return pushContentToWordPress(
        localId,
        connection.apiKey,
        categoryIdsToPush,
        originalContent // Pass original content to preserve page builder structure
      );
    },
    onSuccess: (wpResponseData) => {
      toast({ title: "Pushed to WordPress" });
      queryClient.setQueryData(queryKey, wpResponseData);
      if (wpResponseData.wp_site_url) {
        queryClient.invalidateQueries({
          queryKey: ["localContent", wpResponseData.wp_site_url],
        });
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Push to WordPress Failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Get Translation Preview (HTML)
  const previewTranslationMutation = useMutation({
    mutationFn: () => {
      if (isNaN(localId)) throw new Error("Invalid ID");
      if (!targetLanguage) throw new Error("Select target language");
      if (targetLanguage === contentItem?.language_code)
        throw new Error("Target cannot be same as source");
      setIsPreviewLoading(true);
      setPreviewError(null);
      setTranslatedTitle(null);
      setTranslatedContent(null);

      // Check if content has AVADA shortcodes and clean it before translation
      const sourceContent = sourceForm.getValues().content || "";
      const hasAvadaContent = detectAvadaContent(sourceContent);

      if (hasAvadaContent) {
        // Clean the AVADA content and send it for translation
        const cleanedContent = cleanHtmlContentEnhanced(sourceContent);

        // Create a temporary content item with cleaned content for translation
        return getTranslationPreview(localId, targetLanguage, cleanedContent);
      }

      return getTranslationPreview(localId, targetLanguage);
    },
    onSuccess: (data) => {
      setTranslatedTitle(data.translated_title);
      // Apply enhanced cleanup to translated content for better preview
      const cleanedTranslatedContent = cleanHtmlContentEnhanced(
        data.translated_content
      );
      setTranslatedContent(cleanedTranslatedContent);
      toast({ title: "Translation Preview Ready" });
    },
    onError: (error: Error) => {
      setPreviewError(error.message || "Preview generation failed.");
      toast({
        title: "Translation Preview Failed",
        description: error.message,
        variant: "destructive",
      });
    },
    onSettled: () => setIsPreviewLoading(false),
  });

  // Get Translation Preview (Markdown)
  const previewMarkdownTranslationMutation = useMutation({
    mutationFn: () => {
      if (isNaN(localId)) throw new Error("Invalid ID");
      if (!targetLanguage) throw new Error("Select target language");
      if (targetLanguage === contentItem?.language_code)
        throw new Error("Target cannot be same as source");

      // Check if content has AVADA shortcodes and clean it before markdown conversion
      const sourceContent = sourceForm.getValues().content || "";
      const hasAvadaContent = detectAvadaContent(sourceContent);

      let contentForMarkdown = sourceContent;
      if (hasAvadaContent) {
        contentForMarkdown = cleanHtmlContentEnhanced(sourceContent);
      }

      const sourceMarkdown = convertHtmlToMarkdown(contentForMarkdown);
      setIsPreviewLoading(true);
      setPreviewError(null);
      setTranslatedTitle(null);
      setTranslatedContent(null);
      return getMarkdownTranslationPreview(
        localId,
        targetLanguage,
        sourceMarkdown
      );
    },
    onSuccess: (data) => {
      setTranslatedTitle(data.translated_title);
      const translatedHtml = convertMarkdownToHtml(data.translated_markdown);
      // Apply enhanced cleanup to markdown-converted content as well
      const cleanedTranslatedContent = cleanHtmlContentEnhanced(translatedHtml);
      setTranslatedContent(cleanedTranslatedContent);
      sessionStorage.setItem(
        `translation_markdown_${localId}`,
        data.translated_markdown
      ); // Store markdown
      toast({
        title: "Translation Preview Ready",
        description: "Using enhanced markdown translation.",
      });
    },
    onError: (error: Error) => {
      setPreviewError(error.message || "Markdown preview failed.");
      toast({
        title: "Markdown Preview Failed",
        description: error.message,
        variant: "destructive",
      });
    },
    onSettled: () => setIsPreviewLoading(false),
  });

  // Create Translated Post on WP
  const createWpPostMutation = useMutation({
    mutationFn: () => {
      if (!connection?.apiKey) throw new Error("WordPress API Key is missing.");
      if (isNaN(localId)) throw new Error("Invalid ID");
      if (!targetLanguage) throw new Error("Target language not set.");
      if (translatedTitle === null || translatedContent === null)
        throw new Error("Translation required");

      const numericCategoryIds = selectedTranslationCategoryIds
        .map((idStr) => parseInt(idStr, 10))
        .filter((id) => !isNaN(id) && id > 0);

      const storedMarkdown = sessionStorage.getItem(
        `translation_markdown_${localId}`
      );
      const complexLanguages = ["zh", "ja", "ru", "ko", "ar", "tr"];
      const isComplexLanguage = complexLanguages.includes(targetLanguage);
      let finalContent = getHtmlContent(); // Get current edited HTML

      if (isComplexLanguage && storedMarkdown) {
        // Prefer converted markdown if available for complex langs, unless user edited HTML significantly
        // Simple check: if current HTML is very different from converted markdown HTML, use current HTML
        const mdHtml = convertMarkdownToHtml(storedMarkdown);
        if (Math.abs(mdHtml.length - finalContent.length) < 50) {
          // Arbitrary threshold
          finalContent = mdHtml;
        }
      }

      // Check if original content is AVADA and reconstruct structure if needed
      const originalContent = contentItem?.content || "";
      if (detectAvadaContent(originalContent)) {
        finalContent = reconstructAvadaContent(originalContent, finalContent);
      }

      return createTranslation(
        localId,
        targetLanguage,
        connection.apiKey,
        translatedTitle,
        finalContent,
        numericCategoryIds // Pass selected category IDs
      );
    },
    onSuccess: (data) => {
      toast({
        title: "Translation Created",
        description: data.message,
        variant: "default",
        duration: 6000,
      });
      if (data.new_content_item?.wp_site_url) {
        queryClient.invalidateQueries({
          queryKey: ["localContent", data.new_content_item.wp_site_url],
        });
      }
      setTargetLanguage("");
      setTranslatedTitle(null);
      setTranslatedContent(null);
      setPreviewError(null);
      setIsVisualMode(false);
      setSelectedTranslationCategoryIds(
        contentItem?.categories?.map((cat) => cat.id.toString()) || []
      ); // Reset category selection
      clearStoredTranslationPreview(); // Clear storage
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to Create Post",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // --- Handlers ---
  const handleLocalSave = (formData: ContentEditFormData) => {
    if (!contentItem) return;
    updateLocalMutation.mutate(formData);
  };

  const handlePushToWp = (originalContent?: string) => {
    const isDirty = sourceForm.formState.isDirty;
    if (isDirty) {
      toast({
        title: "Unsaved Changes",
        description: "Save changes locally before pushing.",
      });
      return;
    }
    if (!isConnected || !connection) {
      toast({
        title: "Cannot Push",
        description: "Not connected to WordPress.",
        variant: "destructive",
      });
      return;
    }
    pushToWpMutation.mutate(originalContent); // Pass original content to preserve page builder structure
  };

  const handleTranslatePreview = () => {
    if (!targetLanguage) {
      toast({
        title: "Select Language",
        description: "Choose a target language first.",
      });
      return;
    }
    if (targetLanguage === contentItem?.language_code) {
      toast({
        title: "Invalid Language",
        description: "Target cannot be same as source.",
        variant: "destructive",
      });
      return;
    }

    const complexLanguages = ["zh", "ja", "ru", "ko", "ar", "tr"];
    if (complexLanguages.includes(targetLanguage)) {
      previewMarkdownTranslationMutation.mutate();
    } else {
      previewTranslationMutation.mutate();
    }
  };

  const handleCreateWpPost = () => {
    if (translatedTitle === null || translatedContent === null) {
      toast({
        title: "Translation Required",
        description: "Generate a translation first.",
        variant: "destructive",
      });
      return;
    }
    createWpPostMutation.mutate();
  };

  const copyToClipboard = (text: string, type: "title" | "content") => {
    if (!text) return;
    navigator.clipboard
      .writeText(text)
      .then(() =>
        toast({ title: `${type === "title" ? "Title" : "Content"} Copied` })
      )
      .catch((err) =>
        toast({
          title: "Copy Failed",
          description: "Could not copy.",
          variant: "destructive",
        })
      );
  };

  const exportToFile = (
    title: string,
    content: string,
    fileName: string,
    fileType: string
  ) => {
    if (!title || !content) return;
    let exportContent = "";
    let mimeType = "text/plain";

    if (fileType === "html") {
      mimeType = "text/html";
      exportContent = `<!DOCTYPE html><html><head><meta charset="UTF-8"><title>${title}</title></head><body><h1>${title}</h1>${content}</body></html>`;
    } else if (fileType === "json") {
      mimeType = "application/json";
      exportContent = JSON.stringify(
        {
          title,
          content,
          language: targetLanguage,
          originalTitle: contentItem?.title,
          exportDate: new Date().toISOString(),
        },
        null,
        2
      );
    } else if (fileType === "md") {
      const titleMd = `# ${title}\n\n`;
      const contentMd = convertHtmlToMarkdown(content);
      exportContent = titleMd + contentMd;
    } else {
      exportContent = `${title}\n\n${content}`;
    }

    const blob = new Blob([exportContent], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `${fileName}.${fileType}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    toast({
      title: "Export Complete",
      description: `Exported as ${fileName}.${fileType}`,
    });
  };

  // --- Loading / Error States ---
  if (isLoadingItem) {
    return (
      <PageWrapper>
        <div className="flex justify-center py-10">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </PageWrapper>
    );
  }

  if (error || !contentItem) {
    return (
      <PageWrapper>
        <div className="text-center py-8">
          <XCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-medium">Failed to load content</h3>
          <p className="text-muted-foreground mb-4">
            {error instanceof Error ? error.message : "Unknown error"}
          </p>
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" /> Go Back
          </Button>
        </div>
      </PageWrapper>
    );
  }

  // --- Render Logic ---
  return (
    <PageWrapper>
      <div className="space-y-8">
        {/* Back navigation */}
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={() => router.back()}
            size="sm"
            className="text-gray-600 hover:text-gray-900 bg-primary/5"
          >
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to List
          </Button>
        </div>

        {/* Main Content Grid with Resizable Layout */}
        <div className="hidden md:flex h-[calc(100vh-200px)] min-h-[600px]">
          <PanelGroup direction="horizontal" className="h-full">
            {/* Left Panel - Source Content */}
            <Panel
              defaultSize={50}
              minSize={30}
              maxSize={70}
              className="flex flex-col overflow-hidden"
            >
              <SourceContentColumn
                contentItem={contentItem}
                onLocalSave={handleLocalSave}
                onPushToWp={handlePushToWp}
                isSavingLocal={updateLocalMutation.isLoading}
                isPushingWp={pushToWpMutation.isLoading}
                canPush={isConnected && !sourceForm.formState.isDirty}
              />
            </Panel>

            {/* Resize Handle */}
            <PanelResizeHandle className="w-px bg-gray-300 dark:bg-gray-700 hover:bg-gray-400 dark:hover:bg-gray-600 transition-colors duration-200 cursor-col-resize"></PanelResizeHandle>

            {/* Right Panel - Translation */}
            <Panel
              defaultSize={50}
              minSize={30}
              className="flex flex-col overflow-hidden"
            >
              <TranslationColumn
                contentItem={contentItem}
                localId={localId}
                connection={connection}
                isConnected={isConnected}
                targetLanguage={targetLanguage}
                setTargetLanguage={setTargetLanguage}
                translatedTitle={translatedTitle}
                setTranslatedTitle={setTranslatedTitle}
                translatedContent={translatedContent}
                setTranslatedContent={setTranslatedContent}
                isPreviewLoading={isPreviewLoading}
                setIsPreviewLoading={setIsPreviewLoading}
                previewError={previewError}
                setPreviewError={setPreviewError}
                isVisualMode={isVisualMode}
                setIsVisualMode={setIsVisualMode}
                availableCategories={availableCategories}
                isLoadingCategories={isLoadingCategories}
                selectedTranslationCategoryIds={selectedTranslationCategoryIds}
                setSelectedTranslationCategoryIds={
                  setSelectedTranslationCategoryIds
                }
                categoryOptions={categoryOptions}
                clearStoredTranslationPreview={clearStoredTranslationPreview}
                onTranslationSuccess={() => {
                  setTargetLanguage("");
                  setTranslatedTitle(null);
                  setTranslatedContent(null);
                  setPreviewError(null);
                  setIsVisualMode(false);
                  setSelectedTranslationCategoryIds(
                    contentItem?.categories?.map((cat) => cat.id.toString()) ||
                      []
                  );
                  clearStoredTranslationPreview();
                  if (contentItem?.wp_site_url) {
                    queryClient.invalidateQueries({
                      queryKey: ["localContent", contentItem.wp_site_url],
                    });
                  }
                }}
              />
            </Panel>
          </PanelGroup>
        </div>

        {/* Mobile Layout - Stacked Columns */}
        <div className="md:hidden space-y-6">
          <SourceContentColumn
            contentItem={contentItem}
            onLocalSave={handleLocalSave}
            onPushToWp={handlePushToWp}
            isSavingLocal={updateLocalMutation.isLoading}
            isPushingWp={pushToWpMutation.isLoading}
            canPush={isConnected && !sourceForm.formState.isDirty}
          />

          <TranslationColumn
            contentItem={contentItem}
            localId={localId}
            connection={connection}
            isConnected={isConnected}
            targetLanguage={targetLanguage}
            setTargetLanguage={setTargetLanguage}
            translatedTitle={translatedTitle}
            setTranslatedTitle={setTranslatedTitle}
            translatedContent={translatedContent}
            setTranslatedContent={setTranslatedContent}
            isPreviewLoading={isPreviewLoading}
            setIsPreviewLoading={setIsPreviewLoading}
            previewError={previewError}
            setPreviewError={setPreviewError}
            isVisualMode={isVisualMode}
            setIsVisualMode={setIsVisualMode}
            availableCategories={availableCategories}
            isLoadingCategories={isLoadingCategories}
            selectedTranslationCategoryIds={selectedTranslationCategoryIds}
            setSelectedTranslationCategoryIds={
              setSelectedTranslationCategoryIds
            }
            categoryOptions={categoryOptions}
            clearStoredTranslationPreview={clearStoredTranslationPreview}
            onTranslationSuccess={() => {
              setTargetLanguage("");
              setTranslatedTitle(null);
              setTranslatedContent(null);
              setPreviewError(null);
              setIsVisualMode(false);
              setSelectedTranslationCategoryIds(
                contentItem?.categories?.map((cat) => cat.id.toString()) || []
              );
              clearStoredTranslationPreview();
              if (contentItem?.wp_site_url) {
                queryClient.invalidateQueries({
                  queryKey: ["localContent", contentItem.wp_site_url],
                });
              }
            }}
          />
        </div>
      </div>
    </PageWrapper>
  );
}
