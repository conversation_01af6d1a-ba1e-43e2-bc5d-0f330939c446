"use client";

import { useEffect, useState } from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import { Loader2 } from "lucide-react";
import { useTheme } from "next-themes";
import { signOut } from "next-auth/react";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import Sato<PERSON><PERSON><PERSON><PERSON> from "@/public/images/satoloc_insight_logo.png";

const messages = [
  "Thank you for using SatoLOC Insight!",
  "Would you like to end your current session?",
];

export default function GoodbyeClientPage() {
  const router = useRouter();
  const { theme } = useTheme();
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [displayedMessage, setDisplayedMessage] = useState("");
  const [showButtons, setShowButtons] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [isSkipping, setIsSkipping] = useState(false);

  const handleConfirm = async () => {
    setIsLoggingOut(true);
    try {
      // Save theme preference
      const theme = localStorage.getItem("theme");
      const preservedTheme = theme || "system";

      // Clear storage
      sessionStorage.clear();
      localStorage.clear();

      // Restore theme
      localStorage.setItem("theme", preservedTheme);

      // Sign out and redirect to login
      await signOut({
        callbackUrl: "/login",
      });
    } catch (error) {
      console.error("Error during sign out:", error);
      window.location.replace("/login");
    }
  };

  const handleCancel = () => {
    router.push("/dashboard");
  };

  const handleSkip = async () => {
    setIsSkipping(true);
    try {
      // Save theme preference
      const theme = localStorage.getItem("theme");
      const preservedTheme = theme || "system";

      // Clear storage
      sessionStorage.clear();
      localStorage.clear();

      // Restore theme
      localStorage.setItem("theme", preservedTheme);

      // Sign out and redirect to login
      await signOut({
        callbackUrl: "/login",
      });
    } catch (error) {
      console.error("Error during sign out:", error);
      window.location.replace("/login");
    }
  };

  useEffect(() => {
    let timeout: NodeJS.Timeout;

    if (currentMessageIndex < messages.length) {
      const message = messages[currentMessageIndex];
      let charIndex = 0;

      const typeMessage = () => {
        if (charIndex <= message.length) {
          setDisplayedMessage(message.slice(0, charIndex));
          charIndex++;
          timeout = setTimeout(typeMessage, 50);
        } else {
          timeout = setTimeout(() => {
            if (currentMessageIndex < messages.length - 1) {
              setCurrentMessageIndex((prev) => prev + 1);
            } else {
              setShowButtons(true);
            }
          }, 1000);
        }
      };

      typeMessage();
    }

    return () => clearTimeout(timeout);
  }, [currentMessageIndex]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-2xl w-full"
      >
        <div className="text-center space-y-8">
          {/* Skip button - positioned at top right */}
          {!isLoggingOut && !isSkipping && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="absolute top-8 right-8 border border-default rounded-md"
            >
              <Button
                variant="ghost"
                onClick={handleSkip}
                disabled={isSkipping}
                className="text-sm"
              >
                {isSkipping ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Skipping...
                  </>
                ) : (
                  "Skip"
                )}
              </Button>
            </motion.div>
          )}

          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2 }}
            className="w-[150px] h-[150px] mx-auto mb-2"
          >
            <div className="w-full h-full flex items-center justify-center">
              <Image
                src={SatoLOCLogo}
                alt="SatoLOC Insight Logo"
                width={150}
                height={150}
                priority
                style={{
                  objectFit: "contain",
                }}
              />
            </div>
          </motion.div>

          <div className="min-h-[120px] flex flex-col items-center justify-center">
            <p className="text-2xl font-semibold text-foreground">
              {displayedMessage}
              <span className="animate-pulse">|</span>
            </p>
          </div>

          {showButtons && !isLoggingOut && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex items-center justify-center gap-4"
            >
              <Button
                variant="outline"
                onClick={handleCancel}
                className="min-w-[120px]"
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleConfirm}
                className="min-w-[120px]"
              >
                Confirm
              </Button>
            </motion.div>
          )}

          {isLoggingOut && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex items-center justify-center gap-2 text-muted-foreground"
            >
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Logging out...</span>
            </motion.div>
          )}
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
        className="absolute bottom-8 text-center text-sm text-muted-foreground"
      >
        <p> 2025 SatoLoc Insight. All rights reserved.</p>
      </motion.div>
    </div>
  );
}
