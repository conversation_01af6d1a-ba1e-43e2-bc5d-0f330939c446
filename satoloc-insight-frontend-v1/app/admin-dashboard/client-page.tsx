// app/admin-dashboard/client-page.tsx
"use client";

import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import PageWrapper from "@/components/PageWrapper";
import { Container } from "@mui/material";
import SEOAnalysisForm from "@/components/AdvanceSeo/SEOAnalysisForm";
import WebsiteTable from "@/components/Admin/WebsiteTable";

export default function AdminDashboardClientPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === "loading") return;

    if (!session) {
      router.push("/login");
      return;
    }

    if (!session.user?.isAdmin) {
      router.push("/dashboard");
    }
  }, [session, status, router]);

  if (status === "loading") {
    return (
      <PageWrapper>
        <Container /*className="container mx-auto p-6"*/>
          <div className="grid gap-6 md:grid-cols-3">
            {[1, 2, 3].map((i) => (
              <div
                key={i}
                className="h-[200px] animate-pulse bg-muted rounded-lg"
              />
            ))}
          </div>
        </Container>
      </PageWrapper>
    );
  }

  if (!session?.user?.isAdmin) return null;

  return (
    <PageWrapper isAdmin>
      <div /*className="container mx-auto p-6"*/>
        <div className="flex flex-col gap-6">
          {/* SEO Analysis Section */}
          <div className="">
            <SEOAnalysisForm />
          </div>

          {/* Website Table Section */}
          <div className="">
            <WebsiteTable />
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}
