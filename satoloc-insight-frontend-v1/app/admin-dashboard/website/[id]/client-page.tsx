"use client";

import React, { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import PageWrapper from "@/components/PageWrapper";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertCircle,
  ArrowLeft,
  CheckCircle,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  ExternalLink,
  Globe,
  KeySquare,
  Languages,
  Loader2,
  PlusCircle,
  Save,
  Trash2,
  Users2,
  X,
  <PERSON><PERSON><PERSON>riangle,
  ArrowUpRight,
  <PERSON><PERSON><PERSON>,
  Bar<PERSON>hart3,
  InfoIcon,
  TargetIcon,
  UsersIcon,
  LightbulbIcon,
  Sprout,
  LineChart,
  LoaderIcon,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import Link from "next/link";
import {
  useAddCompetitorKeyword,
  useAddWebsiteCompetitor,
  useAddWebsiteKeyword,
  useAddWebsiteLanguage,
  useAllWebsites,
  useProcessWebsite,
  useRemoveCompetitorKeyword,
  useRemoveWebsiteCompetitor,
  useRemoveWebsiteKeyword,
  useRemoveWebsiteLanguage,
  useUpdateCompetitorKeywords,
  useUpdateWebsiteBasicInfo,
  useUpdateWebsiteCompetitor,
  useUpdateWebsiteKeyword,
  useUploadCompetitorsCsv,
  useUploadCompetitorKeywordsCsv,
  useWebsiteById,
  useAddWebsiteStrategyGap,
  useRemoveWebsiteStrategyGap,
  useAddWebsiteGrowthOpportunity,
  useRemoveWebsiteGrowthOpportunity,
  useUpdateSEOMetrics,
  useAddRankingIssue,
  useRemoveRankingIssue,
  useAddContentRecommendation,
  useRemoveContentRecommendation,
} from "@/internal-api/seo/advance-seo";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";

// Simple Collapsible component implementation
const Collapsible = ({ children }: { children: React.ReactNode }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="collapsible">
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child) && child.type === CollapsibleTrigger) {
          return React.cloneElement(child as React.ReactElement<any>, {
            onClick: () => setIsOpen(!isOpen),
            "aria-expanded": isOpen,
          });
        }
        if (React.isValidElement(child) && child.type === CollapsibleContent) {
          return isOpen ? child : null;
        }
        return child;
      })}
    </div>
  );
};

const CollapsibleTrigger = ({
  children,
  asChild,
  ...props
}: {
  children: React.ReactNode;
  asChild?: boolean;
  [key: string]: any;
}) => {
  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(children as React.ReactElement<any>, props);
  }
  return <div {...props}>{children}</div>;
};

const CollapsibleContent = ({ children }: { children: React.ReactNode }) => {
  return <div className="collapsible-content">{children}</div>;
};

// --- CSV upload component for competitor keywords ---
type CompetitorKeywordsCsvUploadProps = {
  websiteId: string;
  competitorId?: number;
  onSuccess: (added_count: number) => void;
};

const CompetitorKeywordsCsvUpload: React.FC<
  CompetitorKeywordsCsvUploadProps
> = ({ websiteId, competitorId, onSuccess }) => {
  const { toast } = useToast();
  const [file, setFile] = useState<File | undefined>(undefined);
  const uploadKeywordsCsv = useUploadCompetitorKeywordsCsv();

  if (!competitorId) return null;

  return (
    <form
      className="flex items-center gap-2"
      onSubmit={async (e) => {
        e.preventDefault();
        if (!file) return;
        uploadKeywordsCsv.mutate(
          { websiteId, competitorId, file },
          {
            onSuccess: (data: { added_count: number }) => {
              onSuccess(data.added_count);
              setFile(undefined);
            },
            onError: (err: any) => {
              toast({
                title: "Upload failed",
                description: err?.response?.data?.error || err.message,
                variant: "destructive",
              });
            },
          }
        );
      }}
    >
      <Input
        type="file"
        accept=".csv,text/csv"
        onChange={(e) => {
          if (e.target.files && e.target.files[0]) {
            setFile(e.target.files[0]);
          }
        }}
        className="w-auto"
      />
      <Button
        type="submit"
        size="sm"
        disabled={!file || uploadKeywordsCsv.isLoading}
      >
        {uploadKeywordsCsv.isLoading ? (
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
        ) : null}
        Upload Keywords CSV
      </Button>
      {file && (
        <span className="text-xs text-muted-foreground">{file.name}</span>
      )}
    </form>
  );
};

export default function WebsiteDetailsPage({ id }: { id: string }) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { toast } = useToast();

  // Fetch website data
  const {
    data: website,
    isLoading,
    isError,
    error,
    refetch,
  } = useWebsiteById(id);

  // State for edited values
  const [websiteData, setWebsiteData] = useState({
    url: "",
    industry: "",
    language: "",
  });

  // State for languages
  const [languages, setLanguages] = useState<string[]>([]);
  const [newLanguage, setNewLanguage] = useState("");

  // State for keywords
  const [keywords, setKeywords] = useState<
    {
      keyword: string;
      is_target: boolean;
    }[]
  >([]);
  const [newKeyword, setNewKeyword] = useState("");
  const [newKeywordIsTarget, setNewKeywordIsTarget] = useState(false);

  // State for competitors
  const [competitors, setCompetitors] = useState<
    {
      id?: number;
      name: string;
      target_url: string;
      description: string;
      rank?: number;
      top_keywords?: string[];
      strategy_gaps?: { id: number; text: string }[];
      growth_opportunities?: { id: number; text: string }[];
      ranking_issues?: {
        id: number;
        title: string;
        description: string;
        impact: string;
      }[];
      content_recommendations?: {
        id: number;
        title: string;
        description: string;
        impact: string;
        estimated_hours: number;
        is_opportunity: boolean;
      }[];
    }[]
  >([]);

  // State for SEO metrics
  const [seoMetrics, setSeoMetrics] = useState<{
    domain_rating?: {
      description?: string;
      score?: number;
      previous_score?: number | null;
    };
    organic_traffic?: {
      description?: string;
      score?: number;
      previous_score?: number | null;
    };
    search_rankings?: {
      description?: string;
      score?: number;
      previous_score?: number | null;
    };
    search_terms?: {
      description?: string;
      score?: number;
      previous_score?: number | null;
    };
    site_links?: {
      description?: string;
      score?: number;
      previous_score?: number | null;
    };
    content_score?: {
      description?: string;
      score?: number;
      previous_score?: number | null;
    };
    backlinks?: number;
  }>({});

  // State for editing SEO metrics - flattened for easier form handling
  const [editedSeoMetrics, setEditedSeoMetrics] = useState<{
    domain_rating_score?: number | null;
    domain_rating_previous_score?: number | null;
    organic_traffic_score?: number | null;
    organic_traffic_previous_score?: number | null;
    search_rankings_score?: number | null;
    search_rankings_previous_score?: number | null;
    search_terms_score?: number | null;
    search_terms_previous_score?: number | null;
    site_links_score?: number | null;
    site_links_previous_score?: number | null;
    content_score_value?: number | null;
    content_score_previous_score?: number | null;
    backlinks?: number | null;
  }>({});

  // State for editing SEO metrics
  const [isEditingSeoMetrics, setIsEditingSeoMetrics] = useState(false);

  // Pagination state for competitors
  const [currentCompetitorPage, setCurrentCompetitorPage] = useState(1);
  const competitorsPerPage = 5;

  // Sorting state for competitors
  const [competitorSortBy, setCompetitorSortBy] = useState<
    "rank" | "name" | "none"
  >("none");

  // Calculate total pages for competitors
  const totalCompetitorPages = Math.ceil(
    competitors.length / competitorsPerPage
  );

  // Sort competitors based on sorting preference
  const sortedCompetitors = [...competitors].sort((a, b) => {
    if (competitorSortBy === "rank") {
      // Sort by rank (lower rank = higher priority)
      const rankA = a.rank !== undefined ? a.rank : Number.MAX_SAFE_INTEGER;
      const rankB = b.rank !== undefined ? b.rank : Number.MAX_SAFE_INTEGER;
      return rankA - rankB;
    } else if (competitorSortBy === "name") {
      // Sort by name alphabetically
      return a.name.localeCompare(b.name);
    }
    // Default: no sorting (keep original order)
    return 0;
  });

  // Get current competitors for pagination
  const indexOfLastCompetitor = currentCompetitorPage * competitorsPerPage;
  const indexOfFirstCompetitor = indexOfLastCompetitor - competitorsPerPage;
  const currentCompetitors = sortedCompetitors.slice(
    indexOfFirstCompetitor,
    indexOfLastCompetitor
  );

  // State for CSV upload
  const [csvFile, setCsvFile] = useState<File | undefined>(undefined);
  const uploadCsv = useUploadCompetitorsCsv();
  const [newCompetitor, setNewCompetitor] = useState({
    name: "",
    target_url: "",
    description: "",
    rank: 0,
  });
  const [editingCompetitor, setEditingCompetitor] = useState<number | null>(
    null
  );
  const [editedCompetitor, setEditedCompetitor] = useState({
    name: "",
    target_url: "",
    description: "",
    rank: 0,
  });

  // Add these new state variables for keyword editing
  const [editingKeywords, setEditingKeywords] = useState<boolean[]>([]);
  const [editedKeywords, setEditedKeywords] = useState<{
    [key: number]: string[];
  }>({});
  const [newKeywordText, setNewKeywordText] = useState<{
    [key: number]: string;
  }>({});

  // Add states for strategy gaps and growth opportunities
  const [editingStrategyGaps, setEditingStrategyGaps] =
    useState<boolean>(false);
  const [editingGrowthOpportunities, setEditingGrowthOpportunities] =
    useState<boolean>(false);
  const [newStrategyGapText, setNewStrategyGapText] = useState<string>("");
  const [newGrowthOpportunityText, setNewGrowthOpportunityText] =
    useState<string>("");

  // Add states for website strategy gaps and growth opportunities
  const [strategyGaps, setStrategyGaps] = useState<
    { id: number; text: string }[]
  >([]);
  const [growthOpportunities, setGrowthOpportunities] = useState<
    { id: number; text: string }[]
  >([]);

  // Add these new state variables after the existing state declarations
  const [editingRankingIssues, setEditingRankingIssues] = useState<{
    [key: number]: boolean;
  }>({});
  const [editingContentRecommendations, setEditingContentRecommendations] =
    useState<{ [key: number]: boolean }>({});
  const [newRankingIssue, setNewRankingIssue] = useState<{
    [key: number]: {
      title: string;
      description: string;
      impact: "high" | "medium" | "low";
    };
  }>({});
  const [newContentRecommendation, setNewContentRecommendation] = useState<{
    [key: number]: {
      title: string;
      description: string;
      impact: "high" | "medium" | "low";
      estimated_hours: number;
      is_opportunity: boolean;
    };
  }>({});

  // Populate state when website data is loaded
  useEffect(() => {
    if (website) {
      setWebsiteData({
        url: website.url || "",
        industry: website.industry || "",
        language: website.language || "",
      });

      // Set languages
      if (website.available_languages) {
        setLanguages(
          website.available_languages.map((lang: any) => lang.language)
        );
      }

      // Set keywords
      if (website.keywords) {
        setKeywords(website.keywords);
      }

      // Set competitors
      if (website.competitors) {
        setCompetitors(website.competitors);
      }

      // Set strategy gaps and growth opportunities
      if (website.strategy_gaps) {
        setStrategyGaps(website.strategy_gaps);
      }

      if (website.growth_opportunities) {
        setGrowthOpportunities(website.growth_opportunities);
      }

      // Set SEO metrics
      if (website.seo_metrics) {
        // Set the nested structure
        setSeoMetrics(website.seo_metrics);

        // Create a flattened version for the form
        const flattenedMetrics = {
          domain_rating_score: website.seo_metrics.domain_rating?.score || null,
          domain_rating_previous_score:
            website.seo_metrics.domain_rating?.previous_score || null,
          organic_traffic_score:
            website.seo_metrics.organic_traffic?.score || null,
          organic_traffic_previous_score:
            website.seo_metrics.organic_traffic?.previous_score || null,
          search_rankings_score:
            website.seo_metrics.search_rankings?.score || null,
          search_rankings_previous_score:
            website.seo_metrics.search_rankings?.previous_score || null,
          search_terms_score: website.seo_metrics.search_terms?.score || null,
          search_terms_previous_score:
            website.seo_metrics.search_terms?.previous_score || null,
          site_links_score: website.seo_metrics.site_links?.score || null,
          site_links_previous_score:
            website.seo_metrics.site_links?.previous_score || null,
          content_score_value: website.seo_metrics.content_score?.score || null,
          content_score_previous_score:
            website.seo_metrics.content_score?.previous_score || null,
          backlinks: website.backlinks || 0,
        };

        setEditedSeoMetrics(flattenedMetrics);
      }
    }
  }, [website]);

  useEffect(() => {
    if (status === "loading") return;

    if (!session) {
      router.push("/login");
      return;
    }

    if (!session.user?.isAdmin) {
      router.push("/dashboard");
    }
  }, [session, status, router]);

  // Mutations
  const updateBasicInfo = useUpdateWebsiteBasicInfo({
    onSuccess: (data: any) => {
      toast({
        title: "Success",
        description: "Website information updated successfully",
      });

      // Force a refetch to get the updated data
      setTimeout(() => {
        refetch();
      }, 500);
    },
    onError: (error: Error) => {
      console.error("Error response:", error);
      let errorMessage =
        error.message || "Failed to update website information";

      // Try to extract more detailed error information if available
      if (error instanceof Error && "response" in error) {
        const responseData = (error as any).response?.data;
        if (responseData) {
          // Format and display all validation errors
          const errorDetails = Object.entries(responseData)
            .map(([key, value]) => `${key}: ${value}`)
            .join(", ");
          errorMessage = errorDetails || errorMessage;
        }
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  const addLanguage = useAddWebsiteLanguage({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Language added successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add language",
        variant: "destructive",
      });
    },
  });

  const removeLanguage = useRemoveWebsiteLanguage({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Language removed successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to remove language",
        variant: "destructive",
      });
    },
  });

  const addKeyword = useAddWebsiteKeyword({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Keyword added successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add keyword",
        variant: "destructive",
      });
    },
  });

  const removeKeyword = useRemoveWebsiteKeyword({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Keyword removed successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to remove keyword",
        variant: "destructive",
      });
    },
  });

  const updateKeyword = useUpdateWebsiteKeyword({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Keyword updated successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update keyword",
        variant: "destructive",
      });
    },
  });

  const addCompetitor = useAddWebsiteCompetitor({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Competitor added successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add competitor",
        variant: "destructive",
      });
    },
  });

  const removeCompetitor = useRemoveWebsiteCompetitor({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Competitor removed successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to remove competitor",
        variant: "destructive",
      });
    },
  });

  const updateCompetitor = useUpdateWebsiteCompetitor({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Competitor updated successfully",
      });
      refetch();
      setEditingCompetitor(null);
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update competitor",
        variant: "destructive",
      });
    },
  });

  // Add these mutation hooks
  const addCompKeyword = useAddCompetitorKeyword({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Keyword added successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add keyword",
        variant: "destructive",
      });
    },
  });

  const removeCompKeyword = useRemoveCompetitorKeyword({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Keyword removed successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to remove keyword",
        variant: "destructive",
      });
    },
  });

  const updateCompKeywords = useUpdateCompetitorKeywords({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Keywords updated successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update keywords",
        variant: "destructive",
      });
    },
  });

  // Add these after the keyword mutation hooks
  const addStrategyGap = useAddWebsiteStrategyGap({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Strategy gap added successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add strategy gap",
        variant: "destructive",
      });
    },
  });

  const removeStrategyGap = useRemoveWebsiteStrategyGap({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Strategy gap removed successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to remove strategy gap",
        variant: "destructive",
      });
    },
  });

  const addGrowthOpportunity = useAddWebsiteGrowthOpportunity({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Growth opportunity added successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add growth opportunity",
        variant: "destructive",
      });
    },
  });

  const removeGrowthOpportunity = useRemoveWebsiteGrowthOpportunity({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Growth opportunity removed successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to remove growth opportunity",
        variant: "destructive",
      });
    },
  });

  const updateSEOMetrics = useUpdateSEOMetrics({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "SEO metrics updated successfully",
      });
      refetch();
      setIsEditingSeoMetrics(false);
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update SEO metrics",
        variant: "destructive",
      });
    },
  });

  // Add the new mutation hooks
  const addRankingIssue = useAddRankingIssue({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Ranking issue added successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add ranking issue",
        variant: "destructive",
      });
    },
  });

  const removeRankingIssue = useRemoveRankingIssue({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Ranking issue removed successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to remove ranking issue",
        variant: "destructive",
      });
    },
  });

  const addContentRecommendation = useAddContentRecommendation({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Content recommendation added successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add content recommendation",
        variant: "destructive",
      });
    },
  });

  const removeContentRecommendation = useRemoveContentRecommendation({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Content recommendation removed successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to remove content recommendation",
        variant: "destructive",
      });
    },
  });

  // Initialize editing states when competitors data changes
  useEffect(() => {
    if (competitors && competitors.length > 0) {
      setEditingKeywords(new Array(competitors.length).fill(false));

      // Initialize edited keywords with current values
      const keywordsMap: { [key: number]: string[] } = {};
      competitors.forEach((competitor, index) => {
        if (competitor.top_keywords) {
          keywordsMap[index] = [...competitor.top_keywords];
        } else {
          keywordsMap[index] = [];
        }
      });
      setEditedKeywords(keywordsMap);

      // Initialize new keyword input only
      const newKeywordMap: { [key: number]: string } = {};
      competitors.forEach((_, index) => {
        newKeywordMap[index] = "";
      });

      setNewKeywordText(newKeywordMap);
    }
  }, [competitors]);

  // Initialize editing states for ranking issues and content recommendations when competitors data changes
  useEffect(() => {
    if (competitors && competitors.length > 0) {
      // Initialize ranking issues editing state
      const rankingIssuesMap: { [key: number]: boolean } = {};
      competitors.forEach((_, index) => {
        rankingIssuesMap[index] = false;
      });
      setEditingRankingIssues(rankingIssuesMap);

      // Initialize content recommendations editing state
      const contentRecommendationsMap: { [key: number]: boolean } = {};
      competitors.forEach((_, index) => {
        contentRecommendationsMap[index] = false;
      });
      setEditingContentRecommendations(contentRecommendationsMap);

      // Initialize new ranking issue forms
      const newRankingIssueMap: {
        [key: number]: {
          title: string;
          description: string;
          impact: "high" | "medium" | "low";
        };
      } = {};
      competitors.forEach((_, index) => {
        newRankingIssueMap[index] = {
          title: "",
          description: "",
          impact: "medium",
        };
      });
      setNewRankingIssue(newRankingIssueMap);

      // Initialize new content recommendation forms
      const newContentRecommendationMap: {
        [key: number]: {
          title: string;
          description: string;
          impact: "high" | "medium" | "low";
          estimated_hours: number;
          is_opportunity: boolean;
        };
      } = {};
      competitors.forEach((_, index) => {
        newContentRecommendationMap[index] = {
          title: "",
          description: "",
          impact: "medium",
          estimated_hours: 2,
          is_opportunity: false,
        };
      });
      setNewContentRecommendation(newContentRecommendationMap);
    }
  }, [competitors]);

  // Functions to handle editing
  const handleSaveBasicInfo = async () => {
    // No URL formatting - use URL exactly as entered
    const data = {
      ...websiteData,
    };

    try {
      updateBasicInfo.mutate({
        id,
        data,
      });
    } catch (error) {
      console.error("Error in handleSaveBasicInfo:", error);
      toast({
        title: "Error",
        description:
          "There was an error updating the website information. Please check the console for details.",
        variant: "destructive",
      });
    }
  };

  const handleAddLanguage = () => {
    if (!newLanguage) return;

    if (languages.includes(newLanguage)) {
      toast({
        title: "Warning",
        description: "This language is already added",
        variant: "destructive",
      });
      return;
    }

    addLanguage.mutate({
      websiteId: id,
      language: newLanguage,
    });

    setNewLanguage("");
  };

  const handleRemoveLanguage = (language: string) => {
    removeLanguage.mutate({
      websiteId: id,
      language,
    });
  };

  const handleAddKeyword = () => {
    if (!newKeyword) return;

    if (keywords.some((k) => k.keyword === newKeyword)) {
      toast({
        title: "Warning",
        description: "This keyword is already added",
        variant: "destructive",
      });
      return;
    }

    addKeyword.mutate({
      websiteId: id,
      keyword: newKeyword,
      isTarget: newKeywordIsTarget,
    });

    setNewKeyword("");
    setNewKeywordIsTarget(false);
  };

  const handleRemoveKeyword = (keyword: string) => {
    removeKeyword.mutate({
      websiteId: id,
      keyword,
    });
  };

  const handleToggleTargetKeyword = (keyword: string) => {
    const isCurrentlyTarget =
      keywords.find((k) => k.keyword === keyword)?.is_target || false;

    updateKeyword.mutate({
      websiteId: id,
      keyword,
      isTarget: !isCurrentlyTarget,
    });
  };

  const handleAddCompetitor = () => {
    if (!newCompetitor.name || !newCompetitor.target_url) {
      toast({
        title: "Warning",
        description: "Name and URL are required for competitors",
        variant: "destructive",
      });
      return;
    }

    addCompetitor.mutate({
      websiteId: id,
      name: newCompetitor.name,
      targetUrl: newCompetitor.target_url,
      description: newCompetitor.description,
      rank: newCompetitor.rank,
    });

    setNewCompetitor({
      name: "",
      target_url: "",
      description: "",
      rank: 0,
    });
  };

  const handleRemoveCompetitor = (index: number) => {
    const competitor = competitors[index];

    if (competitor.id) {
      removeCompetitor.mutate({
        websiteId: id,
        competitorId: competitor.id,
      });
    } else {
      // If competitor doesn't have an ID, it might be a new one not saved to the database yet
      setCompetitors(competitors.filter((_, i) => i !== index));

      toast({
        title: "Success",
        description: "Competitor removed",
      });
    }
  };

  const handleStartEditingCompetitor = (index: number) => {
    const competitor = competitors[index];
    setEditingCompetitor(index);
    setEditedCompetitor({
      name: competitor.name,
      target_url: competitor.target_url,
      description: competitor.description || "",
      rank: competitor.rank || 0,
    });
  };

  const handleSaveCompetitorEdit = (index: number) => {
    const competitor = competitors[index];

    // Use ID if available, otherwise use the original target_url to identify the competitor
    if (competitor.id) {
      updateCompetitor.mutate({
        websiteId: id,
        competitorId: competitor.id,
        name: editedCompetitor.name,
        targetUrl: editedCompetitor.target_url,
        description: editedCompetitor.description,
        rank: editedCompetitor.rank,
      });
    } else {
      updateCompetitor.mutate({
        websiteId: id,
        originalTargetUrl: competitor.target_url,
        name: editedCompetitor.name,
        targetUrl: editedCompetitor.target_url,
        description: editedCompetitor.description,
        rank: editedCompetitor.rank,
      });
    }
  };

  const handleCancelCompetitorEdit = () => {
    setEditingCompetitor(null);
  };

  // Add these new handler functions
  const handleStartEditingKeywords = (index: number) => {
    const newEditingKeywords = [...editingKeywords];
    newEditingKeywords[index] = true;
    setEditingKeywords(newEditingKeywords);

    // Make sure we have the current keywords in our state
    const keywordsMap = { ...editedKeywords };
    if (!keywordsMap[index] && competitors[index].top_keywords) {
      keywordsMap[index] = [...competitors[index].top_keywords];
    } else if (!keywordsMap[index]) {
      keywordsMap[index] = [];
    }
    setEditedKeywords(keywordsMap);
  };

  const handleCancelEditingKeywords = (index: number) => {
    const newEditingKeywords = [...editingKeywords];
    newEditingKeywords[index] = false;
    setEditingKeywords(newEditingKeywords);

    // Reset to original values
    const keywordsMap = { ...editedKeywords };
    if (competitors[index].top_keywords) {
      keywordsMap[index] = [...competitors[index].top_keywords];
    } else {
      keywordsMap[index] = [];
    }
    setEditedKeywords(keywordsMap);

    // Clear the new keyword input
    const newKeywordMap = { ...newKeywordText };
    newKeywordMap[index] = "";
    setNewKeywordText(newKeywordMap);
  };

  const handleSaveKeywords = (index: number) => {
    const competitor = competitors[index];

    if (!competitor.id) {
      toast({
        title: "Error",
        description: "Cannot update keywords for a competitor without an ID",
        variant: "destructive",
      });
      return;
    }

    updateCompKeywords.mutate({
      websiteId: id,
      competitorId: competitor.id,
      keywords: editedKeywords[index] || [],
    });

    // Exit edit mode
    const newEditingKeywords = [...editingKeywords];
    newEditingKeywords[index] = false;
    setEditingKeywords(newEditingKeywords);
  };

  const handleRemoveCompetitorKeyword = (
    competitorIndex: number,
    keywordIndex: number
  ) => {
    const keywordsMap = { ...editedKeywords };
    const updatedKeywords = [...(keywordsMap[competitorIndex] || [])];
    updatedKeywords.splice(keywordIndex, 1);
    keywordsMap[competitorIndex] = updatedKeywords;
    setEditedKeywords(keywordsMap);
  };

  const handleAddNewKeyword = (index: number) => {
    if (!newKeywordText[index] || newKeywordText[index].trim() === "") {
      return;
    }

    const keywordsMap = { ...editedKeywords };
    if (!keywordsMap[index]) {
      keywordsMap[index] = [];
    }

    // Check if keyword already exists
    if (keywordsMap[index].includes(newKeywordText[index].trim())) {
      toast({
        title: "Warning",
        description: "This keyword already exists",
        variant: "destructive",
      });
      return;
    }

    keywordsMap[index] = [...keywordsMap[index], newKeywordText[index].trim()];
    setEditedKeywords(keywordsMap);

    // Clear the input
    const newKeywordMap = { ...newKeywordText };
    newKeywordMap[index] = "";
    setNewKeywordText(newKeywordMap);
  };

  const handleKeywordTextChange = (index: number, value: string) => {
    const newKeywordMap = { ...newKeywordText };
    newKeywordMap[index] = value;
    setNewKeywordText(newKeywordMap);
  };

  // Strategy Gap handlers
  const handleToggleStrategyGapEditor = () => {
    setEditingStrategyGaps(!editingStrategyGaps);
  };

  const handleAddStrategyGap = () => {
    if (!newStrategyGapText) return;

    addStrategyGap.mutate({
      websiteId: id,
      text: newStrategyGapText,
    });

    // Clear the input
    setNewStrategyGapText("");
  };

  const handleRemoveStrategyGap = (gapId: number) => {
    removeStrategyGap.mutate({
      websiteId: id,
      gapId: gapId,
    });
  };

  const handleStrategyGapTextChange = (value: string) => {
    setNewStrategyGapText(value);
  };

  // Growth Opportunity handlers
  const handleToggleGrowthOpportunityEditor = () => {
    setEditingGrowthOpportunities(!editingGrowthOpportunities);
  };

  const handleAddGrowthOpportunity = () => {
    if (!newGrowthOpportunityText) return;

    addGrowthOpportunity.mutate({
      websiteId: id,
      text: newGrowthOpportunityText,
    });

    // Clear the input
    setNewGrowthOpportunityText("");
  };

  const handleRemoveGrowthOpportunity = (opportunityId: number) => {
    removeGrowthOpportunity.mutate({
      websiteId: id,
      opportunityId: opportunityId,
    });
  };

  const handleGrowthOpportunityTextChange = (value: string) => {
    setNewGrowthOpportunityText(value);
  };

  // Handler to submit SEO metrics update
  const handleSaveSeoMetrics = () => {
    // Send flattened data directly matching the backend model field names
    const transformedData = {
      domain_rating_score: editedSeoMetrics.domain_rating_score,
      domain_rating_previous_score:
        editedSeoMetrics.domain_rating_previous_score,
      organic_traffic_score: editedSeoMetrics.organic_traffic_score,
      organic_traffic_previous_score:
        editedSeoMetrics.organic_traffic_previous_score,
      search_rankings_score: editedSeoMetrics.search_rankings_score,
      search_rankings_previous_score:
        editedSeoMetrics.search_rankings_previous_score,
      search_terms_score: editedSeoMetrics.search_terms_score,
      search_terms_previous_score: editedSeoMetrics.search_terms_previous_score,
      site_links_score: editedSeoMetrics.site_links_score,
      site_links_previous_score: editedSeoMetrics.site_links_previous_score,
      content_score: editedSeoMetrics.content_score_value,
      content_score_previous_score:
        editedSeoMetrics.content_score_previous_score,
      backlinks_count: editedSeoMetrics.backlinks,
    };

    // Submit the transformed data
    updateSEOMetrics.mutate({
      websiteId: id,
      data: transformedData,
    });
  };

  // Ranking Issues handlers
  const handleToggleRankingIssuesEditor = (index: number) => {
    setEditingRankingIssues((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  const handleAddRankingIssue = (index: number) => {
    const competitor = competitors[index];

    if (!competitor.id) {
      toast({
        title: "Error",
        description: "Cannot add ranking issues for a competitor without an ID",
        variant: "destructive",
      });
      return;
    }

    const { title, description, impact } = newRankingIssue[index];

    if (!title || !description) {
      toast({
        title: "Warning",
        description: "Title and description are required for ranking issues",
        variant: "destructive",
      });
      return;
    }

    addRankingIssue.mutate({
      websiteId: id,
      competitorId: competitor.id,
      title,
      description,
      impact,
    });

    // Reset the form
    setNewRankingIssue((prev) => ({
      ...prev,
      [index]: { title: "", description: "", impact: "medium" },
    }));
  };

  const handleRemoveRankingIssue = (issueId: number) => {
    removeRankingIssue.mutate({
      websiteId: id,
      rankingIssueId: issueId,
    });
  };

  // Content Recommendations handlers
  const handleToggleContentRecommendationsEditor = (index: number) => {
    setEditingContentRecommendations((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  const handleAddContentRecommendation = (index: number) => {
    const competitor = competitors[index];

    if (!competitor.id) {
      toast({
        title: "Error",
        description:
          "Cannot add content recommendations for a competitor without an ID",
        variant: "destructive",
      });
      return;
    }

    const { title, description, impact, estimated_hours, is_opportunity } =
      newContentRecommendation[index];

    if (!title || !description) {
      toast({
        title: "Warning",
        description:
          "Title and description are required for content recommendations",
        variant: "destructive",
      });
      return;
    }

    addContentRecommendation.mutate({
      websiteId: id,
      competitorId: competitor.id,
      title,
      description,
      impact,
      estimatedHours: estimated_hours,
      isOpportunity: is_opportunity,
    });

    // Reset the form
    setNewContentRecommendation((prev) => ({
      ...prev,
      [index]: {
        title: "",
        description: "",
        impact: "medium",
        estimated_hours: 2,
        is_opportunity: false,
      },
    }));
  };

  const handleRemoveContentRecommendation = (recommendationId: number) => {
    removeContentRecommendation.mutate({
      websiteId: id,
      recommendationId: recommendationId,
    });
  };

  // Tab handling
  const tabs = [
    {
      id: "basic",
      label: "Basic Info",
      icon: <InfoIcon className="h-4 w-4" />,
    },
    {
      id: "strategy",
      label: "Strategy and Growth",
      icon: <TargetIcon className="h-4 w-4" />,
    },
    {
      id: "seo",
      label: "SEO Metrics",
      icon: <BarChart3 className="h-4 w-4" />,
    },
    {
      id: "competitors",
      label: "Competitors",
      icon: <UsersIcon className="h-4 w-4" />,
    },
  ];

  if (status === "loading" || isLoading) {
    return (
      <PageWrapper isAdmin>
        <div>
          <div className="flex justify-center items-center min-h-[60vh]">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </div>
      </PageWrapper>
    );
  }

  if (!session?.user?.isAdmin) return null;

  if (isError) {
    return (
      <PageWrapper isAdmin>
        <div>
          <div className="flex flex-col gap-4">
            <div className="flex items-center">
              <Link href="/admin-dashboard" className="mr-4">
                <Button variant="outline" size="icon">
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              </Link>
              <h1 className="text-2xl font-bold">Website Details</h1>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Error</CardTitle>
                <CardDescription>
                  Failed to load website details
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-destructive">
                  {error instanceof Error
                    ? error.message
                    : "Unknown error occurred"}
                </p>
              </CardContent>
              <CardFooter>
                <Button
                  variant="outline"
                  onClick={() => router.push("/admin-dashboard")}
                >
                  Back to Dashboard
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </PageWrapper>
    );
  }

  return (
    <PageWrapper isAdmin>
      <div>
        <div className="flex flex-col gap-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link href="/admin-dashboard" className="mr-4">
                <Button variant="outline" size="icon">
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              </Link>
              <h1 className="text-2xl font-bold text-[#1279b4]">
                Website Details
              </h1>
            </div>

            {website && (
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-[#1279b4]">
                  {website.url}
                </Badge>
                <Badge variant="secondary" className="text-[#1279b4]">
                  Created: {format(new Date(website.created_at), "MMM d, yyyy")}
                </Badge>
              </div>
            )}
          </div>

          <Tabs defaultValue="basic-info" className="w-full">
            <TabsList className="grid grid-cols-5 mb-4">
              <TabsTrigger
                value="basic-info"
                className="flex items-center gap-2 text-[#1279b4]"
              >
                <Globe className="h-4 w-4" />
                <span>Basic Info</span>
              </TabsTrigger>
              <TabsTrigger
                value="strategy"
                className="flex items-center gap-2 text-[#1279b4]"
              >
                <TargetIcon className="h-4 w-4" />
                <span>Strategy and Growth </span>
              </TabsTrigger>
              <TabsTrigger
                value="seo"
                className="flex items-center gap-2 text-[#1279b4]"
              >
                <BarChart3 className="h-4 w-4" />
                <span>SEO Metrics</span>
              </TabsTrigger>
              <TabsTrigger
                value="competitors"
                className="flex items-center gap-2 text-[#1279b4]"
              >
                <Users2 className="h-4 w-4" />
                <span>Competitors</span>
              </TabsTrigger>
            </TabsList>

            {/* Basic Info Tab */}
            <TabsContent value="basic-info">
              <Card>
                <CardHeader>
                  <CardTitle className="text-[#1279b4]">
                    Basic Information
                  </CardTitle>
                  <CardDescription className="text-[#1279b4]">
                    Edit the basic details of the website
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="url">URL</Label>
                    <Input
                      id="url"
                      placeholder="example.com"
                      value={websiteData.url}
                      onChange={(e) =>
                        setWebsiteData({ ...websiteData, url: e.target.value })
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="industry">Industry</Label>
                    <Select
                      value={websiteData.industry}
                      onValueChange={(value) => {
                        setWebsiteData({ ...websiteData, industry: value });
                      }}
                      defaultValue={websiteData.industry}
                    >
                      <SelectTrigger id="industry">
                        <SelectValue placeholder="Select industry" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Technology">Technology</SelectItem>
                        <SelectItem value="Finance">Finance</SelectItem>
                        <SelectItem value="Healthcare">Healthcare</SelectItem>
                        <SelectItem value="Education">Education</SelectItem>
                        <SelectItem value="E-commerce">E-commerce</SelectItem>
                        <SelectItem value="Travel">Travel</SelectItem>
                        <SelectItem value="Food & Beverage">
                          Food & Beverage
                        </SelectItem>
                        <SelectItem value="Entertainment">
                          Entertainment
                        </SelectItem>
                        <SelectItem value="Fintech">Fintech</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="language">Primary Language</Label>
                    <Select
                      value={websiteData.language}
                      onValueChange={(value) =>
                        setWebsiteData({ ...websiteData, language: value })
                      }
                    >
                      <SelectTrigger id="language">
                        <SelectValue placeholder="Select language" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="en">English</SelectItem>
                        <SelectItem value="tr">Turkish</SelectItem>
                        <SelectItem value="es">Spanish</SelectItem>
                        <SelectItem value="fr">French</SelectItem>
                        <SelectItem value="de">German</SelectItem>
                        <SelectItem value="zh">Chinese</SelectItem>
                        <SelectItem value="ja">Japanese</SelectItem>
                        <SelectItem value="ar">Arabic</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button
                    onClick={handleSaveBasicInfo}
                    disabled={!!updateBasicInfo.isLoading}
                    className="bg-[#1279b4] hover:bg-[#1279b4]/80 text-white"
                  >
                    {updateBasicInfo.isLoading ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4 mr-2" />
                    )}
                    Save Changes
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            {/* Strategy & Growth Tab */}
            <TabsContent value="strategy">
              <Card>
                <CardHeader className="text-[#1279b4]">
                  <CardTitle>Strategy Gaps and Growth Opportunities</CardTitle>
                  <CardDescription className="text-[#1279b4]">
                    Manage strategy gaps and growth opportunities for this
                    website
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Strategy Gaps Section */}
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-[#1279b4]">
                        Strategy Gaps
                      </h3>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleToggleStrategyGapEditor}
                        className="h-7"
                      >
                        {editingStrategyGaps ? "Done" : "Edit Strategy Gaps"}
                      </Button>
                    </div>

                    {strategyGaps && strategyGaps.length > 0 ? (
                      <div className="space-y-2">
                        {strategyGaps.map((gap) => (
                          <div
                            key={gap.id}
                            className={`flex justify-between items-center p-2 rounded ${
                              editingStrategyGaps ? "bg-amber-50" : ""
                            }`}
                          >
                            <div className="flex items-start gap-2">
                              <LightbulbIcon className="h-4 w-4 text-amber-500 mt-1" />
                              <span>{gap.text}</span>
                            </div>

                            {editingStrategyGaps && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 text-red-500"
                                onClick={() => handleRemoveStrategyGap(gap.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground">
                        No strategy gaps identified yet. Add some to help track
                        improvement areas.
                      </p>
                    )}

                    {editingStrategyGaps && (
                      <div className="flex space-x-2 mt-4">
                        <Input
                          placeholder="Add new strategy gap"
                          value={newStrategyGapText}
                          onChange={(e) =>
                            handleStrategyGapTextChange(e.target.value)
                          }
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              e.preventDefault();
                              handleAddStrategyGap();
                            }
                          }}
                        />
                        <Button
                          size="sm"
                          onClick={handleAddStrategyGap}
                          className="bg-[#1279b4] hover:bg-[#1279b4]/80 text-white"
                        >
                          Add
                        </Button>
                      </div>
                    )}
                  </div>

                  <Separator />

                  {/* Growth Opportunities Section */}
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-[#1279b4]">
                        Growth Opportunities
                      </h3>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleToggleGrowthOpportunityEditor}
                        className="h-7"
                      >
                        {editingGrowthOpportunities
                          ? "Done"
                          : "Edit Opportunities"}
                      </Button>
                    </div>

                    {growthOpportunities && growthOpportunities.length > 0 ? (
                      <div className="space-y-2">
                        {growthOpportunities.map((opportunity) => (
                          <div
                            key={opportunity.id}
                            className={`flex justify-between items-center p-2 rounded ${
                              editingGrowthOpportunities ? "bg-green-50" : ""
                            }`}
                          >
                            <div className="flex items-start gap-2">
                              <Sprout className="h-4 w-4 text-green-500 mt-1" />
                              <span>{opportunity.text}</span>
                            </div>

                            {editingGrowthOpportunities && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 text-red-500"
                                onClick={() =>
                                  handleRemoveGrowthOpportunity(opportunity.id)
                                }
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground">
                        No growth opportunities identified yet. Add some to help
                        track potential improvements.
                      </p>
                    )}

                    {editingGrowthOpportunities && (
                      <div className="flex space-x-2 mt-4">
                        <Input
                          placeholder="Add new growth opportunity"
                          value={newGrowthOpportunityText}
                          onChange={(e) =>
                            handleGrowthOpportunityTextChange(e.target.value)
                          }
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              e.preventDefault();
                              handleAddGrowthOpportunity();
                            }
                          }}
                        />
                        <Button
                          size="sm"
                          onClick={handleAddGrowthOpportunity}
                          className="bg-[#1279b4] hover:bg-[#1279b4]/80 text-white"
                        >
                          Add
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* SEO Metrics Tab */}
            <TabsContent value="seo">
              <Card>
                <CardHeader className="text-[#1279b4] flex flex-row items-center justify-between">
                  <div>
                    <CardTitle>SEO Metrics</CardTitle>
                    <CardDescription>
                      Manage SEO metrics for the website
                    </CardDescription>
                  </div>
                  <Button
                    variant={isEditingSeoMetrics ? "destructive" : "outline"}
                    onClick={() => {
                      if (isEditingSeoMetrics) {
                        // If canceling edit, revert to original values
                        setEditedSeoMetrics({ ...seoMetrics });
                      }
                      setIsEditingSeoMetrics(!isEditingSeoMetrics);
                    }}
                  >
                    {isEditingSeoMetrics ? "Cancel" : "Edit Metrics"}
                  </Button>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Domain Rating Section */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-[#1279b4]">
                      Domain Rating
                    </h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="domain_rating_score">
                          Current Score
                        </Label>
                        <Input
                          id="domain_rating_score"
                          type="number"
                          placeholder={
                            seoMetrics.domain_rating?.score !== undefined
                              ? `${seoMetrics.domain_rating.score}`
                              : "Current domain rating"
                          }
                          disabled={!isEditingSeoMetrics}
                          value={editedSeoMetrics.domain_rating_score ?? ""}
                          onChange={(e) =>
                            setEditedSeoMetrics({
                              ...editedSeoMetrics,
                              domain_rating_score: e.target.value
                                ? parseFloat(e.target.value)
                                : null,
                            })
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="domain_rating_previous_score">
                          Previous Score
                        </Label>
                        <Input
                          id="domain_rating_previous_score"
                          type="number"
                          placeholder={
                            seoMetrics.domain_rating?.previous_score !==
                            undefined
                              ? `${seoMetrics.domain_rating.previous_score}`
                              : "Previous domain rating"
                          }
                          disabled={!isEditingSeoMetrics}
                          value={
                            editedSeoMetrics.domain_rating_previous_score ?? ""
                          }
                          onChange={(e) =>
                            setEditedSeoMetrics({
                              ...editedSeoMetrics,
                              domain_rating_previous_score: e.target.value
                                ? parseFloat(e.target.value)
                                : null,
                            })
                          }
                        />
                      </div>
                    </div>
                  </div>

                  {/* Organic Traffic Section */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-[#1279b4]">
                      Organic Traffic
                    </h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="organic_traffic_score">
                          Current Traffic
                        </Label>
                        <Input
                          id="organic_traffic_score"
                          type="number"
                          placeholder={
                            seoMetrics.organic_traffic?.score !== undefined
                              ? `${seoMetrics.organic_traffic.score}`
                              : "Current organic traffic"
                          }
                          disabled={!isEditingSeoMetrics}
                          value={editedSeoMetrics.organic_traffic_score ?? ""}
                          onChange={(e) =>
                            setEditedSeoMetrics({
                              ...editedSeoMetrics,
                              organic_traffic_score: e.target.value
                                ? parseInt(e.target.value)
                                : null,
                            })
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="organic_traffic_previous_score">
                          Previous Traffic
                        </Label>
                        <Input
                          id="organic_traffic_previous_score"
                          type="number"
                          placeholder={
                            seoMetrics.organic_traffic?.previous_score !==
                            undefined
                              ? `${seoMetrics.organic_traffic.previous_score}`
                              : "Previous organic traffic"
                          }
                          disabled={!isEditingSeoMetrics}
                          value={
                            editedSeoMetrics.organic_traffic_previous_score ??
                            ""
                          }
                          onChange={(e) =>
                            setEditedSeoMetrics({
                              ...editedSeoMetrics,
                              organic_traffic_previous_score: e.target.value
                                ? parseInt(e.target.value)
                                : null,
                            })
                          }
                        />
                      </div>
                    </div>
                  </div>

                  {/* Search Rankings Section */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-[#1279b4]">
                      Search Rankings
                    </h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="search_rankings_score">
                          Current Ranking
                        </Label>
                        <Input
                          id="search_rankings_score"
                          type="number"
                          placeholder={
                            seoMetrics.search_rankings?.score !== undefined
                              ? `${seoMetrics.search_rankings.score}%`
                              : "Current search ranking"
                          }
                          disabled={!isEditingSeoMetrics}
                          value={editedSeoMetrics.search_rankings_score ?? ""}
                          onChange={(e) =>
                            setEditedSeoMetrics({
                              ...editedSeoMetrics,
                              search_rankings_score: e.target.value
                                ? parseFloat(e.target.value)
                                : null,
                            })
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="search_rankings_previous_score">
                          Previous Ranking
                        </Label>
                        <Input
                          id="search_rankings_previous_score"
                          type="number"
                          placeholder={
                            seoMetrics.search_rankings?.previous_score !==
                            undefined
                              ? `${seoMetrics.search_rankings.previous_score}%`
                              : "Previous search ranking"
                          }
                          disabled={!isEditingSeoMetrics}
                          value={
                            editedSeoMetrics.search_rankings_previous_score ??
                            ""
                          }
                          onChange={(e) =>
                            setEditedSeoMetrics({
                              ...editedSeoMetrics,
                              search_rankings_previous_score: e.target.value
                                ? parseFloat(e.target.value)
                                : null,
                            })
                          }
                        />
                      </div>
                    </div>
                  </div>

                  {/* Search Terms Section */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-[#1279b4]">
                      Search Terms
                    </h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="search_terms_score">
                          Current Terms
                        </Label>
                        <Input
                          id="search_terms_score"
                          type="number"
                          placeholder={
                            seoMetrics.search_terms?.score !== undefined
                              ? `${seoMetrics.search_terms.score}`
                              : "Current search terms"
                          }
                          disabled={!isEditingSeoMetrics}
                          value={editedSeoMetrics.search_terms_score ?? ""}
                          onChange={(e) =>
                            setEditedSeoMetrics({
                              ...editedSeoMetrics,
                              search_terms_score: e.target.value
                                ? parseInt(e.target.value)
                                : null,
                            })
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="search_terms_previous_score">
                          Previous Terms
                        </Label>
                        <Input
                          id="search_terms_previous_score"
                          type="number"
                          placeholder={
                            seoMetrics.search_terms?.previous_score !==
                            undefined
                              ? `${seoMetrics.search_terms.previous_score}`
                              : "Previous search terms"
                          }
                          disabled={!isEditingSeoMetrics}
                          value={
                            editedSeoMetrics.search_terms_previous_score ?? ""
                          }
                          onChange={(e) =>
                            setEditedSeoMetrics({
                              ...editedSeoMetrics,
                              search_terms_previous_score: e.target.value
                                ? parseInt(e.target.value)
                                : null,
                            })
                          }
                        />
                      </div>
                    </div>
                  </div>

                  {/* Site Links Section */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-[#1279b4]">
                      Site Links
                    </h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="site_links_score">Current Links</Label>
                        <Input
                          id="site_links_score"
                          type="number"
                          placeholder="Current site links"
                          disabled={!isEditingSeoMetrics}
                          value={editedSeoMetrics.site_links_score || ""}
                          onChange={(e) =>
                            setEditedSeoMetrics({
                              ...editedSeoMetrics,
                              site_links_score: e.target.value
                                ? parseInt(e.target.value)
                                : null,
                            })
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="site_links_previous_score">
                          Previous Links
                        </Label>
                        <Input
                          id="site_links_previous_score"
                          type="number"
                          placeholder="Previous site links"
                          disabled={!isEditingSeoMetrics}
                          value={
                            editedSeoMetrics.site_links_previous_score || ""
                          }
                          onChange={(e) =>
                            setEditedSeoMetrics({
                              ...editedSeoMetrics,
                              site_links_previous_score: e.target.value
                                ? parseInt(e.target.value)
                                : null,
                            })
                          }
                        />
                      </div>
                    </div>
                  </div>

                  {/* Content Score Section */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-[#1279b4]">
                      Content Score
                    </h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="content_score">Current Score</Label>
                        <Input
                          id="content_score"
                          type="number"
                          placeholder="Current content score"
                          disabled={!isEditingSeoMetrics}
                          value={editedSeoMetrics.content_score_value || ""}
                          onChange={(e) =>
                            setEditedSeoMetrics({
                              ...editedSeoMetrics,
                              content_score_value: e.target.value
                                ? parseFloat(e.target.value)
                                : null,
                            })
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="content_score_previous_score">
                          Previous Score
                        </Label>
                        <Input
                          id="content_score_previous_score"
                          type="number"
                          placeholder="Previous content score"
                          disabled={!isEditingSeoMetrics}
                          value={
                            editedSeoMetrics.content_score_previous_score || ""
                          }
                          onChange={(e) =>
                            setEditedSeoMetrics({
                              ...editedSeoMetrics,
                              content_score_previous_score: e.target.value
                                ? parseFloat(e.target.value)
                                : null,
                            })
                          }
                        />
                      </div>
                    </div>
                  </div>

                  {/* Backlinks Section */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-[#1279b4]">
                      Backlinks
                    </h3>
                    <div className="space-y-2">
                      <Label htmlFor="backlinks_count">Backlinks Count</Label>
                      <Input
                        id="backlinks_count"
                        type="number"
                        placeholder="Number of backlinks"
                        disabled={!isEditingSeoMetrics}
                        value={editedSeoMetrics.backlinks || ""}
                        onChange={(e) =>
                          setEditedSeoMetrics({
                            ...editedSeoMetrics,
                            backlinks: e.target.value
                              ? parseInt(e.target.value)
                              : null,
                          })
                        }
                      />
                    </div>
                  </div>
                </CardContent>
                {isEditingSeoMetrics && (
                  <CardFooter>
                    <Button
                      className="bg-[#1279b4] hover:bg-[#1279b4]/80 text-white"
                      onClick={handleSaveSeoMetrics}
                      disabled={updateSEOMetrics.isLoading}
                    >
                      {updateSEOMetrics.isLoading ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Save className="h-4 w-4 mr-2" />
                      )}
                      Save SEO Metrics
                    </Button>
                  </CardFooter>
                )}
              </Card>
            </TabsContent>

            {/* Competitors Tab */}
            <TabsContent value="competitors">
              <Card>
                <CardHeader className="text-[#1279b4]">
                  <CardTitle>Website Competitors</CardTitle>
                  <CardDescription className="text-[#1279b4]">
                    Manage competitors for the website
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center mb-4">
                    <div className="text-sm text-muted-foreground">
                      {competitors.length} competitor
                      {competitors.length !== 1 ? "s" : ""}
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-[#1279b4]">Sort by:</span>
                      <Select
                        value={competitorSortBy}
                        onValueChange={(value) =>
                          setCompetitorSortBy(value as "rank" | "name" | "none")
                        }
                      >
                        <SelectTrigger className="w-[140px] h-8">
                          <SelectValue placeholder="Sort by" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">No sorting</SelectItem>
                          <SelectItem value="rank">
                            Rank (low to high)
                          </SelectItem>
                          <SelectItem value="name">Name (A-Z)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="grid grid-cols-5 gap-2 px-4 py-2 font-semibold bg-muted text-[#1279b4] rounded-lg">
                      <div>Name</div>
                      <div>Target URL</div>
                      <div>Description</div>
                      <div>Rank</div>
                      <div>Delete?</div>
                    </div>

                    {currentCompetitors.length > 0 ? (
                      currentCompetitors.map((competitor, index) => (
                        <div
                          key={index}
                          className="p-4 border rounded space-y-2"
                        >
                          {editingCompetitor === index ? (
                            // Edit mode
                            <div className="grid grid-cols-5 gap-2 items-start">
                              <div>
                                <Input
                                  value={editedCompetitor.name}
                                  onChange={(e) =>
                                    setEditedCompetitor({
                                      ...editedCompetitor,
                                      name: e.target.value,
                                    })
                                  }
                                  placeholder="Competitor name"
                                />
                              </div>
                              <div>
                                <Input
                                  value={editedCompetitor.target_url}
                                  onChange={(e) =>
                                    setEditedCompetitor({
                                      ...editedCompetitor,
                                      target_url: e.target.value,
                                    })
                                  }
                                  placeholder="https://competitor.com"
                                />
                              </div>
                              <div>
                                <Input
                                  value={editedCompetitor.description}
                                  onChange={(e) =>
                                    setEditedCompetitor({
                                      ...editedCompetitor,
                                      description: e.target.value,
                                    })
                                  }
                                  placeholder="Description"
                                />
                              </div>
                              <div>
                                <Input
                                  type="number"
                                  value={editedCompetitor.rank}
                                  onChange={(e) =>
                                    setEditedCompetitor({
                                      ...editedCompetitor,
                                      rank: parseInt(e.target.value) || 0,
                                    })
                                  }
                                  placeholder="Rank"
                                />
                              </div>
                              <div className="flex space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() =>
                                    handleSaveCompetitorEdit(index)
                                  }
                                  disabled={updateCompetitor.isLoading}
                                >
                                  {updateCompetitor.isLoading ? (
                                    <Loader2 className="h-4 animate-spin" />
                                  ) : (
                                    <Save className="h-4 w-4" />
                                  )}
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={handleCancelCompetitorEdit}
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          ) : (
                            // View mode
                            <div className="space-y-4">
                              <div className="grid grid-cols-5 gap-2 items-start">
                                <div>
                                  <h3 className="font-medium">
                                    {competitor.name}
                                  </h3>
                                  <span className="text-xs text-gray-400">
                                    ID: {competitor.id || "No ID"}
                                  </span>
                                </div>
                                <div>
                                  <a
                                    href={
                                      competitor.target_url.startsWith("http")
                                        ? competitor.target_url
                                        : `https://${competitor.target_url}`
                                    }
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-sm text-muted-foreground hover:underline flex items-center gap-1"
                                  >
                                    {competitor.target_url}
                                    <ExternalLink className="h-3 w-3" />
                                  </a>
                                </div>
                                <div>
                                  <p className="text-sm text-muted-foreground">
                                    {competitor.description ||
                                      "No description provided"}
                                  </p>
                                </div>
                                <div>
                                  <p className="text-sm font-medium">
                                    {competitor.rank || "Not set"}
                                  </p>
                                </div>
                                <div className="flex space-x-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() =>
                                      handleStartEditingCompetitor(index)
                                    }
                                  >
                                    Edit
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="text-destructive"
                                    onClick={() =>
                                      handleRemoveCompetitor(index)
                                    }
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>

                              {/* Keywords Section */}
                              <div className="mt-2 pt-2 border-t">
                                {editingKeywords[index] ? (
                                  // Edit mode for keywords
                                  <div className="space-y-4">
                                    <div className="flex justify-between items-center">
                                      <h4 className="text-sm font-medium flex items-center">
                                        <KeySquare className="h-4 w-4 mr-1" />
                                        <span>Edit Keywords</span>
                                      </h4>
                                      <div className="flex space-x-2">
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          onClick={() =>
                                            handleSaveKeywords(index)
                                          }
                                          disabled={
                                            updateCompKeywords.isLoading
                                          }
                                        >
                                          {updateCompKeywords.isLoading ? (
                                            <Loader2 className="h-3 w-3 animate-spin" />
                                          ) : (
                                            <Save className="h-3 w-3" />
                                          )}
                                          <span className="ml-1">Save</span>
                                        </Button>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() =>
                                            handleCancelEditingKeywords(index)
                                          }
                                        >
                                          <X className="h-3 w-3" />
                                          <span className="ml-1">Cancel</span>
                                        </Button>
                                      </div>
                                    </div>

                                    <div className="flex flex-wrap gap-2 p-2 bg-muted/10 rounded-md min-h-16">
                                      {(editedKeywords[index] || []).map(
                                        (keyword, keywordIndex) => (
                                          <Badge
                                            key={keywordIndex}
                                            variant="secondary"
                                            className="px-2 py-1 flex items-center"
                                          >
                                            {keyword}
                                            <button
                                              onClick={() =>
                                                handleRemoveCompetitorKeyword(
                                                  index,
                                                  keywordIndex
                                                )
                                              }
                                              className="ml-1 text-muted-foreground hover:text-destructive"
                                            >
                                              <X className="h-3 w-3" />
                                            </button>
                                          </Badge>
                                        )
                                      )}
                                      {(editedKeywords[index] || []).length ===
                                        0 && (
                                        <p className="text-xs text-muted-foreground p-2">
                                          No keywords added yet
                                        </p>
                                      )}
                                    </div>

                                    <div className="flex space-x-2">
                                      <Input
                                        placeholder="Add new keyword"
                                        value={newKeywordText[index] || ""}
                                        onChange={(e) =>
                                          handleKeywordTextChange(
                                            index,
                                            e.target.value
                                          )
                                        }
                                        className="text-sm"
                                        onKeyPress={(e) => {
                                          if (e.key === "Enter") {
                                            e.preventDefault();
                                            handleAddNewKeyword(index);
                                          }
                                        }}
                                      />
                                      <Button
                                        size="sm"
                                        onClick={() =>
                                          handleAddNewKeyword(index)
                                        }
                                      >
                                        <PlusCircle className="h-3 w-3 mr-1" />
                                        Add
                                      </Button>
                                    </div>
                                  </div>
                                ) : (
                                  // View mode for keywords - direct display instead of collapsible
                                  <div>
                                    <div className="flex justify-between items-center">
                                      <div className="flex items-center gap-1">
                                        <KeySquare className="h-4 w-4" />
                                        <span className="font-medium text-sm">
                                          Top Keywords (
                                          {competitor.top_keywords?.length || 0}
                                          )
                                        </span>
                                      </div>

                                      {competitor.id && (
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          onClick={() =>
                                            handleStartEditingKeywords(index)
                                          }
                                          className="h-7"
                                        >
                                          Edit Keywords
                                        </Button>
                                      )}
                                    </div>

                                    <div className="flex flex-wrap gap-2 mt-2 p-2 bg-muted/20 rounded-md">
                                      {competitor.top_keywords &&
                                      competitor.top_keywords.length > 0 ? (
                                        competitor.top_keywords.map(
                                          (keyword, keywordIndex) => (
                                            <Badge
                                              key={keywordIndex}
                                              variant="secondary"
                                              className="px-2 py-1"
                                            >
                                              {keyword}
                                            </Badge>
                                          )
                                        )
                                      ) : (
                                        <p className="text-xs text-muted-foreground">
                                          No keywords added yet
                                        </p>
                                      )}

                                      {/* --- CSV upload for competitor keywords --- */}
                                      <div className="mt-2 flex items-center gap-2">
                                        <CompetitorKeywordsCsvUpload
                                          websiteId={id}
                                          competitorId={competitor.id}
                                          onSuccess={(added_count: number) => {
                                            toast({
                                              title: "Keywords uploaded!",
                                              description: `Imported ${added_count} keywords from CSV for ${competitor.name}.`,
                                              variant: "default",
                                            });
                                            refetch();
                                          }}
                                        />
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </div>

                              {/* After the keywords section, right before closing the competitor view */}
                              {!editingCompetitor && (
                                <>
                                  {/* Ranking Issues Section */}
                                  <div className="mt-2 pt-2 border-t">
                                    <div className="flex justify-between items-center">
                                      <h4 className="text-sm font-medium flex items-center">
                                        <AlertTriangle className="h-4 w-4 mr-1 text-amber-500" />
                                        <span>Ranking Issues</span>
                                      </h4>
                                      {competitor.id && (
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          onClick={() =>
                                            handleToggleRankingIssuesEditor(
                                              index
                                            )
                                          }
                                          className="h-7"
                                        >
                                          {editingRankingIssues[index]
                                            ? "Done"
                                            : "Edit Issues"}
                                        </Button>
                                      )}
                                    </div>

                                    {editingRankingIssues[index] ? (
                                      <div className="space-y-4 mt-2">
                                        {/* Display current ranking issues */}
                                        {competitor.ranking_issues &&
                                        competitor.ranking_issues.length > 0 ? (
                                          <div className="space-y-2">
                                            {competitor.ranking_issues.map(
                                              (issue) => (
                                                <div
                                                  key={issue.id}
                                                  className="bg-amber-50 p-3 rounded shadow-sm relative"
                                                >
                                                  <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    className="absolute top-2 right-2 text-red-500 h-6 w-6 p-0"
                                                    onClick={() =>
                                                      handleRemoveRankingIssue(
                                                        issue.id
                                                      )
                                                    }
                                                  >
                                                    <Trash2 className="h-4 w-4" />
                                                  </Button>
                                                  <div className="pr-8">
                                                    <h5 className="font-medium mb-1 flex items-center gap-1">
                                                      <span>{issue.title}</span>
                                                      <Badge
                                                        variant={
                                                          issue.impact ===
                                                          "high"
                                                            ? "destructive"
                                                            : issue.impact ===
                                                                "low"
                                                              ? "outline"
                                                              : "secondary"
                                                        }
                                                        className="ml-2"
                                                      >
                                                        {issue.impact
                                                          .charAt(0)
                                                          .toUpperCase() +
                                                          issue.impact.slice(1)}
                                                      </Badge>
                                                    </h5>
                                                    <p className="text-sm text-gray-600">
                                                      {issue.description}
                                                    </p>
                                                  </div>
                                                </div>
                                              )
                                            )}
                                          </div>
                                        ) : (
                                          <p className="text-sm text-gray-500 italic">
                                            No issues found
                                          </p>
                                        )}

                                        {/* Form to add new ranking issue */}
                                        <div className="bg-gray-50 p-3 rounded space-y-3">
                                          <h5 className="font-medium">
                                            Add New Ranking Issue
                                          </h5>
                                          <Input
                                            placeholder="Issue title"
                                            value={
                                              newRankingIssue[index]?.title ||
                                              ""
                                            }
                                            onChange={(e) =>
                                              setNewRankingIssue((prev) => ({
                                                ...prev,
                                                [index]: {
                                                  ...prev[index],
                                                  title: e.target.value,
                                                },
                                              }))
                                            }
                                            className="mb-2"
                                          />
                                          <Textarea
                                            placeholder="Issue description"
                                            value={
                                              newRankingIssue[index]
                                                ?.description || ""
                                            }
                                            onChange={(e) =>
                                              setNewRankingIssue((prev) => ({
                                                ...prev,
                                                [index]: {
                                                  ...prev[index],
                                                  description: e.target.value,
                                                },
                                              }))
                                            }
                                            className="mb-2"
                                          />
                                          <div className="flex items-center space-x-4">
                                            <Label
                                              htmlFor={`issue-impact-${index}`}
                                            >
                                              Impact:
                                            </Label>
                                            <Select
                                              value={
                                                newRankingIssue[index]
                                                  ?.impact || "medium"
                                              }
                                              onValueChange={(value) =>
                                                setNewRankingIssue((prev) => ({
                                                  ...prev,
                                                  [index]: {
                                                    ...prev[index],
                                                    impact: value as
                                                      | "high"
                                                      | "medium"
                                                      | "low",
                                                  },
                                                }))
                                              }
                                            >
                                              <SelectTrigger
                                                id={`issue-impact-${index}`}
                                                className="w-32"
                                              >
                                                <SelectValue placeholder="Select impact" />
                                              </SelectTrigger>
                                              <SelectContent>
                                                <SelectItem value="high">
                                                  High
                                                </SelectItem>
                                                <SelectItem value="medium">
                                                  Medium
                                                </SelectItem>
                                                <SelectItem value="low">
                                                  Low
                                                </SelectItem>
                                              </SelectContent>
                                            </Select>
                                          </div>
                                          <Button
                                            onClick={() =>
                                              handleAddRankingIssue(index)
                                            }
                                            className="mt-2"
                                          >
                                            Add Issue
                                          </Button>
                                        </div>
                                      </div>
                                    ) : (
                                      // Display issues in view mode
                                      <div className="mt-2">
                                        {competitor.ranking_issues &&
                                        competitor.ranking_issues.length > 0 ? (
                                          <div className="space-y-2">
                                            {competitor.ranking_issues
                                              .slice(0, 2)
                                              .map((issue) => (
                                                <div
                                                  key={issue.id}
                                                  className="bg-amber-50 p-3 rounded shadow-sm"
                                                >
                                                  <h5 className="font-medium mb-1 flex items-center gap-1">
                                                    <span>{issue.title}</span>
                                                    <Badge
                                                      variant={
                                                        issue.impact === "high"
                                                          ? "destructive"
                                                          : issue.impact ===
                                                              "low"
                                                            ? "outline"
                                                            : "secondary"
                                                      }
                                                      className="ml-2"
                                                    >
                                                      {issue.impact
                                                        .charAt(0)
                                                        .toUpperCase() +
                                                        issue.impact.slice(1)}
                                                    </Badge>
                                                  </h5>
                                                  <p className="text-sm text-gray-600">
                                                    {issue.description}
                                                  </p>
                                                </div>
                                              ))}
                                            {competitor.ranking_issues.length >
                                              2 && (
                                              <Button
                                                variant="ghost"
                                                size="sm"
                                                className="text-xs"
                                                onClick={() =>
                                                  handleToggleRankingIssuesEditor(
                                                    index
                                                  )
                                                }
                                              >
                                                +
                                                {competitor.ranking_issues
                                                  .length - 2}{" "}
                                                more issues
                                              </Button>
                                            )}
                                          </div>
                                        ) : (
                                          <p className="text-sm text-gray-500 italic">
                                            No issues found
                                          </p>
                                        )}
                                      </div>
                                    )}
                                  </div>

                                  {/* Content Recommendations Section */}
                                  <div className="mt-4 pt-2 border-t">
                                    <div className="flex justify-between items-center">
                                      <h4 className="text-sm font-medium flex items-center">
                                        <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
                                        <span>Content Recommendations</span>
                                      </h4>
                                      {competitor.id && (
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          onClick={() =>
                                            handleToggleContentRecommendationsEditor(
                                              index
                                            )
                                          }
                                          className="h-7"
                                        >
                                          {editingContentRecommendations[index]
                                            ? "Done"
                                            : "Edit Recommendations"}
                                        </Button>
                                      )}
                                    </div>

                                    {editingContentRecommendations[index] ? (
                                      <div className="space-y-4 mt-2">
                                        {/* Display current content recommendations */}
                                        {competitor.content_recommendations &&
                                        competitor.content_recommendations
                                          .length > 0 ? (
                                          <div className="space-y-2">
                                            {competitor.content_recommendations.map(
                                              (recommendation) => (
                                                <div
                                                  key={recommendation.id}
                                                  className={`p-3 rounded shadow-sm relative ${
                                                    recommendation.is_opportunity
                                                      ? "bg-amber-50 border border-amber-200"
                                                      : "bg-white border"
                                                  }`}
                                                >
                                                  <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    className="absolute top-2 right-2 text-red-500 h-6 w-6 p-0"
                                                    onClick={() =>
                                                      handleRemoveContentRecommendation(
                                                        recommendation.id
                                                      )
                                                    }
                                                  >
                                                    <Trash2 className="h-4 w-4" />
                                                  </Button>
                                                  <div className="pr-8 space-y-1">
                                                    <div className="flex justify-between">
                                                      <h5 className="font-medium mb-1">
                                                        {recommendation.title}
                                                      </h5>
                                                      <div className="flex items-center gap-2">
                                                        <Badge
                                                          variant={
                                                            recommendation.impact ===
                                                            "high"
                                                              ? "default"
                                                              : recommendation.impact ===
                                                                  "low"
                                                                ? "outline"
                                                                : "secondary"
                                                          }
                                                        >
                                                          {recommendation.impact
                                                            .charAt(0)
                                                            .toUpperCase() +
                                                            recommendation.impact.slice(
                                                              1
                                                            )}
                                                        </Badge>
                                                        {recommendation.is_opportunity && (
                                                          <Badge
                                                            variant="outline"
                                                            className="bg-amber-50"
                                                          >
                                                            Opportunity
                                                          </Badge>
                                                        )}
                                                      </div>
                                                    </div>
                                                    <p className="text-sm text-gray-600">
                                                      {
                                                        recommendation.description
                                                      }
                                                    </p>
                                                    <div className="text-xs text-gray-500">
                                                      Est. time:{" "}
                                                      {
                                                        recommendation.estimated_hours
                                                      }{" "}
                                                      hrs
                                                    </div>
                                                  </div>
                                                </div>
                                              )
                                            )}
                                          </div>
                                        ) : (
                                          <p className="text-sm text-gray-500 italic">
                                            No recommendations found
                                          </p>
                                        )}

                                        {/* Form to add new content recommendation */}
                                        <div className="bg-gray-50 p-3 rounded space-y-3">
                                          <h5 className="font-medium">
                                            Add New Recommendation
                                          </h5>
                                          <Input
                                            placeholder="Recommendation title"
                                            value={
                                              newContentRecommendation[index]
                                                ?.title || ""
                                            }
                                            onChange={(e) =>
                                              setNewContentRecommendation(
                                                (prev) => ({
                                                  ...prev,
                                                  [index]: {
                                                    ...prev[index],
                                                    title: e.target.value,
                                                  },
                                                })
                                              )
                                            }
                                            className="mb-2"
                                          />
                                          <Textarea
                                            placeholder="Recommendation description"
                                            value={
                                              newContentRecommendation[index]
                                                ?.description || ""
                                            }
                                            onChange={(e) =>
                                              setNewContentRecommendation(
                                                (prev) => ({
                                                  ...prev,
                                                  [index]: {
                                                    ...prev[index],
                                                    description: e.target.value,
                                                  },
                                                })
                                              )
                                            }
                                            className="mb-2"
                                          />
                                          <div className="grid grid-cols-2 gap-4 mb-2">
                                            <div className="space-y-2">
                                              <Label
                                                htmlFor={`rec-impact-${index}`}
                                              >
                                                Impact:
                                              </Label>
                                              <Select
                                                value={
                                                  newContentRecommendation[
                                                    index
                                                  ]?.impact || "medium"
                                                }
                                                onValueChange={(value) =>
                                                  setNewContentRecommendation(
                                                    (prev) => ({
                                                      ...prev,
                                                      [index]: {
                                                        ...prev[index],
                                                        impact: value as
                                                          | "high"
                                                          | "medium"
                                                          | "low",
                                                      },
                                                    })
                                                  )
                                                }
                                              >
                                                <SelectTrigger
                                                  id={`rec-impact-${index}`}
                                                >
                                                  <SelectValue placeholder="Select impact" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                  <SelectItem value="high">
                                                    High
                                                  </SelectItem>
                                                  <SelectItem value="medium">
                                                    Medium
                                                  </SelectItem>
                                                  <SelectItem value="low">
                                                    Low
                                                  </SelectItem>
                                                </SelectContent>
                                              </Select>
                                            </div>
                                            <div className="space-y-2">
                                              <Label
                                                htmlFor={`rec-hours-${index}`}
                                              >
                                                Est. Hours:
                                              </Label>
                                              <Input
                                                id={`rec-hours-${index}`}
                                                type="number"
                                                min="1"
                                                value={
                                                  newContentRecommendation[
                                                    index
                                                  ]?.estimated_hours || 2
                                                }
                                                onChange={(e) =>
                                                  setNewContentRecommendation(
                                                    (prev) => ({
                                                      ...prev,
                                                      [index]: {
                                                        ...prev[index],
                                                        estimated_hours:
                                                          parseInt(
                                                            e.target.value
                                                          ) || 2,
                                                      },
                                                    })
                                                  )
                                                }
                                              />
                                            </div>
                                          </div>
                                          <div className="flex items-center space-x-2">
                                            <Checkbox
                                              id={`is-opportunity-${index}`}
                                              checked={
                                                newContentRecommendation[index]
                                                  ?.is_opportunity || false
                                              }
                                              onCheckedChange={(checked) =>
                                                setNewContentRecommendation(
                                                  (prev) => ({
                                                    ...prev,
                                                    [index]: {
                                                      ...prev[index],
                                                      is_opportunity:
                                                        checked === true,
                                                    },
                                                  })
                                                )
                                              }
                                            />
                                            <Label
                                              htmlFor={`is-opportunity-${index}`}
                                            >
                                              Mark as special opportunity
                                            </Label>
                                          </div>
                                          <Button
                                            onClick={() =>
                                              handleAddContentRecommendation(
                                                index
                                              )
                                            }
                                            className="mt-2"
                                          >
                                            Add Recommendation
                                          </Button>
                                        </div>
                                      </div>
                                    ) : (
                                      // Display recommendations in view mode
                                      <div className="mt-2">
                                        {competitor.content_recommendations &&
                                        competitor.content_recommendations
                                          .length > 0 ? (
                                          <div className="space-y-2">
                                            {competitor.content_recommendations
                                              .slice(0, 2)
                                              .map((recommendation) => (
                                                <div
                                                  key={recommendation.id}
                                                  className={`p-3 rounded shadow-sm ${
                                                    recommendation.is_opportunity
                                                      ? "bg-amber-50 border border-amber-200"
                                                      : "bg-white border"
                                                  }`}
                                                >
                                                  <div className="flex justify-between items-start">
                                                    <h5 className="font-medium mb-1">
                                                      {recommendation.title}
                                                    </h5>
                                                    <Badge
                                                      variant={
                                                        recommendation.impact ===
                                                        "high"
                                                          ? "default"
                                                          : recommendation.impact ===
                                                              "low"
                                                            ? "outline"
                                                            : "secondary"
                                                      }
                                                    >
                                                      {recommendation.impact
                                                        .charAt(0)
                                                        .toUpperCase() +
                                                        recommendation.impact.slice(
                                                          1
                                                        )}
                                                    </Badge>
                                                  </div>
                                                  <p className="text-sm text-gray-600">
                                                    {recommendation.description}
                                                  </p>
                                                  <div className="text-xs text-gray-500 mt-1">
                                                    Est. time:{" "}
                                                    {
                                                      recommendation.estimated_hours
                                                    }{" "}
                                                    hrs
                                                  </div>
                                                </div>
                                              ))}
                                            {competitor.content_recommendations
                                              .length > 2 && (
                                              <Button
                                                variant="ghost"
                                                size="sm"
                                                className="text-xs"
                                                onClick={() =>
                                                  handleToggleContentRecommendationsEditor(
                                                    index
                                                  )
                                                }
                                              >
                                                +
                                                {competitor
                                                  .content_recommendations
                                                  .length - 2}{" "}
                                                more recommendations
                                              </Button>
                                            )}
                                          </div>
                                        ) : (
                                          <p className="text-sm text-gray-500 italic">
                                            No recommendations found
                                          </p>
                                        )}
                                      </div>
                                    )}
                                  </div>
                                </>
                              )}
                            </div>
                          )}
                        </div>
                      ))
                    ) : (
                      <p className="text-muted-foreground">
                        No competitors added yet
                      </p>
                    )}
                  </div>
                  {/* Pagination for competitors list */}
                  {competitors.length > competitorsPerPage && (
                    <div className="flex items-center justify-center space-x-2 py-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          setCurrentCompetitorPage((prev) =>
                            Math.max(prev - 1, 1)
                          )
                        }
                        disabled={currentCompetitorPage === 1}
                        className="text-[#1279b4] border-[#1279b4]/20 hover:bg-[#1279b4]/5"
                      >
                        <ChevronLeft className="h-4 w-4" />
                        <span className="sr-only">Previous Page</span>
                      </Button>

                      <div className="flex items-center gap-1">
                        {Array.from(
                          { length: totalCompetitorPages },
                          (_, i) => i + 1
                        ).map((page) => (
                          <Button
                            key={page}
                            variant={
                              currentCompetitorPage === page
                                ? "default"
                                : "outline"
                            }
                            size="sm"
                            onClick={() => setCurrentCompetitorPage(page)}
                            className={
                              currentCompetitorPage === page
                                ? "bg-[#1279b4] hover:bg-[#1279b4]/90 text-white"
                                : "text-[#1279b4] border-[#1279b4]/20 hover:bg-[#1279b4]/5"
                            }
                          >
                            {page}
                          </Button>
                        ))}
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          setCurrentCompetitorPage((prev) =>
                            Math.min(prev + 1, totalCompetitorPages)
                          )
                        }
                        disabled={
                          currentCompetitorPage === totalCompetitorPages
                        }
                        className="text-[#1279b4] border-[#1279b4]/20 hover:bg-[#1279b4]/5"
                      >
                        <ChevronRight className="h-4 w-4" />
                        <span className="sr-only">Next Page</span>
                      </Button>
                    </div>
                  )}

                  {/* CSV Upload for Competitors */}
                  <div className="space-y-4 pt-4 border-t">
                    <h3 className="font-medium text-[#1279b4]">
                      Upload Competitors CSV
                    </h3>
                    <form
                      className="flex flex-col md:flex-row items-start md:items-end gap-4"
                      onSubmit={async (e) => {
                        e.preventDefault();
                        if (!csvFile) return;
                        uploadCsv.mutate(
                          { websiteId: id, file: csvFile },
                          {
                            onSuccess: (data: { added_count: number }) => {
                              toast({
                                title: "Competitors uploaded!",
                                description: `Imported ${data.added_count} competitors from CSV.`,
                                variant: "default",
                              });
                              setCsvFile(undefined);
                              refetch();
                            },
                            onError: (err: any) => {
                              toast({
                                title: "Upload failed",
                                description:
                                  err?.response?.data?.error || err.message,
                                variant: "destructive",
                              });
                            },
                          }
                        );
                      }}
                    >
                      <Input
                        type="file"
                        accept=".csv,text/csv"
                        onChange={(e) => {
                          if (e.target.files && e.target.files[0]) {
                            setCsvFile(e.target.files[0]);
                          }
                        }}
                        className="w-full md:w-auto"
                      />
                      <Button
                        type="submit"
                        disabled={!csvFile || uploadCsv.isLoading}
                        className="w-full md:w-auto bg-[#1279b4] hover:bg-[#1279b4]/80 text-white"
                      >
                        {uploadCsv.isLoading ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : null}
                        Upload CSV
                      </Button>
                      {csvFile && (
                        <span className="text-xs text-muted-foreground">
                          {csvFile.name}
                        </span>
                      )}
                    </form>

                    <h3 className="font-medium">Add New Competitor</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label
                          htmlFor="competitor-name"
                          className="text-[#1279b4]"
                        >
                          Name
                        </Label>
                        <Input
                          id="competitor-name"
                          placeholder="Competitor name"
                          value={newCompetitor.name}
                          onChange={(e) =>
                            setNewCompetitor({
                              ...newCompetitor,
                              name: e.target.value,
                            })
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label
                          htmlFor="competitor-url"
                          className="text-[#1279b4]"
                        >
                          URL
                        </Label>
                        <Input
                          id="competitor-url"
                          placeholder="competitor.com"
                          value={newCompetitor.target_url}
                          onChange={(e) =>
                            setNewCompetitor({
                              ...newCompetitor,
                              target_url: e.target.value,
                            })
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label
                          htmlFor="competitor-description"
                          className="text-[#1279b4]"
                        >
                          Description
                        </Label>
                        <Input
                          id="competitor-description"
                          placeholder="Description of the competitor"
                          value={newCompetitor.description}
                          onChange={(e) =>
                            setNewCompetitor({
                              ...newCompetitor,
                              description: e.target.value,
                            })
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label
                          htmlFor="competitor-rank"
                          className="text-[#1279b4]"
                        >
                          Rank
                        </Label>
                        <Input
                          id="competitor-rank"
                          type="number"
                          placeholder="Competitor rank"
                          value={newCompetitor.rank || ""}
                          onChange={(e) =>
                            setNewCompetitor({
                              ...newCompetitor,
                              rank: parseInt(e.target.value) || 0,
                            })
                          }
                        />
                      </div>
                    </div>
                    <Button
                      onClick={handleAddCompetitor}
                      disabled={!!addCompetitor.isLoading}
                      className="bg-[#1279b4] hover:bg-[#1279b4]/80 text-white"
                    >
                      {addCompetitor.isLoading ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <PlusCircle className="h-4 w-4 mr-2" />
                      )}
                      Add Competitor
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </PageWrapper>
  );
}
