// app/admin-dashboard/user-management/[id]/page.tsx
"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useParams } from "next/navigation";
import PageWrapper from "@/components/PageWrapper";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Save } from "lucide-react";
import { useFetchUserById, useUpdateUser } from "@/internal-api/admin-user";
import {
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  <PERSON><PERSON> as MUIButton,
} from "@mui/material";

export default function EditUserPage() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalType, setModalType] = useState("");

  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const userId = Number(params?.id);
  const { data: user, isLoading, isError } = useFetchUserById(userId);

  const updateUserMutation = useUpdateUser();
  const [formData, setFormData] = useState({
    username: "",
    first_name: "",
    last_name: "",
    email: "",
    role: "User",
  });

  useEffect(() => {
    if (status === "loading") return;
    if (!session) {
      router.push("/login");
      return;
    }
    if (!session.user?.isAdmin) {
      router.push("/dashboard");
    }
  }, [session, status, router]);

  useEffect(() => {
    if (user) {
      setFormData({
        username: user.username || "",
        first_name: user.first_name || "",
        last_name: user.last_name || "",
        email: user.email || "",
        role: user.role || "user",
      });
    }
  }, [user]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    setFormData((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.role) {
      setFormData((prev) => ({ ...prev, role: "user" }));
    }
    setModalType("confirm");
    setIsModalOpen(true);

    try {
      await updateUserMutation.mutateAsync({
        userId,
        userData: formData,
      });
      router.push("/admin-dashboard/user-management");
    } catch (error) {
      console.error("Failed to update user:", error);
    }
  };

  const handleConfirmUpdate = async () => {
    setIsModalOpen(false); // Close the confirmation modal
    try {
      await updateUserMutation.mutateAsync({
        userId,
        userData: formData,
      });
      // Open the success modal
      setModalType("success");
      setIsModalOpen(true);
    } catch (error) {
      console.error("Failed to update user:", error);
      // Optionally display an error message to the user
    }
  };

  if (status === "loading" || isLoading) {
    return (
      <PageWrapper isAdmin>
        <Container /*className="container mx-auto p-6"*/>
          <Card>
            <CardContent className="space-y-4">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="h-10 animate-pulse bg-muted rounded" />
              ))}
            </CardContent>
          </Card>
        </Container>
      </PageWrapper>
    );
  }

  if (isError) {
    return (
      <PageWrapper isAdmin>
        <Container /*className="container mx-auto p-6"*/>
          <Alert variant="destructive">
            <AlertDescription>Error fetching user data.</AlertDescription>
          </Alert>
        </Container>
      </PageWrapper>
    );
  }

  return (
    <PageWrapper isAdmin>
      <Container>
        <form onSubmit={handleSubmit}>
          <Card>
            <CardHeader>
              <CardTitle>Edit User</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Form fields */}
              <div className="space-y-2">
                <Label htmlFor="first_name">First Name</Label>
                <Input
                  id="first_name"
                  name="first_name"
                  value={formData.first_name}
                  onChange={handleInputChange}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="last_name">Last Name</Label>
                <Input
                  id="last_name"
                  name="last_name"
                  value={formData.last_name}
                  onChange={handleInputChange}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Select
                  value={formData.role}
                  onValueChange={(value) =>
                    setFormData((prev) => ({ ...prev, role: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user">User</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="sub_admin">Sub Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Dialog open={isModalOpen} onClose={() => setIsModalOpen(false)}>
                {modalType === "confirm" && (
                  <>
                    <DialogTitle>Confirm Update</DialogTitle>
                    <DialogContent>
                      <DialogContentText>
                        Are you sure you want to update this user's information?
                      </DialogContentText>
                    </DialogContent>
                    <DialogActions>
                      <MUIButton onClick={() => setIsModalOpen(false)}>
                        Cancel
                      </MUIButton>
                      <MUIButton onClick={handleConfirmUpdate} autoFocus>
                        Confirm
                      </MUIButton>
                    </DialogActions>
                  </>
                )}
                {modalType === "success" && (
                  <>
                    <DialogTitle>Update Successful</DialogTitle>
                    <DialogContent>
                      <DialogContentText>
                        The user's information has been successfully updated.
                      </DialogContentText>
                    </DialogContent>
                    <DialogActions>
                      <MUIButton
                        onClick={() => {
                          setIsModalOpen(false);
                          router.push("/admin-dashboard/user-management");
                        }}
                      >
                        OK
                      </MUIButton>
                    </DialogActions>
                  </>
                )}
              </Dialog>

              <Button
                type="submit"
                disabled={updateUserMutation.isLoading}
                className="w-full"
              >
                {updateUserMutation.isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </form>
      </Container>
    </PageWrapper>
  );
}
