import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { Container } from "@mui/material";
import { AiContentGeneration } from "@/components/AiContentGeneration";
import PageWrapper from "@/components/PageWrapper";
import type { ContentGenerationData } from "@/types/content-generation";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";

export const metadata: Metadata = {
  title: "AI Content Generation | Dashboard",
  description: "Generate optimized content with AI assistance",
};

// Default data structure
const defaultData: ContentGenerationData = {
  //estimatedTraffic: "Estimated Monthly Traffic",
  //potentialVisitors: "+1.2K potential visitors",
  estimatedTraffic: "",
  potentialVisitors: "",
  contentTypes: [
    { id: "blog", label: "Blog Post", icon: "blog" },
    { id: "learn", label: "Learn Page", icon: "learn" },
    { id: "faq", label: "FAQ Page", icon: "faq" },
    { id: "meta", label: "Meta Content", icon: "meta" },
  ],
  targetKeywords: [],
  performancePrediction: {
    potentialTraffic: {
      range: "320-450",
      visitors: "monthly visitors",
      confidence: "High confidence",
    },
    rankingPotential: {
      range: "Top 3-5",
      target: "for target keywords",
      probability: "85% probable",
    },
  },
  timeline: [
    {
      week: "Week 1",
      description: "Indexing & Initial Rankings",
      visitors: "",
    },
    { week: "Week 2", description: "~150 visitors", visitors: "150" },
    { week: "Week 3-4", description: "220-450 visitors", visitors: "450" },
  ],
  successFactors:
    "Content quality, internal linking, and social shares can significantly impact these predictions.",
};

export default function AiContentPage({
  searchParams,
}: {
  searchParams: { keyword?: string };
}) {
  // If a keyword is provided, merge it with the default data
  const initialData: ContentGenerationData | undefined = searchParams.keyword
    ? {
        ...defaultData,
        targetKeywords: [
          {
            keyword: searchParams.keyword,
            monthlyVolume: "5.2K",
            potential: "+320",
          },
        ],
      }
    : undefined;

  return (
    <PageWrapper>
      <div className="mb-6">
        <Link href="/seo-insights">
          <Button variant="outline" className="flex items-center gap-2">
            <ChevronLeft className="h-4 w-4" />
            <span>Back to SEO Insights</span>
          </Button>
        </Link>
      </div>
      <AiContentGeneration data={initialData} />
    </PageWrapper>
  );
}
