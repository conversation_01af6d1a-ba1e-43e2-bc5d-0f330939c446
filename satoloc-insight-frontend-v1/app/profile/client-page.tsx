//app/profile/client-page.tsx
"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import PageWrapper from "@/components/PageWrapper";
import UserProfileComponent from "@/components/UserComponents/UserProfileComponent";
import UpdateProfileForm from "@/components/UserComponents/UpdateProfileForm";
import SubUsersComponent from "@/components/UserComponents/SubUsersComponent";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card } from "@/components/ui/card";
import { useUserData } from "@/internal-api/get-user-data";

export default function ProfileClientPage() {
  const [isEditing, setIsEditing] = useState(false);
  const [showPaymentSuccess, setShowPaymentSuccess] = useState(false);
  const router = useRouter();
  const { data: session, status } = useSession();
  const { data: userData, error, isLoading } = useUserData();

  // Merge session subscription data with user data and convert to camelCase
  const enhancedUserData = userData
    ? {
        ...userData,
        // Convert snake_case to camelCase for subscription fields
        subscriptionPlan:
          session?.user?.subscription_plan || userData.subscription_plan,
        subscriptionPrice:
          session?.user?.subscription_price || userData.subscription_price,
        subscriptionStartDate:
          session?.user?.subscription_start_date ||
          userData.subscription_start_date,
      }
    : null;

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  // Check for payment success in URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get("payment_success") === "true") {
      setShowPaymentSuccess(true);
      // Clean up URL parameters
      const newUrl = window.location.pathname;
      window.history.replaceState({}, "", newUrl);
      // Hide success message after 5 seconds
      setTimeout(() => setShowPaymentSuccess(false), 5000);
    }
  }, []);

  const handleEditProfile = () => setIsEditing(true);
  const handleUpdateSuccess = () => setIsEditing(false);

  if (status === "loading" || isLoading) {
    return (
      <PageWrapper>
        <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
          <div className=" mx-auto px-4 py-8 max-w-6xl">
            {/* Header Skeleton */}
            <div className="mb-8">
              <Skeleton className="h-8 w-48 mb-2" />
              <Skeleton className="h-4 w-64" />
            </div>

            {/* Main Content Skeleton */}
            <div className="grid gap-8 lg:grid-cols-3">
              <div className="lg:col-span-2 space-y-6">
                <Card className="p-6">
                  <Skeleton className="h-32 w-full mb-6" />
                  <div className="space-y-4">
                    <Skeleton className="h-6 w-32" />
                    <div className="grid gap-4 md:grid-cols-2">
                      <Skeleton className="h-24 w-full" />
                      <Skeleton className="h-24 w-full" />
                    </div>
                  </div>
                </Card>
              </div>

              {/* Sidebar Skeleton */}
              <div className="space-y-6">
                <Card className="p-6">
                  <Skeleton className="h-6 w-24 mb-4" />
                  <Skeleton className="h-16 w-full" />
                </Card>
                <Card className="p-6">
                  <Skeleton className="h-6 w-24 mb-4" />
                  <Skeleton className="h-12 w-full" />
                </Card>
              </div>
            </div>
          </div>
        </div>
      </PageWrapper>
    );
  }

  if (status === "unauthenticated") return null;

  if (error) {
    return (
      <PageWrapper>
        <div className="min-h-screen bg-gradient-to-br from-background via-background to-destructive/5">
          <div className=" mx-auto px-4 py-8 max-w-4xl">
            <Alert variant="destructive" className="max-w-md mx-auto">
              <AlertDescription>
                {error?.toString() ||
                  "An error occurred while loading your profile"}
              </AlertDescription>
            </Alert>
          </div>
        </div>
      </PageWrapper>
    );
  }

  return (
    <PageWrapper>
      <div>
        <div className=" mx-auto px-4 py-8 max-w-full">
          {/* Page Header */}
          <div className="mb-8">
            {/* <h1 className="text-3xl font-bold tracking-tight mb-2">
              {isEditing ? "Edit Profile" : "My Profile"}
            </h1> */}
            <p className="text-muted-foreground">
              {isEditing
                ? "Update your personal information and preferences"
                : "Manage your account information and subscription"}
            </p>
          </div>

          {/* Payment Success Message */}
          {showPaymentSuccess && (
            <Alert className="max-w-2xl mx-auto border-green-200 bg-green-50 text-green-800">
              <AlertDescription className="flex items-center gap-2">
                <span className="text-green-600">✓</span>
                <span>
                  <strong>Payment Successful!</strong> Your subscription has
                  been activated and you now have access to all premium
                  features.
                </span>
              </AlertDescription>
            </Alert>
          )}

          {/* Main Content */}
          <div className="space-y-8">
            {enhancedUserData && !isEditing && (
              <UserProfileComponent
                userData={enhancedUserData}
                onEdit={handleEditProfile}
              />
            )}

            {enhancedUserData && isEditing && (
              <div className="max-w-2xl">
                <UpdateProfileForm
                  initialFirstName={userData.first_name || ""}
                  initialLastName={userData.last_name || ""}
                  initialCompanyName={userData.company_name || ""}
                  initialWebsite={userData.website || ""}
                  initialIndustry={userData.industry || ""}
                  onUpdateSuccess={handleUpdateSuccess}
                />
              </div>
            )}

            {enhancedUserData && enhancedUserData.role === "sub_admin" && (
              <SubUsersComponent userData={enhancedUserData} />
            )}
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}
