"use client";

import { useRef, useState, useEffect } from "react";
import Image from "next/image";
import { motion, useScroll, useTransform, MotionValue } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { FloatingNavbar } from "@/components/FrontPage/FloatingNavbar";
import { FeatureCard } from "@/components/FrontPage/FeatureCard";
import { GradientText } from "@/components/FrontPage/GradientText";
import { Container } from "@mui/material";
import { Roadmap } from "@/components/FrontPage/Roadmap";
import { BetaSignupModal } from "@/components/BetaSignupModal";
import { AwardsSection } from "@/components/FrontPage/AwardsSection";
import { PricingSection } from "@/components/FrontPage/PricingSection";
import SatoLOCLogo from "@/public/images/satoloc_insight_logo.png";
import CookieConsent from "@/components/CookieConsent";
import FAQSection from "@/components/FrontPage/FAQSection";
import { NewsletterForm } from "@/components/FrontPage/NewsletterForm";
import { HeroSection } from "@/components/FrontPage/v2/HeroSection";
import { SatolocFeatures } from "@/components/FrontPage/v2/SatolocFeatures";
import { StrategySection } from "@/components/FrontPage/v2/StrategySection";
import { useHomepageContent } from "@/internal-api/wordpress-blog";
import { PartnersComponent } from "@/components/FrontPage/PartnersComponent";
import { SeoPerformance } from "@/components/FrontPage/v2/SeoPerformance";
import { IntegrationSection } from "@/components/FrontPage/v2/IntegrationSection";
import { CTASection } from "@/components/FrontPage/CTASection";
import { FooterSection } from "@/components/FrontPage/FooterSection";
import { BlogPosts } from "@/components/FrontPage/v2/BlogPosts";

import { ArrowRight, Zap } from "lucide-react";

// Floating animation for background elements
const floatingAnimation = {
  y: ["-10%", "10%"],
  transition: {
    duration: 8,
    repeat: Infinity,
    repeatType: "reverse" as const,
    ease: "easeInOut",
  },
};

function Home() {
  const { data: homepageContent, isLoading: isContentLoading } =
    useHomepageContent();
  const targetRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: targetRef,
    offset: ["start start", "end start"],
  });

  const y = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);

  // Add refs for the sections
  const featuresRef = useRef<HTMLDivElement>(null);
  const solutionsRef = useRef<HTMLDivElement>(null);
  const pricingRef = useRef<HTMLDivElement>(null);
  const faqRef = useRef<HTMLDivElement>(null);
  const roadmapRef = useRef<HTMLDivElement>(null);

  // Add a scroll function
  const scrollToSection = (ref: React.RefObject<HTMLDivElement>) => {
    ref.current?.scrollIntoView({ behavior: "smooth" });
  };

  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <div className="min-h-screen bg-background" ref={targetRef}>
      <FloatingNavbar
        onFeaturesClick={() => scrollToSection(featuresRef)}
        onSolutionsClick={() => scrollToSection(solutionsRef)}
        onPricingClick={() => scrollToSection(pricingRef)}
        onFAQClick={() => scrollToSection(faqRef)}
      />

      {/* Hero Section with Enhanced Background */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        <motion.div
          style={{ y, opacity }}
          className="absolute inset-0 w-full h-full"
        >
          {/* Enhanced gradient background */}
          <div className="absolute inset-0 " />
          <motion.div
            animate={floatingAnimation}
            className="absolute inset-0 "
          />
          {/* Animated geometric shapes */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.5 }}
            transition={{ duration: 2 }}
            className="absolute inset-0"
          ></motion.div>
        </motion.div>

        <Container className="container px-4 relative z-10 mt-[-10vh]">
          <HeroSection onJoinBetaClick={() => setIsModalOpen(true)} />
        </Container>
      </section>

      {/* Features Section with Enhanced Cards */}
      <div ref={featuresRef}>
        <SatolocFeatures />
      </div>

      {/* Strategy Section */}
      <div ref={solutionsRef}>
        <StrategySection />
      </div>

      {/* Seo Performance Section */}
      <SeoPerformance />

      {/* Integration Section */}
      <IntegrationSection />

      {/* Awards Section */}
      <AwardsSection />

      {/* Partners Section */}
      <PartnersComponent />

      {/* Roadmap Section */}
      {/* <section ref={roadmapRef} className="py-20">
        <Roadmap />
      </section> */}

      {/* Pricing Section */}
      <section className="py-20" id="pricing" ref={pricingRef}>
        <PricingSection />
      </section>
      <section ref={faqRef}>
        <FAQSection />
      </section>
      <section /*ref={blogRef}*/>
        <BlogPosts />
      </section>

      {/* CTA Section*/}
      <CTASection onJoinBetaClick={() => setIsModalOpen(true)} />

      {/* Modern Footer Section */}
      <FooterSection
        scrollToSection={scrollToSection}
        featuresRef={featuresRef}
        solutionsRef={solutionsRef}
        roadmapRef={roadmapRef}
        onJoinBetaClick={() => setIsModalOpen(true)}
      />

      <BetaSignupModal open={isModalOpen} onOpenChange={setIsModalOpen} />

      {/* Cookie Consent Component */}
      <CookieConsent />
    </div>
  );
}

export default Home;
