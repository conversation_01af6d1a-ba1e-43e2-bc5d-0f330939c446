"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import PageWrapper from "@/components/PageWrapper";
import { UserSettings } from "@/components/UserSettings";
import { UserSettingsSkeleton } from "@/components/UserSettings/UserSettingsSkeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Container } from "@mui/system";

export default function UserSettingsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    } else if (status === "authenticated") {
      // Simulate loading user settings
      setTimeout(() => setIsLoading(false), 1000);
    }
  }, [status, router]);

  if (status === "loading" || isLoading) {
    return (
      <PageWrapper>
        <Container /*className="container mx-auto p-6"*/>
          <div className="h-8 w-48 mb-6">
            <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
          </div>
          <UserSettingsSkeleton />
        </Container>
      </PageWrapper>
    );
  }

  if (status === "unauthenticated") {
    return null;
  }

  if (!session?.user) {
    return (
      <PageWrapper>
        <Container /*className="container mx-auto p-6"*/>
          <Alert variant="destructive">
            <AlertDescription>
              You must be logged in to view this page.
            </AlertDescription>
          </Alert>
        </Container>
      </PageWrapper>
    );
  }

  return (
    <PageWrapper>
      <div /*className="container mx-auto p-6"*/>
        <h1 className="text-3xl font-bold tracking-tight mb-6">Settings</h1>
        <UserSettings />
      </div>
    </PageWrapper>
  );
}
