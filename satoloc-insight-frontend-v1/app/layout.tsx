"use client";
import { ReactNode, useEffect, useState } from "react";
import { Box, CssBaseline } from "@mui/material";
import localFont from "next/font/local";
import { CustomThemeProvider } from "@/theme/themeContext";
import { ThemeProvider } from "@/components/theme-provider";
import { ReactQueryProviders } from "./react-query-provider";
import { SessionProvider } from "next-auth/react";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Toaster } from "@/components/ui/toaster";
import Head from "next/head"; // Import Head component
import "./globals.css";
import SessionHandler from "@/components/SessionHandler";
import { WpConnectionProvider } from "@/hooks/use-wp-connection";
import { GlobalProgressBar } from "@/components/GlobalProgressBar";

interface RootLayoutProps {
  children: ReactNode;
}

const Palaquin = localFont({
  src: "../fonts/Palanquin/Palanquin-Regular.woff2",
  display: "swap",
  variable: "--palaquin",
});

export default function RootLayout({ children }: RootLayoutProps) {
  const [isSessionExpired, setIsSessionExpired] = useState(false);

  useEffect(() => {
    const handleSessionExpired = () => {
      setIsSessionExpired(true);
    };

    window.addEventListener("session-expired", handleSessionExpired);
    return () => {
      window.removeEventListener("session-expired", handleSessionExpired);
    };
  }, []);

  return (
    <html lang="en" className={Palaquin.variable} suppressHydrationWarning>
      <Head>
        <link rel="icon" href="/favicon.ico" />
        <link
          rel="icon"
          type="image/png"
          sizes="32x32"
          href="/favicon-32x32.png"
        />
        <link
          rel="icon"
          type="image/png"
          sizes="16x16"
          href="/favicon-16x16.png"
        />

        {/* Apple Touch Icon */}
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />

        {/* Android Chrome Icons */}
        <link
          rel="icon"
          type="image/png"
          sizes="192x192"
          href="/android-chrome-192x192.png"
        />
        <link
          rel="icon"
          type="image/png"
          sizes="512x512"
          href="/android-chrome-512x512.png"
        />

        {/* Web Manifest */}
        <link rel="manifest" href="/site.webmanifest" />
        {/* Meta Theme Color */}
        <meta name="theme-color" content="#ffffff" />
        <meta name="description" content="Your app description here" />
      </Head>
      <body>
        <SessionProvider>
          <WpConnectionProvider>
            <CustomThemeProvider>
              <ThemeProvider
                attribute="class"
                defaultTheme="system"
                enableSystem={false}
                disableTransitionOnChange
              >
                <ReactQueryProviders>
                  <TooltipProvider>
                    <CssBaseline />
                    <Box
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        minHeight: "100vh",
                        width: "100%",
                      }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          flexDirection: "row",
                          flexGrow: 1,
                          width: "100%",
                        }}
                      >
                        <Box
                          component="main"
                          sx={{ flexGrow: 1, width: "100%", minWidth: 0 }}
                        >
                          {children}
                        </Box>
                      </Box>
                    </Box>
                    <Toaster />
                    <SessionHandler />
                    <GlobalProgressBar />
                  </TooltipProvider>
                </ReactQueryProviders>
              </ThemeProvider>
            </CustomThemeProvider>
          </WpConnectionProvider>
        </SessionProvider>
      </body>
    </html>
  );
}
