"use client";
import CustomContentClientPage from "./client-page";

export default function CustomContent() {
  return (
    <CustomContentClientPage
      onCopyContent={function (): void {
        throw new Error("Function not implemented.");
      }}
      onDownloadContent={function (): void {
        throw new Error("Function not implemented.");
      }}
      onSaveContent={function (): void {
        throw new Error("Function not implemented.");
      }}
      onRegenerate={function (): void {
        throw new Error("Function not implemented.");
      }}
    />
  );
}
