"use client";

import { useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Loader2 } from "lucide-react";

function GSCCallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Get the authorization code from the URL
    const code = searchParams?.get("code") || null;
    const error = searchParams?.get("error") || null;

    if (error) {
      // Send error message to parent window
      if (window.opener) {
        window.opener.postMessage(
          { type: "GSC_AUTH_ERROR", error },
          window.location.origin
        );
      }
      // Try to close, but handle gracefully if blocked
      try {
        window.close();
      } catch (e) {
        // Redirect to main app as fallback
        router.push("/seo-insights");
      }
      return;
    }

    if (code) {
      // Send the code to the parent window
      if (window.opener) {
        window.opener.postMessage(
          { type: "GSC_AUTH_CODE", code },
          window.location.origin
        );
      }

      // Close this window after a short delay, with fallback
      setTimeout(() => {
        try {
          window.close();
        } catch (e) {
          // Redirect to main app as fallback
          router.push("/seo-insights");
        }
      }, 1000);
    } else {
      // No code found, handle the error
      if (window.opener) {
        window.opener.postMessage(
          { type: "GSC_AUTH_ERROR", error: "No authorization code received" },
          window.location.origin
        );
      }
      try {
        window.close();
      } catch (e) {
        router.push("/seo-insights");
      }
    }
  }, [searchParams, router]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <Loader2 className="h-8 w-8 animate-spin text-blue-600 mb-4" />
      <h1 className="text-xl font-semibold">Processing authentication...</h1>
      <p className="text-gray-500 mt-2">
        This window will close automatically.
      </p>
    </div>
  );
}

export default function GSCCallbackPage() {
  return (
    <Suspense
      fallback={
        <div className="flex flex-col items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mb-4" />
          <h1 className="text-xl font-semibold">Loading...</h1>
        </div>
      }
    >
      <GSCCallbackContent />
    </Suspense>
  );
}
