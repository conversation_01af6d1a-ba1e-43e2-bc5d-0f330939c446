// app/api/ai-report/route.ts

export const dynamic = "force-dynamic";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/authOptions";
import apiClient from "@/lib/apiClient";

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get("project");

    if (!session?.accessToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (!projectId) {
      return NextResponse.json(
        { error: "Project ID is required" },
        { status: 400 }
      );
    }

    const response = await apiClient.get(`/scraping/ai-reports/`, {
      params: { project: projectId },
      headers: {
        Authorization: `Bearer ${session.accessToken}`,
      },
    });

    return NextResponse.json(response.data);
  } catch (error: any) {
    console.error("Error fetching AI report:", error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch AI report" },
      { status: error.response?.status || 500 }
    );
  }
}
