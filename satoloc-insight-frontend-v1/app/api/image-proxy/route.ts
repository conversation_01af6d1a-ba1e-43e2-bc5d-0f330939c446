export const dynamic = "force-dynamic";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const url = request.nextUrl.searchParams.get("url");

    if (!url) {
      return new NextResponse("Missing URL parameter", { status: 400 });
    }

    // Validate the URL is from OpenAI's domain for security
    if (!url.startsWith("https://oaidalleapiprodscus.blob.core.windows.net/")) {
      return new NextResponse("Invalid URL domain", { status: 400 });
    }

    const imageResponse = await fetch(url, {
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
      },
    });

    if (!imageResponse.ok) {
      console.error(`Failed to fetch image: ${imageResponse.status}`);
      return new NextResponse("Failed to fetch image", {
        status: imageResponse.status,
      });
    }

    // Get the image data
    const imageArrayBuffer = await imageResponse.arrayBuffer();
    const contentType =
      imageResponse.headers.get("Content-Type") || "image/png";

    // Return the image with appropriate headers
    return new NextResponse(imageArrayBuffer, {
      headers: {
        "Content-Type": contentType,
        "Cache-Control": "public, max-age=86400",
        "Access-Control-Allow-Origin": "*",
      },
    });
  } catch (error) {
    console.error("Error proxying image:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

// This config exports tells Next.js not to use Edge runtime for this route
export const runtime = "nodejs";
