// app/api/get-user-data/route.ts
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/authOptions";

export const dynamic = "force-dynamic"; // Force dynamic routing

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.accessToken) {
      return NextResponse.json({ error: "No token provided" }, { status: 401 });
    }

    if (!process.env.NEXT_PUBLIC_API_BASE_URL) {
      throw new Error("NEXT_PUBLIC_API_BASE_URL is not defined");
    }

    // Make a request to your WordPress backend using fetch
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/get-user-data`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session.accessToken}`,
        },
      }
    );

    // Check if the response is successful
    if (response.ok) {
      const data = await response.json();

      return NextResponse.json(data, { status: 200 });
    } else {
      // Handle HTTP errors
      const errorData = await response.json();
      const errorMessage = errorData.message || "Failed to fetch user data";
      return NextResponse.json(
        {
          error: errorMessage,
          details: errorData,
        },
        { status: response.status }
      );
    }
  } catch (error: any) {
    console.error("Get user data error:", error);
    let errorMessage = "Failed to fetch user data";
    let errorDetails = {};
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
      errorDetails = {
        name: error.name,
        stack: error.stack,
      };
    }

    // Return a JSON response with the error details
    return NextResponse.json(
      {
        error: errorMessage,
        details: errorDetails,
      },
      { status: statusCode }
    );
  }
}
