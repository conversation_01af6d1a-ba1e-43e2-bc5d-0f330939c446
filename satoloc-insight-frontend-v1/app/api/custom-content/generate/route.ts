import { NextResponse } from "next/server";
import apiClient from "@/lib/apiClient";

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const response = await apiClient.post("/custom-content/generate/", body);
    return NextResponse.json(response.data, { status: 201 });
  } catch (error: any) {
    console.error("Error generating content:", error);
    const status = error.response?.status || 500;
    const errorMessage =
      error.response?.data?.error ||
      error.response?.data?.detail ||
      error.message ||
      "An unexpected error occurred";
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
