import { NextResponse } from "next/server";
import apiClient from "@/lib/apiClient";

export async function POST(request: Request) {
  try {
    const requestData = await request.json();
    const { type } = requestData;

    // For server-side route handlers, we'll rely on the cookies being passed automatically
    // The apiClient has built-in interceptors that handle authentication with cookies

    let endpoint;
    switch (type) {
      case "headline":
        endpoint = "/custom-content/optimize_headline/";
        break;
      case "images":
        endpoint = "/custom-content/suggest_images/";
        break;
      case "citations":
        endpoint = "/custom-content/find_citations/";
        break;
      case "plagiarism":
        endpoint = "/custom-content/check_plagiarism/";
        break;
      case "dalle-image":
        endpoint = "/custom-content/generate_dalle_image/";
        break;
      default:
        return NextResponse.json(
          { error: "Invalid enhancement type" },
          { status: 400 }
        );
    }

    // Pass all parameters from the request - includes content_id and other optional params
    const response = await apiClient.post(endpoint, requestData);

    return NextResponse.json(response.data, { status: 200 });
  } catch (error: any) {
    console.error(
      `Error in content enhancement (${error?.response?.config?.url || "unknown endpoint"}):`,
      error
    );
    const status = error.response?.status || 500;
    const errorMessage =
      error.response?.data?.error ||
      error.response?.data?.detail ||
      error.message ||
      "An unexpected error occurred";
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
