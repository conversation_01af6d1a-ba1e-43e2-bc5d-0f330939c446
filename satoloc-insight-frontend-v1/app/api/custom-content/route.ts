import { NextResponse } from "next/server";
import apiClient from "@/lib/apiClient";

// GET handler to fetch all custom content
export async function GET(request: Request) {
  try {
    const response = await apiClient.get("/custom-content/");
    return NextResponse.json(response.data, { status: 200 });
  } catch (error: any) {
    console.error("Error fetching custom content:", error);
    const status = error.response?.status || 500;
    const errorMessage =
      error.response?.data?.error ||
      error.message ||
      "An unexpected error occurred";
    return NextResponse.json({ error: errorMessage }, { status });
  }
}

// POST handler to create new custom content
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const response = await apiClient.post("/custom-content/", body);
    return NextResponse.json(response.data, { status: 201 });
  } catch (error: any) {
    console.error("Error creating custom content:", error);
    const status = error.response?.status || 500;
    const errorMessage =
      error.response?.data?.error ||
      error.message ||
      "An unexpected error occurred";
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
