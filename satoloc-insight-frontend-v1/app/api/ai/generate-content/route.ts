import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import apiClient from "@/lib/apiClient";

export async function POST(request: NextRequest) {
  try {
    // Get the session to check authentication
    const session = await getServerSession();

    if (!session) {
      console.warn("No session found, proceeding without authentication");
      // We'll proceed without authentication and let the backend handle it
    }

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.keyword || !body.contentType || !body.prompt) {
      return NextResponse.json(
        { error: "Keyword, content type, and prompt are required" },
        { status: 400 }
      );
    }

    // Forward the request to the backend with trailing slash
    // This is critical for Django which has APPEND_SLASH=True
    try {
      const response = await apiClient.post("/generate-content/", body);
      return NextResponse.json(response.data);
    } catch (apiError: any) {
      console.error("Backend API error:", apiError);

      // Handle API errors
      if (apiError.response) {
        return NextResponse.json(
          {
            error: apiError.response.data.error || "Failed to generate content",
          },
          { status: apiError.response.status }
        );
      }

      throw apiError; // Re-throw for the outer catch block
    }
  } catch (error: any) {
    console.error("Error generating content:", error);

    // Handle different types of errors
    if (error.request) {
      // The request was made but no response was received
      return NextResponse.json(
        { error: "No response from server" },
        { status: 503 }
      );
    } else {
      // Something happened in setting up the request that triggered an Error
      return NextResponse.json(
        { error: error.message || "An error occurred" },
        { status: 500 }
      );
    }
  }
}
