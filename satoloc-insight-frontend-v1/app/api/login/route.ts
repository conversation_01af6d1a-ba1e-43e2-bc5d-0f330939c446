// app/api/login/route.ts
import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    const { username, password } = await request.json();

    if (!process.env.NEXT_PUBLIC_API_BASE_URL) {
      throw new Error("NEXT_PUBLIC_API_BASE_URL is not defined");
    }

    // Make a request to your WordPress backend using fetch
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/login`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ username, password }),
      }
    );

    // Check if the response is successful
    if (response.ok) {
      const data = await response.json();

      return NextResponse.json(data, { status: 200 });
    } else {
      // Handle HTTP errors
      const errorData = await response.json();
      const errorMessage = errorData.message || "Login failed";
      return NextResponse.json(
        {
          error: errorMessage,
          details: errorData,
        },
        { status: response.status }
      );
    }
  } catch (error: any) {
    console.error("Login error:", error);

    let errorMessage = "Login failed";
    let errorDetails = {};
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
      errorDetails = {
        name: error.name,
        stack: error.stack,
      };
    }

    // Return a JSON response with the error details
    return NextResponse.json(
      {
        error: errorMessage,
        details: errorDetails,
      },
      { status: statusCode }
    );
  }
}
