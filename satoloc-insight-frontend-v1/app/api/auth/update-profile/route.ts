//app/api/update-profile/route.ts
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/authOptions";

export async function PATCH(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.accessToken) {
      return NextResponse.json(
        { error: "No authentication token available" },
        { status: 401 }
      );
    }

    const { firstname, lastname, company_name, website, industry } =
      await request.json();

    const data = {
      first_name: firstname,
      last_name: lastname,
      company_name: company_name,
      website: website,
      industry: industry,
    };

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/profile/update/`,
      {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session.accessToken}`,
        },
        body: JSON.stringify(data),
      }
    );

    const rawResponse = await response.text();

    if (response.ok) {
      const responseData = JSON.parse(rawResponse);

      return NextResponse.json(responseData, { status: 200 });
    } else {
      return NextResponse.json(
        { error: `Backend error: ${rawResponse}` },
        { status: response.status }
      );
    }
  } catch (error: any) {
    console.error("Update profile error:", error);
    return NextResponse.json(
      { error: error.message || "Failed to update profile" },
      { status: 500 }
    );
  }
}
