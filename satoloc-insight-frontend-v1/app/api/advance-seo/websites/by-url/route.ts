import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/authOptions";

// Force dynamic to prevent static rendering issues with headers
export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session || !session.accessToken) {
      return NextResponse.json(
        { detail: "Authentication required" },
        { status: 401 }
      );
    }

    // Get URL from query parameters
    const url = request.nextUrl.searchParams.get("url");

    if (!url) {
      return NextResponse.json(
        { detail: "URL parameter is required" },
        { status: 400 }
      );
    }

    // Forward the request to the backend API
    const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/advance-seo/websites/by_url/?url=${encodeURIComponent(url)}`;

    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${session.accessToken}`,
      },
    });

    // Get the response data
    const data = await response.json();

    // If the response is not ok, return the error
    if (!response.ok) {
      return NextResponse.json(
        { detail: data.error || "Failed to fetch website data" },
        { status: response.status }
      );
    }

    // Return the website data
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error in advance-seo by-url API route:", error);
    return NextResponse.json(
      { detail: "Internal server error" },
      { status: 500 }
    );
  }
}
