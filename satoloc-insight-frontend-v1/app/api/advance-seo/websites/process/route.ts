import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/authOptions";

// Force dynamic to prevent static rendering issues with headers
export const dynamic = "force-dynamic";

export async function POST(request: NextRequest) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session || !session.accessToken) {
      return NextResponse.json(
        { detail: "Authentication required" },
        { status: 401 }
      );
    }

    // Get request body
    const body = await request.json();

    // Validate required fields
    if (!body.url) {
      return NextResponse.json({ detail: "URL is required" }, { status: 400 });
    }

    if (!body.industry) {
      return NextResponse.json(
        { detail: "Industry is required" },
        { status: 400 }
      );
    }

    if (!body.language) {
      return NextResponse.json(
        { detail: "Language is required" },
        { status: 400 }
      );
    }

    // Forward the request to the backend API
    const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/advance-seo/websites/process/`;

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.accessToken}`,
      },
      body: JSON.stringify(body),
    });

    // Get the response data
    const data = await response.json();

    // If the response is not ok, throw an error
    if (!response.ok) {
      return NextResponse.json(
        { detail: data.detail || "Failed to process website" },
        { status: response.status }
      );
    }

    // Return a simple success message
    return NextResponse.json({
      success: true,
      message: `SEO analysis for ${body.url} has been successfully added to the database.`,
    });
  } catch (error) {
    console.error("Error in advance-seo process API route:", error);
    return NextResponse.json(
      { detail: "Internal server error" },
      { status: 500 }
    );
  }
}
