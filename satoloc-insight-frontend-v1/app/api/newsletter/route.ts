import { NextResponse } from "next/server";
import nodemailer from "nodemailer";
import fs from "fs";
import path from "path";

// Function to store subscription in a JSON file
async function storeSubscription(email: string) {
  try {
    const dataDir = path.join(process.cwd(), "data");
    const filePath = path.join(dataDir, "newsletter-subscriptions.json");

    // Create data directory if it doesn't exist
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    // Read existing subscriptions or create empty array
    let subscriptions: { email: string; date: string }[] = [];
    if (fs.existsSync(filePath)) {
      const fileContent = fs.readFileSync(filePath, "utf8");
      subscriptions = JSON.parse(fileContent);
    }

    // Add new subscription if it doesn't already exist
    if (!subscriptions.some((sub) => sub.email === email)) {
      subscriptions.push({
        email,
        date: new Date().toISOString(),
      });

      // Write updated subscriptions back to file
      fs.writeFileSync(filePath, JSON.stringify(subscriptions, null, 2));
    }

    return true;
  } catch (error) {
    console.error("Error storing subscription:", error);
    return false;
  }
}

export async function POST(request: Request) {
  try {
    const { email } = await request.json();

    if (!email || !/^\S+@\S+\.\S+$/.test(email)) {
      return NextResponse.json(
        { error: "Please provide a valid email address" },
        { status: 400 }
      );
    }

    // Store the subscription in our file-based system
    await storeSubscription(email);

    // Create a transporter using Hostinger SMTP configuration
    const transporter = nodemailer.createTransport({
      host: "smtp.hostinger.com",
      port: 465,
      secure: true,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
      // Add debug option for development
      ...(process.env.NEXT_PUBLIC_ENV === "development" && { debug: true }),
    });

    // Verify the transporter configuration
    try {
      await transporter.verify();
    } catch (verifyError) {
      console.error("SMTP verification failed:", verifyError);
      return NextResponse.json(
        {
          success: true,
          message: "Your subscription has been recorded. Thank you!",
        },
        { status: 200 }
      );
    }

    // Email to the subscriber
    const subscriberMailOptions = {
      from: process.env.EMAIL_USER,
      to: email,
      subject: "Welcome to SatoLOC Insight Newsletter",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
          <img src="https://satolocinsight.com/images/satoloc_insight_logo.png" alt="SatoLOC Insight Logo" style="width: 100px; margin-bottom: 20px;" />
          <h2 style="color: #0e7490;">Thank you for subscribing to our newsletter!</h2>
          <p>Dear Subscriber,</p>
          <p>Thank you for joining the SatoLOC Insight newsletter. You'll now receive the latest updates on:</p>
          <ul>
            <li>AI-driven localization insights</li>
            <li>AutoLQA technology advancements</li>
            <li>SEO strategies for global content</li>
            <li>Intelligent content creation tips</li>
          </ul>
          <p>Stay tuned for our upcoming content and product updates!</p>
          <p>Best regards,<br />The SatoLOC Insight Team</p>
          <div style="margin-top: 30px; font-size: 12px; color: #666; border-top: 1px solid #eee; padding-top: 10px;">
            <p>If you didn't subscribe to this newsletter, please ignore this email.</p>
          </div>
        </div>
      `,
    };

    // Email to the admin
    const adminMailOptions = {
      from: process.env.EMAIL_USER,
      to: process.env.EMAIL_USER,
      subject: "New Newsletter Subscription",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
          <h2 style="color: #0e7490;">New Newsletter Subscription</h2>
          <p>A new user has subscribed to the SatoLOC Insight newsletter:</p>
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Date:</strong> ${new Date().toLocaleString()}</p>
        </div>
      `,
    };

    try {
      // Send emails
      await transporter.sendMail(subscriberMailOptions);

      await transporter.sendMail(adminMailOptions);
    } catch (emailError) {
      console.error("Error sending newsletter emails:", emailError);
      // Still return success since we've logged the subscription
      return NextResponse.json(
        {
          success: true,
          message: "Thank you for subscribing! You've been added to our list.",
        },
        { status: 200 }
      );
    }

    return NextResponse.json(
      { success: true, message: "Thank you for subscribing!" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Newsletter subscription error:", error);
    return NextResponse.json(
      {
        error: "Failed to subscribe. Please try again later.",
        debug:
          process.env.NEXT_PUBLIC_ENV === "development" ? error : undefined,
      },
      { status: 500 }
    );
  }
}
