// app/api/scraping/projects/route.ts

export const dynamic = "force-dynamic";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/authOptions";
import apiClient from "@/lib/apiClient";

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.accessToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const response = await apiClient.get("/scraping/projects/", {
      headers: {
        Authorization: `Bearer ${session.accessToken}`,
      },
    });

    if (!response.data || response.data.length === 0) {
      // Create a default project if none exists
      const defaultProject = await apiClient.post(
        "/scraping/projects/",
        {
          name: `Default Project - ${new Date().toLocaleDateString()}`,
          description: "Default project for scraping analysis",
        },
        {
          headers: {
            Authorization: `Bearer ${session.accessToken}`,
          },
        }
      );

      return NextResponse.json([defaultProject.data]);
    }

    return NextResponse.json(response.data);
  } catch (error: any) {
    console.error("Projects fetch error:", error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch projects" },
      { status: error.response?.status || 500 }
    );
  }
}
