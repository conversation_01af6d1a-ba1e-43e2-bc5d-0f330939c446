// app/admin-dashboard/user-management/[id]/page.tsx
"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useParams } from "next/navigation";
import {
  Box,
  Typography,
  CircularProgress,
  Container,
  TextField,
  Button,
  MenuItem,
} from "@mui/material";
import PageWrapper from "@/components/PageWrapper";

export default function UserEditPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const userId = params?.id;
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [formData, setFormData] = useState<any>({
    first_name: "",
    last_name: "",
    email: "",
    roles: "",
  });

  useEffect(() => {
    if (status === "loading") return;

    if (!session) {
      router.push("/login");
      return;
    }

    if (!session.user?.isAdmin) {
      router.push("/dashboard");
    } else {
      fetchUser();
    }
  }, [session, status, router]);

  const fetchUser = async () => {
    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        headers: {
          Authorization: `Bearer ${session?.accessToken}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUser(data);
        setFormData({
          first_name: data.first_name || "",
          last_name: data.last_name || "",
          email: data.email || "",
          roles: data.roles[0] || "",
        });
      } else {
        console.error("Failed to fetch user");
      }
    } catch (error) {
      console.error("Error fetching user:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData((prev: any) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  const handleSave = async () => {
    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session?.accessToken}`,
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        router.push("/admin-dashboard/user-management");
      } else {
        console.error("Failed to update user");
      }
    } catch (error) {
      console.error("Error updating user:", error);
    }
  };

  if (status === "loading" || loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <PageWrapper>
      <Container>
        <Typography variant="h4" gutterBottom sx={{ mb: 4 }}>
          Edit User
        </Typography>
        <TextField
          label="First Name"
          name="first_name"
          value={formData.first_name}
          onChange={handleInputChange}
          fullWidth
          sx={{ mb: 2 }}
        />
        <TextField
          label="Last Name"
          name="last_name"
          value={formData.last_name}
          onChange={handleInputChange}
          fullWidth
          sx={{ mb: 2 }}
        />
        <TextField
          label="Email"
          name="email"
          value={formData.email}
          onChange={handleInputChange}
          fullWidth
          sx={{ mb: 2 }}
        />
        <TextField
          select
          label="Role"
          name="roles"
          value={formData.roles}
          onChange={handleInputChange}
          fullWidth
          sx={{ mb: 2 }}
        >
          <MenuItem value="subscriber">Subscriber</MenuItem>
          <MenuItem value="editor">Editor</MenuItem>
          <MenuItem value="administrator">Administrator</MenuItem>
          {/* Add other roles as needed */}
        </TextField>
        <Button variant="contained" color="primary" onClick={handleSave}>
          Save Changes
        </Button>
      </Container>
    </PageWrapper>
  );
}
