"use client";

import { useState } from "react";
import Link from "next/link";
import {
  useWordPressPostsWithPagination,
  useWordPressCategories,
  useWordPressMedia,
} from "@/internal-api/wordpress-blog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Search, Calendar, User, ArrowRight, Filter } from "lucide-react";
import { FixedNavbar } from "@/components/FrontPage/FixedNavbar";
import Footer from "@/components/Footer";
import { Pagination } from "@/components/Pagination";
import { truncateWordPressContent } from "@/lib/wordpress-content";

export default function BlogPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const postsPerPage = 9;

  // Fetch posts with filters and pagination
  const {
    data: postsResponse,
    isLoading: postsLoading,
    error: postsError,
  } = useWordPressPostsWithPagination({
    per_page: postsPerPage,
    page: currentPage,
    search: searchTerm || undefined,
    categories: selectedCategory ? [selectedCategory] : undefined,
    orderby: "date",
    order: "desc",
  });

  // Fetch categories for filter
  const { data: categories } = useWordPressCategories();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page when searching
  };

  const clearFilters = () => {
    setSearchTerm("");
    setSelectedCategory(null);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Scroll to top when page changes
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const posts = postsResponse?.posts || [];
  const pagination = postsResponse?.pagination;

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <FixedNavbar />

      {/* Main Content Area */}
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative py-20 pt-32 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-b from-primary/5 via-primary/10 to-background" />
          <div className="container mx-auto px-4 relative">
            <div className="text-center max-w-3xl mx-auto">
              <h1 className="text-5xl font-bold mb-6 bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
                SatoLOC Insights Blog
              </h1>
              <p className="text-xl text-muted-foreground mb-8">
                Discover the latest insights, trends, and best practices in
                localization, SEO optimization, and AI-powered content creation.
              </p>
            </div>
          </div>
        </section>

        {/* Search and Filters */}
        <section className="container mx-auto px-4 -mt-8 relative z-10">
          <Card className="mb-8">
            <CardContent className="p-6">
              <form
                onSubmit={handleSearch}
                className="flex flex-col md:flex-row gap-4"
              >
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    type="text"
                    placeholder="Search blog posts..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                <div className="flex gap-2">
                  <select
                    value={selectedCategory || ""}
                    onChange={(e) => {
                      setSelectedCategory(
                        e.target.value ? Number(e.target.value) : null
                      );
                      setCurrentPage(1); // Reset to first page when filtering
                    }}
                    className="px-3 py-2 border border-input bg-background rounded-md text-sm"
                  >
                    <option value="">All Categories</option>
                    {categories?.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name} ({category.count})
                      </option>
                    ))}
                  </select>

                  {(searchTerm || selectedCategory) && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={clearFilters}
                      className="whitespace-nowrap"
                    >
                      <Filter className="h-4 w-4 mr-2" />
                      Clear
                    </Button>
                  )}
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Results Info */}
          {pagination && (
            <div className="mb-6 text-sm text-muted-foreground">
              {searchTerm && <span>Search results for "{searchTerm}" • </span>}
              {selectedCategory && categories && (
                <span>
                  Category:{" "}
                  {categories.find((c) => c.id === selectedCategory)?.name} •{" "}
                </span>
              )}
              <span>
                Showing {(currentPage - 1) * postsPerPage + 1} to{" "}
                {Math.min(currentPage * postsPerPage, pagination.total)} of{" "}
                {pagination.total} post{pagination.total !== 1 ? "s" : ""}
              </span>
            </div>
          )}
        </section>

        {/* Blog Posts Grid */}
        <section className="container mx-auto px-4 pb-20">
          {postsLoading && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[...Array(6)].map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <div className="h-48 bg-muted rounded-t-lg" />
                  <CardContent className="p-6">
                    <div className="h-4 bg-muted rounded w-3/4 mb-4" />
                    <div className="h-3 bg-muted rounded w-full mb-2" />
                    <div className="h-3 bg-muted rounded w-2/3" />
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {postsError && (
            <Card className="p-8 text-center">
              <p className="text-destructive mb-4">Failed to load blog posts</p>
              <p className="text-sm text-muted-foreground">
                {postsError.message}
              </p>
            </Card>
          )}

          {posts && posts.length === 0 && !postsLoading && (
            <Card className="p-8 text-center">
              <p className="text-muted-foreground mb-4">No blog posts found</p>
              {(searchTerm || selectedCategory) && (
                <Button onClick={clearFilters} variant="outline">
                  Clear filters and show all posts
                </Button>
              )}
            </Card>
          )}

          {posts && posts.length > 0 && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                {posts.map((post, index) => (
                  <BlogPostCard key={post.id} post={post} index={index} />
                ))}
              </div>

              {/* Pagination */}
              {pagination && pagination.total_pages > 1 && (
                <div className="flex justify-center mt-12">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={pagination.total_pages}
                    onPageChange={handlePageChange}
                    className="mb-8"
                  />
                </div>
              )}
            </>
          )}
        </section>
      </main>

      <Footer />
    </div>
  );
}

function BlogPostCard({ post, index }: { post: any; index: number }) {
  // Fetch featured media if available
  const { data: media } = useWordPressMedia(
    post.featured_media || 0,
    !!post.featured_media
  );

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getExcerpt = (content: string, length: number = 120) => {
    return truncateWordPressContent(content, length);
  };

  return (
    <Card className="group hover:shadow-xl transition-all duration-300 overflow-hidden">
      <div className="relative">
        {media && media.source_url ? (
          <div className="h-48 overflow-hidden">
            <img
              src={media.source_url}
              alt={media.alt_text || post.title.rendered}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            />
          </div>
        ) : (
          <div className="h-48 bg-gradient-to-br from-primary/20 to-blue-600/20 flex items-center justify-center">
            <div className="text-4xl font-bold text-primary/40">
              {post.title.rendered.charAt(0).toUpperCase()}
            </div>
          </div>
        )}
        <div className="absolute top-4 right-4">
          <Badge variant="secondary" className="bg-white/90 backdrop-blur-sm">
            Post #{post.id}
          </Badge>
        </div>
      </div>

      <CardContent className="p-6">
        <div className="flex items-center gap-2 text-sm text-muted-foreground mb-3">
          <Calendar className="h-4 w-4" />
          <span>{formatDate(post.date)}</span>
          <User className="h-4 w-4 ml-2" />
          <span>Author {post.author}</span>
        </div>

        <h3 className="text-xl font-semibold mb-3 group-hover:text-primary transition-colors line-clamp-2">
          <Link href={`/blog/${post.slug}`}>{post.title.rendered}</Link>
        </h3>

        <p className="text-muted-foreground mb-4 line-clamp-3">
          {getExcerpt(post.excerpt.rendered || post.content.rendered)}
        </p>

        <div className="flex items-center justify-between">
          <div className="flex gap-2">
            {post.categories?.slice(0, 2).map((categoryId: number) => (
              <Badge key={categoryId} variant="outline" className="text-xs">
                Category {categoryId}
              </Badge>
            ))}
          </div>

          <Link href={`/blog/${post.slug}`}>
            <Button
              variant="ghost"
              size="sm"
              className="group-hover:text-primary"
            >
              Read More
              <ArrowRight className="ml-1 h-3 w-3 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
