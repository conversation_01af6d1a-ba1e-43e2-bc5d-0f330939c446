// components/LoadingModal/index.tsx
import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Loader2 } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import Link from "next/link";

const loadingMessages = [
  "🌐 Analyzing webpage structure...",
  "📊 Processing content metrics...",
  "🔍 Evaluating SEO elements...",
  "🌍 Checking localization aspects...",
  "🤖 Generating AI insights...",
  "📝 Preparing comprehensive report...",
];

interface LoadingModalProps {
  isOpen: boolean;
  status: "loading" | "completed" | "failed";
  onClose?: () => void;
}

export function LoadingModal({ isOpen, status, onClose }: LoadingModalProps) {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);

  useEffect(() => {
    if (!isOpen) {
      setCurrentMessageIndex(0);
      return;
    }

    if (status === "loading") {
      const interval = setInterval(() => {
        setCurrentMessageIndex((prev) =>
          prev === loadingMessages.length - 1 ? 0 : prev + 1
        );
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [isOpen, status]);

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open && onClose) {
          onClose();
        }
      }}
    >
      <DialogContent className="sm:max-w-md flex flex-col items-center justify-center p-6">
        {status === "loading" && (
          <>
            <DialogTitle className="text-xl font-semibold mb-2">
              Processing Website
            </DialogTitle>
            <DialogDescription>
              Please wait while we analyze your website
            </DialogDescription>

            <div className="flex flex-col items-center space-y-4 mt-4">
              <motion.div
                animate={{
                  rotate: 360,
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "linear",
                }}
              >
                <Loader2 className="h-12 w-12 text-primary" />
              </motion.div>

              <AnimatePresence mode="wait">
                <motion.div
                  key={currentMessageIndex}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5 }}
                  className="text-center min-h-[80px] flex flex-col justify-center"
                >
                  <p className="text-lg font-medium">
                    {loadingMessages[currentMessageIndex]}
                  </p>
                  <p className="text-sm text-muted-foreground mt-2">
                    This may take a few moments
                  </p>
                </motion.div>
              </AnimatePresence>

              <motion.div
                animate={{
                  scaleX: [1, 1.2, 1],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
                className="w-full max-w-xs h-2 bg-primary/20 rounded-full overflow-hidden mt-4"
              >
                <motion.div
                  animate={{
                    x: ["-100%", "100%"],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "linear",
                  }}
                  className="h-full w-1/2 bg-primary rounded-full"
                />
              </motion.div>
            </div>
          </>
        )}

        {status === "completed" && (
          <>
            <DialogTitle className="text-xl font-semibold mb-2 text-[#1279b4]">
              Your Insight is Ready!
            </DialogTitle>
            <DialogDescription>
              Click the button below to view your insights.
            </DialogDescription>

            <div className="flex flex-col items-center space-y-4 mt-4">
              <div className="text-center">
                <p className="text-lg font-medium">Thank you for waiting.</p>
                <p className="text-sm text-muted-foreground mt-2">
                  Your insights have been generated.
                </p>
              </div>

              <Button onClick={onClose} className="w-full bg-[#1279b4]">
                View Insights
              </Button>
            </div>
          </>
        )}

        {status === "failed" && (
          <>
            <DialogTitle className="text-xl font-semibold mb-2">
              Something Went Wrong
            </DialogTitle>
            <DialogDescription>
              We encountered an error while processing your request.
            </DialogDescription>

            <div className="flex flex-col items-center space-y-4 mt-4">
              <div className="text-center">
                <p className="text-lg font-medium">Please try again later.</p>
              </div>

              <Button onClick={onClose}>Close</Button>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
