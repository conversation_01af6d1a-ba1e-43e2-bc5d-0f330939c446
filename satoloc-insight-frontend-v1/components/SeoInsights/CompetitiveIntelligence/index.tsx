import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowUpIcon, ArrowRightIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Clock } from "lucide-react";

interface CompetitiveIntelligenceProps {
  data: {
    competitors: Array<{
      name: string;
      description: string;
      performance: number;
    }>;
    strategy_gaps?: Array<{
      id: number;
      text: string;
    }>;
    growth_opportunities?: Array<{
      id: number;
      text: string;
    }>;
  };
  className?: string;
}

export function CompetitiveIntelligence({
  data,
  className,
}: CompetitiveIntelligenceProps) {
  return (
    <Card className={cn("p-6", className)}>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-muted-foreground" />
            <h2 className="text-lg font-semibold">
              AI Competitive Intelligence
            </h2>
          </div>
          <span className="text-sm text-muted-foreground">Updated 2h ago</span>
        </div>

        {/* Competitors Section - Updated for better responsive layout */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {data.competitors.map((competitor, index) => (
            <div
              key={competitor.name}
              className="flex flex-col sm:flex-row items-start sm:items-center justify-between p-4 rounded-lg border bg-card space-y-3 sm:space-y-0"
            >
              <div className="space-y-1">
                <h3 className="font-medium">{competitor.name}</h3>
                <p className="text-sm text-muted-foreground">
                  {competitor.description}
                </p>
              </div>
              <div className="flex items-center gap-3 w-full sm:w-auto justify-between sm:justify-end">
                <span className="text-sm font-medium text-emerald-500">
                  ↑ {competitor.performance}%
                </span>
                <Button variant="ghost" size="sm" className="h-8">
                  View Strategy <ArrowRightIcon className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>

        {/* Critical Strategy Gaps */}
        <div className="space-y-3">
          <h3 className="text-base font-medium text-red-500">
            Critical Strategy Gaps
          </h3>
          <ul className="space-y-2">
            {data.strategy_gaps && data.strategy_gaps.length > 0 ? (
              data.strategy_gaps.map((gap, index) => (
                <li key={index} className="text-sm text-muted-foreground">
                  • {gap.text}
                </li>
              ))
            ) : (
              <li className="text-sm text-muted-foreground italic">
                No strategy gaps found
              </li>
            )}
          </ul>
        </div>

        {/* Growth Opportunities */}
        <div className="space-y-3">
          <h3 className="text-base font-medium">Growth Opportunities</h3>
          <ul className="space-y-2">
            {data.growth_opportunities &&
            data.growth_opportunities.length > 0 ? (
              data.growth_opportunities.map((opportunity, index) => (
                <li key={index} className="text-sm text-muted-foreground">
                  • {opportunity.text}
                </li>
              ))
            ) : (
              <li className="text-sm text-muted-foreground italic">
                No growth opportunities found
              </li>
            )}
          </ul>
        </div>
      </div>
    </Card>
  );
}
