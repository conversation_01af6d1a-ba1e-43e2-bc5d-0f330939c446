"use client";

import dynamic from "next/dynamic";
import { useState, useEffect, Suspense } from "react";
import { Box } from "@mui/material";
import { Card, CardContent } from "@/components/ui/card";
import { Loader2 } from "lucide-react";

import {
  useGetCrawlResults,
  CrawlResponse,
  useGetCrawlMetrics,
  useGetLatestCrawl,
  useGetTechnicalMetrics,
  useGetAllCrawls,
} from "@/internal-api/seo";
import { transformMetrics } from "@/lib/metrics";

const CrawlForm = dynamic(() => import("./CrawlForm"), {
  ssr: false,
  loading: () => (
    <div className="h-[200px] animate-pulse bg-gray-100 rounded-lg" />
  ),
});

// Import the CrawlProgressModal
const CrawlProgressModal = dynamic(() => import("./CrawlProgressModal"), {
  ssr: false,
});

import type { SeoInsightData } from "@/types/seo";
import { MetricCard } from "./MetricCard";
import { SeoOptimizationChecklist } from "./SeoOptimizationChecklist";
import { useTheme } from "@mui/material/styles";
import { TechnicalSeoAnalytics } from "./TechnicalSeoAnalytics";
import { RegionalPerformanceTrends } from "./RegionalPerformanceTrends";
import { CrawlPagination } from "./CrawlPagination";

// Dummy data
const dummyData: SeoInsightData = {
  metrics: {
    "Domain Rating": {
      value: 85,
      change: {
        value: 3,
        label: "this month",
        isPositive: true,
      },
      label: "Domain Rating",
    },
    "Organic Traffic": {
      value: "12.5K",
      change: {
        value: 15,
        label: "vs last month",
        isPositive: true,
      },
      label: "Organic Traffic",
    },
    "Search Rankings": {
      value: "72%",
      change: {
        value: 5,
        label: "vs competitors",
        isPositive: true,
      },
      label: "Search Rankings",
    },
    "Search Terms": {
      value: 184,
      change: {
        value: 12,
        label: "new found",
        isPositive: true,
      },
      label: "Search Terms",
    },
    "Site Links": {
      value: 426,
      change: {
        value: 8,
        label: "new this month",
        isPositive: true,
      },
      label: "Site Links",
    },
    "Content Score": {
      value: "89%",
      change: {
        value: 4,
        label: "vs last month",
        isPositive: true,
      },
      label: "Content Score",
    },
  },
  competitiveIntelligence: {
    lastUpdated: "2h",
    competitors: [
      {
        name: "Competitor A",
        description: "Outperforming in local keywords",
        performance: 5,
      },
      {
        name: "Competitor B",
        description: "Better technical SEO setup",
        performance: 8,
      },
    ],
    criticalGaps: [
      '"crypto trading" ranks 40% below competitors',
      "Missing LocalBusiness schema used by 8/10 competitors",
    ],
    opportunities: [
      "Top competitor has 12 city-specific landing pages",
      "45% more featured snippets with FAQ clusters",
    ],
  },
  regionalOpportunities: [
    {
      keyword: "crypto trading",
      demand: "High regional demand",
    },
    {
      keyword: "blockchain tech",
      demand: "High regional demand",
    },
    {
      keyword: "trump token",
      demand: "High regional demand",
    },
  ],
  optimizationChecklist: {
    progress: 65,
    sections: [
      {
        title: "HIGH COMPETITION GAPS",
        icon: "warning",
        subtitle: "Main Trading Pages",
        items: [
          {
            text: "Page(s) to optimize: /bitcoin-trade (competitor ranks #1) / crypto-exchange (gap: -15 positions)",
            trafficPotential: "+65%",
          },
        ],
      },
      {
        title: "TRENDING TOKENS",
        icon: "trending",
        subtitle: "New Token Pages",
        items: [
          {
            text: "New content to generate: /sui-coin-guide (high search volume) + /what-is-celestia (trending) + /chainlink-staking (growing interest)",
          },
        ],
      },
      {
        title: "TECHNICAL UPDATES",
        icon: "technical",
        subtitle: "Infrastructure",
        items: [
          {
            text: "Technical issues to fix: • Mobile page speed (avg 3.2s vs competitor 2.1s) • Schema markup on price pages • Turkish hreflang implementation",
          },
        ],
      },
    ],
  },
  technicalMetrics: [
    {
      label: "Page Speed",
      value: 95,
      regionalAverage: 82,
      status: "success",
    },
    {
      label: "Mobile Optimization",
      value: 88,
      regionalAverage: 75,
      status: "warning",
    },
    {
      label: "Local Schema",
      value: 0,
      regionalAverage: 65,
      status: "missing",
    },
  ],
  topPages: [],
  keywords: [],
};

export default function SeoInsights() {
  const [data, setData] = useState<SeoInsightData>(dummyData);
  const [selectedCrawlId, setSelectedCrawlId] = useState<number | undefined>(
    undefined
  );
  // Add state for the crawl progress modal
  const [isProgressModalOpen, setIsProgressModalOpen] = useState(false);
  const [activeCrawlId, setActiveCrawlId] = useState<number | undefined>(
    undefined
  );

  const theme = useTheme();

  // Get all crawls
  const { data: allCrawls = [], isLoading: isAllCrawlsLoading } =
    useGetAllCrawls();

  // Get the latest crawl with error handling
  const {
    data: latestCrawl,
    isError: isLatestCrawlError,
    isLoading: isLatestCrawlLoading,
  } = useGetLatestCrawl();

  // Set the selected crawl ID to the latest crawl ID if not already set
  useEffect(() => {
    if (latestCrawl?.id && !selectedCrawlId) {
      setSelectedCrawlId(latestCrawl.id);
    }
  }, [latestCrawl, selectedCrawlId]);

  // Get crawl results and metrics only if we have a crawl
  const crawlId = selectedCrawlId;
  const { data: crawlResults, isLoading: isCrawlLoading } =
    useGetCrawlResults(crawlId);
  const { data: metricsData, isLoading: isMetricsLoading } =
    useGetCrawlMetrics(crawlId);
  const { data: technicalMetrics, isLoading: isTechnicalLoading } =
    useGetTechnicalMetrics(crawlId);

  // Combined loading state
  const isLoading =
    isLatestCrawlLoading ||
    isAllCrawlsLoading ||
    (crawlId && (isMetricsLoading || isCrawlLoading));

  // Show no data if:
  // 1. There's an error fetching the latest crawl
  // 2. The latest crawl fetch completed but returned null (404)
  // 3. We're not loading and don't have a crawl ID
  const shouldShowNoData =
    isLatestCrawlError ||
    (!isLatestCrawlLoading && latestCrawl === null) ||
    (!isLoading && !crawlId);

  // Update data when metrics change
  useEffect(() => {
    if (metricsData) {
      setData((prevData) => ({
        ...prevData,
        metrics: transformMetrics(metricsData),
      }));
    }
  }, [metricsData]);

  // Update technical metrics when they change
  useEffect(() => {
    if (technicalMetrics) {
      setData((prevData) => ({
        ...prevData,
        technicalMetrics: technicalMetrics.metrics,
      }));
    }
  }, [technicalMetrics]);

  const handleCrawlSuccess = (response: CrawlResponse) => {
    // The queries will be automatically invalidated by the CrawlForm component
    if (response?.id) {
      // Set the selected crawl ID
      setSelectedCrawlId(response.id);

      // Show the progress modal for the new crawl
      setActiveCrawlId(response.id);
      setIsProgressModalOpen(true);
    } else if (response?.id === -1) {
      // This is a temporary response from the form to show the modal immediately
      // We'll keep the modal open, but the real crawl ID will be set when the API responds
      setIsProgressModalOpen(true);
      setActiveCrawlId(undefined); // Will be updated when the real response comes back
    }
  };

  const handleCrawlChange = (crawlId: number) => {
    setSelectedCrawlId(crawlId);
  };

  // Function to open the progress modal for the selected crawl
  const handleViewProgress = () => {
    if (selectedCrawlId) {
      setActiveCrawlId(selectedCrawlId);
      setIsProgressModalOpen(true);
    }
  };

  // Check if the selected crawl is in progress
  const isSelectedCrawlInProgress = crawlResults?.status === "in_progress";

  return (
    <Box sx={{ py: 4 }}>
      <div className="space-y-4 sm:space-y-6">
        {/* <SeoInsightsHeader /> */}
        <Suspense
          fallback={
            <div className="h-[200px] animate-pulse bg-gray-100 rounded-lg" />
          }
        >
          <CrawlForm onSuccess={handleCrawlSuccess} />
        </Suspense>

        {/* Crawl Progress Modal */}
        <CrawlProgressModal
          crawlId={activeCrawlId}
          isOpen={isProgressModalOpen}
          onClose={() => setIsProgressModalOpen(false)}
        />

        {/* Crawl Pagination with View Progress button */}
        {allCrawls.length > 0 && (
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
            <CrawlPagination
              crawls={allCrawls}
              currentCrawlId={selectedCrawlId}
              isLoading={isAllCrawlsLoading}
              onCrawlChange={handleCrawlChange}
            />

            {isSelectedCrawlInProgress && (
              <button
                onClick={handleViewProgress}
                className="flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-md bg-blue-50 text-blue-700 hover:bg-blue-100 transition-colors"
              >
                <Loader2 className="h-4 w-4 animate-spin" />
                View Crawl Progress
              </button>
            )}
          </div>
        )}

        {/* Metrics Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-3 sm:gap-4">
          {isLoading
            ? // Loading state - show skeleton cards
              Array.from({ length: 6 }).map((_, i) => (
                <Card
                  key={i}
                  className="group hover:shadow-md transition-all bg-background/50"
                >
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      <div className="h-4 w-24 bg-muted/60 animate-pulse rounded" />
                      <div className="h-8 w-20 bg-muted/60 animate-pulse rounded" />
                      <div className="flex items-center space-x-2">
                        <div className="h-3 w-3 bg-muted/60 animate-pulse rounded-full" />
                        <div className="h-3 w-16 bg-muted/60 animate-pulse rounded" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            : // Show actual metrics or no data state
              Object.entries(data.metrics).map(([key, metric]) => (
                <MetricCard
                  key={key}
                  metric={metric}
                  noData={shouldShowNoData}
                />
              ))}
        </div>

        {/* SEO Optimization Checklist */}
        <SeoOptimizationChecklist crawlId={crawlId} />

        {/* Technical SEO Analytics and Regional Performance Trends */}
        <div className="grid gap-4 sm:gap-6 lg:grid-cols-2">
          <TechnicalSeoAnalytics
            metrics={data.technicalMetrics}
            isLoading={isTechnicalLoading}
            noData={shouldShowNoData}
            crawlId={crawlId}
          />
          <RegionalPerformanceTrends
            crawlId={crawlId}
            noData={shouldShowNoData}
          />
        </div>
      </div>
    </Box>
  );
}
