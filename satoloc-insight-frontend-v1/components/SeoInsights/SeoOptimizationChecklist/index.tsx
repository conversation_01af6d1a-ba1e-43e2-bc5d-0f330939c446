import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  AlertTriangle,
  TrendingUp,
  Settings2,
  Info,
  CheckCircle,
} from "lucide-react";
import {
  useGetOptimizationChecklist,
  useGetCrawlResults,
} from "@/internal-api/seo";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";

interface Props {
  crawlId?: number;
}

const iconMap: Record<string, React.ElementType> = {
  warning: AlertTriangle,
  trending: TrendingUp,
  technical: Settings2,
  info: Info,
  success: CheckCircle,
};

export const SeoOptimizationChecklist: React.FC<Props> = ({ crawlId }) => {
  const { data, isLoading, error } = useGetOptimizationChecklist(crawlId);
  const { data: crawlResults } = useGetCrawlResults(crawlId);

  // Check for GSC data in multiple ways
  const hasGSCAccess =
    crawlResults?.has_gsc_data === true ||
    !!crawlResults?.gsc_refresh_token ||
    (!!crawlResults?.gsc_data &&
      Object.keys(crawlResults?.gsc_data).length > 0);

  if (!crawlId) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>SEO Optimization Checklist</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert className="bg-yellow-50/50">
            <AlertDescription>
              Please run a crawl to see optimization suggestions.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>SEO Optimization Checklist</CardTitle>
          <Skeleton className="h-4 w-[200px]" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[200px] w-full" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>SEO Optimization Checklist</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertDescription>
              Failed to load optimization checklist. Please try again later.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!data || !data.sections) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>SEO Optimization Checklist</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert className="bg-yellow-50/50">
            <Info className="h-4 w-4" />
            <AlertDescription>
              No optimization data available. Please run a new crawl.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>SEO Optimization Checklist</CardTitle>
        <div className="flex items-center gap-4">
          <Progress value={data.progress} className="flex-1" />
          <span className="text-sm text-muted-foreground">
            {Math.round(data.progress)}% Complete
          </span>
        </div>
      </CardHeader>
      <CardContent>
        {!hasGSCAccess && (
          <Alert className="mb-6 bg-yellow-50/50">
            <Info className="h-4 w-4" />
            <AlertTitle>Limited Data Accuracy</AlertTitle>
            <AlertDescription className="mt-2">
              <div>
                Currently showing estimates based on technical analysis. For
                more accurate insights including:
                <ul className="list-disc pl-6 mt-2 space-y-1">
                  <li>Real search rankings and positions</li>
                  <li>Actual traffic potential based on impressions</li>
                  <li>Competitor keyword analysis</li>
                  <li>Core Web Vitals data</li>
                </ul>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() => {
                  document
                    .querySelector("form")
                    ?.scrollIntoView({ behavior: "smooth" });
                }}
              >
                Connect Google Search Console
              </Button>
            </AlertDescription>
          </Alert>
        )}

        <div className="space-y-6">
          {data.sections.map((section, index) => {
            const Icon = iconMap[section.icon] || Info;
            return (
              <div key={index} className="space-y-2">
                <div className="flex items-center gap-2">
                  <h3 className="font-semibold">{section.title}</h3>
                </div>
                {section.subtitle && (
                  <p className="text-sm text-muted-foreground">
                    {section.subtitle}
                  </p>
                )}
                <ul className="space-y-2">
                  {section.items.map((item, itemIndex) => (
                    <li
                      key={itemIndex}
                      className="flex items-start justify-between gap-4 rounded-lg border p-3"
                    >
                      <span className="text-sm">{item.text}</span>
                      {item.trafficPotential && (
                        <span className="whitespace-nowrap text-sm font-medium text-green-600">
                          {!hasGSCAccess && "~"}
                          {item.trafficPotential}
                        </span>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};
