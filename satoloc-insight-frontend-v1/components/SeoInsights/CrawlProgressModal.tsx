"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import {
  Loader2,
  CheckCircle2,
  Globe,
  FileText,
  Link2,
  AlertTriangle,
  X,
  Clock,
} from "lucide-react";
import { CrawlResponse } from "@/internal-api/seo";
import { useTrackCrawlStatus } from "@/internal-api/seo";
import { formatDistanceToNow } from "date-fns";
import { Button } from "@/components/ui/button";

interface CrawlProgressModalProps {
  crawlId: number | undefined;
  isOpen: boolean;
  onClose: () => void;
}

export default function CrawlProgressModal({
  crawlId,
  isOpen,
  onClose,
}: CrawlProgressModalProps) {
  const [progress, setProgress] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const [isFailed, setIsFailed] = useState(false);
  const [startTime, setStartTime] = useState<Date | null>(null);
  const [elapsedTime, setElapsedTime] = useState<number>(0);
  const [isTemporary, setIsTemporary] = useState(false);
  const [previousCrawlId, setPreviousCrawlId] = useState<number | undefined>(
    undefined
  );
  const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null);

  // Track crawl status using our custom hook with a faster polling interval
  const {
    data: crawlData,
    isLoading,
    error,
  } = useTrackCrawlStatus(crawlId, {
    enabled: isOpen && !!crawlId,
    pollingInterval: 500, // Reduced from 1000ms to 500ms for more frequent updates
    onComplete: () => {
      // The user will close the modal when they're ready
    },
  });

  // Reset state when crawlId changes
  useEffect(() => {
    if (crawlId !== previousCrawlId) {
      setProgress(0);
      setIsComplete(false);
      setIsFailed(false);
      setStartTime(null);
      setElapsedTime(0);
      setIsTemporary(false);
      setLastUpdateTime(null);
      setPreviousCrawlId(crawlId);
    }
  }, [crawlId, previousCrawlId]);

  // Update elapsed time every second when crawl is in progress
  useEffect(() => {
    if (!startTime || isComplete || isFailed) return;

    const timer = setInterval(() => {
      const now = new Date();
      const diffInMs = now.getTime() - startTime.getTime();
      const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
      setElapsedTime(diffInMinutes);
    }, 1000);

    return () => clearInterval(timer);
  }, [startTime, isComplete, isFailed]);

  // Update state based on crawl data
  useEffect(() => {
    if (!crawlData) return;

    // Check if this is a temporary crawl
    const isTempCrawl = crawlId === -1 || !!(crawlData as any).is_temp;
    setIsTemporary(isTempCrawl);

    // Set start time if not already set
    if (!startTime && crawlData.start_time) {
      setStartTime(new Date(crawlData.start_time));
    }

    // Calculate elapsed time if we have start_time and either end_time or current time
    if (crawlData.start_time) {
      const start = new Date(crawlData.start_time);
      const end =
        crawlData.end_time && crawlData.status === "completed"
          ? new Date(crawlData.end_time)
          : new Date();
      const diffInMs = end.getTime() - start.getTime();
      const diffInMinutes = Math.floor(diffInMs / (1000 * 60));

      setElapsedTime(diffInMinutes);
    }

    // Update status based on crawl data status
    if (crawlData.status === "completed") {
      setIsComplete(true);
      setProgress(100);
    } else if (crawlData.status === "failed") {
      setIsFailed(true);
      setProgress(0);
    } else if (
      crawlData.status === "in_progress" ||
      crawlData.status === "pending"
    ) {
      // Ensure we're not showing complete when status is in_progress
      setIsComplete(false);

      // Calculate progress based on pages crawled vs max_pages
      if (crawlData.pages && crawlData.max_pages) {
        const pagesCount = crawlData.pages.length;
        const maxPages = parseInt(String(crawlData.max_pages));

        if (maxPages > 0) {
          // Calculate progress percentage
          const calculatedProgress = Math.min(
            Math.round((pagesCount / maxPages) * 100),
            99 // Cap at 99% until complete
          );

          // Ensure progress is at least 1% when crawling has started
          const finalProgress =
            pagesCount > 0 ? Math.max(calculatedProgress, 1) : 0;

          // Force a state update even if the calculated progress is the same
          setProgress((prevProgress) => {
            // If the new progress is significantly different or we haven't updated in a while, update
            if (
              Math.abs(finalProgress - prevProgress) >= 1 ||
              !lastUpdateTime ||
              new Date().getTime() - lastUpdateTime.getTime() > 2000
            ) {
              return finalProgress;
            }

            // If the progress hasn't changed but time has passed, increment slightly
            // to show activity (but only if we're not at 99% already)
            if (prevProgress < 99 && prevProgress === finalProgress) {
              // Small random increment (0.5-1.5%)
              const smallIncrement = 0.5 + Math.random();
              return Math.min(prevProgress + smallIncrement, 99);
            }

            return prevProgress;
          });
        } else {
          // If max_pages is 0, show indeterminate progress (50%)
          setProgress(50);
        }
      } else if (crawlData.status === "in_progress") {
        // If we don't have page data but status is in_progress, show at least some progress
        // Increment progress slowly to show activity
        setProgress((prevProgress) => {
          // Start with at least 5% to show activity
          if (prevProgress < 5) return 5;

          // Increment by 1-3% each time, but cap at 90% until we get actual data
          const increment = Math.floor(Math.random() * 3) + 1;
          return Math.min(prevProgress + increment, 90);
        });
      } else if (isTemporary) {
        // For temporary crawls, show a small progress to indicate something is happening
        // Use a pulsing effect by alternating between 1% and 5%
        setProgress((prevProgress) => {
          return prevProgress <= 1 ? 5 : 1;
        });
      }
    }

    // Update last update time
    setLastUpdateTime(new Date());
  }, [crawlData, startTime, crawlId]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setProgress(0);
      setIsComplete(false);
      setIsFailed(false);
      setStartTime(null);
      setElapsedTime(0);
      setIsTemporary(false);
      setLastUpdateTime(null);
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent
        className="sm:max-w-md p-0 overflow-hidden max-h-[90vh] flex flex-col"
        aria-describedby="crawl-progress-description"
      >
        <DialogHeader className="sr-only">
          <DialogTitle>
            {isComplete ? "Crawl Complete" : "Crawling Website"}
          </DialogTitle>
          <DialogDescription id="crawl-progress-description">
            {isComplete
              ? "Website crawl completed successfully. You can now view the results."
              : "Tracking the progress of your website crawl in real-time."}
          </DialogDescription>
        </DialogHeader>
        <div className="p-6 overflow-y-auto flex-grow">
          <div className="absolute right-4 top-4">
            <button
              onClick={onClose}
              className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </button>
          </div>

          <div className="flex items-center gap-2 mb-2">
            {isComplete ? (
              <CheckCircle2 className="h-5 w-5 text-green-500" />
            ) : (
              <Loader2 className="h-5 w-5 animate-spin" />
            )}
            <h2 className="text-lg font-semibold">
              {isLoading
                ? "Loading Crawl Status..."
                : isComplete
                  ? "Crawl Complete"
                  : "Crawling Website"}
            </h2>
          </div>

          <p className="text-sm text-muted-foreground mb-4">
            {isLoading
              ? "Loading crawl information..."
              : isComplete
                ? "Website crawl completed successfully. You can now view the results."
                : crawlData?.error_message
                  ? `Error: ${crawlData.error_message}`
                  : isTemporary
                    ? "Initializing crawl..."
                    : `Crawling ${crawlData?.url || "website"}...`}
          </p>

          <div className="space-y-4">
            {/* Progress bar */}
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span>Progress</span>
                <span data-testid="progress-percentage">
                  {isLoading ? "..." : isComplete ? "100" : `${progress}`}%
                </span>
              </div>
              <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
                <div
                  className={`h-full ${
                    isComplete
                      ? "bg-green-600"
                      : isTemporary
                        ? "bg-blue-400"
                        : "bg-black"
                  } rounded-full transition-all duration-300`}
                  style={{
                    width: `${
                      isLoading
                        ? "5"
                        : isComplete
                          ? "100"
                          : Math.max(progress, 1)
                    }%`,
                  }}
                  data-testid="progress-bar"
                ></div>
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-2">
              <div className="flex flex-col items-center justify-center border rounded-md p-3">
                <Globe className="h-5 w-5 text-gray-500 mb-1" />
                <span
                  className="text-xl font-semibold"
                  data-testid="pages-crawled"
                >
                  {isLoading ? "..." : crawlData?.pages?.length || 0}
                </span>
                <span className="text-xs text-gray-500">Pages Crawled</span>
              </div>
              <div className="flex flex-col items-center justify-center border rounded-md p-3">
                <FileText className="h-5 w-5 text-gray-500 mb-1" />
                <span className="text-xl font-semibold" data-testid="max-pages">
                  {isLoading ? "..." : crawlData?.max_pages || 0}
                </span>
                <span className="text-xs text-gray-500">Max Pages</span>
              </div>
              <div className="flex flex-col items-center justify-center border rounded-md p-3">
                <Clock className="h-5 w-5 text-gray-500 mb-1" />
                <span
                  className="text-xl font-semibold"
                  data-testid="elapsed-time"
                >
                  {isLoading ? "..." : elapsedTime}
                </span>
                <span className="text-xs text-gray-500">minutes</span>
              </div>
            </div>

            {/* Recently crawled pages */}
            <div>
              <h4 className="text-sm font-medium mb-2">
                Recently Crawled Pages
              </h4>
              <ScrollArea className="border rounded-md h-[150px]">
                {error ? (
                  <div className="p-4 text-sm text-red-500 flex items-center justify-center h-full">
                    Error loading crawl data. Please try again.
                  </div>
                ) : isTemporary ? (
                  <div className="p-4 text-sm text-gray-500 flex items-center justify-center h-full">
                    Waiting for crawl to start...
                  </div>
                ) : crawlData?.error_message && !crawlData.pages?.length ? (
                  <div className="p-4 text-sm text-gray-500 flex items-center justify-center h-full">
                    {crawlData.error_message === "Crawl not found"
                      ? "No crawl data available yet. Start a new crawl to see results."
                      : crawlData.error_message}
                  </div>
                ) : crawlData?.pages && crawlData.pages.length > 0 ? (
                  <div className="p-2 space-y-2">
                    {[...crawlData.pages]
                      .reverse() // Reverse to show most recent first
                      .slice(0, 10) // Show up to 10 most recent pages
                      .map((page: any, index: number) => (
                        <div
                          key={index}
                          className="flex items-center gap-2 text-sm px-2 py-1"
                        >
                          <Globe className="h-4 w-4 text-gray-500 flex-shrink-0" />
                          <span className="truncate">{page.url}</span>
                        </div>
                      ))}
                  </div>
                ) : (
                  <div className="p-4 text-sm text-gray-500 flex items-center justify-center h-full">
                    {isLoading
                      ? "Loading crawl data..."
                      : "No pages crawled yet"}
                  </div>
                )}
              </ScrollArea>
            </div>
          </div>
        </div>

        {/* Footer buttons */}
        <div className="flex justify-end gap-2 p-4 border-t mt-auto">
          {isComplete && !isLoading ? (
            <Button
              onClick={onClose}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              View Results
            </Button>
          ) : null}
          <Button onClick={onClose} variant="outline">
            {isComplete ? "Close" : "Close"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
