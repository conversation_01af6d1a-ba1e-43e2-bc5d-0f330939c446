import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  <PERSON>,
  <PERSON>C<PERSON>,
  ResponsiveContainer,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from "recharts";
import { useGetRegionalTrends, useGetCrawlResults } from "@/internal-api/seo";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Info, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";

interface DataPoint {
  month: string;
  organicKeywords: number;
  competitorKeywords: number;
  avgPosition: number;
  visibility: number;
}

interface RegionalPerformanceTrendsProps {
  crawlId?: number;
  noData?: boolean;
}

// Generate estimated data for non-GSC users
const generateEstimatedData = () => {
  const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun"];
  const baseKeywords = 100;
  const baseVisibility = 30;

  return months.map((month, i) => ({
    month,
    // Simulate organic growth
    organicKeywords: Math.round(baseKeywords * (1 + i * 0.1)),
    // Competitors always slightly ahead
    competitorKeywords: Math.round(baseKeywords * (1.2 + i * 0.1)),
    // Average position improves over time (lower is better)
    avgPosition: Math.max(1, Math.round(10 - i * 0.5)),
    // Visibility increases over time
    visibility: Math.round(baseVisibility * (1 + i * 0.15)),
  }));
};

export function RegionalPerformanceTrends({
  crawlId,
  noData,
}: RegionalPerformanceTrendsProps) {
  // First get the crawl results to check if GSC is connected
  const { data: crawlResults } = useGetCrawlResults(crawlId);
  const hasGSCAccess =
    crawlResults?.has_gsc_data === true ||
    !!crawlResults?.gsc_refresh_token ||
    (!!crawlResults?.gsc_data &&
      Object.keys(crawlResults?.gsc_data).length > 0);

  // Only fetch trends data if we have GSC access
  const {
    data: trendsData,
    isLoading,
    error,
  } = useGetRegionalTrends(hasGSCAccess ? crawlId : undefined);

  if (noData) {
    return (
      <Card className="group hover:shadow-md transition-all bg-background/50">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <Select defaultValue="performance" disabled>
            <SelectTrigger className="w-[240px]">
              <SelectValue placeholder="Regional Performance Trends" />
            </SelectTrigger>
          </Select>
        </CardHeader>
        <CardContent>
          <Alert className="bg-blue-50/50">
            <Info className="h-4 w-4" />
            <AlertDescription>
              Run a crawl to see regional performance trends and competitor
              analysis.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  // Transform API data to match our chart format, or use estimated data
  const chartData: DataPoint[] =
    trendsData && hasGSCAccess && trendsData.labels?.length > 0
      ? trendsData.labels.map((month, index) => ({
          month,
          organicKeywords: trendsData.datasets?.organic_keywords?.[index] || 0,
          competitorKeywords:
            trendsData.datasets?.competitor_keywords?.[index] || 0,
          avgPosition: trendsData.datasets?.avg_position?.[index] || 0,
          visibility: trendsData.datasets?.visibility?.[index] || 0,
        }))
      : generateEstimatedData();

  // Show loading state
  if (isLoading && hasGSCAccess) {
    return (
      <Card className="group hover:shadow-md transition-all bg-background/50">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <Select defaultValue="performance" disabled>
            <SelectTrigger className="w-[240px]">
              <SelectValue placeholder="Regional Performance Trends" />
            </SelectTrigger>
          </Select>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] w-full flex items-center justify-center">
            <div className="animate-pulse bg-muted rounded-lg w-full h-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="group hover:shadow-md transition-all bg-background/50">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div className="flex items-center gap-2">
          <Select defaultValue="performance">
            <SelectTrigger className="w-[240px]">
              <SelectValue placeholder="Regional Performance Trends" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="performance">
                Regional Performance Trends
              </SelectItem>
              <SelectItem value="visibility">Visibility Trends</SelectItem>
              <SelectItem value="keywords">Keyword Trends</SelectItem>
            </SelectContent>
          </Select>
          {!hasGSCAccess && (
            <div className="flex items-center text-xs text-muted-foreground gap-1">
              <Info size={12} />
              <span>Estimated data</span>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {!hasGSCAccess && (
          <Alert className="mb-4 bg-yellow-50/50">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Using Estimated Data</AlertTitle>
            <AlertDescription className="mt-2">
              <p>
                Currently showing estimated trends based on industry averages.
                For accurate, real-time data from your website, connect Google
                Search Console.
              </p>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() => {
                  // Scroll to the CrawlForm component
                  document
                    .querySelector("form")
                    ?.scrollIntoView({ behavior: "smooth" });
                }}
              >
                Connect GSC
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {error && hasGSCAccess ? (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load regional trends data. Please try again later.
            </AlertDescription>
          </Alert>
        ) : (
          <div className="h-[300px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={chartData}
                margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
              >
                <XAxis
                  dataKey="month"
                  stroke="#888888"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  stroke="#888888"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => `${value}`}
                />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="organicKeywords"
                  name={`${!hasGSCAccess ? "~ " : ""}Organic Keywords`}
                  stroke="#2563eb"
                  strokeWidth={2}
                  dot={false}
                />
                <Line
                  type="monotone"
                  dataKey="competitorKeywords"
                  stroke="#9333ea"
                  name={`${!hasGSCAccess ? "~ " : ""}Competitor Keywords`}
                  strokeWidth={2}
                  dot={false}
                />
                {hasGSCAccess && trendsData?.datasets?.avg_position && (
                  <Line
                    type="monotone"
                    dataKey="avgPosition"
                    stroke="#f59e0b"
                    name="Avg. Position"
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    dot={false}
                  />
                )}
                {hasGSCAccess && trendsData?.datasets?.visibility && (
                  <Line
                    type="monotone"
                    dataKey="visibility"
                    name="Visibility %"
                    stroke="#f97316"
                    strokeWidth={2}
                    dot={false}
                  />
                )}
                <Legend
                  verticalAlign="bottom"
                  height={24}
                  iconType="line"
                  iconSize={8}
                  wrapperStyle={{
                    fontSize: "10px",
                    paddingTop: "8px",
                  }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
