"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { usePathname } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";

export function GlobalProgressBar() {
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    let progressInterval: NodeJS.Timeout;
    let timeoutId: NodeJS.Timeout;
    let isNavigating = false;

    const startProgress = () => {
      if (isNavigating) return; // Prevent multiple starts

      isNavigating = true;
      setIsLoading(true);
      setProgress(0);

      // Simulate progress with realistic timing
      progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          // Logarithmic progress - fast start, slower middle, holds at 90%
          const increment = prev < 30 ? 15 : prev < 60 ? 8 : prev < 80 ? 4 : 2;
          return Math.min(prev + increment + Math.random() * 5, 90);
        });
      }, 100);

      // Safety timeout - complete after 5 seconds
      timeoutId = setTimeout(() => {
        completeProgress();
      }, 5000);
    };

    const completeProgress = () => {
      if (!isNavigating) return; // Prevent multiple completions

      isNavigating = false;
      if (progressInterval) clearInterval(progressInterval);
      if (timeoutId) clearTimeout(timeoutId);

      setProgress(100);
      setTimeout(() => {
        setIsLoading(false);
        setProgress(0);
      }, 300);
    };

    // Track pathname changes
    let previousPathname = pathname;

    const checkPathnameChange = () => {
      const currentPathname = window.location.pathname;
      if (currentPathname !== previousPathname) {
        previousPathname = currentPathname;
        startProgress();

        // Complete after DOM updates
        setTimeout(() => {
          completeProgress();
        }, 800);
      }
    };

    // Check for pathname changes every 50ms for faster detection
    const pathnameInterval = setInterval(checkPathnameChange, 50);

    // Override router push to trigger progress
    const originalPush = router.push;
    router.push = (...args) => {
      startProgress();
      const result = originalPush.apply(router, args);

      // Complete progress after a delay if still loading
      setTimeout(() => {
        if (isNavigating) {
          completeProgress();
        }
      }, 1500);

      return result;
    };

    // Listen for browser back/forward navigation
    const handlePopState = () => {
      startProgress();
      setTimeout(() => {
        completeProgress();
      }, 800);
    };

    window.addEventListener("popstate", handlePopState);

    return () => {
      if (progressInterval) clearInterval(progressInterval);
      if (timeoutId) clearTimeout(timeoutId);
      clearInterval(pathnameInterval);
      window.removeEventListener("popstate", handlePopState);

      // Restore original router.push
      router.push = originalPush;
    };
  }, [router]);

  return (
    <AnimatePresence>
      {isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed top-0 left-0 right-0 z-[9999] pointer-events-none"
        >
          {/* Gradient progress bar */}
          <div className="h-1 bg-gradient-to-r from-background to-background">
            <motion.div
              className={cn(
                "h-full bg-gradient-to-r from-primary via-primary/80 to-primary",
                "shadow-lg shadow-primary/25"
              )}
              initial={{ width: "0%" }}
              animate={{ width: `${progress}%` }}
              transition={{
                duration: 0.2,
                ease: "easeOut",
              }}
            />
          </div>

          {/* Shimmer effect */}
          <motion.div
            className={cn(
              "absolute top-0 h-1 w-32 bg-gradient-to-r",
              "from-transparent via-white/30 to-transparent",
              "blur-sm"
            )}
            animate={{
              x: ["0%", "300%"],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut",
            }}
            style={{
              left: `${Math.max(0, progress - 20)}%`,
            }}
          />

          {/* Loading indicator in top-right corner */}
          <div className="absolute top-4 right-4 flex items-center gap-2 bg-background/90 backdrop-blur-sm rounded-full px-3 py-1 shadow-lg border border-border/50">
            <motion.div
              className="w-2 h-2 bg-primary rounded-full"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [1, 0.7, 1],
              }}
              transition={{
                duration: 1,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
            <span className="text-xs font-medium text-muted-foreground">
              Loading...
            </span>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
