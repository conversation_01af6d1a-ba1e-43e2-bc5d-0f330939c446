"use client";

import { useState } from "react";
import { cn } from "@/lib/utils";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import AppSidebar from "../Sidebar";
import Topbar from "../Topbar";
import Footer from "../Footer";
import Breadcrumbs from "../Breadcrumbs";
import AIAssistant from "../AIAssistant";

interface PageWrapperProps {
  children: React.ReactNode;
  className?: string;
  isAdmin?: boolean;
}

export default function PageWrapper({
  children,
  className,
  isAdmin = false,
}: PageWrapperProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <SidebarProvider defaultOpen={true}>
      <div className="flex h-screen bg-muted w-full">
        <AppSidebar className="hidden md:block z-30" isAdmin={isAdmin} />

        <div className="flex-1 flex flex-col min-w-0 w-full overflow-hidden">
          {/* Keep Topbar at the top, but below sidebar header */}
          <div className="z-20 w-full flex-shrink-0 flex items-center gap-2 bg-background">
            <SidebarTrigger className="ml-4 my-2" />
            <div className="flex-1">
              <Topbar
                toggleMobileMenu={() => setMobileMenuOpen(!mobileMenuOpen)}
              />
            </div>
          </div>

          <main
            className={cn(
              "flex-1 flex flex-col overflow-auto w-full pb-14",
              className
            )}
          >
            <div className="p-4 md:p-6 space-y-4 flex-1 w-full max-w-none">
              <Breadcrumbs />
              {children}
            </div>
            {/* <AIAssistant /> */}
          </main>
          <Footer />
        </div>

        {/* Mobile Sidebar Overlay */}
        <div
          className={cn(
            "fixed inset-0 z-50 bg-background/80 backdrop-blur-sm md:hidden transition-all duration-300 ease-in-out",
            mobileMenuOpen ? "opacity-100 visible" : "opacity-0 invisible"
          )}
          onClick={() => setMobileMenuOpen(false)}
        >
          {/* Sidebar wrapper with slide-in effect */}
          <div
            className={cn(
              "absolute left-0 top-0 h-full w-64 transition-transform duration-300 transform bg-background",
              mobileMenuOpen ? "translate-x-0" : "-translate-x-full"
            )}
            onClick={(e) => e.stopPropagation()} // Prevent closing if user clicks inside sidebar
          >
            <AppSidebar isAdmin={isAdmin} />
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
}
