import React, { useState } from "react";
import { useUpdateProfile } from "@/internal-api/update-profile";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Pencil,
  X,
  Loader2,
  User,
  Building,
  Globe,
  Save,
  ArrowLeft,
  Mail,
  Link,
} from "lucide-react";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter } from "next/navigation";
import { useTheme } from "@mui/material";

// Available industry options (lowercase to match backend choices)
const INDUSTRIES = ["web3", "technology", "fintech"] as const;

interface UpdateProfileData {
  firstname: string;
  lastname: string;
  company_name: string;
  website: string;
  industry: string;
}

interface UpdateProfileFormProps {
  initialFirstName: string;
  initialLastName: string;
  initialCompanyName: string;
  initialWebsite: string;
  initialIndustry: string;
  onUpdateSuccess: () => void;
}

export default function UpdateProfileForm({
  initialFirstName,
  initialLastName,
  initialCompanyName,
  initialWebsite,
  initialIndustry,
  onUpdateSuccess,
}: UpdateProfileFormProps) {
  const router = useRouter();
  const theme = useTheme();
  const [firstName, setFirstName] = useState(initialFirstName);
  const [lastName, setLastName] = useState(initialLastName);
  const [companyName, setCompanyName] = useState(initialCompanyName);
  const [website, setWebsite] = useState(initialWebsite);
  const [industry, setIndustry] = useState<string>(initialIndustry);
  const [error, setError] = useState<string | null>(null);
  const updateProfileMutation = useUpdateProfile();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    try {
      await updateProfileMutation.mutateAsync({
        firstname: firstName.trim(),
        lastname: lastName.trim(),
        company_name: companyName.trim(),
        website: website.trim(),
        industry: industry,
      });
      router.refresh();
      onUpdateSuccess();
    } catch (error: any) {
      console.error("Profile update error:", error);
      setError(
        error.response?.data?.error ||
          error.message ||
          "Failed to update profile"
      );
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Card */}
      <Card className="overflow-hidden">
        <div className="relative h-20 bg-gradient-to-r from-primary/10 via-primary/5 to-background">
          <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>
          <div className="relative px-6 py-4 flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold tracking-tight">
                Edit Profile
              </h2>
              <p className="text-sm text-muted-foreground">
                Update your personal information
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={onUpdateSuccess}
              className="backdrop-blur-sm"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Profile
            </Button>
          </div>
        </div>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Personal Information Card */}
        <Card className="border-none shadow-sm bg-muted/30">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-semibold flex items-center gap-3">
              <div className="p-2 rounded-full bg-primary/10">
                <User className="h-5 w-5 text-primary" />
              </div>
              Personal Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="firstName" className="text-sm font-medium">
                  First Name
                </Label>
                <div className="relative">
                  <Input
                    id="firstName"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    className="pl-10 transition-all focus:ring-2 focus:ring-primary/20"
                    placeholder="Enter your first name"
                    required
                  />
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName" className="text-sm font-medium">
                  Last Name
                </Label>
                <div className="relative">
                  <Input
                    id="lastName"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    className="pl-10 transition-all focus:ring-2 focus:ring-primary/20"
                    placeholder="Enter your last name"
                    required
                  />
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Company Information Card */}
        <Card className="border-none shadow-sm bg-muted/30">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-semibold flex items-center gap-3">
              <div className="p-2 rounded-full bg-primary/10">
                <Building className="h-5 w-5 text-primary" />
              </div>
              Company Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="companyName" className="text-sm font-medium">
                  Company Name
                </Label>
                <div className="relative">
                  <Input
                    id="companyName"
                    value={companyName}
                    onChange={(e) => setCompanyName(e.target.value)}
                    className="pl-10 transition-all focus:ring-2 focus:ring-primary/20"
                    placeholder="Enter your company name"
                    required
                  />
                  <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="website" className="text-sm font-medium">
                  Website
                </Label>
                <div className="relative">
                  <Input
                    id="website"
                    value={website}
                    onChange={(e) => setWebsite(e.target.value)}
                    className="pl-10 transition-all focus:ring-2 focus:ring-primary/20"
                    placeholder="https://yourcompany.com"
                    required
                  />
                  <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="industry" className="text-sm font-medium">
                  Industry
                </Label>
                <Select
                  value={industry}
                  onValueChange={(value) => setIndustry(value)}
                >
                  <SelectTrigger className="w-full transition-all focus:ring-2 focus:ring-primary/20">
                    <SelectValue placeholder="Select your industry" />
                  </SelectTrigger>
                  <SelectContent>
                    {INDUSTRIES.map((ind) => (
                      <SelectItem key={ind} value={ind}>
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4" />
                          {ind.charAt(0).toUpperCase() + ind.slice(1)}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons Card */}
        <Card className="border-none shadow-sm bg-gradient-to-r from-primary/5 to-primary/10">
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                type="submit"
                size="lg"
                className="flex-1 h-12"
                disabled={updateProfileMutation.isLoading}
                style={{
                  backgroundColor: theme.palette.primary.main,
                  color: theme.palette.primary.contrastText,
                }}
              >
                {updateProfileMutation.isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    Saving Changes...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-5 w-5" />
                    Save Changes
                  </>
                )}
              </Button>

              <Button
                type="button"
                variant="outline"
                size="lg"
                onClick={onUpdateSuccess}
                className="flex-1 h-12"
                disabled={updateProfileMutation.isLoading}
              >
                <X className="mr-2 h-5 w-5" />
                Cancel
              </Button>
            </div>

            <div className="mt-4 text-center">
              <p className="text-sm text-muted-foreground">
                Your changes will be saved immediately and reflected across the
                platform
              </p>
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  );
}
