"use client";

import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Users,
  UserPlus,
  Mail,
  Calendar,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { UserData } from "@/internal-api/get-user-data";

interface SubUsersComponentProps {
  userData: UserData;
}

const SubUsersComponent: React.FC<SubUsersComponentProps> = ({ userData }) => {
  const { created_users, created_users_count, remaining_user_slots } = userData;

  // Don't show if not a sub-admin or no created users
  if (userData.role !== "sub_admin") {
    return null;
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName?.charAt(0) || ""}${lastName?.charAt(0) || ""}`.toUpperCase();
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Users className="h-6 w-6 text-primary" />
            <div>
              <CardTitle className="text-xl">My Users</CardTitle>
              <CardDescription>
                Users you have created and manage ({created_users_count || 0}/3
                used)
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Badge
              variant={remaining_user_slots === 0 ? "destructive" : "secondary"}
            >
              {remaining_user_slots || 0} slots remaining
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {!created_users || created_users.length === 0 ? (
          <div className="text-center py-8">
            <UserPlus className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-muted-foreground mb-2">
              No users created yet
            </h3>
            <p className="text-sm text-muted-foreground">
              You can create up to 3 users. Contact your administrator to create
              your first user.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {created_users.map((user) => (
              <div
                key={user.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-center space-x-4">
                  <Avatar className="h-10 w-10">
                    <AvatarImage
                      src=""
                      alt={`${user.first_name} ${user.last_name}`}
                    />
                    <AvatarFallback className="bg-primary/10 text-primary">
                      {getInitials(user.first_name, user.last_name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <h4 className="text-sm font-medium text-foreground truncate">
                        {user.first_name} {user.last_name}
                      </h4>
                      <Badge variant="outline" className="text-xs">
                        @{user.username}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-4 mt-1">
                      <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                        <Mail className="h-3 w-3" />
                        <span className="truncate">{user.email}</span>
                      </div>
                      <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                        <Calendar className="h-3 w-3" />
                        <span>Joined {formatDate(user.registration_date)}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge
                    variant={user.is_active ? "success" : "secondary"}
                    className="flex items-center space-x-1"
                  >
                    {user.is_active ? (
                      <CheckCircle className="h-3 w-3" />
                    ) : (
                      <XCircle className="h-3 w-3" />
                    )}
                    <span>{user.is_active ? "Active" : "Inactive"}</span>
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Summary footer */}
        <div className="mt-6 pt-4 border-t border-border">
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>Total users: {created_users_count || 0}</span>
            <span>Remaining slots: {remaining_user_slots || 0}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SubUsersComponent;
