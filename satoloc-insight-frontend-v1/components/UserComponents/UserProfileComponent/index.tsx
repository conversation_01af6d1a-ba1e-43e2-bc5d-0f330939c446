// components/UserComponents/UserProfileComponent/index.tsx

import React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Pencil,
  Mail,
  User,
  Link,
  Building,
  Calendar,
  ExternalLink,
  Crown,
  CreditCard,
  Shield,
  Zap,
  Star,
  Globe,
  MapPin,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useTheme } from "@mui/material";
import { Separator } from "@/components/ui/separator";

interface UserProfileProps {
  userData: {
    username: string;
    email: string;
    first_name: string;
    last_name: string;
    company_name: string;
    website: string;
    industry: string;
    avatar?: string;
    registrationDate?: string;
    role?: string;
    name: string;
    created: string;
    subscriptionPlan?: string;
    subscriptionPrice?: string;
    subscriptionStartDate?: string;
  };
  onEdit: () => void;
}

export default function UserProfileComponent({
  userData,
  onEdit,
}: UserProfileProps) {
  const initials =
    `${userData.first_name?.[0]}${userData.last_name?.[0]}`.toUpperCase();
  const theme = useTheme();

  // Get subscription details
  const getSubscriptionIcon = (plan: string) => {
    switch (plan?.toLowerCase()) {
      case "freemium":
        return <Shield className="h-5 w-5 text-blue-500" />;
      case "pro":
        return <Zap className="h-5 w-5 text-purple-500" />;
      case "enterprise":
        return <Star className="h-5 w-5 text-yellow-500" />;
      default:
        return <Crown className="h-5 w-5 text-gray-500" />;
    }
  };

  const getSubscriptionColor = (plan: string) => {
    switch (plan?.toLowerCase()) {
      case "freemium":
        return "bg-background border-blue-200 dark:border-blue-800";
      case "pro":
        return "bg-background border-purple-200 dark:border-purple-800";
      case "enterprise":
        return "bg-background border-yellow-200 dark:border-yellow-800";
      default:
        return "bg-background border-gray-200 dark:border-gray-800";
    }
  };

  const getBadgeVariant = (plan: string) => {
    switch (plan?.toLowerCase()) {
      case "freemium":
        return "secondary";
      case "pro":
        return "default";
      case "enterprise":
        return "destructive";
      default:
        return "outline";
    }
  };

  const formatSubscriptionStartDate = (dateString?: string) => {
    if (!dateString) return "Unknown";
    try {
      return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } catch {
      return "Unknown";
    }
  };

  return (
    <div className="space-y-6">
      {/* Main Profile Card */}
      <Card className="overflow-hidden">
        {/* Header with gradient background */}
        <div className="relative h-32 bg-[#1279b4]">
          <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>
          <Button
            variant="outline"
            size="sm"
            onClick={onEdit}
            className="absolute top-4 right-4 hover:shadow-md transition-all backdrop-blur-sm"
            style={{
              backgroundColor: theme.palette.primary.main,
              color: theme.palette.primary.contrastText,
            }}
          >
            <Pencil className="mr-2 h-4 w-4" />
            Edit Profile
          </Button>
        </div>

        {/* Profile Content */}
        <div className="relative px-6 pb-6 -mt-12">
          {/* Avatar and Basic Info */}
          <div className="flex flex-col sm:flex-row sm:items-end gap-6 mb-8">
            <Avatar className="h-24 w-24 border-4 border-background shadow-xl">
              <AvatarImage src={userData.avatar} alt={userData.username} />
              <AvatarFallback className="text-2xl font-bold">
                {initials}
              </AvatarFallback>
            </Avatar>

            <div className="flex-1 space-y-2">
              <div>
                {/* <h2 className="text-3xl font-bold tracking-tight text-white">
                  {userData.first_name && userData.last_name
                    ? `${userData.first_name} ${userData.last_name}`
                    : userData.username}
                </h2> */}
                {/* <p className="text-xl text-muted-foreground">
                  @{userData.username}
                </p> */}
              </div>

              <div className="flex flex-wrap items-center gap-3">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Mail className="h-4 w-4" />
                  {userData.email}
                </div>
                {userData.role && (
                  <Badge variant="outline" className="capitalize">
                    {userData.role}
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Info Cards Grid */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {/* Personal Information */}
            <Card className="border-none shadow-sm bg-muted/30">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <User className="h-4 w-4 text-primary" />
                  Personal Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="text-sm font-medium">Full Name</p>
                  <p className="text-sm text-muted-foreground">
                    {userData.first_name && userData.last_name
                      ? `${userData.first_name} ${userData.last_name}`
                      : "Not provided"}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">Email</p>
                  <p className="text-sm text-muted-foreground truncate">
                    {userData.email}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Company Information */}
            <Card className="border-none shadow-sm bg-muted/30">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Building className="h-4 w-4 text-primary" />
                  Company Info
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="text-sm font-medium">Company</p>
                  <p className="text-sm text-muted-foreground">
                    {userData.company_name || "Not provided"}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">Industry</p>
                  <p className="text-sm text-muted-foreground capitalize">
                    {userData.industry || "Not specified"}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Website & Links */}
            <Card className="border-none shadow-sm bg-muted/30 md:col-span-2 lg:col-span-1">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Globe className="h-4 w-4 text-primary" />
                  Website & Links
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="text-sm font-medium">Website</p>
                  {userData.website ? (
                    <div className="flex items-center gap-2">
                      <a
                        href={userData.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-primary hover:underline truncate"
                      >
                        {userData.website}
                      </a>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6"
                        onClick={() => window.open(userData.website, "_blank")}
                      >
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">
                      Not provided
                    </p>
                  )}
                </div>
                <div>
                  <p className="text-sm font-medium">Member since</p>
                  <p className="text-sm text-muted-foreground">
                    {userData.created
                      ? new Date(userData.created).toLocaleDateString()
                      : "Unknown"}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </Card>

      {/* Subscription Card */}
      {userData.subscriptionPlan && (
        <Card
          className={`overflow-hidden border-2 ${getSubscriptionColor(userData.subscriptionPlan)}`}
        >
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-semibold flex items-center gap-3">
              {getSubscriptionIcon(userData.subscriptionPlan)}
              <span className="capitalize">
                {userData.subscriptionPlan} Plan
              </span>
              <Badge
                variant={getBadgeVariant(userData.subscriptionPlan)}
                className="ml-auto"
              >
                Active
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Billing</span>
                </div>
                <p className="text-lg font-bold">
                  {userData.subscriptionPrice &&
                  userData.subscriptionPrice !== "Free"
                    ? `$${userData.subscriptionPrice}/month`
                    : "Free"}
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Started</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  {formatSubscriptionStartDate(userData.subscriptionStartDate)}
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Star className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Features</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  {userData.subscriptionPlan === "freemium" && "Basic features"}
                  {userData.subscriptionPlan === "pro" && "Advanced analytics"}
                  {userData.subscriptionPlan === "enterprise" && "Full access"}
                </p>
              </div>
            </div>

            {userData.subscriptionPlan !== "enterprise" && (
              <div className="mt-6 pt-4 border-t border-border/50">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    Want to unlock more features?
                  </p>
                  <Button variant="outline" size="sm" className="ml-4">
                    <Crown className="h-4 w-4 mr-2" />
                    Upgrade Plan
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
