// components/WebScraper/index.tsx
import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Globe, Loader2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useTheme } from "@mui/material";
import apiClient from "@/lib/apiClient";
import { LoadingModal } from "@/components/LoadingModal";
import Link from "next/link";

interface DomNode {
  tagName: string;
  attributes: { [key: string]: string };
  children: DomNode[];
  textContent: string;
}

interface UrlHistory {
  sourceUrl: string;
  targetUrl?: string;
  timestamp: number;
  tags?: string[];
  service?: string;
  sourceLanguage?: string;
  targetLanguage?: string;
  industry?: string;
}

interface Industry {
  name: string;
  disabled: boolean;
}

interface ScrapingStatus {
  status:
    | "idle"
    | "pending"
    | "processing"
    | "processing_ai"
    | "completed"
    | "failed";
}

interface Service {
  name: string;
  disabled?: boolean;
  comingSoon?: boolean;
}

interface WebScraperProps {
  onScrapingComplete?: (projectId: number) => void;
}

const SERVICES: Service[] = [
  {
    name: "Localization Insights",
    disabled: false,
  },
  {
    name: "SEO Insights",
    disabled: true,
    comingSoon: true,
  },
  {
    name: "Content Generation & Insights",
    disabled: true,
    comingSoon: true,
  },
];
const SOURCE_LANGUAGES = [
  "English",
  "Turkish",
  "Vietnamese",
  "Spanish",
  "Russian",
  "Arabic",
  "French",
  "Portuguese (Brazil)",
  "German",
  "Polish",
  "Italian",
  "Chinese",
] as const;
const TARGET_LANGUAGES = [
  "English",
  "Turkish",
  "Vietnamese",
  "Spanish",
  "Russian",
  "Arabic",
  "French",
  "Portuguese (Brazil)",
  "German",
  "Polish",
  "Italian",
  "Chinese",
] as const;
const INDUSTRIES: Industry[] = [
  { name: "Fintech", disabled: false },
  { name: "Web3", disabled: false },
  { name: "Technology", disabled: false },
  { name: "Finance", disabled: false },
  { name: "Education", disabled: false },
  { name: "Healthcare", disabled: false },
  { name: "E-commerce", disabled: false },
  { name: "Gaming", disabled: false },
  { name: "Travel & Tourism", disabled: false },
  { name: "Real Estate", disabled: false },
  { name: "Automotive", disabled: false },
  { name: "Food & Beverage", disabled: false },
  { name: "Fashion & Retail", disabled: false },
  { name: "Media & Entertainment", disabled: false },
  { name: "Manufacturing", disabled: false },
  { name: "Consulting", disabled: false },
  { name: "Insurance", disabled: false },
  { name: "Telecommunications", disabled: false },
  { name: "Government", disabled: false },
  { name: "Other", disabled: false },
];

const LOCAL_STORAGE_KEY = "url-scraper-history";

export default function WebScraper({ onScrapingComplete }: WebScraperProps) {
  const { data: session } = useSession();
  const theme = useTheme();

  const [isDualMode, setIsDualMode] = useState(false);
  const [sourceUrl, setSourceUrl] = useState("");
  const [targetUrl, setTargetUrl] = useState("");

  const [scrapedData, setScrapedData] = useState<DomNode[] | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filterTags, setFilterTags] = useState<string>("");
  const [showHistory, setShowHistory] = useState(true);

  const [scrapingStatus, setScrapingStatus] =
    useState<ScrapingStatus["status"]>("idle");

  const [loadingStatus, setLoadingStatus] = useState<
    "idle" | "loading" | "completed" | "failed"
  >("idle");

  // New form fields
  const [service, setService] = useState<string>(SERVICES[0].name);
  const [sourceLanguage, setSourceLanguage] =
    useState<(typeof SOURCE_LANGUAGES)[number]>("English");
  const [targetLanguage, setTargetLanguage] =
    useState<(typeof TARGET_LANGUAGES)[number]>("Turkish");
  const [industry, setIndustry] = useState<string>(INDUSTRIES[0].name);

  // For local history
  const [urlHistory, setUrlHistory] = useState<UrlHistory[] | null>(null);

  // Load from localStorage on mount
  useEffect(() => {
    try {
      const savedHistory = localStorage.getItem(LOCAL_STORAGE_KEY);
      if (savedHistory) {
        setUrlHistory(JSON.parse(savedHistory));
      } else {
        setUrlHistory([]);
      }
    } catch (error) {
      console.error("Error loading history from localStorage:", error);
      setUrlHistory([]);
    }
  }, []);

  // Persist to localStorage whenever urlHistory changes
  useEffect(() => {
    if (urlHistory !== null) {
      try {
        localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(urlHistory));
      } catch (error) {
        console.error("Error saving history to localStorage:", error);
      }
    }
  }, [urlHistory]);

  const addToHistory = (newSourceUrl: string, newTargetUrl?: string) => {
    const now = Date.now();
    const tags = filterTags
      .split(",")
      .map((tag) => tag.trim())
      .filter(Boolean);

    setUrlHistory((prev) => {
      if (!prev) return null;
      const filtered = prev.filter(
        (item) =>
          !(item.sourceUrl === newSourceUrl && item.targetUrl === newTargetUrl)
      );
      return [
        {
          sourceUrl: newSourceUrl,
          targetUrl: newTargetUrl,
          timestamp: now,
          tags,
          service,
          sourceLanguage,
          targetLanguage,
          industry,
        },
        ...filtered,
      ];
    });
  };

  const removeFromHistory = (
    sourceUrlToRemove: string,
    targetUrlToRemove?: string
  ) => {
    setUrlHistory((prev) => {
      if (!prev) return null;
      return prev.filter(
        (item) =>
          !(
            item.sourceUrl === sourceUrlToRemove &&
            item.targetUrl === (targetUrlToRemove || "")
          )
      );
    });
  };

  const handleSourceLanguageChange = (
    value: (typeof SOURCE_LANGUAGES)[number]
  ) => {
    setSourceLanguage(value);
    // If target language is the same as new source language, pick a different language
    if (value === targetLanguage) {
      // Find the first language that's different from the current selection
      const differentLanguage = SOURCE_LANGUAGES.find((lang) => lang !== value);
      setTargetLanguage(differentLanguage || "English");
    }
  };

  const handleTargetLanguageChange = (
    value: (typeof TARGET_LANGUAGES)[number]
  ) => {
    setTargetLanguage(value);
    // If source language is the same as new target language, pick a different language
    if (value === sourceLanguage) {
      // Find the first language that's different from the current selection
      const differentLanguage = TARGET_LANGUAGES.find((lang) => lang !== value);
      setSourceLanguage(differentLanguage || "English");
    }
  };

  const handleScrape = async () => {
    setLoadingStatus("loading");
    setScrapingStatus("pending");
    setError(null);
    setScrapedData(null);

    try {
      if (!sourceUrl) {
        throw new Error("Source URL is required");
      }

      if (!session?.accessToken) {
        throw new Error("Please log in to use this feature");
      }

      // 1) Create a Project
      const projectResponse = await apiClient.post("/scraping/projects/", {
        name: `Scrape Project - ${new Date().toLocaleString()}`,
        description: `Scraping data from ${sourceUrl} (and possibly ${targetUrl})`,
        service,
        sourceLanguage,
        targetLanguage,
        industry,
      });

      const projectId = projectResponse.data.id;

      // 2) Perform the scraping
      const scrapeResponse = await apiClient.post(
        "/scraping/scrape/",
        {
          source_url: sourceUrl,
          target_url: targetUrl.trim() ? targetUrl : null,
          project_id: projectId,
          service,
          sourceLanguage,
          targetLanguage,
          industry,
        },
        {
          headers: {
            Authorization: `Bearer ${session.accessToken}`,
          },
        }
      );

      const data = scrapeResponse.data;
      const scrapedDataId = data.id;

      // Add to local history
      addToHistory(sourceUrl, targetUrl);

      // 3) Poll for status and only complete when ready
      let checkCount = 0;
      const maxChecks = 60; // check up to 60 times (5 minutes if each is 5s)

      const checkStatus = async () => {
        try {
          const statusResponse = await apiClient.get(
            `/scraping/scraped_data/${scrapedDataId}/`,
            {
              headers: {
                Authorization: `Bearer ${session.accessToken}`,
              },
            }
          );
          const currentStatus = statusResponse.data.status;
          setScrapingStatus(currentStatus);

          if (currentStatus === "completed") {
            setLoadingStatus("completed");
            // Only call onScrapingComplete when the scraping is actually complete
            if (onScrapingComplete) {
              onScrapingComplete(projectId);
            }
          } else if (currentStatus === "failed") {
            setLoadingStatus("failed");
            throw new Error("Scraping process failed");
          } else if (checkCount < maxChecks) {
            checkCount++;
            setTimeout(checkStatus, 5000);
          } else {
            setLoadingStatus("failed");
            throw new Error("Scraping process timed out");
          }
        } catch (pollError) {
          console.error("Error in checkStatus:", pollError);
          setError("Failed to get scraping status");
          setScrapingStatus("failed");
          setLoadingStatus("failed");
        }
      };

      setTimeout(checkStatus, 5000);
    } catch (err: any) {
      console.error("Scraping error:", err);
      const errorMessage =
        err.response?.data?.error ||
        err.response?.data?.detail ||
        err.message ||
        "Failed to scrape the website. Please check the URLs and try again.";
      setError(errorMessage);
      setScrapingStatus("failed");
      setLoadingStatus("failed");
    }
  };

  return (
    <div>
      <LoadingModal
        isOpen={loadingStatus !== "idle"}
        status={
          loadingStatus === "completed"
            ? "completed"
            : loadingStatus === "failed"
              ? "failed"
              : "loading"
        }
        onClose={() => setLoadingStatus("idle")}
      />

      <Card className="w-full shadow-none rounded-md">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl font-semibold">
                Get Insights
              </CardTitle>
              <CardDescription className="text-xs text-muted-foreground">
                Enter your website URL to receive customized insights and
                analysis.
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsDualMode(!isDualMode)}
                className="hidden md:flex items-center gap-2"
              >
                <Globe className="h-4 w-4" />
                {isDualMode ? "Single URL" : "Dual URL"}
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* URL Input Section */}
          <div
            className="grid gap-4"
            style={{ gridTemplateColumns: isDualMode ? "1fr 1fr" : "1fr" }}
          >
            <div>
              <Label
                htmlFor="sourceUrl"
                className="text-sm flex items-center gap-2 mb-2"
              >
                {isDualMode ? "Source URL" : "Website URL"}
                <Globe className="h-3 w-3 text-muted-foreground" />
              </Label>
              <Input
                id="sourceUrl"
                placeholder="https://example.com"
                value={sourceUrl}
                onChange={(e) => setSourceUrl(e.target.value)}
              />
            </div>

            {isDualMode && (
              <div>
                <Label
                  htmlFor="targetUrl"
                  className="text-sm flex items-center gap-2 mb-2"
                >
                  Target URL
                  <Globe className="h-3 w-3 text-muted-foreground" />
                </Label>
                <Input
                  id="targetUrl"
                  placeholder="https://example-target.com"
                  value={targetUrl}
                  onChange={(e) => setTargetUrl(e.target.value)}
                />
              </div>
            )}
          </div>

          {/* Settings Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="service" className="text-sm mb-2">
                Service
              </Label>
              <Select
                value={service}
                onValueChange={(value) => setService(value)}
              >
                <SelectTrigger id="service">
                  <SelectValue placeholder="Select service" />
                </SelectTrigger>
                <SelectContent>
                  {SERVICES.map((item) => (
                    <SelectItem
                      key={item.name}
                      value={item.name}
                      disabled={item.disabled}
                    >
                      <div className="flex items-center justify-between w-full">
                        {item.name}
                        {item.comingSoon && (
                          <Badge variant="secondary" className="ml-2">
                            Soon
                          </Badge>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="industry" className="text-sm mb-2">
                Industry
              </Label>
              <Select
                value={industry}
                onValueChange={(value) => setIndustry(value)}
              >
                <SelectTrigger id="industry">
                  <SelectValue placeholder="Select industry" />
                </SelectTrigger>
                <SelectContent>
                  {INDUSTRIES.map((ind) => (
                    <SelectItem
                      key={ind.name}
                      value={ind.name}
                      disabled={ind.disabled}
                    >
                      <div className="flex items-center justify-between w-full">
                        {ind.name}
                        {ind.disabled && (
                          <Badge variant="secondary" className="ml-2">
                            Soon
                          </Badge>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="sourceLanguage" className="text-sm mb-2">
                Source Language
              </Label>
              <Select
                value={sourceLanguage}
                onValueChange={handleSourceLanguageChange}
              >
                <SelectTrigger id="sourceLanguage">
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  {SOURCE_LANGUAGES.map((lang) => (
                    <SelectItem key={lang} value={lang}>
                      {lang}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="targetLanguage" className="text-sm mb-2">
                Target Language
              </Label>
              <Select
                value={targetLanguage}
                onValueChange={handleTargetLanguageChange}
              >
                <SelectTrigger id="targetLanguage">
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  {TARGET_LANGUAGES.map((lang) => (
                    <SelectItem key={lang} value={lang}>
                      {lang}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Submit Section */}
          <div className="flex flex-col sm:flex-row items-start sm:items-end justify-end gap-4 pt-2">
            <div className="flex flex-col items-end gap-2">
              <Button
                onClick={handleScrape}
                disabled={loading || !sourceUrl}
                className="w-full sm:w-auto min-w-[200px] bg-[#1279b4] text-white"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Analyzing...
                  </>
                ) : (
                  <>
                    <Globe className="mr-2 h-4 w-4" />
                    Get Insights
                  </>
                )}
              </Button>
              <p className="text-xs text-muted-foreground text-right">
                By clicking "Get Insights", you accept our{" "}
                <Link
                  href="https://www.satoloc.com/terms-of-service-satoloc-insight/"
                  target="_blank"
                  className="text-primary hover:underline"
                >
                  Terms of Service ↗
                </Link>
              </p>
            </div>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
