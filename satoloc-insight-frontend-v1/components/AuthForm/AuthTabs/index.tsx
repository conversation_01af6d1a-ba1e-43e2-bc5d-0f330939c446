// src/components/AuthForm/AuthTabs.tsx
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface AuthTabsProps {
  activeTab: "login" | "registration";
  onTabChange: (value: "login" | "registration") => void;
}

export function AuthTabs({ activeTab, onTabChange }: AuthTabsProps) {
  return (
    <Tabs
      value={activeTab}
      onValueChange={(value: string) =>
        onTabChange(value as "login" | "registration")
      }
      className="w-full"
    >
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="login">Sign In</TabsTrigger>
        <TabsTrigger value="registration" disabled>Register</TabsTrigger>
      </TabsList>
    </Tabs>
  );
}
