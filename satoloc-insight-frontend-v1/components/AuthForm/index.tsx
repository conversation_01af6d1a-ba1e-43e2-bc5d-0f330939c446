import { useState, useC<PERSON>back, useEffect } from "react";
import Image from "next/image";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { signIn, useSession, getSession } from "next-auth/react";
import { useRegistration } from "@/internal-api/user-registration";
import { useLogin } from "@/internal-api/user-login";
import { useGoogleAuth } from "@/hooks/useGoogleAuth";
import { useStripeCheckout } from "@/hooks/use-stripe-checkout";
import { usePricing } from "@/hooks/use-pricing";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Eye, EyeOff, ChevronLeft } from "lucide-react";
import { useTheme } from "next-themes";
import { AuthTabs } from "./AuthTabs";
import { ConfirmationModal } from "@/components/ConfirmationModal";
import { motion } from "framer-motion";
import SatoLOCLogo from "@/public/images/satoloc_insight_logo.png";
import SatoLOCLogoWhite from "@/public/images/satoloc_insight_logo_white.png";

interface AuthFormData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  subscriptionPlan?: string;
  subscriptionPrice?: string;
}

type AuthMode = "login" | "registration";

export default function AuthForm() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const mode: AuthMode = pathname?.includes("registration")
    ? "registration"
    : "login";

  // Get subscription info from URL parameters
  const subscriptionPlan = searchParams?.get("plan");
  const subscriptionPrice = searchParams?.get("price");

  const [formData, setFormData] = useState<AuthFormData>({
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
    subscriptionPlan: subscriptionPlan || undefined,
    subscriptionPrice: subscriptionPrice || undefined,
  });

  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);

  const { data: session, status, update: updateSession } = useSession();
  const registrationMutation = useRegistration();
  const loginMutation = useLogin();
  const {
    handleGoogleSignIn,
    isLoading: googleLoading,
    error: googleError,
  } = useGoogleAuth();
  const { createCheckoutSession } = useStripeCheckout();
  const { products: stripeProducts } = usePricing();
  const { theme, resolvedTheme } = useTheme();
  const isDarkTheme = theme === "dark" || resolvedTheme === "dark";
  const logoSrc = isDarkTheme ? SatoLOCLogoWhite.src : SatoLOCLogo.src;

  useEffect(() => {
    if (status === "authenticated" && !openModal && !isProcessingPayment) {
      // Only redirect if not in the middle of a payment flow
      router.push("/profile");
    }
  }, [status, router, openModal, isProcessingPayment]);

  useEffect(() => {
    const channel = new BroadcastChannel("google-auth");
    const handleAuthMessage = (event: MessageEvent) => {
      if (event.data.status === "success") {
        window.location.reload();
      } else if (event.data.status === "error") {
        console.error(
          "[AuthForm] Auth error message received.",
          event.data.message
        );
        setErrorMessage(event.data.message || "Google authentication failed.");
      }
    };

    channel.addEventListener("message", handleAuthMessage);

    return () => {
      channel.removeEventListener("message", handleAuthMessage);
      channel.close();
    };
  }, [updateSession, router]);

  const handleTabChange = (value: AuthMode) => {
    setErrorMessage(null);
    setFormData({
      username: "",
      email: "",
      password: "",
      confirmPassword: "",
    });
    router.push(`/${value}`);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setErrorMessage(null); // Clear error when user types
    setIsLoading(false); // Reset loading state when user types
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMessage(null);
    setIsLoading(true);

    try {
      if (mode === "login") {
        const result = await signIn("credentials", {
          username: formData.username,
          password: formData.password,
          redirect: false,
        });

        if (result?.error) {
          setErrorMessage(result.error);
          setIsLoading(false);
          return;
        }

        if (result?.ok) {
          const session = await getSession();
          setIsLoading(false);

          if (session?.user?.isAdmin) {
            router.replace("/admin-dashboard");
          } else {
            router.replace("/profile");
          }
        }
      } else {
        // Registration
        if (formData.password !== formData.confirmPassword) {
          setErrorMessage("Passwords do not match");
          setIsLoading(false);
          return;
        }
        if (!agreeToTerms) {
          setErrorMessage("You must agree to the terms and conditions");
          setIsLoading(false);
          return;
        }

        try {
          await registrationMutation.mutateAsync({
            username: formData.username,
            email: formData.email,
            password: formData.password,
            confirmPassword: formData.confirmPassword,
            subscriptionPlan: formData.subscriptionPlan,
            subscriptionPrice: formData.subscriptionPrice,
          });
          setIsLoading(false);
          setOpenModal(true);
        } catch (error: any) {
          if (error.response?.data?.details) {
            const errorDetails = error.response.data.details;
            const errorMessages = Object.entries(errorDetails)
              .map(([key, value]) => `${key}: ${value}`)
              .join("\n");
            setErrorMessage(errorMessages);
          } else {
            setErrorMessage(error.message || "Registration failed");
          }
          setIsLoading(false);
        }
      }
    } catch (error) {
      setErrorMessage("An error occurred during authentication");
      setIsLoading(false);
    }
  };

  // Helper function to get Stripe price for a plan
  const getStripePriceForPlan = (planName: string) => {
    const planType = planName.toLowerCase();
    if (!["freemium", "pro", "premium"].includes(planType)) {
      return null;
    }
    const product = stripeProducts.find((p) => p.plan_type === planType);
    if (!product || !product.prices.length) {
      return null;
    }
    return product.prices.find((p) => p.is_active);
  };

  const handleCloseModal = useCallback(async () => {
    setOpenModal(false);

    // Check if this was a paid plan registration
    if (formData.subscriptionPlan && formData.subscriptionPlan !== "freemium") {
      try {
        // Set processing payment state to prevent automatic redirect
        setIsProcessingPayment(true);

        // Get the Stripe price for the plan
        const stripePrice = getStripePriceForPlan(formData.subscriptionPlan);

        if (stripePrice) {
          // Sign in the user first
          const result = await signIn("credentials", {
            username: formData.username,
            password: formData.password,
            redirect: false,
          });

          if (result?.ok) {
            // Wait for session to be established and update
            await updateSession();

            // Additional delay to ensure session is fully established
            await new Promise((resolve) => setTimeout(resolve, 2000));

            // User is now authenticated, proceed with Stripe checkout
            await createCheckoutSession({
              price_id: stripePrice.stripe_price_id,
              success_url: `${window.location.origin}/profile?session_id={CHECKOUT_SESSION_ID}&payment_success=true`,
              cancel_url: `${window.location.origin}/profile`,
              trial_days: undefined,
            });
            return; // Don't redirect to login, user will be redirected to Stripe
          } else {
            console.error("Sign in failed:", result?.error);
            // Show error and redirect to login
            setErrorMessage(
              "Authentication failed. Please try logging in manually."
            );
            setIsProcessingPayment(false); // Reset payment processing state
          }
        } else {
          console.error(
            "No Stripe price found for plan:",
            formData.subscriptionPlan
          );
          setErrorMessage(
            "Pricing information not available. Please contact support."
          );
          setIsProcessingPayment(false); // Reset payment processing state
        }
      } catch (error) {
        console.error("Error in payment flow:", error);
        setErrorMessage(
          "Payment setup failed. Please try again or contact support."
        );
        setIsProcessingPayment(false); // Reset payment processing state
        // Fall through to regular login redirect
      }
    }

    // Reset payment processing state before redirecting
    setIsProcessingPayment(false);
    // Default behavior: redirect to login
    router.push("/login");
  }, [
    router,
    formData,
    createCheckoutSession,
    getStripePriceForPlan,
    updateSession,
  ]);

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 flex min-h-screen items-center justify-center">
      <div className="fixed top-4 left-4 z-50">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Button
            variant="ghost"
            className="group flex items-center gap-2 px-4 py-2 hover:bg-background/60 backdrop-blur-sm rounded-full border border-border/40"
            onClick={() => router.push("/")}
          >
            <ChevronLeft className="h-4 w-4 transition-transform group-hover:-translate-x-1" />
            <span className="font-medium">Back to Home</span>
            <motion.div
              className="absolute bottom-0 left-0 h-0.5 w-0 bg-primary rounded-full"
              whileHover={{ width: "100%" }}
              transition={{ duration: 0.2 }}
            />
          </Button>
        </motion.div>
      </div>
      <Card className="w-full max-w-2xl gap-6 p-0 overflow-hidden shadow-none">
        <div className="relative  text-primary">
          <div className="absolute inset-0 " />
          <div className="relative h-full p-6 flex flex-col justify-center items-center text-center space-y-6">
            <Image
              src={logoSrc}
              alt="SatoLOC Insight Logo"
              width={100}
              height={100}
            />

            <p className="text-md text-muted-foreground">
              AI-driven localization insights, AutoLQA technology, data-backed
              SEO strategies, and intelligent content creation for
              content-driven industries.
            </p>
            <Button
              variant="secondary"
              size="lg"
              className="mt-6 bg-gradient-to-r from-primary via-[#1279b4] to-primary animate-gradient-x text-white border-0"
              onClick={() => router.push("/")}
            >
              Learn More
            </Button>
          </div>
        </div>
        <div className="p-6">
          <AuthTabs activeTab={mode} onTabChange={handleTabChange} />
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold">
              {mode === "login" ? "Welcome back" : "Create an account"}
            </CardTitle>
            <CardDescription>
              {mode === "login"
                ? "Enter your credentials to sign in"
                : "Fill in your details to create your account"}
            </CardDescription>
          </CardHeader>

          <CardContent>
            {errorMessage && (
              <Alert variant="destructive" className="mb-6">
                <AlertDescription>{errorMessage}</AlertDescription>
              </Alert>
            )}

            {mode === "registration" && subscriptionPlan && (
              <div className="mb-6 p-4 bg-primary/5 border border-primary/20 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold text-primary">
                      Selected Plan
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      You're registering for the{" "}
                      <span className="font-medium capitalize">
                        {subscriptionPlan}
                      </span>{" "}
                      plan
                      {subscriptionPrice && subscriptionPrice !== "Free" && (
                        <span> at {subscriptionPrice}/month</span>
                      )}
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => router.push("/#pricing")}
                    className="text-primary hover:text-primary/80"
                  >
                    Change Plan
                  </Button>
                </div>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <div className="relative">
                  <Input
                    id="username"
                    name="username"
                    value={formData.username}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>

              {mode === "registration" && (
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-2 top-1/2 -translate-y-1/2"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              {mode === "registration" && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirm Password</Label>
                    <div className="relative">
                      <Input
                        id="confirmPassword"
                        name="confirmPassword"
                        type={showConfirmPassword ? "text" : "password"}
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute right-2 top-1/2 -translate-y-1/2"
                        onClick={() =>
                          setShowConfirmPassword(!showConfirmPassword)
                        }
                      >
                        {showConfirmPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="terms"
                      checked={agreeToTerms}
                      onCheckedChange={(checked) => {
                        setAgreeToTerms(checked as boolean);
                        setErrorMessage(null); // Clear error when checkbox changes
                        setIsLoading(false); // Reset loading state when checkbox changes
                      }}
                    />
                    <label
                      htmlFor="terms"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      I agree to the terms and conditions
                    </label>
                  </div>
                </>
              )}

              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-primary via-[#1279b4] to-primary animate-gradient-x text-white border-0"
                disabled={
                  isLoading ||
                  (mode === "registration" &&
                    (!formData.username ||
                      !formData.email ||
                      !formData.password ||
                      !formData.confirmPassword ||
                      !agreeToTerms))
                }
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    {mode === "login" ? "Signing In..." : "Creating Account..."}
                  </div>
                ) : mode === "login" ? (
                  "Sign In"
                ) : (
                  "Create Account"
                )}
              </Button>
            </form>

            <div className="relative my-6">
              <Separator />
              <span className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-background px-2 text-muted-foreground">
                Or continue with
              </span>
            </div>

            <div className="grid grid-cols-1 gap-4">
              <Button
                variant="outline"
                onClick={handleGoogleSignIn}
                disabled={googleLoading}
                className="relative"
              >
                {googleLoading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2" />
                ) : (
                  <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                    <path
                      fill="currentColor"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="currentColor"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                )}
                Google
              </Button>
            </div>

            {googleError && (
              <Alert variant="destructive" className="mt-4">
                <AlertDescription>{googleError}</AlertDescription>
              </Alert>
            )}
          </CardContent>
        </div>
      </Card>
      <ConfirmationModal
        isOpen={openModal}
        onClose={handleCloseModal}
        onConfirm={handleCloseModal}
        title="Registration Successful"
        description={
          formData.subscriptionPlan && formData.subscriptionPlan !== "freemium"
            ? `Your account has been created successfully! You will now be redirected to complete your ${formData.subscriptionPlan} subscription payment.`
            : "Your account has been created successfully. Please check your email for verification."
        }
        confirmText={
          formData.subscriptionPlan && formData.subscriptionPlan !== "freemium"
            ? "Continue to Payment"
            : "Close"
        }
      />
    </div>
  );
}
