// src/components/AuthForm/SocialLogins.tsx
import { But<PERSON> } from "@/components/ui/button";
import { Chrome } from "lucide-react";

interface SocialLoginsProps {
  onGoogleSignIn: () => void;
}

export function SocialLogins({ onGoogleSignIn }: SocialLoginsProps) {
  return (
    <div className="grid grid-cols-1 gap-4">
      <Button variant="outline" onClick={onGoogleSignIn}>
        <Chrome className="mr-2 h-4 w-4" />
        Google
      </Button>
    </div>
  );
}
