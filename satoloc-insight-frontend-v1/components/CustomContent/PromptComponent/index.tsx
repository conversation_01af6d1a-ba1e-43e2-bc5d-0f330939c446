"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Loader2, ChevronDown, History, Sparkles, Trash2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import HistoryComponent from "@/components/CustomContent/HistoryComponent";
import { CustomContent, useCustomContent } from "@/internal-api/custom-content";
import { useToast } from "@/hooks/use-toast";

interface PromptComponentProps {
  title: string;
  setTitle: (title: string) => void;
  prompt: string;
  setPrompt: (prompt: string) => void;
  keywords: string;
  setKeywords: (keywords: string) => void;
  tone: string;
  setTone: (tone: string) => void;
  language: string;
  setLanguage: (language: string) => void;
  isLoading: boolean;
  onGenerate: () => void;
  onClearForm: () => void;
  // History modal props
  savedContents: CustomContent[];
  onLoadContent: (content: CustomContent) => void;
  onDeleteContent: (id: number, e: React.MouseEvent) => void;
  getLanguageName: (code: string) => string;
}

const templateOptions = [
  {
    id: "blog-post",
    name: "Blog Post",
    prompt:
      "Create a well-structured blog post about [TOPIC]. Include an engaging introduction, 3-4 main sections with subheadings, and a conclusion.",
  },
  {
    id: "product-description",
    name: "Product Description",
    prompt:
      "Write a compelling product description for [PRODUCT]. Highlight its key features, benefits, and why customers should purchase it.",
  },
  {
    id: "email-copy",
    name: "Email Newsletter",
    prompt:
      "Craft an engaging email newsletter about [TOPIC]. Include a catchy subject line, introduction, main content sections, and a clear call to action.",
  },
  {
    id: "social-media",
    name: "Social Media Post",
    prompt:
      "Create a set of social media posts about [TOPIC]. Include attention-grabbing headlines and appropriate hashtags.",
  },
  {
    id: "seo-article",
    name: "SEO Article",
    prompt:
      "Write an SEO-optimized article about [TOPIC]. Use the provided keywords naturally throughout the content, include subheadings, and ensure it's informative and engaging.",
  },
];

const toneOptions = [
  { value: "formal", label: "Formal" },
  { value: "casual", label: "Casual" },
  { value: "professional", label: "Professional" },
  { value: "playful", label: "Playful" },
  { value: "persuasive", label: "Persuasive" },
];

const languageOptions = [
  { value: "en", label: "English (en)" },
  { value: "es", label: "Spanish (es)" },
  { value: "fr", label: "French (fr)" },
  { value: "de", label: "German (de)" },
  { value: "it", label: "Italian (it)" },
  { value: "pt", label: "Portuguese (pt)" },
  { value: "ru", label: "Russian (ru)" },
  { value: "zh", label: "Chinese (zh)" },
  { value: "ja", label: "Japanese (ja)" },
  { value: "ar", label: "Arabic (ar)" },
  { value: "tr", label: "Turkish (tr)" },
  { value: "vi", label: "Vietnamese (vi)" },
  { value: "ko", label: "Korean (ko)" },
  { value: "nl", label: "Dutch (nl)" },
  { value: "sv", label: "Swedish (sv)" },
  { value: "pl", label: "Polish (pl)" },
  { value: "da", label: "Danish (da)" },
  { value: "fi", label: "Finnish (fi)" },
  { value: "no", label: "Norwegian (no)" },
  { value: "cs", label: "Czech (cs)" },
  { value: "hu", label: "Hungarian (hu)" },
  { value: "el", label: "Greek (el)" },
  { value: "he", label: "Hebrew (he)" },
  { value: "hi", label: "Hindi (hi)" },
  { value: "id", label: "Indonesian (id)" },
  { value: "ms", label: "Malay (ms)" },
  { value: "th", label: "Thai (th)" },
  { value: "uk", label: "Ukrainian (uk)" },
  { value: "ro", label: "Romanian (ro)" },
  { value: "bg", label: "Bulgarian (bg)" },
];

export default function PromptComponent({
  title,
  setTitle,
  prompt,
  setPrompt,
  keywords,
  setKeywords,
  tone,
  setTone,
  language,
  setLanguage,
  isLoading,
  onGenerate,
  onClearForm,
  savedContents,
  onLoadContent,
  onDeleteContent,
  getLanguageName,
}: PromptComponentProps) {
  const [languageSearch, setLanguageSearch] = useState<string>("");
  const [showHistoryModal, setShowHistoryModal] = useState<boolean>(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [isImprovingPrompt, setIsImprovingPrompt] = useState<boolean>(false);

  const { improvePrompt } = useCustomContent();
  const { toast } = useToast();

  // Reset selected template when prompt is cleared
  useEffect(() => {
    if (prompt === "") {
      setSelectedTemplate(null);
    }
  }, [prompt]);

  // Update prompt placeholders when title changes and a template is selected
  useEffect(() => {
    if (selectedTemplate && title) {
      const template = templateOptions.find((t) => t.id === selectedTemplate);
      if (template) {
        // Replace placeholders with current title
        let populatedPrompt = template.prompt;

        if (title.trim()) {
          populatedPrompt = populatedPrompt.replace(/\[TOPIC\]/g, title.trim());
          populatedPrompt = populatedPrompt.replace(
            /\[PRODUCT\]/g,
            title.trim()
          );
        }

        setPrompt(populatedPrompt);
      }
    }
  }, [title, selectedTemplate]);

  // Handle template selection
  const handleTemplateSelect = (templateId: string) => {
    const template = templateOptions.find((t) => t.id === templateId);
    if (template) {
      // Replace placeholders with current form values
      let populatedPrompt = template.prompt;

      // Replace [TOPIC] with current title if title exists
      if (title && title.trim()) {
        populatedPrompt = populatedPrompt.replace(/\[TOPIC\]/g, title.trim());
      }

      // Replace [PRODUCT] with current title if title exists (for product description template)
      if (title && title.trim()) {
        populatedPrompt = populatedPrompt.replace(/\[PRODUCT\]/g, title.trim());
      }

      setPrompt(populatedPrompt);
      setSelectedTemplate(templateId);
    }
  };

  // Handle improve prompt
  const handleImprovePrompt = async () => {
    if (!prompt.trim()) {
      toast({
        title: "No Prompt",
        description: "Please enter a prompt to improve.",
        variant: "destructive",
      });
      return;
    }

    setIsImprovingPrompt(true);

    try {
      const response = await improvePrompt({
        prompt: prompt.trim(),
        title: title.trim() || undefined,
        keywords: keywords.trim() || undefined,
        tone: tone || undefined,
      });

      if (response.improved_prompt) {
        setPrompt(response.improved_prompt);
        setSelectedTemplate(null);
        toast({
          title: "Prompt Improved!",
          description: "Your prompt has been enhanced for better results.",
        });
      }
    } catch (error) {
      console.error("Error improving prompt:", error);
      toast({
        title: "Error",
        description: "Failed to improve prompt. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsImprovingPrompt(false);
    }
  };

  return (
    <div className="flex-1 flex flex-col h-full">
      <div className="flex-1 overflow-y-auto">
        <div className="space-y-4 p-4">
          <div>
            <label htmlFor="title" className="block text-sm font-medium mb-1">
              Title
            </label>
            <Textarea
              id="title"
              placeholder="Enter a title for your content"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="h-auto"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Template</label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between">
                  {selectedTemplate
                    ? templateOptions.find((t) => t.id === selectedTemplate)
                        ?.name || "Choose Template"
                    : "Choose Template"}
                  <ChevronDown className="h-4 w-4 opacity-50" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56">
                {templateOptions.map((template) => (
                  <DropdownMenuItem
                    key={template.id}
                    onClick={() => handleTemplateSelect(template.id)}
                  >
                    {template.name}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
              <div className="p-2">
                <p className="text-xs text-muted-foreground">
                  {templateOptions.find((t) => t.id === "blog-post")?.prompt}
                </p>
              </div>
            </DropdownMenu>
          </div>

          <div className="flex gap-4">
            <div className="flex-1">
              <label htmlFor="tone" className="block text-sm font-medium mb-1">
                Tone
              </label>
              <Select value={tone} onValueChange={setTone}>
                <SelectTrigger id="tone">
                  <SelectValue placeholder="Select tone" />
                </SelectTrigger>
                <SelectContent>
                  {toneOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex-1">
              <label
                htmlFor="language"
                className="block text-sm font-medium mb-1"
              >
                Language
              </label>
              <Select value={language} onValueChange={setLanguage}>
                <SelectTrigger>
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent className="max-h-[300px] overflow-y-auto">
                  <div className="p-2">
                    <div className="relative">
                      <Input
                        placeholder="Search languages..."
                        className="mb-2 pr-8"
                        value={languageSearch}
                        onChange={(e) => setLanguageSearch(e.target.value)}
                      />
                      {languageSearch && (
                        <button
                          onClick={() => setLanguageSearch("")}
                          className="absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                          aria-label="Clear language search"
                        >
                          ✕
                        </button>
                      )}
                    </div>
                  </div>
                  {languageOptions
                    .filter(
                      (option) =>
                        option.label
                          .toLowerCase()
                          .includes(languageSearch.toLowerCase()) ||
                        option.value
                          .toLowerCase()
                          .includes(languageSearch.toLowerCase())
                    )
                    .map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex w-full justify-between items-center">
                          <span className="min-w-[100px]">
                            {option.label.split(" ")[0]}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {option.value}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <label
              htmlFor="keywords"
              className="block text-sm font-medium mb-1"
            >
              Keywords (optional)
            </label>
            <Input
              id="keywords"
              placeholder="Add keyword..."
              value={keywords}
              onChange={(e) => setKeywords(e.target.value)}
            />
          </div>

          <div>
            <div className="flex items-center justify-between mb-1">
              <label htmlFor="prompt" className="block text-sm font-medium">
                Prompt
              </label>
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-[#1279b4] hover:text-[#1279b4]/80 p-1 h-auto"
                  onClick={() => setShowHistoryModal(true)}
                >
                  <History className="h-4 w-4 mr-1" />
                  History
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-[#1279b4] hover:text-[#1279b4]/80 p-1 h-auto"
                  onClick={handleImprovePrompt}
                  disabled={!prompt.trim() || isImprovingPrompt}
                >
                  {isImprovingPrompt ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                      Improving...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-1" />
                      Improve Prompt
                    </>
                  )}
                </Button>
              </div>
            </div>
            <Textarea
              id="prompt"
              placeholder="Enter your prompt here..."
              className="min-h-[200px]"
              value={prompt}
              onChange={(e) => {
                setPrompt(e.target.value);
                setSelectedTemplate(null);
              }}
            />
          </div>
        </div>
      </div>
      <div className="flex justify-between gap-2 p-4 border-t border-gray-200 dark:border-gray-800">
        <Button
          onClick={onGenerate}
          disabled={isLoading}
          className="bg-[#1279b4] text-white hover:bg-[#1279b4]/90 w-full"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <Sparkles className="mr-2 h-4 w-4" />
              Generate Content
            </>
          )}
        </Button>
        <Button variant="outline" onClick={onClearForm}>
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>

      {/* History Modal */}
      <Dialog open={showHistoryModal} onOpenChange={setShowHistoryModal}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="text-[#1279b4]">
              Content History
            </DialogTitle>
            <DialogDescription>
              View and manage your previously generated content. Click on any
              item to load it, or use the delete button to remove it
              permanently.
            </DialogDescription>
          </DialogHeader>
          <div className="max-h-[60vh] overflow-y-auto">
            <HistoryComponent
              isLoading={isLoading}
              savedContents={savedContents}
              onLoadContent={(content) => {
                onLoadContent(content);
                setShowHistoryModal(false);
              }}
              onDeleteContent={onDeleteContent}
              getLanguageName={getLanguageName}
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
