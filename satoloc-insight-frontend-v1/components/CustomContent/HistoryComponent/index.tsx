"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Loader2, Trash2 } from "lucide-react";
import { CustomContent } from "@/internal-api/custom-content";

interface HistoryComponentProps {
  isLoading: boolean;
  savedContents: CustomContent[];
  onLoadContent: (content: CustomContent) => void;
  onDeleteContent: (id: number, e: React.MouseEvent) => void;
  getLanguageName: (code: string) => string;
}

export default function HistoryComponent({
  isLoading,
  savedContents,
  onLoadContent,
  onDeleteContent,
  getLanguageName,
}: HistoryComponentProps) {
  const [historyFilter, setHistoryFilter] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState<string>("");

  // Simplified delete handler to prevent focus management conflicts
  const handleDeleteClick = useCallback(
    (contentId: number, e: React.MouseEvent) => {
      e.stopPropagation(); // Prevent card click

      // Call the parent delete handler immediately
      onDeleteContent(contentId, e);
    },
    [onDeleteContent]
  );

  // Clear search when savedContents changes (after delete)
  useEffect(() => {
    if (searchQuery && savedContents.length === 0) {
      setSearchQuery("");
    }
  }, [savedContents.length, searchQuery]);

  return (
    <Card className="flex-1 flex flex-col h-auto shadow-none rounded-md">
      <CardHeader>
        <CardTitle className="text-[#1279b4]">Saved Content</CardTitle>
        <CardDescription>Your previously generated content</CardDescription>
        <div className="mt-2 flex flex-col gap-2">
          <div className="relative w-full">
            <Input
              placeholder="Search content..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pr-8"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery("")}
                className="absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                aria-label="Clear search"
              >
                ✕
              </button>
            )}
          </div>
          <Select value={historyFilter} onValueChange={setHistoryFilter}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All content</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent className="flex-1 overflow-y-auto max-h-[420px]">
        {isLoading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : savedContents.length === 0 ? (
          <p className="text-center py-8 text-muted-foreground">
            No saved content yet
          </p>
        ) : (
          <div className="space-y-4">
            {(() => {
              const filteredContents = savedContents.filter(
                (content) =>
                  searchQuery === "" ||
                  content.title
                    .toLowerCase()
                    .includes(searchQuery.toLowerCase()) ||
                  content.prompt
                    .toLowerCase()
                    .includes(searchQuery.toLowerCase())
              );

              if (filteredContents.length === 0) {
                return (
                  <p className="text-center py-8 text-muted-foreground">
                    No matching content found
                  </p>
                );
              }

              return filteredContents.map((content) => (
                <Card
                  key={content.id}
                  className="cursor-pointer hover:bg-accent/50 shadow-none rounded-md"
                  onClick={() => onLoadContent(content)}
                >
                  <div className="p-3 pr-12 relative">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => handleDeleteClick(content.id, e)}
                      title="Delete content"
                      className="absolute right-1 top-1 h-8 w-8"
                    >
                      <Trash2 className="h-3.5 w-3.5 text-red-500" />
                    </Button>
                    <div>
                      <h4 className="font-semibold text-sm truncate max-w-full text-[#1279b4]">
                        {content.title}
                      </h4>
                      <div className="flex flex-wrap items-center text-xs text-muted-foreground mt-0.5 gap-1">
                        <span className="whitespace-nowrap">
                          {new Date(content.created_at).toLocaleDateString()}
                        </span>
                        <span className="hidden sm:inline mx-1">•</span>
                        <span className="whitespace-nowrap">
                          {getLanguageName(content.language)}
                        </span>
                      </div>
                      {content.keywords && (
                        <p className="text-xs text-primary-foreground/70 mt-1 bg-primary/10 rounded px-1 py-0.5 truncate max-w-full">
                          {content.keywords}
                        </p>
                      )}
                      <p className="truncate text-xs text-muted-foreground mt-1 max-w-full">
                        {content.prompt.substring(0, 50)}
                        {content.prompt.length > 50 ? "..." : ""}
                      </p>
                    </div>
                  </div>
                </Card>
              ));
            })()}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
