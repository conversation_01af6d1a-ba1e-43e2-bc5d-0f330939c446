"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { CardFooter } from "@/components/ui/card";
import {
  Copy,
  Check,
  RefreshCw,
  Download,
  Save,
  Edit,
  Wifi,
  WifiOff,
  Loader2,
  Globe,
  Eye,
  EyeOff,
  Upload,
} from "lucide-react";
import { useWpConnection } from "@/hooks/use-wp-connection";
import { toast } from "sonner";
import ExportButton from "@/components/ExportButton";
import { ExportContent } from "@/lib/exportUtils";

interface FooterToolbarProps {
  generatedContent: string;
  isLoading: boolean;
  isCopied: boolean;
  prompt: string;
  title?: string;
  keywords?: string;
  language?: string;
  currentContentId?: number;
  onCopyContent: () => void;
  onDownloadContent: () => void;
  onSaveContent: () => void;
  onRegenerate: () => void;
  onSaveAndPushToWordPress?: () => Promise<void>;
}

export default function FooterToolbar({
  generatedContent,
  isLoading,
  isCopied,
  prompt,
  title,
  keywords,
  language,
  currentContentId,
  onCopyContent,
  onDownloadContent,
  onSaveContent,
  onRegenerate,
  onSaveAndPushToWordPress,
}: FooterToolbarProps) {
  const {
    connection,
    isConnected,
    isLoading: wpLoading,
    error,
    connect,
    disconnect,
  } = useWpConnection();
  const [wpUrl, setWpUrl] = useState<string>("");
  const [wpApiKey, setWpApiKey] = useState<string>("");
  const [isConnecting, setIsConnecting] = useState<boolean>(false);
  const [showApiKey, setShowApiKey] = useState<boolean>(false);
  const [isPushingToWp, setIsPushingToWp] = useState<boolean>(false);

  // Prepare content for export
  const getExportContent = (): ExportContent => {
    return {
      title: title || 'Untitled Content',
      content: generatedContent || '',
      metadata: {
        author: 'Content Creator',
        createdAt: new Date(),
        keywords: keywords || '',
        language: language || 'en',
        prompt: prompt || '',
      },
    };
  };

  const handleConnect = async () => {
    if (!wpUrl.trim() || !wpApiKey.trim()) {
      toast.error("Please enter both WordPress URL and API Key");
      return;
    }

    setIsConnecting(true);
    try {
      const success = await connect({
        baseUrl: wpUrl.trim(),
        apiKey: wpApiKey.trim(),
      });

      if (success) {
        toast.success("Successfully connected to WordPress!");
        setWpUrl("");
        setWpApiKey("");
      } else {
        toast.error(error || "Failed to connect to WordPress");
      }
    } catch (err) {
      toast.error("Connection failed. Please check your credentials.");
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisconnect = () => {
    disconnect();
    toast.success("Disconnected from WordPress");
  };

  const handlePushToWordPress = async () => {
    if (!isConnected || !connection) {
      toast.error("Please connect to WordPress first");
      return;
    }

    if (!generatedContent || !prompt) {
      toast.error("No content to push. Please generate content first.");
      return;
    }

    if (!onSaveAndPushToWordPress) {
      toast.error("Push to WordPress functionality not available");
      return;
    }

    setIsPushingToWp(true);
    try {
      await onSaveAndPushToWordPress();
      toast.success("Content successfully pushed to WordPress!");
    } catch (err: any) {
      toast.error(err.message || "Failed to push content to WordPress");
    } finally {
      setIsPushingToWp(false);
    }
  };

  return (
    <div className="w-full p-2 sm:p-4 bg-white dark:bg-background border-t border-gray-200 dark:border-gray-800">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between w-full text-sm text-foreground gap-3 sm:gap-4">
        <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
          {/* WordPress Connection Section */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 sm:border-r border-gray-200 dark:border-gray-700 sm:pr-4">
            {/* Connection Status Indicator */}
            <div className="flex items-center gap-2">
              {wpLoading ? (
                <Loader2 className="h-4 w-4 animate-spin text-gray-500" />
              ) : isConnected ? (
                <Wifi className="h-4 w-4 text-green-500" />
              ) : (
                <WifiOff className="h-4 w-4 text-gray-400" />
              )}
              <span
                className={`text-xs font-medium ${
                  isConnected
                    ? "text-green-600 dark:text-green-400"
                    : "text-gray-500"
                }`}
              >
                {wpLoading
                  ? "Checking..."
                  : isConnected
                    ? "Connected"
                    : "Not Connected"}
              </span>
            </div>

            {/* Connection Details or Input Fields */}
            {isConnected && connection ? (
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400">
                  <Globe className="h-3 w-3" />
                  <span
                    className="max-w-32 truncate"
                    title={connection.baseUrl}
                  >
                    {new URL(connection.baseUrl).hostname}
                  </span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDisconnect}
                  className="h-7 px-2 text-xs text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950"
                >
                  Disconnect
                </Button>
              </div>
            ) : (
              <div className="flex flex-col sm:flex-row gap-2">
                <Input
                  type="url"
                  placeholder="WordPress URL (e.g., https://yoursite.com)"
                  value={wpUrl}
                  onChange={(e) => setWpUrl(e.target.value)}
                  className="h-7 w-full sm:w-56 text-xs"
                  disabled={isConnecting}
                />
                <div className="flex gap-2">
                  <div className="relative flex-1 sm:flex-none">
                    <Input
                      type={showApiKey ? "text" : "password"}
                      placeholder="API Key"
                      value={wpApiKey}
                      onChange={(e) => setWpApiKey(e.target.value)}
                      className="h-7 w-full sm:w-44 text-xs pr-8"
                      disabled={isConnecting}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowApiKey(!showApiKey)}
                      className="absolute right-0 top-0 h-7 w-7 p-0 hover:bg-transparent"
                      disabled={isConnecting}
                      tabIndex={-1}
                    >
                      {showApiKey ? (
                        <EyeOff className="h-3 w-3 text-gray-400 hover:text-gray-600" />
                      ) : (
                        <Eye className="h-3 w-3 text-gray-400 hover:text-gray-600" />
                      )}
                    </Button>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleConnect}
                    disabled={isConnecting || !wpUrl.trim() || !wpApiKey.trim()}
                    className="h-7 px-3 text-xs text-[#1279b4] hover:bg-blue-50 dark:hover:bg-blue-950 disabled:opacity-50 flex-shrink-0"
                  >
                    {isConnecting ? (
                      <>
                        <Loader2 className="h-3 w-3 animate-spin mr-1" />
                        <span className="hidden sm:inline">Connecting...</span>
                      </>
                    ) : (
                      "Connect"
                    )}
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
        <div className="flex flex-wrap items-center gap-1 sm:gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onCopyContent}
            disabled={!generatedContent || isLoading}
            className="text-[#1279b4] px-2 sm:px-3"
            title="Copy to clipboard"
          >
            {isCopied ? (
              <>
                <Check className="h-4 w-4 sm:mr-2" />
                <span className="hidden sm:inline">Copied</span>
              </>
            ) : (
              <>
                <Copy className="h-4 w-4 sm:mr-2 text-[#1279b4]" />
                <span className="hidden sm:inline">Copy</span>
              </>
            )}
          </Button>
          <ExportButton
            content={getExportContent()}
            variant="ghost"
            size="sm"
            disabled={!generatedContent || isLoading}
            className="text-[#1279b4] px-2 sm:px-3"
          />
          <Button
            variant="ghost"
            size="sm"
            onClick={onSaveContent}
            disabled={!generatedContent || isLoading}
            className="text-[#1279b4] px-2 sm:px-3"
            title="Save content"
          >
            <Save className="h-4 w-4 sm:mr-2 text-[#1279b4]" />
            <span className="hidden sm:inline">Save</span>
          </Button>
          {/* Push to WordPress Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handlePushToWordPress}
            disabled={
              !generatedContent || isLoading || isPushingToWp || !isConnected
            }
            className="text-[#1279b4] disabled:opacity-50 px-2 sm:px-3"
            title={
              isConnected
                ? "Push content to WordPress"
                : "Connect to WordPress first"
            }
          >
            {isPushingToWp ? (
              <>
                <Loader2 className="h-4 w-4 sm:mr-2 animate-spin" />
                <span className="hidden sm:inline">Pushing...</span>
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 sm:mr-2 text-[#1279b4]" />
                <span className="hidden sm:inline">Push to WP</span>
              </>
            )}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onRegenerate}
            disabled={!prompt || isLoading}
            title="Regenerate content"
            className="text-[#1279b4] px-2 sm:px-3"
          >
            <RefreshCw className="h-4 w-4 sm:mr-2 text-[#1279b4]" />
            <span className="hidden sm:inline">Regenerate</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
