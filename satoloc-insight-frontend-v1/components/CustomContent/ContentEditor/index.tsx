"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Loader2, Code, Edit3 } from "lucide-react";
import AceHtmlEditor from "./AceHtmlEditor";
import VisualEditor from "./VisualEditor";
import ExportButton from "@/components/ExportButton";
import { convertMarkdownToHtml } from "@/lib/markdownUtils";
import { ExportContent } from "@/lib/exportUtils";

interface ContentEditorProps {
  generatedContent: string;
  setGeneratedContent: (content: string) => void;
  contentFormat: string;
  setContentFormat: (format: string) => void;
  isLoading: boolean;
  isCopied: boolean;
  prompt: string;
  onCopyContent: () => void;
  onDownloadContent: () => void;
  onSaveContent: () => void;
  onRegenerate: () => void;
  // Context props for AI image generation
  title?: string;
  keywords?: string;
  tone?: string;
  // Callback to sync title when headline changes
  onTitleChange?: (newTitle: string) => void;
}

export default function ContentEditor({
  generatedContent,
  setGeneratedContent,
  contentFormat,
  setContentFormat,
  isLoading,
  prompt,
  title,
  keywords,
  tone,
  onTitleChange,
}: ContentEditorProps) {
  const [lastKnownTitle, setLastKnownTitle] = React.useState<
    string | undefined
  >(title);

  // Prepare content for export
  const getExportContent = (): ExportContent => {
    return {
      title: title || 'Untitled Content',
      content: generatedContent || '',
      metadata: {
        author: 'Content Creator',
        createdAt: new Date(),
        keywords: keywords || '',
        tone: tone || 'professional',
        prompt: prompt || '',
        language: 'en',
      },
    };
  };

  // Effect to sync title changes to content headline
  React.useEffect(() => {
    if (title && title !== lastKnownTitle && generatedContent) {
      const currentHeadline = extractHeadlineFromContent(generatedContent);
      if (currentHeadline && currentHeadline !== title) {
        // Update the headline in the content
        const updatedContent = updateHeadlineInContent(generatedContent, title);
        if (updatedContent !== generatedContent) {
          setGeneratedContent(updatedContent);
        }
      }
      setLastKnownTitle(title);
    }
  }, [title, lastKnownTitle, generatedContent]);
  const handleContentChange = (newContent: string) => {
    setGeneratedContent(newContent);

    // Extract headline from content and sync with title field
    if (onTitleChange) {
      const extractedHeadline = extractHeadlineFromContent(newContent);
      if (extractedHeadline && extractedHeadline !== title) {
        onTitleChange(extractedHeadline);
      }
    }
  };

  // Function to extract headline from HTML/Markdown content
  const extractHeadlineFromContent = (content: string): string | null => {
    if (!content) return null;

    // Try to extract from HTML first (for visual editor)
    const htmlH1Match = content.match(/<h1[^>]*>([^<]+)<\/h1>/i);
    if (htmlH1Match) {
      return htmlH1Match[1].trim();
    }

    const htmlH2Match = content.match(/<h2[^>]*>([^<]+)<\/h2>/i);
    if (htmlH2Match) {
      return htmlH2Match[1].trim();
    }

    // Try to extract from Markdown (for code editor)
    const markdownH1Match = content.match(/^#\s+(.+)$/m);
    if (markdownH1Match) {
      return markdownH1Match[1].trim();
    }

    const markdownH2Match = content.match(/^##\s+(.+)$/m);
    if (markdownH2Match) {
      return markdownH2Match[1].trim();
    }

    // Try to extract bold text at the beginning
    const boldMatch = content.match(/^\*\*(.+?)\*\*/m);
    if (boldMatch) {
      return boldMatch[1].trim();
    }

    return null;
  };

  // Function to update headline in HTML/Markdown content
  const updateHeadlineInContent = (
    content: string,
    newTitle: string
  ): string => {
    if (!content || !newTitle) return content;

    // Try to update HTML headlines first (for visual editor)
    let updatedContent = content.replace(
      /<h1[^>]*>([^<]+)<\/h1>/i,
      `<h1>${newTitle}</h1>`
    );
    if (updatedContent !== content) {
      return updatedContent;
    }

    updatedContent = content.replace(
      /<h2[^>]*>([^<]+)<\/h2>/i,
      `<h2>${newTitle}</h2>`
    );
    if (updatedContent !== content) {
      return updatedContent;
    }

    // Try to update Markdown headlines (for code editor)
    updatedContent = content.replace(/^#\s+(.+)$/m, `# ${newTitle}`);
    if (updatedContent !== content) {
      return updatedContent;
    }

    updatedContent = content.replace(/^##\s+(.+)$/m, `## ${newTitle}`);
    if (updatedContent !== content) {
      return updatedContent;
    }

    // Try to update bold text at the beginning
    updatedContent = content.replace(/^\*\*(.+?)\*\*/m, `**${newTitle}**`);
    if (updatedContent !== content) {
      return updatedContent;
    }

    return content; // Return original if no headline found
  };

  // Helper function to detect if content is markdown
  const isMarkdownContent = (content: string): boolean => {
    if (!content) return false;
    // Check for common markdown patterns
    const markdownPatterns = [
      /^#{1,6}\s+/m, // Headers
      /\*\*.*?\*\*/, // Bold
      /\*.*?\*/, // Italic
      /^[-*+]\s+/m, // Unordered lists
      /^\d+\.\s+/m, // Ordered lists
      /\[.*?\]\(.*?\)/, // Links
      /!\[.*?\]\(.*?\)/, // Images
    ];
    return markdownPatterns.some((pattern) => pattern.test(content));
  };

  const getEditorContent = (): string => {
    if (!generatedContent) return "";

    // If content looks like markdown, convert it to HTML
    if (isMarkdownContent(generatedContent)) {
      return convertMarkdownToHtml(generatedContent);
    }

    // Otherwise return as-is (assuming it's already HTML)
    return generatedContent;
  };

  const handleFormatChange = (newFormat: string) => {
    if (newFormat === contentFormat) return;

    // Only handle conversion between rich text editor and other formats
    // AceHtmlEditor handles internal format switching between markdown/html/json
    let convertedContent = generatedContent;

    if (contentFormat === "editor" && newFormat !== "editor") {
      // Converting from rich text editor to AceHtmlEditor - content is already HTML
      convertedContent = generatedContent;
    } else if (contentFormat !== "editor" && newFormat === "editor") {
      // Converting from AceHtmlEditor to rich text editor - ensure content is HTML
      convertedContent = generatedContent; // AceHtmlEditor always outputs HTML
    }

    setGeneratedContent(convertedContent);
    setContentFormat(newFormat);
  };

  const renderEditor = () => {
    if (isLoading) {
      return (
        <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
          <Loader2 className="h-8 w-8 animate-spin mb-2" />
          <p>Generating content...</p>
        </div>
      );
    }

    if (contentFormat === "editor") {
      return (
        <VisualEditor
          content={getEditorContent()}
          onChange={handleContentChange}
          placeholder="Generated content will appear here..."
          disabled={isLoading}
          contentTitle={title}
          contentText={generatedContent}
          keywords={keywords}
          tone={tone}
          originalPrompt={prompt}
          onTitleChange={onTitleChange}
        />
      );
    }

    // Use AceHtmlEditor for all other formats (markdown, html, json) since it has built-in format switching
    return (
      <AceHtmlEditor
        content={getEditorContent()}
        onChange={handleContentChange}
        placeholder="Enter content..."
        disabled={isLoading}
      />
    );
  };

  return (
    <div className="flex flex-col h-full gap-2 dark:bg-background min-h-0">
      {/* Editor Mode Toggle and Export */}
      <div className="flex items-center justify-between p-2 border-b border-gray-200 bg-white dark:border-gray-800 dark:bg-background flex-shrink-0">
        <div className="flex justify-start gap-1">
          <Button
            variant={contentFormat === "editor" ? "default" : "ghost"}
            size="sm"
            onClick={() => handleFormatChange("editor")}
            className={`h-8 px-2 sm:px-3 border border-[#1279b4] transition-colors ${
              contentFormat === "editor"
                ? "bg-[#1279b4] text-white hover:bg-[#1279b4]/90"
                : "text-[#1279b4] bg-transparent hover:bg-[#1279b4]/10"
            }`}
          >
            <Edit3 className="h-4 w-4 sm:mr-1" />
            <span className="hidden sm:inline">Visual Editor</span>
            <span className="sm:hidden">Visual</span>
          </Button>
          <Button
            variant={contentFormat !== "editor" ? "default" : "ghost"}
            size="sm"
            onClick={() => handleFormatChange("html")}
            className={`h-8 px-2 sm:px-3 border border-[#1279b4] transition-colors ${
              contentFormat !== "editor"
                ? "bg-[#1279b4] text-white hover:bg-[#1279b4]/90"
                : "text-[#1279b4] bg-transparent hover:bg-[#1279b4]/10"
            }`}
          >
            <Code className="h-4 w-4 sm:mr-1" />
            <span className="hidden sm:inline">Code Editor</span>
            <span className="sm:hidden">Code</span>
          </Button>
        </div>
        
        {/* Export Button */}
        <div className="flex items-center gap-2">
          <ExportButton
            content={getExportContent()}
            variant="outline"
            size="sm"
            className="h-8 border-[#1279b4] text-[#1279b4] hover:bg-[#1279b4]/10"
          />
        </div>
      </div>

      <div className="flex-1 overflow-hidden min-h-0">
        <div className="h-full p-2 sm:p-4">{renderEditor()}</div>
      </div>
    </div>
  );
}
