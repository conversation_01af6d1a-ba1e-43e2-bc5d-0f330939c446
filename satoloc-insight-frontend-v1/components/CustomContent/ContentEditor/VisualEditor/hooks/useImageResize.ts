import { useState, useCallback, useEffect } from 'react';
import { SelectedElementData } from '@/components/CustomContent/ContentEditor/SideToolbar';

interface ResizeStartData {
  startX: number;
  startY: number;
  startWidth: number;
  startHeight: number;
  element: HTMLImageElement;
  aspectRatio: number;
}

interface UseImageResizeProps {
  selectedElement: SelectedElementData | null;
  setSelectedElement: React.Dispatch<React.SetStateAction<SelectedElementData | null>>;
  handleEditorChange: () => void;
  disabled: boolean;
}

interface UseImageResizeReturn {
  isResizing: boolean;
  resizeStartData: ResizeStartData | null;
  handleResizeStart: (e: React.MouseEvent, element: HTMLImageElement, handle: string) => void;
}

/**
 * Custom hook for managing image resizing functionality in the VisualEditor
 */
export const useImageResize = ({
  selectedElement,
  setSelectedElement,
  handleEditorChange,
  disabled,
}: UseImageResizeProps): UseImageResizeReturn => {
  const [isResizing, setIsResizing] = useState(false);
  const [resizeStartData, setResizeStartData] = useState<ResizeStartData | null>(null);

  // Handle resize start
  const handleResizeStart = useCallback(
    (e: React.MouseEvent, element: HTMLImageElement, handle: string) => {
      if (disabled) return;
      
      e.preventDefault();
      e.stopPropagation();

      const rect = element.getBoundingClientRect();
      const aspectRatio = rect.width / rect.height;

      setIsResizing(true);
      setResizeStartData({
        startX: e.clientX,
        startY: e.clientY,
        startWidth: rect.width,
        startHeight: rect.height,
        element,
        aspectRatio,
      });

      // Prevent text selection during resize
      document.body.style.userSelect = "none";
      document.body.style.cursor = "nw-resize";
    },
    [disabled]
  );

  // Handle resize move
  const handleResizeMove = useCallback(
    (e: MouseEvent) => {
      if (!isResizing || !resizeStartData) return;

      const { startX, startY, startWidth, startHeight, element, aspectRatio } =
        resizeStartData;

      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;

      // Calculate new dimensions based on the larger delta to maintain aspect ratio
      const delta = Math.max(deltaX, deltaY);
      let newWidth = Math.max(50, startWidth + delta); // Minimum width of 50px
      let newHeight = newWidth / aspectRatio;

      // Apply the new dimensions
      element.style.width = `${newWidth}px`;
      element.style.height = `${newHeight}px`;
      element.width = Math.round(newWidth);
      element.height = Math.round(newHeight);

      // Update the sidebar if the element is selected
      if (selectedElement?.element === element) {
        setSelectedElement((prev) => {
          if (!prev) return prev;
          return {
            ...prev,
            properties: {
              ...prev.properties,
              width: Math.round(newWidth).toString(),
              height: Math.round(newHeight).toString(),
            },
          };
        });
      }
    },
    [isResizing, resizeStartData, selectedElement, setSelectedElement]
  );

  // Handle resize end
  const handleResizeEnd = useCallback(() => {
    if (!isResizing) return;

    setIsResizing(false);
    setResizeStartData(null);

    // Restore normal cursor and text selection
    document.body.style.userSelect = "";
    document.body.style.cursor = "";

    // Trigger content change to save the resize
    handleEditorChange();
  }, [isResizing, handleEditorChange]);

  // Add global mouse event listeners for resize
  useEffect(() => {
    if (isResizing) {
      document.addEventListener("mousemove", handleResizeMove);
      document.addEventListener("mouseup", handleResizeEnd);

      return () => {
        document.removeEventListener("mousemove", handleResizeMove);
        document.removeEventListener("mouseup", handleResizeEnd);
      };
    }
  }, [isResizing, handleResizeMove, handleResizeEnd]);

  return {
    isResizing,
    resizeStartData,
    handleResizeStart,
  };
};
