import { useState, useCallback, useRef } from 'react';

interface UseUndoRedoProps {
  initialContent: string;
  onChange: (content: string) => void;
  editorRef: React.RefObject<HTMLDivElement>;
  addElementSpacing: (editorRef: React.RefObject<HTMLDivElement>) => void;
}

interface UseUndoRedoReturn {
  history: string[];
  historyIndex: number;
  isUndoRedoAction: boolean;
  setIsUndoRedoAction: (value: boolean) => void;
  addToHistory: (newContent: string) => void;
  handleUndo: () => void;
  handleRedo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  setHistory: React.Dispatch<React.SetStateAction<string[]>>;
  setHistoryIndex: React.Dispatch<React.SetStateAction<number>>;
}

/**
 * Custom hook for managing undo/redo functionality in the VisualEditor
 */
export const useUndoRedo = ({
  initialContent,
  onChange,
  editorRef,
  addElementSpacing,
}: UseUndoRedoProps): UseUndoRedoReturn => {
  // Undo/Redo state management
  const [history, setHistory] = useState<string[]>([initialContent]);
  const [historyIndex, setHistoryIndex] = useState(0);
  const [isUndoRedoAction, setIsUndoRedoAction] = useState(false);

  // Add content to history for undo/redo
  const addToHistory = useCallback((newContent: string) => {
    setHistory(prev => {
      // Don't add if content is the same as current
      if (prev[historyIndex] === newContent) {
        return prev;
      }

      // Remove any history after current index (when user made changes after undo)
      const newHistory = prev.slice(0, historyIndex + 1);
      newHistory.push(newContent);

      // Limit history to 50 items for performance
      if (newHistory.length > 50) {
        newHistory.shift();
        setHistoryIndex(prev => prev - 1);
        return newHistory;
      }

      setHistoryIndex(newHistory.length - 1);
      return newHistory;
    });
  }, [historyIndex]);

  // Undo function
  const handleUndo = useCallback(() => {
    if (historyIndex > 0) {
      const previousIndex = historyIndex - 1;
      const previousContent = history[previousIndex];
      
      setHistoryIndex(previousIndex);
      setIsUndoRedoAction(true);
      
      if (editorRef.current) {
        editorRef.current.innerHTML = previousContent;
        onChange(previousContent);
        setTimeout(() => addElementSpacing(editorRef), 10);
      }
      
      // Reset the flag after a short delay
      setTimeout(() => setIsUndoRedoAction(false), 100);
    }
  }, [historyIndex, history, onChange, editorRef, addElementSpacing]);

  // Redo function
  const handleRedo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const nextIndex = historyIndex + 1;
      const nextContent = history[nextIndex];
      
      setHistoryIndex(nextIndex);
      setIsUndoRedoAction(true);
      
      if (editorRef.current) {
        editorRef.current.innerHTML = nextContent;
        onChange(nextContent);
        setTimeout(() => addElementSpacing(editorRef), 10);
      }
      
      // Reset the flag after a short delay
      setTimeout(() => setIsUndoRedoAction(false), 100);
    }
  }, [historyIndex, history, onChange, editorRef, addElementSpacing]);

  const canUndo = historyIndex > 0;
  const canRedo = historyIndex < history.length - 1;

  return {
    history,
    historyIndex,
    isUndoRedoAction,
    setIsUndoRedoAction,
    addToHistory,
    handleUndo,
    handleRedo,
    canUndo,
    canRedo,
    setHistory,
    setHistoryIndex,
  };
};
