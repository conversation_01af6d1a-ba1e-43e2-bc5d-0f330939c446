import { useState, useCallback, useEffect } from 'react';
import { SelectedElementData } from '@/components/CustomContent/ContentEditor/SideToolbar';

interface UseElementSelectionProps {
  editorRef: React.RefObject<HTMLDivElement>;
  getElementProperties: (element: HTMLElement) => { [key: string]: string };
  content: string;
  disabled: boolean;
}

interface UseElementSelectionReturn {
  selectedElement: SelectedElementData | null;
  setSelectedElement: React.Dispatch<React.SetStateAction<SelectedElementData | null>>;
  sidebarVisible: boolean;
  setSidebarVisible: React.Dispatch<React.SetStateAction<boolean>>;
  hoveredElement: HTMLElement | null;
  setHoveredElement: React.Dispatch<React.SetStateAction<HTMLElement | null>>;
  triggerPosition: { top: number; left: number } | null;
  setTriggerPosition: React.Dispatch<React.SetStateAction<{ top: number; left: number } | null>>;
  handleElementSelection: (element: HTMLElement) => void;
  handleDirectClick: (e: React.MouseEvent) => void;
}

/**
 * Custom hook for managing element selection functionality in the VisualEditor
 */
export const useElementSelection = ({
  editorRef,
  getElementProperties,
  content,
  disabled,
}: UseElementSelectionProps): UseElementSelectionReturn => {
  const [selectedElement, setSelectedElement] = useState<SelectedElementData | null>(null);
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [hoveredElement, setHoveredElement] = useState<HTMLElement | null>(null);
  const [triggerPosition, setTriggerPosition] = useState<{
    top: number;
    left: number;
  } | null>(null);

  // Handle element selection
  const handleElementSelection = useCallback(
    (element: HTMLElement) => {
      if (!element || !editorRef.current?.contains(element)) {
        setSelectedElement(null);
        setSidebarVisible(false);
        return;
      }

      // Don't select the editor itself
      if (element === editorRef.current) {
        setSelectedElement(null);
        setSidebarVisible(false);
        return;
      }

      const properties = getElementProperties(element);

      const elementData = {
        tagName: element.tagName.toLowerCase(),
        element,
        properties,
      };

      setSelectedElement(elementData);
      setSidebarVisible(true);
      setHoveredElement(null);
      setTriggerPosition(null);
    },
    [editorRef, getElementProperties]
  );

  // Handle direct element click for selection
  const handleDirectClick = useCallback(
    (e: React.MouseEvent) => {
      if (disabled) return;

      // Always try to select elements with regular clicks (more intuitive)
      // But prevent default editing behavior when selecting
      const target = e.target as HTMLElement;

      // Get the closest meaningful element
      let element = target;
      while (element && element !== editorRef.current) {
        const tagName = element.tagName.toLowerCase();
        if (
          [
            "h1",
            "h2",
            "h3",
            "h4",
            "h5",
            "h6",
            "p",
            "div",
            "span",
            "a",
            "img",
            "ul",
            "ol",
            "li",
            "blockquote",
            "button",
          ].includes(tagName)
        ) {
          break;
        }
        element = element.parentElement as HTMLElement;
      }

      if (element && element !== editorRef.current) {
        // Always select the element on click to show SideToolbar
        // This provides immediate access to element properties
        handleElementSelection(element);
      }
    },
    [disabled, handleElementSelection, editorRef]
  );

  // Add visual indicators to elements
  const addElementIndicators = useCallback(() => {
    if (!editorRef.current) return;

    const elements = editorRef.current.querySelectorAll(
      "h1, h2, h3, h4, h5, h6, p, div, span, a, img, ul, ol, li, blockquote"
    );

    elements.forEach((element) => {
      const htmlElement = element as HTMLElement;

      // Skip if already has handlers attached
      if (htmlElement.dataset.visualEditorHandlers === "true") return;

      // Mark as having handlers to prevent duplicates
      htmlElement.dataset.visualEditorHandlers = "true";

      // Add hover effect
      htmlElement.style.position = "relative";

      const handleMouseEnter = () => {
        if (selectedElement?.element === htmlElement) return;
        htmlElement.style.outline = "1px dashed #3b82f6";
        htmlElement.style.outlineOffset = "2px";
      };

      const handleMouseLeave = () => {
        if (selectedElement?.element === htmlElement) return;
        htmlElement.style.outline = "none";
      };

      const handleClick = (e: Event) => {
        e.stopPropagation();
        handleElementSelection(htmlElement);
      };

      htmlElement.addEventListener("mouseenter", handleMouseEnter);
      htmlElement.addEventListener("mouseleave", handleMouseLeave);
      htmlElement.addEventListener("click", handleClick);

      // Store cleanup function
      (htmlElement as any)._visualEditorCleanup = () => {
        htmlElement.removeEventListener("mouseenter", handleMouseEnter);
        htmlElement.removeEventListener("mouseleave", handleMouseLeave);
        htmlElement.removeEventListener("click", handleClick);
        delete htmlElement.dataset.visualEditorHandlers;
        delete (htmlElement as any)._visualEditorCleanup;
      };
    });
  }, [selectedElement, handleElementSelection, editorRef]);

  // Cleanup function to remove all event listeners
  const cleanupEventListeners = useCallback(() => {
    if (!editorRef.current) return;

    const elements = editorRef.current.querySelectorAll(
      "h1, h2, h3, h4, h5, h6, p, div, span, a, img, ul, ol, li, blockquote"
    );

    elements.forEach((element) => {
      const htmlElement = element as HTMLElement;
      if ((htmlElement as any)._visualEditorCleanup) {
        (htmlElement as any)._visualEditorCleanup();
      }
    });
  }, [editorRef]);

  // Update indicators when content changes
  useEffect(() => {
    // Clean up existing listeners first
    cleanupEventListeners();

    const timeout = setTimeout(addElementIndicators, 100);
    return () => {
      clearTimeout(timeout);
      cleanupEventListeners();
    };
  }, [content, addElementIndicators, cleanupEventListeners]);

  // Cleanup on unmount
  useEffect(() => {
    return cleanupEventListeners;
  }, [cleanupEventListeners]);

  return {
    selectedElement,
    setSelectedElement,
    sidebarVisible,
    setSidebarVisible,
    hoveredElement,
    setHoveredElement,
    triggerPosition,
    setTriggerPosition,
    handleElementSelection,
    handleDirectClick,
  };
};
