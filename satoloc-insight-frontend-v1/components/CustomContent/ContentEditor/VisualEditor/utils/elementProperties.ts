/**
 * Utility functions for extracting and managing element properties in the VisualEditor
 */

/**
 * Extracts properties from an HTML element based on its tag type
 * @param element - The HTML element to extract properties from
 * @returns Object containing the element's properties
 */
export const getElementProperties = (element: HTMLElement): { [key: string]: string } => {
  const properties: { [key: string]: string } = {};
  const tagName = element.tagName.toLowerCase();

  // Helper function to get style value, prioritizing inline styles over computed styles
  const getStyleValue = (property: string): string => {
    // First check inline styles
    const inlineValue = element.style.getPropertyValue(property);
    if (inlineValue) {
      return inlineValue;
    }
    // Fall back to computed styles
    return getComputedStyle(element).getPropertyValue(property);
  };

  // Common properties for all elements
  if (element.id) properties.id = element.id;
  if (element.className) properties.className = element.className;
  if (element.style.cssText) properties.style = element.style.cssText;

  // Tag-specific properties
  switch (tagName) {
    case "h1":
    case "h2":
    case "h3":
    case "h4":
    case "h5":
    case "h6":
      properties.textContent = element.textContent || "";
      properties.fontSize = getStyleValue("font-size");
      properties.fontWeight = getStyleValue("font-weight");
      properties.color = getStyleValue("color");
      properties.textAlign = getStyleValue("text-align");
      properties.fontStyle = getStyleValue("font-style");
      properties.textDecorationLine = getStyleValue("text-decoration-line");
      break;
    case "p":
      properties.textContent = element.textContent || "";
      properties.fontSize = getStyleValue("font-size");
      properties.color = getStyleValue("color");
      properties.textAlign = getStyleValue("text-align");
      properties.lineHeight = getStyleValue("line-height");
      properties.fontStyle = getStyleValue("font-style");
      properties.textDecorationLine = getStyleValue("text-decoration-line");
      break;
    case "a":
      const linkElement = element as HTMLAnchorElement;
      properties.href = linkElement.href || "";
      properties.target = linkElement.target || "";
      properties.textContent = element.textContent || "";
      properties.color = getStyleValue("color");
      properties.fontStyle = getStyleValue("font-style");
      properties.textDecorationLine = getStyleValue("text-decoration-line");
      break;
    case "img":
      const imgElement = element as HTMLImageElement;
      properties.src = imgElement.src || "";
      properties.alt = imgElement.alt || "";
      properties.width = imgElement.width
        ? imgElement.width.toString()
        : "";
      properties.height = imgElement.height
        ? imgElement.height.toString()
        : "";
      break;
    case "div":
    case "span":
      properties.textContent = element.textContent || "";
      properties.backgroundColor = getStyleValue("background-color");
      properties.padding = getStyleValue("padding");
      properties.margin = getStyleValue("margin");
      properties.fontStyle = getStyleValue("font-style");
      properties.textDecorationLine = getStyleValue("text-decoration-line");
      break;
    case "ul":
    case "ol":
      properties.listStyleType = getComputedStyle(element).listStyleType;
      properties.itemCount = element.children.length.toString();
      break;
    case "li":
      properties.textContent = element.textContent || "";
      properties.listStyleType = getStyleValue("list-style-type");
      properties.fontStyle = getStyleValue("font-style");
      properties.textDecorationLine = getStyleValue("text-decoration-line");
      break;
    case "blockquote":
      properties.textContent = element.textContent || "";
      properties.borderLeft = getStyleValue("border-left");
      properties.paddingLeft = getStyleValue("padding-left");
      properties.fontStyle = getStyleValue("font-style");
      properties.textDecorationLine = getStyleValue("text-decoration-line");
      break;
    case "button":
      const buttonElement = element as HTMLButtonElement;
      properties.textContent = element.textContent || "";
      properties.type = buttonElement.type || "button";
      properties.backgroundColor = getStyleValue("background-color");
      properties.color = getStyleValue("color");
      properties.border = getStyleValue("border");
      properties.borderRadius = getStyleValue("border-radius");
      properties.padding = getStyleValue("padding");
      properties.fontSize = getStyleValue("font-size");
      properties.fontWeight = getStyleValue("font-weight");
      // Get data attributes for button configuration
      properties["data-href"] =
        buttonElement.getAttribute("data-href") || "";
      properties["data-target"] =
        buttonElement.getAttribute("data-target") || "";
      properties["data-button-size"] =
        buttonElement.getAttribute("data-button-size") || "medium";
      properties["data-button-type"] =
        buttonElement.getAttribute("data-button-type") || "flat";
      properties["data-button-style"] =
        buttonElement.getAttribute("data-button-style") || "default";
      break;
    default:
      properties.textContent = element.textContent || "";
      break;
  }

  return properties;
};
