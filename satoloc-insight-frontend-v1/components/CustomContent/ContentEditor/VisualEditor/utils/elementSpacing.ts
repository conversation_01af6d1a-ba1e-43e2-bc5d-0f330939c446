/**
 * Utility functions for managing element spacing in the VisualEditor
 */

/**
 * Adds appropriate spacing between elements for better insertion experience
 * @param editorRef - Reference to the editor container element
 */
export const addElementSpacing = (editorRef: React.RefObject<HTMLDivElement>) => {
  if (!editorRef.current) return;

  const elements = editorRef.current.querySelectorAll(
    "h1, h2, h3, h4, h5, h6, p, div, ul, ol, blockquote, pre, img"
  );

  elements.forEach((element) => {
    const htmlElement = element as HTMLElement;
    const tagName = htmlElement.tagName.toLowerCase();

    // Add appropriate margins for better spacing
    if (["h1", "h2", "h3", "h4", "h5", "h6"].includes(tagName)) {
      htmlElement.style.marginTop = htmlElement.style.marginTop || "24px";
      htmlElement.style.marginBottom =
        htmlElement.style.marginBottom || "12px";
    } else if (tagName === "p") {
      htmlElement.style.marginTop = htmlElement.style.marginTop || "12px";
      htmlElement.style.marginBottom =
        htmlElement.style.marginBottom || "12px";
    } else if (["ul", "ol"].includes(tagName)) {
      htmlElement.style.marginTop = htmlElement.style.marginTop || "12px";
      htmlElement.style.marginBottom =
        htmlElement.style.marginBottom || "12px";
    } else if (["blockquote", "pre"].includes(tagName)) {
      htmlElement.style.marginTop = htmlElement.style.marginTop || "20px";
      htmlElement.style.marginBottom =
        htmlElement.style.marginBottom || "20px";
    } else if (tagName === "img") {
      htmlElement.style.marginTop = htmlElement.style.marginTop || "16px";
      htmlElement.style.marginBottom =
        htmlElement.style.marginBottom || "16px";
      htmlElement.style.display = htmlElement.style.display || "block";
    }
  });
};
