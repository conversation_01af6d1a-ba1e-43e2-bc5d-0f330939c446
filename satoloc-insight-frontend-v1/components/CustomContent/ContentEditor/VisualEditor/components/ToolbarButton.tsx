import React from "react";
import { Button } from "@/components/ui/button";

interface ToolbarButtonProps {
  onClick: () => void;
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  isActive?: boolean;
  disabled?: boolean;
}

/**
 * Reusable toolbar button component for the VisualEditor
 */
export const ToolbarButton: React.FC<ToolbarButtonProps> = ({
  onClick,
  icon: Icon,
  title,
  isActive = false,
  disabled: buttonDisabled = false,
}) => (
  <Button
    variant="ghost"
    size="sm"
    onClick={onClick}
    title={title}
    disabled={buttonDisabled}
    className={`h-8 w-8 p-0 ${
      isActive
        ? "bg-blue-100 text-blue-600 dark:bg-background dark:text-blue-300"
        : "dark:hover:bg-gray-800"
    } ${buttonDisabled ? "opacity-50 cursor-not-allowed" : ""}`}
  >
    <Icon className="h-4 w-4" />
  </Button>
);
