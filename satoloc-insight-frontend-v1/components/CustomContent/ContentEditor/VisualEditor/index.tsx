"use client";

import React, { useState, useRef, useEffect, useCallback } from "react";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { useCustomContent } from "@/internal-api/custom-content";
import { Undo, Redo, Settings, Maximize, Minimize } from "lucide-react";
import SideToolbar from "@/components/CustomContent/ContentEditor/SideToolbar";
import ElementInsertion from "@/components/CustomContent/ContentEditor/ElementInsertion";
import { getElementProperties, addElementSpacing } from "./utils";
import { useUndoRedo, useElementSelection, useImageResize } from "./hooks";
import { ToolbarButton } from "./components";

interface VisualEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  disabled?: boolean;
  // Context props for AI image generation
  contentTitle?: string;
  contentText?: string;
  keywords?: string;
  tone?: string;
  originalPrompt?: string;
  // Callback to sync title when headline changes
  onTitleChange?: (newTitle: string) => void;
}

export default function VisualEditor({
  content,
  onChange,
  placeholder = "Start typing...",
  disabled = false,
  contentTitle,
  contentText,
  keywords,
  tone,
  originalPrompt,
  onTitleChange,
}: VisualEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isEditorFocused, setIsEditorFocused] = useState(false);

  const [selectionMode, setSelectionMode] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Element selection functionality using custom hook
  const {
    selectedElement,
    setSelectedElement,
    sidebarVisible,
    setSidebarVisible,
    handleElementSelection,
    handleDirectClick,
  } = useElementSelection({
    editorRef,
    getElementProperties,
    content,
    disabled,
  });

  // Undo/Redo functionality using custom hook
  const {
    history,
    historyIndex,
    isUndoRedoAction,
    setIsUndoRedoAction,
    addToHistory,
    handleUndo,
    handleRedo,
    canUndo,
    canRedo,
    setHistory,
    setHistoryIndex,
  } = useUndoRedo({
    initialContent: content,
    onChange,
    editorRef,
    addElementSpacing,
  });

  const { toast } = useToast();
  const { improveContent } = useCustomContent();

  // Toggle fullscreen
  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  // Initialize editor content
  useEffect(() => {
    if (editorRef.current && content !== editorRef.current.innerHTML) {
      editorRef.current.innerHTML = content || "";
      // Add spacing to elements for better insertion UI
      addElementSpacing(editorRef);
    }
  }, [content]);

  // Sync external content changes with history
  useEffect(() => {
    if (content && content !== history[historyIndex]) {
      setHistory([content]);
      setHistoryIndex(0);
    }
  }, [content]);

  const handleEditorChange = () => {
    if (editorRef.current) {
      const newContent = editorRef.current.innerHTML;
      onChange(newContent);

      // Add to history if this is not an undo/redo action
      if (!isUndoRedoAction) {
        addToHistory(newContent);
      }

      // Update selected element properties if an element is selected
      // This ensures bilateral live editing - content changes update SideToolbar
      if (selectedElement) {
        const currentProperties = getElementProperties(selectedElement.element);
        setSelectedElement((prev) => {
          if (!prev) return prev;
          return {
            ...prev,
            properties: currentProperties,
          };
        });
      }

      // Re-apply spacing after content changes
      setTimeout(() => addElementSpacing(editorRef), 10);
    }
  };

  // Image resizing functionality using custom hook
  const { isResizing, resizeStartData, handleResizeStart } = useImageResize({
    selectedElement,
    setSelectedElement,
    handleEditorChange,
    disabled,
  });

  // Add content to history for undo/redo

  // Set up keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // F11 for fullscreen toggle
      if (event.key === "F11") {
        event.preventDefault();
        toggleFullscreen();
      }

      // Undo/Redo shortcuts
      if (event.ctrlKey || event.metaKey) {
        if (event.key === "z" && !event.shiftKey) {
          event.preventDefault();
          handleUndo();
        } else if (event.key === "y" || (event.key === "z" && event.shiftKey)) {
          event.preventDefault();
          handleRedo();
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [toggleFullscreen, handleUndo, handleRedo]);

  // Update element property
  const updateElementProperty = useCallback(
    (
      element: HTMLElement,
      property: string,
      value: string,
      persist = false
    ) => {
      if (!element) return;

      switch (property) {
        case "textContent":
          element.textContent = value;
          break;
        case "id":
          element.id = value;
          break;
        case "className":
          element.className = value;
          break;
        case "href":
          if (element instanceof HTMLAnchorElement) {
            element.href = value;
          }
          break;
        case "target":
          if (element instanceof HTMLAnchorElement) {
            element.target = value;
          }
          break;
        case "src":
          if (element instanceof HTMLImageElement) {
            element.src = value;
          }
          break;
        case "alt":
          if (element instanceof HTMLImageElement) {
            element.alt = value;
          }
          break;
        case "width":
          if (element instanceof HTMLImageElement) {
            element.width = parseInt(value) || 0;
          }
          break;
        case "height":
          if (element instanceof HTMLImageElement) {
            element.height = parseInt(value) || 0;
          }
          break;
        case "color":
          element.style.color = value;
          break;
        case "fontSize":
          element.style.fontSize = value;
          break;
        case "fontWeight":
          element.style.fontWeight = value;
          break;
        case "textAlign":
          element.style.textAlign = value;
          break;
        case "backgroundColor":
          element.style.backgroundColor = value;
          break;
        case "padding":
          element.style.padding = value;
          break;
        case "margin":
          element.style.margin = value;
          break;
        case "textDecoration":
          element.style.textDecoration = value;
          break;
        case "fontStyle":
          element.style.fontStyle = value;
          break;
        case "textDecorationLine":
          element.style.textDecorationLine = value;
          break;
        default:
          // For other properties, set as style
          element.style.setProperty(property, value);
          break;
      }

      if (persist) {
        handleEditorChange();
      }
    },
    [handleEditorChange]
  );

  const handleTagChange = (newTag: string) => {
    if (!selectedElement) return;

    const range = document.createRange();
    range.selectNodeContents(selectedElement.element);
    const selection = window.getSelection();
    if (selection) {
      selection.removeAllRanges();
      selection.addRange(range);
    }

    document.execCommand("formatBlock", false, newTag);

    const newElement = window.getSelection()?.focusNode?.parentElement;

    if (newElement && editorRef.current?.contains(newElement)) {
      handleElementSelection(newElement);
    } else {
      setSelectedElement(null);
      setSidebarVisible(false);
    }

    handleEditorChange();
  };

  const handleUpdateProperty = useCallback(
    (property: string, value: string, persist: boolean = false) => {
      if (selectedElement) {
        updateElementProperty(
          selectedElement.element,
          property,
          value,
          persist
        );

        // Only keep sidebar in sync for color changes to avoid affecting other properties
        if (persist) {
          setSelectedElement((prev) => {
            if (!prev) return prev;
            const newProperties = {
              ...prev.properties,
              [property]: value,
            };
            return {
              ...prev,
              properties: newProperties,
            };
          });

          // Only notify parent form when persisting changes to avoid cursor loss
          handleEditorChange();
        }
      }
    },
    [selectedElement, updateElementProperty, handleEditorChange]
  );

  const handleCloseSidebar = useCallback(() => {
    setSidebarVisible(false);
  }, []);

  const executeCommand = (command: string, value?: string) => {
    if (disabled) return;

    document.execCommand(command, false, value);
    editorRef.current?.focus();
    handleEditorChange();
  };

  const changeTextColor = (color: string) => {
    executeCommand("foreColor", color);

    // If an element is selected, update its color property in state so the sidebar color picker reflects the change immediately
    if (selectedElement) {
      // Apply the color directly to the element so computed styles reflect the change
      selectedElement.element.style.color = color;
    }

    setSelectedElement((prev) => {
      if (!prev) return prev;
      return {
        ...prev,
        properties: {
          ...prev.properties,
          color,
        },
      };
    });
  };

  return (
    <div
      className={`flex border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden ${
        isFullscreen
          ? "fixed inset-0 z-50 bg-white dark:bg-background h-screen"
          : "h-full"
      }`}
    >
      {/* Main editor area */}
      <div className="flex flex-col flex-1 overflow-hidden min-h-0">
        {/* Toolbar */}
        <div className="flex items-center gap-1 p-1 sm:p-2 bg-gray-50 dark:bg-background border-b border-gray-200 dark:border-gray-700 flex-wrap shrink-0">
          {/* Undo/Redo */}
          <ToolbarButton
            onClick={handleUndo}
            icon={Undo}
            title="Undo (Ctrl+Z)"
            isActive={false}
            disabled={!canUndo}
          />
          <ToolbarButton
            onClick={handleRedo}
            icon={Redo}
            title="Redo (Ctrl+Y)"
            isActive={false}
            disabled={!canRedo}
          />
          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Fullscreen Toggle */}
          <ToolbarButton
            onClick={toggleFullscreen}
            icon={isFullscreen ? Minimize : Maximize}
            title={
              isFullscreen ? "Exit Fullscreen (F11)" : "Enter Fullscreen (F11)"
            }
          />

          {/* Element Selection Mode - Removed as elements should be directly clickable */}
        </div>

        {/* Editor */}
        <div className="flex-1 relative overflow-hidden">
          <style jsx>{`
            .placeholder-element:empty::before {
              content: attr(data-placeholder);
              color: #9ca3af;
              font-style: italic;
              pointer-events: none;
            }
            .placeholder-element:focus::before {
              display: none;
            }
          `}</style>
          <div
            ref={editorRef}
            contentEditable={!disabled}
            onInput={handleEditorChange}
            onFocus={() => setIsEditorFocused(true)}
            onBlur={() => setIsEditorFocused(false)}
            onClick={handleDirectClick}
            className={`
              absolute inset-0 pl-16 pr-4 py-4 outline-none overflow-y-auto
              prose prose-sm dark:prose-invert max-w-none
              ${disabled ? "opacity-50 cursor-not-allowed" : ""}
              ${
                !content && !isEditorFocused
                  ? "text-gray-400 dark:text-gray-500"
                  : ""
              }
              ""
            `}
            data-placeholder={placeholder}
          />

          {/* Selection Mode Instruction */}
          {false && !selectedElement && (
            <div className="absolute top-2 left-2 bg-blue-100 text-blue-800 dark:bg-background dark:text-blue-300 px-2 py-1 rounded text-xs font-medium z-10">
              Click any element to select it
            </div>
          )}

          {/* Selected Element Highlight */}
          {selectedElement &&
            (() => {
              const rect = selectedElement.element.getBoundingClientRect();
              const editorRect = editorRef.current?.getBoundingClientRect();

              if (!editorRect) return null;

              return (
                <>
                  <div
                    className="absolute border border-blue-100 bg-blue-100/5 pointer-events-none z-5 rounded"
                    style={{
                      top: rect.top - editorRect.top,
                      left: rect.left - editorRect.left,
                      width: rect.width,
                      height: rect.height,
                    }}
                  />

                  {/* Resize handles for images */}
                  {selectedElement.element instanceof HTMLImageElement && (
                    <>
                      {/* Bottom-right resize handle */}
                      <div
                        className="absolute w-3 h-3 bg-blue-500 border border-white cursor-nw-resize z-10 hover:bg-blue-600 transition-colors"
                        style={{
                          top: rect.top - editorRect.top + rect.height - 6,
                          left: rect.left - editorRect.left + rect.width - 6,
                        }}
                        onMouseDown={(e) =>
                          handleResizeStart(
                            e,
                            selectedElement.element as HTMLImageElement,
                            "se"
                          )
                        }
                      />

                      {/* Bottom-left resize handle */}
                      <div
                        className="absolute w-3 h-3 bg-blue-500 border border-white cursor-ne-resize z-10 hover:bg-blue-600 transition-colors"
                        style={{
                          top: rect.top - editorRect.top + rect.height - 6,
                          left: rect.left - editorRect.left - 6,
                        }}
                        onMouseDown={(e) =>
                          handleResizeStart(
                            e,
                            selectedElement.element as HTMLImageElement,
                            "sw"
                          )
                        }
                      />

                      {/* Top-right resize handle */}
                      <div
                        className="absolute w-3 h-3 bg-blue-500 border border-white cursor-ne-resize z-10 hover:bg-blue-600 transition-colors"
                        style={{
                          top: rect.top - editorRect.top - 6,
                          left: rect.left - editorRect.left + rect.width - 6,
                        }}
                        onMouseDown={(e) =>
                          handleResizeStart(
                            e,
                            selectedElement.element as HTMLImageElement,
                            "ne"
                          )
                        }
                      />

                      {/* Top-left resize handle */}
                      <div
                        className="absolute w-3 h-3 bg-blue-500 border border-white cursor-nw-resize z-10 hover:bg-blue-600 transition-colors"
                        style={{
                          top: rect.top - editorRect.top - 6,
                          left: rect.left - editorRect.left - 6,
                        }}
                        onMouseDown={(e) =>
                          handleResizeStart(
                            e,
                            selectedElement.element as HTMLImageElement,
                            "nw"
                          )
                        }
                      />
                    </>
                  )}
                </>
              );
            })()}

          {/* Placeholder */}
          {!content && !isEditorFocused && (
            <div className="absolute top-4 left-16 text-gray-400 pointer-events-none">
              {placeholder}
            </div>
          )}

          {/* Element Insertion Component */}
          <ElementInsertion
            editorRef={editorRef}
            onContentChange={handleEditorChange}
            disabled={disabled}
            contentTitle={contentTitle}
            contentText={contentText}
            keywords={keywords}
            tone={tone}
            originalPrompt={originalPrompt}
          />
        </div>
      </div>

      {/* Side Toolbar */}
      {sidebarVisible && selectedElement && (
        <SideToolbar
          key={selectedElement.element.outerHTML}
          selectedElement={selectedElement}
          onClose={handleCloseSidebar}
          onUpdateProperty={handleUpdateProperty}
          improveContent={improveContent}
          onTagChange={handleTagChange}
        />
      )}
    </div>
  );
}
