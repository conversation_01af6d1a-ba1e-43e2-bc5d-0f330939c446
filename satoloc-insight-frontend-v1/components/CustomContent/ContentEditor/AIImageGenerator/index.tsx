"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Loader2,
  Palette,
  Download,
  ExternalLink,
  Sparkles,
  Wand2,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import {
  useContentEnhancement,
  AIImageResult,
} from "@/internal-api/custom-content/enhance";
import { useCustomContent } from "@/internal-api/custom-content";

interface AIImageGeneratorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImageGenerated: (imageUrl: string, altText?: string) => void;
  initialPrompt?: string;
  // Context-aware props
  contentTitle?: string;
  contentText?: string;
  keywords?: string;
  tone?: string;
  originalPrompt?: string;
}

export default function AIImageGenerator({
  open,
  onOpenChange,
  onImageGenerated,
  initialPrompt = "",
  contentTitle,
  contentText,
  keywords,
  tone = "professional",
  originalPrompt,
}: AIImageGeneratorProps) {
  const [dallePrompt, setDallePrompt] = useState<string>(initialPrompt);
  const [dalleSize, setDalleSize] = useState<string>("1024x1024");
  const [dalleModel, setDalleModel] = useState<string>("gpt-image-1");
  const [dalleQuality, setDalleQuality] = useState<string>("medium");
  const [dalleStyle, setDalleStyle] = useState<string>("standard");
  const [dalleImage, setDalleImage] = useState<AIImageResult | null>(null);
  const [generatedAltText, setGeneratedAltText] = useState<string>("");
  const [isGeneratingPrompt, setIsGeneratingPrompt] = useState<boolean>(false);

  const { enhanceContent, isLoading } = useContentEnhancement();
  const { improvePrompt } = useCustomContent();
  const { toast } = useToast();

  // Generate context-aware image prompt
  const generateContextAwarePrompt = async () => {
    if (!contentTitle && !contentText && !originalPrompt) {
      return "";
    }

    setIsGeneratingPrompt(true);
    try {
      // Build context from available information
      let context = "";

      if (contentTitle) {
        context += `Title: ${contentTitle}\n`;
      }

      if (originalPrompt) {
        context += `Original Prompt: ${originalPrompt}\n`;
      }

      if (keywords) {
        context += `Keywords: ${keywords}\n`;
      }

      if (contentText) {
        // Extract first 500 characters of content for context
        const contentPreview = contentText
          .replace(/<[^>]*>/g, "")
          .substring(0, 500);
        context += `Content Preview: ${contentPreview}...\n`;
      }

      // Create a prompt for generating an image description
      const promptGenerationRequest = `Based on the following content context, generate a detailed, 
      specific image prompt that would create a relevant, professional image suitable for this content. 
      The image should complement the content and be visually appealing.

      ${context}

      Tone: ${tone}

      Generate a clear, detailed image prompt (2-3 sentences) that describes what the image should show, including style, composition, colors, and mood. 
      Focus on creating an image that would enhance and support the content above.`;

      const result = await improvePrompt({
        prompt: promptGenerationRequest,
        title: contentTitle,
        keywords: keywords,
        tone: tone,
      });

      if (result?.improved_prompt) {
        setDallePrompt(result.improved_prompt);
        toast({
          title: "Smart Prompt Generated",
          description:
            "AI has created a contextual image prompt based on your content",
        });
      }
    } catch (error: any) {
      console.error("Error generating context-aware prompt:", error);
      // Fallback to basic prompt generation
      generateFallbackPrompt();
    } finally {
      setIsGeneratingPrompt(false);
    }
  };

  // Fallback prompt generation using simple context
  const generateFallbackPrompt = () => {
    let fallbackPrompt = "";

    if (contentTitle) {
      fallbackPrompt = `Create a professional, high-quality image related to "${contentTitle}"`;

      if (keywords) {
        fallbackPrompt += ` incorporating themes of ${keywords}`;
      }

      fallbackPrompt += `. The image should be visually appealing, modern, and suitable for ${tone} content.`;
    } else if (originalPrompt) {
      fallbackPrompt = `Create a professional image that complements content about: ${originalPrompt.substring(0, 100)}...`;
    } else {
      fallbackPrompt =
        "Create a professional, modern, visually appealing image";
    }

    setDallePrompt(fallbackPrompt);
  };

  // Auto-generate prompt when dialog opens and we have context
  React.useEffect(() => {
    if (
      open &&
      !dallePrompt &&
      (contentTitle || contentText || originalPrompt)
    ) {
      generateContextAwarePrompt();
    }
  }, [open, contentTitle, contentText, originalPrompt]);

  const handleGenerateImage = async () => {
    if (!dallePrompt.trim()) {
      toast({
        title: "Error",
        description: "Please enter a prompt for the AI image",
        variant: "destructive",
      });
      return;
    }

    try {
      const result = await enhanceContent<AIImageResult>({
        type: "ai-image",
        content: dallePrompt,
        title: dallePrompt,
        keywords: "",
        prompt: dallePrompt,
        size: dalleSize,
        model: dalleModel,
        quality: dalleQuality,
        style: dalleStyle,
      });

      if (result) {
        setDalleImage(result);
        // Generate alt text based on the prompt
        const altText = `AI-generated image: ${dallePrompt.substring(0, 100)}${
          dallePrompt.length > 100 ? "..." : ""
        }`;
        setGeneratedAltText(altText);

        toast({
          title: "Image Generated",
          description: "AI image has been generated successfully",
        });
      }
    } catch (error: any) {
      console.error("Error generating AI image:", error);
      toast({
        title: "Generation Failed",
        description: error.message || "Failed to generate AI image",
        variant: "destructive",
      });
    }
  };

  const handleUseImage = () => {
    if (dalleImage?.url) {
      onImageGenerated(dalleImage.url, generatedAltText);
      onOpenChange(false);
      // Reset state
      setDalleImage(null);
      setGeneratedAltText("");
    }
  };

  const handleDownloadImage = async () => {
    if (!dalleImage?.url) return;

    try {
      toast({
        title: "Downloading...",
        description: "Preparing your image for download",
      });

      let url = dalleImage.url;
      let blob;

      // If it's a data URL, we can use it directly
      if (dalleImage.url.startsWith("data:")) {
        // Extract base64 data from data URL
        const base64Data = dalleImage.url.split(",")[1];
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        blob = new Blob([byteArray], { type: "image/png" });
        url = window.URL.createObjectURL(blob);
      } else {
        // For external URLs, try to fetch and create blob
        try {
          const response = await fetch(dalleImage.url);
          blob = await response.blob();
          url = window.URL.createObjectURL(blob);
        } catch (fetchError) {
          console.warn("Could not fetch image for download:", fetchError);
        }
      }

      const filename = `ai-generated-image-${Date.now()}.png`;
      const a = document.createElement("a");
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      if (blob) {
        window.URL.revokeObjectURL(url);
      }

      toast({
        title: "Image Downloaded",
        description: "Image has been downloaded successfully",
      });
    } catch (error) {
      console.error("Error downloading image:", error);
      toast({
        title: "Download Failed",
        description: "Failed to download the image. Please try again.",
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setDallePrompt(initialPrompt);
    setDalleImage(null);
    setGeneratedAltText("");
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Generate AI Image</DialogTitle>
          <DialogDescription>
            Create custom images using AI. Describe what you want to see and
            adjust the settings below.
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column - Controls */}
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label htmlFor="dallePrompt" className="text-sm font-medium">
                  Image Description *
                </Label>
                {(contentTitle || contentText || originalPrompt) && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={generateContextAwarePrompt}
                    disabled={isLoading || isGeneratingPrompt}
                    className="h-7 px-2 text-xs border-[#1279b4] text-[#1279b4] hover:bg-[#1279b4] hover:text-white"
                  >
                    {isGeneratingPrompt ? (
                      <>
                        <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-3 w-3 mr-1" />
                        Smart Prompt
                      </>
                    )}
                  </Button>
                )}
              </div>
              <Textarea
                id="dallePrompt"
                placeholder={
                  contentTitle || contentText || originalPrompt
                    ? "Click 'Smart Prompt' to generate a contextual image description, or describe the image manually..."
                    : "Describe the image you want to generate..."
                }
                value={dallePrompt}
                onChange={(e) => setDallePrompt(e.target.value)}
                disabled={isLoading || isGeneratingPrompt}
                className="mt-1 min-h-[80px]"
              />
              {(contentTitle || contentText || originalPrompt) && (
                <p className="text-xs text-muted-foreground mt-1 flex items-center">
                  <Wand2 className="h-3 w-3 mr-1" />
                  AI can generate a contextual prompt based on your content:
                  {contentTitle && (
                    <span className="font-medium ml-1">"{contentTitle}"</span>
                  )}
                </p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label className="text-xs font-medium mb-1 block">
                  Image Size
                </Label>
                <Select
                  value={dalleSize}
                  onValueChange={setDalleSize}
                  disabled={isLoading}
                >
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue placeholder="Select size" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1024x1024">
                      Square (1024x1024)
                    </SelectItem>
                    <SelectItem value="1536x1024">
                      Landscape (1536x1024)
                    </SelectItem>
                    <SelectItem value="1024x1536">
                      Portrait (1024x1536)
                    </SelectItem>
                    <SelectItem value="auto">Auto (Model Selected)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-xs font-medium mb-1 block">
                  Quality
                </Label>
                <Select
                  value={dalleQuality}
                  onValueChange={setDalleQuality}
                  disabled={isLoading}
                >
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue placeholder="Select quality" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low (Fast)</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High (Detailed)</SelectItem>
                    <SelectItem value="auto">Auto (Model Selected)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label className="text-xs font-medium mb-1 block">
                Background
              </Label>
              <Select
                value={dalleStyle}
                onValueChange={setDalleStyle}
                disabled={isLoading}
              >
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue placeholder="Select background" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="standard">Standard</SelectItem>
                  <SelectItem value="transparent">Transparent</SelectItem>
                  <SelectItem value="auto">Auto</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-[10px] text-muted-foreground mt-1">
                Transparent requires PNG format
              </p>
            </div>

            <Button
              onClick={handleGenerateImage}
              disabled={isLoading || !dallePrompt.trim()}
              className="w-full bg-[#1279b4] text-white hover:bg-[#1279b4]/90"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Generating...
                </>
              ) : (
                "Generate AI Image"
              )}
            </Button>
          </div>

          {/* Right Column - Image Preview */}
          <div className="border rounded-md overflow-hidden relative flex flex-col items-center justify-center min-h-[300px] bg-slate-50 dark:bg-gray-800">
            {isLoading ? (
              <div className="flex flex-col items-center justify-center h-full w-full">
                <Loader2 className="h-8 w-8 animate-spin mb-2 text-[#1279b4]" />
                <p className="text-sm text-muted-foreground">
                  Generating image...
                </p>
              </div>
            ) : dalleImage ? (
              <div className="flex flex-col h-full w-full">
                <div className="relative flex-1 flex items-center justify-center p-4">
                  {dalleImage.url ? (
                    <img
                      src={dalleImage.url}
                      alt="AI-generated image"
                      className="max-w-full max-h-[300px] object-contain rounded"
                      onError={(e) => {
                        // Try proxy endpoint for external URLs (like S3) if direct loading fails
                        if (
                          !dalleImage.url.startsWith("data:") &&
                          !e.currentTarget.src.includes("/proxy-image")
                        ) {
                          e.currentTarget.src = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/custom-content/proxy-image/?imageUrl=${encodeURIComponent(
                            dalleImage.url
                          )}`;
                        } else {
                          e.currentTarget.style.display = "none";
                        }
                      }}
                    />
                  ) : (
                    <div className="text-center text-muted-foreground">
                      <p>Image generation failed</p>
                    </div>
                  )}
                </div>

                {/* Image Actions */}
                <div className="p-3 bg-white dark:bg-gray-700 border-t flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleDownloadImage}
                    className="flex-1"
                  >
                    <Download className="h-3 w-3 mr-1" />
                    Download
                  </Button>
                  {dalleImage.url && !dalleImage.url.startsWith("data:") && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => window.open(dalleImage.url, "_blank")}
                    >
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                  )}
                </div>

                {/* Image Details */}
                <div className="p-2 text-xs bg-slate-100 dark:bg-gray-800 border-t">
                  <div className="flex flex-wrap gap-2 text-muted-foreground">
                    <span>
                      <strong>Model:</strong> GPT Image
                    </span>
                    <span className="mx-1">•</span>
                    <span>
                      <strong>Size:</strong> {dalleSize}
                    </span>
                    {dalleImage?.quality && (
                      <>
                        <span className="mx-1">•</span>
                        <span>
                          <strong>Quality:</strong> {dalleImage.quality}
                        </span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center p-6 text-center text-muted-foreground h-full">
                <Palette className="h-12 w-12 mb-3 opacity-20" />
                <p className="text-sm">
                  Enter a description and click "Generate AI Image" to create
                  your custom image
                </p>
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => {
              resetForm();
              onOpenChange(false);
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleUseImage}
            disabled={!dalleImage?.url}
            className="bg-[#1279b4] text-white hover:bg-[#1279b4]/90"
          >
            Use This Image
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
