"use client";

import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo,
} from "react";
import AceEditor from "react-ace";

// Import Ace Editor modes and themes
import "ace-builds/src-noconflict/mode-html";
import "ace-builds/src-noconflict/theme-github";
import "ace-builds/src-noconflict/theme-monokai";
import "ace-builds/src-noconflict/theme-tomorrow";
import "ace-builds/src-noconflict/theme-twilight";

// Import Ace Editor extensions
import "ace-builds/src-noconflict/ext-language_tools";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import "ace-builds/src-noconflict/ext-beautify";
import "ace-builds/src-noconflict/ext-searchbox";
import "ace-builds/src-noconflict/ext-code_lens";
import "ace-builds/src-noconflict/ext-elastic_tabstops_lite";
import "ace-builds/src-noconflict/ext-error_marker";
import "ace-builds/src-noconflict/ext-keybinding_menu";
import "ace-builds/src-noconflict/ext-settings_menu";
import "ace-builds/src-noconflict/ext-spellcheck";
import "ace-builds/src-noconflict/ext-split";
import "ace-builds/src-noconflict/ext-static_highlight";
import "ace-builds/src-noconflict/ext-statusbar";
import "ace-builds/src-noconflict/ext-textarea";
import "ace-builds/src-noconflict/ext-themelist";
import "ace-builds/src-noconflict/ext-whitespace";

import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Settings,
  Palette,
  Eye,
  Split,
  Maximize,
  Minimize,
  RefreshCw,
  Globe,
} from "lucide-react";


interface AceHtmlEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  disabled?: boolean;
  viewMode?: "code" | "split" | "preview";
}

type ViewMode = "code" | "split" | "preview";

export default function AceHtmlEditor({
  content,
  onChange,
  placeholder = "Enter content...",
  disabled = false,
  viewMode: initialViewMode = "split",
}: AceHtmlEditorProps) {
  const htmlEditorRef = useRef<AceEditor>(null);

  const [theme, setTheme] = useState("github");
  const [fontSize, setFontSize] = useState(14);
  const [viewMode, setViewMode] = useState<ViewMode>(initialViewMode);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  // HTML content state
  const [htmlContent, setHtmlContent] = useState("");

  // Track if content is being updated to prevent infinite loops
  const [lastUpdatedEditor, setLastUpdatedEditor] = useState<
    "html" | null
  >(null);

  // Professional HTML beautification using Ace's built-in beautify
  const formatHtmlContent = useCallback((html: string): string => {
    if (!html) return html;

    try {
      // Use Ace's built-in beautify for better formatting
      const ace = require('ace-builds/src-noconflict/ace');
      const beautify = require('ace-builds/src-noconflict/ext-beautify');
      
      // Create a temporary editor session for formatting
      const EditSession = ace.require('ace/edit_session').EditSession;
      const HTMLMode = ace.require('ace/mode/html').Mode;
      
      const session = new EditSession(html);
      session.setMode(new HTMLMode());
      
      // Apply beautify
      const beautifier = beautify.beautify;
      if (beautifier && typeof beautifier === 'function') {
        beautifier(session);
        return session.getValue();
      }
      
      // Fallback to manual formatting if beautify fails
      return formatHtmlManually(html);
    } catch (error) {
      console.warn('Ace beautify failed, using fallback:', error);
      return formatHtmlManually(html);
    }
  }, []);

  // Fallback manual HTML formatting
  const formatHtmlManually = useCallback((html: string): string => {
    if (!html) return html;

    try {
      // More sophisticated HTML formatting
      let formatted = html
        // Normalize whitespace
        .replace(/\s+/g, ' ')
        .replace(/\s*<\s*/g, '<')
        .replace(/\s*>\s*/g, '>')
        // Add line breaks
        .replace(/></g, '>\n<')
        .replace(/(<\/?(?:div|p|h[1-6]|ul|ol|li|table|tr|td|th|thead|tbody|tfoot|section|article|header|footer|nav|main|aside)[^>]*>)/gi, '\n$1\n')
        .replace(/(<\/?(?:br|hr|img|input|meta|link)[^>]*\/?\s*>)/gi, '\n$1\n')
        // Clean up multiple line breaks
        .replace(/\n\s*\n\s*\n/g, '\n\n')
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0);

      // Apply proper indentation
      let indentLevel = 0;
      const indentSize = 2;
      const voidElements = new Set(['area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input', 'link', 'meta', 'param', 'source', 'track', 'wbr']);
      
      const indentedLines = formatted.map((line) => {
        const trimmed = line.trim();
        if (!trimmed) return '';

        // Handle closing tags
        if (trimmed.startsWith('</')) {
          indentLevel = Math.max(0, indentLevel - 1);
        }
        // Handle self-closing tags and comments
        else if (trimmed.startsWith('<!--') || trimmed.endsWith('-->')) {
          // Comments don't affect indentation
        }
        // Handle DOCTYPE
        else if (trimmed.toLowerCase().startsWith('<!doctype')) {
          // DOCTYPE doesn't affect indentation
        }

        const indentedLine = ' '.repeat(indentLevel * indentSize) + trimmed;

        // Handle opening tags (increase indent for next line)
        if (trimmed.startsWith('<') && !trimmed.startsWith('</') && !trimmed.startsWith('<!--')) {
          const tagMatch = trimmed.match(/<([a-zA-Z][a-zA-Z0-9]*)/i);
          if (tagMatch) {
            const tagName = tagMatch[1].toLowerCase();
            const isSelfClosing = trimmed.endsWith('/>') || voidElements.has(tagName);
            const isSelfContained = trimmed.match(new RegExp(`<${tagName}[^>]*>.*<\/${tagName}>`, 'i'));
            
            if (!isSelfClosing && !isSelfContained) {
              indentLevel++;
            }
          }
        }

        return indentedLine;
      });

      return indentedLines.join('\n');
    } catch (error) {
      console.error('Error in manual HTML formatting:', error);
      return html;
    }
  }, []);

  // Initialize HTML content
  useEffect(() => {
    if (!content) {
      setHtmlContent("");
      return;
    }

    // Format the HTML content for better readability
    const formattedHtml = formatHtmlContent(content);
    setHtmlContent(formattedHtml);
  }, [content, formatHtmlContent]);





  // Handle HTML content changes
  const handleHtmlChange = useCallback(
    (newHtml: string) => {
      if (isUpdating) return;

      setIsUpdating(true);
      setLastUpdatedEditor("html");
      setHtmlContent(newHtml);

      // Update parent component with HTML content
      onChange(newHtml);

      setTimeout(() => {
        setIsUpdating(false);
        setLastUpdatedEditor(null);
      }, 100);
    },
    [isUpdating, onChange]
  );

  // Beautify HTML content (manual trigger)
  const beautifyHtml = useCallback(() => {
    if (!htmlContent || !htmlEditorRef.current) return;
    
    try {
      const editor = htmlEditorRef.current.editor;
      if (editor) {
        // Use Ace's built-in beautify command
        editor.execCommand('beautify');
        
        // If that doesn't work, fallback to manual formatting
        setTimeout(() => {
          const currentValue = editor.getValue();
          if (currentValue === htmlContent) {
            // Beautify command didn't work, use manual formatting
            const formatted = formatHtmlContent(htmlContent);
            if (formatted !== htmlContent) {
              editor.setValue(formatted);
              editor.clearSelection();
            }
          }
        }, 100);
      }
    } catch (error) {
      console.warn('Beautify command failed, using manual formatting:', error);
      const formatted = formatHtmlContent(htmlContent);
      if (formatted !== htmlContent) {
        handleHtmlChange(formatted);
      }
    }
  }, [htmlContent, formatHtmlContent, handleHtmlChange]);

  // Toggle fullscreen
  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  // Set up keyboard shortcuts
  useEffect(() => {
    if (!htmlEditorRef.current?.editor) return;

    const editor = htmlEditorRef.current.editor;
    
    // Add custom keyboard shortcuts
    editor.commands.addCommand({
      name: 'beautifyHtml',
      bindKey: { win: 'Ctrl-Alt-B', mac: 'Cmd-Alt-B' },
      exec: () => {
        beautifyHtml();
      },
      readOnly: false
    });

    editor.commands.addCommand({
      name: 'toggleFullscreen',
      bindKey: { win: 'F11', mac: 'F11' },
      exec: () => {
        toggleFullscreen();
      },
      readOnly: false
    });

    return () => {
      // Cleanup commands when component unmounts
      try {
        editor.commands.removeCommand('beautifyHtml');
        editor.commands.removeCommand('toggleFullscreen');
      } catch (error) {
        console.warn('Error cleaning up editor commands:', error);
      }
    };
  }, [beautifyHtml, toggleFullscreen]);

  // Render HTML preview
  const renderPreview = useMemo(() => {
    return (
      <div
        className="prose prose-sm dark:prose-invert max-w-none p-4 h-full overflow-auto bg-white dark:bg-background border border-gray-200 dark:border-gray-800 rounded"
        dangerouslySetInnerHTML={{ __html: htmlContent }}
      />
    );
  }, [htmlContent]);

  // Toolbar component
  const ToolbarButton = ({
    onClick,
    icon: Icon,
    title,
    isActive = false,
    disabled: buttonDisabled = false,
  }: {
    onClick: () => void;
    icon: React.ComponentType<{ className?: string }>;
    title: string;
    isActive?: boolean;
    disabled?: boolean;
  }) => (
    <Button
      variant={isActive ? "default" : "ghost"}
      size="sm"
      onClick={onClick}
      disabled={disabled || buttonDisabled}
      title={title}
      className="h-8 w-8 p-0 dark:text-gray-300 dark:hover:bg-gray-800"
    >
      <Icon className="h-4 w-4" />
    </Button>
  );

  return (
    <div
      className={`flex flex-col h-full ${isFullscreen ? "fixed inset-0 z-50 bg-white dark:bg-background" : ""}`}
    >
      {/* Toolbar */}
      <div className="flex items-center justify-between p-2 border-b border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-background">
        <div className="flex items-center gap-2">
          {/* View Mode Tabs */}
          <Tabs
            value={viewMode}
            onValueChange={(value) => setViewMode(value as ViewMode)}
            className=""
          >
            <TabsList className="grid w-full grid-cols-2 dark:bg-background">
              <TabsTrigger value="split" className="flex items-center gap-1">
                <Split className="h-3 w-3" />
                Code + Preview
              </TabsTrigger>
              <TabsTrigger value="preview" className="flex items-center gap-1">
                <Eye className="h-3 w-3" />
                Preview
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Format HTML Button */}
          <ToolbarButton
            onClick={beautifyHtml}
            icon={RefreshCw}
            title="Format HTML (Ctrl+Alt+B)"
            disabled={disabled || !htmlContent}
          />
          
          {/* Find & Replace */}
          <ToolbarButton
            onClick={() => {
              if (htmlEditorRef.current?.editor) {
                htmlEditorRef.current.editor.execCommand('find');
              }
            }}
            icon={Settings}
            title="Find & Replace (Ctrl+F)"
            disabled={disabled}
          />
        </div>

        <div className="flex items-center gap-2">
          {/* Theme Selector */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 dark:hover:bg-background"
              >
                <Palette className="h-4 w-4 mr-1" />
                Theme
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setTheme("github")}>
                GitHub
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme("monokai")}>
                Monokai
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme("tomorrow")}>
                Tomorrow
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme("twilight")}>
                Twilight
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Font Size Selector */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 dark:hover:bg-background"
              >
                <Settings className="h-4 w-4 mr-1" />
                {fontSize}px
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setFontSize(12)}>
                12px
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFontSize(14)}>
                14px
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFontSize(16)}>
                16px
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFontSize(18)}>
                18px
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Fullscreen Toggle */}
          <ToolbarButton
            onClick={toggleFullscreen}
            icon={isFullscreen ? Minimize : Maximize}
            title={isFullscreen ? "Exit Fullscreen (F11)" : "Enter Fullscreen (F11)"}
          />
        </div>
      </div>

      {/* Editor Content */}
      <div className="flex-1 flex overflow-hidden">
        {viewMode === "split" && (
          <PanelGroup direction="horizontal" className="h-full">
            {/* HTML Editor Panel */}
            <Panel
              defaultSize={50}
              minSize={30}
              maxSize={70}
              className="flex flex-col"
            >
              <div className="px-3 py-2 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-700 dark:bg-background dark:border-gray-800 dark:text-gray-300">
                HTML Code
              </div>
              <div className="flex-1">
                <AceEditor
                  ref={htmlEditorRef}
                  mode="html"
                  theme={theme}
                  name="html-editor"
                  onChange={handleHtmlChange}
                  value={htmlContent}
                  fontSize={fontSize}
                  width="100%"
                  height="100%"
                  readOnly={disabled}
                  placeholder="Enter HTML content..."
                  setOptions={{
                    // Autocompletion
                    enableBasicAutocompletion: true,
                    enableLiveAutocompletion: true,
                    enableSnippets: true,
                    
                    // Line numbers and gutter
                    showLineNumbers: true,
                    showGutter: true,
                    showPrintMargin: true,
                    printMarginColumn: 120,
                    
                    // Indentation
                    tabSize: 2,
                    useSoftTabs: true,
                    
                    // Code folding
                    showFoldWidgets: true,
                    foldStyle: 'markbeginend',
                    
                    // Selection and cursor
                    highlightActiveLine: true,
                    highlightSelectedWord: true,
                    selectionStyle: 'line',
                    
                    // Wrapping
                    wrap: true,
                    wrapBehavioursEnabled: true,
                    autoScrollEditorIntoView: true,
                    
                    // Brackets
                    behavioursEnabled: true,
                    showMatchingBrackets: true,
                    
                    // Performance
                    useWorker: false,
                    animatedScroll: true,
                    
                    // Search
                    enableMultiselect: true,
                    
                    // Whitespace
                    showInvisibles: false,
                    displayIndentGuides: true,
                    
                    // Scrolling
                    scrollPastEnd: true,
                    fixedWidthGutter: false,
                    
                    // Font
                    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, source-code-pro, monospace',
                    
                    // Drag and drop
                    dragEnabled: true,
                    dragDelay: 0,
                    
                    // Focus
                    focusTimeout: 0,
                    tooltipFollowsMouse: false,
                    
                    // Advanced features
                    enableKeyboardAccessibility: true,
                    copyWithEmptySelection: true,
                    
                    // HTML specific
                    mode: 'ace/mode/html',
                  }}
                  editorProps={{
                    $blockScrolling: true,
                  }}
                />
              </div>
            </Panel>

            {/* Resize Handle */}
            <PanelResizeHandle className="w-2 bg-gray-200 dark:bg-background hover:bg-gray-300 dark:hover:bg-background transition-colors duration-200 cursor-col-resize flex items-center justify-center group">
              <div className="w-1 h-8 bg-gray-400 dark:bg-gray-600 rounded-full group-hover:bg-gray-500 transition-colors duration-200"></div>
            </PanelResizeHandle>

            {/* Live Preview Panel */}
            <Panel defaultSize={50} minSize={30} className="flex flex-col">
              <div className="px-3 py-2 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-700 flex items-center gap-2 dark:bg-background dark:border-gray-800 dark:text-gray-300">
                <Globe className="h-4 w-4" />
                Live Preview
              </div>
              <div className="flex-1 overflow-auto">{renderPreview}</div>
            </Panel>
          </PanelGroup>
        )}



        {viewMode === "preview" && (
          <div className="flex-1 flex flex-col">
            <div className="px-3 py-2 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-700 flex items-center gap-2 dark:bg-background dark:border-gray-800 dark:text-gray-300">
              <Globe className="h-4 w-4" />
              HTML Preview
            </div>
            <div className="flex-1 overflow-auto">{renderPreview}</div>
          </div>
        )}

        {viewMode === "code" && (
          <div className="flex-1 flex flex-col">
            <div className="px-3 py-2 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-700 dark:bg-background dark:border-gray-800 dark:text-gray-300">
              HTML Code
            </div>
            <div className="flex-1">
              <AceEditor
                ref={htmlEditorRef}
                mode="html"
                theme={theme}
                name="html-editor-code-only"
                onChange={handleHtmlChange}
                value={htmlContent}
                fontSize={fontSize}
                width="100%"
                height="100%"
                readOnly={disabled}
                placeholder="Enter HTML content..."
                setOptions={{
                  enableBasicAutocompletion: true,
                  enableLiveAutocompletion: true,
                  enableSnippets: true,
                  showLineNumbers: true,
                  tabSize: 2,
                  wrap: true,
                  showPrintMargin: false,
                  highlightActiveLine: true,
                  highlightSelectedWord: true,
                  selectionStyle: 'line',
                  wrapBehavioursEnabled: true,
                  autoScrollEditorIntoView: true,
                  behavioursEnabled: true,
                  showMatchingBrackets: true,
                  useWorker: false,
                  animatedScroll: true,
                  enableMultiselect: true,
                  showInvisibles: false,
                  displayIndentGuides: true,
                  scrollPastEnd: true,
                  fixedWidthGutter: false,
                  fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, source-code-pro, monospace',
                  dragEnabled: true,
                  dragDelay: 0,
                  focusTimeout: 0,
                  tooltipFollowsMouse: false,
                  enableKeyboardAccessibility: true,
                  copyWithEmptySelection: true,
                  mode: 'ace/mode/html',
                }}
                editorProps={{
                  $blockScrolling: true,
                }}
              />
            </div>
          </div>
        )}
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-3 py-1 bg-gray-50 dark:bg-background border-t border-gray-200 dark:border-gray-800 text-xs text-gray-600 dark:text-gray-400">
        <div className="flex items-center gap-4">
          <span>Mode: {viewMode}</span>
          <span>Theme: {theme}</span>
          <span>Font: {fontSize}px</span>
          {isUpdating && (
            <span className="inline-flex items-center gap-1 text-blue-600">
              <RefreshCw className="w-3 h-3 animate-spin" />
              Syncing...
            </span>
          )}
        </div>
        <div className="flex items-center gap-4">
          <span>HTML Code + Live Preview</span>
          <span>Ctrl+Alt+B: Format</span>
        </div>
      </div>
    </div>
  );
}
