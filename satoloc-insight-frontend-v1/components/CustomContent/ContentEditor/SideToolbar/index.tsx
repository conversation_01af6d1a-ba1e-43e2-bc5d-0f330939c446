"use client";

import React, { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { Setting<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";

const rgbToHex = (rgb: string) => {
  if (!rgb || !rgb.startsWith("rgb")) {
    return rgb;
  }
  const result = rgb.match(/\d+/g);
  if (!result) return "#000000";

  const [r, g, b] = result.map(Number);
  return (
    "#" +
    [r, g, b]
      .map((x) => {
        const hex = x.toString(16);
        return hex.length === 1 ? "0" + hex : hex;
      })
      .join("")
  );
};

export interface SelectedElementData {
  tagName: string;
  element: HTMLElement;
  properties: {
    [key: string]: string;
  };
}

interface SideToolbarProps {
  selectedElement: SelectedElementData;
  onClose: () => void;
  onUpdateProperty: (
    property: string,
    value: string,
    persist?: boolean
  ) => void;
  improveContent: (args: {
    content: string;
    element_type: string;
  }) => Promise<any>;
  onTagChange: (newTag: string) => void;
}

const SideToolbar = React.memo(
  ({
    selectedElement,
    onClose,
    onUpdateProperty,
    improveContent,
    onTagChange,
  }: SideToolbarProps) => {
    const [localProperties, setLocalProperties] = useState(
      selectedElement.properties
    );
    const [localColor, setLocalColor] = useState(
      selectedElement.properties.color
    );
    const [isImprovingContent, setIsImprovingContent] = useState(false);
    const { toast } = useToast();
    const scrollContainerRef = useRef<HTMLDivElement>(null);
    const scrollPositionRef = useRef<number>(0);

    useEffect(() => {
      setLocalProperties(selectedElement.properties);
      // eslint-disable-next-line react-hooks/exhaustive-deps
      if (selectedElement.properties.color !== localColor) {
        setLocalColor(selectedElement.properties.color);
      }
    }, [selectedElement.properties]);

    // Save scroll position before re-renders
    useEffect(() => {
      const container = scrollContainerRef.current;
      if (container) {
        const handleScroll = () => {
          scrollPositionRef.current = container.scrollTop;
        };
        container.addEventListener('scroll', handleScroll);
        return () => container.removeEventListener('scroll', handleScroll);
      }
    }, []);

    // Restore scroll position after re-renders
    useEffect(() => {
      const container = scrollContainerRef.current;
      if (container && scrollPositionRef.current > 0) {
        // Use requestAnimationFrame to ensure DOM is updated
        requestAnimationFrame(() => {
          container.scrollTop = scrollPositionRef.current;
        });
      }
    });

    const handlePropertyChange = (property: string, value: string) => {
      if (property === "color" || property === "backgroundColor") {
        setLocalColor(value);
        onUpdateProperty(property, value, false); // Live update without re-render
      } else {
        setLocalProperties((prev) => ({ ...prev, [property]: value }));
        onUpdateProperty(property, value, false);
      }
    };

    const handlePropertyBlur = (property: string, value: string) => {
      onUpdateProperty(property, value, true); // Persist on blur
    };

    const handleImproveContent = async (
      property: string,
      currentContent: string
    ) => {
      if (!currentContent.trim()) return;

      setIsImprovingContent(true);
      try {
        const elementType = selectedElement.tagName.toLowerCase();
        const response = await improveContent({
          content: currentContent.trim(),
          element_type: elementType,
        });

        if (response.improved_content) {
          handlePropertyChange(property, response.improved_content);
          onUpdateProperty(property, response.improved_content, true);
          toast({
            title: "Content Improved!",
            description: `${elementType.toUpperCase()} content has been enhanced with AI.`,
          });
        }
      } catch (error) {
        console.error("Error improving content:", error);
        toast({
          title: "Improvement Failed",
          description: "Failed to improve content. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsImprovingContent(false);
      }
    };

    return (
      <div className="w-80 bg-white dark:bg-background border-l border-gray-200 dark:border-gray-800 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-background">
          <div className="flex items-center gap-2">
            <Settings className="h-4 w-4 text-gray-600 dark:text-gray-400" />
            <span className="font-medium text-sm">
              {selectedElement.tagName.toUpperCase()} Properties
            </span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-6 w-6 p-0"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>

        {/* Properties */}
        <div ref={scrollContainerRef} className="flex-1 overflow-y-auto p-3 space-y-4">
          {["p", "h1", "h2", "h3", "h4", "h5", "h6"].includes(
            selectedElement.tagName
          ) && (
            <div className="space-y-1">
              <Label className="text-xs font-medium text-gray-700">
                Element Type
              </Label>
              <select
                value={selectedElement.tagName}
                onChange={(e) => onTagChange(e.target.value)}
                className="w-full px-2 py-1 text-xs border border-gray-300 rounded dark:bg-background dark:border-gray-600"
              >
                <option value="p">Paragraph</option>
                <option value="h1">Heading 1</option>
                <option value="h2">Heading 2</option>
                <option value="h3">Heading 3</option>
                <option value="h4">Heading 4</option>
                <option value="h5">Heading 5</option>
                <option value="h6">Heading 6</option>
              </select>
            </div>
          )}

          {/* Button-specific properties */}
          {selectedElement.tagName === "button" && (
            <>
              {/* Button URL */}
              <div className="space-y-1">
                <Label className="text-xs font-medium text-gray-700 dark:text-gray-300">
                  Button URL
                </Label>
                <Input
                  type="url"
                  value={selectedElement.element.getAttribute("data-href") || ""}
                  onChange={(e) => {
                    selectedElement.element.setAttribute("data-href", e.target.value);
                    handlePropertyChange("data-href", e.target.value);
                  }}
                  onBlur={(e) => handlePropertyBlur("data-href", e.target.value)}
                  className="text-xs dark:bg-background dark:border-gray-600"
                  placeholder="http://example.com"
                />
              </div>

              {/* Button Target */}
              <div className="space-y-1">
                <Label className="text-xs font-medium text-gray-700 dark:text-gray-300">
                  Button Target
                </Label>
                <select
                  value={selectedElement.element.getAttribute("data-target") || ""}
                  onChange={(e) => {
                    selectedElement.element.setAttribute("data-target", e.target.value);
                    handlePropertyChange("data-target", e.target.value);
                  }}
                  onBlur={(e) => handlePropertyBlur("data-target", e.target.value)}
                  className="w-full px-2 py-1 text-xs border border-gray-300 rounded dark:bg-background dark:border-gray-600"
                >
                  <option value="">Same window</option>
                  <option value="_blank">New window</option>
                  <option value="_parent">Parent frame</option>
                  <option value="_top">Top frame</option>
                </select>
              </div>

              {/* Button Size */}
              <div className="space-y-1">
                <Label className="text-xs font-medium text-gray-700 dark:text-gray-300">
                  Button Size
                </Label>
                <select
                  value={selectedElement.element.getAttribute("data-button-size") || "medium"}
                  onChange={(e) => {
                    const size = e.target.value;
                    let padding = "12px 24px";
                    let fontSize = "14px";
                    
                    switch (size) {
                      case "small":
                        padding = "8px 16px";
                        fontSize = "12px";
                        break;
                      case "large":
                        padding = "16px 32px";
                        fontSize = "16px";
                        break;
                      case "xlarge":
                        padding = "20px 40px";
                        fontSize = "18px";
                        break;
                      default:
                        padding = "12px 24px";
                        fontSize = "14px";
                    }
                    
                    selectedElement.element.setAttribute("data-button-size", size);
                    selectedElement.element.style.padding = padding;
                    selectedElement.element.style.fontSize = fontSize;
                    
                    handlePropertyChange("data-button-size", size);
                    handlePropertyChange("padding", padding);
                    handlePropertyChange("fontSize", fontSize);
                  }}
                  onBlur={(e) => handlePropertyBlur("data-button-size", e.target.value)}
                  className="w-full px-2 py-1 text-xs border border-gray-300 rounded dark:bg-background dark:border-gray-600"
                >
                  <option value="small">Small</option>
                  <option value="medium">Medium</option>
                  <option value="large">Large</option>
                  <option value="xlarge">X-Large</option>
                </select>
              </div>

              {/* Button Type */}
              <div className="space-y-1">
                <Label className="text-xs font-medium text-gray-700 dark:text-gray-300">
                  Button Type
                </Label>
                <select
                  value={selectedElement.element.getAttribute("data-button-type") || "flat"}
                  onChange={(e) => {
                    const type = e.target.value;
                    let borderRadius = "6px";
                    
                    switch (type) {
                      case "flat":
                        borderRadius = "6px";
                        break;
                      case "rounded":
                        borderRadius = "25px";
                        break;
                      case "square":
                        borderRadius = "0px";
                        break;
                    }
                    
                    selectedElement.element.setAttribute("data-button-type", type);
                    selectedElement.element.style.borderRadius = borderRadius;
                    
                    handlePropertyChange("data-button-type", type);
                    handlePropertyChange("borderRadius", borderRadius);
                  }}
                  onBlur={(e) => handlePropertyBlur("data-button-type", e.target.value)}
                  className="w-full px-2 py-1 text-xs border border-gray-300 rounded dark:bg-background dark:border-gray-600"
                >
                  <option value="flat">Flat</option>
                  <option value="rounded">Rounded</option>
                  <option value="square">Square</option>
                </select>
              </div>

              {/* Button Style */}
              <div className="space-y-1">
                <Label className="text-xs font-medium text-gray-700 dark:text-gray-300">
                  Button Style
                </Label>
                <select
                  value={selectedElement.element.getAttribute("data-button-style") || "default"}
                  onChange={(e) => {
                    const style = e.target.value;
                    let backgroundColor = "#3b82f6";
                    let color = "white";
                    let border = "none";
                    
                    switch (style) {
                      case "default":
                        backgroundColor = "#3b82f6";
                        color = "white";
                        border = "none";
                        break;
                      case "outline":
                        backgroundColor = "transparent";
                        color = "#3b82f6";
                        border = "2px solid #3b82f6";
                        break;
                      case "ghost":
                        backgroundColor = "transparent";
                        color = "#3b82f6";
                        border = "none";
                        break;
                    }
                    
                    selectedElement.element.setAttribute("data-button-style", style);
                    selectedElement.element.style.backgroundColor = backgroundColor;
                    selectedElement.element.style.color = color;
                    selectedElement.element.style.border = border;
                    
                    handlePropertyChange("data-button-style", style);
                    handlePropertyChange("backgroundColor", backgroundColor);
                    handlePropertyChange("color", color);
                    handlePropertyChange("border", border);
                  }}
                  onBlur={(e) => handlePropertyBlur("data-button-style", e.target.value)}
                  className="w-full px-2 py-1 text-xs border border-gray-300 rounded dark:bg-background dark:border-gray-600"
                >
                  <option value="default">Default</option>
                  <option value="outline">Outline</option>
                  <option value="ghost">Ghost</option>
                </select>
              </div>
            </>
          )}
          {Object.entries(localProperties).map(([key, value]) => {
            if (!value && key !== "textContent") return null;

            return (
              <div key={key} className="space-y-1">
                <Label className="text-xs font-medium text-gray-700 capitalize dark:text-gray-300">
                  {key.replace(/([A-Z])/g, " $1").toLowerCase()}
                </Label>
                {key === "textContent" ? (
                  <div className="space-y-2">
                    <textarea
                      value={localProperties[key] || ""}
                      onChange={(e) =>
                        handlePropertyChange(key, e.target.value)
                      }
                      onBlur={(e) => handlePropertyBlur(key, e.target.value)}
                      className="w-full h-[150px] px-1 text-xs border border-gray-300 rounded resize-none dark:bg-background dark:border-gray-600"
                      rows={6}
                      placeholder="Enter text content..."
                    />
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() =>
                        handleImproveContent(key, localProperties[key])
                      }
                      disabled={
                        isImprovingContent || !localProperties[key]?.trim()
                      }
                      className="w-full h-7 text-xs dark:bg-background dark:hover:bg-background dark:border-gray-600"
                    >
                      {isImprovingContent ? (
                        <>
                          <Loader2 className="h-3 w-3 animate-spin mr-1" />
                          Improving...
                        </>
                      ) : (
                        <>
                          <Sparkles className="h-3 w-3 mr-1" />
                          Improve Content
                        </>
                      )}
                    </Button>
                  </div>
                ) : key === "href" ? (
                  <Input
                    type="url"
                    value={localProperties[key] || ""}
                    onChange={(e) => handlePropertyChange(key, e.target.value)}
                    onBlur={(e) => handlePropertyBlur(key, e.target.value)}
                    className="text-xs dark:bg-background dark:border-gray-600"
                    placeholder="https://example.com"
                  />
                ) : key === "src" ? (
                  <Input
                    type="url"
                    value={localProperties[key] || ""}
                    onChange={(e) => handlePropertyChange(key, e.target.value)}
                    onBlur={(e) => handlePropertyBlur(key, e.target.value)}
                    className="text-xs dark:bg-background dark:border-gray-600"
                    placeholder="https://example.com/image.jpg"
                  />
                ) : key === "color" || key === "backgroundColor" ? (
                  <div className="flex gap-2">
                    <Input
                      type="color"
                      value={rgbToHex(localColor || "#000000")}
                      onChange={(e) =>
                        handlePropertyChange(key, e.target.value)
                      }
                      onBlur={(e) => handlePropertyBlur(key, e.target.value)}
                      className="w-12 h-8 p-0 border-0"
                    />
                    <Input
                      type="text"
                      value={localColor || ""}
                      onChange={(e) =>
                        handlePropertyChange(key, e.target.value)
                      }
                      onBlur={(e) => handlePropertyBlur(key, e.target.value)}
                      className="text-xs flex-1 dark:bg-background dark:border-gray-600"
                      placeholder="#000000 or rgb(0,0,0)"
                    />
                  </div>
                ) : key === "textAlign" ? (
                  <select
                    value={localProperties[key] || ""}
                    onChange={(e) => handlePropertyChange(key, e.target.value)}
                    onBlur={(e) => handlePropertyBlur(key, e.target.value)}
                    className="w-full px-2 py-1 text-xs border border-gray-300 rounded dark:bg-background dark:border-gray-600"
                  >
                    <option value="left">Left</option>
                    <option value="center">Center</option>
                    <option value="right">Right</option>
                    <option value="justify">Justify</option>
                  </select>
                ) : key === "fontStyle" ? (
                  <select
                    value={localProperties[key] || ""}
                    onChange={(e) => handlePropertyChange(key, e.target.value)}
                    onBlur={(e) => handlePropertyBlur(key, e.target.value)}
                    className="w-full px-2 py-1 text-xs border border-gray-300 rounded dark:bg-background dark:border-gray-600"
                  >
                    <option value="normal">Normal</option>
                    <option value="italic">Italic</option>
                  </select>
                ) : key === "textDecorationLine" ? (
                  <select
                    value={localProperties[key] || ""}
                    onChange={(e) => handlePropertyChange(key, e.target.value)}
                    onBlur={(e) => handlePropertyBlur(key, e.target.value)}
                    className="w-full px-2 py-1 text-xs border border-gray-300 rounded dark:bg-background dark:border-gray-600"
                  >
                    <option value="none">None</option>
                    <option value="underline">Underline</option>
                    <option value="line-through">Strikethrough</option>
                  </select>
                ) : key === "fontWeight" ? (
                  <select
                    value={localProperties[key] || ""}
                    onChange={(e) => handlePropertyChange(key, e.target.value)}
                    onBlur={(e) => handlePropertyBlur(key, e.target.value)}
                    className="w-full px-2 py-1 text-xs border border-gray-300 rounded dark:bg-background dark:border-gray-600"
                  >
                    <option value="normal">Normal</option>
                    <option value="bold">Bold</option>
                    <option value="lighter">Lighter</option>
                    <option value="100">100</option>
                    <option value="200">200</option>
                    <option value="300">300</option>
                    <option value="400">400</option>
                    <option value="500">500</option>
                    <option value="600">600</option>
                    <option value="700">700</option>
                    <option value="800">800</option>
                    <option value="900">900</option>
                  </select>
                ) : key === "target" ? (
                  <select
                    value={localProperties[key] || ""}
                    onChange={(e) => handlePropertyChange(key, e.target.value)}
                    onBlur={(e) => handlePropertyBlur(key, e.target.value)}
                    className="w-full px-2 py-1 text-xs border border-gray-300 rounded dark:bg-background dark:border-gray-600"
                  >
                    <option value="">Same window</option>
                    <option value="_blank">New window</option>
                    <option value="_parent">Parent frame</option>
                    <option value="_top">Top frame</option>
                  </select>
                ) : key === "width" || key === "height" ? (
                  <div className="space-y-2">
                    <div className="flex gap-2">
                      <Input
                        type="number"
                        value={localProperties[key] || ""}
                        onChange={(e) =>
                          handlePropertyChange(key, e.target.value)
                        }
                        onBlur={(e) => handlePropertyBlur(key, e.target.value)}
                        className="text-xs dark:bg-background dark:border-gray-600 flex-1"
                        placeholder={
                          key === "width" ? "Width (px)" : "Height (px)"
                        }
                        min="10"
                        max="2000"
                      />
                      <span className="text-xs text-muted-foreground self-center">
                        px
                      </span>
                    </div>
                    {key === "width" &&
                      selectedElement?.element instanceof HTMLImageElement && (
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              const img =
                                selectedElement.element as HTMLImageElement;
                              const aspectRatio =
                                img.naturalWidth / img.naturalHeight;
                              const currentWidth = parseInt(
                                localProperties.width || "0"
                              );
                              if (currentWidth > 0) {
                                const newHeight = Math.round(
                                  currentWidth / aspectRatio
                                );
                                handlePropertyChange(
                                  "height",
                                  newHeight.toString()
                                );
                                handlePropertyBlur(
                                  "height",
                                  newHeight.toString()
                                );
                              }
                            }}
                            className="text-xs h-6 px-2"
                          >
                            Lock Ratio
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              const img =
                                selectedElement.element as HTMLImageElement;
                              handlePropertyChange(
                                "width",
                                img.naturalWidth.toString()
                              );
                              handlePropertyChange(
                                "height",
                                img.naturalHeight.toString()
                              );
                              handlePropertyBlur(
                                "width",
                                img.naturalWidth.toString()
                              );
                              handlePropertyBlur(
                                "height",
                                img.naturalHeight.toString()
                              );
                            }}
                            className="text-xs h-6 px-2"
                          >
                            Original Size
                          </Button>
                        </div>
                      )}
                  </div>
                ) : (
                  <Input
                    type="text"
                    value={localProperties[key] || ""}
                    onChange={(e) => handlePropertyChange(key, e.target.value)}
                    onBlur={(e) => handlePropertyBlur(key, e.target.value)}
                    className="text-xs dark:bg-background dark:border-gray-600"
                    placeholder={`Enter ${key}...`}
                  />
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  }
);
SideToolbar.displayName = "SideToolbar";

export default SideToolbar;
