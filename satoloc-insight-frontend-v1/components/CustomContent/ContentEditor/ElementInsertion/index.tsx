"use client";

import React, { useState, useCallback, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import ElementInsertionDropdown from "./DropdownMenu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Trash2 } from "lucide-react";
import AIImageGenerator from "../AIImageGenerator";

interface ElementInsertionProps {
  editorRef: React.RefObject<HTMLDivElement>;
  onContentChange: () => void;
  disabled?: boolean;
  // Context props for AI image generation
  contentTitle?: string;
  contentText?: string;
  keywords?: string;
  tone?: string;
  originalPrompt?: string;
}

interface InsertionPoint {
  position: "before" | "after" | "beside";
  element: HTMLElement;
  rect: DOMRect;
  editorRect: DOMRect;
}

export default function ElementInsertion({
  editorRef,
  onContentChange,
  disabled = false,
  contentTitle,
  contentText,
  keywords,
  tone,
  originalPrompt,
}: ElementInsertionProps) {
  const [insertionPoints, setInsertionPoints] = useState<InsertionPoint[]>([]);
  const [activePoint, setActivePoint] = useState<InsertionPoint | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [showImageModal, setShowImageModal] = useState(false);
  const [imageUrl, setImageUrl] = useState("");
  const [imageAlt, setImageAlt] = useState("");
  const [pendingImagePoint, setPendingImagePoint] =
    useState<InsertionPoint | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [pendingDeletePoint, setPendingDeletePoint] =
    useState<InsertionPoint | null>(null);
  const [showAIImageModal, setShowAIImageModal] = useState(false);
  const [pendingAIImagePoint, setPendingAIImagePoint] =
    useState<InsertionPoint | null>(null);

  // Calculate insertion points for each element (Notion-style)
  const calculateInsertionPoints = useCallback(() => {
    if (!editorRef.current) return [];

    const editor = editorRef.current;
    const editorRect = editor.getBoundingClientRect();
    const elements = Array.from(
      editor.querySelectorAll(
        "h1, h2, h3, h4, h5, h6, p, div, ul, ol, blockquote, pre, img, button"
      )
    ).filter((el) => {
      // Filter elements
      const htmlEl = el as HTMLElement;
      // Only include top-level elements that are direct children of the editor
      const isDirectChild = htmlEl.parentElement === editor;
      const isVisible = htmlEl.offsetParent !== null;

      // Include empty divs (placeholder elements) and elements with content
      const hasContent =
        htmlEl.textContent?.trim() !== "" ||
        htmlEl.tagName.toLowerCase() === "img" ||
        htmlEl.tagName.toLowerCase() === "button" ||
        (htmlEl.tagName.toLowerCase() === "div" &&
          htmlEl.classList.contains("placeholder-element"));

      return isDirectChild && isVisible && hasContent;
    }) as HTMLElement[];

    const points: InsertionPoint[] = [];

    // Sort elements by their vertical position
    elements.sort((a, b) => {
      const aRect = a.getBoundingClientRect();
      const bRect = b.getBoundingClientRect();
      return aRect.top - bRect.top;
    });

    // Create insertion point for each element (Notion-style - left side of element)
    elements.forEach((element) => {
      const rect = element.getBoundingClientRect();
      points.push({
        position: "beside", // New position type for Notion-style
        element,
        rect,
        editorRect,
      });
    });

    return points;
  }, [editorRef]);

  // Update insertion points when editor content changes
  const updateInsertionPoints = useCallback(() => {
    if (isUpdating || isOpen || showImageModal) return; // Prevent overlapping updates and don't update while any UI is open

    setIsUpdating(true);
    // Use requestAnimationFrame to ensure layout has been updated
    requestAnimationFrame(() => {
      const points = calculateInsertionPoints();
      setInsertionPoints(points);
      setIsUpdating(false);
    });
  }, [calculateInsertionPoints, isUpdating, isOpen, showImageModal]);

  // Show image modal
  const showImageDialog = (point: InsertionPoint) => {
    setPendingImagePoint(point);
    setImageUrl("");
    setImageAlt("");
    setShowImageModal(true);
    setIsOpen(false);
    setActivePoint(null);
  };

  // Insert image element
  const insertImage = () => {
    if (!pendingImagePoint || !imageUrl.trim() || !editorRef.current) return;

    const imgElement = document.createElement("img");
    imgElement.src = imageUrl.trim();
    imgElement.alt = imageAlt.trim() || "Inserted image";
    imgElement.style.maxWidth = "100%";
    imgElement.style.height = "auto";
    imgElement.style.display = "block";
    imgElement.style.margin = "16px 0";

    // For Notion-style, always insert after the current element
    pendingImagePoint.element.parentNode?.insertBefore(
      imgElement,
      pendingImagePoint.element.nextSibling
    );

    onContentChange();
    // Delay the insertion points update to allow DOM to update
    setTimeout(() => {
      updateInsertionPoints();
    }, 50);
    setShowImageModal(false);
    setPendingImagePoint(null);
    setImageUrl("");
    setImageAlt("");
  };

  // Cancel image insertion
  const cancelImageInsertion = () => {
    setShowImageModal(false);
    setPendingImagePoint(null);
    setImageUrl("");
    setImageAlt("");
  };

  // Insert placeholder element (empty space like Notion)
  const insertPlaceholder = (point: InsertionPoint) => {
    if (!editorRef.current) return;

    const placeholderElement = document.createElement("div");
    placeholderElement.className = "placeholder-element";
    placeholderElement.contentEditable = "true";
    placeholderElement.textContent = "";
    placeholderElement.style.minHeight = "1.5em";
    placeholderElement.style.margin = "8px 0";
    placeholderElement.style.padding = "4px 0";
    placeholderElement.style.border = "1px dashed transparent";
    placeholderElement.style.borderRadius = "4px";
    placeholderElement.setAttribute(
      "data-placeholder",
      "Type '/' for commands or click + to add content..."
    );

    // Add styling for placeholder text
    placeholderElement.style.position = "relative";
    placeholderElement.style.cursor = "text";

    // Add hover effect
    placeholderElement.addEventListener("mouseenter", () => {
      placeholderElement.style.border = "1px dashed #d1d5db";
      placeholderElement.style.backgroundColor = "#f9fafb";
    });

    placeholderElement.addEventListener("mouseleave", () => {
      placeholderElement.style.border = "1px dashed transparent";
      placeholderElement.style.backgroundColor = "transparent";
    });

    // Add CSS for placeholder text using pseudo-element
    placeholderElement.style.setProperty(
      "--placeholder-text",
      `"${placeholderElement.getAttribute("data-placeholder")}"`
    );

    // Add focus behavior
    placeholderElement.addEventListener("focus", () => {
      placeholderElement.style.border = "1px solid #3b82f6";
      placeholderElement.style.backgroundColor = "transparent";
    });

    placeholderElement.addEventListener("blur", () => {
      if (!placeholderElement.textContent?.trim()) {
        placeholderElement.style.border = "1px dashed transparent";
        placeholderElement.style.backgroundColor = "transparent";
      }
    });

    // Insert after the current element (Notion behavior)
    point.element.parentNode?.insertBefore(
      placeholderElement,
      point.element.nextSibling
    );

    onContentChange();
    setIsOpen(false);
    setActivePoint(null);

    // Focus the new placeholder
    setTimeout(() => {
      updateInsertionPoints();
      placeholderElement.focus();
    }, 50);
  };

  // Insert heading element
  const insertHeading = (point: InsertionPoint, level: number) => {
    if (!editorRef.current) return;

    const headingElement = document.createElement(`h${level}`);
    headingElement.textContent = "New heading";
    headingElement.style.margin = "16px 0 8px 0";

    // For Notion-style, always insert after the current element
    point.element.parentNode?.insertBefore(
      headingElement,
      point.element.nextSibling
    );

    onContentChange();
    setIsOpen(false);
    setActivePoint(null);

    // Delay the insertion points update and focus to allow DOM to update
    setTimeout(() => {
      updateInsertionPoints();
      const range = document.createRange();
      range.selectNodeContents(headingElement);
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(range);
      }
      headingElement.focus();
    }, 50);
  };

  // Insert paragraph element
  const insertParagraph = (point: InsertionPoint) => {
    if (!editorRef.current) return;

    const pElement = document.createElement("p");
    pElement.textContent = "New paragraph";
    pElement.style.margin = "8px 0";

    // For Notion-style, always insert after the current element
    point.element.parentNode?.insertBefore(pElement, point.element.nextSibling);

    onContentChange();
    setIsOpen(false);
    setActivePoint(null);

    // Delay the insertion points update and focus to allow DOM to update
    setTimeout(() => {
      updateInsertionPoints();
      const range = document.createRange();
      range.selectNodeContents(pElement);
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(range);
      }
      pElement.focus();
    }, 50);
  };

  // Insert list element
  const insertList = (point: InsertionPoint, ordered: boolean = false) => {
    if (!editorRef.current) return;

    const listElement = document.createElement(ordered ? "ol" : "ul");
    const listItem = document.createElement("li");
    listItem.textContent = "New list item";
    listElement.appendChild(listItem);
    listElement.style.margin = "8px 0";

    // For Notion-style, always insert after the current element
    point.element.parentNode?.insertBefore(
      listElement,
      point.element.nextSibling
    );

    onContentChange();
    setIsOpen(false);
    setActivePoint(null);

    // Delay the insertion points update and focus to allow DOM to update
    setTimeout(() => {
      updateInsertionPoints();
      const range = document.createRange();
      range.selectNodeContents(listItem);
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(range);
      }
      listItem.focus();
    }, 50);
  };

  // Insert blockquote element
  const insertBlockquote = (point: InsertionPoint) => {
    if (!editorRef.current) return;

    const blockquoteElement = document.createElement("blockquote");
    blockquoteElement.textContent = "New quote";
    blockquoteElement.style.margin = "16px 0";
    blockquoteElement.style.paddingLeft = "16px";
    blockquoteElement.style.borderLeft = "4px solid #e5e7eb";
    blockquoteElement.style.fontStyle = "italic";

    // For Notion-style, always insert after the current element
    point.element.parentNode?.insertBefore(
      blockquoteElement,
      point.element.nextSibling
    );

    onContentChange();
    setIsOpen(false);
    setActivePoint(null);

    // Delay the insertion points update and focus to allow DOM to update
    setTimeout(() => {
      updateInsertionPoints();
      const range = document.createRange();
      range.selectNodeContents(blockquoteElement);
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(range);
      }
      blockquoteElement.focus();
    }, 50);
  };

  // Insert code block element
  const insertCodeBlock = (point: InsertionPoint) => {
    if (!editorRef.current) return;

    const preElement = document.createElement("pre");
    const codeElement = document.createElement("code");
    codeElement.textContent = "// New code block";
    preElement.appendChild(codeElement);
    preElement.style.margin = "16px 0";
    preElement.style.padding = "12px";
    preElement.style.backgroundColor = "#f3f4f6";
    preElement.style.borderRadius = "6px";
    preElement.style.fontFamily = "monospace";

    // For Notion-style, always insert after the current element
    point.element.parentNode?.insertBefore(
      preElement,
      point.element.nextSibling
    );

    onContentChange();
    setIsOpen(false);
    setActivePoint(null);

    // Delay the insertion points update and focus to allow DOM to update
    setTimeout(() => {
      updateInsertionPoints();
      const range = document.createRange();
      range.selectNodeContents(codeElement);
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(range);
      }
      codeElement.focus();
    }, 50);
  };

  // Insert button element
  const insertButton = (point: InsertionPoint) => {
    if (!editorRef.current) return;

    const buttonElement = document.createElement("button");
    buttonElement.textContent = "Click me";
    buttonElement.type = "button";

    // Set default attributes for SideToolbar configuration
    buttonElement.setAttribute("data-href", "");
    buttonElement.setAttribute("data-target", "");
    buttonElement.setAttribute("data-button-size", "medium");
    buttonElement.setAttribute("data-button-type", "flat");
    buttonElement.setAttribute("data-button-style", "default");

    // Default button styling
    buttonElement.style.padding = "12px 24px";
    buttonElement.style.margin = "8px 0";
    buttonElement.style.backgroundColor = "#3b82f6";
    buttonElement.style.color = "white";
    buttonElement.style.border = "none";
    buttonElement.style.borderRadius = "6px";
    buttonElement.style.cursor = "pointer";
    buttonElement.style.fontSize = "14px";
    buttonElement.style.fontWeight = "500";
    buttonElement.style.transition = "all 0.2s ease";
    buttonElement.style.display = "inline-block";
    buttonElement.style.textDecoration = "none";

    // Add hover effects
    const addHoverEffects = () => {
      buttonElement.addEventListener("mouseenter", () => {
        const currentBg = buttonElement.style.backgroundColor;
        if (currentBg === "#3b82f6" || currentBg === "rgb(59, 130, 246)") {
          buttonElement.style.backgroundColor = "#2563eb";
        }
        buttonElement.style.transform = "translateY(-1px)";
        buttonElement.style.boxShadow = "0 4px 12px rgba(0, 0, 0, 0.15)";
      });

      buttonElement.addEventListener("mouseleave", () => {
        const style =
          buttonElement.getAttribute("data-button-style") || "default";
        if (style === "default") {
          buttonElement.style.backgroundColor = "#3b82f6";
        }
        buttonElement.style.transform = "translateY(0)";
        buttonElement.style.boxShadow = "none";
      });
    };

    // Add click handler for URL navigation
    const handleClick = (e: Event) => {
      const mouseEvent = e as MouseEvent;

      // If Ctrl/Cmd is pressed or it's a selection click, don't navigate
      if (mouseEvent.ctrlKey || mouseEvent.metaKey) {
        e.preventDefault();
        e.stopPropagation();
        return;
      }

      const href = buttonElement.getAttribute("data-href");
      const target = buttonElement.getAttribute("data-target");

      if (href && href.trim()) {
        e.preventDefault();
        if (target === "_blank") {
          window.open(href, "_blank", "noopener,noreferrer");
        } else {
          window.location.href = href;
        }
      } else {
        // No URL configured - allow selection to work
        e.preventDefault();
      }
    };

    buttonElement.addEventListener("click", handleClick);
    addHoverEffects();

    // For Notion-style, always insert after the current element
    point.element.parentNode?.insertBefore(
      buttonElement,
      point.element.nextSibling
    );

    onContentChange();
    setIsOpen(false);
    setActivePoint(null);

    // Delay the insertion points update to allow DOM to update
    setTimeout(() => {
      updateInsertionPoints();
      // Focus the button for immediate editing
      buttonElement.focus();
    }, 50);
  };

  // Show delete confirmation modal
  const showDeleteDialog = (point: InsertionPoint) => {
    setPendingDeletePoint(point);
    setShowDeleteModal(true);
    setIsOpen(false);
    setActivePoint(null);
  };

  // Confirm element deletion
  const confirmDelete = () => {
    if (!pendingDeletePoint || !editorRef.current) return;

    // Remove the element from the DOM
    pendingDeletePoint.element.remove();

    // Trigger content change and update insertion points
    onContentChange();
    setShowDeleteModal(false);
    setPendingDeletePoint(null);

    // Update insertion points after DOM changes
    setTimeout(() => {
      updateInsertionPoints();
    }, 50);
  };

  // Cancel element deletion
  const cancelDelete = () => {
    setShowDeleteModal(false);
    setPendingDeletePoint(null);
  };

  // Show AI image generation modal
  const showAIImageDialog = (point: InsertionPoint) => {
    setPendingAIImagePoint(point);
    setShowAIImageModal(true);
    setIsOpen(false);
    setActivePoint(null);
  };

  // Handle AI image generation result
  const handleAIImageGenerated = (imageUrl: string, altText?: string) => {
    if (!pendingAIImagePoint || !editorRef.current) return;

    const imgElement = document.createElement("img");
    imgElement.src = imageUrl;
    imgElement.alt = altText || "AI-generated image";
    imgElement.style.maxWidth = "100%";
    imgElement.style.height = "auto";
    imgElement.style.display = "block";
    imgElement.style.margin = "16px 0";

    // For Notion-style, always insert after the current element
    pendingAIImagePoint.element.parentNode?.insertBefore(
      imgElement,
      pendingAIImagePoint.element.nextSibling
    );

    onContentChange();
    // Delay the insertion points update to allow DOM to update
    setTimeout(() => {
      updateInsertionPoints();
    }, 50);

    setShowAIImageModal(false);
    setPendingAIImagePoint(null);
  };

  // Render insertion buttons
  useEffect(() => {
    const updatePoints = () => {
      updateInsertionPoints();
    };

    // Initial calculation
    updatePoints();

    // Update points when editor content changes
    const mutationObserver = new MutationObserver(() => {
      // Use setTimeout to allow DOM to settle
      setTimeout(updatePoints, 10);
    });

    // Update points when scrolling (throttled and only when UI is closed)
    let scrollTimeout: NodeJS.Timeout;
    const handleScroll = () => {
      if (isOpen || showImageModal) return; // Don't update while UI is open
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(updatePoints, 100); // Reduced frequency
    };

    // Update points when window resizes
    let resizeTimeout: NodeJS.Timeout;
    const handleResize = () => {
      if (isOpen || showImageModal) return; // Don't update while UI is open
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(updatePoints, 200); // Debounced
    };

    // Update points when layout changes (like sidebar opening/closing)
    let layoutTimeout: NodeJS.Timeout;
    const resizeObserver = new ResizeObserver(() => {
      if (isOpen || showImageModal) return; // Don't update while UI is open
      clearTimeout(layoutTimeout);
      layoutTimeout = setTimeout(updatePoints, 300); // Debounced
    });

    if (editorRef.current) {
      // Watch for content changes
      mutationObserver.observe(editorRef.current, {
        childList: true,
        subtree: true,
        characterData: true,
      });

      // Watch for scroll events on the editor
      editorRef.current.addEventListener("scroll", handleScroll);

      // Watch for resize events on the editor
      resizeObserver.observe(editorRef.current);

      // Also watch the parent container for layout changes
      const editorParent = editorRef.current.parentElement;
      if (editorParent) {
        resizeObserver.observe(editorParent);
      }
    }

    // Watch for window events
    window.addEventListener("scroll", handleScroll);
    window.addEventListener("resize", handleResize);

    return () => {
      clearTimeout(scrollTimeout);
      clearTimeout(resizeTimeout);
      clearTimeout(layoutTimeout);
      mutationObserver.disconnect();
      resizeObserver.disconnect();
      window.removeEventListener("scroll", handleScroll);
      window.removeEventListener("resize", handleResize);

      if (editorRef.current) {
        editorRef.current.removeEventListener("scroll", handleScroll);
      }
    };
  }, [updateInsertionPoints]);

  // Recalculate when modal closes (not when it opens)
  useEffect(() => {
    if (!showImageModal) {
      // Small delay to allow modal animation to complete
      const timer = setTimeout(() => {
        updateInsertionPoints();
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [showImageModal, updateInsertionPoints]);

  if (disabled) return null;

  return (
    <>
      {insertionPoints.map((point, index) => {
        // Calculate position for Notion-style left-side placement
        const currentElementRect = point.element.getBoundingClientRect();
        const currentEditorRect = editorRef.current?.getBoundingClientRect();

        if (!currentEditorRect) return null;

        // Position on the left side of the element, vertically centered
        const yPosition =
          currentElementRect.top -
          currentEditorRect.top +
          currentElementRect.height / 2 -
          12;

        return (
          <div
            key={`${point.element.tagName}-${index}-${point.position}`}
            className="absolute flex items-center z-10 group"
            style={{
              top: `${yPosition}px`,
              left: "20px", // Centered in the 64px gutter (64/2 - 20/2 = 22px from left)
              height: "20px",
              width: "20px",
            }}
          >
            <ElementInsertionDropdown
              point={point}
              isOpen={isOpen}
              activePoint={activePoint}
              setIsOpen={setIsOpen}
              setActivePoint={setActivePoint}
              insertPlaceholder={insertPlaceholder}
              insertParagraph={insertParagraph}
              insertHeading={insertHeading}
              insertList={insertList}
              insertBlockquote={insertBlockquote}
              insertCodeBlock={insertCodeBlock}
              insertButton={insertButton}
              showImageDialog={showImageDialog}
              showAIImageDialog={showAIImageDialog}
              showDeleteDialog={showDeleteDialog}
            />
          </div>
        );
      })}

      {/* Image Insertion Modal */}
      <Dialog open={showImageModal} onOpenChange={setShowImageModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Insert Image</DialogTitle>
            <DialogDescription>
              Add an image to your content. Enter the image URL and optional alt
              text.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="imageUrl">Image URL *</Label>
              <Input
                id="imageUrl"
                placeholder="https://example.com/image.jpg"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter" && imageUrl.trim()) {
                    insertImage();
                  }
                }}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="imageAlt">Alt Text (optional)</Label>
              <Input
                id="imageAlt"
                placeholder="Description of the image"
                value={imageAlt}
                onChange={(e) => setImageAlt(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter" && imageUrl.trim()) {
                    insertImage();
                  }
                }}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={cancelImageInsertion}>
              Cancel
            </Button>
            <Button onClick={insertImage} disabled={!imageUrl.trim()}>
              Insert Image
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <Dialog open={showDeleteModal} onOpenChange={setShowDeleteModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Element</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this{" "}
              <span className="font-medium">
                {pendingDeletePoint?.element.tagName.toLowerCase()}
              </span>{" "}
              element? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button variant="outline" onClick={cancelDelete}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Element
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* AI Image Generator Modal */}
      <AIImageGenerator
        open={showAIImageModal}
        onOpenChange={setShowAIImageModal}
        onImageGenerated={handleAIImageGenerated}
        initialPrompt=""
        contentTitle={contentTitle}
        contentText={contentText}
        keywords={keywords}
        tone={tone}
        originalPrompt={originalPrompt}
      />
    </>
  );
}
