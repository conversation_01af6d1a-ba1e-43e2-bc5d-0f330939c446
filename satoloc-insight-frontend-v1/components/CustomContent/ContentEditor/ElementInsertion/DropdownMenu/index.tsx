import React from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu as UIDropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import {
  Plus,
  Image,
  Type,
  Heading1,
  Heading2,
  Heading3,
  List,
  ListOrdered,
  Quote,
  Code,
  Trash2,
  <PERSON><PERSON><PERSON>,
  <PERSON>Pointer,
} from "lucide-react";

// Import InsertionPoint type from parent component or create a shared types file
interface InsertionPoint {
  position: "before" | "after" | "beside";
  element: HTMLElement;
  rect: DOMRect;
  editorRect: DOMRect;
}

interface ElementInsertionDropdownProps {
  point: InsertionPoint;
  isOpen: boolean;
  activePoint: InsertionPoint | null;
  setIsOpen: (open: boolean) => void;
  setActivePoint: (point: InsertionPoint | null) => void;
  insertPlaceholder: (point: InsertionPoint) => void;
  insertParagraph: (point: InsertionPoint) => void;
  insertHeading: (point: InsertionPoint, level: number) => void;
  insertList: (point: InsertionPoint, ordered: boolean) => void;
  insertBlockquote: (point: InsertionPoint) => void;
  insertCodeBlock: (point: InsertionPoint) => void;
  insertButton: (point: InsertionPoint) => void;
  showImageDialog: (point: InsertionPoint) => void;
  showAIImageDialog: (point: InsertionPoint) => void;
  showDeleteDialog: (point: InsertionPoint) => void;
}

export default function ElementInsertionDropdown({
  point,
  isOpen,
  activePoint,
  setIsOpen,
  setActivePoint,
  insertPlaceholder,
  insertParagraph,
  insertHeading,
  insertList,
  insertBlockquote,
  insertCodeBlock,
  insertButton,
  showImageDialog,
  showAIImageDialog,
  showDeleteDialog,
}: ElementInsertionDropdownProps) {
  return (
    <UIDropdownMenu
      open={isOpen && activePoint === point}
      onOpenChange={(open) => {
        setIsOpen(open);
        if (!open) setActivePoint(null);
      }}
    >
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={`
            h-5 w-5 p-0 rounded-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600
            hover:border-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700
            transition-all duration-200 shadow-sm
            opacity-100 scale-100
          `}
          onClick={() => {
            setActivePoint(point);
            setIsOpen(true);
          }}
        >
          <Plus className="h-3 w-3 text-gray-500" />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="start" className="w-48">
        <DropdownMenuLabel>Add Content</DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuItem onClick={() => insertPlaceholder(point)}>
          <Plus className="mr-2 h-4 w-4" />
          Empty Space
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem onClick={() => insertParagraph(point)}>
          <Type className="mr-2 h-4 w-4" />
          Paragraph
        </DropdownMenuItem>

        <DropdownMenuItem onClick={() => insertHeading(point, 1)}>
          <Heading1 className="mr-2 h-4 w-4" />
          Heading 1
        </DropdownMenuItem>

        <DropdownMenuItem onClick={() => insertHeading(point, 2)}>
          <Heading2 className="mr-2 h-4 w-4" />
          Heading 2
        </DropdownMenuItem>

        <DropdownMenuItem onClick={() => insertHeading(point, 3)}>
          <Heading3 className="mr-2 h-4 w-4" />
          Heading 3
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem onClick={() => insertList(point, false)}>
          <List className="mr-2 h-4 w-4" />
          Bullet List
        </DropdownMenuItem>

        <DropdownMenuItem onClick={() => insertList(point, true)}>
          <ListOrdered className="mr-2 h-4 w-4" />
          Numbered List
        </DropdownMenuItem>

        <DropdownMenuItem onClick={() => insertBlockquote(point)}>
          <Quote className="mr-2 h-4 w-4" />
          Blockquote
        </DropdownMenuItem>

        <DropdownMenuItem onClick={() => insertCodeBlock(point)}>
          <Code className="mr-2 h-4 w-4" />
          Code Block
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuLabel>Interactive Elements</DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuItem onClick={() => insertButton(point)}>
          <MousePointer className="mr-2 h-4 w-4" />
          Button
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem onClick={() => showImageDialog(point)}>
          <Image className="mr-2 h-4 w-4" />
          Image URL
        </DropdownMenuItem>

        <DropdownMenuItem onClick={() => showAIImageDialog(point)}>
          <Sparkles className="mr-2 h-4 w-4" />
          AI Image
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem
          onClick={() => showDeleteDialog(point)}
          className="text-red-600 dark:text-red-400 focus:text-red-600 dark:focus:text-red-400"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Delete Element
        </DropdownMenuItem>
      </DropdownMenuContent>
    </UIDropdownMenu>
  );
}
