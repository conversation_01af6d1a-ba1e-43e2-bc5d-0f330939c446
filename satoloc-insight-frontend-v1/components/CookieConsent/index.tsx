"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>, Settings, Shield } from "lucide-react";
import Link from "next/link";

type CookiePreferences = {
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
  preferences: boolean;
};

export const CookieConsent = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [preferences, setPreferences] = useState<CookiePreferences>({
    necessary: true,
    analytics: false,
    marketing: false,
    preferences: false,
  });

  useEffect(() => {
    // Check if user has already made a choice
    const cookieConsent = localStorage.getItem("cookie-consent");
    if (!cookieConsent) {
      // Show the cookie consent after a short delay
      const timer = setTimeout(() => {
        setIsOpen(true);
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, []);

  const handleAcceptAll = () => {
    setPreferences({
      necessary: true,
      analytics: true,
      marketing: true,
      preferences: true,
    });
    localStorage.setItem("cookie-consent", "all");
    setIsOpen(false);
  };

  const handleAcceptNecessary = () => {
    localStorage.setItem("cookie-consent", "necessary");
    setIsOpen(false);
  };

  const handleSavePreferences = () => {
    localStorage.setItem("cookie-consent", JSON.stringify(preferences));
    setIsOpen(false);
  };

  const togglePreference = (key: keyof CookiePreferences) => {
    if (key === "necessary") return; // Necessary cookies can't be toggled
    setPreferences((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  return (
    <>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: 100, opacity: 0 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className="fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:bottom-4 md:max-w-md z-50"
          >
            <div className="relative bg-background backdrop-blur-md border border-[#1279b4] rounded-xl shadow-lg overflow-hidden">
              {/* Gradient border effect */}
              <div className="absolute inset-0 rounded-xl overflow-hidden pointer-events-none">
                <div className="absolute inset-0 bg-background" />
              </div>

              {/* Content */}
              <div className="relative p-6">
                {/* Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <Cookie className="h-5 w-5 text-[#1279b4]" />
                    <h3 className="text-lg font-semibold text-[#1279b4]">
                      Cookie Preferences
                    </h3>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setIsOpen(false)}
                    className="h-8 w-8 rounded-full hover:bg-primary/10"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                {/* Main content */}
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    We use cookies to enhance your experience, analyze site
                    traffic, and for marketing purposes. By clicking "Accept
                    All", you consent to our use of cookies.
                  </p>

                  {/* Detailed preferences */}
                  <AnimatePresence>
                    {showDetails && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="overflow-hidden"
                      >
                        <div className="space-y-3 pt-2 pb-4">
                          {[
                            {
                              id: "necessary",
                              label: "Necessary",
                              description:
                                "Essential cookies that enable basic functionality.",
                              icon: (
                                <Shield className="h-4 w-4 text-[#1279b4]" />
                              ),
                            },
                            {
                              id: "analytics",
                              label: "Analytics",
                              description:
                                "Cookies that help us analyze how our website is used.",
                              icon: (
                                <Settings className="h-4 w-4 text-[#1279b4]" />
                              ),
                            },
                            {
                              id: "marketing",
                              label: "Marketing",
                              description:
                                "Cookies used for marketing and retargeting purposes.",
                              icon: (
                                <Cookie className="h-4 w-4 text-[#1279b4]" />
                              ),
                            },
                            {
                              id: "preferences",
                              label: "Preferences",
                              description:
                                "Cookies that remember your settings and preferences.",
                              icon: (
                                <Settings className="h-4 w-4 text-[#1279b4]" />
                              ),
                            },
                          ].map((item) => (
                            <div
                              key={item.id}
                              className="flex items-start gap-3"
                            >
                              <div className="pt-0.5">{item.icon}</div>
                              <div className="flex-1">
                                <div className="flex items-center justify-between">
                                  <p className="text-sm font-medium">
                                    {item.label}
                                  </p>
                                  <div
                                    onClick={() =>
                                      togglePreference(
                                        item.id as keyof CookiePreferences
                                      )
                                    }
                                    className={`relative inline-flex h-5 w-9 flex-shrink-0 cursor-pointer rounded-full transition-colors duration-200 ease-in-out ${
                                      item.id === "necessary"
                                        ? "bg-primary/70"
                                        : preferences[
                                              item.id as keyof CookiePreferences
                                            ]
                                          ? "bg-primary"
                                          : "bg-primary/20"
                                    }`}
                                  >
                                    <span
                                      className={`pointer-events-none inline-block h-4 w-4 transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out ${
                                        preferences[
                                          item.id as keyof CookiePreferences
                                        ]
                                          ? "translate-x-4"
                                          : "translate-x-0.5"
                                      }`}
                                      style={{ margin: "2px 0" }}
                                    />
                                  </div>
                                </div>
                                <p className="text-xs text-muted-foreground mt-0.5">
                                  {item.description}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                        <div className="border-t border-primary/10 pt-4">
                          <p className="text-xs text-muted-foreground mb-2">
                            For more information, please read our{" "}
                            <Link
                              href="/cookies"
                              className="text-[#1279b4] hover:underline"
                            >
                              Cookie Policy
                            </Link>
                            .
                          </p>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>

                  {/* Actions */}
                  <div className="flex flex-wrap gap-2 justify-end">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowDetails(!showDetails)}
                      className="text-xs"
                    >
                      {showDetails ? "Hide Preferences" : "Customize"}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleAcceptNecessary}
                      className="text-xs"
                    >
                      Necessary Only
                    </Button>
                    {showDetails ? (
                      <Button
                        size="sm"
                        onClick={handleSavePreferences}
                        className="text-xs bg-[#1279b4] hover:bg-[#1279b4]/90"
                      >
                        Save Preferences
                      </Button>
                    ) : (
                      <Button
                        size="sm"
                        onClick={handleAcceptAll}
                        className="text-xs bg-[#1279b4] hover:bg-[#1279b4]/90"
                      >
                        Accept All
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Floating button to reopen cookie settings */}
      {!isOpen && (
        <motion.button
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          whileHover={{ scale: 1.05 }}
          onClick={() => setIsOpen(true)}
          className="fixed bottom-4 right-4 p-2 bg-background/95 backdrop-blur-md border border-[#1279b4]/10 rounded-full shadow-lg z-50 hover:bg-[#1279b4]/10 transition-colors"
        >
          <Cookie className="h-5 w-5 text-[#1279b4]" />
        </motion.button>
      )}
    </>
  );
};

export default CookieConsent;
