import { LogIn } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface SessionExpiredModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
}

export function SessionExpiredModal({
  isOpen,
  onClose,
  onConfirm,
}: SessionExpiredModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-[#1279b4]">Session Expired</DialogTitle>
          <DialogDescription className="">
            Your session has expired. Please log in again to continue using the
            platform.
          </DialogDescription>
        </DialogHeader>
        <div className="flex flex-col gap-4">
          <Button onClick={onConfirm} className="w-full bg-[#1279b4]">
            <LogIn className="mr-2 h-4 w-4" />
            Log In Again
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
