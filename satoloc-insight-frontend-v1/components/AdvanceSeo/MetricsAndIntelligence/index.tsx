import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>gle,
  <PERSON>Up<PERSON><PERSON>,
  ChevronRight,
} from "lucide-react";
import Link from "next/link";

// Define interfaces for competitor data
interface Competitor {
  name: string;
  url: string;
  description: string;
  percentage: string;
  keywords: string[];
  rank: number;
  strategy_gaps?: { id: number; text: string }[];
  growth_opportunities?: { id: number; text: string }[];
  ranking_issues?: { id: number; title: string; description: string }[];
  content_recommendations?: {
    id: number;
    title: string;
    description: string;
    impact: string;
    estimated_hours: number;
    is_opportunity: boolean;
  }[];
}

// Define interface for content opportunities
interface ContentOpportunity {
  title: string;
  demand: string;
  source: string;
}

interface MetricsAndIntelligenceProps {
  isWebsiteLoading: boolean;
  isUserWebsiteLoading: boolean;
  hasMetrics: boolean;
  metricsToDisplay: any[];
  topCompetitors: Competitor[];
  currentWebsite: any;
  regionalContentOpportunities: ContentOpportunity[];
  openStrategyModal: (competitor: Competitor) => void;
}

export default function MetricsAndIntelligence({
  isWebsiteLoading,
  isUserWebsiteLoading,
  hasMetrics,
  metricsToDisplay,
  topCompetitors,
  currentWebsite,
  regionalContentOpportunities,
  openStrategyModal,
}: MetricsAndIntelligenceProps) {
  if (isWebsiteLoading || isUserWebsiteLoading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
        <p className="mt-4 text-muted-foreground">Loading SEO metrics...</p>
      </div>
    );
  }

  if (!hasMetrics) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">
          No SEO metrics available. Please analyze a website to see metrics.
        </p>
      </div>
    );
  }

  return (
    <Card className="space-y-4 p-4 shadow-none">
      {/* SEO Metrics Grid */}
      <div className="grid grid-cols-2 md:grid-cols-6 gap-2 mb-4">
        {metricsToDisplay.map((metric, index) => (
          <Card key={index} className="shadow-none rounded-md">
            <CardContent className="p-2 text-center">
              <div className="text-xs text-gray-600">{metric.title}</div>
              <div className="text-base font-bold my-1">{metric.value}</div>
              {metric.trend && (
                <div
                  className={`text-xs flex items-center ${metric.trendPositive ? "text-green-600" : "text-red-600"}`}
                >
                  <ArrowUp
                    className={`h-3 w-3 mr-1 ${!metric.trendPositive && "transform rotate-180"}`}
                  />
                  {metric.trend}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Intelligence and Opportunities Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        {/* AI Competitive Intelligence Card */}
        <Card className="shadow-none rounded-md">
          <CardContent className="p-4">
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center">
                <span className="text-lg font-semibold mr-2">
                  AI Competitive Intelligence
                </span>
              </div>
            </div>

            {topCompetitors.length > 0 ? (
              <div className="grid grid-cols-2 gap-4 mb-4">
                {topCompetitors.map((competitor: Competitor, index: number) => (
                  <div key={index} className="border rounded p-3">
                    <div className="font-medium">{competitor.name}</div>
                    <div className="text-sm text-gray-600 mb-2">
                      {competitor.description}
                    </div>
                    <div className="flex justify-between items-center">
                      <div
                        className={`font-medium ${parseFloat(competitor.percentage) >= 0 ? "text-green-600" : "text-red-600"}`}
                      >
                        {competitor.percentage}
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => openStrategyModal(competitor)}
                          className="text-xs text-indigo-600 flex items-center hover:underline cursor-pointer"
                        >
                          View Strategy <ChevronRight size={14} />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="border rounded p-6 text-center mb-4">
                <p className="text-gray-500 dark:text-gray-400 mb-2">
                  No competitor data available
                </p>
                <p className="text-sm text-gray-400 dark:text-gray-500">
                  Submit a website for analysis to see your top competitors
                </p>
              </div>
            )}

            {/* AI Strategy Recommendations */}
            <div className="border rounded p-3 max-h-[400px] overflow-y-auto">
              <div className="font-medium mb-2">
                AI Strategy Recommendations
              </div>
              <div className="text-sm font-semibold mb-2">
                Critical Strategy Gaps
              </div>
              {currentWebsite?.strategy_gaps &&
              currentWebsite.strategy_gaps.length > 0 ? (
                currentWebsite.strategy_gaps.map(
                  (gap: { id: number; text: string }, index: number) => (
                    <div key={index} className="flex items-start mb-2">
                      <AlertTriangle
                        size={16}
                        className="text-amber-500 mr-2 mt-0.5"
                      />
                      <div className="text-sm">{gap.text}</div>
                    </div>
                  )
                )
              ) : (
                <div className="text-sm text-gray-500 italic">
                  No strategy gaps found
                </div>
              )}

              <div className="text-sm font-semibold mt-4 mb-2">
                Growth Opportunities
              </div>
              {currentWebsite?.growth_opportunities &&
              currentWebsite.growth_opportunities.length > 0 ? (
                currentWebsite.growth_opportunities.map(
                  (
                    opportunity: { id: number; text: string },
                    index: number
                  ) => (
                    <div key={index} className="flex items-start mb-2">
                      <ArrowUpRight
                        size={16}
                        className="text-green-600 mr-2 mt-0.5"
                      />
                      <div className="text-sm">{opportunity.text}</div>
                    </div>
                  )
                )
              ) : (
                <div className="text-sm text-gray-500 italic">
                  No growth opportunities found
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Regional Content Opportunities Card */}
        <Card className="shadow-none rounded-md">
          <CardContent className="p-4">
            <div className="flex justify-between items-center mb-4">
              <div className="text-lg font-semibold">
                Regional Content Opportunities
              </div>
            </div>

            {regionalContentOpportunities.length > 0 ? (
              <div className="space-y-4">
                {regionalContentOpportunities.map((content, index) => (
                  <div key={index} className="border rounded p-3">
                    <div className="font-medium">{content.title}</div>
                    <div className="text-sm text-gray-600 mb-1">
                      {content.demand}
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="text-xs text-gray-500">
                        From: {content.source}
                      </div>
                      <Link
                        href={`/ai-content?keyword=${encodeURIComponent(content.title)}`}
                        className="text-xs bg-amber-100 text-amber-800 py-1 px-2 rounded flex items-center hover:bg-amber-200 transition-colors"
                      >
                        Generate Content <ChevronRight size={14} />
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="border rounded p-6 text-center">
                <p className="text-gray-500 dark:text-gray-400 mb-2">
                  No content opportunities available
                </p>
                <p className="text-sm text-gray-400 dark:text-gray-500">
                  Submit a website for analysis to see regional content
                  opportunities
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </Card>
  );
}
