import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Activity,
  Smartphone,
  Monitor,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Zap,
  Clock,
  Eye,
  AlertTriangle,
  CheckCircle,
  XCircle,
  ChevronRight,
  BarChart3,
  Info,
} from "lucide-react";
import {
  useCoreWebVitals,
  useAnalyzeCoreWebVitalsMultiStrategy,
} from "@/internal-api/seo/advance-seo";
import { useToast } from "@/hooks/use-toast";
import SEOAIAssistant from "./SEOAIAssistant";

// Types for Core Web Vitals data
interface CoreWebVitalMetric {
  field_value: number | null;
  field_category: "GOOD" | "NEEDS_IMPROVEMENT" | "POOR" | null;
  lab_value: number | null;
  lab_score: number | null;
  assessment: "PASS" | "AVERAGE" | "FAIL" | null;
}

interface CoreWebVitalsData {
  url: string;
  strategy: "mobile" | "desktop";
  timestamp: string;
  lighthouse_version: string;
  performance_score: number;
  core_web_vitals: {
    lcp: CoreWebVitalMetric;
    fid: CoreWebVitalMetric;
    cls: CoreWebVitalMetric;
    fcp: CoreWebVitalMetric;
  };
  categories: {
    performance: { score: number; title: string };
    accessibility: { score: number; title: string };
    "best-practices": { score: number; title: string };
    seo: { score: number; title: string };
  };
  lab_data: {
    speed_index: { score: number; numeric_value: number };
    tti: { score: number; numeric_value: number };
    tbt: { score: number; numeric_value: number };
  };
  opportunities: Array<{
    opportunity_id: string;
    title: string;
    description: string;
    score: number;
    display_value: string;
    overall_savings_ms: number;
    overall_savings_bytes: number;
  }>;
  diagnostics: Array<{
    diagnostic_id: string;
    title: string;
    description: string;
    score: number;
    display_value: string;
  }>;
}

interface CoreWebVitalsProps {
  websiteUrl: string;
}

const CoreWebVitals: React.FC<CoreWebVitalsProps> = ({ websiteUrl }) => {
  const [activeStrategy, setActiveStrategy] = useState<"mobile" | "desktop">(
    "mobile"
  );
  const [isCollapsed, setIsCollapsed] = useState<boolean>(false);
  const { toast } = useToast();

  // Fetch existing Core Web Vitals data
  const {
    data: mobileData,
    isLoading: isMobileLoading,
    refetch: refetchMobile,
  } = useCoreWebVitals(websiteUrl || "", "mobile", {
    enabled: !!websiteUrl,
  });

  const {
    data: desktopData,
    isLoading: isDesktopLoading,
    refetch: refetchDesktop,
  } = useCoreWebVitals(websiteUrl || "", "desktop", {
    enabled: !!websiteUrl,
  });

  // Mutation for analyzing both strategies
  const analyzeMultiStrategy = useAnalyzeCoreWebVitalsMultiStrategy({
    onSuccess: (data: any) => {
      toast({
        title: "Analysis Complete",
        description:
          "Core Web Vitals analysis has been updated for both mobile and desktop.",
      });
      // Refetch data to get the latest results
      refetchMobile();
      refetchDesktop();
    },
    onError: (error: any) => {
      toast({
        title: "Analysis Failed",
        description:
          error.message ||
          "Failed to analyze Core Web Vitals. Please try again.",
        variant: "destructive",
      });
    },
  });

  const analyzeAllStrategies = async () => {
    if (!websiteUrl) {
      toast({
        title: "No URL provided",
        description: "Please select a website to analyze.",
        variant: "destructive",
      });
      return;
    }

    analyzeMultiStrategy.mutate({ url: websiteUrl });
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600";
    if (score >= 50) return "text-amber-600";
    return "text-red-600";
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 90) return "bg-green-100";
    if (score >= 50) return "bg-amber-100";
    return "bg-red-100";
  };

  const getCategoryIcon = (
    category: "GOOD" | "NEEDS_IMPROVEMENT" | "POOR" | null
  ) => {
    switch (category) {
      case "GOOD":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "NEEDS_IMPROVEMENT":
        return <AlertTriangle className="h-4 w-4 text-amber-600" />;
      case "POOR":
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-400" />;
    }
  };

  const formatTime = (value: number | null, unit: string = "s") => {
    if (value === null) return "N/A";
    if (unit === "s") return `${value.toFixed(1)}s`;
    if (unit === "ms") return `${Math.round(value)}ms`;
    return value.toString();
  };

  // Get current data based on active strategy
  const rawData = activeStrategy === "mobile" ? mobileData : desktopData;
  const currentData = rawData?.data || rawData; // Handle both wrapped and direct data formats
  const isLoading =
    isMobileLoading || isDesktopLoading || analyzeMultiStrategy.isLoading;

  // Debug logging to help troubleshoot data structure
  React.useEffect(() => {
    if (rawData) {
    }
  }, [rawData, currentData, activeStrategy]);

  return (
    <TooltipProvider>
      <div className="relative">
        <Card className="shadow-sm rounded-md">
          <CardContent className="p-4">
            {/* Compact Header */}
            <div
              className="flex justify-between items-center cursor-pointer hover:bg-gray-50 dark:hover:bg-black/50 p-2 rounded-lg -m-2 mb-4"
              onClick={() => setIsCollapsed(!isCollapsed)}
            >
              <div className="flex items-center space-x-3">
                <div className="p-1.5 bg-blue-100 rounded-lg">
                  <Activity className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-base font-semibold flex items-center space-x-2">
                    <span>Core Web Vitals</span>
                    {currentData && (
                      <Badge
                        variant={
                          currentData?.performance_score >= 90
                            ? "default"
                            : currentData?.performance_score >= 50
                              ? "secondary"
                              : "destructive"
                        }
                        className="text-xs"
                      >
                        {currentData?.performance_score || 0}
                      </Badge>
                    )}
                  </h3>
                  <p className="text-xs text-gray-600">
                    Real user experience metrics •{" "}
                    {activeStrategy === "mobile" ? "📱" : "💻"} {activeStrategy}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    analyzeAllStrategies();
                  }}
                  disabled={isLoading || !websiteUrl}
                  className="flex items-center space-x-1 h-8"
                >
                  <RefreshCw
                    className={`h-3 w-3 ${isLoading ? "animate-spin" : ""}`}
                  />
                  <span className="text-xs">Analyze</span>
                </Button>
                <ChevronRight
                  className={`h-4 w-4 text-gray-400 transition-transform ${isCollapsed ? "rotate-90" : "rotate-0"}`}
                />
              </div>
            </div>

            {/* Collapsible Content */}
            {!isCollapsed && (
              <div className="space-y-4 animate-in slide-in-from-top-2 duration-200">
                {/* Compact Strategy Tabs */}
                <Tabs
                  value={activeStrategy}
                  onValueChange={(value) =>
                    setActiveStrategy(value as "mobile" | "desktop")
                  }
                >
                  <TabsList className="grid w-full grid-cols-2 h-8">
                    <TabsTrigger
                      value="mobile"
                      className="flex items-center space-x-1 text-xs"
                    >
                      <Smartphone className="h-3 w-3" />
                      <span>Mobile</span>
                    </TabsTrigger>
                    <TabsTrigger
                      value="desktop"
                      className="flex items-center space-x-1 text-xs"
                    >
                      <Monitor className="h-3 w-3" />
                      <span>Desktop</span>
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent
                    value={activeStrategy}
                    className="mt-4 space-y-4"
                  >
                    {isLoading ? (
                      <div className="text-center py-8">
                        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
                        <p className="mt-4 text-gray-600">
                          Analyzing Core Web Vitals...
                        </p>
                      </div>
                    ) : !currentData ? (
                      <div className="text-center py-6">
                        <Activity className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-600 mb-1">
                          No Core Web Vitals data available
                        </p>
                        <p className="text-xs text-gray-500">
                          Click "Analyze" to get Core Web Vitals metrics for
                          this website
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {/* Compact Core Web Vitals Grid */}
                        <div className="grid grid-cols-2 lg:grid-cols-5 gap-3">
                          {/* LCP */}
                          <div className="bg-gray-50 dark:bg-black/50 rounded-lg p-3">
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-xs font-medium text-foreground dark:text-white">
                                LCP
                              </span>
                              {getCategoryIcon(
                                currentData?.core_web_vitals?.lcp
                                  ?.field_category
                              )}
                            </div>
                            <div className="text-sm font-semibold">
                              {formatTime(
                                currentData?.core_web_vitals?.lcp?.lab_score
                              )}
                            </div>
                            <div className="text-xs text-foreground dark:text-white">
                              Largest Paint
                            </div>
                          </div>

                          {/* FID */}
                          <div className="bg-gray-50 dark:bg-black/50 rounded-lg p-3">
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-xs font-medium text-foreground dark:text-white">
                                FID
                              </span>
                              {getCategoryIcon(
                                currentData?.core_web_vitals?.fid
                                  ?.field_category
                              )}
                            </div>
                            <div className="text-sm font-semibold">
                              {formatTime(
                                currentData?.core_web_vitals?.fid?.lab_score
                              )}
                            </div>
                            <div className="text-xs text-foreground">
                              Input Delay
                            </div>
                          </div>

                          {/* CLS */}
                          <div className="bg-gray-50 dark:bg-black/50 rounded-lg p-3">
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-xs font-medium text-foreground dark:text-white">
                                CLS
                              </span>
                              {getCategoryIcon(
                                currentData?.core_web_vitals?.cls
                                  ?.field_category
                              )}
                            </div>
                            <div className="text-sm font-semibold">
                              {formatTime(
                                currentData?.core_web_vitals?.cls?.lab_score
                              )}
                            </div>
                            <div className="text-xs text-foreground">
                              Layout Shift
                            </div>
                          </div>

                          {/* FCP */}
                          <div className="bg-gray-50 dark:bg-black/50 rounded-lg p-3">
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-xs font-medium text-foreground dark:text-white">
                                FCP
                              </span>
                              {getCategoryIcon(
                                currentData?.core_web_vitals?.fcp
                                  ?.field_category
                              )}
                            </div>
                            <div className="text-sm font-semibold">
                              {formatTime(
                                currentData?.core_web_vitals?.fcp?.lab_score
                              )}
                            </div>
                            <div className="text-xs text-foreground">
                              First Paint
                            </div>
                          </div>

                          {/* Performance Score */}
                          <div className="bg-blue-50 dark:bg-black/50 rounded-lg p-3 text-center">
                            <div className="text-xs text-foreground mb-1">
                              Performance
                            </div>
                            <div
                              className={`inline-flex items-center justify-center w-10 h-10 rounded-full text-sm font-bold ${getScoreBgColor(currentData?.performance_score || 0)} ${getScoreColor(currentData?.performance_score || 0)}`}
                            >
                              {currentData?.performance_score || 0}
                            </div>
                          </div>
                        </div>

                        {/* Compact Categories */}
                        <div className="flex justify-between bg-gray-50 dark:bg-black/50 rounded-lg p-3">
                          {currentData?.categories &&
                            Object.entries(currentData.categories).map(
                              ([key, category]: [string, any]) => (
                                <div key={key} className="text-center">
                                  <div
                                    className={`inline-flex items-center justify-center w-8 h-8 rounded-full text-xs font-bold ${getScoreBgColor(category?.score || 0)} ${getScoreColor(category?.score || 0)}`}
                                  >
                                    {category?.score || 0}
                                  </div>
                                  <p className="mt-1 text-xs text-foreground">
                                    {category?.title || key}
                                  </p>
                                </div>
                              )
                            )}
                        </div>

                        {/* Compact Opportunities & Diagnostics */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                          {/* Opportunities */}
                          {currentData?.opportunities &&
                            currentData.opportunities.length > 0 && (
                              <div className="bg-green-50 dark:bg-black/50 rounded-lg p-3">
                                <h4 className="text-sm font-semibold mb-2 flex items-center space-x-1">
                                  <TrendingUp className="h-3 w-3 text-green-600" />
                                  <span>Top Opportunities</span>
                                  <span className="text-foreground text-xs">
                                    (hover for details)
                                  </span>
                                </h4>
                                <div className="space-y-2">
                                  {currentData?.opportunities
                                    ?.slice(0, 3)
                                    .map((opportunity: any, index: number) => (
                                      <Tooltip key={index}>
                                        <TooltipTrigger asChild>
                                          <div className="text-xs cursor-help hover:bg-green-100 p-2 rounded transition-colors">
                                            <div className="flex items-center justify-between">
                                              <div className="font-medium text-green-700 flex-1 pr-2">
                                                {opportunity.title}
                                              </div>
                                              <Info className="h-3 w-3 text-green-600 flex-shrink-0" />
                                            </div>
                                            <div className="text-green-600 font-semibold">
                                              {opportunity.display_value}
                                            </div>
                                            {opportunity.overall_savings_ms >
                                              0 && (
                                              <div className="text-green-500 text-xs">
                                                Save:{" "}
                                                {Math.round(
                                                  opportunity.overall_savings_ms
                                                )}
                                                ms
                                              </div>
                                            )}
                                          </div>
                                        </TooltipTrigger>
                                        <TooltipContent className="max-w-sm">
                                          <div className="space-y-2">
                                            <div className="font-semibold text-sm">
                                              {opportunity.title}
                                            </div>
                                            <div className="text-sm">
                                              {opportunity.description}
                                            </div>
                                            <div className="flex gap-4 text-xs text-foreground">
                                              {opportunity.overall_savings_ms >
                                                0 && (
                                                <span>
                                                  Time savings:{" "}
                                                  {Math.round(
                                                    opportunity.overall_savings_ms
                                                  )}
                                                  ms
                                                </span>
                                              )}
                                              {opportunity.overall_savings_bytes >
                                                0 && (
                                                <span>
                                                  Size savings:{" "}
                                                  {Math.round(
                                                    opportunity.overall_savings_bytes /
                                                      1024
                                                  )}
                                                  KB
                                                </span>
                                              )}
                                            </div>
                                            <div className="text-xs bg-green-50 p-2 rounded text-green-600">
                                              <strong>Impact:</strong>{" "}
                                              {opportunity.display_value}
                                            </div>
                                          </div>
                                        </TooltipContent>
                                      </Tooltip>
                                    ))}
                                </div>
                              </div>
                            )}

                          {/* Diagnostics */}
                          {currentData?.diagnostics &&
                            currentData.diagnostics.length > 0 && (
                              <div className="bg-amber-50 dark:bg-black/50 rounded-lg p-3">
                                <h4 className="text-sm font-semibold mb-2 flex items-center space-x-1">
                                  <BarChart3 className="h-3 w-3 text-amber-600" />
                                  <span>Key Issues</span>
                                  <span className="text-foreground text-xs">
                                    (hover for details)
                                  </span>
                                </h4>
                                <div className="space-y-2">
                                  {currentData?.diagnostics
                                    ?.slice(0, 3)
                                    .map((diagnostic: any, index: number) => (
                                      <Tooltip key={index}>
                                        <TooltipTrigger asChild>
                                          <div className="text-xs cursor-help hover:bg-amber-100 p-2 rounded transition-colors">
                                            <div className="flex items-center justify-between">
                                              <div className="font-medium text-amber-700 flex-1 pr-2">
                                                {diagnostic.title}
                                              </div>
                                              <Info className="h-3 w-3 text-amber-600 flex-shrink-0" />
                                            </div>
                                            <div className="text-amber-600 font-semibold">
                                              {diagnostic.display_value}
                                            </div>
                                            {diagnostic.score !== undefined && (
                                              <div className="text-amber-500 text-xs">
                                                Score:{" "}
                                                {Math.round(
                                                  diagnostic.score * 100
                                                )}
                                                /100
                                              </div>
                                            )}
                                          </div>
                                        </TooltipTrigger>
                                        <TooltipContent className="max-w-sm">
                                          <div className="space-y-2">
                                            <div className="font-semibold text-sm">
                                              {diagnostic.title}
                                            </div>
                                            <div className="text-sm">
                                              {diagnostic.description}
                                            </div>
                                            {diagnostic.score !== undefined && (
                                              <div className="text-xs text-foreground">
                                                Performance Score:{" "}
                                                {Math.round(
                                                  diagnostic.score * 100
                                                )}
                                                /100
                                              </div>
                                            )}
                                            <div className="text-xs bg-amber-50 p-2 rounded text-amber-600">
                                              <strong>Current Status:</strong>{" "}
                                              {diagnostic.display_value}
                                            </div>
                                          </div>
                                        </TooltipContent>
                                      </Tooltip>
                                    ))}
                                </div>
                              </div>
                            )}
                        </div>

                        {/* Compact Timestamp */}
                        <div className="text-xs text-foreground text-center pt-2 border-t flex items-center justify-center space-x-2">
                          <Clock className="h-3 w-3" />
                          <span>
                            {currentData?.timestamp
                              ? new Date(currentData.timestamp).toLocaleString()
                              : "No data"}{" "}
                            • Lighthouse{" "}
                            {currentData?.lighthouse_version || "N/A"}
                          </span>
                        </div>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </div>
            )}
          </CardContent>
        </Card>

        {/* AI Assistant */}
        <SEOAIAssistant
          componentType="core-web-vitals"
          componentData={currentData}
          metrics={{
            performance_score: currentData?.performance_score,
            core_web_vitals: currentData?.core_web_vitals,
            categories: currentData?.categories,
          }}
          websiteUrl={websiteUrl}
          position="bottom-left"
          size="md"
        />
      </div>
    </TooltipProvider>
  );
};

export default CoreWebVitals;
