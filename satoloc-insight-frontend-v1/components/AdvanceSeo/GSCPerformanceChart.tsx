"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from "recharts";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  ChevronDown,
  ChevronUp,
  BarChart3,
  TrendingUp,
  Eye,
  Mouse,
  MapPin,
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import SEOAIAssistant from "./SEOAIAssistant";

// Chart skeleton component
const ChartSkeleton: React.FC = () => (
  <div className="h-64 w-full space-y-4">
    {/* Chart area skeleton */}
    <div className="h-48 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
      <div className="text-center space-y-2">
        <div className="animate-pulse">
          <BarChart3 className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto" />
        </div>
        <Skeleton className="h-4 w-32 mx-auto" />
      </div>
    </div>

    {/* Summary skeleton */}
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      {Array.from({ length: 4 }).map((_, index) => (
        <div key={index} className="text-center space-y-2">
          <div className="flex items-center justify-center space-x-1">
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-3 w-12" />
          </div>
          <Skeleton className="h-6 w-16 mx-auto" />
          <Skeleton className="h-3 w-10 mx-auto" />
        </div>
      ))}
    </div>
  </div>
);

interface GSCPerformanceChartProps {
  data: Array<{
    date: string;
    clicks: number;
    impressions: number;
    ctr: number;
    position: number;
  }>;
  timeRange: string;
  websiteUrl: string;
  isLoading?: boolean;
  enabledMetrics?: {
    clicks: boolean;
    impressions: boolean;
    ctr: boolean;
    position: boolean;
  };
  onMetricToggle?: (metric: string) => void;
}

interface MetricConfig {
  key: string;
  label: string;
  color: string;
  icon: React.ReactNode;
  enabled: boolean;
  yAxisId?: string;
  formatter?: (value: number) => string;
}

const GSCPerformanceChart: React.FC<GSCPerformanceChartProps> = ({
  data,
  timeRange,
  websiteUrl,
  isLoading = false,
  enabledMetrics,
  onMetricToggle,
}) => {
  const [isExpanded, setIsExpanded] = useState(() => {
    // Load expanded state from localStorage, default to true
    try {
      const saved = localStorage.getItem("gsc-chart-expanded");
      return saved !== null ? JSON.parse(saved) : true;
    } catch {
      return true;
    }
  });

  const [cachedData, setCachedData] = useState(() => {
    // Load cached chart data from localStorage
    try {
      const saved = localStorage.getItem("gsc-chart-data");
      if (saved) {
        const parsedData = JSON.parse(saved);
        // Check if cached data matches current context
        if (
          parsedData.websiteUrl === websiteUrl &&
          parsedData.timeRange === timeRange &&
          parsedData.timestamp &&
          Date.now() - parsedData.timestamp < 300000 // 5 minutes
        ) {
          return parsedData.data || [];
        }
      }
    } catch (error) {
      console.warn("Failed to load cached chart data:", error);
    }
    return [];
  });

  // Use external enabled metrics if provided, otherwise use internal state
  const [internalMetrics, setInternalMetrics] = useState<MetricConfig[]>([
    {
      key: "clicks",
      label: "Clicks",
      color: "#3b82f6",
      icon: <Mouse className="h-4 w-4" />,
      enabled: true,
      yAxisId: "left",
    },
    {
      key: "impressions",
      label: "Impressions",
      color: "#10b981",
      icon: <Eye className="h-4 w-4" />,
      enabled: true,
      yAxisId: "left",
    },
    {
      key: "ctr",
      label: "CTR",
      color: "#f59e0b",
      icon: <TrendingUp className="h-4 w-4" />,
      enabled: false,
      yAxisId: "right",
      formatter: (value: number) => `${value.toFixed(2)}%`,
    },
    {
      key: "position",
      label: "Position",
      color: "#ef4444",
      icon: <MapPin className="h-4 w-4" />,
      enabled: false,
      yAxisId: "right",
      formatter: (value: number) => value.toFixed(1),
    },
  ]);

  // Update metrics based on external enabledMetrics prop
  // Persist expanded state to localStorage
  useEffect(() => {
    try {
      localStorage.setItem("gsc-chart-expanded", JSON.stringify(isExpanded));
    } catch (error) {
      console.warn(
        "Failed to save chart expanded state to localStorage:",
        error
      );
    }
  }, [isExpanded]);

  // Persist chart data to localStorage when it changes
  useEffect(() => {
    if (data && data.length > 0 && websiteUrl && timeRange) {
      try {
        const cacheData = {
          data,
          websiteUrl,
          timeRange,
          timestamp: Date.now(),
        };
        localStorage.setItem("gsc-chart-data", JSON.stringify(cacheData));
        setCachedData(data);
      } catch (error) {
        console.warn("Failed to save chart data to localStorage:", error);
      }
    }
  }, [data, websiteUrl, timeRange]);

  // Clear cached data when context changes
  useEffect(() => {
    setCachedData((prevCached: any) => {
      try {
        const saved = localStorage.getItem("gsc-chart-data");
        if (saved) {
          const parsedData = JSON.parse(saved);
          // If context changed, clear cached data
          if (
            parsedData.websiteUrl !== websiteUrl ||
            parsedData.timeRange !== timeRange
          ) {
            return [];
          }
          // If data is stale (older than 5 minutes), clear it
          if (
            !parsedData.timestamp ||
            Date.now() - parsedData.timestamp >= 300000
          ) {
            return [];
          }
          return parsedData.data || [];
        }
      } catch (error) {
        console.warn("Failed to validate cached chart data:", error);
      }
      return [];
    });
  }, [websiteUrl, timeRange]);

  const metrics = enabledMetrics
    ? internalMetrics.map((metric) => ({
        ...metric,
        enabled: enabledMetrics[metric.key as keyof typeof enabledMetrics],
      }))
    : internalMetrics;

  const toggleMetric = (index: number) => {
    if (onMetricToggle) {
      // Use external toggle function if provided
      const metricKey = internalMetrics[index]?.key;
      if (metricKey) {
        onMetricToggle(metricKey);
      }
    } else {
      // Use internal state if no external toggle provided
      setInternalMetrics((prev) =>
        prev.map((metric, i) =>
          i === index ? { ...metric, enabled: !metric.enabled } : metric
        )
      );
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    if (timeRange === "24hours") {
      return date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      });
    } else if (timeRange === "7days") {
      return date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      });
    } else {
      return date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "2-digit",
      });
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">
            {new Date(label).toLocaleDateString("en-US", {
              weekday: "short",
              month: "short",
              day: "numeric",
              year: "numeric",
            })}
          </p>
          {payload.map((entry: any, index: number) => {
            const metric = metrics.find((m) => m.key === entry.dataKey);
            return (
              <div key={index} className="flex items-center space-x-2 mb-1">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: entry.color }}
                />
                <span className="text-sm text-gray-600">{entry.name}:</span>
                <span className="text-sm font-medium">
                  {metric?.formatter
                    ? metric.formatter(entry.value)
                    : formatNumber(entry.value)}
                </span>
              </div>
            );
          })}
        </div>
      );
    }
    return null;
  };

  // Use cached data if available and current data is empty/loading
  const displayData = data && data.length > 0 ? data : cachedData;
  const showCachedIndicator =
    cachedData.length > 0 && data.length === 0 && !isLoading;

  const activeMetrics = metrics.filter((m) => m.enabled);
  const hasLeftAxis = activeMetrics.some((m) => m.yAxisId === "left");
  const hasRightAxis = activeMetrics.some((m) => m.yAxisId === "right");

  return (
    <div className="relative w-full">
      <Card className="w-full shadow-none rounded-md">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              <CardTitle className="text-lg">Performance Chart</CardTitle>
              <span className="text-sm text-gray-500">
                ({displayData.length}{" "}
                {displayData.length === 1 ? "day" : "days"})
              </span>
              {showCachedIndicator && (
                <span className="text-xs bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full">
                  Cached
                </span>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-8 w-8 p-0 bg-gray-100 dark:bg-gray-800 rounded-md"
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </div>

          {/* Metric Toggles - Only show if not controlled by parent */}
          {!enabledMetrics && (
            <div className="flex flex-wrap gap-3 pt-2">
              {isLoading ? (
                // Skeleton for metric toggles
                <div className="flex gap-3">
                  {Array.from({ length: 4 }).map((_, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <Skeleton className="w-4 h-4" />
                      <Skeleton className="w-4 h-4" />
                      <Skeleton className="h-4 w-16" />
                    </div>
                  ))}
                </div>
              ) : (
                metrics.map((metric, index) => (
                  <div
                    key={metric.key}
                    className="flex items-center space-x-2 cursor-pointer"
                    onClick={() => toggleMetric(index)}
                  >
                    <Checkbox
                      checked={metric.enabled}
                      onChange={() => toggleMetric(index)}
                      className="data-[state=checked]:bg-current"
                      style={{
                        color: metric.enabled ? metric.color : undefined,
                        borderColor: metric.color,
                      }}
                    />
                    <div className="flex items-center space-x-1">
                      <div style={{ color: metric.color }}>{metric.icon}</div>
                      <span
                        className={`text-sm font-medium ${
                          metric.enabled ? "text-gray-900" : "text-gray-500"
                        }`}
                        style={{
                          color: metric.enabled ? metric.color : undefined,
                        }}
                      >
                        {metric.label}
                      </span>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
        </CardHeader>

        {isExpanded && (
          <CardContent>
            {isLoading ? (
              <div className="space-y-3">
                <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span>Loading chart data...</span>
                </div>
                <ChartSkeleton />
              </div>
            ) : displayData.length === 0 ? (
              <div className="h-64 flex items-center justify-center">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                  <p className="text-gray-500">
                    No data available for the selected period
                  </p>
                </div>
              </div>
            ) : (
              <div className="h-64 w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={displayData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis
                      dataKey="date"
                      tickFormatter={formatDate}
                      stroke="#6b7280"
                      fontSize={12}
                    />

                    {hasLeftAxis && (
                      <YAxis
                        yAxisId="left"
                        stroke="#6b7280"
                        fontSize={12}
                        tickFormatter={formatNumber}
                      />
                    )}

                    {hasRightAxis && (
                      <YAxis
                        yAxisId="right"
                        orientation="right"
                        stroke="#6b7280"
                        fontSize={12}
                      />
                    )}

                    <Tooltip content={<CustomTooltip />} />

                    {activeMetrics.map((metric) => (
                      <Line
                        key={metric.key}
                        type="monotone"
                        dataKey={metric.key}
                        stroke={metric.color}
                        strokeWidth={2}
                        dot={{ fill: metric.color, strokeWidth: 1, r: 2 }}
                        activeDot={{
                          r: 4,
                          stroke: metric.color,
                          strokeWidth: 2,
                        }}
                        yAxisId={metric.yAxisId}
                        name={metric.label}
                      />
                    ))}
                  </LineChart>
                </ResponsiveContainer>
              </div>
            )}

            {/* Chart Summary */}
            {!isLoading && displayData.length > 0 && (
              <div className="mt-4 pt-4 border-t border-gray-100">
                {showCachedIndicator && (
                  <div className="mb-3 text-center">
                    <span className="text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded-md">
                      📊 Showing cached data from previous session
                    </span>
                  </div>
                )}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                  {metrics.map((metric) => {
                    const values = displayData.map(
                      (d: any) => d[metric.key as keyof typeof d] as number
                    );
                    const sum = values.reduce(
                      (a: number, b: number) => a + b,
                      0
                    );
                    const avg = sum / values.length;
                    const displayValue =
                      metric.key === "ctr"
                        ? `${avg.toFixed(2)}%`
                        : metric.key === "position"
                          ? avg.toFixed(1)
                          : formatNumber(sum);

                    return (
                      <div key={metric.key} className="text-center">
                        <div className="flex items-center justify-center space-x-1 mb-1">
                          <div style={{ color: metric.color }}>
                            {metric.icon}
                          </div>
                          <span className="text-xs font-medium text-gray-600">
                            {metric.label}
                          </span>
                        </div>
                        <div
                          className="text-lg font-semibold"
                          style={{ color: metric.color }}
                        >
                          {displayValue}
                        </div>
                        <div className="text-xs text-gray-500">
                          {metric.key === "position" || metric.key === "ctr"
                            ? "Average"
                            : "Total"}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </CardContent>
        )}
      </Card>

      {/* AI Assistant */}
      <SEOAIAssistant
        componentType="gsc-data"
        componentData={{ data, timeRange, enabledMetrics: metrics }}
        metrics={metrics}
        websiteUrl={websiteUrl}
        position="bottom-left"
        size="md"
      />
    </div>
  );
};

export default GSCPerformanceChart;
