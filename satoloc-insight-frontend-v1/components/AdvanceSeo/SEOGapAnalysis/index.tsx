import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronRight, Bot } from "lucide-react";
import Link from "next/link";

// Define interfaces for the data
interface Competitor {
  name: string;
  url: string;
  target_url?: string;
  description: string;
  percentage: string;
  keywords: string[];
  top_keywords?: string[];
  rank: number;
  strategy_gaps?: { id: number; text: string }[];
  growth_opportunities?: { id: number; text: string }[];
  ranking_issues?: { id: number; title: string; description: string }[];
  content_recommendations?: {
    id: number;
    title: string;
    description: string;
    impact: string;
    estimated_hours: number;
    is_opportunity: boolean;
  }[];
}

interface ContentOpportunity {
  title: string;
  demand: string;
  source: string;
}

interface SEOGapRow {
  id: string;
  category: string;
  gapIdentified: string;
  recommendedAction: string;
  impact: "High" | "Medium" | "Low";
  status: "To Do" | "In Progress" | "Done";
  keyword?: string;
}

interface SEOGapAnalysisProps {
  isWebsiteLoading: boolean;
  isUserWebsiteLoading: boolean;
  hasMetrics: boolean;
  metricsToDisplay: any[];
  topCompetitors: Competitor[];
  currentWebsite: any;
  regionalContentOpportunities: ContentOpportunity[];
  openStrategyModal: (competitor: Competitor) => void;
}

export default function SEOGapAnalysis({
  isWebsiteLoading,
  isUserWebsiteLoading,
  hasMetrics,
  metricsToDisplay,
  topCompetitors,
  currentWebsite,
  regionalContentOpportunities,
  openStrategyModal,
}: SEOGapAnalysisProps) {
  const [competitor1Data, setCompetitor1Data] = useState<SEOGapRow[]>([]);
  const [competitor2Data, setCompetitor2Data] = useState<SEOGapRow[]>([]);
  const [topTwoCompetitors, setTopTwoCompetitors] = useState<Competitor[]>([]);

  // Transform data into table rows separated by competitor
  useEffect(() => {
    if (!topCompetitors || topCompetitors.length === 0) {
      setCompetitor1Data([]);
      setCompetitor2Data([]);
      setTopTwoCompetitors([]);
      return;
    }

    // Get top 2 competitors by rank (sort by rank ascending, take first 2)
    const sortedByRank = [...topCompetitors].sort((a, b) => a.rank - b.rank);
    const topTwo = sortedByRank.slice(0, 2);
    setTopTwoCompetitors(topTwo);

    // Function to create gap analysis data for a specific competitor
    const createCompetitorGapData = (
      competitor: Competitor,
      competitorIndex: number
    ): SEOGapRow[] => {
      const gapData: SEOGapRow[] = [];
      let rowId = 1;

      // Get competitor keywords (mapped from top_keywords in AdvanceSeo component)
      const competitorKeywords =
        competitor.keywords || competitor.top_keywords || [];

      if (competitorKeywords.length === 0) {
        return gapData;
      }

      // For each keyword from this competitor, create a gap analysis row
      competitorKeywords.forEach((keyword: string, index: number) => {
        // Create different types of gaps for variety
        const gapTypes = [
          {
            gapIdentified: `Weak "${keyword}" content depth vs ${competitor.name}`,
            recommendedAction: `Create comprehensive content targeting "${keyword}" to compete with ${competitor.name}'s ranking strategy`,
            impact: "High" as const,
          },
          {
            gapIdentified: `Missing "${keyword}" optimization vs ${competitor.name}`,
            recommendedAction: `Optimize existing pages for "${keyword}" or create new targeted content to match ${competitor.name}'s approach`,
            impact: "Medium" as const,
          },
          {
            gapIdentified: `Low "${keyword}" search visibility vs ${competitor.name}`,
            recommendedAction: `Improve "${keyword}" content quality and SEO structure based on ${competitor.name}'s successful strategy`,
            impact: "Medium" as const,
          },
        ];

        const gapType = gapTypes[index % gapTypes.length];

        const newRow: SEOGapRow = {
          id: `comp${competitorIndex}-${rowId++}`,
          category: keyword,
          gapIdentified: gapType.gapIdentified,
          recommendedAction: gapType.recommendedAction,
          impact: gapType.impact,
          status: "To Do" as const,
          keyword: keyword,
        };

        gapData.push(newRow);
      });

      // Add strategy gaps from currentWebsite if they relate to this competitor
      if (currentWebsite?.strategy_gaps) {
        currentWebsite.strategy_gaps.forEach(
          (gap: { id: number; text: string }) => {
            // Check if this gap mentions the competitor
            if (
              gap.text.toLowerCase().includes(competitor.name.toLowerCase())
            ) {
              // Extract keyword from the gap text
              const keywordMatch = gap.text.match(
                /(?:Weak\s+["']([^"']+)["']|["']([^"']+)["'])/i
              );
              const extractedKeyword = keywordMatch
                ? keywordMatch[1] || keywordMatch[2]
                : null;

              // Use extracted keyword if it exists in competitor's keywords, otherwise use first competitor keyword
              const keyword =
                extractedKeyword &&
                competitorKeywords.includes(extractedKeyword)
                  ? extractedKeyword
                  : competitorKeywords[0];

              const impact =
                gap.text.toLowerCase().includes("critical") ||
                gap.text.toLowerCase().includes("urgent")
                  ? "High"
                  : gap.text.toLowerCase().includes("important") ||
                      gap.text.toLowerCase().includes("significant")
                    ? "Medium"
                    : "Low";

              if (keyword) {
                gapData.push({
                  id: `strategy-comp${competitorIndex}-${rowId++}`,
                  category: keyword,
                  gapIdentified: `Strategy gap identified vs ${competitor.name}`,
                  recommendedAction: gap.text,
                  impact: impact as "High" | "Medium" | "Low",
                  status: "To Do",
                  keyword: keyword,
                });
              }
            }
          }
        );
      }

      // Add regional content opportunities using competitor keywords
      if (
        regionalContentOpportunities &&
        regionalContentOpportunities.length > 0 &&
        competitorKeywords.length > 0
      ) {
        regionalContentOpportunities
          .slice(0, Math.min(3, competitorKeywords.length))
          .forEach((content: ContentOpportunity, index: number) => {
            const keyword = competitorKeywords[index];

            gapData.push({
              id: `content-comp${competitorIndex}-${rowId++}`,
              category: keyword,
              gapIdentified: `Missing regional content opportunity for "${keyword}"`,
              recommendedAction: `Create regional content for "${content.title}" targeting "${keyword}" - ${content.demand}. Source: ${content.source}`,
              impact: "Medium",
              status: "To Do",
              keyword: keyword,
            });
          });
      }

      return gapData;
    };

    // Create gap data for each competitor
    const comp1Data = topTwo[0] ? createCompetitorGapData(topTwo[0], 1) : [];
    const comp2Data = topTwo[1] ? createCompetitorGapData(topTwo[1], 2) : [];

    setCompetitor1Data(comp1Data);
    setCompetitor2Data(comp2Data);
  }, [currentWebsite, regionalContentOpportunities, topCompetitors]);

  if (isWebsiteLoading || isUserWebsiteLoading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
        <p className="mt-4 text-muted-foreground">
          Loading SEO gap analysis...
        </p>
      </div>
    );
  }

  if (!hasMetrics) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">
          No SEO data available. Please analyze a website to see gap analysis.
        </p>
      </div>
    );
  }

  const getImpactBadgeVariant = (impact: string) => {
    switch (impact) {
      case "High":
        return "destructive";
      case "Medium":
        return "warning";
      case "Low":
        return "secondary";
      default:
        return "default";
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "Done":
        return "success";
      case "In Progress":
        return "warning";
      case "To Do":
        return "secondary";
      default:
        return "default";
    }
  };

  const handleStatusChange = (
    rowId: string,
    newStatus: "To Do" | "In Progress" | "Done",
    competitorIndex: number
  ) => {
    if (competitorIndex === 1) {
      setCompetitor1Data((prevData) =>
        prevData.map((row) =>
          row.id === rowId ? { ...row, status: newStatus } : row
        )
      );
    } else if (competitorIndex === 2) {
      setCompetitor2Data((prevData) =>
        prevData.map((row) =>
          row.id === rowId ? { ...row, status: newStatus } : row
        )
      );
    }
  };

  const renderTable = (
    data: SEOGapRow[],
    competitorIndex: number,
    competitorName: string
  ) => (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[150px]">Category</TableHead>
            <TableHead className="w-[200px]">Gap Identified</TableHead>
            <TableHead className="min-w-[300px]">Recommended Action</TableHead>
            <TableHead className="w-[100px]">Impact</TableHead>
            <TableHead className="w-[120px]">Status</TableHead>
            <TableHead className="w-[140px]">AI Content</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.length === 0 ? (
            <TableRow>
              <TableCell colSpan={6} className="text-center py-8">
                <p className="text-muted-foreground">
                  No gap analysis data available for {competitorName}. Submit a
                  website for analysis to see SEO opportunities.
                </p>
              </TableCell>
            </TableRow>
          ) : (
            data.map((row) => (
              <TableRow key={row.id}>
                <TableCell className="font-medium">{row.category}</TableCell>
                <TableCell className="text-sm">{row.gapIdentified}</TableCell>
                <TableCell className="text-sm">
                  {row.recommendedAction}
                </TableCell>
                <TableCell>
                  <Badge variant={getImpactBadgeVariant(row.impact)}>
                    {row.impact}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Select
                    value={row.status}
                    onValueChange={(value) =>
                      handleStatusChange(
                        row.id,
                        value as "To Do" | "In Progress" | "Done",
                        competitorIndex
                      )
                    }
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="To Do">To Do</SelectItem>
                      <SelectItem value="In Progress">In Progress</SelectItem>
                      <SelectItem value="Done">Done</SelectItem>
                    </SelectContent>
                  </Select>
                </TableCell>
                <TableCell>
                  <Button
                    variant="default"
                    size="sm"
                    className="w-full"
                    asChild
                  >
                    <Link
                      href={`/ai-content?keyword=${encodeURIComponent(row.keyword || row.category)}`}
                    >
                      <Bot className="w-4 h-4 mr-1" />
                      Generate Content
                    </Link>
                  </Button>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">
          SEO Gap Analysis
        </CardTitle>
      </CardHeader>
      <CardContent>
        {topTwoCompetitors.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">
              No competitor data available. Submit a website for analysis to see
              SEO gap analysis.
            </p>
          </div>
        ) : (
          <Tabs defaultValue="competitor1" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="competitor1">
                {topTwoCompetitors[0]?.name || "Competitor 1"}
              </TabsTrigger>
              {topTwoCompetitors[1] && (
                <TabsTrigger value="competitor2">
                  {topTwoCompetitors[1].name || "Competitor 2"}
                </TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="competitor1" className="mt-4">
              {renderTable(
                competitor1Data,
                1,
                topTwoCompetitors[0]?.name || "Competitor 1"
              )}
            </TabsContent>

            {topTwoCompetitors[1] && (
              <TabsContent value="competitor2" className="mt-4">
                {renderTable(
                  competitor2Data,
                  2,
                  topTwoCompetitors[1].name || "Competitor 2"
                )}
              </TabsContent>
            )}
          </Tabs>
        )}
      </CardContent>
    </Card>
  );
}
