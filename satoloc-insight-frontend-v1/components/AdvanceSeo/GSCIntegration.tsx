import React, { useState, useEffect, useCallback, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Loader2, AlertCircle, CheckCircle, ExternalLink } from "lucide-react";
import { useQueryClient } from "@tanstack/react-query";
import {
  useConnectToGSC,
  useDisconnectFromGSC,
  useGSCConnectionStatus,
} from "@/internal-api/seo/gsc";

// Google OAuth configuration
const GOOGLE_CLIENT_ID = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
const GOOGLE_REDIRECT_URI =
  typeof window !== "undefined" ? `${window.location.origin}/gsc-callback` : "";
const GOOGLE_SCOPE =
  "https://www.googleapis.com/auth/webmasters.readonly https://www.googleapis.com/auth/webmasters";

const GSCIntegration: React.FC = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const messageListenerRef = useRef<((event: MessageEvent) => void) | null>(
    null
  );
  const authWindowRef = useRef<Window | null>(null);
  const authCodeUsedRef = useRef<Set<string>>(new Set());

  // React Query hooks
  const {
    data: connectionStatus,
    isLoading: isStatusLoading,
    refetch: refetchStatus,
  } = useGSCConnectionStatus();

  const { mutate: connectToGSC, isLoading: isConnecting } = useConnectToGSC({
    onSuccess: (data: any) => {
      toast({
        title: "Connected to Google Search Console",
        description:
          "Your Google Search Console account has been successfully connected.",
      });

      // Invalidate all related queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["gscConnectionStatus"] });
      queryClient.invalidateQueries({ queryKey: ["gscSites"] });

      // Clean up auth state
      setIsAuthenticating(false);
      cleanupAuthListener();
    },
    onError: (error: Error) => {
      console.error("GSC Connection Error:", error);
      toast({
        title: "Connection failed",
        description:
          error.message || "Failed to connect to Google Search Console.",
        variant: "destructive",
      });

      // Clean up auth state on error
      setIsAuthenticating(false);
      cleanupAuthListener();
    },
  });

  const { mutate: disconnectFromGSC, isLoading: isDisconnecting } =
    useDisconnectFromGSC({
      onSuccess: () => {
        toast({
          title: "Disconnected from Google Search Console",
          description:
            "Your Google Search Console account has been successfully disconnected.",
        });

        // Invalidate all related queries to clear data
        queryClient.invalidateQueries({ queryKey: ["gscConnectionStatus"] });
        queryClient.invalidateQueries({ queryKey: ["gscSites"] });

        // Clear used auth codes
        authCodeUsedRef.current.clear();
      },
      onError: (error: Error) => {
        toast({
          title: "Disconnection failed",
          description:
            error.message || "Failed to disconnect from Google Search Console.",
          variant: "destructive",
        });
      },
    });

  // Clean up message listener and auth window
  const cleanupAuthListener = useCallback(() => {
    if (messageListenerRef.current) {
      window.removeEventListener("message", messageListenerRef.current);
      messageListenerRef.current = null;
    }
    if (authWindowRef.current && !authWindowRef.current.closed) {
      authWindowRef.current.close();
      authWindowRef.current = null;
    }
  }, []);

  // Clean up on component unmount
  useEffect(() => {
    return () => {
      cleanupAuthListener();
    };
  }, [cleanupAuthListener]);

  // Handle OAuth authentication flow
  const initiateGoogleAuth = useCallback(() => {
    if (!GOOGLE_CLIENT_ID) {
      toast({
        title: "Configuration Error",
        description: "Google Client ID is not configured.",
        variant: "destructive",
      });
      return;
    }

    // Clean up any existing listeners
    cleanupAuthListener();

    setIsAuthenticating(true);

    // Add timestamp to avoid cache issues
    const timestamp = Date.now();
    const authUrl = `https://accounts.google.com/o/oauth2/auth?client_id=${GOOGLE_CLIENT_ID}&redirect_uri=${encodeURIComponent(GOOGLE_REDIRECT_URI)}&scope=${encodeURIComponent(GOOGLE_SCOPE)}&response_type=code&access_type=offline&prompt=consent&state=${timestamp}`;

    // Open the authentication window
    authWindowRef.current = window.open(
      authUrl,
      `gsc_auth_${timestamp}`,
      "width=600,height=700"
    );

    // Create new message listener
    const handleMessage = (event: MessageEvent) => {
      // Verify origin for security
      if (event.origin !== window.location.origin) {
        console.warn("Received message from untrusted origin:", event.origin);
        return;
      }

      if (event.data.type === "GSC_AUTH_CODE") {
        const authCode = event.data.code;

        // Check if this auth code has already been used
        if (authCodeUsedRef.current.has(authCode)) {
          console.warn("Authorization code already used:", authCode);
          toast({
            title: "Authorization Code Already Used",
            description:
              "This authorization code has already been processed. Please try connecting again.",
            variant: "destructive",
          });
          setIsAuthenticating(false);
          cleanupAuthListener();
          return;
        }

        // Mark auth code as used
        authCodeUsedRef.current.add(authCode);

        // Close auth window
        if (authWindowRef.current) {
          authWindowRef.current.close();
          authWindowRef.current = null;
        }

        // Call the API to exchange the code for tokens
        connectToGSC({
          authCode: authCode,
          redirectUri: GOOGLE_REDIRECT_URI,
        });
      } else if (event.data.type === "GSC_AUTH_ERROR") {
        console.error("GSC Auth Error:", event.data.error);
        toast({
          title: "Authentication Error",
          description:
            event.data.error || "Authentication failed. Please try again.",
          variant: "destructive",
        });
        setIsAuthenticating(false);
        cleanupAuthListener();
      }
    };

    // Store reference and add listener
    messageListenerRef.current = handleMessage;
    window.addEventListener("message", handleMessage);

    // Check if auth window was blocked
    if (!authWindowRef.current) {
      toast({
        title: "Popup Blocked",
        description: "Please allow popups for this site and try again.",
        variant: "destructive",
      });
      setIsAuthenticating(false);
      cleanupAuthListener();
      return;
    }

    // Monitor if auth window is closed manually
    const checkClosed = setInterval(() => {
      if (authWindowRef.current?.closed) {
        clearInterval(checkClosed);
        if (isAuthenticating) {
          setIsAuthenticating(false);
          cleanupAuthListener();
        }
      }
    }, 1000);

    // Cleanup interval after 5 minutes
    setTimeout(() => {
      clearInterval(checkClosed);
      if (isAuthenticating) {
        setIsAuthenticating(false);
        cleanupAuthListener();
        toast({
          title: "Authentication Timeout",
          description: "Authentication took too long. Please try again.",
          variant: "destructive",
        });
      }
    }, 300000); // 5 minutes
  }, [connectToGSC, toast, cleanupAuthListener, isAuthenticating]);

  // Handle disconnect
  const handleDisconnect = () => {
    disconnectFromGSC();
  };

  // Determine connection status
  const isConnected = connectionStatus?.is_connected;
  const connectedEmail = connectionStatus?.email;
  const apiEnabled = connectionStatus?.api_enabled;
  const apiError = connectionStatus?.api_error;
  const sitesCount = connectionStatus?.sites_count || 0;
  const isLoading =
    isStatusLoading || isConnecting || isDisconnecting || isAuthenticating;

  return (
    <Card className="w-full shadow-none rounded-md">
      <CardHeader className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">
              Google Search Console Integration
            </CardTitle>
            <CardDescription>
              Connect your Google Search Console account to access search
              performance data
            </CardDescription>
          </div>
          {/* {isConnected && (
            <Badge
              variant="outline"
              className="bg-green-50 text-green-700 border-green-200"
            >
              <CheckCircle className="h-3 w-3 mr-1" />
              Connected
            </Badge>
          )} */}
        </div>
      </CardHeader>
      <CardContent className="px-4">
        {isLoading ? (
          <div className="flex items-center justify-center p-4">
            <Loader2 className="h-6 w-6 animate-spin text-gray-500 mr-2" />
            <span>
              {isAuthenticating ? "Authenticating..." : "Processing..."}
            </span>
          </div>
        ) : isConnected ? (
          <div className="space-y-4">
            {apiEnabled === false ? (
              <Alert variant="destructive" className="bg-red-50 border-red-200">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <AlertTitle>API Not Enabled</AlertTitle>
                <AlertDescription>
                  {apiError ||
                    "Google Search Console API is not enabled in your Google Cloud project. Please enable it in the Google Cloud Console."}
                  <br />
                  <a
                    href="https://console.developers.google.com/apis/api/searchconsole.googleapis.com"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 underline mt-2 inline-block"
                  >
                    Enable the API here →
                  </a>
                </AlertDescription>
              </Alert>
            ) : (
              <Alert
                variant="default"
                className="bg-green-50 dark:bg-green-950/50 border-green-200 dark:border-green-800"
              >
                <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                <AlertTitle className="text-green-800 dark:text-green-200">
                  Connected to Google Search Console
                </AlertTitle>
                <AlertDescription className="text-green-700 dark:text-green-300">
                  {connectedEmail && `Connected as ${connectedEmail}`}
                  {sitesCount > 0 && (
                    <span className="block mt-1 text-sm text-green-600 dark:text-green-400">
                      {sitesCount} site{sitesCount !== 1 ? "s" : ""} accessible
                    </span>
                  )}
                </AlertDescription>
              </Alert>
            )}

            <div className="flex justify-between items-center">
              <p className="text-xs text-gray-500">
                You can disconnect at any time without affecting your Satoloc
                Insight account.
              </p>
              <Button
                variant="destructive"
                onClick={handleDisconnect}
                disabled={isLoading}
              >
                Disconnect
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <Alert variant="default" className="bg-blue-50 border-blue-200">
              <AlertCircle className="h-4 w-4 text-blue-600" />
              <AlertTitle>Not connected</AlertTitle>
              <AlertDescription>
                Connect your Google Search Console account to access search
                performance data and insights.
              </AlertDescription>
            </Alert>

            <div className="flex justify-between items-center">
              <p className="text-sm text-gray-500">
                Your Google credentials are used only for accessing Search
                Console data.
              </p>
              <Button
                variant="default"
                onClick={initiateGoogleAuth}
                disabled={isLoading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Connect to GSC
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default GSCIntegration;
