import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  CalendarIcon,
  Globe2,
  ChevronLeft,
  ChevronRight,
  Trash2,
} from "lucide-react";

interface WebsiteAnalysisPaginationProps {
  localUserWebsiteData: any[];
  currentWebsite: any;
  currentPage: number;
  handlePageChange: (page: number) => void;
  removeWebsiteFromFrontend: () => void;
}

export default function WebsiteAnalysisPagination({
  localUserWebsiteData,
  currentWebsite,
  currentPage,
  handlePageChange,
  removeWebsiteFromFrontend,
}: WebsiteAnalysisPaginationProps) {
  if (!localUserWebsiteData || localUserWebsiteData.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-col md:flex-row items-start md:items-center justify-between bg-card border rounded-lg p-3 gap-2">
      <div className="flex flex-col md:flex-row md:items-center gap-2">
        <span className="text-sm text-muted-foreground">
          <CalendarIcon className="inline-block w-4 h-4 mr-1" />
          Generated on{" "}
          {new Date(currentWebsite?.created_at || Date.now()).toLocaleString()}
        </span>
        <span className="text-sm md:ml-4">
          <Globe2 className="inline-block w-4 h-4 mr-1" />
          <span className="font-medium">{currentWebsite?.url || "N/A"}</span>
        </span>
      </div>
      <div className="flex items-center justify-between md:justify-end w-full md:w-auto gap-2">
        <span className="text-sm font-medium">
          Report {currentPage} of {localUserWebsiteData.length}
        </span>
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={() =>
              handlePageChange(
                Math.min(localUserWebsiteData.length, currentPage + 1)
              )
            }
            disabled={currentPage === localUserWebsiteData.length}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 ml-1"
            onClick={removeWebsiteFromFrontend}
            title="Remove from dashboard"
          >
            <Trash2 className="h-4 w-4 text-red-500" />
          </Button>
        </div>
      </div>
    </div>
  );
}
