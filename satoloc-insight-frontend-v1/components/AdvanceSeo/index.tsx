import React, { useState, useEffect } from "react";
import { CrawlPagination } from "../SeoInsights/CrawlPagination";
import SeoCrawlForm from "./SeoCrawlForm";

import {
  useMyAnalyses,
  useProcessWebsite,
} from "@/internal-api/seo/advance-seo";

import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import GSCIntegration from "./GSCIntegration";
import GSCPerformanceData from "./GSCPerformanceData";
import CoreWebVitals from "./CoreWebVitals";
import WebsiteAnalysisPagination from "./WebsiteAnalysisPagination";
import SEOGapAnalysis from "./SEOGapAnalysis";
import CompetitorStrategyModal from "./CompetitorStrategyModal";

// Define types for our data structures
interface ChecklistItem {
  text: string;
  completed: boolean;
}

interface TechnicalChecklist {
  competitionGaps: ChecklistItem[];
  trendingTokens: ChecklistItem[];
  technicalUpdates: ChecklistItem[];
}

// Define interfaces for competitor data
interface Competitor {
  name: string;
  url: string;
  description: string;
  percentage: string;
  keywords: string[];
  rank: number;
  strategy_gaps?: { id: number; text: string }[];
  growth_opportunities?: { id: number; text: string }[];
  ranking_issues?: { id: number; title: string; description: string }[];
  content_recommendations?: {
    id: number;
    title: string;
    description: string;
    impact: string;
    estimated_hours: number;
    is_opportunity: boolean;
  }[];
}

// Define interface for content opportunities
interface ContentOpportunity {
  title: string;
  demand: string;
  source: string;
}

export { default as GSCIntegration } from "./GSCIntegration";

export default function AdvanceSeoComponent() {
  // State for the current website data
  const [websiteData, setWebsiteData] = React.useState(null);
  const [isWebsiteLoading, setIsWebsiteLoading] = React.useState(false);
  const [currentPage, setCurrentPage] = React.useState(1);
  // State for user website data with local management
  const [localUserWebsiteData, setLocalUserWebsiteData] = React.useState<any[]>(
    []
  );
  // Keep track of removed website IDs to persist across refreshes
  const [removedWebsiteIds, setRemovedWebsiteIds] = React.useState<number[]>(
    []
  );

  // Fetch user's website analyses
  const { data: userWebsiteData, isLoading: isUserWebsiteLoading } =
    useMyAnalyses();

  // Load removed website IDs from localStorage on initial render
  useEffect(() => {
    const storedRemovedIds = localStorage.getItem("removedWebsiteIds");
    if (storedRemovedIds) {
      try {
        const parsedIds = JSON.parse(storedRemovedIds);
        setRemovedWebsiteIds(Array.isArray(parsedIds) ? parsedIds : []);
      } catch (error) {
        console.error(
          "Error parsing removed website IDs from localStorage:",
          error
        );
        // If there's an error, reset the localStorage item
        localStorage.setItem("removedWebsiteIds", JSON.stringify([]));
      }
    }
  }, []);

  // Initialize local state from API data when it loads, filtering out removed websites
  useEffect(() => {
    if (userWebsiteData) {
      // Filter out websites that have been removed
      const filteredData = userWebsiteData.filter(
        (website: any) => !removedWebsiteIds.includes(website.id)
      );
      setLocalUserWebsiteData(filteredData);
    }
  }, [userWebsiteData, removedWebsiteIds]);

  // Check if a website is in the removed list
  const isWebsiteRemoved = (websiteId: number): boolean => {
    return removedWebsiteIds.includes(websiteId);
  };

  // Remove a website from the frontend only (not from the database)
  const removeWebsiteFromFrontend = () => {
    if (!currentWebsite) return;

    // Add current website ID to removed list
    const updatedRemovedIds = [...removedWebsiteIds, currentWebsite.id];
    setRemovedWebsiteIds(updatedRemovedIds);

    // Store in localStorage to persist across refreshes
    localStorage.setItem(
      "removedWebsiteIds",
      JSON.stringify(updatedRemovedIds)
    );

    // Filter out the current website from localUserWebsiteData
    const updatedWebsiteData = localUserWebsiteData.filter(
      (website) => website.id !== currentWebsite.id
    );

    setLocalUserWebsiteData(updatedWebsiteData);

    // Reset the current page if needed
    if (currentPage > updatedWebsiteData.length) {
      setCurrentPage(Math.max(1, updatedWebsiteData.length));
    }

    // Reset the current website data
    setWebsiteData(null);

    // Show toast notification
    toast({
      title: "Website removed",
      description: `${currentWebsite.url} has been removed from your dashboard. You can re-analyze it anytime.`,
      variant: "default",
    });
  };

  // Use the process website mutation
  const processWebsite = useProcessWebsite();
  const { toast } = useToast();

  // Get the current website based on pagination
  const getCurrentWebsite = () => {
    if (websiteData) return websiteData;
    if (!localUserWebsiteData || localUserWebsiteData.length === 0) return null;

    // Adjust index for 1-based pagination (currentPage - 1)
    const index = currentPage - 1;
    if (index >= 0 && index < localUserWebsiteData.length) {
      return localUserWebsiteData[index];
    }

    return localUserWebsiteData[0];
  };

  const currentWebsite = getCurrentWebsite();

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Reset websiteData when changing pages to show data from userWebsiteData
    setWebsiteData(null);
  };

  // Log the data for debugging
  useEffect(() => {
    if (websiteData) {
    }
  }, [websiteData]);

  // Log user website data for debugging
  useEffect(() => {
    if (userWebsiteData) {
    }
    if (localUserWebsiteData) {
    }
  }, [userWebsiteData, localUserWebsiteData]);

  // Handler for when the crawl form successfully fetches data
  const handleCrawlSuccess = (data: any) => {
    // If the website was previously removed, remove it from the removed list
    if (data && data.id && removedWebsiteIds.includes(data.id)) {
      const updatedRemovedIds = removedWebsiteIds.filter(
        (id) => id !== data.id
      );
      setRemovedWebsiteIds(updatedRemovedIds);
      localStorage.setItem(
        "removedWebsiteIds",
        JSON.stringify(updatedRemovedIds)
      );
    }

    setWebsiteData(data);
    setCurrentPage(1); // Reset to first page when new data is fetched

    // Add the new data to localUserWebsiteData if it doesn't exist already
    const existingWebsiteIndex = localUserWebsiteData.findIndex(
      (website) => website.id === data.id
    );

    if (existingWebsiteIndex >= 0) {
      // Update existing website data
      const updatedWebsiteData = [...localUserWebsiteData];
      updatedWebsiteData[existingWebsiteIndex] = data;
      setLocalUserWebsiteData(updatedWebsiteData);
    } else {
      // Add new website data
      setLocalUserWebsiteData([data, ...localUserWebsiteData]);
    }
  };

  // Generate SEO metrics from the current website data
  const getSeoMetrics = () => {
    if (!currentWebsite) return [];

    return [
      {
        title: "Domain Rating",
        value: currentWebsite.seo_metrics?.domain_rating?.score || "N/A",
        trend: currentWebsite.seo_metrics?.domain_rating?.previous_score
          ? `${currentWebsite.seo_metrics.domain_rating.score > currentWebsite.seo_metrics.domain_rating.previous_score ? "+" : ""}${currentWebsite.seo_metrics.domain_rating.score - currentWebsite.seo_metrics.domain_rating.previous_score} points`
          : "",
        trendPositive: currentWebsite.seo_metrics?.domain_rating?.previous_score
          ? currentWebsite.seo_metrics.domain_rating.score >=
            currentWebsite.seo_metrics.domain_rating.previous_score
          : true,
      },
      {
        title: "Organic Traffic",
        value:
          currentWebsite.seo_metrics?.organic_traffic?.score?.toLocaleString() ||
          "N/A",
        trend: currentWebsite.seo_metrics?.organic_traffic?.previous_score
          ? `${currentWebsite.seo_metrics.organic_traffic.score > currentWebsite.seo_metrics.organic_traffic.previous_score ? "+" : ""}${Math.round(((currentWebsite.seo_metrics.organic_traffic.score - currentWebsite.seo_metrics.organic_traffic.previous_score) / currentWebsite.seo_metrics.organic_traffic.previous_score) * 100)}% vs last month`
          : "",
        trendPositive: currentWebsite.seo_metrics?.organic_traffic
          ?.previous_score
          ? currentWebsite.seo_metrics.organic_traffic.score >=
            currentWebsite.seo_metrics.organic_traffic.previous_score
          : true,
      },
      {
        title: "Search Rankings",
        value: `${currentWebsite.seo_metrics?.search_rankings?.score || "N/A"}%`,
        trend: currentWebsite.seo_metrics?.search_rankings?.previous_score
          ? `${currentWebsite.seo_metrics.search_rankings.score > currentWebsite.seo_metrics.search_rankings.previous_score ? "+" : ""}${Math.round(currentWebsite.seo_metrics.search_rankings.score - currentWebsite.seo_metrics.search_rankings.previous_score)}% vs previous`
          : "",
        trendPositive: currentWebsite.seo_metrics?.search_rankings
          ?.previous_score
          ? currentWebsite.seo_metrics.search_rankings.score >=
            currentWebsite.seo_metrics.search_rankings.previous_score
          : true,
      },
      {
        title: "Search Terms",
        value:
          currentWebsite.seo_metrics?.search_terms?.score?.toLocaleString() ||
          "N/A",
        trend: currentWebsite.seo_metrics?.search_terms?.previous_score
          ? `${currentWebsite.seo_metrics.search_terms.score - currentWebsite.seo_metrics.search_terms.previous_score} new found`
          : "",
        trendPositive: currentWebsite.seo_metrics?.search_terms?.previous_score
          ? currentWebsite.seo_metrics.search_terms.score >=
            currentWebsite.seo_metrics.search_terms.previous_score
          : true,
      },
      {
        title: "Site Links",
        value:
          currentWebsite.seo_metrics?.site_links?.score?.toLocaleString() ||
          "N/A",
        trend: "", // No previous data available in the example
        trendPositive: true,
      },
      {
        title: "Content Score",
        value: `${currentWebsite.seo_metrics?.content_score?.score || "N/A"}%`,
        trend: currentWebsite.seo_metrics?.content_score?.previous_score
          ? `${currentWebsite.seo_metrics.content_score.score > currentWebsite.seo_metrics.content_score.previous_score ? "+" : ""}${Math.round(currentWebsite.seo_metrics.content_score.score - currentWebsite.seo_metrics.content_score.previous_score)}%`
          : "",
        trendPositive: currentWebsite.seo_metrics?.content_score?.previous_score
          ? currentWebsite.seo_metrics.content_score.score >=
            currentWebsite.seo_metrics.content_score.previous_score
          : true,
      },
    ];
  };

  // State to track checklist items
  const [checklist, setChecklist] = useState<TechnicalChecklist>({
    competitionGaps: [],
    trendingTokens: [],
    technicalUpdates: [],
  });

  // Get technical analysis data for the checklist
  const getTechnicalAnalysisChecklist = (): TechnicalChecklist => {
    if (!currentWebsite || !currentWebsite.technical_analysis) {
      return {
        competitionGaps: [],
        trendingTokens: [],
        technicalUpdates: [],
      };
    }

    // Extract technical issues and categorize them
    const technicalIssues: string[] =
      currentWebsite.technical_analysis.technical_issues || [];

    // Categorize issues into three groups
    const highPriorityIssues = technicalIssues.slice(
      0,
      Math.min(3, technicalIssues.length)
    );
    const mediumPriorityIssues = technicalIssues.slice(
      Math.min(3, technicalIssues.length),
      Math.min(6, technicalIssues.length)
    );
    const lowPriorityIssues = technicalIssues.slice(
      Math.min(6, technicalIssues.length)
    );

    return {
      competitionGaps: highPriorityIssues.map((issue: string) => ({
        text: issue,
        completed: false,
      })),
      trendingTokens: mediumPriorityIssues.map((issue: string) => ({
        text: issue,
        completed: false,
      })),
      technicalUpdates: lowPriorityIssues.map((issue: string) => ({
        text: issue,
        completed: false,
      })),
    };
  };

  // Initialize checklist when website data changes
  useEffect(() => {
    setChecklist(getTechnicalAnalysisChecklist());
  }, [currentWebsite]);

  // Calculate completion percentage for the progress bar
  const calculateCompletionPercentage = (): number => {
    const totalIssues =
      checklist.competitionGaps.length +
      checklist.trendingTokens.length +
      checklist.technicalUpdates.length;

    if (totalIssues === 0) return 0;

    const completedIssues =
      checklist.competitionGaps.filter((item: ChecklistItem) => item.completed)
        .length +
      checklist.trendingTokens.filter((item: ChecklistItem) => item.completed)
        .length +
      checklist.technicalUpdates.filter((item: ChecklistItem) => item.completed)
        .length;

    return Math.round((completedIssues / totalIssues) * 100);
  };

  // Get the completion percentage
  const completionPercentage = calculateCompletionPercentage();

  // Get the SEO metrics to display
  const metricsToDisplay = getSeoMetrics();
  const hasMetrics = metricsToDisplay.length > 0;

  // Get top competitors from the website data
  const getTopCompetitors = (): Competitor[] => {
    if (!currentWebsite || !currentWebsite.competitors) {
      return [];
    }

    // Extract competitors from the API response
    const apiCompetitors = currentWebsite.competitors || [];

    // Map API data to our competitor interface
    const mappedCompetitors = apiCompetitors.map((competitor: any) => {
      // Calculate percentage difference (could be positive or negative)
      const percentageDiff =
        competitor.percentage_diff || Math.floor(Math.random() * 10) + 1;
      const formattedPercentage =
        percentageDiff > 0 ? `+${percentageDiff}%` : `${percentageDiff}%`;

      return {
        name: competitor.name || "Unknown Competitor",
        url: (competitor.url || "#") as string,
        description: competitor.description || "Competing in similar keywords",
        percentage: formattedPercentage,
        keywords: competitor.top_keywords || [],
        rank: competitor.rank || 999, // Use a high default rank if not provided
        strategy_gaps: competitor.strategy_gaps || [],
        growth_opportunities: competitor.growth_opportunities || [],
        ranking_issues: competitor.ranking_issues || [],
        content_recommendations: competitor.content_recommendations || [],
      };
    });

    // Sort by rank (ascending, lower rank = higher position) and take top 2
    return mappedCompetitors
      .sort((a: Competitor, b: Competitor) => (a.rank || 999) - (b.rank || 999))
      .slice(0, 2);
  };

  // Get the top competitors
  const topCompetitors = getTopCompetitors();

  // Get regional content opportunities from competitor keywords
  const getRegionalContentOpportunities = (): ContentOpportunity[] => {
    if (!topCompetitors || topCompetitors.length === 0) {
      return [];
    }

    // Collect keywords from top competitors
    const contentOpportunities: ContentOpportunity[] = [];

    // Get up to 2 keywords from first competitor
    if (
      topCompetitors[0] &&
      topCompetitors[0].keywords &&
      topCompetitors[0].keywords.length > 0
    ) {
      const competitor1Keywords = topCompetitors[0].keywords.slice(0, 2);
      competitor1Keywords.forEach((keyword) => {
        contentOpportunities.push({
          title: keyword,
          demand: "High regional demand",
          source: topCompetitors[0].name,
        });
      });
    }

    // Get up to 2 keywords from second competitor
    if (
      topCompetitors[1] &&
      topCompetitors[1].keywords &&
      topCompetitors[1].keywords.length > 0
    ) {
      const competitor2Keywords = topCompetitors[1].keywords.slice(0, 2);
      competitor2Keywords.forEach((keyword) => {
        // Only add if not already added (avoid duplicates)
        if (!contentOpportunities.some((item) => item.title === keyword)) {
          contentOpportunities.push({
            title: keyword,
            demand: "High regional demand",
            source: topCompetitors[1].name,
          });
        }
      });
    }

    return contentOpportunities;
  };

  // Get the regional content opportunities
  const regionalContentOpportunities = getRegionalContentOpportunities();

  // State for the competitor strategy modal
  const [selectedCompetitor, setSelectedCompetitor] =
    useState<Competitor | null>(null);
  const [isStrategyModalOpen, setIsStrategyModalOpen] = useState(false);

  // Function to open the strategy modal
  const openStrategyModal = (competitor: Competitor) => {
    setSelectedCompetitor(competitor);
    setIsStrategyModalOpen(true);
  };

  // Function to close the strategy modal
  const closeStrategyModal = () => {
    setIsStrategyModalOpen(false);
    // Reset the selected competitor after a short delay to prevent UI flicker
    setTimeout(() => setSelectedCompetitor(null), 300);
  };

  // State for the AI content generation tab
  const [showContentGeneration, setShowContentGeneration] =
    useState<boolean>(false);
  const [selectedCompetitorForContent, setSelectedCompetitorForContent] =
    useState<Competitor | null>(null);

  // Function to open the content generation tab with a specific competitor's keywords
  const openContentGeneration = (competitor: Competitor) => {
    setSelectedCompetitorForContent(competitor);
    setShowContentGeneration(true);
  };

  // Add a function to get regional performance data
  const getRegionalPerformanceData = () => {
    if (
      !currentWebsite ||
      !currentWebsite.regional_performance ||
      currentWebsite.regional_performance.length === 0
    ) {
      return [];
    }

    // Sort the data by year and month to ensure chronological order
    const sortedData = [...currentWebsite.regional_performance].sort((a, b) => {
      if (a.year !== b.year) {
        return a.year - b.year;
      }
      // Convert month names to numbers for sorting
      const monthToNum = (month: string) => {
        const months = [
          "Jan",
          "Feb",
          "Mar",
          "Apr",
          "May",
          "Jun",
          "Jul",
          "Aug",
          "Sep",
          "Oct",
          "Nov",
          "Dec",
        ];
        return months.indexOf(month);
      };
      return monthToNum(a.month) - monthToNum(b.month);
    });

    // Map the data to the format expected by the chart
    return sortedData.map((item) => ({
      month: item.month,
      organic: item.organic_keywords || 0,
      competitor: item.competitors_average || 0,
      rank: item.avg_position || 0,
      traffic: item.visitors_trend || 0,
    }));
  };

  // Get the regional performance data
  const regionalPerformanceData = getRegionalPerformanceData();

  return (
    <div className=" mx-auto ">
      <div className="space-y-6">
        <div className="space-y-4">
          <div className="space-y-2">
            {/* Website Analysis Pagination */}
            <WebsiteAnalysisPagination
              localUserWebsiteData={localUserWebsiteData}
              currentWebsite={currentWebsite}
              currentPage={currentPage}
              handlePageChange={handlePageChange}
              removeWebsiteFromFrontend={removeWebsiteFromFrontend}
            />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              <SeoCrawlForm onSuccess={handleCrawlSuccess} />
              <GSCIntegration />
            </div>
          </div>

          {/* Main Content Tabs */}
          <Tabs defaultValue="gsc" className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-muted/50 rounded-md p-0">
              <TabsTrigger
                value="gsc"
                className="text-sm p-4 rounded-md font-medium data-[state=active]:bg-background data-[state=active]:shadow-none data-[state=active]:border "
              >
                GSC Performance Insight
              </TabsTrigger>
              <TabsTrigger
                value="metrics"
                className="text-sm p-4 rounded-md font-medium data-[state=active]:bg-background data-[state=active]:shadow-none data-[state=active]:border"
              >
                Metrics & Intelligence
              </TabsTrigger>
              <TabsTrigger
                value="vitals"
                className="text-sm p-4 rounded-md font-medium data-[state=active]:bg-background data-[state=active]:shadow-none data-[state=active]:border"
              >
                Core Web Vitals
              </TabsTrigger>
            </TabsList>

            {/* GSC Performance Insight Tab */}
            <TabsContent value="gsc" className="space-y-4 mt-6">
              <div className="space-y-2">
                <GSCPerformanceData currentWebsite={currentWebsite} />
              </div>

              <div className="mb-4">
                <CrawlPagination
                  crawls={[]}
                  isLoading={false}
                  onCrawlChange={() => {}}
                />
              </div>
            </TabsContent>

            {/* Metrics & Intelligence Tab */}
            <TabsContent value="metrics" className="space-y-4 mt-6">
              <SEOGapAnalysis
                isWebsiteLoading={isWebsiteLoading}
                isUserWebsiteLoading={isUserWebsiteLoading}
                hasMetrics={hasMetrics}
                metricsToDisplay={metricsToDisplay}
                topCompetitors={topCompetitors}
                currentWebsite={currentWebsite}
                regionalContentOpportunities={regionalContentOpportunities}
                openStrategyModal={openStrategyModal}
              />
            </TabsContent>

            {/* Core Web Vitals Tab */}
            <TabsContent value="vitals" className="space-y-4 mt-6">
              <div className="grid grid-cols-1 gap-4">
                {/* Core Web Vitals Component */}
                {currentWebsite?.url && (
                  <CoreWebVitals websiteUrl={currentWebsite.url} />
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Competitor Strategy Modal */}
      <CompetitorStrategyModal
        competitor={selectedCompetitor}
        isOpen={isStrategyModalOpen}
        onClose={closeStrategyModal}
      />
    </div>
  );
}
