import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { Mouse, Eye, TrendingUp, MapPin, RefreshCw } from "lucide-react";
import { useGSCDataAvailability } from "@/internal-api/seo/gsc";
import { useToast } from "@/hooks/use-toast";
import GSCPerformanceChart from "./GSCPerformanceChart";
import { Divider } from "@mui/material";
import { Skeleton } from "@/components/ui/skeleton";

interface GSCPerformanceMetricsProps {
  selectedSite: string | null;
  timeRange: string;
  setTimeRange: (value: string) => void;
  searchType: string;
  setSearchType: (value: string) => void;
  currentWebsite?: {
    url: string;
    [key: string]: any;
  } | null;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: string;
  enabled: boolean;
  onToggle: () => void;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  icon,
  color,
  enabled,
  onToggle,
}) => {
  const handleCardClick = (e: React.MouseEvent) => {
    // Only trigger if not clicking on the checkbox itself
    if ((e.target as HTMLInputElement).type !== "checkbox") {
      e.preventDefault();
      e.stopPropagation();
      onToggle();
    }
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.stopPropagation();
    // Don't call onToggle here since the checkbox change will trigger the card click
  };

  const handleCheckboxClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onToggle();
  };

  return (
    <div
      className={`
        relative p-4 rounded-lg border cursor-pointer transition-all duration-200 bg-white dark:bg-background/10
        ${enabled ? "border-gray-300" : "border-gray-200 opacity-60"}
      `}
      onClick={handleCardClick}
    >
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={enabled}
            onChange={handleCheckboxChange}
            onClick={handleCheckboxClick}
            className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
          />
          <span
            className={`text-sm font-medium ${enabled ? "text-gray-700" : "text-gray-400"} dark:text-white`}
          >
            {title}
          </span>
        </div>
        <div
          className={`${enabled ? "text-gray-600" : "text-gray-400"} dark:text-white`}
        >
          {icon}
        </div>
      </div>
      <div
        className={`text-2xl font-bold ${enabled ? "text-gray-900" : "text-gray-400"} dark:text-white`}
      >
        {value}
      </div>
    </div>
  );
};

// Skeleton component for metric cards
const MetricCardSkeleton: React.FC = () => (
  <div className="relative p-4 rounded-lg border bg-white dark:bg-background/10 border-gray-200">
    <div className="flex items-center justify-between mb-2">
      <div className="flex items-center space-x-2">
        <Skeleton className="w-4 h-4 rounded" />
        <Skeleton className="h-4 w-24" />
      </div>
      <Skeleton className="w-4 h-4" />
    </div>
    <Skeleton className="h-8 w-16" />
  </div>
);

const GSCPerformanceMetrics: React.FC<GSCPerformanceMetricsProps> = ({
  selectedSite,
  timeRange,
  setTimeRange,
  searchType,
  setSearchType,
  currentWebsite,
}) => {
  const { toast } = useToast();
  const [isFilterDialogOpen, setIsFilterDialogOpen] = useState(false);

  // Function to normalize URLs for comparison
  const normalizeUrl = (url: string): string => {
    if (!url) return "";
    // Remove protocol, www, and trailing slash for comparison
    return url
      .replace(/^https?:\/\//, "")
      .replace(/^www\./, "")
      .replace(/\/$/, "")
      .toLowerCase();
  };

  // Function to check if a GSC site matches the current website
  const isGSCSiteMatchingCurrentWebsite = (gscSiteUrl: string): boolean => {
    if (!currentWebsite?.url || !gscSiteUrl) return false;

    const currentUrl = normalizeUrl(currentWebsite.url);
    const gscUrl = normalizeUrl(gscSiteUrl.replace("sc-domain:", ""));

    // Check if they match exactly or if one contains the other
    return (
      currentUrl === gscUrl ||
      currentUrl.includes(gscUrl) ||
      gscUrl.includes(currentUrl)
    );
  };

  // Metric visibility toggles
  const [enabledMetrics, setEnabledMetrics] = useState({
    clicks: true,
    impressions: true,
    ctr: true,
    position: true,
  });

  // Real performance data from API
  const [performanceData, setPerformanceData] = useState({
    clicks: 0,
    impressions: 0,
    ctr: 0,
    position: 0,
  });

  // Chart data from availability API
  const [chartData, setChartData] = useState<
    Array<{
      date: string;
      clicks: number;
      impressions: number;
      ctr: number;
      position: number;
    }>
  >([]);

  // State to track if data has been loaded for current context
  const [hasLoadedData, setHasLoadedData] = useState(false);
  const [lastLoadedContext, setLastLoadedContext] = useState<{
    site: string | null;
    timeRange: string;
    searchType: string;
  }>({
    site: null,
    timeRange: "",
    searchType: "",
  });

  const { mutate: fetchPerformanceData, isLoading } = useGSCDataAvailability({
    onSuccess: (data: any) => {
      // Get the availability info from the response
      const availabilityInfo = data.availability_info;

      if (
        availabilityInfo?.available_dates &&
        availabilityInfo.available_dates.length > 0
      ) {
        // Calculate totals based on time range
        let totalClicks = 0;
        let totalImpressions = 0;
        let weightedPositionSum = 0;
        let totalImpressionWeights = 0;
        let validDays = 0;

        // Determine how many days to aggregate based on time range
        const daysToAggregate =
          timeRange === "24hours"
            ? 1 // Most recent day only
            : timeRange === "7days"
              ? Math.min(7, availabilityInfo.available_dates.length)
              : timeRange === "28days"
                ? Math.min(28, availabilityInfo.available_dates.length)
                : timeRange === "3months"
                  ? Math.min(90, availabilityInfo.available_dates.length)
                  : 1;

        // Take the most recent dates (they're already sorted by date desc)
        const datesToProcess = availabilityInfo.available_dates.slice(
          0,
          daysToAggregate
        );

        datesToProcess.forEach((dateData: any) => {
          const clicks = dateData.clicks || 0;
          const impressions = dateData.impressions || 0;
          const position = dateData.position || 0;

          // Sum up clicks and impressions
          totalClicks += clicks;
          totalImpressions += impressions;

          // For average position, weight by impressions (GSC methodology)
          if (impressions > 0) {
            weightedPositionSum += position * impressions;
            totalImpressionWeights += impressions;
            validDays++;
          }
        });

        // Calculate metrics following GSC formulas
        const calculatedCtr =
          totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;
        const calculatedPosition =
          totalImpressionWeights > 0
            ? weightedPositionSum / totalImpressionWeights
            : 0;

        const processedData = {
          clicks: totalClicks,
          impressions: totalImpressions,
          ctr: calculatedCtr,
          position: calculatedPosition,
        };

        setPerformanceData(processedData);

        // Also prepare chart data (convert for chart format)
        const formattedChartData = datesToProcess.map((dateData: any) => ({
          date: dateData.date,
          clicks: dateData.clicks || 0,
          impressions: dateData.impressions || 0,
          ctr: (dateData.ctr || 0) * 100, // Convert to percentage for chart
          position: dateData.position || 0,
        }));

        // Sort by date ascending for chart
        formattedChartData.sort(
          (a: { date: string }, b: { date: string }) =>
            new Date(a.date).getTime() - new Date(b.date).getTime()
        );
        setChartData(formattedChartData);

        // Mark data as loaded for current context
        setHasLoadedData(true);
        setLastLoadedContext({
          site: selectedSite,
          timeRange,
          searchType,
        });
      } else {
        // Reset to zero if no data
        setPerformanceData({
          clicks: 0,
          impressions: 0,
          ctr: 0,
          position: 0,
        });
        setChartData([]);
        setHasLoadedData(true);
        setLastLoadedContext({
          site: selectedSite,
          timeRange,
          searchType,
        });
      }

      toast({
        title: "Performance Data Updated",
        description: `Data loaded for ${formatSiteUrl(selectedSite || "")} (${timeRange})`,
      });
    },
    onError: (error: Error) => {
      console.error("❌ API Error:", error);
      toast({
        title: "Error Loading Performance Data",
        description: error.message || "Failed to load performance data.",
        variant: "destructive",
      });
      // Still mark as "loaded" to prevent infinite retries
      setHasLoadedData(true);
      setLastLoadedContext({
        site: selectedSite,
        timeRange,
        searchType,
      });
    },
  });

  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value);
    // Data will be auto-loaded by useEffect
  };

  const handleSearchTypeChange = (value: string) => {
    setSearchType(value);
    // Data will be auto-loaded by useEffect
  };

  const loadPerformanceData = (
    currentTimeRange?: string,
    currentSearchType?: string
  ) => {
    if (!selectedSite) return;

    // Validate that the selected site matches the current website
    if (!isGSCSiteMatchingCurrentWebsite(selectedSite)) {
      console.warn(
        "🚫 Selected GSC site doesn't match current website, skipping data load"
      );
      toast({
        title: "Site Mismatch",
        description: "Selected GSC site doesn't match the current website.",
        variant: "destructive",
      });
      return;
    }

    // Use provided values or fall back to state
    const effectiveTimeRange = currentTimeRange || timeRange;
    const effectiveSearchType = currentSearchType || searchType;

    // Calculate days to check based on selection
    // For data availability, we want to check enough days to get the required data
    const daysToCheck =
      effectiveTimeRange === "24hours"
        ? 10 // Check last 10 days to find recent data
        : effectiveTimeRange === "7days"
          ? 14 // Check last 14 days to get 7 days of data
          : effectiveTimeRange === "28days"
            ? 35 // Check last 35 days to get 28 days of data
            : effectiveTimeRange === "3months"
              ? 100 // Check last 100 days to get 90 days of data
              : 100;

    fetchPerformanceData({
      site_url: selectedSite,
      type: effectiveSearchType,
      days_to_check: daysToCheck,
    });
  };

  // Auto-load data when component mounts or context changes
  useEffect(() => {
    const currentContext = {
      site: selectedSite,
      timeRange,
      searchType,
    };

    // Check if we need to load data for the current context
    const needsDataLoad =
      selectedSite &&
      isGSCSiteMatchingCurrentWebsite(selectedSite) &&
      (!hasLoadedData ||
        lastLoadedContext.site !== currentContext.site ||
        lastLoadedContext.timeRange !== currentContext.timeRange ||
        lastLoadedContext.searchType !== currentContext.searchType);

    if (needsDataLoad && !isLoading) {
      loadPerformanceData();
    }
  }, [
    selectedSite,
    timeRange,
    searchType,
    hasLoadedData,
    lastLoadedContext,
    isLoading,
  ]);

  // Reset loading state when selectedSite changes
  useEffect(() => {
    if (selectedSite !== lastLoadedContext.site) {
      setHasLoadedData(false);
    }
  }, [selectedSite, lastLoadedContext.site]);

  const toggleMetric = (metric: keyof typeof enabledMetrics) => {
    setEnabledMetrics((prev) => {
      const newState = {
        ...prev,
        [metric]: !prev[metric],
      };

      return newState;
    });
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const formatSiteUrl = (url: string) => {
    if (url?.startsWith("sc-domain:")) {
      return url.replace("sc-domain:", "");
    }
    return url?.replace(/^https?:\/\//, "").replace(/\/$/, "");
  };

  // Don't show if no selected site or if site doesn't match current website
  if (!selectedSite || !isGSCSiteMatchingCurrentWebsite(selectedSite)) {
    return null;
  }

  return (
    <div className="w-full">
      <Divider className="my-2 border-gray-200 dark:border-gray-800" />
      <CardHeader className="pb-3 grid grid-cols-1 md:grid-cols-3 items-center justify-between">
        <div className="">
          <CardTitle className="text-lg">Performance Overview</CardTitle>
          <div className="text-xs text-gray-500">
            Last update: {new Date().toLocaleString()}
          </div>
        </div>

        {/* Filter Controls */}
        <div className="flex  justify-center pt-2 md:pt-0">
          <div className="flex items-center space-x-2">
            {/* Time Range Selector */}
            <div className="flex border rounded-md">
              {[
                { value: "24hours", label: "24 hours" },
                { value: "7days", label: "7 days" },
                { value: "28days", label: "28 days" },
                { value: "3months", label: "3 months" },
              ].map((option) => (
                <button
                  key={option.value}
                  onClick={() => handleTimeRangeChange(option.value)}
                  className={`
                    px-3 py-1 text-sm font-medium transition-all duration-200
                    ${
                      timeRange === option.value
                        ? "bg-[#1279b4] text-white"
                        : "text-gray-700 hover:bg-gray-100"
                    }
                    ${option.value === "24hours" ? "rounded-l-lg" : ""}
                    ${option.value === "3months" ? "rounded-r-lg" : ""}
                  `}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2 justify-end">
          {/* Search Type Selector */}
          <Select value={searchType} onValueChange={handleSearchTypeChange}>
            <SelectTrigger className="w-32 h-8 text-xs border-none bg-gray-100 dark:bg-gray-800 rounded-md">
              <SelectValue placeholder="Search type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="web">Web</SelectItem>
              <SelectItem value="image">Image</SelectItem>
              <SelectItem value="video">Video</SelectItem>
              <SelectItem value="news">News</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>

      <CardContent>
        {/* Performance Metrics Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {isLoading ? (
            // Skeleton loading state
            <>
              <MetricCardSkeleton />
              <MetricCardSkeleton />
              <MetricCardSkeleton />
              <MetricCardSkeleton />
            </>
          ) : (
            // Actual metric cards
            <>
              <MetricCard
                title="Total clicks"
                value={formatNumber(performanceData.clicks)}
                icon={<Mouse className="h-4 w-4" />}
                color=""
                enabled={enabledMetrics.clicks}
                onToggle={() => toggleMetric("clicks")}
              />

              <MetricCard
                title="Total impressions"
                value={formatNumber(performanceData.impressions)}
                icon={<Eye className="h-4 w-4" />}
                color=""
                enabled={enabledMetrics.impressions}
                onToggle={() => toggleMetric("impressions")}
              />

              <MetricCard
                title="Average CTR"
                value={`${performanceData.ctr.toFixed(1)}%`}
                icon={<TrendingUp className="h-4 w-4" />}
                color=""
                enabled={enabledMetrics.ctr}
                onToggle={() => toggleMetric("ctr")}
              />

              <MetricCard
                title="Average position"
                value={performanceData.position.toFixed(1)}
                icon={<MapPin className="h-4 w-4" />}
                color=""
                enabled={enabledMetrics.position}
                onToggle={() => toggleMetric("position")}
              />
            </>
          )}
        </div>

        {/* Performance Chart */}
        <div className="mt-4">
          {isLoading ? (
            // Chart skeleton
            <div className="w-full border rounded-lg">
              <div className="p-6 border-b">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Skeleton className="h-5 w-5" />
                    <Skeleton className="h-6 w-32" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                  <Skeleton className="h-8 w-8" />
                </div>
              </div>
              <div className="p-6">
                <Skeleton className="h-64 w-full" />
              </div>
            </div>
          ) : (
            <GSCPerformanceChart
              data={chartData}
              timeRange={timeRange}
              websiteUrl={currentWebsite?.url || ""}
              isLoading={isLoading}
              enabledMetrics={enabledMetrics}
              onMetricToggle={(metric: string) =>
                toggleMetric(metric as keyof typeof enabledMetrics)
              }
            />
          )}
        </div>

        {/* Refresh Data Button & Status */}
        {selectedSite && (
          <div className="mt-4 flex items-center justify-center space-x-4">
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {hasLoadedData ? (
                <span className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Data loaded automatically</span>
                </span>
              ) : (
                <span className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                  <span>Loading data...</span>
                </span>
              )}
            </div>
            {isLoading ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              <Button
                onClick={() => {
                  setHasLoadedData(false);
                  loadPerformanceData();
                }}
                disabled={isLoading}
                variant="outline"
                size="sm"
                className="text-xs"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Refresh
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </div>
  );
};

export default GSCPerformanceMetrics;
