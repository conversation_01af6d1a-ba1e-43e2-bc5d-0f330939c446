"use client";

import React, { useEffect, useState } from "react";
import {
  useCrawlWebsite,
  CrawlResponse,
  CrawlRequest,
} from "@/internal-api/seo";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Info, AlertCircle, CheckCircle } from "lucide-react";
import {
  useWebsiteByUrl,
  useProcessWebsite,
  useMyAnalyses,
} from "@/internal-api/seo/advance-seo";

interface CrawlFormProps {
  onSuccess?: (response: CrawlResponse | any) => void;
}

export default function CrawlForm({ onSuccess }: CrawlFormProps) {
  const [formData, setFormData] = useState({
    url: "",
    language: "",
    industry: "",
    max_depth: 3, // Fixed depth of 3
    max_pages: 10, // Default to 10 pages
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingAnimation, setIsLoadingAnimation] = useState(false);
  const [urlError, setUrlError] = useState<string | null>(null);
  const { toast } = useToast();

  // Update the advance-seo hooks
  const [advanceSeoUrl, setAdvanceSeoUrl] = useState<string>("");
  const [shouldFetch, setShouldFetch] = useState(false);

  // Fetch user's analyses to check if websites already exist in database
  const { data: userAnalyses } = useMyAnalyses();

  const {
    data: websiteData,
    isLoading: isWebsiteLoading,
    refetch: refetchWebsite,
    isError: isWebsiteError,
    error: websiteError,
  } = useWebsiteByUrl(advanceSeoUrl, {
    enabled: shouldFetch, // Only fetch when explicitly enabled
    retry: false, // Don't retry on error
    onError: (error: any) => {
      console.error("Error in useWebsiteByUrl:", error);
      // If we get a 404, it means the website doesn't exist yet
      if (error.response?.status === 404) {
        // Process the website if it doesn't exist
        if (formData.url) {
          const domain = extractDomain(formData.url);

          if (domain) {
            processWebsite({
              url: domain,
              industry: formData.industry || "Technology",
              language: formData.language || "en",
            });
          } else {
            console.error("Domain is empty after extraction");
            toast({
              title: "Error",
              description: "Could not extract a valid domain from the URL",
              variant: "destructive",
            });
            // Reset loading states
            setIsSubmitting(false);
            setIsLoadingAnimation(false);
          }
        } else {
          // Reset loading states
          setIsSubmitting(false);
          setIsLoadingAnimation(false);
        }
      } else {
        console.error("Error fetching website data:", error);
        toast({
          title: "Error",
          description: error.message || "Failed to fetch website data",
          variant: "destructive",
        });
        // Reset loading states
        setIsSubmitting(false);
        setIsLoadingAnimation(false);
      }
      // Reset the fetch flag
      setShouldFetch(false);
    },
    onSuccess: (data: any) => {
      // Keep loading animation for a minimum of 3 seconds
      setTimeout(() => {
        // Reset the fetch flag after successful fetch
        setShouldFetch(false);
        // Reset loading states
        setIsSubmitting(false);
        setIsLoadingAnimation(false);

        // If onSuccess callback is provided, pass the data
        if (onSuccess) {
          onSuccess(data);
        }
      }, 3000); // 3 second minimum loading time for better UX
    },
  });

  const { mutate: processWebsite, isLoading: isProcessing } = useProcessWebsite(
    {
      onSuccess: (data: any) => {
        toast({
          title: "Success",
          description:
            "SEO analysis has been successfully added to the database.",
        });

        // After processing, fetch the website data
        if (formData.url) {
          const domain = extractDomain(formData.url);
          setAdvanceSeoUrl(domain);

          // Add delay before fetching to ensure data is available
          setTimeout(() => {
            setShouldFetch(true);
            refetchWebsite();
          }, 1000);
        } else {
          // Reset loading states if we're not going to fetch
          setIsSubmitting(false);
          setIsLoadingAnimation(false);
        }
      },
      onError: (error: Error) => {
        toast({
          title: "Error",
          description:
            error.message ||
            "Failed to process website. Please try again later.",
          variant: "destructive",
        });
        // Reset loading states
        setIsSubmitting(false);
        setIsLoadingAnimation(false);
      },
    }
  );

  // Check if URL has prefixes that should be removed
  const hasInvalidPrefix = (url: string): boolean => {
    const lowerUrl = url.toLowerCase().trim();
    return (
      lowerUrl.startsWith("www.") ||
      lowerUrl.startsWith("http://") ||
      lowerUrl.startsWith("https://")
    );
  };

  // Validate URL format - should be just the domain without prefixes
  const validateUrlFormat = (url: string): string | null => {
    if (!url || url.trim() === "") {
      return null; // Empty URL is handled separately
    }

    if (hasInvalidPrefix(url)) {
      return "Please enter only the base domain (e.g., 'example.com' instead of 'www.example.com' or 'https://example.com')";
    }

    // Check if it's a valid domain format
    const domainRegex =
      /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+$/;
    if (!domainRegex.test(url)) {
      return "Please enter a valid domain format (e.g., 'example.com')";
    }

    return null; // No errors
  };

  // Extract domain from URL (remove protocol, www, and path)
  const extractDomain = (url: string): string => {
    if (!url || url.trim() === "") {
      return "";
    }

    try {
      // First ensure URL has a protocol
      let processedUrl = url;
      if (!url.startsWith("http://") && !url.startsWith("https://")) {
        processedUrl = "https://" + url;
      }

      // Parse the URL
      const urlObj = new URL(processedUrl);

      // Extract domain (remove www if present)
      const domain = urlObj.hostname.replace(/^www\./, "");

      // Return empty string if domain is empty
      return domain || "";
    } catch (error) {
      console.error("Error extracting domain:", error);
      // Return original input if parsing fails, but ensure it's not empty
      const fallbackDomain = url
        .replace(/^https?:\/\//, "")
        .replace(/^www\./, "")
        .split("/")[0];
      return fallbackDomain || "";
    }
  };

  // Normalize a URL to a standard format for comparison
  const normalizeUrl = (url: string): string => {
    return extractDomain(url).toLowerCase().trim();
  };

  // Check if a website already exists in the database
  const websiteExistsInDatabase = (
    url: string
  ): boolean | { id: number; url: string } => {
    if (!userAnalyses || !url) return false;

    const normalizedInput = normalizeUrl(url);

    // Return false if normalized input is empty
    if (!normalizedInput) return false;

    // Find any website that matches the normalized domain
    const existingWebsite = userAnalyses.find(
      (website: any) => normalizeUrl(website.url) === normalizedInput
    );

    return existingWebsite ? existingWebsite : false;
  };

  const validateUrl = (url: string): string => {
    if (!url || url.trim() === "") return "";
    // Add protocol if missing
    if (!url.startsWith("http://") && !url.startsWith("https://")) {
      return `https://${url}`;
    }
    return url;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate URL format before submission
    const urlValidationError = validateUrlFormat(formData.url);
    if (urlValidationError) {
      setUrlError(urlValidationError);
      toast({
        title: "Invalid URL Format",
        description: urlValidationError,
        variant: "destructive",
      });
      return;
    }

    // Keep the existing functionality for empty URL check
    if (!formData.url || formData.url.trim() === "") {
      toast({
        title: "Error",
        description: "Please enter a URL",
        variant: "destructive",
      });
      return;
    }

    // Since we've validated the URL is in the correct format (no prefixes),
    // we can use it directly as the domain
    const domain = formData.url.trim();

    // Check if the website already exists in the database
    const existingWebsite = websiteExistsInDatabase(domain);

    try {
      // Set loading states
      setIsSubmitting(true);
      setIsLoadingAnimation(true);

      if (existingWebsite && typeof existingWebsite !== "boolean") {
        toast({
          title: "Website found",
          description: "Fetching existing data from the database",
        });

        // Use the existing website's domain to ensure consistent URL format
        const existingDomain = extractDomain(existingWebsite.url);

        // Set the new domain first

        setAdvanceSeoUrl(existingDomain);

        // Short delay to ensure state is updated before triggering fetch
        setTimeout(() => {
          // Now that advanceSeoUrl is set, enable fetching
          if (existingDomain && existingDomain.trim() !== "") {
            setShouldFetch(true);

            // Trigger the fetch

            refetchWebsite();
          } else {
            console.error("Domain is empty or invalid, cannot fetch data");
            setIsSubmitting(false);
            setIsLoadingAnimation(false);
            toast({
              title: "Error",
              description: "Invalid domain, please check the URL and try again",
              variant: "destructive",
            });
          }
        }, 100);
      } else {
        // Website doesn't exist yet, proceed with normal flow
        // Set the new domain first

        setAdvanceSeoUrl(domain);

        // Short delay to ensure state is updated before triggering fetch
        setTimeout(() => {
          // Now that advanceSeoUrl is set, enable fetching
          if (domain && domain.trim() !== "") {
            setShouldFetch(true);

            // Trigger the fetch

            refetchWebsite();
          } else {
            console.error("Domain is empty or invalid, cannot fetch data");
            setIsSubmitting(false);
            setIsLoadingAnimation(false);
            toast({
              title: "Error",
              description: "Invalid domain, please check the URL and try again",
              variant: "destructive",
            });
          }
        }, 100);
      }
    } catch (error) {
      console.error("Error in handleSubmit:", error);
      toast({
        title: "Error",
        description: "An error occurred while submitting the form",
        variant: "destructive",
      });

      // Reset loading states
      setIsSubmitting(false);
      setIsLoadingAnimation(false);
    }
  };

  // Add a safety mechanism to reset loading state after a timeout
  React.useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if (isSubmitting) {
      // If still submitting after 30 seconds, reset the state
      timeoutId = setTimeout(() => {
        setIsSubmitting(false);
        setIsLoadingAnimation(false);
      }, 30000);
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [isSubmitting]);

  return (
    <Card className="bg-background shadow-none rounded-md">
      <CardContent className="p-4">
        <form onSubmit={handleSubmit}>
          {/* URL Input and Submit Button in a single row */}
          <div className="flex flex-col md:flex-row gap-4 items-center pb-6 md:pb-0">
            <div className="flex-1 relative">
              <Input
                className={`${urlError ? "border-red-500 focus-visible:ring-red-500" : ""} w-full h-10`}
                placeholder="example.com"
                value={formData.url}
                onChange={(e) => {
                  const newUrl = e.target.value;
                  setFormData((prev) => ({ ...prev, url: newUrl }));
                  setUrlError(validateUrlFormat(newUrl));
                }}
                required
                disabled={isSubmitting || isLoadingAnimation}
              />
              {urlError && (
                <div className="flex items-start gap-2 text-sm text-red-500 absolute top-full left-0 mt-1">
                  <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>{urlError}</span>
                </div>
              )}
            </div>

            <Button
              type="submit"
              className="shrink-0 h-10 bg-[#1279b4] hover:bg-[#002A41] dark:text-white"
              disabled={
                isSubmitting ||
                isLoadingAnimation ||
                !formData.url ||
                urlError !== null
              }
            >
              {isSubmitting || isLoadingAnimation ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading Data...
                </>
              ) : (
                "Start Analysis"
              )}
            </Button>
          </div>
        </form>
      </CardContent>
      <CardFooter>
        <div className="text-xs text-gray-600 space-y-2">
          <p className="flex items-center gap-2">
            <strong>
              SEO analysis by{" "}
              <span className="text-black-600">
                <em>Satoloc Insight</em>
              </span>
            </strong>
          </p>
          <p>
            This feature is currently in{" "}
            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">
              BETA
            </span>{" "}
            as we continuously enhance its capabilities. Our advanced crawler
            analyzes your website's technical SEO, content structure, and
            performance metrics to provide actionable insights.
          </p>
          <p className="flex items-center gap-2 text-blue-600">
            <em>
              Coming soon: AI-powered analysis with our custom LLM for even more
              precise, industry-specific recommendations.
            </em>
          </p>
        </div>
      </CardFooter>
    </Card>
  );
}
