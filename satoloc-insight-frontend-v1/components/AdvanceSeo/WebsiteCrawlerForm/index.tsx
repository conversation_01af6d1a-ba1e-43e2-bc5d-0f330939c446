import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const WebsiteCrawlerForm = () => {
  const [url, setUrl] = useState("");
  const [language, setLanguage] = useState("");
  const [industry, setIndustry] = useState("");
  const [pageLimit, setPageLimit] = useState("10 pages");

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
  };

  return (
    <Card className="w-full max-w-4xl mx-auto bg-white shadow-sm">
      <CardContent className="p-6">
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <div className="flex items-center rounded-md border border-gray-200 bg-white focus-within:ring-1 focus-within:ring-blue-500 focus-within:border-blue-500">
              <div className="px-3 text-gray-400 flex items-center">
                https://
              </div>
              <Input
                type="text"
                placeholder="satoloc.com"
                className="flex-1 border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
              />
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <Select value={language} onValueChange={setLanguage}>
              <SelectTrigger className="border bg-white">
                <SelectValue placeholder="Language" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="tr">Turkish</SelectItem>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="es">Spanish</SelectItem>
                <SelectItem value="fr">French</SelectItem>
                <SelectItem value="de">German</SelectItem>
                <SelectItem value="it">Italian</SelectItem>
              </SelectContent>
            </Select>

            <Select value={industry} onValueChange={setIndustry}>
              <SelectTrigger className="border bg-white">
                <SelectValue placeholder="Industry" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="tech">Technology</SelectItem>
                <SelectItem value="finance">Fintech</SelectItem>
                <SelectItem value="health">Web3</SelectItem>
              </SelectContent>
            </Select>

            <Select value={pageLimit} onValueChange={setPageLimit}>
              <SelectTrigger className="border bg-white">
                <SelectValue placeholder="10 pages" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10 pages">10 pages</SelectItem>
                <SelectItem value="25 pages">25 pages</SelectItem>
                <SelectItem value="50 pages">50 pages</SelectItem>
                <SelectItem value="100 pages">100 pages</SelectItem>
                <SelectItem value="unlimited">Unlimited</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex justify-end mt-6">
            <Button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 text-white px-5 py-2 rounded"
            >
              Start Crawl
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default WebsiteCrawlerForm;
