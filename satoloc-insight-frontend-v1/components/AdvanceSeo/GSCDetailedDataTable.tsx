"use client";

import React, { useState, useMemo, useEffect } from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ChevronDown,
  ChevronUp,
  Search,
  Globe,
  FileText,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
  TrendingUp,
  Eye,
  Mouse,
  MapPin,
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { useGSCAdvancedSearchAnalytics } from "@/internal-api/seo/gsc";
import { useToast } from "@/hooks/use-toast";

// Skeleton component for loading state
const TableSkeleton: React.FC<{ rows?: number }> = ({ rows = 10 }) => (
  <div className="rounded-lg border">
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="font-semibold">
            <Skeleton className="h-4 w-24" />
          </TableHead>
          <TableHead className="text-center font-semibold">
            <div className="flex items-center justify-center">
              <Skeleton className="h-4 w-16" />
            </div>
          </TableHead>
          <TableHead className="text-center font-semibold">
            <div className="flex items-center justify-center">
              <Skeleton className="h-4 w-20" />
            </div>
          </TableHead>
          <TableHead className="text-center font-semibold">
            <div className="flex items-center justify-center">
              <Skeleton className="h-4 w-12" />
            </div>
          </TableHead>
          <TableHead className="text-center font-semibold">
            <div className="flex items-center justify-center">
              <Skeleton className="h-4 w-16" />
            </div>
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {Array.from({ length: rows }).map((_, index) => (
          <TableRow
            key={index}
            className="hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            <TableCell className="font-medium max-w-xs">
              <Skeleton className="h-4 w-full max-w-[200px]" />
            </TableCell>
            <TableCell className="text-center">
              <div className="flex justify-center">
                <Skeleton className="h-4 w-12" />
              </div>
            </TableCell>
            <TableCell className="text-center">
              <div className="flex justify-center">
                <Skeleton className="h-4 w-14" />
              </div>
            </TableCell>
            <TableCell className="text-center">
              <div className="flex justify-center">
                <Skeleton className="h-4 w-12" />
              </div>
            </TableCell>
            <TableCell className="text-center">
              <div className="flex justify-center">
                <Skeleton className="h-4 w-10" />
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  </div>
);

interface GSCDetailedDataTableProps {
  selectedSite: string | null;
  timeRange: string;
  searchType: string;
  currentWebsite?: {
    url: string;
    [key: string]: any;
  } | null;
}

interface TableData {
  key: string;
  clicks: number;
  impressions: number;
  ctr: number;
  position: number;
  [key: string]: any;
}

type DataType = "queries" | "pages" | "countries";
type SortField = "clicks" | "impressions" | "ctr" | "position";
type SortDirection = "asc" | "desc";

const GSCDetailedDataTable: React.FC<GSCDetailedDataTableProps> = ({
  selectedSite,
  timeRange,
  searchType,
  currentWebsite,
}) => {
  const { toast } = useToast();

  // Function to normalize URLs for comparison
  const normalizeUrl = (url: string): string => {
    if (!url) return "";
    // Remove protocol, www, and trailing slash for comparison
    return url
      .replace(/^https?:\/\//, "")
      .replace(/^www\./, "")
      .replace(/\/$/, "")
      .toLowerCase();
  };

  // Function to check if a GSC site matches the current website
  const isGSCSiteMatchingCurrentWebsite = (gscSiteUrl: string): boolean => {
    if (!currentWebsite?.url || !gscSiteUrl) return false;

    const currentUrl = normalizeUrl(currentWebsite.url);
    const gscUrl = normalizeUrl(gscSiteUrl.replace("sc-domain:", ""));

    // Check if they match exactly or if one contains the other
    return (
      currentUrl === gscUrl ||
      currentUrl.includes(gscUrl) ||
      gscUrl.includes(currentUrl)
    );
  };

  const [isExpanded, setIsExpanded] = useState(() => {
    // Load expanded state from localStorage, default to true
    try {
      const saved = localStorage.getItem("gsc-detailed-table-expanded");
      return saved !== null ? JSON.parse(saved) : true;
    } catch {
      return true;
    }
  });
  const [activeDataType, setActiveDataType] = useState<DataType>("queries");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortField, setSortField] = useState<SortField>("clicks");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");
  const [tableData, setTableData] = useState<TableData[]>([]);
  const [totalRows, setTotalRows] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Persist expanded state to localStorage
  useEffect(() => {
    try {
      localStorage.setItem(
        "gsc-detailed-table-expanded",
        JSON.stringify(isExpanded)
      );
    } catch (error) {
      console.warn("Failed to save expanded state to localStorage:", error);
    }
  }, [isExpanded]);

  const { mutate: fetchAdvancedData } = useGSCAdvancedSearchAnalytics({
    onSuccess: (data: any) => {
      if (data.success && data.data?.rows && Array.isArray(data.data.rows)) {
        const processedData = data.data.rows.map((row: any, index: number) => {
          // Extract the key based on dimension type
          let displayKey = "Unknown";

          if (row.keys && Array.isArray(row.keys) && row.keys.length > 0) {
            displayKey = row.keys[0];

            // Clean up the display for different dimension types
            if (activeDataType === "pages") {
              // For pages, show clean URL
              displayKey = displayKey
                .replace(/^https?:\/\//, "")
                .replace(/\/$/, "");
            } else if (activeDataType === "countries") {
              // For countries, capitalize first letter
              displayKey =
                displayKey.charAt(0).toUpperCase() +
                displayKey.slice(1).toLowerCase();
            }
            // For queries, keep as-is
          }

          const processedRow = {
            id: index,
            key: displayKey,
            clicks: parseInt(row.clicks) || 0,
            impressions: parseInt(row.impressions) || 0,
            ctr: parseFloat(row.ctr) * 100 || 0, // Convert to percentage
            position: parseFloat(row.position) || 0,
          };

          return processedRow;
        });

        setTableData(processedData);
        setTotalRows(processedData.length);
        setCurrentPage(1); // Reset to first page
      } else {
        setTableData([]);
        setTotalRows(0);
      }

      setIsLoading(false);
    },
    onError: (error: Error) => {
      setIsLoading(false);
      toast({
        title: "Error Loading Data",
        description: error.message || "Failed to load detailed analytics data.",
        variant: "destructive",
      });
    },
  });

  const getDimensionFromDataType = (dataType: DataType): string => {
    switch (dataType) {
      case "queries":
        return "query";
      case "pages":
        return "page";
      case "countries":
        return "country";
      default:
        return "query";
    }
  };

  const getDateRange = () => {
    // Use the same date calculation logic as GSCPerformanceMetrics
    const today = new Date();
    const endDate = new Date(today);
    endDate.setDate(today.getDate() - 3); // End 3 days ago (GSC data delay)

    const startDate = new Date(endDate);

    switch (timeRange) {
      case "24hours":
        // For 24 hours, use the same day (endDate = startDate)
        startDate.setTime(endDate.getTime());
        break;
      case "7days":
        startDate.setDate(endDate.getDate() - 6); // 7 days including end date
        break;
      case "28days":
        startDate.setDate(endDate.getDate() - 27); // 28 days including end date
        break;
      case "3months":
        startDate.setDate(endDate.getDate() - 89); // 90 days including end date
        break;
      default:
        startDate.setDate(endDate.getDate() - 27); // Default to 28 days
    }

    const dateRange = {
      start_date: startDate.toISOString().split("T")[0],
      end_date: endDate.toISOString().split("T")[0],
    };

    return dateRange;
  };

  const loadData = () => {
    if (!selectedSite || !activeDataType) return;

    // Validate that the selected site matches the current website
    if (!isGSCSiteMatchingCurrentWebsite(selectedSite)) {
      console.warn(
        "🚫 Selected GSC site doesn't match current website, skipping data load"
      );
      toast({
        title: "Site Mismatch",
        description: "Selected GSC site doesn't match the current website.",
        variant: "destructive",
      });
      return;
    }

    // Clear existing data first
    setTableData([]);
    setTotalRows(0);
    setIsLoading(true);

    const { start_date, end_date } = getDateRange();
    const dimension = getDimensionFromDataType(activeDataType);

    const requestParams = {
      site_url: selectedSite,
      start_date,
      end_date,
      dimensions: [dimension],
      type: searchType,
      row_limit: 100,
      start_row: 0,
      aggregation_type: activeDataType === "pages" ? "byPage" : "auto",
      data_state: "final",
    };

    fetchAdvancedData(requestParams);
  };

  const handleDataTypeChange = (newDataType: DataType) => {
    // Immediately clear existing data to show loading state
    setTableData([]);
    setTotalRows(0);
    setCurrentPage(1);

    // Set the new data type - this will trigger the useEffect to reload data
    setActiveDataType(newDataType);
  };

  // Effect to reload data when key parameters change and component is expanded
  useEffect(() => {
    if (isExpanded && selectedSite && activeDataType) {
      // Validate that the selected site matches the current website
      if (!isGSCSiteMatchingCurrentWebsite(selectedSite)) {
        console.warn(
          "🚫 Selected GSC site doesn't match current website, skipping data load"
        );
        return;
      }

      // Clear existing data and set loading state
      setTableData([]);
      setTotalRows(0);
      setIsLoading(true);

      // Get current date range and dimension
      const { start_date, end_date } = getDateRange();
      const dimension = getDimensionFromDataType(activeDataType);

      const requestParams = {
        site_url: selectedSite,
        start_date,
        end_date,
        dimensions: [dimension],
        type: searchType,
        row_limit: 100,
        start_row: 0,
        aggregation_type: activeDataType === "pages" ? "byPage" : "auto",
        data_state: "final",
      };

      fetchAdvancedData(requestParams);
    }
  }, [activeDataType, isExpanded, selectedSite, timeRange, searchType]);

  const handleSort = (field: SortField) => {
    const newDirection =
      sortField === field && sortDirection === "desc" ? "asc" : "desc";
    setSortField(field);
    setSortDirection(newDirection);
  };

  const handleExpand = () => {
    const newExpandedState = !isExpanded;
    setIsExpanded(newExpandedState);

    // Only load data when expanding (not when collapsing)
    if (newExpandedState && selectedSite) {
      loadData();
    }
  };

  // Sort and paginate data
  const sortedData = useMemo(() => {
    const sorted = [...tableData].sort((a, b) => {
      const aVal = a[sortField];
      const bVal = b[sortField];
      const multiplier = sortDirection === "asc" ? 1 : -1;
      return (aVal - bVal) * multiplier;
    });
    return sorted;
  }, [tableData, sortField, sortDirection]);

  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return sortedData.slice(startIndex, startIndex + pageSize);
  }, [sortedData, currentPage, pageSize]);

  const totalPages = Math.ceil(totalRows / pageSize);

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const formatCtr = (ctr: number) => `${ctr.toFixed(2)}%`;
  const formatPosition = (position: number) => position.toFixed(1);

  const getDataTypeIcon = (dataType: DataType) => {
    switch (dataType) {
      case "queries":
        return <Search className="h-4 w-4" />;
      case "pages":
        return <FileText className="h-4 w-4" />;
      case "countries":
        return <Globe className="h-4 w-4" />;
    }
  };

  const getDataTypeLabel = (dataType: DataType) => {
    switch (dataType) {
      case "queries":
        return "Top Queries";
      case "pages":
        return "Top Pages";
      case "countries":
        return "Top Countries";
    }
  };

  const truncateText = (text: string, maxLength: number = 50) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  const formatSiteUrl = (url: string) => {
    // Clean up the URL display
    if (url?.startsWith("sc-domain:")) {
      return url.replace("sc-domain:", "");
    }
    return url?.replace(/^https?:\/\//, "").replace(/\/$/, "");
  };

  // Don't show if no selected site or if site doesn't match current website
  if (!selectedSite || !isGSCSiteMatchingCurrentWebsite(selectedSite)) {
    return null;
  }

  return (
    <Card className="w-full shadow-none rounded-md">
      <CardHeader className="pb-3 grid grid-cols-1 md:grid-cols-3 items-center justify-between">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              {getDataTypeIcon(activeDataType)}
              <CardTitle className="text-lg">
                {getDataTypeLabel(activeDataType)}
              </CardTitle>
              <span className="text-sm text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-950/50 px-2 py-1 rounded-md border border-blue-200 dark:border-blue-800">
                {formatSiteUrl(selectedSite || "")}
              </span>
              <span className="text-xs text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded-md">
                {timeRange === "24hours"
                  ? "24h"
                  : timeRange === "7days"
                    ? "7d"
                    : timeRange === "28days"
                      ? "28d"
                      : "3m"}
              </span>
            </div>
            {totalRows > 0 && (
              <span className="text-sm text-gray-600 dark:text-gray-300">
                ({totalRows} total)
              </span>
            )}
          </div>
        </div>

        {/* Data Type Selector */}
        <div className="flex items-center space-x-2 pt-2 md:pt-0 justify-center">
          <div className="flex border rounded-lg">
            {(["queries", "pages", "countries"] as DataType[]).map(
              (dataType) => (
                <button
                  key={dataType}
                  onClick={() => handleDataTypeChange(dataType)}
                  disabled={isLoading}
                  className={`
                  px-3 py-1 text-sm font-medium transition-all duration-200 flex items-center space-x-1
                  ${
                    activeDataType === dataType
                      ? "bg-[#1279b4] text-white"
                      : "text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                  }
                  ${dataType === "queries" ? "rounded-l-lg" : ""}
                  ${dataType === "countries" ? "rounded-r-lg" : ""}
                  ${isLoading ? "opacity-50 cursor-not-allowed" : ""}
                `}
                >
                  {getDataTypeIcon(dataType)}
                  <span>
                    {dataType.charAt(0).toUpperCase() + dataType.slice(1)}
                  </span>
                  {isLoading && activeDataType === dataType && (
                    <div className="animate-spin rounded-full h-3 w-3 border border-white border-t-transparent"></div>
                  )}
                </button>
              )
            )}
          </div>
        </div>
        <div className="flex items-center space-x-2 justify-end">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleExpand}
            className="h-8 w-8 p-0 bg-gray-100 dark:bg-gray-800 rounded-md"
          >
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span>Loading {activeDataType}...</span>
              </div>
              <TableSkeleton rows={pageSize} />

              {/* Pagination Skeleton */}
              <div className="flex items-center justify-between pt-4">
                <div className="flex items-center space-x-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-8 w-16" />
                </div>
                <div className="flex items-center space-x-2">
                  <Skeleton className="h-4 w-32" />
                  <div className="flex items-center space-x-1">
                    <Skeleton className="h-8 w-8" />
                    <Skeleton className="h-8 w-8" />
                  </div>
                </div>
              </div>
            </div>
          ) : tableData.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <Search className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-2" />
                <p className="text-gray-600 dark:text-gray-300">
                  No {activeDataType} data available
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadData}
                  className="mt-2"
                >
                  Refresh Data
                </Button>
              </div>
            </div>
          ) : (
            <>
              {/* Table */}
              <div className="rounded-lg border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="font-semibold">
                        {getDataTypeLabel(activeDataType).slice(4)}{" "}
                        {/* Remove "Top " */}
                      </TableHead>
                      <TableHead
                        className="text-center cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 font-semibold"
                        onClick={() => handleSort("clicks")}
                      >
                        <div className="flex items-center justify-center space-x-1">
                          <Mouse className="h-4 w-4 text-blue-600" />
                          <span>Clicks</span>
                          <ArrowUpDown className="h-3 w-3" />
                        </div>
                      </TableHead>
                      <TableHead
                        className="text-center cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 font-semibold"
                        onClick={() => handleSort("impressions")}
                      >
                        <div className="flex items-center justify-center space-x-1">
                          <Eye className="h-4 w-4 text-green-600" />
                          <span>Impressions</span>
                          <ArrowUpDown className="h-3 w-3" />
                        </div>
                      </TableHead>
                      <TableHead
                        className="text-center cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 font-semibold"
                        onClick={() => handleSort("ctr")}
                      >
                        <div className="flex items-center justify-center space-x-1">
                          <TrendingUp className="h-4 w-4 text-orange-600" />
                          <span>CTR</span>
                          <ArrowUpDown className="h-3 w-3" />
                        </div>
                      </TableHead>
                      <TableHead
                        className="text-center cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 font-semibold"
                        onClick={() => handleSort("position")}
                      >
                        <div className="flex items-center justify-center space-x-1">
                          <MapPin className="h-4 w-4 text-red-600" />
                          <span>Position</span>
                          <ArrowUpDown className="h-3 w-3" />
                        </div>
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paginatedData.map((row, index) => (
                      <TableRow
                        key={row.id}
                        className="hover:bg-gray-50 dark:hover:bg-gray-700"
                      >
                        <TableCell className="font-medium max-w-xs">
                          <div className="truncate" title={row.key}>
                            {truncateText(row.key)}
                          </div>
                        </TableCell>
                        <TableCell className="text-center text-blue-600 font-semibold">
                          {formatNumber(row.clicks)}
                        </TableCell>
                        <TableCell className="text-center text-green-600 font-semibold">
                          {formatNumber(row.impressions)}
                        </TableCell>
                        <TableCell className="text-center text-orange-600 font-semibold">
                          {formatCtr(row.ctr)}
                        </TableCell>
                        <TableCell className="text-center text-red-600 font-semibold">
                          {formatPosition(row.position)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between pt-4">
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    Rows per page:
                  </span>
                  <Select
                    value={pageSize.toString()}
                    onValueChange={(value) => {
                      setPageSize(Number(value));
                      setCurrentPage(1);
                    }}
                  >
                    <SelectTrigger className="w-16 h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="25">25</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    {(currentPage - 1) * pageSize + 1}-
                    {Math.min(currentPage * pageSize, totalRows)} of {totalRows}
                  </span>
                  <div className="flex items-center space-x-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setCurrentPage(Math.max(1, currentPage - 1))
                      }
                      disabled={currentPage === 1}
                      className="h-8 w-8 p-0"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setCurrentPage(Math.min(totalPages, currentPage + 1))
                      }
                      disabled={currentPage === totalPages}
                      className="h-8 w-8 p-0"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </>
          )}
        </CardContent>
      )}
    </Card>
  );
};

export default GSCDetailedDataTable;
