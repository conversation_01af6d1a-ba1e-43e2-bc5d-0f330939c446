import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Bot,
  Sparkles,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Lightbulb,
  Target,
  Zap,
  X,
  Loader2,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import {
  useGenerateSEOAIInsights,
  SEOAIInsight,
  extractEssentialData,
} from "@/internal-api/seo/seo-ai-assistant";

interface SEOAIAssistantProps {
  componentType:
    | "performance-chart"
    | "core-web-vitals"
    | "gsc-data"
    | "competitor-analysis"
    | "technical-analysis";
  componentData?: any;
  metrics?: any;
  websiteUrl: string; // Required for API calls
  isVisible?: boolean;
  position?: "bottom-right" | "bottom-left" | "bottom-center";
  size?: "sm" | "md" | "lg";
}

const SEOAIAssistant: React.FC<SEOAIAssistantProps> = ({
  componentType,
  componentData,
  metrics,
  websiteUrl,
  isVisible = true,
  position = "bottom-right",
  size = "md",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [insights, setInsights] = useState<SEOAIInsight[]>([]);
  const { toast } = useToast();

  // Use the real API hook
  const generateInsightsMutation = useGenerateSEOAIInsights({
    onSuccess: (data) => {
      if (data.success && data.insights) {
        setInsights(data.insights);
        toast({
          title: "AI Analysis Complete",
          description: `Generated ${data.insights.length} SEO insights and recommendations.`,
        });
      } else {
        toast({
          title: "Analysis Failed",
          description:
            data.error || "Unable to generate AI insights. Please try again.",
          variant: "destructive",
        });
      }
    },
    onError: (error) => {
      toast({
        title: "Analysis Failed",
        description: "Unable to generate AI insights. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Real AI analysis function using the API
  const analyzeComponentData = async () => {
    if (!websiteUrl) {
      toast({
        title: "Website URL Required",
        description: "Please provide a website URL for analysis.",
        variant: "destructive",
      });
      return;
    }

    // Extract essential data to avoid circular references
    const essentialData = extractEssentialData(
      componentType,
      componentData,
      metrics
    );

    generateInsightsMutation.mutate({
      component_type: componentType,
      website_url: websiteUrl,
      additional_data: essentialData,
    });
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case "opportunity":
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-amber-600" />;
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      default:
        return <Lightbulb className="h-4 w-4 text-blue-600" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800 border-red-200";
      case "medium":
        return "bg-amber-100 text-amber-800 border-amber-200";
      default:
        return "bg-blue-100 text-blue-800 border-blue-200";
    }
  };

  const positionClasses = {
    "bottom-right": "absolute bottom-2 right-2",
    "bottom-left": "absolute bottom-2 left-2",
    "bottom-center": "absolute bottom-2 left-1/2 transform -translate-x-1/2",
  };

  const sizeClasses = {
    sm: "h-8 w-8",
    md: "h-10 w-10",
    lg: "h-12 w-12",
  };

  if (!isVisible) return null;

  return (
    <div className={positionClasses[position]}>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className={`${sizeClasses[size]} p-0 bg-[#1279b4] text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 group`}
            onClick={() => {
              setIsOpen(true);
              if (insights.length === 0) {
                analyzeComponentData();
              }
            }}
          >
            <div className="flex items-center justify-center">
              <Bot className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
              <Sparkles className="h-2 w-2 text-white/70 absolute -top-1 -right-1 animate-pulse" />
            </div>
          </Button>
        </DialogTrigger>

        <DialogContent className="max-w-2xl h-[600px] flex flex-col p-0">
          <DialogHeader className="flex-shrink-0 p-6 pb-4">
            <div className="flex items-center justify-between">
              <DialogTitle className="flex items-center space-x-2">
                <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
                  <Bot className="h-5 w-5 text-white" />
                </div>
                <div>
                  <span>Satoloc Insight AI Assistant</span>
                  <p className="text-sm text-gray-600 font-normal">
                    Smart insights for {componentType.replace("-", " ")}
                  </p>
                </div>
              </DialogTitle>
            </div>
          </DialogHeader>

          <div className="flex-1 overflow-hidden px-6">
            <ScrollArea className="h-full" type="always">
              <div className="space-y-4 pb-4 pr-4">
                {/* Analysis Status */}
                {generateInsightsMutation.isLoading && (
                  <Card className="border-blue-200 bg-blue-50">
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        <div className="animate-spin rounded-full h-6 w-6 border-2 border-blue-500 border-t-transparent"></div>
                        <div>
                          <p className="font-medium text-blue-800">
                            Analyzing your SEO data...
                          </p>
                          <p className="text-sm text-blue-600">
                            AI is examining patterns and generating insights
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Insights */}
                {insights.length > 0 && (
                  <div className="space-y-3">
                    {insights.map((insight, index) => (
                      <Card
                        key={index}
                        className="border-l-4 border-l-current"
                        style={{
                          borderLeftColor:
                            insight.type === "opportunity"
                              ? "#10b981"
                              : insight.type === "warning"
                                ? "#f59e0b"
                                : insight.type === "success"
                                  ? "#10b981"
                                  : "#3b82f6",
                        }}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between space-x-3">
                            <div className="flex items-start space-x-3 flex-1">
                              {getInsightIcon(insight.type)}
                              <div className="flex-1">
                                <div className="flex items-center space-x-2 mb-1">
                                  <h4 className="font-medium text-gray-900">
                                    {insight.title}
                                  </h4>
                                  <Badge
                                    variant="outline"
                                    className={getPriorityColor(
                                      insight.priority
                                    )}
                                  >
                                    {insight.priority}
                                  </Badge>
                                  {insight.actionable && (
                                    <Badge
                                      variant="secondary"
                                      className="bg-green-100 text-green-800"
                                    >
                                      <Target className="h-3 w-3 mr-1" />
                                      Actionable
                                    </Badge>
                                  )}
                                </div>
                                <p className="text-sm text-gray-600 mb-2">
                                  {insight.description}
                                </p>
                                {insight.metrics &&
                                  insight.metrics.length > 0 && (
                                    <div className="flex flex-wrap gap-1">
                                      {insight.metrics.map((metric, i) => (
                                        <Badge
                                          key={i}
                                          variant="outline"
                                          className="text-xs"
                                        >
                                          {metric}
                                        </Badge>
                                      ))}
                                    </div>
                                  )}
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}

                {/* Empty State */}
                {!generateInsightsMutation.isLoading &&
                  insights.length === 0 && (
                    <Card className="border-dashed border-2 border-gray-200">
                      <CardContent className="p-8 text-center">
                        <Bot className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          Ready to Analyze
                        </h3>
                        <p className="text-gray-600 mb-4">
                          Click the button below to get AI-powered SEO insights
                          and recommendations.
                        </p>
                        <Button
                          onClick={analyzeComponentData}
                          disabled={generateInsightsMutation.isLoading}
                        >
                          <Sparkles className="h-4 w-4 mr-2" />
                          Start Analysis
                        </Button>
                      </CardContent>
                    </Card>
                  )}
              </div>
            </ScrollArea>
          </div>

          {/* Quick Actions - Fixed at bottom */}
          {insights.length > 0 && (
            <div className="flex-shrink-0 flex items-center justify-between p-6 pt-4 border-t bg-white">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Zap className="h-4 w-4" />
                <span>
                  {insights.filter((i) => i.actionable).length} actionable
                  insights
                </span>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={analyzeComponentData}
                  disabled={generateInsightsMutation.isLoading}
                >
                  Re-analyze
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => setIsOpen(false)}
                >
                  Got it
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SEOAIAssistant;
