"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { useToast } from "@/hooks/use-toast";
import { useProcessWebsite } from "@/internal-api/seo/advance-seo";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";

// Industry options
const INDUSTRIES = ["Technology", "Fintech", "Web3", "Finance"];

// Language options
const LANGUAGES = ["English", "Turkish", "Chinese"];

export default function SEOAnalysisForm() {
  const [url, setUrl] = useState("");
  const [industry, setIndustry] = useState("");
  const [language, setLanguage] = useState("");
  const { toast } = useToast();
  const { data: session } = useSession();

  // Use the mutation hook
  const { mutate: processWebsite, isLoading } = useProcessWebsite({
    onSuccess: (data: any) => {
      // Reset form fields
      setUrl("");
      setIndustry("");
      setLanguage("");

      // Show success toast
      toast({
        title: "Success",
        description:
          data.message ||
          "SEO analysis has been successfully added to the database.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description:
          error.message || "Failed to process website. Please try again later.",
        variant: "destructive",
      });
    },
  });

  // Function to validate URL format
  const isValidUrl = (urlString: string) => {
    try {
      // Add protocol if missing
      if (
        !urlString.startsWith("http://") &&
        !urlString.startsWith("https://")
      ) {
        urlString = "https://" + urlString;
      }
      new URL(urlString);
      return true;
    } catch (e) {
      return false;
    }
  };

  // Extract domain from URL (remove protocol, www, and path)
  const extractDomain = (url: string): string => {
    if (!url || url.trim() === "") {
      return "";
    }
    try {
      // First ensure URL has a protocol
      let processedUrl = url;
      if (!url.startsWith("http://") && !url.startsWith("https://")) {
        processedUrl = "https://" + url;
      }
      // Parse the URL
      const urlObj = new URL(processedUrl);
      // Extract domain (remove www if present)
      const domain = urlObj.hostname.replace(/^www\./, "");
      return domain || "";
    } catch (error) {
      // Return original input if parsing fails, but ensure it's not empty
      const fallbackDomain = url
        .replace(/^https?:\/\//, "")
        .replace(/^www\./, "")
        .split("/")[0];
      return fallbackDomain || "";
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate inputs
    if (!url) {
      toast({
        title: "Error",
        description: "Please enter a URL",
        variant: "destructive",
      });
      return;
    }

    if (!industry) {
      toast({
        title: "Error",
        description: "Please select an industry",
        variant: "destructive",
      });
      return;
    }

    if (!language) {
      toast({
        title: "Error",
        description: "Please select a language",
        variant: "destructive",
      });
      return;
    }

    if (!isValidUrl(url)) {
      toast({
        title: "Error",
        description: "Please enter a valid URL",
        variant: "destructive",
      });
      return;
    }

    // Check if user is authenticated
    if (!session) {
      toast({
        title: "Error",
        description: "You must be logged in to perform this action",
        variant: "destructive",
      });
      return;
    }

    // Extract only the base domain (no protocol, no path)
    const domain = extractDomain(url);

    processWebsite({
      url: domain,
      industry,
      language,
    });
  };

  return (
    <Card className="w-full  mx-auto shadow-sm">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl text-[#1279b4]">
              Advanced SEO Analysis
            </CardTitle>
            <CardDescription className="text-xs mt-1">
              Analyze websites with real data from DataForSEO
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <form onSubmit={handleSubmit} className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-3">
            {/* Website URL - takes more space */}
            <div className="space-y-1 md:col-span-6">
              <Label
                htmlFor="url"
                className="text-xs font-medium text-[#1279b4]"
              >
                Website URL
              </Label>
              <Input
                id="url"
                placeholder="example.com"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                disabled={isLoading}
                className="h-9"
              />
            </div>

            {/* Industry and Language selectors side by side on larger screens */}
            <div className="space-y-1 md:col-span-3">
              <Label htmlFor="industry" className="text-xs font-medium">
                Industry
              </Label>
              <Select
                value={industry}
                onValueChange={setIndustry}
                disabled={isLoading}
              >
                <SelectTrigger id="industry" className="h-9">
                  <SelectValue placeholder="Industry" />
                </SelectTrigger>
                <SelectContent>
                  {INDUSTRIES.map((ind) => (
                    <SelectItem key={ind} value={ind}>
                      {ind}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-1 md:col-span-3">
              <Label htmlFor="language" className="text-xs font-medium">
                Language
              </Label>
              <Select
                value={language}
                onValueChange={setLanguage}
                disabled={isLoading}
              >
                <SelectTrigger id="language" className="h-9">
                  <SelectValue placeholder="Language" />
                </SelectTrigger>
                <SelectContent>
                  {LANGUAGES.map((lang) => (
                    <SelectItem key={lang} value={lang}>
                      {lang}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center justify-between pt-1">
            <p className="text-xs text-[#1279b4]">
              Analysis may take a few minutes to complete.
            </p>
            <Button
              type="submit"
              size="sm"
              disabled={isLoading}
              className="bg-[#003B5C] hover:bg-[#002A41]"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                  Processing...
                </>
              ) : (
                "Analyze Website"
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
