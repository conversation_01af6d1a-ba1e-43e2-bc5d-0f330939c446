import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Title,
  DialogDescription,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";

// Define interface for competitor data
interface Competitor {
  name: string;
  url: string;
  description: string;
  percentage: string;
  keywords: string[];
  rank: number;
  strategy_gaps?: { id: number; text: string }[];
  growth_opportunities?: { id: number; text: string }[];
  ranking_issues?: { id: number; title: string; description: string }[];
  content_recommendations?: {
    id: number;
    title: string;
    description: string;
    impact: string;
    estimated_hours: number;
    is_opportunity: boolean;
  }[];
}

interface CompetitorStrategyModalProps {
  competitor: Competitor | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function CompetitorStrategyModal({
  competitor,
  isOpen,
  onClose,
}: CompetitorStrategyModalProps) {
  if (!competitor) return null;

  // Intent types for the gap analysis
  const intentTypes = ["I", "N", "T", "C"];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center justify-between">
            <span>Competitor Battle Map: {competitor.name}</span>
            <Badge
              variant="outline"
              className="bg-indigo-100 text-indigo-800 hover:bg-indigo-200"
            >
              Insights
            </Badge>
          </DialogTitle>
          <DialogDescription>
            Detailed competitive analysis and strategy recommendations
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="intent" className="mt-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="intent">Intent Gap Analysis</TabsTrigger>
            <TabsTrigger value="quality">Content Quality</TabsTrigger>
          </TabsList>

          {/* Intent Gap Analysis Tab */}
          <TabsContent
            value="intent"
            className="mt-4 max-h-[60vh] overflow-y-auto"
          >
            <div className="bg-gray-50 p-4 rounded-md">
              <h3 className="font-semibold mb-4">
                Content Intent Gap Analysis
              </h3>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="text-left p-2">Keyword</th>
                      <th className="text-left p-2">Competitor Intent</th>
                      <th className="text-left p-2">Your Intent</th>
                    </tr>
                  </thead>
                  <tbody>
                    {competitor.keywords.slice(0, 3).map((keyword, idx) => (
                      <tr
                        key={idx}
                        className={idx % 2 === 0 ? "bg-gray-50" : "bg-white"}
                      >
                        <td className="p-2">{keyword}</td>
                        <td className="p-2">
                          <div className="flex space-x-1">
                            {intentTypes.map((type, i) => (
                              <div
                                key={i}
                                className={`w-6 h-6 flex items-center justify-center text-xs ${
                                  Math.random() > 0.5
                                    ? "bg-green-500 text-white"
                                    : "bg-gray-200"
                                }`}
                              >
                                {type}
                              </div>
                            ))}
                          </div>
                        </td>
                        <td className="p-2">
                          <div className="flex space-x-1">
                            {intentTypes.map((type, i) => (
                              <div
                                key={i}
                                className={`w-6 h-6 flex items-center justify-center text-xs ${
                                  Math.random() > 0.5
                                    ? "bg-green-500 text-white"
                                    : "bg-gray-200"
                                }`}
                              >
                                {type}
                              </div>
                            ))}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="mt-4 text-xs">
                <span className="font-semibold">Intent Legend:</span>
                <span className="ml-2">I = Informational</span>
                <span className="ml-2">N = Navigational</span>
                <span className="ml-2">T = Transactional</span>
                <span className="ml-2">C = Commercial</span>
              </div>
            </div>
          </TabsContent>

          {/* Content Quality Analysis Tab */}
          <TabsContent
            value="quality"
            className="mt-4 max-h-[60vh] overflow-y-auto"
          >
            <div className="bg-gray-50 p-4 rounded-md">
              <h3 className="font-semibold text-red-600 mb-4">
                Why am I losing rankings?
              </h3>

              <div className="grid grid-cols-2 gap-4 mb-6">
                {competitor.ranking_issues &&
                competitor.ranking_issues.length > 0 ? (
                  competitor.ranking_issues.map((issue) => (
                    <div
                      key={issue.id}
                      className="bg-white p-3 rounded shadow-sm"
                    >
                      <h4 className="font-medium mb-1">{issue.title}</h4>
                      <p className="text-sm text-gray-600">
                        {issue.description}
                      </p>
                    </div>
                  ))
                ) : (
                  <div className="col-span-2 bg-white p-3 rounded shadow-sm">
                    <p className="text-sm text-gray-600">
                      No ranking issues identified yet.
                    </p>
                  </div>
                )}
              </div>

              <h3 className="font-semibold text-green-600 mb-4">
                Top recommendations
              </h3>

              <div className="space-y-3">
                {competitor.content_recommendations &&
                competitor.content_recommendations.length > 0 ? (
                  competitor.content_recommendations.map((recommendation) => (
                    <div
                      key={recommendation.id}
                      className={`${
                        recommendation.is_opportunity
                          ? "bg-amber-50 border border-amber-200"
                          : "bg-white"
                      } p-3 rounded shadow-sm flex justify-between`}
                    >
                      <div className="flex-1 pr-4">
                        <h4 className="font-medium">{recommendation.title}</h4>
                        <p className="text-sm text-gray-600">
                          {recommendation.description}
                        </p>
                      </div>
                      <div className="text-right w-36 flex-shrink-0">
                        <div
                          className={`${
                            recommendation.impact === "high"
                              ? "text-green-600"
                              : recommendation.impact === "medium"
                                ? "text-amber-600"
                                : "text-gray-600"
                          } font-medium`}
                        >
                          {recommendation.impact.charAt(0).toUpperCase() +
                            recommendation.impact.slice(1)}{" "}
                          Impact
                        </div>
                        <div className="text-xs text-gray-500">
                          Est. time: {recommendation.estimated_hours} hours
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="bg-white p-3 rounded shadow-sm">
                    <p className="text-sm text-gray-600">
                      No content recommendations available yet.
                    </p>
                  </div>
                )}

                {competitor.content_recommendations &&
                  competitor.content_recommendations.some(
                    (r) => r.is_opportunity
                  ) && (
                    <div className="bg-amber-50 border border-amber-200 p-3 rounded shadow-sm">
                      <div className="flex items-center">
                        <div className="text-amber-500 mr-2">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                        <div>
                          <h4 className="font-medium">
                            Opportunity: Focus on high-impact recommendations
                            first
                          </h4>
                          <p className="text-sm">
                            Prioritize items marked as opportunities
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
