import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import {
  Loader2,
  AlertCircle,
  Globe,
  BarChart3,
  ExternalLink,
  CheckCircle2,
  RefreshCw,
} from "lucide-react";
import { useQueryClient } from "@tanstack/react-query";
import {
  useGSCConnectionStatus,
  useGSCSites,
  useGSCDataAvailability,
} from "@/internal-api/seo/gsc";
import GSCPerformanceMetrics from "./GSCPerformanceMetrics";
import GSCDetailedDataTable from "./GSCDetailedDataTable";

interface GSCPerformanceDataProps {
  currentWebsite?: {
    url: string;
    [key: string]: any;
  } | null;
}

const GSCPerformanceData: React.FC<GSCPerformanceDataProps> = ({
  currentWebsite,
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedSite, setSelectedSite] = useState<string | null>(null);
  // Shared state for time range and search type between components
  const [timeRange, setTimeRange] = useState("3months");
  const [searchType, setSearchType] = useState("web");
  const [lastConnectionCheck, setLastConnectionCheck] = useState<number>(0);

  // Function to normalize URLs for comparison
  const normalizeUrl = (url: string): string => {
    if (!url) return "";
    // Remove protocol, www, and trailing slash for comparison
    return url
      .replace(/^https?:\/\//, "")
      .replace(/^www\./, "")
      .replace(/\/$/, "")
      .toLowerCase();
  };

  // Function to check if a GSC site matches the current website
  const isGSCSiteMatchingCurrentWebsite = (gscSiteUrl: string): boolean => {
    if (!currentWebsite?.url || !gscSiteUrl) return false;

    const currentUrl = normalizeUrl(currentWebsite.url);
    const gscUrl = normalizeUrl(gscSiteUrl.replace("sc-domain:", ""));

    // Check if they match exactly or if one contains the other
    return (
      currentUrl === gscUrl ||
      currentUrl.includes(gscUrl) ||
      gscUrl.includes(currentUrl)
    );
  };

  // Load persisted state from localStorage on component mount
  useEffect(() => {
    try {
      const savedSite = localStorage.getItem("gsc-selected-site");
      const savedTimeRange = localStorage.getItem("gsc-time-range");
      const savedSearchType = localStorage.getItem("gsc-search-type");

      if (savedSite) {
        setSelectedSite(savedSite);
      }
      if (savedTimeRange) {
        setTimeRange(savedTimeRange);
      }
      if (savedSearchType) {
        setSearchType(savedSearchType);
      }
    } catch (error) {
      console.warn("Failed to load GSC preferences from localStorage:", error);
    }
  }, []);

  // Persist state changes to localStorage
  useEffect(() => {
    try {
      if (selectedSite) {
        localStorage.setItem("gsc-selected-site", selectedSite);
      } else {
        localStorage.removeItem("gsc-selected-site");
      }
    } catch (error) {
      console.warn("Failed to save selected site to localStorage:", error);
    }
  }, [selectedSite]);

  useEffect(() => {
    try {
      localStorage.setItem("gsc-time-range", timeRange);
    } catch (error) {
      console.warn("Failed to save time range to localStorage:", error);
    }
  }, [timeRange]);

  useEffect(() => {
    try {
      localStorage.setItem("gsc-search-type", searchType);
    } catch (error) {
      console.warn("Failed to save search type to localStorage:", error);
    }
  }, [searchType]);

  // Get connection status with proper refetch interval
  const {
    data: connectionStatus,
    isLoading: isStatusLoading,
    refetch: refetchConnectionStatus,
  } = useGSCConnectionStatus({
    refetchInterval: (data: any) => {
      // Refetch more frequently if not connected
      return data?.is_connected ? 30000 : 5000; // 30s if connected, 5s if not
    },
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  });

  // Get sites (only when connected)
  const isConnected =
    connectionStatus?.is_connected && connectionStatus?.api_enabled;
  const {
    data: sitesData,
    isLoading: isSitesLoading,
    refetch: refetchSites,
  } = useGSCSites(isConnected, {
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    staleTime: 30000, // Consider data stale after 30 seconds
  });

  // Force refresh function
  const forceRefresh = async () => {
    try {
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ["gscConnectionStatus"] }),
        queryClient.invalidateQueries({ queryKey: ["gscSites"] }),
      ]);

      toast({
        title: "Refreshed",
        description: "Connection status and sites have been refreshed.",
      });
    } catch (error) {
      console.error("Error refreshing data:", error);
      toast({
        title: "Refresh Failed",
        description: "Failed to refresh data. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Data availability checker
  const { mutate: checkDataAvailability, isLoading: isCheckingData } =
    useGSCDataAvailability({
      onSuccess: (data: any) => {
        const availabilityInfo = data.availability_info;
        if (availabilityInfo?.most_recent_date) {
          toast({
            title: "Data Available",
            description: `Most recent data: ${availabilityInfo.most_recent_date}`,
          });
        } else {
          toast({
            title: "No Data Available",
            description: "No performance data found for this site.",
            variant: "destructive",
          });
        }
      },
      onError: (error: Error) => {
        toast({
          title: "Error Checking Data",
          description: error.message || "Failed to check data availability.",
          variant: "destructive",
        });
      },
    });

  // Monitor connection state changes
  useEffect(() => {
    if (connectionStatus) {
    }
  }, [connectionStatus]);

  // Monitor sites data changes
  useEffect(() => {
    if (sitesData?.sites) {
    }
  }, [sitesData]);

  // Validate and restore selected site when sites data is loaded
  useEffect(() => {
    if (sitesData?.sites && selectedSite) {
      const sites = sitesData.sites;
      const isValidSite = sites.some(
        (site: any) => site.site_url === selectedSite
      );

      if (!isValidSite) {
        // Selected site is no longer available, clear it
        console.warn(
          "Previously selected site is no longer available:",
          selectedSite
        );
        setSelectedSite(null);
        toast({
          title: "Site No Longer Available",
          description:
            "Your previously selected website is no longer accessible.",
          variant: "destructive",
        });
      } else {
        // Site is valid, automatically check data availability

        checkDataAvailability({
          site_url: selectedSite,
          type: searchType,
          days_to_check: 10,
        });
      }
    }
  }, [sitesData, selectedSite, searchType, checkDataAvailability, toast]);

  // Clear selected site when current website changes
  useEffect(() => {
    if (selectedSite && !isGSCSiteMatchingCurrentWebsite(selectedSite)) {
      setSelectedSite(null);

      // Clear cached GSC data for old website
      queryClient.invalidateQueries({ queryKey: ["gscConnectionStatus"] });
      queryClient.invalidateQueries({ queryKey: ["gscSites"] });
    }
  }, [currentWebsite?.url, selectedSite, queryClient]);

  const handleSiteSelect = (siteUrl: string) => {
    setSelectedSite(siteUrl);

    // Validate siteUrl before making API call
    if (!siteUrl || siteUrl.trim() === "") {
      toast({
        title: "Invalid Site URL",
        description: "Please select a valid site URL.",
        variant: "destructive",
      });
      return;
    }

    checkDataAvailability({
      site_url: siteUrl,
      type: "web",
      days_to_check: 10,
    });
  };

  const formatSiteUrl = (url: string) => {
    // Clean up the URL display
    if (url?.startsWith("sc-domain:")) {
      return url.replace("sc-domain:", "");
    }
    return url?.replace(/^https?:\/\//, "").replace(/\/$/, "");
  };

  const getSiteType = (url: string) => {
    if (url?.startsWith("sc-domain:")) {
      return "Domain Property";
    }
    return "URL Prefix Property";
  };

  // Filter sites to only show those that match the current website
  const filteredSites =
    sitesData?.sites.filter((site: any) =>
      isGSCSiteMatchingCurrentWebsite(site.site_url)
    ) || [];

  const isLoading = isStatusLoading || isSitesLoading || isCheckingData;

  // Don't show if no current website is selected
  if (!currentWebsite?.url) {
    return (
      <Card className="w-full shadow-none">
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            GSC Performance Insight
          </CardTitle>
          <CardDescription>
            Select a website to analyze performance data from Google Search
            Console
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert
            variant="default"
            className="bg-blue-50 dark:bg-blue-950/50 border-blue-200 dark:border-blue-800"
          >
            <AlertCircle className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            <AlertTitle>No Website Selected</AlertTitle>
            <AlertDescription>
              Please select a website from the crawl pagination above to view
              GSC performance data.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  // Don't show if not connected
  if (!isConnected) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            GSC Performance Insight
          </CardTitle>
          <CardDescription>
            Connect to Google Search Console to access performance data for{" "}
            {formatSiteUrl(currentWebsite.url)}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert
            variant="default"
            className="bg-blue-50 dark:bg-blue-950/50 border-blue-200 dark:border-blue-800"
          >
            <AlertCircle className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            <AlertTitle>Google Search Console Required</AlertTitle>
            <AlertDescription>
              Please connect your Google Search Console account above to view
              performance data and analytics for{" "}
              {formatSiteUrl(currentWebsite.url)}.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full shadow-none rounded-md">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg flex items-center">
              GSC Performance Insight
            </CardTitle>
            <CardDescription className="text-xs">
              Performance data for{" "}
              <strong>{formatSiteUrl(currentWebsite.url)}</strong> from Google
              Search Console
            </CardDescription>
          </div>
          {isConnected && (
            <Button
              variant="outline"
              size="sm"
              onClick={forceRefresh}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <RefreshCw
                className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-6 w-6 animate-spin text-gray-500 dark:text-gray-400 mr-2" />
            <span className="text-gray-700 dark:text-gray-300">
              Loading sites...
            </span>
          </div>
        ) : filteredSites.length === 0 ? (
          <Alert
            variant="default"
            className="bg-yellow-50 dark:bg-yellow-950/50 border-yellow-200 dark:border-yellow-800"
          >
            <AlertCircle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
            <AlertTitle>No Matching Sites Found</AlertTitle>
            <AlertDescription>
              No GSC properties found for{" "}
              <strong>{formatSiteUrl(currentWebsite.url)}</strong>. Make sure
              this website is added and verified in your Google Search Console
              account.
              <br />
              <a
                href="https://search.google.com/search-console"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 underline mt-2 inline-flex items-center"
              >
                Open Google Search Console{" "}
                <ExternalLink className="h-3 w-3 ml-1" />
              </a>
            </AlertDescription>
          </Alert>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Column 1: Site Information */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2 mb-3">
                <BarChart3 className="h-4 w-4 text-primary" />
                <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200">
                  Site Information
                </h3>
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 py-2 px-3 rounded-md flex justify-between border border-gray-200 dark:border-gray-700">
                <div className="font-medium mb-1">Current Website:</div>
                <div className="text-xs text-gray-500 dark:text-gray-400 break-all">
                  {formatSiteUrl(currentWebsite.url)}
                </div>
              </div>
            </div>

            {/* Column 2: Site Selection */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2 mb-3">
                <Globe className="h-4 w-4 text-primary" />
                <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200">
                  Select Property
                </h3>
              </div>
              <div className="space-y-2">
                {filteredSites.map((site: any, index: number) => (
                  <Button
                    key={index}
                    variant={
                      selectedSite === site.site_url ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() => handleSiteSelect(site.site_url)}
                    disabled={isCheckingData}
                    className={`
                      w-full text-xs px-3 py-3 h-auto transition-all duration-200 justify-start
                      ${
                        selectedSite === site.site_url
                          ? "bg-[#1279b4] text-white"
                          : "hover:bg-gray-50 dark:hover:bg-gray-700"
                      }
                    `}
                  >
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center space-x-2">
                        <Globe className="h-3 w-3 flex-shrink-0" />
                        <span className="font-medium truncate">
                          {formatSiteUrl(site.site_url)}
                        </span>
                      </div>
                      <div className="flex items-center space-x-1 flex-shrink-0">
                        {selectedSite === site.site_url && (
                          <CheckCircle2 className="h-3 w-3" />
                        )}
                        {isCheckingData && selectedSite === site.site_url && (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        )}
                      </div>
                    </div>
                  </Button>
                ))}
              </div>
            </div>

            {/* Column 3: Status & Actions */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2 mb-3">
                <CheckCircle2 className="h-4 w-4 text-primary" />
                <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200">
                  Status
                </h3>
              </div>
              {selectedSite ? (
                <div className="space-y-3">
                  <div className="text-xs text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-950/50 border border-green-200 dark:border-green-800 rounded-lg py-2 px-3 flex justify-between">
                    <div className="text-xs text-green-700 dark:text-green-300 break-all">
                      {formatSiteUrl(selectedSite)}
                    </div>
                    <div className="text-xs text-green-600 dark:text-green-400 mt-1">
                      Type: {getSiteType(selectedSite)}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3">
                  <div className="flex items-center space-x-2 mb-2">
                    <AlertCircle className="h-3 w-3 text-amber-500" />
                    <span className="font-medium">No Property Selected</span>
                  </div>
                  <div className="text-xs">
                    Select a GSC property from the middle column to view
                    performance data and analytics.
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>

      {/* Performance Metrics Component */}
      {selectedSite && (
        <div className="mt-4">
          <GSCPerformanceMetrics
            selectedSite={selectedSite}
            timeRange={timeRange}
            setTimeRange={setTimeRange}
            searchType={searchType}
            setSearchType={setSearchType}
            currentWebsite={currentWebsite}
          />
        </div>
      )}

      {/* Detailed Data Table Component */}
      {selectedSite && (
        <div className="px-6 py-4">
          <GSCDetailedDataTable
            selectedSite={selectedSite}
            timeRange={timeRange}
            searchType={searchType}
            currentWebsite={currentWebsite}
          />
        </div>
      )}
    </Card>
  );
};

export default GSCPerformanceData;
