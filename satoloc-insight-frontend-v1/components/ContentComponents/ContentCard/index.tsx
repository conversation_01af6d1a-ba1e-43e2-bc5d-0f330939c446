"use client";

import React from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Trash2,
  ExternalLink,
  Edit,
  Loader2,
  Database,
  ChevronDown,
} from "lucide-react";
import { SyncedContent } from "@/types";

interface ContentCardProps {
  item: SyncedContent;
  onDelete: (id: number) => void;
  onDeleteLocally: (id: number) => void;
  isDeletingWp: boolean;
  isDeletingLocal: boolean;
}

export default function ContentCard({
  item,
  onDelete,
  onDeleteLocally,
  isDeletingWp,
  isDeletingLocal,
}: ContentCardProps) {
  const isItemLoading = isDeletingWp || isDeletingLocal;

  return (
    <div
      key={item.id}
      className={`p-3 rounded-lg border bg-card hover:bg-accent/5 transition-colors min-h-[160px] flex flex-col relative ${
        isItemLoading ? "opacity-60 pointer-events-none" : ""
      }`}
    >
      {isItemLoading && (
        <div className="absolute inset-0 flex items-center justify-center z-10 rounded-lg bg-background/50">
          <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
        </div>
      )}

      {/* Content Header */}
      <div className="px-3">
        <div className="flex justify-between mb-2 items-start">
          <div className="font-medium text-sm text-[#1279b4] dark:text-foreground line-clamp-2 mr-2 flex-grow">
            {item.title || "Untitled"}
          </div>
          <div className="flex-shrink-0 space-x-1 text-right">
            <span
              className={`inline-block whitespace-nowrap px-2 py-0.5 rounded-md text-xs font-medium ${
                item.status === "publish"
                  ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                  : item.status === "draft"
                    ? "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400"
                    : "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"
              }`}
            >
              {item.status === "publish"
                ? "Published"
                : item.status === "draft"
                  ? "Draft"
                  : item.status}
            </span>
          </div>
        </div>

        {/* Content Preview */}
        <div className="text-xs text-muted-foreground line-clamp-3 mb-2">
          {item.content
            ? item.content.replace(/<[^>]*>/g, "").substring(0, 150) +
              (item.content.length > 150 ? "..." : "")
            : "No content"}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="mt-auto pt-3 flex flex-wrap justify-between items-center px-3 gap-2">
        <div className="text-xs text-muted-foreground">
          {item.wp_id ? `WP ID: ${item.wp_id}` : "Not synced"} •{" "}
          {item.id ? `ID: ${item.id}` : ""}
        </div>

        <div className="flex gap-1 text-right">
          {/* Edit Button */}
          <Button
            size="sm"
            variant="ghost"
            className="h-7 w-7 p-0"
            asChild
            title="Edit content"
          >
            <Link href={`/content/${item.id}`}>
              <Edit className="h-3.5 w-3.5" />
            </Link>
          </Button>

          {/* More Options Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                size="sm"
                variant="ghost"
                className="h-7 w-7 p-0"
                title="More options"
              >
                <ChevronDown className="h-3.5 w-3.5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => onDelete(item.id)}
                className="text-destructive"
                disabled={isDeletingWp || isDeletingLocal}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete (WP & Local)
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDeleteLocally(item.id)}
                disabled={isDeletingWp || isDeletingLocal}
              >
                <Database className="mr-2 h-4 w-4" />
                Delete Locally Only
              </DropdownMenuItem>
              {typeof item.wp_link === "string" && item.wp_link && (
                <DropdownMenuItem asChild>
                  <a
                    href={item.wp_link}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <ExternalLink className="mr-2 h-4 w-4" />
                    View on WordPress
                  </a>
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
}
