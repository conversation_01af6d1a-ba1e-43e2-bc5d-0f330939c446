"use client";

import React from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Languages, X } from "lucide-react";
import { Button } from "@/components/ui/button";

interface LanguageFilter {
  key: string; // e.g., "English (en)"
  name: string; // e.g., "English"
  code: string; // e.g., "en"
  count: number; // number of items in this language
  checked: boolean;
}

interface ContentFilterProps {
  availableLanguages: Map<string, number>; // Map of language key to count
  selectedLanguages: Set<string>; // Set of selected language keys
  onLanguageToggle: (languageKey: string) => void;
  onClearAll: () => void;
  totalItems: number;
  filteredItems: number;
}

export default function ContentFilter({
  availableLanguages,
  selectedLanguages,
  onLanguageToggle,
  onClearAll,
  totalItems,
  filteredItems,
}: ContentFilterProps) {
  // Convert Map to array of LanguageFilter objects
  const languageFilters: LanguageFilter[] = Array.from(
    availableLanguages.entries()
  )
    .map(([key, count]) => {
      // Parse language key format: "English (en)"
      const match = key.match(/^(.+)\s\((.+)\)$/);
      const name = match ? match[1] : key;
      const code = match ? match[2] : "unknown";

      return {
        key,
        name,
        code,
        count,
        checked: selectedLanguages.has(key),
      };
    })
    .sort((a, b) => a.name.localeCompare(b.name)); // Sort alphabetically by name

  const allSelected =
    languageFilters.length > 0 && languageFilters.every((lang) => lang.checked);
  const noneSelected = languageFilters.every((lang) => !lang.checked);
  const someSelected =
    languageFilters.some((lang) => lang.checked) && !allSelected;

  return (
    <Card className="w-full shadow-none">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Languages className="h-4 w-4 text-muted-foreground" />
            <CardTitle className="text-sm font-medium">
              Filter by Language
            </CardTitle>
          </div>
          <div className="flex items-center gap-2">
            {filteredItems !== totalItems && (
              <Badge variant="secondary" className="text-xs">
                {filteredItems} of {totalItems}
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0 space-y-3">
        {/* Clear All Control */}
        {languageFilters.length > 1 && someSelected && (
          <div className="flex justify-end">
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearAll}
              className="h-7 px-2 text-xs"
            >
              <X className="h-3 w-3 mr-1" />
              Clear All
            </Button>
          </div>
        )}

        {/* Language Checkboxes - Horizontal Layout */}
        <div className="space-y-3">
          {languageFilters.length === 0 ? (
            <div className="text-center py-4 text-muted-foreground text-sm">
              No languages available
            </div>
          ) : (
            <div className="flex flex-wrap gap-3">
              {languageFilters.map((language) => (
                <div
                  key={language.key}
                  className="flex items-center space-x-2 bg-muted/30 hover:bg-muted/50 rounded-lg px-3 py-2 transition-colors border border-border/50"
                >
                  <Checkbox
                    id={`lang-${language.code}`}
                    checked={language.checked}
                    onCheckedChange={() => onLanguageToggle(language.key)}
                  />
                  <Label
                    htmlFor={`lang-${language.code}`}
                    className="text-sm cursor-pointer flex items-center gap-2"
                  >
                    <span className="font-medium">{language.name}</span>
                    <span className="text-xs text-muted-foreground">
                      ({language.code})
                    </span>
                    <Badge variant="secondary" className="text-xs ml-1">
                      {language.count}
                    </Badge>
                  </Label>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Filter Summary */}
        {languageFilters.length > 0 && (
          <div className="pt-2 border-t">
            <div className="text-xs text-muted-foreground">
              {noneSelected
                ? "No languages selected - showing all content"
                : someSelected
                  ? `Showing ${selectedLanguages.size} of ${languageFilters.length} languages`
                  : "All languages selected"}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
