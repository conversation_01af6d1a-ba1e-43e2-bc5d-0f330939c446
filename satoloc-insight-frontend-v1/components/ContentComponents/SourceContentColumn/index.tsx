import React, { useState, useRef, useEffect } from "react";
import { SyncedContent } from "@/types";
import { ContentEditFormData } from "@/lib/validators";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  ContentEditForm,
  ContentEditFormRef,
} from "@/components/ContentEditForm";
import {
  Languages,
  Maximize,
  Minimize,
  Save,
  UploadCloud,
  Loader2,
} from "lucide-react";
import { Button } from "@/components/ui/button";

interface SourceContentColumnProps {
  contentItem: SyncedContent;
  onLocalSave: (formData: ContentEditFormData) => void;
  onPushToWp: (originalContent?: string) => void; // Modified to accept original content
  isSavingLocal: boolean;
  isPushingWp: boolean;
  canPush: boolean;
}

export default function SourceContentColumn({
  contentItem,
  onLocalSave,
  onPushToWp,
  isSavingLocal,
  isPushingWp,
  canPush,
}: SourceContentColumnProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const formRef = useRef<ContentEditFormRef>(null);

  const languageDisplay =
    contentItem.language_name || contentItem.language_code;
  const sourceLanguageCode = contentItem.language_code || "en";

  // Toggle fullscreen
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // --- Display Assigned Categories ---
  const renderAssignedCategories = () => {
    if (!contentItem?.categories || contentItem.categories.length === 0) {
      return (
        <span className="text-muted-foreground italic text-xs">
          Uncategorized
        </span>
      );
    }
    return contentItem.categories.map((cat) => (
      <Badge
        key={cat.id}
        variant="outline"
        className="mr-1 mb-1 text-xs font-normal"
      >
        {cat.name}
      </Badge>
    ));
  };

  return (
    <Card
      className={`p-0 overflow-hidden flex flex-col shadow-none rounded-md h-full ${
        isFullscreen ? "fixed inset-0 z-50 bg-white dark:bg-background" : ""
      }`}
    >
      <CardHeader className="bg-[#1279b4] text-white rounded-t-lg py-4 px-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-white"
            >
              <path d="M17 6.1H3" />
              <path d="M21 12.1H3" />
              <path d="M15.1 18H3" />
            </svg>
            <CardTitle className="text-xl font-semibold">
              API Integration
            </CardTitle>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleFullscreen}
            className="h-8 w-8 p-0 text-white hover:bg-white/20"
            title={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
          >
            {isFullscreen ? (
              <Minimize className="h-4 w-4" />
            ) : (
              <Maximize className="h-4 w-4" />
            )}
          </Button>
        </div>
        <CardDescription className="text-white/80 mt-1 flex items-center">
          <Languages className="h-4 w-4 mr-1.5" /> {languageDisplay} (
          {sourceLanguageCode}) • ID: {contentItem.wp_id}
        </CardDescription>
      </CardHeader>

      <CardContent className="p-6 space-y-6 flex-grow overflow-y-auto">
        {/* Source Content Editing Form with integrated categories */}
        <ContentEditForm
          ref={formRef}
          initialData={contentItem}
          onLocalSave={onLocalSave}
          onPushToWp={onPushToWp}
          isSavingLocal={isSavingLocal}
          isPushingWp={isPushingWp}
          canPush={canPush}
          renderCategories={() => (
            <div className="flex-1">
              <Label className="text-sm font-medium block mb-1.5 text-[#1279b4]">
                Current Categories
              </Label>
              <div className="min-h-[36px] rounded-md border border-input bg-background px-3 py-1.5">
                {renderAssignedCategories()}
              </div>
            </div>
          )}
        />
      </CardContent>

      <CardFooter className="p-0 border-t bg-background">
        <div className="w-full">
          {/* Row 1: Action Buttons */}
          <div className="px-6 py-3 border-b border-gray-100">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              <Button
                onClick={() => formRef.current?.submitForm()}
                disabled={isSavingLocal || isPushingWp}
                className="w-full bg-[#1279b4] hover:bg-[#00497A] text-white"
              >
                {isSavingLocal ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Save className="mr-2 h-4 w-4" />
                )}
                {isSavingLocal ? "Saving..." : "Save Changes"}
              </Button>

              <Button
                onClick={() => {
                  // Get original content for WordPress push to preserve page builder structure
                  const originalContent = formRef.current?.getOriginalContent();
                  onPushToWp(originalContent);
                }}
                disabled={isPushingWp || !canPush}
                variant="outline"
                className="w-full"
              >
                {isPushingWp ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <UploadCloud className="mr-2 h-4 w-4" />
                )}
                {isPushingWp ? "Pushing..." : "Push to WordPress"}
              </Button>
            </div>
          </div>

          {/* Row 2: Metadata */}
          <div className="px-6 py-2 text-xs text-muted-foreground">
            <div className="flex flex-wrap justify-between gap-x-4 gap-y-1">
              <span>
                <strong>Modified:</strong>{" "}
                {contentItem.date_modified_gmt
                  ? new Date(contentItem.date_modified_gmt).toLocaleString()
                  : "N/A"}
              </span>
              <span>
                <strong>Created:</strong>{" "}
                {contentItem.date_created_gmt
                  ? new Date(contentItem.date_created_gmt).toLocaleDateString()
                  : "N/A"}
              </span>
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
