import React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Languages } from "lucide-react";

interface LanguageChangeModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  onCancel: () => void;
  currentLanguage?: string;
  newLanguage?: string;
}

export default function LanguageChangeModal({
  isOpen,
  onOpenChange,
  onConfirm,
  onCancel,
  currentLanguage,
  newLanguage,
}: LanguageChangeModalProps) {
  const handleConfirm = () => {
    onConfirm();
    onOpenChange(false);
  };

  const handleCancel = () => {
    onCancel();
    onOpenChange(false);
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent className="sm:max-w-[425px]">
        <AlertDialogHeader>
          <div className="flex items-center gap-2">
            <Languages className="h-5 w-5 text-[#1279b4]" />
            <AlertDialogTitle className="text-lg font-semibold">
              Change Translation Language
            </AlertDialogTitle>
          </div>
          <AlertDialogDescription className="text-sm text-muted-foreground">
            Changing the translation language will clear the current translation
            preview and any unsaved changes.
          </AlertDialogDescription>
          {currentLanguage && newLanguage && (
            <div className="mt-3 p-3 bg-muted rounded-md">
              <div className="text-xs font-medium text-muted-foreground mb-1">
                Language Change:
              </div>
              <div className="flex items-center gap-2 text-sm">
                <span className="font-medium">{currentLanguage}</span>
                <span className="text-muted-foreground">→</span>
                <span className="font-medium text-[#1279b4]">
                  {newLanguage}
                </span>
              </div>
            </div>
          )}
        </AlertDialogHeader>
        <AlertDialogFooter className="flex gap-2">
          <AlertDialogCancel
            onClick={handleCancel}
            className="flex-1 sm:flex-none"
          >
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            className="flex-1 sm:flex-none bg-[#1279b4] hover:bg-[#1279b4]/90 text-white"
          >
            Continue
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
