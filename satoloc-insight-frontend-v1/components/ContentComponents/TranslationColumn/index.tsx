import React, { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import {
  getTranslationPreview,
  createTranslation,
  getMarkdownTranslationPreview,
} from "@/lib/apiClient";
import {
  convertHtmlToMarkdown,
  convertMarkdownToHtml,
  cleanHtmlContent,
  cleanHtmlContentEnhanced,
  detectAvadaContent,
  reconstructAvadaContent,
} from "@/lib/markdownUtils";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import {
  Loader2,
  Languages,
  Globe,
  UploadCloud,
  InfoIcon,
  Copy,
  Download,
  Maximize,
  Minimize,
} from "lucide-react";
import { WpCategory, SyncedContent } from "@/types";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { MultiSelect } from "@/components/ui/multi-select";
import VisualEditor from "@/components/CustomContent/ContentEditor/VisualEditor";
import AceHtmlEditor from "@/components/CustomContent/ContentEditor/AceHtmlEditor";
import LanguageChangeModal from "@/components/ContentComponents/LanguageChangeModal";

// Define available languages for translation dropdown
const AVAILABLE_LANGUAGES = [
  { code: "en", name: "English" },
  { code: "es", name: "Spanish" },
  { code: "fr", name: "French" },
  { code: "de", name: "German" },
  { code: "it", name: "Italian" },
  { code: "pt", name: "Portuguese" },
  { code: "ru", name: "Russian" },
  { code: "zh", name: "Chinese" },
  { code: "ja", name: "Japanese" },
  { code: "ar", name: "Arabic" },
  { code: "tr", name: "Turkish" },
  { code: "vi", name: "Vietnamese" },
  { code: "pl", name: "Polish" },
];

interface TranslationColumnProps {
  contentItem: SyncedContent;
  localId: number;
  connection: any;
  isConnected: boolean;
  targetLanguage: string;
  setTargetLanguage: (language: string) => void;
  translatedTitle: string | null;
  setTranslatedTitle: (title: string | null) => void;
  translatedContent: string | null;
  setTranslatedContent: (content: string | null) => void;
  isPreviewLoading: boolean;
  setIsPreviewLoading: (loading: boolean) => void;
  previewError: string | null;
  setPreviewError: (error: string | null) => void;
  isVisualMode: boolean;
  setIsVisualMode: (visual: boolean) => void;
  availableCategories: WpCategory[];
  isLoadingCategories: boolean;
  selectedTranslationCategoryIds: string[];
  setSelectedTranslationCategoryIds: (ids: string[]) => void;
  categoryOptions: Array<{ value: string; label: string }>;
  clearStoredTranslationPreview: () => void;
  onTranslationSuccess: () => void;
}

export default function TranslationColumn({
  contentItem,
  localId,
  connection,
  isConnected,
  targetLanguage,
  setTargetLanguage,
  translatedTitle,
  setTranslatedTitle,
  translatedContent,
  setTranslatedContent,
  isPreviewLoading,
  setIsPreviewLoading,
  previewError,
  setPreviewError,
  isVisualMode,
  setIsVisualMode,
  availableCategories,
  isLoadingCategories,
  selectedTranslationCategoryIds,
  setSelectedTranslationCategoryIds,
  categoryOptions,
  clearStoredTranslationPreview,
  onTranslationSuccess,
}: TranslationColumnProps) {
  const { toast } = useToast();
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Modal state for language change confirmation
  const [showLanguageModal, setShowLanguageModal] = useState(false);
  const [pendingLanguage, setPendingLanguage] = useState<string>("");

  // Toggle fullscreen
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Handle language change confirmation
  const handleLanguageChangeConfirm = () => {
    setTranslatedTitle(null);
    setTranslatedContent(null);
    setPreviewError(null);
    setSelectedTranslationCategoryIds(
      contentItem?.categories?.map((cat) => cat.id.toString()) || []
    );
    setTargetLanguage(pendingLanguage);
    clearStoredTranslationPreview();
    setPendingLanguage("");
  };

  const handleLanguageChangeCancel = () => {
    setPendingLanguage("");
  };

  // --- Helper Functions ---
  const getHtmlContent = (): string => {
    if (!translatedContent) return "";
    return cleanHtmlContentEnhanced(translatedContent);
  };

  const handleContentChange = (newContent: string) => {
    setTranslatedContent(newContent);
  };

  // --- Mutations ---
  // Get Translation Preview (HTML)
  const previewTranslationMutation = useMutation({
    mutationFn: () => {
      if (isNaN(localId)) throw new Error("Invalid ID");
      if (!targetLanguage) throw new Error("Select target language");
      if (targetLanguage === contentItem?.language_code)
        throw new Error("Target cannot be same as source");
      setIsPreviewLoading(true);
      setPreviewError(null);
      setTranslatedTitle(null);
      setTranslatedContent(null);

      // Check if content has AVADA shortcodes and clean it before translation
      const sourceContent = contentItem?.content || "";
      const hasAvadaContent = detectAvadaContent(sourceContent);

      if (hasAvadaContent) {
        // Clean the AVADA content and send it for translation
        const cleanedContent = cleanHtmlContentEnhanced(sourceContent);

        // Send cleaned content for translation
        return getTranslationPreview(localId, targetLanguage, cleanedContent);
      }

      return getTranslationPreview(localId, targetLanguage);
    },
    onSuccess: (data) => {
      setTranslatedTitle(data.translated_title);
      // Apply enhanced cleanup to translated content for better preview
      const cleanedTranslatedContent = cleanHtmlContentEnhanced(
        data.translated_content
      );
      setTranslatedContent(cleanedTranslatedContent);
      toast({ title: "Translation Preview Ready" });
    },
    onError: (error: Error) => {
      setPreviewError(error.message || "Preview generation failed.");
      toast({
        title: "Translation Preview Failed",
        description: error.message,
        variant: "destructive",
      });
    },
    onSettled: () => setIsPreviewLoading(false),
  });

  // Get Translation Preview (Markdown)
  const previewMarkdownTranslationMutation = useMutation({
    mutationFn: () => {
      if (isNaN(localId)) throw new Error("Invalid ID");
      if (!targetLanguage) throw new Error("Select target language");
      if (targetLanguage === contentItem?.language_code)
        throw new Error("Target cannot be same as source");

      // Check if content has AVADA shortcodes and clean it before markdown conversion
      const sourceContent = contentItem?.content || "";
      const hasAvadaContent = detectAvadaContent(sourceContent);

      let contentForMarkdown = sourceContent;
      if (hasAvadaContent) {
        contentForMarkdown = cleanHtmlContentEnhanced(sourceContent);
      }

      const sourceMarkdown = convertHtmlToMarkdown(contentForMarkdown);

      setIsPreviewLoading(true);
      setPreviewError(null);
      setTranslatedTitle(null);
      setTranslatedContent(null);
      return getMarkdownTranslationPreview(
        localId,
        targetLanguage,
        sourceMarkdown
      );
    },
    onSuccess: (data) => {
      setTranslatedTitle(data.translated_title);

      const translatedHtml = convertMarkdownToHtml(data.translated_markdown);
      // Apply enhanced cleanup to markdown-converted content as well
      const cleanedTranslatedContent = cleanHtmlContentEnhanced(translatedHtml);

      setTranslatedContent(cleanedTranslatedContent);
      sessionStorage.setItem(
        `translation_markdown_${localId}`,
        data.translated_markdown
      ); // Store markdown
      toast({
        title: "Translation Preview Ready",
        description: "Using enhanced markdown translation.",
      });
    },
    onError: (error: Error) => {
      setPreviewError(error.message || "Markdown preview failed.");
      toast({
        title: "Markdown Preview Failed",
        description: error.message,
        variant: "destructive",
      });
    },
    onSettled: () => setIsPreviewLoading(false),
  });

  // Create Translated Post on WP
  const createWpPostMutation = useMutation({
    mutationFn: () => {
      if (!connection?.apiKey) throw new Error("WordPress API Key is missing.");
      if (isNaN(localId)) throw new Error("Invalid ID");
      if (!targetLanguage) throw new Error("Target language not set.");
      if (translatedTitle === null || translatedContent === null)
        throw new Error("Translation required");

      const numericCategoryIds = selectedTranslationCategoryIds
        .map((idStr) => parseInt(idStr, 10))
        .filter((id) => !isNaN(id) && id > 0);

      const storedMarkdown = sessionStorage.getItem(
        `translation_markdown_${localId}`
      );
      const complexLanguages = ["zh", "ja", "ru", "ko", "ar", "tr"];
      const isComplexLanguage = complexLanguages.includes(targetLanguage);
      let finalContent = getHtmlContent(); // Get current edited HTML

      if (isComplexLanguage && storedMarkdown) {
        // Prefer converted markdown if available for complex langs, unless user edited HTML significantly
        // Simple check: if current HTML is very different from converted markdown HTML, use current HTML
        const mdHtml = convertMarkdownToHtml(storedMarkdown);
        if (Math.abs(mdHtml.length - finalContent.length) < 50) {
          // Arbitrary threshold
          finalContent = mdHtml;
        }
      }

      // Check if original content is AVADA and reconstruct structure if needed
      const originalContent = contentItem?.content || "";
      if (detectAvadaContent(originalContent)) {
        finalContent = reconstructAvadaContent(originalContent, finalContent);
      }

      return createTranslation(
        localId,
        targetLanguage,
        connection.apiKey,
        translatedTitle,
        finalContent,
        numericCategoryIds // Pass selected category IDs
      );
    },
    onSuccess: (data) => {
      toast({
        title: "Translation Created",
        description: data.message,
        variant: "default",
        duration: 6000,
      });
      onTranslationSuccess();
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to Create Post",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // --- Handlers ---
  const handleTranslatePreview = () => {
    if (!targetLanguage) {
      toast({
        title: "Select Language",
        description: "Choose a target language first.",
      });
      return;
    }
    if (targetLanguage === contentItem?.language_code) {
      toast({
        title: "Invalid Language",
        description: "Target cannot be same as source.",
        variant: "destructive",
      });
      return;
    }

    const complexLanguages = ["zh", "ja", "ru", "ko", "ar", "tr"];
    if (complexLanguages.includes(targetLanguage)) {
      previewMarkdownTranslationMutation.mutate();
    } else {
      previewTranslationMutation.mutate();
    }
  };

  const handleCreateWpPost = () => {
    if (translatedTitle === null || translatedContent === null) {
      toast({
        title: "Translation Required",
        description: "Generate a translation first.",
        variant: "destructive",
      });
      return;
    }
    createWpPostMutation.mutate();
  };

  const copyToClipboard = (text: string, type: "title" | "content") => {
    if (!text) return;
    navigator.clipboard
      .writeText(text)
      .then(() =>
        toast({ title: `${type === "title" ? "Title" : "Content"} Copied` })
      )
      .catch((err) =>
        toast({
          title: "Copy Failed",
          description: "Could not copy.",
          variant: "destructive",
        })
      );
  };

  const exportToFile = (
    title: string,
    content: string,
    fileName: string,
    fileType: string
  ) => {
    if (!title || !content) return;
    let exportContent = "";
    let mimeType = "text/plain";

    if (fileType === "html") {
      mimeType = "text/html";
      exportContent = `<!DOCTYPE html><html><head><meta charset="UTF-8"><title>${title}</title></head><body><h1>${title}</h1>${content}</body></html>`;
    } else if (fileType === "json") {
      mimeType = "application/json";
      exportContent = JSON.stringify(
        {
          title,
          content,
          language: targetLanguage,
          originalTitle: contentItem?.title,
          exportDate: new Date().toISOString(),
        },
        null,
        2
      );
    } else if (fileType === "md") {
      const titleMd = `# ${title}\n\n`;
      const contentMd = convertHtmlToMarkdown(content);
      exportContent = titleMd + contentMd;
    } else {
      exportContent = `${title}\n\n${content}`;
    }

    const blob = new Blob([exportContent], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `${fileName}.${fileType}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    toast({
      title: "Export Complete",
      description: `Exported as ${fileName}.${fileType}`,
    });
  };

  // --- Variables for Render ---
  const sourceLanguageCode = contentItem.language_code || "en";
  const targetLanguageOptions = AVAILABLE_LANGUAGES.filter(
    (lang) => lang.code !== sourceLanguageCode
  );

  return (
    <>
      <Card
        className={`p-0 overflow-hidden flex flex-col shadow-none rounded-md h-full ${
          isFullscreen ? "fixed inset-0 z-50 bg-white dark:bg-background" : ""
        }`}
      >
        <CardHeader className="bg-[#1279b4] text-white rounded-t-lg py-4 px-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              <CardTitle className="text-xl font-semibold">
                Translation
              </CardTitle>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleFullscreen}
              className="h-8 w-8 p-0 text-white hover:bg-white/20"
              title={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
            >
              {isFullscreen ? (
                <Minimize className="h-4 w-4" />
              ) : (
                <Maximize className="h-4 w-4" />
              )}
            </Button>
          </div>
          <CardDescription className="text-white/80 mt-1">
            Select a language to translate
          </CardDescription>
        </CardHeader>

        <CardContent className="p-6 space-y-6 flex-grow overflow-y-auto">
          {/* Language Selector and Translate Button */}
          <div>
            <Label
              htmlFor="target-language"
              className="text-sm font-medium block mb-1.5 text-[#1279b4]"
            >
              Select Translation Language
            </Label>
            <div className="flex gap-2">
              <Select
                value={targetLanguage}
                onValueChange={(value) => {
                  if (
                    value !== targetLanguage &&
                    (translatedTitle || translatedContent)
                  ) {
                    // Show modal for confirmation
                    setPendingLanguage(value);
                    setShowLanguageModal(true);
                  } else {
                    setTargetLanguage(value);
                  }
                }}
                disabled={isPreviewLoading || createWpPostMutation.isLoading}
              >
                <SelectTrigger id="target-language" className="flex-grow">
                  <SelectValue placeholder="Select a language" />
                </SelectTrigger>
                <SelectContent>
                  {targetLanguageOptions.map((lang) => (
                    <SelectItem key={lang.code} value={lang.code}>
                      {lang.name} ({lang.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                onClick={handleTranslatePreview}
                disabled={
                  !targetLanguage ||
                  isPreviewLoading ||
                  createWpPostMutation.isLoading ||
                  targetLanguage === sourceLanguageCode
                }
                className="bg-[#1279b4] hover:bg-[#00497A] text-white px-4 py-2 whitespace-nowrap"
              >
                {isPreviewLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Languages className="mr-2 h-4 w-4" />
                )}
                Translate
              </Button>
            </div>
          </div>

          {previewError && (
            <p className="text-sm text-destructive bg-red-50 p-2 rounded">
              {previewError}
            </p>
          )}

          {/* Translation Preview Area */}
          {isPreviewLoading && (
            <div className="flex flex-col items-center justify-center p-6 text-muted-foreground bg-gray-50 rounded-lg min-h-[300px]">
              <Loader2 className="h-8 w-8 animate-spin mb-4" />{" "}
              <p>Generating translation...</p>
            </div>
          )}

          {!isPreviewLoading &&
            translatedTitle !== null &&
            translatedContent !== null && (
              <div className="space-y-4 border-t pt-4">
                {/* Categories Selector for Translation */}
                <div>
                  <Label
                    htmlFor="translation-categories"
                    className="text-sm font-medium block mb-1.5 text-[#1279b4]"
                  >
                    Assign Categories for New Post
                  </Label>
                  {isLoadingCategories ? (
                    <div className="flex items-center text-muted-foreground text-sm">
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Loading
                      categories...
                    </div>
                  ) : categoryOptions.length === 0 ? (
                    <p className="text-sm text-muted-foreground italic">
                      No categories found for {connection?.baseUrl}.
                    </p>
                  ) : (
                    <MultiSelect
                      options={categoryOptions}
                      selected={selectedTranslationCategoryIds}
                      onChange={setSelectedTranslationCategoryIds}
                      placeholder="Select categories..."
                      disabled={createWpPostMutation.isLoading}
                      className="w-full"
                    />
                  )}
                </div>

                {/* Translated Title */}
                <div className="bg-gray-50 p-4 rounded-lg border">
                  <div className="flex justify-between items-center">
                    <Label
                      htmlFor="translated-title"
                      className="font-medium text-[#1279b4]"
                    >
                      Translated Title
                    </Label>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(translatedTitle, "title")}
                      className="h-7 w-7 p-0"
                      title="Copy title"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <Input
                    id="translated-title"
                    value={translatedTitle}
                    onChange={(e) => setTranslatedTitle(e.target.value)}
                    disabled={createWpPostMutation.isLoading}
                    className="mt-1.5 border-gray-300"
                  />
                </div>

                {/* Translated Content */}
                <div className="bg-gray-50 p-4 rounded-lg border">
                  <div className="flex justify-between items-center mb-2">
                    <Label
                      htmlFor="translated-content"
                      className="font-medium text-[#1279b4]"
                    >
                      Translated Content
                    </Label>
                    <div className="flex items-center gap-2">
                      {/* Visual/HTML Toggle */}
                      <div className="flex items-center space-x-0 border border-input rounded-md overflow-hidden">
                        <button
                          type="button"
                          onClick={() => setIsVisualMode(false)}
                          className={`px-2 py-0.5 text-xs ${!isVisualMode ? "bg-[#1279b4] text-white" : "bg-background text-muted-foreground"}`}
                        >
                          HTML
                        </button>
                        <button
                          type="button"
                          onClick={() => setIsVisualMode(true)}
                          className={`px-2 py-0.5 text-xs ${isVisualMode ? "bg-[#1279b4] text-white" : "bg-background text-muted-foreground"}`}
                        >
                          Visual
                        </button>
                      </div>
                      {/* Copy/Export Buttons */}
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() =>
                          copyToClipboard(translatedContent || "", "content")
                        }
                        className="h-7 w-7 p-0"
                        title="Copy content"
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-7 px-2"
                            title="Export"
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onSelect={() =>
                              exportToFile(
                                translatedTitle,
                                translatedContent,
                                `translation-${targetLanguage}`,
                                "html"
                              )
                            }
                          >
                            Export as HTML
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onSelect={() =>
                              exportToFile(
                                translatedTitle,
                                convertHtmlToMarkdown(translatedContent),
                                `translation-${targetLanguage}`,
                                "md"
                              )
                            }
                          >
                            Export as Markdown
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onSelect={() =>
                              exportToFile(
                                translatedTitle,
                                convertHtmlToMarkdown(translatedContent),
                                `translation-${targetLanguage}`,
                                "txt"
                              )
                            }
                          >
                            Export as Text
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onSelect={() =>
                              exportToFile(
                                translatedTitle,
                                translatedContent,
                                `translation-${targetLanguage}`,
                                "json"
                              )
                            }
                          >
                            Export as JSON
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                  {/* Conditional Rendering based on isVisualMode */}
                  {isVisualMode ? (
                    <div className="h-[400px]">
                      <VisualEditor
                        content={translatedContent || ""}
                        onChange={handleContentChange}
                        placeholder="Translation content..."
                        disabled={
                          createWpPostMutation.isLoading || isPreviewLoading
                        }
                      />
                    </div>
                  ) : (
                    <div className="h-[400px]">
                      <AceHtmlEditor
                        content={translatedContent || ""}
                        onChange={handleContentChange}
                        placeholder="Translation content..."
                        disabled={
                          createWpPostMutation.isLoading || isPreviewLoading
                        }
                        viewMode="code"
                      />
                    </div>
                  )}
                </div>
              </div>
            )}

          {!isPreviewLoading && translatedTitle === null && !previewError && (
            <div className="flex flex-col items-center justify-center text-center p-8 text-muted-foreground bg-gray-50 rounded-lg min-h-[300px] border">
              <Globe className="h-8 w-8 text-gray-300 mb-2" />
              <p className="text-sm font-medium text-gray-500">
                No translation preview yet
              </p>
              <p className="text-sm text-gray-400 mt-1 font-medium">
                Select a language and click Translate
              </p>
            </div>
          )}
        </CardContent>

        <CardFooter className="bg-background p-4 border-t">
          <Button
            onClick={handleCreateWpPost}
            disabled={
              translatedTitle === null ||
              translatedContent === null ||
              createWpPostMutation.isLoading ||
              isPreviewLoading ||
              !isConnected
            }
            className="w-full bg-[#1279b4] hover:bg-[#00497A] text-white"
          >
            {createWpPostMutation.isLoading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <UploadCloud className="mr-2 h-4 w-4" />
            )}
            {createWpPostMutation.isLoading
              ? "Creating Post..."
              : "Push Translation to WordPress"}
          </Button>
        </CardFooter>
      </Card>

      {/* Language Change Confirmation Modal */}
      <LanguageChangeModal
        isOpen={showLanguageModal}
        onOpenChange={setShowLanguageModal}
        onConfirm={handleLanguageChangeConfirm}
        onCancel={handleLanguageChangeCancel}
        currentLanguage={
          targetLanguage
            ? AVAILABLE_LANGUAGES.find((lang) => lang.code === targetLanguage)
                ?.name
            : undefined
        }
        newLanguage={
          pendingLanguage
            ? AVAILABLE_LANGUAGES.find((lang) => lang.code === pendingLanguage)
                ?.name
            : undefined
        }
      />
    </>
  );
}
