"use client";

import { motion } from "framer-motion";
import { Container } from "@mui/material";
import { FeatureCard } from "../FeatureCard";
import { GradientText } from "../GradientText";
import { useHomepageContent } from "@/internal-api/wordpress-blog";
import { forwardRef } from "react";
import {
  Languages,
  Target,
  BarChart3,
  PenTool,
  TrendingUp,
  Sparkles,
} from "lucide-react";

// Icon mapping - cycles through icons for each feature
const iconArray = [Languages, Target, BarChart3, PenTool, TrendingUp, Sparkles];

export const DynamicFeaturesSection = forwardRef<HTMLElement>((props, ref) => {
  const { data: homepageContent, isLoading, error } = useHomepageContent();

  if (isLoading) {
    return (
      <section ref={ref} className="py-24 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background" />
        <Container className="container px-4 relative">
          <div className="text-center mb-16">
            <div className="animate-pulse">
              <div className="h-12 bg-muted rounded w-2/3 mx-auto mb-4" />
              <div className="h-6 bg-muted rounded w-1/2 mx-auto" />
            </div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 auto-rows-fr">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="h-48 bg-muted rounded-lg" />
              </div>
            ))}
          </div>
        </Container>
      </section>
    );
  }

  // Prepare features array from WordPress repeater data
  const featuresData = Array.isArray(homepageContent?.features)
    ? homepageContent.features
    : [];

  const features = featuresData
    .map((wpFeature: any, index: number) => {
      // Only return feature if both title and description exist
      if (wpFeature?.title && wpFeature?.description) {
        return {
          title: wpFeature.title,
          description: wpFeature.description,
          icon: iconArray[index % iconArray.length], // Cycle through icons
        };
      }
      return null;
    })
    .filter(
      (
        feature: any
      ): feature is { title: string; description: string; icon: any } =>
        feature !== null
    );

  // Show error or no content state
  if (error || !featuresData.length || features.length === 0) {
    return (
      <section ref={ref} className="py-24 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background" />
        <Container className="container px-4 relative">
          <div className="text-center">
            <div className="p-8 border border-dashed border-muted-foreground/30 rounded-lg max-w-2xl mx-auto">
              <h2 className="text-2xl font-semibold mb-4 text-muted-foreground">
                No Features Content
              </h2>
              <p className="text-muted-foreground mb-4">
                Please add feature content in the WordPress admin panel under
                the "Key Features" section.
              </p>
              {error && (
                <p className="text-sm text-destructive">
                  Error: {error.message}
                </p>
              )}
            </div>
          </div>
        </Container>
      </section>
    );
  }

  return (
    <section ref={ref} id="features" className="py-24 relative">
      <div className="absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background" />
      <Container className="container px-4 relative">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold mb-4">
            <GradientText>SatoLOC Insight Key Features</GradientText>
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Your ultimate toolkit for transforming and mastering global content
            strategies.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 auto-rows-fr">
          {features.map((feature: any, index: number) => (
            <FeatureCard key={index} {...feature} index={index} />
          ))}
        </div>

        {/* Show content source indicator in development */}
        {process.env.NODE_ENV === "development" && (
          <div className="mt-8 text-center text-xs text-muted-foreground">
            Features content source: WordPress CMS ({features.length} features
            loaded)
          </div>
        )}
      </Container>
    </section>
  );
});
