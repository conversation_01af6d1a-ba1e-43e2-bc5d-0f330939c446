"use client";

import { motion } from "framer-motion";
import { Container } from "@mui/material";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { GradientText } from "../GradientText";
import { useHomepageContent } from "@/internal-api/wordpress-blog";
import { forwardRef } from "react";
import { ArrowRight, Search, PenTool, Target, Zap } from "lucide-react";

// Icon and letter arrays for strategy steps - cycles through them
const stepIconArray = [Search, PenTool, Target, Zap];
const stepLetterArray = ["L", "I", "F", "T"];

export const DynamicStrategySection = forwardRef<HTMLElement>((props, ref) => {
  const { data: homepageContent, isLoading, error } = useHomepageContent();

  if (isLoading) {
    return (
      <section ref={ref} className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-t from-background via-primary/5 to-background" />
        <Container className="container px-4 relative">
          <div className="text-center mb-16">
            <div className="animate-pulse">
              <div className="h-12 bg-muted rounded w-2/3 mx-auto mb-4" />
              <div className="h-6 bg-muted rounded w-1/2 mx-auto" />
            </div>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="h-48 bg-muted rounded-lg" />
              </div>
            ))}
          </div>
        </Container>
      </section>
    );
  }

  // Prepare strategy steps from WordPress repeater data
  const strategyData = Array.isArray((homepageContent?.strategy as any)?.items)
    ? (homepageContent?.strategy as any).items
    : [];

  const strategySteps = strategyData
    .map((wpStep: any, index: number) => {
      // Only return step if both title and description exist
      if (wpStep?.title && wpStep?.description) {
        return {
          title: wpStep.title,
          description: wpStep.description,
          icon: stepIconArray[index % stepIconArray.length], // Cycle through icons
          step: stepLetterArray[index % stepLetterArray.length], // Cycle through letters
        };
      }
      return null;
    })
    .filter(
      (
        step: any
      ): step is {
        title: string;
        description: string;
        icon: any;
        step: string;
      } => step !== null
    );

  // Show error or no content state
  if (error || !strategyData.length || strategySteps.length === 0) {
    return (
      <section ref={ref} className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-t from-background via-primary/5 to-background" />
        <Container className="container px-4 relative">
          <div className="text-center">
            <div className="p-8 border border-dashed border-muted-foreground/30 rounded-lg max-w-2xl mx-auto">
              <h2 className="text-2xl font-semibold mb-4 text-muted-foreground">
                No Strategy Content
              </h2>
              <p className="text-muted-foreground mb-4">
                Please add strategy content in the WordPress admin panel under
                the "Strategy Section".
              </p>
              {error && (
                <p className="text-sm text-destructive">
                  Error: {error.message}
                </p>
              )}
            </div>
          </div>
        </Container>
      </section>
    );
  }

  return (
    <section
      ref={ref}
      id="solutions"
      className="py-24 relative overflow-hidden"
    >
      <div className="absolute inset-0 bg-gradient-to-t from-background via-primary/5 to-background" />
      <Container className="container px-4 relative">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold mb-4">
            <GradientText>
              {homepageContent.strategy.title ||
                "How It Works – Powered by the LIFT Strategy"}
            </GradientText>
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            {homepageContent.strategy.description ||
              "SatoLOC Insight simplifies your journey to global growth with our unique LIFT Strategy. Follow four simple steps to transform your localization, SEO, or content creation process."}
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {strategySteps.map((step: any, index: number) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="h-full bg-gradient-to-br from-background to-primary/5 hover:shadow-xl transition-all duration-300">
                <CardContent className="pt-6">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="bg-primary/10 p-3 rounded-lg w-12 h-12 flex items-center justify-center hover:scale-110 hover:rounded-full transition-transform duration-300">
                      <span className="text-3xl font-bold text-primary">
                        {step.step}
                      </span>
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{step.title}</h3>
                  <p className="text-muted-foreground text-sm">
                    {step.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
          <Button
            size="lg"
            className="group px-8 w-full sm:w-auto bg-gradient-to-r from-primary via-[#1279b4] to-primary animate-gradient-x"
          >
            Get Started with LIFT
            <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
          </Button>
        </div>

        {/* Show content source indicator in development */}
        {process.env.NODE_ENV === "development" && (
          <div className="mt-8 text-center text-xs text-muted-foreground">
            Strategy content source: WordPress CMS ({strategySteps.length} steps
            loaded)
          </div>
        )}
      </Container>
    </section>
  );
});
