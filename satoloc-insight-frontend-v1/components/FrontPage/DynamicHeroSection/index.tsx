"use client";

import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { GradientText } from "../GradientText";
import { ArrowRight, Zap } from "lucide-react";
import { useHomepageContent } from "@/internal-api/wordpress-blog";
import { useState, useEffect } from "react";
import Image from "next/image";
import { useTheme } from "next-themes";
import SatoLOCLogo from "@/public/images/satoloc_insight_logo.png";
import SatoLOCLogoWhite from "@/public/images/satoloc_insight_logo_white.png";

interface DynamicHeroSectionProps {
  onJoinBetaClick?: () => void;
}

// Component to handle logo switching based on theme
function LogoWithTheme() {
  const { theme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // After mounting, we can access the theme
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // Return a placeholder during SSR
    return <div style={{ width: 120, height: 120 }} />;
  }

  const isDarkTheme = theme === "dark" || resolvedTheme === "dark";
  const logoSrc = isDarkTheme ? SatoLOCLogoWhite.src : SatoLOCLogo.src;

  return (
    <Image
      src={logoSrc}
      alt="SatoLOC Insight Logo"
      width={120}
      height={120}
      priority
      style={{
        objectFit: "contain",
      }}
    />
  );
}

export function DynamicHeroSection({
  onJoinBetaClick,
}: DynamicHeroSectionProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Fetch homepage content from WordPress
  const { data: homepageContent, isLoading, error } = useHomepageContent();

  const handlePrimaryButtonClick = () => {
    if (onJoinBetaClick) {
      onJoinBetaClick();
    } else {
      setIsModalOpen(true);
    }
  };

  const handleSecondaryButtonClick = () => {
    window.open("https://calendly.com/satoloc/30min", "_blank");
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="text-center">
        <div className="animate-pulse">
          <div className="h-32 w-32 bg-muted rounded mx-auto mb-6" />
          <div className="h-16 bg-muted rounded w-3/4 mx-auto mb-6" />
          <div className="h-6 bg-muted rounded w-full max-w-2xl mx-auto mb-12" />
          <div className="flex items-center justify-center gap-6">
            <div className="h-12 bg-muted rounded w-32" />
            <div className="h-12 bg-muted rounded w-32" />
          </div>
        </div>
      </div>
    );
  }

  // Show error or no content state
  if (error || !homepageContent?.header?.title) {
    return (
      <div className="text-center">
        <motion.div
          initial={{ scale: 0.9, rotate: -10 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{
            duration: 0.8,
            type: "spring",
            stiffness: 100,
          }}
          className="mb-10 inline-block"
        >
          <LogoWithTheme />
        </motion.div>
        <div className="p-8 border border-dashed border-muted-foreground/30 rounded-lg">
          <h2 className="text-2xl font-semibold mb-4 text-muted-foreground">
            No Homepage Content
          </h2>
          <p className="text-muted-foreground mb-4">
            Please add content in the WordPress admin panel.
          </p>
          {error && (
            <p className="text-sm text-destructive">Error: {error.message}</p>
          )}
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8 }}
      className="text-center"
    >
      <motion.div
        initial={{ scale: 0.9, rotate: -10 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{
          duration: 0.8,
          type: "spring",
          stiffness: 100,
        }}
        className="mb-10 inline-block"
      >
        <LogoWithTheme />
      </motion.div>

      <motion.h1
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.8 }}
        className="text-7xl font-bold tracking-tight mb-10"
      >
        <GradientText>{homepageContent.header.title}</GradientText>
      </motion.h1>

      {homepageContent.header.description && (
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.8 }}
          className="text-xl text-muted-foreground max-w-2xl mx-auto mb-12"
        >
          {homepageContent.header.description}
        </motion.p>
      )}

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7, duration: 0.8 }}
        className="flex items-center justify-center gap-6"
      >
        <Button
          size="lg"
          className="group px-8 py-6 relative overflow-hidden bg-gradient-to-r from-primary via-[#1279b4] to-primary animate-gradient-x hover:bg-[#1279b4]/80 text-primary-foreground text-lg"
          style={{
            backgroundSize: "200% 100%",
            transition: "all 0.3s ease",
          }}
          onClick={handlePrimaryButtonClick}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundPosition = "-100% 0";
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundPosition = "0 0";
          }}
        >
          Join the Beta
          <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
        </Button>

        <Button
          size="lg"
          variant="outline"
          className="group px-8 py-6 relative overflow-hidden hover:border-primary transition-colors text-lg"
          onClick={handleSecondaryButtonClick}
        >
          Book a Demo
          <Zap className="ml-2 h-5 w-5 group-hover:scale-110 transition-transform" />
        </Button>
      </motion.div>

      {/* Show content source indicator in development */}
      {process.env.NODE_ENV === "development" && (
        <div className="mt-4 text-xs text-muted-foreground">
          Content source: WordPress CMS
          {error && " (Error occurred)"}
        </div>
      )}
    </motion.div>
  );
}
