"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Container } from "@mui/material";
import { GradientText } from "@/components/FrontPage/GradientText";
import { useHomepageContent } from "@/internal-api/wordpress-blog";
import { ChevronDown } from "lucide-react";
import { Pagination } from "@/components/Pagination";

export const FAQSection = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const { data: homepageContent, isLoading, error } = useHomepageContent();

  // Pagination settings
  const itemsPerPage = 5;
  const totalItems = homepageContent?.faq?.items?.length || 0;
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Calculate current page items
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems =
    homepageContent?.faq?.items?.slice(startIndex, endIndex) || [];

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    setOpenIndex(null); // Close any open FAQ when changing pages
  };

  if (isLoading) {
    return (
      <section id="faq" className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background opacity-50" />
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 right-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl" />
          <div className="absolute bottom-1/4 left-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl" />
        </div>

        <Container className="container px-4 relative z-10">
          <div className="text-center mb-16">
            <div className="animate-pulse">
              <div className="h-12 bg-muted rounded w-1/2 mx-auto mb-4" />
              <div className="h-6 bg-muted rounded w-1/3 mx-auto" />
            </div>
          </div>
          <div className="max-w-4xl mx-auto space-y-4">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="h-16 bg-muted rounded-lg" />
              </div>
            ))}
          </div>
        </Container>
      </section>
    );
  }

  // Show error or no content state
  if (
    error ||
    !homepageContent?.faq ||
    homepageContent.faq.items.length === 0
  ) {
    return;
  }

  return (
    <section id="faq" className="py-24 relative overflow-hidden">
      <Container className="container px-4 relative z-10">
        {/* Section header */}
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-4xl font-bold mb-4"
          >
            <GradientText>
              {homepageContent.faq.title || "Frequently Asked Questions"}
            </GradientText>
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-muted-foreground max-w-2xl mx-auto font-thin"
          >
            {homepageContent.faq.description ||
              "Everything you need to know about SatoLOC Insight and how it can transform your multilingual content strategy."}
          </motion.p>
        </div>

        {/* FAQ Cards Container with Auto Height */}
        <div className="max-w-4xl mx-auto">
          <div className="min-h-[400px] flex flex-col">
            <div className="grid gap-4">
              {currentItems.map((item, index) => {
                const globalIndex = startIndex + index; // Calculate global index for unique IDs
                return (
                  <motion.div
                    key={globalIndex}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: index * 0.05 }}
                    className={`rounded-xl overflow-hidden transition-all duration-300 ${
                      openIndex === index
                        ? "bg-white dark:bg-black shadow-none border-primary/20"
                        : "bg-white/80 dark:bg-black/80 hover:bg-white dark:hover:bg-black shadow-none"
                    } backdrop-blur-sm border border-primary/10`}
                  >
                    <button
                      onClick={() => toggleFAQ(index)}
                      className="w-full px-6 py-5 flex items-center justify-between text-left focus:outline-none"
                      aria-expanded={openIndex === index}
                      aria-controls={`faq-answer-${globalIndex}`}
                    >
                      <h3
                        className={`text-md font-normal transition-colors ${
                          openIndex === index
                            ? "text-primary"
                            : "group-hover:text-primary"
                        }`}
                      >
                        {item.question}
                      </h3>
                      <motion.div
                        animate={{ rotate: openIndex === index ? 180 : 0 }}
                        transition={{ duration: 0.3 }}
                        className={`ml-4 flex-shrink-0 p-1 rounded-full ${
                          openIndex === index
                            ? "bg-primary text-white"
                            : "bg-primary/10 text-primary"
                        }`}
                      >
                        <ChevronDown className="h-4 w-4" />
                      </motion.div>
                    </button>

                    <AnimatePresence initial={false}>
                      {openIndex === index && (
                        <motion.div
                          id={`faq-answer-${globalIndex}`}
                          initial={{ height: 0, opacity: 0 }}
                          animate={{
                            height: "auto",
                            opacity: 1,
                            transition: {
                              height: { duration: 0.3 },
                              opacity: { duration: 0.3, delay: 0.1 },
                            },
                          }}
                          exit={{
                            height: 0,
                            opacity: 0,
                            transition: {
                              height: { duration: 0.3 },
                              opacity: { duration: 0.2 },
                            },
                          }}
                          className="overflow-hidden"
                        >
                          <div className="px-6 pb-5 text-muted-foreground">
                            <div
                              dangerouslySetInnerHTML={{ __html: item.answer }}
                              className="prose prose-sm max-w-none dark:prose-invert"
                            />
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3 }}
            className="mt-6 flex justify-center"
          >
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
              className=""
            />
          </motion.div>
        )}

        {/* Show content source indicator in development */}
        {process.env.NODE_ENV === "development" && (
          <div className="mt-8 text-center text-xs text-muted-foreground">
            FAQ content source: WordPress CMS (
            {homepageContent.faq.items.length} FAQs loaded)
          </div>
        )}
      </Container>
    </section>
  );
};

export default FAQSection;
