"use client";

import { motion } from "framer-motion";
import { Container } from "@mui/material";
import { Card } from "@/components/ui/card";
import { GradientText } from "../GradientText";
import { useHomepageContent } from "@/internal-api/wordpress-blog";
import { ExternalLink, Award } from "lucide-react";
import Image from "next/image";
import shesMercedesLogo from "@/public/images/shes-mercedes.png";

export function AwardsSection() {
  const { data: homepageContent, isLoading, error } = useHomepageContent();

  if (isLoading) {
    return (
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background" />
          <div className="absolute top-1/4 right-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl" />
          <div className="absolute bottom-1/4 left-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl" />
        </div>

        <Container className="container px-4 relative z-10">
          <div className="text-center mb-16">
            <div className="animate-pulse">
              <div className="h-12 bg-muted rounded w-1/2 mx-auto mb-4" />
              <div className="h-6 bg-muted rounded w-1/3 mx-auto" />
            </div>
          </div>
          <div className="max-w-4xl mx-auto">
            <div className="animate-pulse">
              <div className="h-64 bg-muted rounded-xl" />
            </div>
          </div>
        </Container>
      </section>
    );
  }

  // Show error or no content state
  if (error || !homepageContent?.awards) {
    return (
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0">
          {/* <div className="absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background" /> */}
          {/* <div className="absolute top-1/4 right-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl" />
          <div className="absolute bottom-1/4 left-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl" /> */}
        </div>

        <Container className="container px-4 relative z-10">
          <div className="text-center">
            <div className="p-8 border border-dashed border-muted-foreground/30 rounded-lg max-w-2xl mx-auto">
              <h2 className="text-2xl font-semibold mb-4 text-muted-foreground">
                No Awards Content
              </h2>
              <p className="text-muted-foreground mb-4">
                Please add awards content in the WordPress admin panel under the
                "Award Section".
              </p>
              {error && (
                <p className="text-sm text-destructive">
                  Error: {error.message}
                </p>
              )}
            </div>
          </div>
        </Container>
      </section>
    );
  }

  return (
    <section className="py-24 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0">
        {/* <div className="absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background" /> */}
        {/* <div className="absolute inset-0 " />
        <div className="absolute top-1/4 right-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 left-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl" /> */}
      </div>

      <Container className="container px-4 relative z-10">
        {/* Section header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="flex items-center justify-center gap-3 mb-4">
            <motion.div
              initial={{ scale: 0, rotate: -10 }}
              whileInView={{ scale: 1, rotate: 0 }}
              viewport={{ once: true }}
              transition={{ type: "spring", stiffness: 200, delay: 0.2 }}
              className="bg-primary/10 p-3 rounded-full"
            >
              <Award className="h-6 w-6 text-primary" />
            </motion.div>
            <h2 className="text-4xl font-bold">
              <GradientText>{homepageContent.awards.title}</GradientText>
            </h2>
          </div>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            {homepageContent.awards.description}
          </p>
        </motion.div>

        {/* Award card */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.7 }}
          whileHover={{ y: -5 }}
          className="max-w-4xl mx-auto"
        >
          <Card className="overflow-hidden bg-background rounded-lg shadow-none">
            <div className="grid md:grid-cols-2 items-center">
              {/* Logo side */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.3 }}
                className="p-8 flex items-center justify-center "
              >
                <div className="relative w-full max-w-[280px] h-[180px] flex items-center justify-center rounded-lg overflow-hidden ">
                  <div className="absolute inset-0 bg-white" />
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.3 }}
                    className="relative z-10"
                  >
                    <Image
                      src={shesMercedesLogo}
                      alt="She's Mercedes Logo"
                      width={250}
                      height={150}
                      className="object-contain"
                    />
                  </motion.div>
                </div>
              </motion.div>

              {/* Content side */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.4 }}
                className="p-8"
              >
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl font-semibold mb-4">
                      Read More About Our Achievement
                    </h3>
                    <div className="space-y-4">
                      <a
                        href="https://medya.mercedes-benz.com.tr/shes-mentoring-programnn-ilk-grubu-mezun-oldu/"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-3 p-3 rounded-lg bg-primary/5 hover:bg-primary/10 text-primary transition-all duration-300 group"
                      >
                        <div className="p-2 bg-primary/10 rounded-full group-hover:bg-primary/20 transition-colors">
                          <ExternalLink className="h-4 w-4" />
                        </div>
                        <span className="font-medium">
                          Official Mercedes-Benz Press Release
                        </span>
                      </a>
                      <a
                        href="https://www.satoloc.com/blog/satoloc-insight-wins-first-place-at-shes-mentoring-program/"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-3 p-3 rounded-lg bg-primary/5 hover:bg-primary/10 text-primary transition-all duration-300 group"
                      >
                        <div className="p-2 bg-primary/10 rounded-full group-hover:bg-primary/20 transition-colors">
                          <ExternalLink className="h-4 w-4" />
                        </div>
                        <span className="font-medium">
                          SatoLOC Insight's Journey to Victory
                        </span>
                      </a>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </Card>
        </motion.div>

        {/* Show content source indicator in development */}
        {process.env.NODE_ENV === "development" && (
          <div className="mt-8 text-center text-xs text-muted-foreground">
            Awards content source: WordPress CMS
          </div>
        )}
      </Container>
    </section>
  );
}
