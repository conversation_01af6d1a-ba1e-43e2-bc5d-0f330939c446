"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { motion } from "framer-motion";
import { Check, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Container } from "@mui/material";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { GradientText } from "../GradientText";
import { useHomepageContent } from "@/internal-api/wordpress-blog";
import { BillingToggle } from "@/components/stripe/BillingToggle";
import { useStripeCheckout } from "@/hooks/use-stripe-checkout";
import { usePricing } from "@/hooks/use-pricing";
import { useSubscription } from "@/hooks/use-subscription";
import { toast } from "sonner";

interface PricingFeature {
  name: string;
  included: boolean;
}

interface PricingTier {
  name: string;
  description: string;
  price: string;
  annualPrice: string;
  discount: string;
  audience: string;
  translationLimit: string;
  contentLimit: string;
  seoAnalysis: string;
  teamMembers: string;
  features: PricingFeature[];
  popular?: boolean;
  buttonText: string;
  buttonVariant?:
    | "default"
    | "outline"
    | "secondary"
    | "ghost"
    | "link"
    | "destructive";
  isComing?: boolean;
}

export function PricingSection() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const { data: homepageContent, isLoading, error } = useHomepageContent();
  const [isYearly, setIsYearly] = useState(false);

  // Stripe hooks
  const { products: stripeProducts } = usePricing();
  const { subscription, isSubscriptionActive, planType } = useSubscription();
  const { createCheckoutSession, isLoading: checkoutLoading } =
    useStripeCheckout();
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);

  // Helper function to get Stripe price for a plan
  const getStripePriceForPlan = (planName: string) => {
    // Simple mapping for the three plans: freemium, pro, premium
    const planType = planName.toLowerCase();

    if (!["freemium", "pro", "premium"].includes(planType)) {
      console.error("Invalid plan type:", planType);
      return null;
    }

    const product = stripeProducts.find((p) => p.plan_type === planType);
    if (!product || !product.prices.length) {
      console.error("No Stripe product found for plan type:", planType);
      return null;
    }

    const activePrice = product.prices.find((p) => p.is_active);

    return activePrice;
  };

  // Helper function to handle Stripe checkout
  const handleStripeCheckout = async (planName: string) => {
    if (!planName) return;

    setSelectedPlan(planName);

    try {
      const planType = planName.toLowerCase();

      // For freemium plan, always redirect to registration
      if (planType === "freemium") {
        const registrationUrl = new URL(
          "/registration",
          window.location.origin
        );
        registrationUrl.searchParams.set("plan", planType);

        router.push(registrationUrl.toString());
        return;
      }

      // For paid plans, check authentication status
      if (status === "authenticated" && session) {
        // User is logged in - proceed with Stripe checkout

        const stripePrice = getStripePriceForPlan(planName);
        if (!stripePrice) {
          console.error("No Stripe price found for plan:", planName);
          toast.error("Pricing information not available for this plan");
          return;
        }

        // Create checkout session
        await createCheckoutSession({
          price_id: stripePrice.stripe_price_id,
          success_url: `${window.location.origin}/profile?session_id={CHECKOUT_SESSION_ID}&payment_success=true`,
          cancel_url: `${window.location.origin}/`,
          trial_days: undefined,
        });
      } else {
        // User is not logged in - redirect to registration with plan data

        const stripePrice = getStripePriceForPlan(planName);
        if (!stripePrice) {
          console.error("No Stripe price found for plan:", planName);
          toast.error("Pricing information not available for this plan");
          return;
        }

        const registrationUrl = new URL(
          "/registration",
          window.location.origin
        );
        registrationUrl.searchParams.set("plan", planType);
        registrationUrl.searchParams.set("price", stripePrice.amount);

        router.push(registrationUrl.toString());
      }
    } catch (error) {
      console.error("Error handling plan selection:", error);
      toast.error("Failed to process plan selection");
    } finally {
      setSelectedPlan(null);
    }
  };

  // Helper function to check if plan is current user's plan
  const isCurrentPlan = (planName: string) => {
    const planType = planName.toLowerCase();
    return planType === subscription?.plan_type;
  };

  if (isLoading) {
    return (
      <section className="py-24 bg-gradient-to-b from-background via-primary/5 to-background">
        <Container className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
            <div className="animate-pulse">
              <div className="h-6 bg-muted rounded w-20 mx-auto mb-4" />
              <div className="h-10 bg-muted rounded w-80 mx-auto mb-4" />
              <div className="h-6 bg-muted rounded w-96 mx-auto" />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-4xl mx-auto">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="h-96 bg-muted rounded-lg" />
              </div>
            ))}
          </div>
        </Container>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-24 bg-gradient-to-b from-background via-primary/5 to-background">
        <Container className="container px-4 md:px-6">
          <div className="text-center">
            <p className="text-muted-foreground">
              Unable to load pricing information. Please try again later.
            </p>
          </div>
        </Container>
      </section>
    );
  }

  // Convert WordPress subscription repeater data to component format
  const subscriptionData = Array.isArray(homepageContent?.pricing?.plans)
    ? homepageContent.pricing.plans
    : [];

  const pricingTiers: PricingTier[] = subscriptionData.map(
    (plan: any, index: number) => {
      return {
        name: plan.title || "Plan",
        description: plan.description || "Pricing plan",
        price: plan.price
          ? plan.price === "0"
            ? "Free"
            : `$${plan.price}`
          : "Contact Us",
        annualPrice: plan.annual_price
          ? plan.annual_price === "0"
            ? "Free"
            : `$${plan.annual_price}`
          : "Contact Us",
        discount: plan.discount ? `(${plan.discount}% discount)` : "",
        audience: plan.audience || "N/A",
        translationLimit: plan.translation_limit
          ? `${parseInt(plan.translation_limit).toLocaleString()} words`
          : "N/A",
        contentLimit: plan.content_limit
          ? `${parseInt(plan.content_limit).toLocaleString()} words`
          : "N/A",
        seoAnalysis: plan.seo_analysis || "N/A",
        teamMembers: plan.team_members ? plan.team_members.toString() : "1",
        features: plan.features || [],
        popular: index === 1, // Set second plan as popular by default
        buttonText: plan.button_text || "Get Started",
        buttonVariant: (plan.button_variant as any) || "outline",
        isComing: plan.is_coming || false,
      };
    }
  );

  // Show message if no pricing plans are configured
  if (subscriptionData.length === 0 || pricingTiers.length === 0) {
    return (
      <section className="py-24 ">
        <Container className="container px-4 md:px-6">
          <div className="text-center">
            <Badge
              variant="outline"
              className="mb-3 px-3 py-1 text-sm border border-[#1279b4] text-[#1279b4]"
            >
              Pricing
            </Badge>
            <h2 className="text-4xl font-bold mb-4">
              <GradientText className="text-3xl md:text-4xl font-bold tracking-tight">
                Choose the right plan for your needs
              </GradientText>
            </h2>
            <p className="text-muted-foreground">
              Pricing plans are being configured. Please check back soon.
            </p>
            {process.env.NODE_ENV === "development" && (
              <div className="mt-4 text-xs text-muted-foreground">
                No pricing plans configured in WordPress CMS (Pricing Section)
              </div>
            )}
          </div>
        </Container>
      </section>
    );
  }

  return (
    <section className="py-24 bg-background">
      <Container className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <Badge
              variant="outline"
              className="mb-3 px-3 py-1 text-sm border border-[#1279b4] text-[#1279b4]"
            >
              Pricing
            </Badge>
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-4xl font-bold mb-4"
            >
              <GradientText className="text-3xl md:text-4xl font-bold tracking-tight">
                {homepageContent?.pricing?.title}
              </GradientText>
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 }}
              className="text-muted-foreground max-w-2xl mx-auto"
            >
              {homepageContent?.pricing?.description}
            </motion.p>

            {/* Current subscription status */}
            {isSubscriptionActive && planType && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.15 }}
                className="mt-4"
              >
                <Badge variant="secondary" className="text-sm">
                  Current Plan:{" "}
                  {planType.charAt(0).toUpperCase() + planType.slice(1)}
                </Badge>
              </motion.div>
            )}

            {/* Billing Toggle */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
              className="mt-8"
            >
              <BillingToggle
                isYearly={isYearly}
                onToggle={setIsYearly}
                yearlyDiscount="Save 17%"
                className="border border-primary/20 rounded-md p-2"
              />
            </motion.div>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-4xl mx-auto">
          {pricingTiers.map((tier, index) => (
            <motion.div
              key={tier.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className={`${!tier.popular ? "mt-8" : ""}`}
            >
              <Card
                className={`h-full flex flex-col ${tier.popular ? "border-[#1279b4] shadow-lg shadow-[#1279b4]/10 relative overflow-hidden" : ""} ${tier.isComing ? "opacity-60" : ""} ${isCurrentPlan(tier.name) ? "ring-2 ring-primary" : ""}`}
              >
                {tier.popular && (
                  <div className="absolute top-0 right-0">
                    <div className="bg-gradient-to-r from-primary via-[#1279b4] to-primary animate-gradient-x text-primary-foreground text-xs font-medium px-3 py-1 rounded-bl-lg">
                      Most Popular
                    </div>
                  </div>
                )}

                {isCurrentPlan(tier.name) && (
                  <div className="absolute top-0 left-0">
                    <div className="bg-green-500 text-white text-xs font-medium px-3 py-1 rounded-br-lg">
                      Current Plan
                    </div>
                  </div>
                )}
                <CardHeader>
                  <CardTitle>{tier.name}</CardTitle>
                  <CardDescription>{tier.description}</CardDescription>
                  <div className="mt-4">
                    <span className="text-4xl font-bold text-[#1279b4]">
                      {tier.name === "Freemium"
                        ? "Free"
                        : isYearly
                          ? `$${Math.floor(parseInt(tier.annualPrice.replace("$", "").replace(",", "")) / 12)}`
                          : tier.price}
                    </span>
                    <span className="text-muted-foreground ml-2">
                      {tier.name === "Freemium" ? "" : "/month"}
                    </span>
                    {isYearly && tier.name !== "Freemium" && (
                      <p className="text-sm text-muted-foreground mt-1">
                        Billed annually ({tier.annualPrice})
                      </p>
                    )}
                    {!isYearly && tier.discount && tier.name !== "Freemium" && (
                      <p className="text-sm text-green-600 mt-1">
                        Save {tier.discount} with yearly billing
                      </p>
                    )}
                    {tier.audience && (
                      <p className="text-sm text-muted-foreground mt-1">
                        {tier.audience}
                      </p>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="flex-grow">
                  <div className="grid gap-4">
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="font-medium">Translation Limit:</div>
                      <div>{tier.translationLimit}</div>

                      <div className="font-medium">Content Creation:</div>
                      <div>{tier.contentLimit}</div>

                      <div className="font-medium">SEO Analysis:</div>
                      <div>{tier.seoAnalysis}</div>

                      <div className="font-medium">Team Members:</div>
                      <div>{tier.teamMembers}</div>
                    </div>

                    {tier.features.length > 0 && (
                      <div className="border-t border-border pt-4 mt-2">
                        <p className="font-medium mb-2">Features:</p>
                        <ul className="space-y-2">
                          {tier.features.map((feature, i) => (
                            <li
                              key={i}
                              className="flex items-start gap-2 text-sm"
                            >
                              {feature.included ? (
                                <Check className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                              ) : (
                                <X className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                              )}
                              <span>{feature.name}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter>
                  {tier.isComing ? (
                    <Button
                      className="w-full bg-muted text-muted-foreground cursor-not-allowed"
                      variant="outline"
                      size="lg"
                      disabled
                    >
                      Coming Soon
                    </Button>
                  ) : isCurrentPlan(tier.name) ? (
                    <Button
                      className="w-full bg-green-500 text-white cursor-not-allowed"
                      variant="default"
                      size="lg"
                      disabled
                    >
                      Current Plan
                    </Button>
                  ) : tier.name === "Freemium" ? (
                    <Button
                      className="w-full border-[#1279b4]"
                      variant="outline"
                      size="lg"
                      onClick={() => handleStripeCheckout(tier.name)}
                      disabled={checkoutLoading && selectedPlan === tier.name}
                    >
                      {checkoutLoading && selectedPlan === tier.name ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                          Processing...
                        </>
                      ) : (
                        tier.buttonText
                      )}
                    </Button>
                  ) : (
                    <Button
                      className={`w-full ${
                        tier.popular
                          ? "bg-gradient-to-r from-primary via-[#1279b4] to-primary animate-gradient-x text-white border-0 hover:opacity-90"
                          : "border-[#1279b4] text-[#1279b4] hover:bg-[#1279b4] hover:text-white"
                      }`}
                      variant={tier.popular ? "default" : "outline"}
                      size="lg"
                      onClick={() => handleStripeCheckout(tier.name)}
                      disabled={checkoutLoading && selectedPlan === tier.name}
                    >
                      {checkoutLoading && selectedPlan === tier.name ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                          Processing...
                        </>
                      ) : (
                        tier.buttonText
                      )}
                    </Button>
                  )}
                </CardFooter>
              </Card>
            </motion.div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <p className="text-muted-foreground">
            Need a custom solution?{" "}
            <span>
              <strong> <EMAIL></strong>
            </span>{" "}
            for more information.
          </p>
        </div>

        {/* Show content source indicator in development */}
        {process.env.NODE_ENV === "development" &&
          homepageContent?.pricing?.plans && (
            <div className="mt-8 text-xs text-muted-foreground text-center">
              Pricing content source: WordPress CMS (Pricing Section)
            </div>
          )}
      </Container>
    </section>
  );
}
