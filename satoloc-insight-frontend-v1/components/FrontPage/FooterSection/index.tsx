"use client";

import { motion } from "framer-motion";
import { Container } from "@mui/material";
import Image from "next/image";
import SatoL<PERSON><PERSON>ogo from "@/public/images/satoloc_insight_logo.png";
import { NewsletterForm } from "@/components/FrontPage/NewsletterForm";

interface FooterSectionProps {
  scrollToSection?: (ref: React.RefObject<HTMLDivElement>) => void;
  featuresRef?: React.RefObject<HTMLDivElement>;
  solutionsRef?: React.RefObject<HTMLDivElement>;
  roadmapRef?: React.RefObject<HTMLDivElement>;
  onJoinBetaClick?: () => void;
}

// Floating animation for background elements
const floatingAnimation = {
  y: ["-10%", "10%"],
  transition: {
    duration: 8,
    repeat: Infinity,
    repeatType: "reverse" as const,
    ease: "easeInOut",
  },
};

export const FooterSection = ({
  scrollToSection,
  featuresRef,
  solutionsRef,
  roadmapRef,
  onJoinBetaClick,
}: FooterSectionProps) => {
  return (
    <footer className="relative pt-16 pb-10 overflow-hidden border-t border-primary/10">
      {/* Animated background elements */}
      <motion.div animate={floatingAnimation} />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.3 }}
        transition={{ duration: 2 }}
        className="absolute inset-0"
      />

      <Container className="container px-4 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-10 mb-12">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-1">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="mb-6"
            >
              <Image
                src={SatoLOCLogo.src}
                alt="SatoLOC Insight Logo"
                width={60}
                height={60}
                className="mb-4"
              />
              <p className="text-sm text-muted-foreground mb-4 font-thin">
                Transforming global content strategies with AI-driven
                localization insights and intelligent content creation.
              </p>
              <div className="flex space-x-4">
                {[
                  {
                    icon: "linkedin",
                    href: "https://linkedin.com/company/satoloc",
                  },
                  {
                    icon: "instagram",
                    href: "https://instagram.com/satoloc",
                  },
                ].map((social, index) => (
                  <a
                    key={index}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary transition-colors"
                  >
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center hover:bg-primary/20 transition-colors">
                      {social.icon === "linkedin" && (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                          <rect x="2" y="9" width="4" height="12"></rect>
                          <circle cx="4" cy="4" r="2"></circle>
                        </svg>
                      )}
                      {social.icon === "instagram" && (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <rect
                            x="2"
                            y="2"
                            width="20"
                            height="20"
                            rx="5"
                            ry="5"
                          ></rect>
                          <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                          <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                        </svg>
                      )}
                    </div>
                  </a>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Quick Links */}
          <div className="col-span-1">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 }}
            >
              <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
              <ul className="space-y-2 font-thin text-sm">
                {[
                  { name: "Home", href: "/", onClick: undefined },
                  {
                    name: "Features",
                    href: "#features",
                    onClick: (e: React.MouseEvent) => {
                      e.preventDefault();
                      if (scrollToSection && featuresRef) {
                        scrollToSection(featuresRef);
                      }
                    },
                  },
                  {
                    name: "Solutions",
                    href: "#solutions",
                    onClick: (e: React.MouseEvent) => {
                      e.preventDefault();
                      if (scrollToSection && solutionsRef) {
                        scrollToSection(solutionsRef);
                      }
                    },
                  },
                  {
                    name: "Roadmap",
                    href: "#roadmap",
                    onClick: (e: React.MouseEvent) => {
                      e.preventDefault();
                      if (scrollToSection && roadmapRef) {
                        scrollToSection(roadmapRef);
                      }
                    },
                  },
                  {
                    name: "Beta Program",
                    href: "#beta",
                    onClick: (e: React.MouseEvent) => {
                      e.preventDefault();
                      if (onJoinBetaClick) {
                        onJoinBetaClick();
                      }
                    },
                  },
                ].map((link, index) => (
                  <li key={index}>
                    <a
                      href={link.href}
                      onClick={link.onClick}
                      className="text-muted-foreground hover:text-primary transition-colors"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>
          </div>

          {/* Services */}
          <div className="col-span-1">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
            >
              <h3 className="text-lg font-semibold mb-4">Services</h3>
              <ul className="space-y-2 font-thin text-sm">
                {[
                  {
                    name: "AI Localization",
                    href: "#localization",
                    onClick: (e: React.MouseEvent) => {
                      e.preventDefault();
                      if (scrollToSection && featuresRef) {
                        scrollToSection(featuresRef);
                      }
                    },
                  },
                  {
                    name: "SEO Optimization",
                    href: "#seo",
                    onClick: (e: React.MouseEvent) => {
                      e.preventDefault();
                      if (scrollToSection && featuresRef) {
                        scrollToSection(featuresRef);
                      }
                    },
                  },
                  {
                    name: "Content Analytics",
                    href: "#analytics",
                    onClick: (e: React.MouseEvent) => {
                      e.preventDefault();
                      if (scrollToSection && featuresRef) {
                        scrollToSection(featuresRef);
                      }
                    },
                  },
                  {
                    name: "Content Creation",
                    href: "#content",
                    onClick: (e: React.MouseEvent) => {
                      e.preventDefault();
                      if (scrollToSection && featuresRef) {
                        scrollToSection(featuresRef);
                      }
                    },
                  },
                  {
                    name: "AutoLQA",
                    href: "#autolqa",
                    onClick: (e: React.MouseEvent) => {
                      e.preventDefault();
                      if (scrollToSection && featuresRef) {
                        scrollToSection(featuresRef);
                      }
                    },
                  },
                ].map((service, index) => (
                  <li key={index}>
                    <a
                      href={service.href}
                      onClick={service.onClick}
                      className="text-muted-foreground hover:text-primary transition-colors"
                    >
                      {service.name}
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>
          </div>

          {/* Contact & Newsletter */}
          <div className="col-span-1">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.3 }}
            >
              <h3 className="text-lg font-semibold mb-4">Stay Updated</h3>
              <p className="text-sm text-muted-foreground mb-4 font-thin">
                Subscribe to our newsletter for the latest updates and insights.
              </p>
              <NewsletterForm />
              <div className="mt-6">
                <h4 className="text-sm font-semibold mb-2">Contact Us</h4>
                <p className="text-sm text-muted-foreground">
                  <a
                    href="mailto:<EMAIL>"
                    className="hover:text-primary transition-colors"
                  >
                    <EMAIL>
                  </a>
                </p>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="pt-8 border-t border-primary/10">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-muted-foreground mb-4 md:mb-0 font-thin">
              © {new Date().getFullYear()} SatoLOC Insight. All rights
              reserved.
            </p>
            <div className="flex space-x-6">
              <a
                href="https://www.satoloc.com/privacy-policy/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-muted-foreground hover:text-primary transition-colors font-thin"
              >
                Privacy Policy
              </a>
              <a
                href="https://www.satoloc.com/terms-of-service-satoloc-insight/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-muted-foreground hover:text-primary transition-colors font-thin"
              >
                Terms of Service
              </a>
              <a
                href="/cookies"
                className="text-sm text-muted-foreground hover:text-primary transition-colors font-thin"
              >
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </Container>
    </footer>
  );
};
