"use client";

import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { GradientText } from "../GradientText";
import { Moon, Sun, Menu, X } from "lucide-react";
import { useTheme } from "next-themes";
import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import {
  Sheet,
  <PERSON>etContent,
  SheetHeader,
  She<PERSON><PERSON><PERSON>le,
  SheetTrigger,
} from "@/components/ui/sheet";

interface FloatingNavbarProps {
  onFeaturesClick: () => void;
  onSolutionsClick: () => void;
  onPricingClick?: () => void;
  onFAQClick?: () => void;
}

export function FloatingNavbar({
  onFeaturesClick,
  onSolutionsClick,
  onPricingClick,
  onFAQClick,
}: FloatingNavbarProps) {
  const { theme, setTheme } = useTheme();
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const { data: session, status } = useSession();

  const navItems = [
    { name: "Features", onClick: onFeaturesClick },
    { name: "Solutions", onClick: onSolutionsClick },
    {
      name: "Pricing",
      onClick:
        onPricingClick ||
        (() => {
          const pricingElement = document.getElementById("pricing");
          if (pricingElement) {
            pricingElement.scrollIntoView({ behavior: "smooth" });
          }
        }),
    },
    { name: "Blog", onClick: () => router.push("/blog") },
    // Only show Dashboard if user is authenticated
    ...(session && status === "authenticated"
      ? [{ name: "Dashboard", onClick: () => router.push("/dashboard") }]
      : []),
  ];

  const handleMobileNavClick = (callback: () => void) => {
    setIsOpen(false);
    callback();
  };

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-background backdrop-blur-sm border-b border-border/40">
      <motion.nav
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="mx-auto max-w-full px-6 py-4"
      >
        <div className="flex items-center justify-between">
          <motion.div
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            <GradientText className="text-xl font-bold">
              SatoLOC Insight
            </GradientText>
          </motion.div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center gap-6">
            {navItems.map((item, index) => (
              <motion.div
                key={item.name}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 + 0.3 }}
              >
                <Button
                  variant="ghost"
                  className="relative overflow-hidden group"
                  onClick={item.onClick}
                >
                  <span className="relative z-10">{item.name}</span>
                  <motion.div
                    className="absolute bottom-0 left-0 h-0.5 w-0 bg-primary"
                    whileHover={{ width: "100%" }}
                    transition={{ duration: 0.2 }}
                  />
                </Button>
              </motion.div>
            ))}

            <motion.div
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5 }}
            >
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
                className="mr-2 relative"
              >
                <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
                <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
                <span className="sr-only">Toggle theme</span>
              </Button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.6 }}
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  variant="default"
                  onClick={() => router.push("/login")}
                  className="relative overflow-hidden group bg-gradient-to-r from-primary via-[#1279b4] to-primary animate-gradient-x"
                >
                  <span className="relative z-10 ">Get Started</span>
                  <motion.div
                    className="absolute inset-0 bg-primary/20"
                    initial={{ scale: 0, borderRadius: "100%" }}
                    whileHover={{ scale: 1.5, borderRadius: "0%" }}
                    transition={{ duration: 0.4 }}
                  />
                </Button>
              </motion.div>
            </motion.div>
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
              className="relative"
            >
              <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
              <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
              <span className="sr-only">Toggle theme</span>
            </Button>

            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Menu className="h-6 w-6" />
                </Button>
              </SheetTrigger>
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>
                    <GradientText>Menu</GradientText>
                  </SheetTitle>
                </SheetHeader>
                <div className="flex flex-col gap-4 mt-8">
                  {navItems.map((item, index) => (
                    <Button
                      key={item.name}
                      variant="ghost"
                      className="w-full justify-start"
                      onClick={() => handleMobileNavClick(item.onClick)}
                    >
                      {item.name}
                    </Button>
                  ))}
                  <Button
                    variant="default"
                    className="w-full bg-gradient-to-r from-primary via-[#1279b4] to-primary animate-gradient-x"
                    onClick={() => {
                      setIsOpen(false);
                      router.push("/login");
                    }}
                  >
                    Get Started
                  </Button>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </motion.nav>
    </div>
  );
}
