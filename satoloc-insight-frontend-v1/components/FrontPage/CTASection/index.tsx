"use client";

import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { GradientText } from "@/components/FrontPage/GradientText";
import { useHomepageContent } from "@/internal-api/wordpress-blog";
import { Container } from "@mui/material";
import { ArrowRight, Zap } from "lucide-react";

interface CTASectionProps {
  onJoinBetaClick?: () => void;
}

export const CTASection = ({ onJoinBetaClick }: CTASectionProps) => {
  const { data: homepageContent, isLoading: isContentLoading } =
    useHomepageContent();

  return (
    <section className="py-24 relative overflow-hidden bg-gradient-to-b from-[#1279b4] to-[#1279b4]/80">
      <div className="absolute inset-0" />
      <Container className="container px-4 relative">
        {isContentLoading ? (
          <div className="text-center max-w-3xl mx-auto">
            <div className="animate-pulse">
              <div className="h-12 bg-muted rounded w-1/2 mx-auto mb-6" />
              <div className="h-6 bg-muted rounded w-1/3 mx-auto mb-8" />
              <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                <div className="h-12 bg-muted rounded w-32" />
                <div className="h-12 bg-muted rounded w-32" />
              </div>
            </div>
          </div>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center max-w-4xl mx-auto"
          >
            <h2 className="text-4xl font-bold mb-6 text-white">
              {/* <GradientText>
                {homepageContent?.auto_lqa?.title ||
              </GradientText> */}
              Your content is global. Now your strategy is too.
            </h2>
            <p className="text-lg text-white mb-8 font-thin max-w-2xl mx-auto">
              {/* {homepageContent?.auto_lqa?.description || */}
              Join innovative teams transforming how they create, translate, and
              optimize content for global audiences.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Button
                size="lg"
                className="group px-8 relative overflow-hidden bg-white text-[#1279b4] hover:bg-primary/90 font-semibold"
                style={{
                  backgroundSize: "200% 100%",
                  transition: "all 0.3s ease",
                }}
                onClick={onJoinBetaClick}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundPosition = "-100% 0";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundPosition = "0 0";
                }}
              >
                Join the Beta
                {/* <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" /> */}
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="group px-8 w-full sm:w-auto bg-transparent text-white border border-white font-semibold"
                onClick={() => {
                  window.open("https://calendly.com/satoloc/30min", "_blank");
                }}
              >
                Book a Demo
                {/* <Zap className="ml-2 h-4 w-4 group-hover:scale-110 transition-transform" /> */}
              </Button>
            </div>

            {/* Show content source indicator in development */}
            {process.env.NODE_ENV === "development" &&
              homepageContent?.auto_lqa && (
                <div className="mt-8 text-xs text-white">
                  CTA content source: WordPress CMS (Auto LQA section)
                </div>
              )}
          </motion.div>
        )}
      </Container>
    </section>
  );
};
