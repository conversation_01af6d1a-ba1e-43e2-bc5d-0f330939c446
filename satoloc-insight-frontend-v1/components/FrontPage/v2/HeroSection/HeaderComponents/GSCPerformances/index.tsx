"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Loader2,
  AlertCircle,
  Globe,
  BarChart3,
  ExternalLink,
  CheckCircle2,
  RefreshCw,
  Mouse,
  Eye,
  TrendingUp,
  MapPin,
} from "lucide-react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";

// Mock data for demonstration
const MOCK_SITES = [
  {
    site_url: "https://satoloc.com",
    permission_level: "siteOwner",
  },
  {
    site_url: "https://www.satoloc.com",
    permission_level: "siteOwner",
  },
];

const MOCK_PERFORMANCE_DATA = {
  clicks: 88,
  impressions: 7800,
  ctr: 1.1,
  position: 26.7,
};

const MOCK_CHART_DATA = [
  { date: "Apr 28", clicks: 45, impressions: 3500, ctr: 1.3, position: 28.5 },
  { date: "May 5", clicks: 52, impressions: 4200, ctr: 1.2, position: 27.8 },
  { date: "May 12", clicks: 38, impressions: 3800, ctr: 1.0, position: 29.2 },
  { date: "May 19", clicks: 65, impressions: 5200, ctr: 1.25, position: 25.4 },
  { date: "May 26", clicks: 72, impressions: 6100, ctr: 1.18, position: 24.8 },
  { date: "Jun 2", clicks: 58, impressions: 4900, ctr: 1.18, position: 26.1 },
  { date: "Jun 9", clicks: 88, impressions: 7800, ctr: 1.1, position: 26.7 },
];

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  enabled: boolean;
  onToggle: () => void;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  icon,
  enabled,
  onToggle,
}) => {
  const handleCardClick = (e: React.MouseEvent) => {
    if ((e.target as HTMLInputElement).type !== "checkbox") {
      e.preventDefault();
      e.stopPropagation();
      onToggle();
    }
  };

  const handleCheckboxClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onToggle();
  };

  return (
    <div
      className={`
        relative p-4 rounded-lg border cursor-pointer transition-all duration-200 bg-white dark:bg-background/10
        ${enabled ? "border-gray-300" : "border-gray-200 opacity-60"}
      `}
      onClick={handleCardClick}
    >
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={enabled}
            onChange={() => {}}
            onClick={handleCheckboxClick}
            className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
          />
          <span
            className={`text-xs font-medium ${enabled ? "text-gray-700" : "text-gray-400"} dark:text-white`}
          >
            {title}
          </span>
        </div>
        <div
          className={`${enabled ? "text-gray-600" : "text-gray-400"} dark:text-white`}
        >
          {icon}
        </div>
      </div>
      <div
        className={`text-xl font-bold ${enabled ? "text-gray-900" : "text-gray-400"} dark:text-white`}
      >
        {value}
      </div>
    </div>
  );
};

const MetricCardSkeleton: React.FC = () => (
  <div className="relative p-4 rounded-lg border bg-white dark:bg-background/10 border-gray-200">
    <div className="flex items-center justify-between mb-2">
      <div className="flex items-center space-x-2">
        <Skeleton className="w-4 h-4 rounded" />
        <Skeleton className="h-4 w-24" />
      </div>
      <Skeleton className="w-4 h-4" />
    </div>
    <Skeleton className="h-8 w-16" />
  </div>
);

export const GSCPerformances = () => {
  const [selectedSite, setSelectedSite] = useState<string | null>(
    "https://satoloc.com"
  );
  const [timeRange, setTimeRange] = useState("3months");
  const [searchType, setSearchType] = useState("web");
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(true);
  const [enabledMetrics, setEnabledMetrics] = useState({
    clicks: true,
    impressions: true,
    ctr: true,
    position: true,
  });

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const forceRefresh = async () => {
    setIsLoading(true);
    // Mock refresh delay
    await new Promise((resolve) => setTimeout(resolve, 2000));
    setIsLoading(false);
  };

  const toggleMetric = (metric: keyof typeof enabledMetrics) => {
    setEnabledMetrics((prev) => ({
      ...prev,
      [metric]: !prev[metric],
    }));
  };

  return (
    <Card className="w-full shadow-none rounded-xl">
      <CardContent>
        {selectedSite && (
          <div className="mt-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold">Performance Overview</h3>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  Last update: 7/27/2025, 6:01:03 PM
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Select value={timeRange} onValueChange={setTimeRange}>
                  <SelectTrigger className="w-32 h-8 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="24hours">24 hours</SelectItem>
                    <SelectItem value="7days">7 days</SelectItem>
                    <SelectItem value="28days">28 days</SelectItem>
                    <SelectItem value="3months">3 months</SelectItem>
                  </SelectContent>
                </Select>
                <Badge variant="secondary" className="text-xs">
                  {searchType === "web" ? "Web" : "Image"}
                </Badge>
              </div>
            </div>

            {/* Metric Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              {isLoading ? (
                <>
                  <MetricCardSkeleton />
                  <MetricCardSkeleton />
                  <MetricCardSkeleton />
                  <MetricCardSkeleton />
                </>
              ) : (
                <>
                  <MetricCard
                    title="Total clicks"
                    value={formatNumber(MOCK_PERFORMANCE_DATA.clicks)}
                    icon={<Mouse className="h-4 w-4" />}
                    enabled={enabledMetrics.clicks}
                    onToggle={() => toggleMetric("clicks")}
                  />
                  <MetricCard
                    title="Total impressions"
                    value={formatNumber(MOCK_PERFORMANCE_DATA.impressions)}
                    icon={<Eye className="h-4 w-4" />}
                    enabled={enabledMetrics.impressions}
                    onToggle={() => toggleMetric("impressions")}
                  />
                  <MetricCard
                    title="Average CTR"
                    value={`${MOCK_PERFORMANCE_DATA.ctr.toFixed(1)}%`}
                    icon={<TrendingUp className="h-4 w-4" />}
                    enabled={enabledMetrics.ctr}
                    onToggle={() => toggleMetric("ctr")}
                  />
                  <MetricCard
                    title="Average position"
                    value={MOCK_PERFORMANCE_DATA.position.toFixed(1)}
                    icon={<MapPin className="h-4 w-4" />}
                    enabled={enabledMetrics.position}
                    onToggle={() => toggleMetric("position")}
                  />
                </>
              )}
            </div>

            {/* Performance Chart */}
            <div className="mt-6">
              <Card className="w-full border shadow-none">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <BarChart3 className="h-5 w-5 text-primary" />
                      <CardTitle className="text-base">
                        Performance Chart
                      </CardTitle>
                      <Badge variant="secondary" className="text-xs">
                        (90 days)
                      </Badge>
                    </div>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <Skeleton className="h-64 w-full" />
                  ) : (
                    <div className="h-64">
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart data={MOCK_CHART_DATA}>
                          <CartesianGrid
                            strokeDasharray="3 3"
                            className="opacity-30"
                          />
                          <XAxis
                            dataKey="date"
                            tick={{ fontSize: 12 }}
                            className="text-gray-600 dark:text-gray-400"
                          />
                          <YAxis
                            tick={{ fontSize: 12 }}
                            className="text-gray-600 dark:text-gray-400"
                          />
                          <Tooltip
                            contentStyle={{
                              backgroundColor: "white",
                              border: "1px solid #e5e7eb",
                              borderRadius: "6px",
                              fontSize: "12px",
                            }}
                          />
                          <Legend fontSize={10} />
                          {enabledMetrics.clicks && (
                            <Line
                              type="monotone"
                              dataKey="clicks"
                              stroke="#3b82f6"
                              strokeWidth={2}
                              name="Clicks"
                            />
                          )}
                          {enabledMetrics.impressions && (
                            <Line
                              type="monotone"
                              dataKey="impressions"
                              stroke="#10b981"
                              strokeWidth={2}
                              name="Impressions"
                            />
                          )}
                          {enabledMetrics.ctr && (
                            <Line
                              type="monotone"
                              dataKey="ctr"
                              stroke="#f59e0b"
                              strokeWidth={2}
                              name="CTR"
                            />
                          )}
                          {enabledMetrics.position && (
                            <Line
                              type="monotone"
                              dataKey="position"
                              stroke="#ef4444"
                              strokeWidth={2}
                              name="Position"
                            />
                          )}
                        </LineChart>
                      </ResponsiveContainer>
                    </div>
                  )}

                  {/* Chart Legend Summary */}
                  <div className="mt-4 flex items-center justify-center space-x-6 text-xs">
                    {enabledMetrics.clicks && (
                      <div className="flex items-center space-x-1">
                        <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                        <span className="text-gray-600 dark:text-gray-400">
                          Clicks
                        </span>
                        <span className="font-semibold">
                          {MOCK_PERFORMANCE_DATA.clicks}
                        </span>
                        <span className="text-gray-500">Total</span>
                      </div>
                    )}
                    {enabledMetrics.impressions && (
                      <div className="flex items-center space-x-1">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span className="text-gray-600 dark:text-gray-400">
                          Impressions
                        </span>
                        <span className="font-semibold">
                          {formatNumber(MOCK_PERFORMANCE_DATA.impressions)}
                        </span>
                        <span className="text-gray-500">Total</span>
                      </div>
                    )}
                    {enabledMetrics.ctr && (
                      <div className="flex items-center space-x-1">
                        <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <span className="text-gray-600 dark:text-gray-400">
                          CTR
                        </span>
                        <span className="font-semibold">
                          {MOCK_PERFORMANCE_DATA.ctr.toFixed(2)}%
                        </span>
                        <span className="text-gray-500">Average</span>
                      </div>
                    )}
                    {enabledMetrics.position && (
                      <div className="flex items-center space-x-1">
                        <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                        <span className="text-gray-600 dark:text-gray-400">
                          Position
                        </span>
                        <span className="font-semibold">
                          {MOCK_PERFORMANCE_DATA.position.toFixed(1)}
                        </span>
                        <span className="text-gray-500">Average</span>
                      </div>
                    )}
                  </div>

                  {/* Data Status */}
                  <div className="mt-4 flex items-center justify-center space-x-4">
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      <span className="flex items-center space-x-1">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span>Data loaded automatically</span>
                      </span>
                    </div>
                    <Button
                      onClick={forceRefresh}
                      disabled={isLoading}
                      variant="outline"
                      size="sm"
                      className="text-xs"
                    >
                      <RefreshCw className="h-3 w-3 mr-1" />
                      Refresh
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
