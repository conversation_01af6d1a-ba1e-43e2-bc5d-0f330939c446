"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Eye, EyeOff, Info, AlertCircle } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import Link from "next/link";
import { Divider } from "@mui/material";

interface ConnectionFormData {
  baseUrl: string;
  apiKey: string;
  agreeToTerms: boolean;
}

const API_PLATFORMS = [
  { name: "WordPress", disabled: false },
  { name: "Shopify", disabled: true, comingSoon: true },
  { name: "Contentful", disabled: true, comingSoon: true },
  { name: "Custom API", disabled: false },
];

const PLATFORM_INSTRUCTIONS = {
  WordPress: {
    steps: [
      "Install the SatoLoc WordPress Plugin from our download page",
      "Go to WordPress Admin → Settings → SatoLoc Connector",
      "Generate an API key and copy it",
      "Enter your WordPress site URL and the API key here",
    ],
    apiUrlExample: "https://your-wordpress-site.com/wp-json/satoloc/v1/",
    downloadLink: "/downloads/wordpress-plugin",
  },
  "Custom API": {
    steps: [
      "Review our API specification at docs.satoloc.com/integration",
      "Implement the required endpoints following our REST API specification",
      "Generate an API key with content read/write permissions",
      "Configure CORS to allow requests from SatoLoc Insight",
      "Test your implementation using our validation tool",
    ],
    apiUrlExample: "https://api.your-platform.com/v1/",
    docsLink: "/docs/api-specification",
  },
};

export const ApiComponent = () => {
  const [formData, setFormData] = useState<ConnectionFormData>({
    baseUrl: "",
    apiKey: "",
    agreeToTerms: false,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showApiKey, setShowApiKey] = useState(false);
  const [tosAccepted, setTosAccepted] = useState(false);
  const [platform, setPlatform] = useState<string>(API_PLATFORMS[0].name);
  const [connectionExists, setConnectionExists] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    setConnectionError(null);

    if (!formData.baseUrl) {
      newErrors.baseUrl = "API URL is required";
    } else if (
      !/^https?:\/\/[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+/.test(
        formData.baseUrl
      )
    ) {
      newErrors.baseUrl = "Please enter a valid URL";
    }

    if (!formData.apiKey) {
      newErrors.apiKey = "API Key is required";
    } else if (formData.apiKey.length < 8) {
      newErrors.apiKey = "API Key must be at least 8 characters";
    }

    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = "You must agree to the Terms of Service";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));

    if (connectionExists) {
      setConnectionExists(false);
    }
    if (connectionError) {
      setConnectionError(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      try {
        setIsLoading(true);
        await new Promise((resolve) => setTimeout(resolve, 2000));
        setIsLoading(false);
      } catch (error) {
        setIsLoading(false);
        setConnectionError("Failed to connect. Please try again.");
      }
    }
  };

  const handleConnect = async () => {
    try {
      setIsLoading(true);
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      setConnectionError("Failed to connect to existing site.");
    }
  };

  const isFormValid = !!formData.baseUrl && !!formData.apiKey && tosAccepted;

  return (
    <Card className="grid grid-cols-1 gap-4 shadow-none p-2">
      <div className="grid md:grid-cols-2 gap-4">
        {/* Left panel */}
        <Card className="shadow-none p-0 overflow-hidden">
          <CardHeader className="bg-[#1279b4] rounded-t-lg py-4">
            <CardTitle className="text-xl text-white dark:text-white font-bold flex items-center gap-2">
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-white dark:text-white"
              >
                <path d="M17 6.1H3" />
                <path d="M21 12.1H3" />
                <path d="M15.1 18H3" />
              </svg>
              API Integration
            </CardTitle>
            <CardDescription className="text-white dark:text-white text-[.75rem]">
              Connect your content sources through APIs for automated syncing
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-5">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                <div className="space-y-1.5">
                  <Label
                    htmlFor="baseUrl"
                    className="text-sm font-medium text-black dark:text-foreground"
                  >
                    API URL
                  </Label>
                  <Input
                    id="baseUrl"
                    name="baseUrl"
                    placeholder="https://your-wordpress-site.com"
                    value={formData.baseUrl}
                    onChange={handleChange}
                    disabled={isLoading}
                    className="bg-white/10 border-black/50 text-black h-10 text-sm placeholder:text-gray-400"
                  />
                  <p className="text-black/70 text-xs mt-1 dark:text-foreground">
                    Enter your domain without trailing slash (e.g.,
                    www.example.com). Each domain can only be connected once.
                  </p>
                  {errors.baseUrl && (
                    <p className="text-red-500 text-xs mt-1">
                      {errors.baseUrl}
                    </p>
                  )}
                </div>

                <div className="space-y-1.5">
                  <Label
                    htmlFor="apiKey"
                    className="font-medium text-black dark:text-foreground"
                  >
                    API Key
                  </Label>
                  <div className="relative">
                    <Input
                      id="apiKey"
                      name="apiKey"
                      type={showApiKey ? "text" : ""}
                      placeholder="Enter your API key"
                      disabled={isLoading}
                      className="pr-10 h-9 text-black dark:text-foreground"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => setShowApiKey(!showApiKey)}
                      className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                      title={showApiKey ? "Hide API key" : "Show API key"}
                    >
                      {showApiKey ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  {errors.apiKey && (
                    <p className="text-red-500 text-xs mt-1">{errors.apiKey}</p>
                  )}
                </div>
              </div>
              <Divider className="my-2 bg-foreground/10" />
              <div className=" flex justify-between gap-2">
                <div className="flex items-center space-x-2 w-full sm:w-auto">
                  <Checkbox
                    id="tos"
                    checked={tosAccepted}
                    onCheckedChange={(checked) => {
                      setTosAccepted(checked as boolean);
                      setFormData((prev) => ({
                        ...prev,
                        agreeToTerms: checked as boolean,
                      }));
                    }}
                    className="h-4 w-4"
                  />
                  <label
                    htmlFor="tos"
                    className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-black dark:text-foreground"
                  >
                    I agree to the{" "}
                    <Link
                      href="https://www.satoloc.com/terms-of-service-satoloc-insight/"
                      target="_blank"
                      className="font-medium text-primary hover:underline"
                    >
                      Terms of Service
                    </Link>
                  </label>
                </div>

                <Button
                  type="submit"
                  disabled={isLoading || !isFormValid}
                  className="bg-[#1279b4] hover:bg-[#00497A] text-white font-medium rounded-md px-4 h-10"
                >
                  {connectionExists ? "Reconnect" : "Add Connection"}
                </Button>
              </div>
              {errors.agreeToTerms && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.agreeToTerms}
                </p>
              )}
            </form>
          </CardContent>
        </Card>

        {/* Instructions Card */}
        <Card className="shadow-none p-0 overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-secondary/10 to-secondary/5 rounded-t-lg py-4">
            <CardTitle className="text-xl font-bold flex items-center gap-2 text-[#1279b4] dark:text-foreground">
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-[#1279b4] dark:text-white"
              >
                <circle cx="12" cy="12" r="10" />
                <path d="M12 16v-4" />
                <path d="M12 8h.01" />
              </svg>
              Setup Instructions
            </CardTitle>
            <CardDescription className="text-[#1279b4] dark:text-foreground">
              Follow these steps to connect your {platform} content
            </CardDescription>
          </CardHeader>
          <CardContent>
            {PLATFORM_INSTRUCTIONS[
              platform as keyof typeof PLATFORM_INSTRUCTIONS
            ] && (
              <div className="space-y-4">
                <ol className="list-decimal list-inside space-y-1 ml-1 text-xs">
                  {PLATFORM_INSTRUCTIONS[
                    platform as keyof typeof PLATFORM_INSTRUCTIONS
                  ].steps.map((step, index) => (
                    <li key={index} className="text-muted-foreground">
                      {step}
                    </li>
                  ))}
                </ol>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      <Card className="shadow-none">
        <CardHeader>
          <CardTitle className="text-[#1279b4]">
            SatoLoc Insight Connector v1.0
          </CardTitle>
          <CardDescription>
            SatoLoc Insight Connector is a plugin that allows you to connect
            your site to SatoLoc Insight and sync your content. The plugin is
            currently in beta and is subject to change.
          </CardDescription>
        </CardHeader>

        <CardContent>
          {platform === "WordPress" && (
            <div className="mt-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-lg p-4 shadow-sm">
              <div className="flex flex-col items-center text-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-primary mb-3"
                >
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                  <polyline points="7 10 12 15 17 10" />
                  <line x1="12" y1="15" x2="12" y2="3" />
                </svg>
                <h3 className="text-base font-medium text-[#1279b4] dark:text-white mb-1">
                  Download the Plugin
                </h3>
                <p className="text-xs text-muted-foreground mb-3">
                  Get the latest version of SatoLoc Insight Connector
                </p>
                <Button className="inline-flex items-center justify-center rounded-md bg-[#1279b4] px-4 py-2 text-sm font-medium text-white shadow transition-colors hover:bg-[#00497A] focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-2"
                  >
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                    <polyline points="7 10 12 15 17 10" />
                    <line x1="12" y1="15" x2="12" y2="3" />
                  </svg>
                  Download Now
                </Button>
                <div className="mt-2 text-[10px] text-muted-foreground">
                  Version 1.0 • WordPress 5.8+
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </Card>
  );
};
