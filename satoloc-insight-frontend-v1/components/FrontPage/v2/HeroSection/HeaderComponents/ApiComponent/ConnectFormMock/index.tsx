"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Eye, EyeOff, AlertCircle } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Divider } from "@mui/material";

interface ConnectionFormData {
  baseUrl: string;
  apiKey: string;
  agreeToTerms: boolean;
}

export const ConnectFormMock = () => {
  const [formData, setFormData] = useState<ConnectionFormData>({
    baseUrl: "",
    apiKey: "",
    agreeToTerms: false,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const [tosAccepted, setTosAccepted] = useState(false);
  const [connectionExists, setConnectionExists] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
    // Clear connection exists state when form changes
    if (connectionExists) {
      setConnectionExists(false);
    }
  };

  const isFormValid = !!formData.baseUrl && !!formData.apiKey && tosAccepted;

  return (
    /* Left panel */
    <Card className="shadow-none p-0 overflow-hidden">
      <CardHeader className="bg-[#1279b4] rounded-t-lg py-2">
        <CardTitle className="text-md text-white dark:text-white font-bold flex items-center gap-2">
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-white dark:text-white"
          >
            <path d="M17 6.1H3" />
            <path d="M21 12.1H3" />
            <path d="M15.1 18H3" />
          </svg>
          API Integration
        </CardTitle>
        <CardDescription className="text-white dark:text-white text-[.8rem]">
          Connect your content sources through APIs for automated syncing
        </CardDescription>
      </CardHeader>
      <CardContent>
        {connectionExists && (
          <Alert className="mb-4 bg-amber-50 border-amber-200 text-amber-800">
            <AlertCircle className="h-4 w-4 mr-2" />
            <AlertTitle className="text-sm font-medium">
              Connection already exists
            </AlertTitle>
            <AlertDescription className="text-xs mt-1">
              This site URL has already been registered. You can connect with
              your API key.
            </AlertDescription>
            <div className="mt-3">
              <Button
                disabled={isLoading}
                className="bg-amber-600 hover:bg-amber-700 text-white"
                size="sm"
              >
                Connect to Existing Site
              </Button>
            </div>
          </Alert>
        )}

        <form className="space-y-5">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <div className="space-y-1.5">
              <Label
                htmlFor="baseUrl"
                className="text-[.75rem] font-medium text-black dark:text-foreground"
              >
                API URL
              </Label>
              <Input
                id="baseUrl"
                name="baseUrl"
                placeholder="https://your-wordpress-site.com"
                className="bg-white/10 border-black/50 text-black h-10 text-sm placeholder:text-gray-400"
              />
              <p className="text-black/70 text-[.65rem] mt-1 dark:text-foreground">
                Enter your domain without trailing slash (e.g.,
                www.example.com). Each domain can only be connected once.
              </p>
              {errors.baseUrl && (
                <p className="text-red-500 text-xs mt-1">{errors.baseUrl}</p>
              )}
            </div>

            <div className="space-y-1.5">
              <Label
                htmlFor="apiKey"
                className="text-[.75rem] font-medium text-black dark:text-foreground"
              >
                API Key
              </Label>
              <div className="relative">
                <Input
                  id="apiKey"
                  name="apiKey"
                  type={"text"}
                  placeholder="Enter your API key"
                  className="pr-10 h-9 text-black dark:text-foreground"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                  title={"Show API key"}
                >
                  <Eye className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
          <Divider className="my-2 bg-foreground/10" />
          <div className=" flex justify-between gap-2">
            <div className="flex items-center space-x-2 w-full sm:w-auto">
              <Checkbox
                id="tos"
                checked={tosAccepted}
                onCheckedChange={(checked) => {
                  setTosAccepted(checked as boolean);
                  setFormData((prev) => ({
                    ...prev,
                    agreeToTerms: checked as boolean,
                  }));
                }}
                className="h-4 w-4"
              />
              <label
                htmlFor="tos"
                className="text-[.75rem] leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-black dark:text-foreground"
              >
                I agree to the{" "}
                <span className="font-medium text-primary hover:underline">
                  Terms of Service
                </span>
              </label>
            </div>

            <Button
              type="submit"
              disabled={isLoading || !isFormValid}
              className="bg-[#1279b4] hover:bg-[#00497A] text-white font-medium rounded-md px-4 h-10"
            >
              {connectionExists ? "Reconnect" : "Add Connection"}
            </Button>
          </div>
          {errors.agreeToTerms && (
            <p className="text-red-500 text-xs mt-1">{errors.agreeToTerms}</p>
          )}
        </form>
      </CardContent>
    </Card>
  );
};
