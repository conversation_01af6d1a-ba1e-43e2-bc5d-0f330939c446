"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { ApiComponent } from "./ApiComponent";
import { CoreVitals } from "./CoreVitals";
import { GSCPerformances } from "./GSCPerformances";
import { CustomComponent } from "./CustomComponent";
import { ErrorComponent } from "./ErrorComponent";

interface CarouselItem {
  id: string;
  title: string;
  component: React.ComponentType;
}

const carouselItems: CarouselItem[] = [
  {
    id: "api",
    title: "API Integration",
    component: ApiComponent,
  },
  {
    id: "gsc",
    title: "GSC Performance",
    component: GSCPerformances,
  },
  {
    id: "error",
    title: "Error Analysis",
    component: ErrorComponent,
  },
  // Future components can be added here
  // {
  //   id: "vitals",
  //   title: "Core Vitals",
  //   component: CoreVitals,
  // },
];

export const HeaderComponents = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlay, setIsAutoPlay] = useState(true);

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlay) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % carouselItems.length);
    }, 8000); // Change slide every 8 seconds

    return () => clearInterval(interval);
  }, [isAutoPlay]);

  const goToNext = () => {
    setCurrentIndex((prev) => (prev + 1) % carouselItems.length);
    setIsAutoPlay(false); // Disable auto-play when user interacts
  };

  const goToPrevious = () => {
    setCurrentIndex(
      (prev) => (prev - 1 + carouselItems.length) % carouselItems.length
    );
    setIsAutoPlay(false); // Disable auto-play when user interacts
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
    setIsAutoPlay(false); // Disable auto-play when user interacts
  };

  const getSlidePosition = (index: number) => {
    const diff = index - currentIndex;
    if (diff === 0) return "center";
    if (diff === 1 || diff === -(carouselItems.length - 1)) return "right";
    if (diff === -1 || diff === carouselItems.length - 1) return "left";
    return "hidden";
  };

  const getSlideVariants = (position: string) => {
    switch (position) {
      case "center":
        return {
          x: 0,
          scale: 0.85, // Reduced from 1 to make room for side components
          zIndex: 3,
          filter: "blur(0px)",
          opacity: 1,
        };
      case "left":
        return {
          x: "-45%", // Reduced from -60% to make more visible
          scale: 0.65, // Reduced from 0.8 to fit better
          zIndex: 1,
          filter: "blur(1px)", // Reduced blur to see better
          opacity: 0.8, // Increased opacity to see better
        };
      case "right":
        return {
          x: "45%", // Reduced from 60% to make more visible
          scale: 0.65, // Reduced from 0.8 to fit better
          zIndex: 1,
          filter: "blur(1px)", // Reduced blur to see better
          opacity: 0.8, // Increased opacity to see better
        };
      default:
        return {
          x: 0,
          scale: 0.5,
          zIndex: 0,
          filter: "blur(3px)",
          opacity: 0,
        };
    }
  };

  return (
    <div className="relative w-full h-full mt-16 p-4">
      {/* Carousel Container */}
      <div className="relative h-[600px] overflow-hidden rounded-xl">
        {/* Slides */}
        <div className="relative w-full h-full flex items-center justify-center">
          {carouselItems.map((item, index) => {
            const position = getSlidePosition(index);
            const Component = item.component;

            return (
              <motion.div
                key={item.id}
                className="absolute w-full max-w-4xl"
                initial={getSlideVariants("hidden")}
                animate={getSlideVariants(position)}
                transition={{
                  duration: 0.6,
                  ease: "easeInOut",
                }}
                style={{
                  pointerEvents: position === "center" ? "auto" : "none",
                }}
              >
                <div className="w-full">
                  <Component />
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Navigation Arrows */}
        <Button
          variant="outline"
          size="icon"
          className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-background hover:bg-background border-gray-200 shadow-lg"
          onClick={goToPrevious}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        <Button
          variant="outline"
          size="icon"
          className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-background hover:bg-background border-gray-200 shadow-lg"
          onClick={goToNext}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Dots Indicator */}
      <div className="flex justify-center space-x-2 mt-6">
        {carouselItems.map((_, index) => (
          <button
            key={index}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentIndex
                ? "bg-[#1279b4] scale-110"
                : "bg-gray-300 hover:bg-gray-400"
            }`}
            onClick={() => goToSlide(index)}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>

      {/* Slide Title */}
      <div className="text-center mt-4">
        <motion.h3
          key={currentIndex}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="text-lg font-semibold text-gray-800 dark:text-gray-200"
        >
          {carouselItems[currentIndex]?.title}
        </motion.h3>
      </div>

      {/* Auto-play indicator */}
      {isAutoPlay && (
        <div className="absolute top-4 right-4 z-10">
          <div className="flex items-center space-x-2 bg-black/20 rounded-full px-3 py-1 text-xs text-white">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            <span>Auto</span>
          </div>
        </div>
      )}
    </div>
  );
};
