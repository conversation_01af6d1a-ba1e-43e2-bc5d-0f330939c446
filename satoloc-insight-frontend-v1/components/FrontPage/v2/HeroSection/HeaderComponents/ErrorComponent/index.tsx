"use client";

import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
} from "@/components/ui/card";

import { <PERSON>, Doughnut } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  ChartData,
  ChartOptions,
} from "chart.js";
import { useTheme } from "@mui/material";
import ChartDataLabels from "chartjs-plugin-datalabels";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  ChartDataLabels
);

// Mock data for demonstration - matching the original structure
const MOCK_ERROR_DATA = [
  {
    category: "Terminology",
    Critical: 0,
    Major: 1,
    Minor: 1,
    Neutral: 0,
  },
  {
    category: "Accuracy",
    Critical: 1,
    Major: 2,
    Minor: 0,
    Neutral: 0,
  },
  {
    category: "Linguistic",
    Critical: 0,
    Major: 1,
    Minor: 2,
    Neutral: 0,
  },
  {
    category: "Style",
    Critical: 0,
    Major: 1,
    Minor: 2,
    Neutral: 0,
  },
  {
    category: "Locale",
    Critical: 0,
    Major: 1,
    Minor: 0,
    Neutral: 0,
  },
  {
    category: "Cultural",
    Critical: 0,
    Major: 0,
    Minor: 1,
    Neutral: 0,
  },
  {
    category: "Design",
    Critical: 0,
    Major: 0,
    Minor: 1,
    Neutral: 0,
  },
];

export const ErrorComponent = () => {
  const theme = useTheme();

  const transformToChartData = () => ({
    labels: MOCK_ERROR_DATA.map((item) => item.category),
    datasets: [
      {
        label: "Critical",
        data: MOCK_ERROR_DATA.map((item) => item.Critical),
        backgroundColor: theme.palette.error.main,
        barThickness: 35,
      },
      {
        label: "Major",
        data: MOCK_ERROR_DATA.map((item) => item.Major),
        backgroundColor: theme.palette.warning.main,
        barThickness: 35,
      },
      {
        label: "Minor",
        data: MOCK_ERROR_DATA.map((item) => item.Minor),
        backgroundColor: theme.palette.info.main,
        barThickness: 35,
      },
      {
        label: "Neutral",
        data: MOCK_ERROR_DATA.map((item) => item.Neutral),
        backgroundColor: theme.palette.success.main,
        barThickness: 35,
      },
    ],
  });

  const transformToDoughnutData = () => {
    const totals = MOCK_ERROR_DATA.reduce(
      (acc, item) => ({
        Critical: acc.Critical + item.Critical,
        Major: acc.Major + item.Major,
        Minor: acc.Minor + item.Minor,
        Neutral: acc.Neutral + item.Neutral,
      }),
      { Critical: 0, Major: 0, Minor: 0, Neutral: 0 }
    );

    return {
      labels: ["Critical", "Major", "Minor", "Neutral"],
      datasets: [
        {
          data: [totals.Critical, totals.Major, totals.Minor, totals.Neutral],
          backgroundColor: [
            theme.palette.error.main,
            theme.palette.warning.main,
            theme.palette.info.main,
            theme.palette.success.main,
          ],
        },
      ],
    };
  };

  const chartOptions: ChartOptions<"bar"> = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        stacked: true,
        grid: {
          display: false,
        },
        border: {
          display: false,
        },
        ticks: {
          font: {
            family: "Inter",
          },
        },
      },
      y: {
        stacked: true,
        border: {
          dash: [4, 4] as number[],
        },
        grid: {
          color: theme.palette.divider,
          display: true,
        },
        ticks: {
          font: {
            family: "Inter",
          },
        },
      },
    },
    plugins: {
      legend: {
        position: "top",
        labels: {
          padding: 20,
          font: {
            family: "Inter",
            weight: 500,
          },
          usePointStyle: true,
          pointStyle: "circle",
        },
      },
      datalabels: {
        color: theme.palette.text.primary,
        font: (context: any) => ({
          family: "Inter",
          weight: 600,
          size: 11,
        }),
        padding: 6,
        formatter: (value: number) => (value > 0 ? value : ""),
      },
    },
  };

  const doughnutOptions: ChartOptions<"doughnut"> = {
    cutout: "75%",
    plugins: {
      legend: {
        position: "bottom",
        labels: {
          padding: 20,
          font: {
            family: "Inter",
            weight: 500,
          },
          usePointStyle: true,
          pointStyle: "circle",
        },
      },
      datalabels: {
        color: theme.palette.background.paper,
        font: (context: any) => ({
          family: "Inter",
          weight: 600,
          size: 13,
        }),
        formatter: (value: number) => (value > 0 ? value : ""),
      },
    },
    maintainAspectRatio: false,
  };

  return (
    <div className="space-y-6 p-2 bg-background w-full">
      {/* Static Fake Tabs (Non-functional) */}
      <div className="w-full">
        <div className="inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground grid grid-cols-4 w-full">
          <div className="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 cursor-not-allowed">
            LQA Score
          </div>
          <div className="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-background text-foreground shadow-sm cursor-not-allowed">
            Error Category
          </div>
          <div className="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 cursor-not-allowed">
            Content Report
          </div>
          <div className="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 cursor-not-allowed">
            One Pager
          </div>
        </div>
        <div className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-[2fr_1fr] gap-6">
            {/* Left Panel - Bar Chart */}
            <Card className="group hover:shadow-lg transition-all duration-300 shadow-none rounded-md">
              <CardHeader>
                <CardTitle className="text-xl font-semibold flex items-center justify-between">
                  Error Categories and Severity
                </CardTitle>
                <CardDescription className="text-sm text-muted-foreground">
                  Distribution of errors across categories and severity levels
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[400px] w-full p-4">
                  <Bar data={transformToChartData()} options={chartOptions} />
                </div>
              </CardContent>
            </Card>

            {/* Right Panel - Doughnut Chart */}
            <Card className="group hover:shadow-lg transition-all duration-300 shadow-none rounded-md">
              <CardHeader>
                <CardTitle className="text-xl font-semibold">
                  Overall Distribution
                </CardTitle>
                <CardDescription className="text-muted-foreground/80">
                  Breakdown of errors by severity level
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[400px] w-full p-4 flex items-center justify-center">
                  <Doughnut
                    data={transformToDoughnutData()}
                    options={doughnutOptions}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};
