"use client";

import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, Play } from "lucide-react";
import { useState } from "react";
import HeaderImage from "@/public/images/front-images/HeaderS.png";
import Image from "next/image";
import { HeaderComponents } from "./HeaderComponents";

interface DynamicHeroSectionProps {
  onJoinBetaClick?: () => void;
}

export function HeroSection({ onJoinBetaClick }: DynamicHeroSectionProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handlePrimaryButtonClick = () => {
    if (onJoinBetaClick) {
      onJoinBetaClick();
    } else {
      setIsModalOpen(true);
    }
  };

  const handleSecondaryButtonClick = () => {
    window.open("https://calendly.com/satoloc/30min", "_blank");
  };

  return (
    <div className="min-h-screen flex items-center pt-32">
      <div className="container mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-1 gap-10 items-center justify-center max-w-4xl mx-auto">
          {/* Left Column - Content + Components */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            {/* Main Content */}
            <div className="space-y-8 flex flex-col items-center justify-center">
              {/* Beta Badge */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.6 }}
                className="inline-flex items-center px-4 py-2 rounded-full bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 text-sm font-medium"
              >
                ⚡ Now in Beta
              </motion.div>

              {/* Main Heading */}
              <motion.h1
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.8 }}
                className="text-4xl lg:text-5xl xl:text-5xl font-bold leading-tight text-center"
              >
                <span className="text-foreground">Smarter Localization.</span>
                <br />
                <span className="text-foreground">Better SEO. Faster</span>
                <br />
                <span className="text-foreground">Global Growth.</span>
              </motion.h1>

              {/* Description */}
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5, duration: 0.8 }}
                className="text-base font-thin lg:text-lg text-foreground max-w-2xl leading-relaxed text-center"
              >
                The AI-powered platform that helps you translate at scale, run
                quality assurance, analyze multilingual SEO, get ahead of your
                competitors, and generate content tailored to your audience.
              </motion.p>

              {/* Action Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7, duration: 0.8 }}
                className="flex flex-col sm:flex-row gap-4"
              >
                <Button
                  size="lg"
                  className="group px-6 py-6 bg-[#1279b4] hover:bg-[#1279b4]/80 text-white text-md font-thin rounded-lg transition-all duration-200"
                  onClick={handlePrimaryButtonClick}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundPosition = "-100% 0";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundPosition = "0 0";
                  }}
                >
                  Join the Beta
                </Button>

                <Button
                  size="lg"
                  variant="ghost"
                  className="group px-6 py-6 text-md font-thin rounded-md hover:bg-muted transition-all duration-200 border"
                  onClick={handleSecondaryButtonClick}
                >
                  <Play className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform " />
                  Book a Demo
                </Button>
              </motion.div>
            </div>
          </motion.div>

          {/* Right Column - Header Components */}
          {/* <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4, duration: 0.8 }}
            className="w-full px-8 py-10"
          >
            <HeaderComponents />
          </motion.div> */}
        </div>
      </div>
    </div>
  );
}
