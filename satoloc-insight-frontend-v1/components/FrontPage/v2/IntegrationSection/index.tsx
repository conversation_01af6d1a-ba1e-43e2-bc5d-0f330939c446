"use client";

import { motion } from "framer-motion";
import { Cloud, Code, Settings } from "lucide-react";
import { ConnectFormMock } from "../HeroSection/HeaderComponents/ApiComponent/ConnectFormMock";

export function IntegrationSection() {
  const integrations = [
    {
      name: "WordPress",
      icon: "W",
      bgColor: "bg-blue-100 dark:bg-blue-900/20",
      textColor: "text-blue-600",
    },
    {
      name: "Custom CMS",
      icon: "☁",
      bgColor: "bg-gray-100 dark:bg-gray-800",
      textColor: "text-gray-600",
    },
    {
      name: "API",
      icon: "⚡",
      bgColor: "bg-purple-100 dark:bg-purple-900/20",
      textColor: "text-purple-600",
    },
    {
      name: "Coming Soon",
      icon: "⚙",
      bgColor: "bg-gray-100 dark:bg-gray-800",
      textColor: "text-gray-400",
      isComingSoon: true,
    },
  ];

  return (
    <section className="py-24 bg-background">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
            Seamless Integrations
          </h2>
          <p className="text-lg text-muted-foreground font-thin">
            Works where your content lives
          </p>
        </motion.div>

        <motion.div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left Column - Placeholder */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="hidden lg:block"
          >
            <div className="w-full h-96 flex items-center justify-center p-6">
              <ConnectFormMock />
            </div>
          </motion.div>

          {/* Right Column - Integration Content */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2, duration: 0.8 }}
            className="space-y-8"
          >
            {/* Connect Section */}
            <div>
              <h3 className="text-2xl font-semibold text-foreground mb-4">
                Connect with your favorite platforms
              </h3>
              <p className="text-muted-foreground mb-8 font-thin">
                SatoLOC Insight integrates with the tools you already use,
                making it easy to optimize your content workflow without
                changing your existing processes.
              </p>

              {/* Integration Icons */}
              <div className="grid grid-cols-2 gap-4 mb-8">
                {integrations.map((integration, index) => (
                  <motion.div
                    key={integration.name}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: 0.4 + index * 0.1, duration: 0.5 }}
                    className="flex flex-col items-center p-6 rounded-lg border border-border hover:border-primary/20 transition-colors"
                  >
                    <div
                      className={`w-12 h-12 rounded-lg flex items-center justify-center mb-3`}
                    >
                      {integration.name === "WordPress" ? (
                        <img
                          src="/images/wordpress-tile.svg"
                          alt="WordPress"
                          className="w-8 h-8"
                        />
                      ) : (
                        <span
                          className={`text-xl font-bold ${integration.textColor}`}
                        >
                          {integration.icon}
                        </span>
                      )}
                    </div>
                    <span
                      className={`text-sm font-medium ${
                        integration.isComingSoon
                          ? "text-muted-foreground"
                          : "text-foreground"
                      }`}
                    >
                      {integration.name}
                    </span>
                    {integration.isComingSoon && (
                      <span className="text-xs text-muted-foreground/60 mt-1">
                        Coming Soon
                      </span>
                    )}
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Developer Friendly Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.6, duration: 0.6 }}
            >
              <h4 className="text-xl font-semibold text-foreground mb-3">
                Developer friendly
              </h4>
              <p className="text-muted-foreground font-thin">
                Our API allows for custom integrations with any platform.
                Documentation and support available.
              </p>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
