"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Mouse, Eye, TrendingUp } from "lucide-react";

interface TableData {
  clicks: number;
  impressions: number;
  ctr: number;
}

export function Focus() {
  // Mock data based on the image
  const tableData: TableData[] = [
    { clicks: 31, impressions: 148, ctr: 20.95 },
    { clicks: 17, impressions: 345, ctr: 4.93 },
    { clicks: 9, impressions: 1100, ctr: 0.85 }, // Using 1.1K as 1100
    { clicks: 6, impressions: 910, ctr: 0.66 },
    { clicks: 6, impressions: 467, ctr: 1.28 },
    { clicks: 3, impressions: 87, ctr: 3.45 },
  ];

  const formatNumber = (num: number) => {
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const formatCtr = (ctr: number) => `${ctr.toFixed(2)}%`;

  return (
    <div className="w-full h-full bg-background shadow-none rounded-sm">
      <div className="p-3">
        {/* Table */}
        <div className="rounded-lg border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-center font-semibold text-xs">
                  <div className="flex items-center justify-center space-x-1">
                    <Mouse className="h-3 w-3 text-blue-600" />
                    <span>Clicks</span>
                  </div>
                </TableHead>
                <TableHead className="text-center font-semibold text-xs">
                  <div className="flex items-center justify-center space-x-1">
                    <Eye className="h-3 w-3 text-green-600" />
                    <span>Impressions</span>
                  </div>
                </TableHead>
                <TableHead className="text-center font-semibold text-xs">
                  <div className="flex items-center justify-center space-x-1">
                    <TrendingUp className="h-3 w-3 text-orange-600" />
                    <span>CTR</span>
                  </div>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {tableData.map((row, index) => (
                <TableRow key={index} className="hover:bg-accent/50">
                  <TableCell className="text-center text-blue-600 font-semibold text-xs py-2">
                    {row.clicks}
                  </TableCell>
                  <TableCell className="text-center text-green-600 font-semibold text-xs py-2">
                    {formatNumber(row.impressions)}
                  </TableCell>
                  <TableCell className="text-center text-orange-600 font-semibold text-xs py-2">
                    {formatCtr(row.ctr)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
