"use client";

import React from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface ActionData {
  impact: "High" | "Medium" | "Low";
  status: "To Do" | "In Prog" | "Done";
  assignee: string;
}

export function TakeAction() {
  // Mock data based on the image
  const actionData: ActionData[] = [
    { impact: "High", status: "To Do", assignee: "Unassigned" },
    { impact: "High", status: "In Prog", assignee: "Ahmad Y" },
    { impact: "Medium", status: "To Do", assignee: "Mehmet S" },
    { impact: "Medium", status: "To Do", assignee: "Mehmet S" },
    { impact: "Medium", status: "To Do", assignee: "Mehmet S" },
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "To Do":
        return (
          <Badge
            variant="outline"
            className="text-[7px] px-1 py-0 text-foreground border-border shadow-none"
          >
            To Do
          </Badge>
        );
      case "In Prog":
        return (
          <Badge
            variant="outline"
            className="text-[7px] px-1 py-0.5 text-blue-600 border-blue-300 bg-blue-50 shadow-none"
          >
            In Prog
          </Badge>
        );
      case "Done":
        return (
          <Badge
            variant="outline"
            className="text-[7px] px-1 py-0.5 text-green-600 border-green-300 bg-green-50 shadow-none"
          >
            Done
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="text-[7px] px-1 py-0 shadow-none">
            {status}
          </Badge>
        );
    }
  };

  const getImpactBadge = (impact: string) => {
    switch (impact) {
      case "High":
        return (
          <Badge
            variant="destructive"
            className="text-[6px] px-2 py-0 bg-red-100 text-red-700 border-red-200 shadow-none"
          >
            High
          </Badge>
        );
      case "Medium":
        return (
          <Badge
            variant="outline"
            className="text-[6px] px-2 py-0 text-orange-600 border-orange-300 bg-orange-50 shadow-none"
          >
            Medium
          </Badge>
        );
      case "Low":
        return (
          <Badge
            variant="outline"
            className="text-[6px] px-2 py-0 text-gray-600 border-gray-300 bg-gray-50 shadow-none"
          >
            Low
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="text-[6px] px-2 py-0 shadow-none">
            {impact}
          </Badge>
        );
    }
  };

  return (
    <div className="w-full h-full bg-background shadow-none rounded-sm">
      <div className="p-0">
        {/* Table */}
        <div className="">
          <Table>
            <TableHeader className="bg-background">
              <TableRow className="bg-background border-b border-border">
                <TableHead className="text-center font-semibold text-[8px] px-1 py-1">
                  Impact
                </TableHead>
                <TableHead className="text-center font-semibold text-[8px] px-1 py-1">
                  Status
                </TableHead>
                <TableHead className="text-center font-semibold text-[8px] px-1 py-1">
                  Assignee
                </TableHead>
                <TableHead className="text-center font-semibold text-[8px] px-1 py-1">
                  AI Content
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {actionData.map((row, index) => (
                <TableRow key={index} className="border-b border-border">
                  <TableCell className="text-center py-2 px-1">
                    {getImpactBadge(row.impact)}
                  </TableCell>
                  <TableCell className="text-center py-1 px-1">
                    {getStatusBadge(row.status)}
                  </TableCell>
                  <TableCell className="text-center text-foreground text-[7px] py-1 px-1">
                    {row.assignee}
                  </TableCell>
                  <TableCell className="text-center py-1 px-1">
                    <Button className="h-4 px-1 text-[6px] bg-[#1279b4] hover:bg-[#0f6a9a] text-white rounded-sm shadow-none">
                      Generate Content
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
