"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Footer } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Loader2, AlertCircle } from "lucide-react";

export function InputCard() {
  const [formData, setFormData] = useState({
    url: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [urlError, setUrlError] = useState<string | null>(null);

  const validateUrlFormat = (url: string): string | null => {
    if (!url || url.trim() === "") return null;

    // Remove protocol if present
    const cleanUrl = url.replace(/^https?:\/\//, "").replace(/^www\./, "");

    // Basic domain validation
    const domainPattern =
      /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

    if (!domainPattern.test(cleanUrl)) {
      return "Please enter a valid domain (e.g., example.com)";
    }

    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.url || formData.url.trim() === "") {
      return;
    }

    const urlValidationError = validateUrlFormat(formData.url);
    if (urlValidationError) {
      setUrlError(urlValidationError);
      return;
    }

    setIsSubmitting(true);

    // Simulate analysis
    setTimeout(() => {
      setIsSubmitting(false);
      // In real implementation, this would trigger the actual analysis
    }, 2000);
  };

  return (
    <div className="w-full h-full bg-background shadow-none rounded-sm">
      <CardContent className="p-3">
        <form onSubmit={handleSubmit}>
          <div className="space-y-3">
            {/* URL Input and Submit Button in same row */}
            <div className="flex gap-2 items-start">
              <div className="flex-1 relative">
                <Input
                  className={`${
                    urlError ? "border-red-500 focus-visible:ring-red-500" : ""
                  } w-full h-8 text-xs`}
                  placeholder="example.com"
                  value={formData.url}
                  onChange={(e) => {
                    const newUrl = e.target.value;
                    setFormData((prev) => ({ ...prev, url: newUrl }));
                    setUrlError(validateUrlFormat(newUrl));
                  }}
                  required
                  disabled={isSubmitting}
                />
                {urlError && (
                  <div className="flex items-start gap-1 text-xs text-red-500 absolute top-full left-0 mt-1">
                    <AlertCircle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                    <span className="text-xs">{urlError}</span>
                  </div>
                )}
              </div>

              <Button
                type="submit"
                className="shrink-0 h-8 px-3 text-xs bg-[#1279b4] hover:bg-[#0f6a9a] text-white"
                disabled={isSubmitting || !formData.url || urlError !== null}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                    Analyzing...
                  </>
                ) : (
                  "Start Analysis"
                )}
              </Button>
            </div>
          </div>
        </form>
      </CardContent>

      <CardFooter className="p-2 pt-0">
        <div className="text-xs text-muted-foreground space-y-1 text-left">
          <p className="text-xs">
            <strong className="text-foreground">
              SEO analysis by <em>Satoloc Insight</em>
            </strong>
          </p>
          <p className="text-xs">
            This feature is currently in{" "}
            <span className="bg-blue-100 text-blue-800 px-1 py-0.5 rounded text-xs font-medium">
              BETA
            </span>{" "}
            as we continuously enhance its capabilities. Our advanced crawler
            analyzes your website's technical SEO, content structure, and
            performance metrics to provide actionable insights.
          </p>
          <p className="text-xs text-blue-600">
            <em>
              Coming soon: AI-powered analysis with our custom LLM for even more
              precise, industry-specific recommendations.
            </em>
          </p>
        </div>
      </CardFooter>
    </div>
  );
}
