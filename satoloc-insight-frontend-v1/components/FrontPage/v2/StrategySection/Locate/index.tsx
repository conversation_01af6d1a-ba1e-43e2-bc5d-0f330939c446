"use client";

import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Languages, LineChart, ArrowRightLeft, SquarePen } from "lucide-react";

interface NavItem {
  title: string;
  icon: React.ReactNode;
  href: string;
  isActive?: boolean;
}

export function Locate() {
  const [activeItem, setActiveItem] = useState<string>("Custom Content");

  const navItems: NavItem[] = [
    {
      title: "Localization",
      icon: <Languages className="h-3 w-3" />,
      href: "/dashboard",
      isActive: activeItem === "Localization",
    },
    {
      title: "SEO Analytics",
      icon: <LineChart className="h-3 w-3" />,
      href: "/seo-insights",
      isActive: activeItem === "SEO Analytics",
    },
    {
      title: "Api Integration",
      icon: <ArrowRightLeft className="h-3 w-3" />,
      href: "/content",
      isActive: activeItem === "Api Integration",
    },
    {
      title: "Custom Content",
      icon: <SquarePen className="h-3 w-3" />,
      href: "/custom-content",
      isActive: activeItem === "Custom Content",
    },
  ];

  const handleItemClick = (title: string) => {
    setActiveItem(title);
  };

  return (
    <div className="w-full h-full bg-background shadow-none rounded-sm">
      {/* Header */}
      <div className="px-3 py-3 border-b border-border">
        <h3 className="text-xs font-medium text-muted-foreground text-center">
          Navigation
        </h3>
      </div>

      {/* Navigation Items */}
      <div className="p-3">
        <nav className="space-y-1">
          {navItems.map((item) => (
            <button
              key={item.title}
              onClick={() => handleItemClick(item.title)}
              className={`
                w-full flex items-center gap-2 px-3 py-2 text-xs rounded transition-all duration-200
                ${
                  item.isActive
                    ? "bg-accent text-accent-foreground font-medium"
                    : "text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground"
                }
              `}
            >
              <span
                className={
                  item.isActive
                    ? "text-accent-foreground"
                    : "text-muted-foreground"
                }
              >
                {item.icon}
              </span>
              <span className="text-left text-xs">{item.title}</span>
            </button>
          ))}
        </nav>
      </div>
    </div>
  );
}
