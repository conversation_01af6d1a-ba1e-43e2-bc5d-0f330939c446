import { Card } from "@/components/ui/card";
import { Locate } from "./Locate";
import { InputCard } from "./InputCard";
import { Focus } from "./Focus";
import { TakeAction } from "./TakeAction";

export function StrategySection() {
  const steps = [
    {
      number: 1,
      title: "Locate",
      description: "Choose your focus area: Localization, SEO, or Content.",
      component: <Locate />,
    },
    {
      number: 2,
      title: "Input",
      description: "Enter your URL and select industry, language, and service.",
      component: <InputCard />,
    },
    {
      number: 3,
      title: "Focus",
      description: "Review insights from our comprehensive analysis.",
      component: <Focus />,
    },
    {
      number: 4,
      title: "Take Action",
      description:
        "Generate content, download reports, or fix identified issues.",
      component: <TakeAction />,
    },
  ];

  return (
    <section className="py-16 sm:py-24 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl sm:text-4xl font-extrabold text-foreground tracking-tight">
            How It Works: The LIFT Method
          </h2>
          <p className="mt-2 max-w-2xl mx-auto text-lg sm:text-xl text-muted-foreground font-thin">
            Our four-step process to transform your multilingual content
            strategy
          </p>
        </div>
        <div className="mt-20">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10">
            {steps.map((step, index) => (
              <div key={index} className="text-center">
                <Card className="relative h-60 w-full rounded-lg overflow-hidden mb-6 shadow-none bg-muted hover:border-primary/20 transition-colors">
                  {step.component}
                </Card>
                <div className="text-left">
                  <div className=" bg-primary/10 rounded-full w-10 h-10 flex items-center justify-center">
                    <span className="text-xl font-bold text-muted-foreground">
                      {step.number}
                    </span>
                  </div>
                  <h3 className="mt-1 text-xl font-bold text-foreground">
                    {step.title}
                  </h3>
                  <p className="mt-1 text-base text-muted-foreground font-thin">
                    {step.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
