"use client";

import { motion } from "framer-motion";
import { TrendingUp, Search, Target, Zap } from "lucide-react";
import { GSCPerformances } from "../HeroSection/HeaderComponents/GSCPerformances";

export function SeoPerformance() {
  const features = [
    {
      icon: TrendingUp,
      text: "Track performance across all languages",
      color: "text-teal-600",
    },
    {
      icon: Search,
      text: "Identify top-performing queries by market",
      color: "text-teal-600",
    },
    {
      icon: Target,
      text: "Compare CTR and position improvements over time",
      color: "text-teal-600",
    },
    {
      icon: Zap,
      text: "Get AI-powered recommendations for optimization",
      color: "text-teal-600",
    },
  ];

  return (
    <section className="py-24 bg-muted/40">
      <div className="container mx-auto px-0">
        <div className="grid grid-cols-1 lg:grid-cols-[1fr_1.5fr] gap-6 items-center">
          {/* Left Column - Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            {/* Section Title */}
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="text-3xl lg:text-4xl font-bold text-foreground leading-tight"
            >
              Live SEO Performance
            </motion.h2>

            {/* Section Description */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="text-md text-muted-foreground font-thin max-w-lg"
            >
              We don't just translate your content. We optimize it for global
              search performance. See how our platform helps you track and
              improve your multilingual SEO metrics.
            </motion.p>

            {/* Features List */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.4, duration: 0.6 }}
              className="space-y-4"
            >
              {features.map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: 0.5 + index * 0.1, duration: 0.5 }}
                    className="flex items-center gap-4 group"
                  >
                    <div className="flex-shrink-0">
                      <IconComponent
                        className={`h-5 w-5 ${feature.color} group-hover:scale-110 transition-transform duration-200`}
                      />
                    </div>
                    <span className="text-foreground font-medium">
                      {feature.text}
                    </span>
                  </motion.div>
                );
              })}
            </motion.div>
          </motion.div>

          {/* Right Column - Placeholder */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.4, duration: 0.8 }}
            className="hidden lg:block"
          >
            {/* Placeholder for SEO Dashboard */}
            <div className="text-center">
              <GSCPerformances />
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
