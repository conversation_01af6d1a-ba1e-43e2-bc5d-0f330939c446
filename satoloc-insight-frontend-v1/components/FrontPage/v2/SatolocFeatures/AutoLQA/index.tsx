import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Text, User } from "lucide-react";

export const AutoLQA = () => {
  // Mock data based on the image
  const mockData = {
    sourceWords: 462,
    lqaScore: 88,
    createdDate: "May 22, 2023 14:19",
    severityData: {
      critical: 2,
      major: 4,
      minor: 8,
      neutral: 6
    },
    categoryData: {
      terminology: 2,
      accuracy: 4,
      linguistic: 2,
      style: 3,
      locale: 1,
      cultural: 1,
      design: 1
    }
  };

  const totalIssues = Object.values(mockData.severityData).reduce((a, b) => a + b, 0);
  const targetWords = 10000; // 10k words target

  return (
    <div className="w-full h-full bg-background">
      {/* Header Tabs */}
      <div className="mb-2">
        <Tabs value="lqa-score" className="w-full">
          <TabsList className="grid w-full grid-cols-4 h-7 text-[10px] pointer-events-none">
            <TabsTrigger value="lqa-score" className="px-1 text-[9px]">
              LQA Score
            </TabsTrigger>
            <TabsTrigger value="error-category" className="px-1 text-[9px]">
              Error Category
            </TabsTrigger>
            <TabsTrigger value="content-report" className="px-1 text-[9px]">
              Content Report
            </TabsTrigger>
            <TabsTrigger value="one-pager" className="px-1 text-[9px]">
              One Pager
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Main Content */}
      <Card className="shadow-none rounded-sm border">
        <CardHeader className="pb-2 px-3 pt-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-semibold text-foreground">
              Quality Metrics Overview
            </CardTitle>
            <span className="text-[8px] text-muted-foreground">
              Created {mockData.createdDate}
            </span>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-0">
          {/* Top Metrics */}
          <div className="grid grid-cols-2 gap-3 mb-3">
            {/* Source Words */}
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <span className="text-[9px] text-muted-foreground">Source Words</span>
                <span className="text-sm font-bold text-foreground">{mockData.sourceWords}</span>
              </div>
              <div className="space-y-1">
                <span className="text-[8px] text-muted-foreground">Total analyzed words</span>
                <Progress 
                  value={(mockData.sourceWords / targetWords) * 100} 
                  className="h-1.5" 
                />
                <div className="flex justify-between text-[7px] text-muted-foreground">
                  <span>0% of 10k words</span>
                </div>
              </div>
            </div>

            {/* LQA Score */}
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <span className="text-[9px] text-muted-foreground">LQA Score</span>
                <span className="text-sm font-bold text-foreground">{mockData.lqaScore}%</span>
              </div>
              <div className="space-y-1">
                <span className="text-[8px] text-muted-foreground">Overall quality score</span>
                <Progress 
                  value={mockData.lqaScore} 
                  className="h-1.5" 
                />
                <div className="flex justify-between text-[7px] text-muted-foreground">
                  <span></span>
                  <span>Target: 100%</span>
                </div>
              </div>
            </div>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-2 gap-3">
            {/* Issues by Severity */}
            <div className="space-y-2">
              <div>
                <h4 className="text-[10px] font-medium text-foreground mb-1">Issues by Severity</h4>
                <p className="text-[8px] text-muted-foreground">Distribution of issues across severity levels</p>
              </div>
              
              {/* Mock Donut Chart */}
              <div className="relative h-[80px] flex items-center justify-center">
                <div className="relative w-16 h-16">
                  {/* Outer ring segments */}
                  <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 64 64">
                    {/* Critical (red) */}
                    <circle cx="32" cy="32" r="24" fill="none" stroke="#ef4444" strokeWidth="8" 
                            strokeDasharray="9.42 75.36" strokeDashoffset="0" />
                    {/* Major (orange) */}
                    <circle cx="32" cy="32" r="24" fill="none" stroke="#f97316" strokeWidth="8" 
                            strokeDasharray="18.84 65.94" strokeDashoffset="-9.42" />
                    {/* Minor (yellow) */}
                    <circle cx="32" cy="32" r="24" fill="none" stroke="#eab308" strokeWidth="8" 
                            strokeDasharray="37.68 47.1" strokeDashoffset="-28.26" />
                    {/* Neutral (teal) */}
                    <circle cx="32" cy="32" r="24" fill="none" stroke="#14b8a6" strokeWidth="8" 
                            strokeDasharray="28.26 56.52" strokeDashoffset="-65.94" />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-[8px] font-bold text-foreground">{totalIssues}</span>
                  </div>
                </div>
              </div>
              
              {/* Legend */}
              <div className="grid grid-cols-2 gap-1 text-[7px]">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 rounded-full bg-red-500"></div>
                  <span>Critical</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                  <span>Major</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                  <span>Minor</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 rounded-full bg-teal-500"></div>
                  <span>Neutral</span>
                </div>
              </div>
            </div>

            {/* Issues by Category */}
            <div className="space-y-2">
              <div>
                <h4 className="text-[10px] font-medium text-foreground mb-1">Issues by Category</h4>
                <p className="text-[8px] text-muted-foreground">Distribution of issues across categories</p>
              </div>
              
              {/* Mock Donut Chart */}
              <div className="relative h-[80px] flex items-center justify-center">
                <div className="relative w-16 h-16">
                  {/* Outer ring segments for categories */}
                  <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 64 64">
                    {/* Terminology (red) */}
                    <circle cx="32" cy="32" r="24" fill="none" stroke="#ef4444" strokeWidth="8" 
                            strokeDasharray="12 72" strokeDashoffset="0" />
                    {/* Accuracy (green) */}
                    <circle cx="32" cy="32" r="24" fill="none" stroke="#22c55e" strokeWidth="8" 
                            strokeDasharray="24 60" strokeDashoffset="-12" />
                    {/* Linguistic (purple) */}
                    <circle cx="32" cy="32" r="24" fill="none" stroke="#a855f7" strokeWidth="8" 
                            strokeDasharray="12 72" strokeDashoffset="-36" />
                    {/* Style (teal) */}
                    <circle cx="32" cy="32" r="24" fill="none" stroke="#14b8a6" strokeWidth="8" 
                            strokeDasharray="18 66" strokeDashoffset="-48" />
                    {/* Others (blue/yellow) */}
                    <circle cx="32" cy="32" r="24" fill="none" stroke="#3b82f6" strokeWidth="8" 
                            strokeDasharray="6 78" strokeDashoffset="-66" />
                    <circle cx="32" cy="32" r="24" fill="none" stroke="#eab308" strokeWidth="8" 
                            strokeDasharray="6 78" strokeDashoffset="-72" />
                    <circle cx="32" cy="32" r="24" fill="none" stroke="#f97316" strokeWidth="8" 
                            strokeDasharray="6 78" strokeDashoffset="-78" />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-[8px] font-bold text-foreground">14</span>
                  </div>
                </div>
              </div>
              
              {/* Legend */}
              <div className="grid grid-cols-2 gap-1 text-[7px]">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 rounded-full bg-red-500"></div>
                  <span>Terminology</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 rounded-full bg-green-500"></div>
                  <span>Accuracy</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                  <span>Linguistic</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 rounded-full bg-teal-500"></div>
                  <span>Style</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                  <span>Locale</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                  <span>Cultural</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                  <span>Design</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
