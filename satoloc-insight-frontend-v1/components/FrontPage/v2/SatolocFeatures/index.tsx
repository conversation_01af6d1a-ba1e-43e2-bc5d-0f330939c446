"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Languages, CheckCircle, Search, PenTool } from "lucide-react";
import { AILocalization } from "./AILocalization";
import { AutoLQA } from "./AutoLQA";
import { SmartContent } from "./SmartContent";
import { Multilanguages } from "./Multilanguages";

export function SatolocFeatures() {
  const features = [
    {
      id: "ai-localization",
      icon: Languages,
      iconColor: "text-blue-600",
      iconBorder: "border-blue-600",
      iconBg: "bg-blue-100 dark:bg-blue-900/20",
      title: "AI Localization",
      description:
        "Translate content in your brand tone, with full QA and style consistency.",
      imagePlaceholder: "AI Localization Dashboard",
    },
    {
      id: "auto-lqa",
      icon: CheckCircle,
      iconColor: "text-pink-600",
      iconBorder: "border-pink-600",
      iconBg: "bg-pink-100 dark:bg-pink-900/20",
      title: "AutoLQA (Linguistic QA)",
      description:
        "See translation issues directly on your URL without any plugins or exports.",
      imagePlaceholder: "Quality Metrics Overview",
    },
    {
      id: "multilingual-seo",
      icon: Search,
      iconColor: "text-green-600",
      iconBorder: "border-green-600",
      iconBg: "bg-green-100 dark:bg-green-900/20",
      title: "Multilingual SEO Insights",
      description:
        "Compete in every market. Detect keyword gaps and generate SEO-ready pages.",
      imagePlaceholder: "SEO Analytics Dashboard",
    },
    {
      id: "smart-content",
      icon: PenTool,
      iconColor: "text-purple-600",
      iconBorder: "border-purple-600",
      iconBg: "bg-purple-100 dark:bg-purple-900/20",
      title: "Smart Content Engine",
      description:
        "AI-written pages tailored to your goals, powered by your brand data.",
      imagePlaceholder: "Content Creation Interface",
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <section className="py-24 bg-muted/40">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-2">
            What You Can Do with SatoLOC Insight
          </h2>
          <p className="text-lg font-thin text-muted-foreground max-w-2xl mx-auto">
            All your multilingual content needs in one powerful platform
          </p>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-6"
        >
          {features.map((feature) => {
            const IconComponent = feature.icon;

            return (
              <motion.div
                key={feature.id}
                variants={itemVariants}
                className="group"
              >
                <Card className="h-full border border-border hover:border-primary/20 transition-colors shadow-none">
                  <CardContent className="p-4">
                    {/* Feature Header */}
                    <div className="flex items-center gap-4 mb-2">
                      <div
                        className={`p-2 rounded-lg border ${feature.iconBorder}`}
                      >
                        <IconComponent
                          className={`h-4 w-4 ${feature.iconColor}`}
                        />
                      </div>
                      <h3 className="text-xl font-semibold text-foreground">
                        {feature.title}
                      </h3>
                    </div>

                    {/* Feature Description */}
                    <p className="text-muted-foreground mb-8 leading-relaxed font-thin">
                      {feature.description}
                    </p>

                    {/* Feature Image Placeholder */}
                    <div className="w-full ">
                      {feature.id === "ai-localization" && <AILocalization />}
                      {feature.id === "auto-lqa" && <AutoLQA />}
                      {feature.id === "smart-content" && <SmartContent />}
                      {feature.id === "multilingual-seo" && <Multilanguages />}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </motion.div>
      </div>
    </section>
  );
}
