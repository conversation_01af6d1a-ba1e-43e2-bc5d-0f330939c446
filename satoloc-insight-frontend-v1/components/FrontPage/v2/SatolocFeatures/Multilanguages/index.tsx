"use client";

import React from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  ChevronDown,
  Filter,
  Search,
  BarChart3,
  Target,
  TrendingUp,
  Award,
  ArrowUp,
  ArrowDown,
  ArrowRight,
} from "lucide-react";
import { Card } from "@/components/ui/card";

export const Multilanguages = () => {
  const metrics = [
    {
      label: "Organic Traffic",
      value: "158,423",
      change: "12.3% vs last month",
      changeType: "positive",
      icon: BarChart3,
      iconColor: "text-blue-600",
      iconBg: "bg-blue-100",
    },
    {
      label: "Shared Keywords",
      value: "2,845",
      change: "38% of total keywords",
      changeType: "neutral",
      icon: Target,
      iconColor: "text-purple-600",
      iconBg: "bg-purple-100",
    },
    {
      label: "Lost Rankings",
      value: "128",
      change: "4.5% since last week",
      changeType: "negative",
      icon: TrendingUp,
      iconColor: "text-red-600",
      iconBg: "bg-red-100",
    },
    {
      label: "Opportunity Score",
      value: "76/100",
      change: "8 points vs competitor",
      changeType: "positive",
      icon: Award,
      iconColor: "text-green-600",
      iconBg: "bg-green-100",
    },
  ];

  const tableData = [
    {
      category: "Gold prices",
      gap: "Page lacks live pricing vs competitor",
      recommendation: [
        "1. Create live gold rates page with hourly updates",
        "2. Add market analysis section with expert commentary",
        "3. Implement price history charts (1D | 1W | 1M | 1Y views)",
      ],
      competitorUrl: "gold-prices...",
      impact: "High",
      status: "To Do",
      assignee: "Unassigned",
    },
    {
      category: "Interest calculation",
      gap: "Weak loan/deposit calculator",
      recommendation: [
        "1. Rebuild calculator with side-by-side comparison feature",
        "2. Add interactive sliders for amount, term, and rate inputs",
        "3. Include downloadable amortization schedule with tax benefits",
        "4. Create mobile-optimized version with simplified UI",
      ],
      competitorUrl: "interest-calculation-tool...",
      impact: "High",
      status: "In Prog",
      assignee: "Ahmad Y",
    },
    {
      category: "USD TRY",
      gap: "No FX forecast page",
      recommendation: [
        "1. Create USD/TRY prediction simulator with 3/6/12-month forecasts",
        "2. Add FAQ section with 15+ common questions and structured data",
        "3. Include expert commentary from bank economists (weekly updates)",
        "4. Implement email alerts for major exchange rate movements",
      ],
      competitorUrl: "dollar-exchange-rate...",
      impact: "Medium",
      status: "To Do",
      assignee: "Mehmet S",
    },
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "To Do":
        return (
          <Badge
            variant="outline"
            className="text-[7px] px-1 py-0 text-muted-foreground border-gray-300"
          >
            To Do
          </Badge>
        );
      case "In Prog":
        return (
          <Badge
            variant="outline"
            className="text-[7px] px-1 py-0 text-blue-600 border-blue-300 bg-blue-50"
          >
            In Prog
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="text-[7px] px-1 py-0">
            {status}
          </Badge>
        );
    }
  };

  const getImpactBadge = (impact: string) => {
    switch (impact) {
      case "High":
        return (
          <Badge
            variant="destructive"
            className="text-[7px] px-1 py-0 bg-red-100 text-red-700 border-red-200"
          >
            High
          </Badge>
        );
      case "Medium":
        return (
          <Badge
            variant="outline"
            className="text-[7px] px-1 py-0 text-orange-600 border-orange-300 bg-orange-50"
          >
            Medium
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="text-[7px] px-1 py-0">
            {impact}
          </Badge>
        );
    }
  };

  return (
    <Card className="w-full bg-background rounded-lg border overflow-hidden text-[10px] shadow-none">
      {/* Header */}
      <div className="p-2 bg-background ">
        {/* Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1">
              <span className="text-[8px] text-muted-foreground">
                Select Competitor
              </span>
              <Button variant="outline" className="h-4 px-1 text-[8px]">
                Gareth <ChevronDown className="w-2 h-2 ml-1" />
              </Button>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <Button
              variant="outline"
              size="sm"
              className="h-5 px-2 text-[8px] text-muted-foreground border"
            >
              {/* <Filter className="w-2 h-2 mr-1" /> */}
              Filter
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="h-5 px-2 text-[8px] text-muted-foreground border"
            >
              {/* <Search className="w-2 h-2 mr-1" /> */}
              Search insights...
            </Button>
          </div>
        </div>
      </div>

      {/* Metrics Cards */}
      <div className="p-2 bg-background border-b border-border shadow-none">
        <div className="grid grid-cols-4 gap-2">
          {metrics.map((metric, index) => {
            const IconComponent = metric.icon;
            return (
              <Card
                key={index}
                className="bg-background border rounded-lg p-2 shadow-none"
              >
                {/* Header with title and icon */}
                <div className="flex justify-between items-start mb-1">
                  <div className="text-[8px] text-muted-foreground text-left">
                    {metric.label}
                  </div>
                  <div
                    className={`w-3 h-3 rounded-full ${metric.iconBg} flex items-center justify-center`}
                  >
                    <IconComponent className={`w-2 h-2 ${metric.iconColor}`} />
                  </div>
                </div>

                {/* Large value */}
                <div className="text-[10px] font-bold text-muted-foreground mb-1 text-left">
                  {metric.value}
                </div>

                {/* Change indicator */}
                <div
                  className={`text-[8px] flex items-center gap-1 ${
                    metric.changeType === "positive"
                      ? "text-green-600"
                      : metric.changeType === "negative"
                        ? "text-red-600"
                        : "text-muted-foreground"
                  }`}
                >
                  {metric.changeType === "positive" && (
                    <ArrowUp className="w-2 h-2" />
                  )}
                  {metric.changeType === "negative" && (
                    <ArrowDown className="w-2 h-2" />
                  )}
                  {metric.changeType === "neutral" && (
                    <ArrowRight className="w-2 h-2" />
                  )}
                  {metric.change}
                </div>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <div className="bg-background px-2 py-1 border">
          <div className="text-[8px] font-medium text-muted-foreground">
            SEO Gap Analysis & Action Items
          </div>
          <div className="text-[7px] text-muted-foreground">
            Actionable insights based on competitor analysis. Quick filters to
            track progress.
          </div>
        </div>

        <table className="w-full text-[6px]">
          <thead className="bg-background">
            <tr className="bg-background border-b border-border">
              <th className="px-2 py-1 text-left font-medium text-foreground uppercase">
                Category
              </th>
              <th className="px-2 py-1 text-left font-medium text-foreground uppercase">
                Gap Identified
              </th>
              <th className="px-2 py-1 text-left font-medium text-foreground uppercase">
                Recommended Action
              </th>
              <th className="px-2 py-1 text-left font-medium text-foreground uppercase">
                Competitor URL
              </th>
              <th className="px-2 py-1 text-left font-medium text-foreground uppercase">
                Impact
              </th>
              <th className="px-2 py-1 text-left font-medium text-foreground uppercase">
                Status
              </th>
              <th className="px-2 py-1 text-left font-medium text-foreground uppercase">
                Assignee
              </th>
              <th className="px-2 py-1 text-left font-medium text-foreground uppercase">
                AI Content
              </th>
            </tr>
          </thead>
          <tbody>
            {tableData.map((row, index) => (
              <tr key={index} className="border-b border-border ">
                <td className="px-2 py-1 font-medium text-foreground">
                  {row.category}
                </td>
                <td className="px-2 py-1 text-foreground">{row.gap}</td>
                <td className="px-2 py-1 text-foreground max-w-[120px]">
                  <div className="space-y-1">
                    {row.recommendation.map((line, i) => (
                      <div key={i} className="truncate text-[7px]" title={line}>
                        {line.length > 50
                          ? line.substring(0, 50) + "..."
                          : line}
                      </div>
                    ))}
                  </div>
                </td>
                <td className="px-2 py-1">
                  <span className="text-[#1279b4] underline cursor-pointer hover:text-blue-800">
                    {row.competitorUrl}
                  </span>
                </td>
                <td className="px-2 py-1">{getImpactBadge(row.impact)}</td>
                <td className="px-2 py-1">{getStatusBadge(row.status)}</td>
                <td className="px-2 py-1 text-foreground">{row.assignee}</td>
                <td className="px-2 py-1">
                  <Button className="h-4 px-1 text-[6px] bg-[#1279b4] hover:bg-[#0f6a9a] text-white rounded-sm shadow-none">
                    Generate Content
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </Card>
  );
};
