"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Globe, RefreshCw, Search, X } from "lucide-react";

// Mock data that matches the image
const mockContent = [
  {
    id: 1,
    title: "Discover South Africa",
    status: "Draft",
    language: "English (en)",
    post_type: "post",
    excerpt: "A tapestry of landscapes and cultures awaiting exploration",
    wp_id: 2640,
    id_number: 225,
  },
  {
    id: 2,
    title: "İçerikle Güven Oluşturma",
    status: "Published",
    language: "Türkçe (tr)",
    post_type: "post",
    excerpt: "<PERSON><PERSON>ş içerikli pazarlama stratejileri ile güven oluşturma",
    wp_id: 2479,
    id_number: 193,
  },
  {
    id: 3,
    title: "人工智能内容创作",
    status: "Published",
    language: "中文 (中国) (zh)",
    post_type: "post",
    excerpt: "利用人工智能工具提高内容创作效率",
    wp_id: 2470,
    id_number: 196,
  },
];

const mockLanguages = [
  { key: "English (en)", count: 16, checked: false },
  { key: "Türkçe (tr)", count: 12, checked: false },
  { key: "中文 (中国) (zh)", count: 10, checked: false },
];

export const AILocalization = () => {
  const [activeTab, setActiveTab] = useState("posts");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedLanguages, setSelectedLanguages] = useState<Set<string>>(
    new Set()
  );

  // Filter content based on search and language selection
  const filteredContent = mockContent.filter((item) => {
    const matchesSearch =
      searchTerm === "" ||
      item.title.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesLanguage =
      selectedLanguages.size === 0 || selectedLanguages.has(item.language);
    return matchesSearch && matchesLanguage;
  });

  // Group content by language
  const contentByLanguage = filteredContent.reduce(
    (acc, item) => {
      if (!acc[item.language]) {
        acc[item.language] = [];
      }
      acc[item.language].push(item);
      return acc;
    },
    {} as Record<string, typeof mockContent>
  );

  const handleLanguageToggle = (language: string) => {
    const newSelected = new Set(selectedLanguages);
    if (newSelected.has(language)) {
      newSelected.delete(language);
    } else {
      newSelected.add(language);
    }
    setSelectedLanguages(newSelected);
  };

  const ContentCard = ({ item }: { item: (typeof mockContent)[0] }) => (
    <Card className="mb-2 shadow-none border rounded-sm">
      <CardContent className="p-2">
        <div className="flex items-center justify-between mb-1">
          <h3 className="font-medium text-xs text-foreground line-clamp-1 flex-1 mr-1">
            {item.title}
          </h3>
          <Badge
            variant={item.status === "Published" ? "default" : "secondary"}
            className={`text-[8px] px-1 py-0 h-4 flex-shrink-0 ${
              item.status === "Published"
                ? "bg-green-100 text-green-800 hover:bg-green-100"
                : "bg-yellow-100 text-yellow-800 hover:bg-yellow-100"
            }`}
          >
            {item.status}
          </Badge>
        </div>
        <p className="text-[9px] text-muted-foreground line-clamp-1 mb-1">
          {item.excerpt}
        </p>
        <div className="text-[8px] text-muted-foreground">
          <span>
            WP ID: {item.wp_id} • ID: {item.id_number}
          </span>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="w-full h-full bg-background">
      {/* Header */}
      <Card className="shadow-none rounded-sm p-0 overflow-hidden mb-3">
        <CardHeader className="flex flex-col md:flex-row items-start md:items-center justify-between gap-2 py-2 px-3 border-b">
          <div className="flex-grow">
            <div className="flex items-center gap-2">
              <Globe className="h-4 w-4 text-[#1279b4] flex-shrink-0" />
              <CardTitle className="text-sm font-semibold text-[#1279b4]">
                Content Management
              </CardTitle>
            </div>
            <div className="flex items-center gap-2 mt-1">
              <span className="text-[10px] text-muted-foreground flex-shrink-0">
                Connected to:
              </span>
              <div className="px-2 py-0.5 text-[10px] rounded-sm bg-accent/50 text-accent-foreground truncate">
                www.satoloc.com
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-5 px-2 py-0 text-[8px] text-destructive border border-destructive"
                disabled
              >
                Disconnect
              </Button>
            </div>
          </div>
          <div className="flex gap-2 flex-wrap items-center flex-shrink-0">
            <Button
              variant="outline"
              size="sm"
              className="h-6 text-[10px] px-2"
              disabled
            >
              <RefreshCw className="mr-1 h-3 w-3" />
              Refresh
            </Button>
            <Button
              size="sm"
              className="h-6 bg-[#1279b4] hover:bg-[#1279b4]/90 text-white text-[10px] px-2"
              disabled
            >
              Sync All from WP
            </Button>
          </div>
        </CardHeader>

        {/* Search Section */}
        <div className="px-3 py-2 border-b bg-muted/30">
          <div className="relative">
            <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search posts and pages by title..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-4 py-1.5 text-xs border border-input bg-background rounded-sm focus:outline-none focus:ring-1 focus:ring-ring focus:border-transparent"
              disabled
            />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm("")}
                className="absolute right-6 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground hover:text-foreground transition-colors"
                title="Clear search"
              >
                <X className="h-3 w-3" />
              </button>
            )}
          </div>
        </div>

        {/* Tabs and Content */}
        <div className="p-3">
          <Tabs value={activeTab} onValueChange={() => {}} className="w-full">
            <TabsList className="grid w-full grid-cols-2 pointer-events-none h-8">
              <TabsTrigger
                value="posts"
                className="flex items-center gap-2 text-xs px-1"
              >
                Posts
                <span className="text-[8px] bg-muted-foreground/20 px-1 py-0 rounded-full">
                  38
                </span>
              </TabsTrigger>
              <TabsTrigger
                value="pages"
                className="flex items-center gap-2 text-xs px-1"
              >
                Pages
                <span className="text-[8px] bg-muted-foreground/20 px-1 py-0 rounded-full">
                  32
                </span>
              </TabsTrigger>
            </TabsList>

            {/* Language Filter */}
            <div className="mt-3">
              <div className="flex items-center gap-2 mb-2">
                <span className="text-xs font-medium text-foreground">
                  Filter by Language
                </span>
              </div>
              <div className="flex gap-2">
                {mockLanguages.map((lang) => (
                  <label
                    key={lang.key}
                    className="flex items-center gap-1.5 text-[10px] cursor-pointer px-2 py-1 rounded-sm border border-input"
                  >
                    <input
                      type="checkbox"
                      checked={selectedLanguages.has(lang.key)}
                      onChange={() => handleLanguageToggle(lang.key)}
                      className="rounded border-gray-300 w-3 h-3"
                      disabled
                    />
                    <span className="text-foreground">{lang.key}</span>
                    <span className="text-[9px] text-muted-foreground">
                      ({lang.count})
                    </span>
                  </label>
                ))}
              </div>
            </div>

            <TabsContent value="posts" className="mt-3">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {Object.entries(contentByLanguage).map(
                  ([languageKey, items]) => (
                    <div
                      key={languageKey}
                      className="flex flex-col border rounded-md"
                    >
                      <div className="font-medium text-[#1279b4] px-3 pt-2 flex items-center gap-2">
                        <Globe className="h-3 w-3 text-muted-foreground" />
                        <span className="text-xs">{languageKey}</span>
                        <span className="text-[10px] text-muted-foreground">
                          ({items.length})
                        </span>
                      </div>
                      <ScrollArea className="h-[95px] w-full rounded-md p-2">
                        {items.length > 0 ? (
                          <div className="space-y-2">
                            {items.map((item) => (
                              <ContentCard key={item.id} item={item} />
                            ))}
                          </div>
                        ) : (
                          <div className="text-center p-3 text-muted-foreground italic text-xs">
                            No posts in this language
                          </div>
                        )}
                      </ScrollArea>
                    </div>
                  )
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </Card>
    </div>
  );
};
