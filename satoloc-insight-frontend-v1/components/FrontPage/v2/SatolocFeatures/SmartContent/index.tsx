import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Plus,
  Type,
  Heading1,
  Heading2,
  Heading3,
  List,
  ListOrdered,
  Quote,
  Code,
  MousePointer,
  Image,
  Sparkles,
  Trash2,
  Copy,
  FileDown,
  Save,
  Share,
  RotateCcw,
  Settings,
} from "lucide-react";

export const SmartContent = () => {
  const addContentItems = [
    { icon: Plus, label: "Empty Space", shortcut: "" },
    { icon: Type, label: "Paragraph", shortcut: "" },
    { icon: Heading1, label: "Heading 1", shortcut: "" },
    { icon: Heading2, label: "Heading 2", shortcut: "" },
    { icon: Heading3, label: "Heading 3", shortcut: "" },
    { icon: List, label: "Bullet List", shortcut: "" },
    { icon: ListOrdered, label: "Numbered List", shortcut: "" },
    { icon: Quote, label: "Blockquote", shortcut: "" },
    { icon: Code, label: "Code Block", shortcut: "" },
  ];

  const interactiveElements = [
    { icon: MousePointer, label: "Button", shortcut: "" },
    { icon: Image, label: "Image URL", shortcut: "" },
    { icon: Sparkles, label: "AI Image", shortcut: "" },
  ];

  return (
    <div className="w-full h-full bg-background">
      <div className="flex h-full">
        {/* Left Panel - Add Content */}
        <Card className="w-[120px] border-r bg-background self-start shadow-none">
          <div className="p-1">
            <h3 className="text-[10px] font-semibold text-foreground mb-2 mt-1 pl-2">
              Add Content
            </h3>

            {/* Content Elements */}
            <div className="space-y-1 mb-3">
              {addContentItems.map((item, index) => {
                const IconComponent = item.icon;
                return (
                  <button
                    key={index}
                    className="w-full flex items-center gap-2 px-2 py-0.5 text-[9px] text-muted-foreground hover:bg-muted/50 rounded-sm transition-colors cursor-pointer"
                    disabled
                  >
                    <IconComponent className="h-3 w-3 flex-shrink-0" />
                    <span className="truncate">{item.label}</span>
                  </button>
                );
              })}
            </div>

            {/* Interactive Elements */}
            <div className="border-t pt-2">
              <h4 className="text-[9px] font-medium text-foreground mb-1">
                Interactive Elements
              </h4>
              <div className="space-y-1 mb-2">
                {interactiveElements.map((item, index) => {
                  const IconComponent = item.icon;
                  return (
                    <button
                      key={index}
                      className="w-full flex items-center gap-2 px-2 py-0.5 text-[9px] text-muted-foreground hover:bg-muted/50 rounded-sm transition-colors cursor-pointer"
                      disabled
                    >
                      <IconComponent className="h-3 w-3 flex-shrink-0" />
                      <span className="truncate">{item.label}</span>
                    </button>
                  );
                })}
              </div>

              {/* Delete Element */}
              <button
                className="w-full flex items-center gap-2 px-2 py-0.5 text-[9px] text-red-600 hover:bg-red-50 rounded-sm transition-colors cursor-pointer"
                disabled
              >
                <Trash2 className="h-3 w-3 flex-shrink-0" />
                <span className="truncate">Delete Element</span>
              </button>
            </div>
          </div>
        </Card>

        {/* Right Panel - Visual Editor */}
        <div className="p-2">
          <Card className="flex border shadow-none">
            <div className="flex-1 flex flex-col">
              {/* Editor Content */}
              <div className="flex-1 p-3 bg-background overflow-hidden">
                <div className="h-full bg-background rounded-sm border p-3 overflow-y-auto">
                  {/* Article Title */}
                  <h1 className="text-[12px] font-bold text-foreground mb-3 pl-4 leading-tight">
                    SEO, AEO, and GEO: The New Trifecta of Digital Marketing
                    Success in 2025
                  </h1>

                  {/* Article Content */}
                  <div className="space-y-3 text-[10px] leading-relaxed">
                    <div className="flex items-start gap-2">
                      <Plus className="h-3 w-3 text-muted-foreground mt-0.5 flex-shrink-0" />
                      <div>
                        <h2 className="font-semibold text-foreground mb-1">
                          Introduction: The Evolution of Search in 2025
                        </h2>
                        <p className="text-muted-foreground">
                          Digital marketing in 2025 is about understanding user
                          intent, leveraging AI-powered assistants,
                          hyper-personalized mobile experiences, and
                          location-aware devices have fundamentally changed the
                          search landscape. Users expect instant, relevant, and
                          context-sensitive answers whether they are asking
                          their smart speaker for financial advice, searching
                          for a nearby crypto ATM, or comparing products via
                          their phone camera.
                        </p>
                      </div>
                    </div>

                    <div className="flex gap-3">
                      {/* Featured Image - Left aligned */}
                      <div className="flex-shrink-0 pl-4">
                        <div className="relative">
                          <img
                            src="https://satoloc-insight-images.s3.eu-north-1.amazonaws.com/custom-content-images/2025/07/f6d4c4caae224d1fb650794cf8c35401.png"
                            alt="SEO, AEO, and GEO Digital Marketing Trifecta"
                            style={{ height: "150px", width: "auto" }}
                          />
                          {/* Selection border */}
                          <div className="absolute inset-0 border-2 border-blue-500 rounded pointer-events-none opacity-50"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Properties Panel */}
            <div className="w-[120px] border-l bg-background">
              <div className="p-2">
                <div className="flex items-center gap-1 mb-2">
                  <Settings className="h-3 w-3 text-muted-foreground" />
                  <span className="text-[9px] font-semibold text-foreground">
                    Properties
                  </span>
                </div>

                <div className="space-y-2">
                  <div>
                    <label className="text-[8px] text-muted-foreground block mb-1">
                      Element Type
                    </label>
                    <select
                      className="w-full text-[8px] p-1 border rounded"
                      disabled
                    >
                      <option>Paragraph</option>
                    </select>
                  </div>

                  <div>
                    <label className="text-[8px] text-muted-foreground block mb-1">
                      Style
                    </label>
                    <input
                      type="text"
                      className="w-full text-[8px] p-1 border rounded"
                      placeholder="margin-top: 24px; margin-bottom: 12px; position:"
                      disabled
                    />
                  </div>

                  <div>
                    <label className="text-[8px] text-muted-foreground block mb-1">
                      Text Content
                    </label>
                    <textarea
                      className="w-full text-[7px] p-1 border rounded h-16 resize"
                      placeholder="SEO, AEO, and GEO: The New Trifecta of Digital Marketing Success in 2025"
                      disabled
                    />
                  </div>

                  <div className="pt-2 border-t">
                    <button
                      className="w-full bg-[#1279b4] text-white text-[8px] py-1.5 px-2 rounded hover:bg-[#1279b4]/90 transition-colors"
                      disabled
                    >
                      <Sparkles className="h-3 w-3 inline mr-1" />
                      Improve Content
                    </button>
                  </div>

                  <div className="space-y-1">
                    <label className="text-[8px] text-muted-foreground block">
                      Font Size
                    </label>
                    <input
                      type="text"
                      className="w-full text-[8px] p-1 border rounded"
                      placeholder="30px"
                      disabled
                    />
                  </div>

                  <div className="space-y-1">
                    <label className="text-[8px] text-muted-foreground block">
                      Font Weight
                    </label>
                    <select
                      className="w-full text-[8px] p-1 border rounded"
                      disabled
                    >
                      <option>800</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Bottom Footer Toolbar */}
      <div className="flex items-center justify-between px-3 py-2 border-t bg-background">
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="text-[6px] px-2 py-1 h-5">
            <div className="w-1 h-1 bg-green-500 rounded-full mr-1"></div>
            Connected
          </Badge>
          <span className="text-[8px] text-muted-foreground">
            www.satoloc.com
          </span>
          <button
            className="text-[8px] text-red-500 hover:underline ml-1"
            disabled
          >
            Disconnect
          </button>
        </div>

        <div className="flex items-center gap-1">
          <button
            className="flex items-center gap-1 hover:bg-muted rounded-sm text-[8px] px-2 py-1 transition-colors text-[#1279b4]"
            disabled
          >
            <Copy className="h-3 w-3 text-[#1279b4]" />
            Copy
          </button>
          <button
            className="flex items-center gap-1 hover:bg-muted rounded-sm text-[8px] px-2 py-1 transition-colors text-[#1279b4]"
            disabled
          >
            <FileDown className="h-3 w-3 text-[#1279b4]" />
            Export
          </button>
          <button
            className="flex items-center gap-1 hover:bg-muted rounded-sm text-[8px] px-2 py-1 transition-colors text-[#1279b4]"
            disabled
          >
            <Save className="h-3 w-3 text-[#1279b4]" />
            Save
          </button>
          <button
            className="flex items-center gap-1 hover:bg-muted rounded-sm text-[8px] px-2 py-1 transition-colors text-[#1279b4]"
            disabled
          >
            <Share className="h-3 w-3 text-[#1279b4]" />
            Push to WP
          </button>
          <button
            className="flex items-center gap-1 hover:bg-muted rounded-sm text-[8px] px-2 py-1 transition-colors text-[#1279b4]"
            disabled
          >
            <RotateCcw className="h-3 w-3 text-[#1279b4]" />
            Regenerate
          </button>
        </div>
      </div>
    </div>
  );
};
