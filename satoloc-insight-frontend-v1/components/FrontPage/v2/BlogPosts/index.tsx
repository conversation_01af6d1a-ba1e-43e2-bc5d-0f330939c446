"use client";

import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, User, ArrowRight, BookOpen } from "lucide-react";
import {
  useWordPressPosts,
  useWordPressCategories,
  useWordPressMedia,
} from "@/internal-api/wordpress-blog";
import { truncateWordPressContent } from "@/lib/wordpress-content";

export const BlogPosts = () => {
  // Fetch the latest 3 posts
  const {
    data: posts,
    isLoading: postsLoading,
    error: postsError,
  } = useWordPressPosts({
    per_page: 3,
    orderby: "date",
    order: "desc",
    status: "publish",
  });

  // Fetch categories for display
  const { data: categories } = useWordPressCategories();

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getExcerpt = (content: string, length: number = 120) => {
    return truncateWordPressContent(content, length);
  };

  const getCategoryName = (categoryId: number) => {
    const category = categories?.find((cat) => cat.id === categoryId);
    return category?.name || `Category ${categoryId}`;
  };

  if (postsLoading) {
    return (
      <section className="py-20 bg-gradient-to-b from-background to-muted/20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="h-8 bg-muted animate-pulse rounded mb-4 max-w-md mx-auto" />
            <div className="h-4 bg-muted animate-pulse rounded max-w-2xl mx-auto" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="overflow-hidden">
                <div className="h-48 bg-muted animate-pulse" />
                <CardContent className="p-6">
                  <div className="h-4 bg-muted animate-pulse rounded mb-3" />
                  <div className="h-6 bg-muted animate-pulse rounded mb-3" />
                  <div className="h-4 bg-muted animate-pulse rounded mb-4" />
                  <div className="h-4 bg-muted animate-pulse rounded" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (postsError || !posts || posts.length === 0) {
    return (
      <section className="py-20 bg-gradient-to-b from-background to-muted/20">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <BookOpen className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h2 className="text-3xl font-bold mb-4">Latest Insights</h2>
            <p className="text-muted-foreground mb-8">
              Stay updated with our latest insights and best practices.
            </p>
            <Link href="/blog">
              <Button className="bg-[#1279b4] hover:bg-[#00497A] text-white">
                Visit Our Blog
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 bg-gradient-to-b from-background to-muted/20">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
            Knowledge Hub
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto font-thin">
            Latest insights on localization, SEO, and content strategy.
          </p>
        </div>

        {/* Blog Posts Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {posts.map((post, index) => (
            <BlogPostCard
              key={post.id}
              post={post}
              index={index}
              getCategoryName={getCategoryName}
              formatDate={formatDate}
              getExcerpt={getExcerpt}
            />
          ))}
        </div>

        {/* View All Articles Button */}
        <div className="text-center">
          <Link href="/blog">
            <Button
              size="lg"
              className="bg-transparent shadow-none text-[#1279b4] hover:text-[#1279b4]/80 hover:bg-transparent px-8 py-3 text-lg font-thin group"
            >
              View All Articles
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

// Blog Post Card Component
interface BlogPostCardProps {
  post: any;
  index: number;
  getCategoryName: (categoryId: number) => string;
  formatDate: (dateString: string) => string;
  getExcerpt: (content: string, length?: number) => string;
}

function BlogPostCard({
  post,
  index,
  getCategoryName,
  formatDate,
  getExcerpt,
}: BlogPostCardProps) {
  // Fetch featured media if available
  const { data: media } = useWordPressMedia(
    post.featured_media || 0,
    !!post.featured_media
  );

  return (
    <Card className="group hover:shadow-lg transition-all duration-300 overflow-hidden border-0 shadow-none border">
      <div className="relative">
        {media && media.source_url ? (
          <div className="h-48 overflow-hidden">
            <img
              src={media.source_url}
              alt={media.alt_text || post.title.rendered}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            />
          </div>
        ) : (
          <div className="h-48 bg-gradient-to-br from-primary/20 to-blue-600/20 flex items-center justify-center">
            <div className="text-4xl font-bold text-primary/40">
              {post.title.rendered.charAt(0).toUpperCase()}
            </div>
          </div>
        )}

        {/* Category Badge */}
        {post.categories && post.categories.length > 0 && (
          <div className="absolute top-4 left-4">
            <Badge className="bg-background/90 text-primary hover:bg-background backdrop-blur-sm">
              {getCategoryName(post.categories[0])}
            </Badge>
          </div>
        )}
      </div>

      <CardContent className="p-6">
        {/* Meta Information */}
        <div className="flex items-center gap-4 text-sm text-muted-foreground mb-3">
          <div className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            <span>{formatDate(post.date)}</span>
          </div>
          <div className="flex items-center gap-1">
            <User className="h-4 w-4" />
            <span>Author {post.author}</span>
          </div>
        </div>

        {/* Title */}
        <h3 className="text-xl font-semibold mb-3 group-hover:text-primary transition-colors line-clamp-2">
          <Link href={`/blog/${post.slug}`} className="hover:underline">
            {post.title.rendered}
          </Link>
        </h3>

        {/* Excerpt */}
        <p className="text-muted-foreground mb-4 line-clamp-3">
          {getExcerpt(post.excerpt.rendered || post.content.rendered)}
        </p>

        {/* Read More Link */}
        <Link href={`/blog/${post.slug}`}>
          <Button
            variant="ghost"
            size="sm"
            className="p-0 h-auto font-semibold text-primary hover:text-primary/80 group-hover:gap-2 transition-all"
          >
            Read More
            <ArrowRight className="ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform" />
          </Button>
        </Link>
      </CardContent>
    </Card>
  );
}
