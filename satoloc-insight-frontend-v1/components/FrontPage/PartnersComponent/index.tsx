"use client";

import { motion } from "framer-motion";
import { Container, Divider } from "@mui/material";
import { useHomepageContent } from "@/internal-api/wordpress-blog";
import { GradientText } from "@/components/FrontPage/GradientText";
import Image from "next/image";
import { Divide, Handshake } from "lucide-react";
import { Card } from "@/components/ui/card";

export function PartnersComponent() {
  const { data: homepageContent, isLoading, error } = useHomepageContent();

  if (isLoading) {
    return (
      <section className="py-24 relative overflow-hidden">
        {/* <div className="absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background opacity-50" />
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 right-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl" />
          <div className="absolute bottom-1/4 left-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl" />
        </div> */}

        <Container className="container px-4 relative z-10">
          <div className="text-center mb-16">
            {/* <div className="animate-pulse">
              <div className="h-12 bg-muted rounded w-1/2 mx-auto mb-4" />
              <div className="h-6 bg-muted rounded w-1/3 mx-auto" />
            </div> */}
          </div>
          <div className="flex justify-center items-center gap-8 flex-wrap">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="h-20 w-32 bg-muted rounded-lg" />
              </div>
            ))}
          </div>
        </Container>
      </section>
    );
  }

  const partners = homepageContent?.partners as any;
  const partnerLogos = partners?.logos as any[];

  if (
    error ||
    !partners ||
    !partnerLogos ||
    !Array.isArray(partnerLogos) ||
    partnerLogos.length === 0
  ) {
    return null;
  }

  return partners ? (
    <section className="py-24 relative overflow-hidden bg-background">
      {/* Background gradient and effects */}

      <Card className=" p-6  max-w-7xl mx-auto bg-background shadow-none border-none">
        {/* Section header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center gap-3 mb-4">
            <h2 className="text-4xl font-bold">
              <GradientText>
                {partners.title || "Trusted Partners"}
              </GradientText>
            </h2>
          </div>
          <p className="text-muted-foreground max-w-xl mx-auto">
            {partners.description ||
              "We collaborate with industry leaders to deliver exceptional results and drive innovation forward."}
          </p>
        </motion.div>
        <Divider className="w-full" />
        {/* Partners grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex justify-center items-center gap-6 flex-wrap max-w-5xl mx-auto py-4"
        >
          {partnerLogos.map((partnerLogo: any, index: number) => {
            const PartnerContent = (
              <Card className="p-4 h-20 w-48 flex items-center justify-center bg-white shadow-none border border-gray-200 hover:border-primary/20 transition-colors">
                <Image
                  src={partnerLogo.logo.url}
                  alt={
                    partnerLogo.logo.alt ||
                    partnerLogo.logo.title ||
                    "Partner logo"
                  }
                  width={200}
                  height={200}
                  className="object-contain transition-all duration-300 h-24 w-auto opacity-70 hover:opacity-100"
                />
              </Card>
            );

            const motionProps = {
              key: partnerLogo.logo.id || index,
              initial: { opacity: 0, scale: 0.8 },
              whileInView: { opacity: 1, scale: 1 },
              viewport: { once: true },
              transition: {
                duration: 0.5,
                delay: index * 0.1,
                type: "spring" as const,
                stiffness: 100,
              },
              whileHover: {
                scale: 1.05,
                transition: { duration: 0.2 },
              },
              className: "group",
            };

            // If partner has a URL, wrap in a link
            if (partnerLogo.partner_url) {
              return (
                <motion.a
                  {...motionProps}
                  href={partnerLogo.partner_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`${motionProps.className} cursor-pointer`}
                >
                  {PartnerContent}
                </motion.a>
              );
            }

            // Otherwise, just show the logo without link
            return <motion.div {...motionProps}>{PartnerContent}</motion.div>;
          })}
        </motion.div>

        {/* Show content source indicator in development */}
        {process.env.NODE_ENV === "development" && (
          <div className="mt-8 text-center text-xs text-muted-foreground">
            Partners content source: WordPress CMS ({partnerLogos.length}{" "}
            partners loaded)
          </div>
        )}
      </Card>
    </section>
  ) : null;
}
