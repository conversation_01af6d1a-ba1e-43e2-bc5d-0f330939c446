import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Container } from "@mui/material";
import { GradientText } from "../GradientText";
import { useHomepageContent } from "@/internal-api/wordpress-blog";
import { ArrowRight, Globe2, Search, Sparkles } from "lucide-react";
import { LucideIcon } from "lucide-react";

interface TimelineItemProps {
  phase: string;
  title: string;
  description: string;
  icon: LucideIcon;
  align: "left" | "right";
  index: number;
}

const timelineItems = [
  {
    phase: "Phase 1",
    title: "Launching Localization Insights",
    description: "Advanced LQA automation in beta",
    icon: Globe2,
    align: "right" as const,
  },
  {
    phase: "Phase 2",
    title: "Introducing SEO Insights",
    description: "Elevate your global search visibility",
    icon: Search,
    align: "left" as const,
  },
  {
    phase: "Phase 3",
    title: "Unlocking AI-powered Content",
    description: "Content creation and insights",
    icon: Sparkles,
    align: "right" as const,
  },
];

// Modern Timeline Card Component
const ModernTimelineCard = ({
  phase,
  title,
  description,
  icon: Icon,
  align,
  index,
}: TimelineItemProps) => {
  return (
    <div className="relative mb-24 last:mb-0">
      {/* Timeline dot with pulse effect */}
      <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          whileInView={{ scale: 1, opacity: 1 }}
          viewport={{ once: true }}
          transition={{ delay: 0.2 + index * 0.1, duration: 0.5 }}
          className="w-5 h-5 bg-[#1279b4] rounded-full relative"
        >
          {/* Pulse animation */}
          <span className="absolute w-full h-full rounded-full bg-primary/30 animate-ping" />
        </motion.div>
      </div>

      {/* Card */}
      <div
        className={`flex ${align === "left" ? "justify-end" : "justify-start"} relative`}
      >
        <motion.div
          initial={{ opacity: 0, x: align === "left" ? 50 : -50, y: 20 }}
          whileInView={{ opacity: 1, x: 0, y: 0 }}
          viewport={{ once: true }}
          transition={{
            delay: 0.3 + index * 0.1,
            duration: 0.5,
            type: "spring",
            stiffness: 100,
          }}
          className={`w-[90%] md:w-[45%] bg-white dark:bg-black backdrop-blur-lg rounded-xl shadow-lg border border-primary/10 overflow-hidden hover:shadow-xl transition-all duration-300`}
          whileHover={{ y: -5 }}
        >
          <div className="p-6">
            <div className="flex items-start gap-4">
              <div className="bg-primary/10 p-3 rounded-lg flex-shrink-0">
                <Icon className="h-6 w-6 text-primary" />
              </div>
              <div>
                <span className="inline-block px-3 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full mb-2">
                  {phase}
                </span>
                <h3 className="text-xl font-semibold mb-2">{title}</h3>
                <p className="text-muted-foreground">{description}</p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export function Roadmap() {
  const { data: homepageContent, isLoading, error } = useHomepageContent();

  if (isLoading) {
    return (
      <section id="roadmap" className="py-24 relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background" />
        </div>

        <Container className="container px-4 relative">
          <div className="text-center mb-16">
            <div className="animate-pulse">
              <div className="h-12 bg-muted rounded w-1/2 mx-auto mb-4" />
              <div className="h-6 bg-muted rounded w-1/3 mx-auto" />
            </div>
          </div>
          <div className="max-w-4xl mx-auto space-y-8">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="h-32 bg-muted rounded-xl" />
              </div>
            ))}
          </div>
        </Container>
      </section>
    );
  }

  // Show error or no content state
  if (error || !homepageContent?.roadmap) {
    return (
      <section id="roadmap" className="py-24 relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background" />
        </div>

        <Container className="container px-4 relative">
          <div className="text-center">
            <div className="p-8 border border-dashed border-muted-foreground/30 rounded-lg max-w-2xl mx-auto">
              <h2 className="text-2xl font-semibold mb-4 text-muted-foreground">
                No Roadmap Content
              </h2>
              <p className="text-muted-foreground mb-4">
                Please add roadmap content in the WordPress admin panel under
                the "Roadmap" section.
              </p>
              {error && (
                <p className="text-sm text-destructive">
                  Error: {error.message}
                </p>
              )}
            </div>
          </div>
        </Container>
      </section>
    );
  }

  return (
    <section id="roadmap" className="py-24 relative overflow-hidden">
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background" />
        <div className="absolute inset-0 " />
      </div>

      <Container className="container px-4 relative">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold mb-4">
            <GradientText>
              {homepageContent.roadmap.title ||
                "What's Next: The Evolution of SatoLOC Insight"}
            </GradientText>
          </h2>
          <p className="text-muted-foreground max-w-3xl mx-auto">
            {homepageContent.roadmap.description ||
              "At SatoLOC, we know that one size doesn't fit all. That's why our platform is designed for ultimate flexibility, putting customization at its core. Whether it's bespoke LQA insights, targeted SEO strategies, or smart content creation, SatoLOC Insight molds itself to your unique business needs."}
          </p>
        </motion.div>

        <div className="relative max-w-4xl mx-auto mb-16">
          {/* Animated Timeline Line */}
          <motion.div
            initial={{ scaleY: 0 }}
            whileInView={{ scaleY: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 1, delay: 0.5 }}
            className="absolute left-1/2 transform -translate-x-1/2 h-full w-0.5 bg-gray-200 dark:bg-gray-700"
            style={{ transformOrigin: "top" }}
          />

          {/* Timeline Items */}
          {timelineItems.map((item, index) => (
            <ModernTimelineCard key={index} {...item} index={index} />
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="flex justify-center"
        >
          <Button
            size="lg"
            className="group px-8 py-3 relative overflow-hidden bg-black dark:bg-white text-white dark:text-black hover:bg-black/90 dark:hover:bg-white/90 rounded-md"
            onClick={() => {
              window.open("https://calendly.com/satoloc/30min", "_blank");
            }}
          >
            <span className="relative z-10 flex items-center">
              Contact Us
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </span>
          </Button>
        </motion.div>

        {/* Show content source indicator in development */}
        {process.env.NODE_ENV === "development" && (
          <div className="mt-8 text-center text-xs text-muted-foreground">
            Roadmap content source: WordPress CMS
          </div>
        )}
      </Container>
    </section>
  );
}
