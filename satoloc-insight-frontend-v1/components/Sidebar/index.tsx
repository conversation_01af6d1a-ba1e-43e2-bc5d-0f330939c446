"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useSession } from "next-auth/react";
import { cn } from "@/lib/utils";
import Image from "next/image";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
  SidebarSeparator,
  useSidebar,
} from "@/components/ui/sidebar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Settings,
  Users,
  LogOut,
  LogIn,
  ChevronDown,
  Shield,
  LineChart,
  ArrowRightLeft,
  Languages,
  SquarePen,
  Crown,
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useTheme } from "@mui/material/styles";
import { SidebarSkeleton } from "./SidebarSkeleton";
import satoloc_insight_logo from "@/public/images/satoloc_insight_logo.png";
import React from "react";
import { Loader2 } from "lucide-react";
import { useSidebarPrefetch } from "@/hooks/useRoutePrefetch";

interface NavItem {
  title: string;
  icon: React.ReactNode;
  href: string;
  subItems?: NavItem[];
  onClick?: () => void;
  disabled?: boolean;
  badge?: string;
}

const userMainNavItems = [
  {
    title: "Localization",
    icon: <Languages className="h-4 w-4" />,
    href: "/dashboard",
  },
  {
    title: "SEO Analytics",
    icon: <LineChart className="h-4 w-4" />,
    href: "/seo-insights",
  },
  {
    title: "Api Integration",
    icon: <ArrowRightLeft className="h-4 w-4" />,
    href: "/content",
  },
  {
    title: "Custom Content",
    icon: <SquarePen className="h-4 w-4" />,
    href: "/custom-content",
  },
] as const;

const adminMainNavItems = [
  {
    title: "Admin Panel",
    icon: <Shield className="h-4 w-4" />,
    href: "/admin-dashboard",
    badge: "Admin",
  },
  {
    title: "User Management",
    icon: <Users className="h-4 w-4" />,
    href: "/admin-dashboard/user-management",
  },
  ...userMainNavItems,
] as const;

interface NavItemProps {
  item: NavItem;
  selected: string | null;
  openMenus: Record<string, boolean>;
  isCollapsed: boolean;
  navigatingTo: string | null;
  onToggleMenu: (title: string) => void;
  onNavigate: (href: string) => void;
  onHover: (href: string) => () => void;
}

const NavItemComponent = React.memo(
  ({
    item,
    selected,
    openMenus,
    isCollapsed,
    navigatingTo,
    onToggleMenu,
    onNavigate,
    onHover,
  }: NavItemProps) => {
    const normalizePath = (path: string | null) =>
      path ? path.replace(/\/+$/, "") : "";
    const normalizedSelected = normalizePath(selected);
    const normalizedItemHref = normalizePath(item.href);
    const isActive = normalizedSelected === normalizedItemHref;
    const isNavigating = navigatingTo === item.href;
    const hasSubItems = item.subItems && item.subItems.length > 0;
    const isMenuOpen = openMenus[item.title];

    if (hasSubItems) {
      return (
        <Collapsible
          key={item.title}
          open={isMenuOpen && !isCollapsed}
          onOpenChange={() => onToggleMenu(item.title)}
        >
          <SidebarMenuItem>
            <CollapsibleTrigger asChild>
              <SidebarMenuButton
                tooltip={item.title}
                className="w-full justify-between"
                disabled={item.disabled}
              >
                <div className="flex items-center gap-2">
                  {item.icon}
                  <span>{item.title}</span>
                  {item.badge && (
                    <span className="ml-auto text-xs bg-orange-500 text-white px-1.5 py-0.5 rounded-full">
                      {item.badge}
                    </span>
                  )}
                </div>
                {!isCollapsed && (
                  <ChevronDown
                    className={cn(
                      "h-4 w-4 transition-transform duration-200",
                      isMenuOpen && "rotate-180"
                    )}
                  />
                )}
              </SidebarMenuButton>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarMenuSub>
                {item.subItems?.map((subItem) => {
                  const normalizedSubItemHref = normalizePath(subItem.href);
                  const isSubItemActive =
                    normalizedSelected === normalizedSubItemHref;

                  return (
                    <SidebarMenuSubItem key={subItem.title}>
                      <SidebarMenuSubButton asChild isActive={isSubItemActive}>
                        <div
                          onClick={() =>
                            !subItem.disabled && onNavigate(subItem.href)
                          }
                          onMouseEnter={onHover(subItem.href)}
                          className={`w-full flex items-center gap-2 cursor-pointer ${
                            subItem.disabled || navigatingTo === subItem.href
                              ? "opacity-50 cursor-not-allowed"
                              : ""
                          }`}
                        >
                          {navigatingTo === subItem.href ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            subItem.icon
                          )}
                          <span>{subItem.title}</span>
                          {subItem.disabled && (
                            <span className="text-xs text-muted-foreground ml-auto">
                              Soon
                            </span>
                          )}
                        </div>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  );
                })}
              </SidebarMenuSub>
            </CollapsibleContent>
          </SidebarMenuItem>
        </Collapsible>
      );
    }

    return (
      <SidebarMenuItem key={item.title}>
        <SidebarMenuButton
          tooltip={item.title}
          isActive={isActive}
          onClick={() => !item.disabled && onNavigate(item.href)}
          onMouseEnter={onHover(item.href)}
          disabled={item.disabled || isNavigating}
          className="w-full flex items-center gap-2"
        >
          {isNavigating ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            item.icon
          )}
          <span>{item.title}</span>
          {item.badge && (
            <span className="ml-auto text-xs bg-orange-500 text-white px-1.5 py-0.5 rounded-full">
              {item.badge}
            </span>
          )}
          {item.disabled && (
            <span className="text-xs text-muted-foreground ml-auto">
              Coming soon
            </span>
          )}
        </SidebarMenuButton>
      </SidebarMenuItem>
    );
  }
);
NavItemComponent.displayName = "NavItemComponent";

interface AppSidebarProps {
  className?: string;
  isAdmin?: boolean;
}

export default function AppSidebar({ className, isAdmin }: AppSidebarProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { data: session } = useSession();
  const theme = useTheme();
  const [selected, setSelected] = useState<string | null>(pathname);
  const [mounted, setMounted] = useState<boolean>(false);
  const [openMenus, setOpenMenus] = useState<Record<string, boolean>>({});
  const [navigatingTo, setNavigatingTo] = useState<string | null>(null);
  const { state } = useSidebar();
  const { handleHover } = useSidebarPrefetch();

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    setSelected(pathname);
  }, [pathname, session]);

  const handleLogout = useCallback(() => {
    router.push("/goodbye");
  }, [router]);

  const handleLogin = useCallback(() => {
    router.push("/login");
  }, [router]);

  const handleNavigate = useCallback(
    (href: string) => {
      // Show loading state immediately
      setNavigatingTo(href);

      // Add a small delay to ensure the loading state is visible
      setTimeout(() => {
        router.push(href);

        // Clear loading state after navigation attempt
        setTimeout(() => {
          setNavigatingTo(null);
        }, 1000);
      }, 50);
    },
    [router]
  );

  const toggleMenu = useCallback(
    (menuTitle: string) => {
      if (state === "collapsed") return;
      setOpenMenus((prev) => ({
        ...prev,
        [menuTitle]: !prev[menuTitle],
      }));
    },
    [state]
  );

  const mainNavItems = isAdmin ? adminMainNavItems : userMainNavItems;

  if (!mounted) {
    return (
      <SidebarSkeleton
        className={className}
        isCollapsed={state === "collapsed"}
      />
    );
  }

  const normalizePath = (path: string | null) =>
    path ? path.replace(/\/+$/, "") : "";

  const getUserInitials = () => {
    if (!session?.user?.name) return "U";
    return session.user.name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Sidebar collapsible="icon" className={className}>
      {/* Header */}
      <SidebarHeader className="relative z-40 bg-background">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary/10">
                <Image
                  src={satoloc_insight_logo}
                  alt="Satoloc Logo"
                  width={32}
                  height={32}
                  className="rounded-lg"
                />
              </div>
              <button
                onClick={() => router.push("/")}
                className="grid flex-1 text-left text-sm leading-tight"
              >
                <span className="truncate font-semibold text-[#1279b4]">
                  SatoLOC Insight
                </span>
                <span className="truncate text-xs text-muted-foreground">
                  Platform
                </span>
              </button>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      {/* Content */}
      <SidebarContent className="bg-background">
        {/* User Profile Section */}
        {session && (
          <SidebarGroup>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton
                  className="data-[state=open]:bg-primary/5 border border-transparent data-[state=open]:border-primary/10"
                  size="lg"
                >
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={session.user?.image || ""} />
                    <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                      {getUserInitials()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-medium">
                      {session.user?.name || "User"}
                    </span>
                    <span className="truncate text-xs text-muted-foreground">
                      {session.user?.email}
                    </span>
                  </div>
                  {isAdmin && <Crown className="h-4 w-4 text-orange-500" />}
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroup>
        )}

        <SidebarSeparator />

        {/* Main Navigation */}
        {session && (
          <SidebarGroup>
            <SidebarGroupLabel>Navigation</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {mainNavItems.map((item) => (
                  <NavItemComponent
                    key={item.title}
                    item={item}
                    selected={selected}
                    openMenus={openMenus}
                    isCollapsed={state === "collapsed"}
                    navigatingTo={navigatingTo}
                    onToggleMenu={toggleMenu}
                    onNavigate={handleNavigate}
                    onHover={handleHover}
                  />
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )}
      </SidebarContent>

      {/* Footer */}
      <SidebarFooter className="bg-background">
        <SidebarMenu>
          {session ? (
            <>
              {/* Settings */}
              <SidebarMenuItem>
                <SidebarMenuButton
                  tooltip="Settings"
                  onClick={() => router.push("/user-settings")}
                >
                  <Settings className="h-4 w-4" />
                  <span>Settings</span>
                </SidebarMenuButton>
              </SidebarMenuItem>

              {/* Logout */}
              <SidebarMenuItem>
                <SidebarMenuButton
                  tooltip="Logout"
                  onClick={handleLogout}
                  className="hover:bg-destructive/10 hover:text-destructive data-[state=open]:hover:bg-destructive/10 data-[state=open]:hover:text-destructive"
                >
                  <LogOut className="h-4 w-4" />
                  <span>Logout</span>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </>
          ) : (
            /* Login for non-authenticated users */
            <SidebarMenuItem>
              <SidebarMenuButton tooltip="Login" onClick={handleLogin}>
                <LogIn className="h-4 w-4" />
                <span>Login</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          )}
        </SidebarMenu>
      </SidebarFooter>

      <SidebarRail />
    </Sidebar>
  );
}
