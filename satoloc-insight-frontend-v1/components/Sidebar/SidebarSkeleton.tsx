import { Skeleton } from "@/components/ui/skeleton";
import {
  <PERSON><PERSON>,
  <PERSON>barContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuSkeleton,
  SidebarRail,
  SidebarSeparator,
} from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";

interface SidebarSkeletonProps {
  className?: string;
  isCollapsed?: boolean;
}

export function SidebarSkeleton({
  className,
  isCollapsed = false,
}: SidebarSkeletonProps) {
  const skeletonCount = 5;

  return (
    <div
      className={cn(
        "flex h-full flex-col border-r bg-background",
        isCollapsed ? "w-14" : "w-64",
        className
      )}
    >
      <div className="p-3">
        <div className="flex items-center gap-2">
          <Skeleton className="h-8 w-8 rounded-lg" />
          {!isCollapsed && <Skeleton className="h-6 w-32" />}
        </div>
      </div>
      <div className="flex-1 space-y-4 p-3">
        {[...Array(skeletonCount)].map((_, i) => (
          <div key={i} className="flex items-center gap-2">
            <Skeleton className="h-6 w-6 rounded-md" />
            {!isCollapsed && <Skeleton className="h-6 w-full" />}
          </div>
        ))}
      </div>
      <div className="p-3">
        <div className="flex items-center gap-2">
          <Skeleton className="h-8 w-8 rounded-full" />
          {!isCollapsed && (
            <div className="flex-1">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="mt-1 h-3 w-32" />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
