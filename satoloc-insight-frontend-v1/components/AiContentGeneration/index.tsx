"use client";
import React, { useState, useEffect, use<PERSON><PERSON>back, useRef } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  FileText,
  Rocket,
  HelpCircle,
  Tag,
  Edit2,
  Save,
  MessageSquarePlus,
  Loader2,
  Copy,
  Check,
  Code,
  FileJson,
  History,
  X,
  Download,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import type { ContentGenerationData } from "@/types/content-generation";
import { CustomPromptModal } from "./CustomPromptModal";
import { useToast } from "@/hooks/use-toast";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useGenerateContent } from "@/internal-api/ai/content-generation";
import { useMyAnalyses } from "@/internal-api/seo/advance-seo";

interface AiContentGenerationProps {
  data?: ContentGenerationData;
  initialKeywords?: string[];
  competitorName?: string;
}

// Sample data
const dummyData: ContentGenerationData = {
  estimatedTraffic: "",
  potentialVisitors: "",
  contentTypes: [
    { id: "blog", label: "Blog Post", icon: "blog" },
    { id: "learn", label: "Learn Page", icon: "learn" },
    { id: "faq", label: "FAQ Page", icon: "faq" },
    { id: "meta", label: "Meta Content", icon: "meta" },
  ],
  targetKeywords: [
    { keyword: "crypto trading", monthlyVolume: "5.2K", potential: "+320" },
    { keyword: "blockchain tech", monthlyVolume: "5.2K", potential: "+320" },
    { keyword: "trump token", monthlyVolume: "5.2K", potential: "+320" },
  ],
  performancePrediction: {
    potentialTraffic: {
      range: "320-450",
      visitors: "monthly visitors",
      confidence: "High confidence",
    },
    rankingPotential: {
      range: "Top 3-5",
      target: "for target keywords",
      probability: "85% probable",
    },
  },
  timeline: [
    {
      week: "Week 1",
      description: "Indexing & Initial Rankings",
      visitors: "",
    },
    { week: "Week 2", description: "~150 visitors", visitors: "150" },
    { week: "Week 3-4", description: "220-450 visitors", visitors: "450" },
  ],
  successFactors:
    "Content quality, internal linking, and social shares can significantly impact these predictions.",
};

// Define the interface for the prompt history
interface PromptHistoryItem {
  id: string;
  keyword: string;
  contentType: string;
  prompt: string;
  timestamp: Date;
}

// Add interface for saved content
interface SavedContent extends PromptHistoryItem {
  content: string;
}

export function AiContentGeneration({
  data = dummyData,
  initialKeywords = [],
  competitorName = "",
}: AiContentGenerationProps) {
  const { toast } = useToast();
  const [isCustomPromptOpen, setIsCustomPromptOpen] = useState(false);
  const [selectedKeyword, setSelectedKeyword] = useState<string>("");
  const [selectedContentType, setSelectedContentType] =
    useState<string>("blog");
  const [customPrompt, setCustomPrompt] = useState<string>("");
  const [generatedContent, setGeneratedContent] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [wordCount, setWordCount] = useState<number>(0);
  const [readingTime, setReadingTime] = useState<number>(0);
  const [promptHistory, setPromptHistory] = useState<PromptHistoryItem[]>([]);
  const [savedContents, setSavedContents] = useState<SavedContent[]>([]);
  const [showHistory, setShowHistory] = useState<boolean>(false);
  const [contentFormat, setContentFormat] = useState<"markdown" | "json">(
    "markdown"
  );
  const [isCopied, setIsCopied] = useState<boolean>(false);
  const [historyTab, setHistoryTab] = useState<"prompts" | "saved">("prompts");
  const [keywordsWithMetrics, setKeywordsWithMetrics] = useState<
    Array<{
      keyword: string;
      potential: string;
      volume: string;
    }>
  >([]);

  // Fetch user's website analyses to get competitor data
  const { data: userWebsiteData, isLoading: isUserWebsiteLoading } =
    useMyAnalyses();

  // Refs to track initialization
  const isInitialized = useRef(false);
  const keywordsInitialized = useRef(false);

  // Get the generate content mutation
  const generateContentMutation = useGenerateContent();

  // Load saved content and history from localStorage on mount
  useEffect(() => {
    const savedHistoryString = localStorage.getItem("promptHistory");
    const savedContentsString = localStorage.getItem("savedContents");

    if (savedHistoryString) {
      try {
        const savedHistory = JSON.parse(savedHistoryString);
        // Convert string dates back to Date objects
        const parsedHistory = savedHistory.map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp),
        }));
        setPromptHistory(parsedHistory);
      } catch (e) {
        console.error("Error parsing prompt history:", e);
      }
    }

    if (savedContentsString) {
      try {
        const savedContentItems = JSON.parse(savedContentsString);
        // Convert string dates back to Date objects
        const parsedContents = savedContentItems.map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp),
        }));
        setSavedContents(parsedContents);
      } catch (e) {
        console.error("Error parsing saved contents:", e);
      }
    }
  }, []);

  // Save history to localStorage when it changes
  useEffect(() => {
    if (promptHistory.length > 0) {
      localStorage.setItem("promptHistory", JSON.stringify(promptHistory));
    }
  }, [promptHistory]);

  // Save contents to localStorage when they change
  useEffect(() => {
    if (savedContents.length > 0) {
      localStorage.setItem("savedContents", JSON.stringify(savedContents));
    }
  }, [savedContents]);

  // Extract competitor keywords from userWebsiteData
  useEffect(() => {
    if (keywordsInitialized.current) return;

    if (
      userWebsiteData &&
      Array.isArray(userWebsiteData) &&
      userWebsiteData.length > 0
    ) {
      // Extract competitor keywords from the website data
      const competitorKeywords: Array<{
        keyword: string;
        potential: string;
        volume: string;
      }> = [];

      // Process each website in the user's analyses
      userWebsiteData.forEach((website) => {
        // Check if the website has competitor data
        if (website.competitor_analysis) {
          // Process competitor A keywords
          if (
            website.competitor_analysis.competitor_a &&
            website.competitor_analysis.competitor_a.keywords &&
            Array.isArray(website.competitor_analysis.competitor_a.keywords)
          ) {
            website.competitor_analysis.competitor_a.keywords.forEach(
              (keyword: any) => {
                if (typeof keyword === "object" && keyword.keyword) {
                  competitorKeywords.push({
                    keyword: keyword.keyword,
                    potential: keyword.traffic ? `+${keyword.traffic}` : "+0",
                    volume: keyword.volume ? `${keyword.volume}` : "N/A",
                  });
                }
              }
            );
          }

          // Process competitor B keywords
          if (
            website.competitor_analysis.competitor_b &&
            website.competitor_analysis.competitor_b.keywords &&
            Array.isArray(website.competitor_analysis.competitor_b.keywords)
          ) {
            website.competitor_analysis.competitor_b.keywords.forEach(
              (keyword: any) => {
                if (typeof keyword === "object" && keyword.keyword) {
                  // Check if this keyword is already in the array
                  const existingKeyword = competitorKeywords.find(
                    (k) => k.keyword === keyword.keyword
                  );
                  if (!existingKeyword) {
                    competitorKeywords.push({
                      keyword: keyword.keyword,
                      potential: keyword.traffic ? `+${keyword.traffic}` : "+0",
                      volume: keyword.volume ? `${keyword.volume}` : "N/A",
                    });
                  }
                }
              }
            );
          }
        }
      });

      // If we have competitor keywords, use them
      if (competitorKeywords.length > 0) {
        setKeywordsWithMetrics(competitorKeywords);

        // Set the initial selected keyword
        if (!selectedKeyword && competitorKeywords.length > 0) {
          setSelectedKeyword(competitorKeywords[0].keyword);
        }

        // Mark as initialized
        keywordsInitialized.current = true;
        return;
      }
    }

    // Fallback to initialKeywords and data.targetKeywords if no competitor data
    if (
      !keywordsInitialized.current &&
      (initialKeywords.length > 0 || data.targetKeywords.length > 0)
    ) {
      // Combine keywords from both sources
      const combinedKeywords: string[] = [];

      // Add initialKeywords
      initialKeywords.forEach((keyword) => {
        if (!combinedKeywords.includes(keyword)) {
          combinedKeywords.push(keyword);
        }
      });

      // Add keywords from data.targetKeywords
      data.targetKeywords.forEach((item) => {
        if (!combinedKeywords.includes(item.keyword)) {
          combinedKeywords.push(item.keyword);
        }
      });

      // Create metrics for all keywords
      const keywordsWithMetricsData = combinedKeywords.map((keyword) => {
        // Check if the keyword exists in data.targetKeywords
        const existingKeyword = data.targetKeywords.find(
          (k) => k.keyword === keyword
        );

        if (existingKeyword) {
          return {
            keyword,
            potential: existingKeyword.potential,
            volume: existingKeyword.monthlyVolume,
          };
        } else {
          // Generate random metrics for new keywords
          return {
            keyword,
            potential: `+${Math.floor(Math.random() * 500)}`,
            volume: `${Math.floor(Math.random() * 10) + 1}.${Math.floor(Math.random() * 10)}K`,
          };
        }
      });

      // Update state
      setKeywordsWithMetrics(keywordsWithMetricsData);

      // Set the initial selected keyword if we have keywords
      if (combinedKeywords.length > 0 && !selectedKeyword) {
        setSelectedKeyword(combinedKeywords[0]);
      }

      // Mark as initialized
      keywordsInitialized.current = true;
    }
  }, [userWebsiteData, data.targetKeywords, initialKeywords, selectedKeyword]);

  // Set up the initial prompt once we have a selected keyword
  useEffect(() => {
    if (!isInitialized.current && selectedKeyword) {
      updatePromptForKeywordAndType(selectedKeyword, selectedContentType);
      isInitialized.current = true;
    }
  }, [selectedKeyword]);

  // Function to update prompt when keyword or content type changes
  const updatePromptForKeywordAndType = (
    keyword: string,
    contentType: string
  ) => {
    if (!keyword) return;

    // Detect language (simple check for Turkish characters)
    const isTurkish =
      /[çğıöşüÇĞİÖŞÜ]/.test(keyword) ||
      keyword.toLowerCase().includes("türk") ||
      keyword.toLowerCase().includes("türkiye");

    // Language instruction to add to the prompt
    const languageInstruction = isTurkish
      ? "Write this content in Turkish language."
      : "Write this content in English language.";

    let prompt = "";

    switch (contentType) {
      case "blog":
        prompt = `Write a comprehensive, SEO-optimized blog post about "${keyword}". 
Include an engaging introduction, at least 3 main sections with subheadings, and a conclusion. 
Use a conversational yet authoritative tone, include relevant statistics, and address common questions.
${competitorName ? `This content should outperform ${competitorName}'s content on this topic.` : ""}
${languageInstruction}`;
        break;
      case "learn":
        prompt = `Create an educational "Learn" page about "${keyword}" that explains the concept from beginner to advanced level.
Include definitions, how-to sections, best practices, and visual descriptions.
Structure with clear headings and use an educational, helpful tone.
${competitorName ? `This content should be more comprehensive than ${competitorName}'s educational content.` : ""}
${languageInstruction}`;
        break;
      case "faq":
        prompt = `Generate a comprehensive FAQ page about "${keyword}" with at least 10 common questions and detailed answers.
Include questions about basics, advanced topics, misconceptions, and practical applications.
Use a clear, direct tone and organize from basic to advanced questions.
${competitorName ? `Address questions that ${competitorName}'s content fails to answer.` : ""}
${languageInstruction}`;
        break;
      case "meta":
        prompt = `Create meta content for "${keyword}" including:
1. SEO title (under 60 characters)
2. Meta description (under 160 characters)
3. 5 focus keywords
4. Social media sharing description
5. Page heading suggestions
${competitorName ? `Optimize to outrank ${competitorName} for this keyword.` : ""}
${languageInstruction}`;
        break;
      default:
        prompt = `Write high-quality content about "${keyword}" that is SEO-optimized and engaging for readers. ${languageInstruction}`;
    }

    setCustomPrompt(prompt);
  };

  // Handle keyword selection
  const handleKeywordSelect = (keyword: string) => {
    setSelectedKeyword(keyword);
    updatePromptForKeywordAndType(keyword, selectedContentType);
  };

  // Handle content type selection
  const handleContentTypeSelect = (contentType: string) => {
    setSelectedContentType(contentType);
    updatePromptForKeywordAndType(selectedKeyword, contentType);
  };

  // Function to generate content
  const generateContent = async () => {
    if (!selectedKeyword) {
      toast({
        title: "No keyword selected",
        description: "Please select a keyword to generate content.",
        variant: "destructive",
      });
      return;
    }

    // Save to prompt history
    const newHistoryItem: PromptHistoryItem = {
      id: Date.now().toString(),
      keyword: selectedKeyword,
      contentType: selectedContentType,
      prompt: customPrompt,
      timestamp: new Date(),
    };

    setPromptHistory((prev) => [newHistoryItem, ...prev]);

    try {
      setIsLoading(true);

      // Call the mutation
      const result = await generateContentMutation.mutateAsync({
        keyword: selectedKeyword,
        contentType: selectedContentType,
        prompt: customPrompt,
      });

      setGeneratedContent(result.content);

      // Calculate word count and reading time
      const words = result.content.split(/\s+/).filter(Boolean).length;
      setWordCount(words);
      setReadingTime(Math.ceil(words / 200)); // Assuming 200 words per minute reading speed

      toast({
        title: "Content generated successfully",
        description: `Generated ${words} words of content for "${selectedKeyword}"`,
      });
    } catch (error: any) {
      console.error("Error generating content:", error);

      // Extract the error message from the error object
      let errorMessage =
        "There was an error generating content. Please try again.";

      if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Error generating content",
        description: errorMessage,
        variant: "destructive",
      });

      // For demo purposes, generate mock content if API fails
      const mockContent = `# ${selectedKeyword}: A Comprehensive Guide\n\n## Introduction\nIn this article, we'll explore everything you need to know about ${selectedKeyword}. This topic has gained significant attention in recent years, and understanding it can provide valuable insights for both beginners and experts.\n\n## What is ${selectedKeyword}?\n${selectedKeyword} refers to a specialized area within the broader context of digital technology and finance. It encompasses various aspects including technical implementation, market dynamics, and regulatory considerations.\n\n## Key Benefits\n1. **Enhanced Security**: Provides robust protection mechanisms\n2. **Efficiency**: Streamlines processes and reduces overhead\n3. **Accessibility**: Makes complex systems more user-friendly\n4. **Innovation**: Opens new possibilities for development\n\n## Best Practices\nWhen working with ${selectedKeyword}, consider these best practices:\n- Always verify sources and information\n- Stay updated with the latest developments\n- Implement proper security measures\n- Engage with the community for insights\n\n## Conclusion\n${selectedKeyword} continues to evolve and shape various industries. By staying informed and applying the principles discussed in this guide, you can effectively navigate this exciting field.`;

      setGeneratedContent(mockContent);
      const words = mockContent.split(/\s+/).filter(Boolean).length;
      setWordCount(words);
      setReadingTime(Math.ceil(words / 200));
    } finally {
      setIsLoading(false);
    }
  };

  // Function to load a prompt from history
  const loadPromptFromHistory = (historyItem: PromptHistoryItem) => {
    setSelectedKeyword(historyItem.keyword);
    setSelectedContentType(historyItem.contentType);
    setCustomPrompt(historyItem.prompt);
    setShowHistory(false);
  };

  // Function to convert markdown to JSON
  const convertToJson = (markdown: string): string => {
    try {
      // Simple conversion for demonstration
      const lines = markdown.split("\n");
      let title = "";
      const sections: { heading: string; content: string }[] = [];
      let currentSection: { heading: string; content: string } | null = null;

      lines.forEach((line) => {
        if (line.startsWith("# ")) {
          title = line.substring(2).trim();
        } else if (line.startsWith("## ")) {
          // Save previous section if exists
          if (currentSection) {
            sections.push(currentSection);
          }
          // Start new section
          currentSection = {
            heading: line.substring(3).trim(),
            content: "",
          };
        } else if (currentSection) {
          // Add to current section content
          currentSection.content += line + "\n";
        }
      });

      // Add the last section
      if (currentSection) {
        sections.push(currentSection);
      }

      const jsonContent = {
        title,
        sections,
        metadata: {
          keyword: selectedKeyword,
          contentType: selectedContentType,
          wordCount,
          readingTime,
          generatedAt: new Date().toISOString(),
        },
      };

      return JSON.stringify(jsonContent, null, 2);
    } catch (error) {
      console.error("Error converting to JSON:", error);
      return '{\n  "error": "Failed to convert to JSON"\n}';
    }
  };

  // Function to copy content to clipboard
  const copyToClipboard = () => {
    const contentToCopy =
      contentFormat === "json"
        ? convertToJson(generatedContent)
        : generatedContent;

    navigator.clipboard.writeText(contentToCopy).then(
      () => {
        setIsCopied(true);
        toast({
          title: "Content copied",
          description: "Content has been copied to clipboard",
        });
        setTimeout(() => setIsCopied(false), 2000);
      },
      (err) => {
        console.error("Could not copy text: ", err);
        toast({
          title: "Copy failed",
          description: "Failed to copy content to clipboard",
          variant: "destructive",
        });
      }
    );
  };

  // Function to download content
  const downloadContent = () => {
    const contentToDownload =
      contentFormat === "json"
        ? convertToJson(generatedContent)
        : generatedContent;

    const fileExtension = contentFormat === "json" ? "json" : "md";
    const fileName = `${selectedKeyword.replace(/\s+/g, "-").toLowerCase()}-${new Date().toISOString().split("T")[0]}.${fileExtension}`;

    const blob = new Blob([contentToDownload], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Content downloaded",
      description: `Content saved as ${fileName}`,
    });
  };

  // Function to save content
  const saveContent = () => {
    if (!generatedContent) {
      toast({
        title: "No content to save",
        description: "Please generate content first before saving.",
        variant: "destructive",
      });
      return;
    }

    const newSavedContent: SavedContent = {
      id: Date.now().toString(),
      keyword: selectedKeyword,
      contentType: selectedContentType,
      prompt: customPrompt,
      timestamp: new Date(),
      content: generatedContent,
    };

    setSavedContents((prev) => [newSavedContent, ...prev]);

    toast({
      title: "Content saved",
      description: `Content for "${selectedKeyword}" has been saved successfully.`,
    });
  };

  // Function to delete saved content
  const deleteSavedContent = (id: string) => {
    setSavedContents((prev) => prev.filter((item) => item.id !== id));

    toast({
      title: "Content deleted",
      description: "The saved content has been deleted.",
    });
  };

  // Function to format the date for display
  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Function to get the content type icon
  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case "blog":
        return <FileText className="h-6 w-6" />;
      case "learn":
        return <Rocket className="h-6 w-6" />;
      case "faq":
        return <HelpCircle className="h-6 w-6" />;
      case "meta":
        return <Tag className="h-6 w-6" />;
      default:
        return null;
    }
  };

  // Function to handle custom prompt
  const handleCustomPrompt = (prompt: string) => {
    setCustomPrompt(prompt);
    setIsCustomPromptOpen(false);
    // Generate content with the custom prompt
    generateContent();
  };

  return (
    <div className="flex">
      {/* Main Content */}
      <Card
        className={`flex-1 space-y-4 sm:space-y-6 transition-all ${showHistory ? "mr-80" : ""} shadow-none rounded-md p-4`}
      >
        <div className="flex flex-col space-y-2 bg-muted/50 rounded-md shadow-none">
          {/* <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground">
                {data.estimatedTraffic}
              </span>
              <span className="text-[#1279b4] font-medium">
                {data.potentialVisitors}
              </span>
            </div>
          </div> */}

          <Card className="text-sm text-muted-foreground dark:text-white bg-white dark:bg-black/50 p-3 rounded-md shadow-none">
            <p>
              Create SEO-optimized content with our AI assistant. Select a{" "}
              <strong className="text-[#1279b4]">content type</strong> and{" "}
              <strong className="text-[#1279b4]">target keyword</strong>, then
              generate high-quality content tailored to your needs. You can
              customize the prompt, save your work, and export in various
              formats.
            </p>
            <ul className="mt-2 space-y-1 list-disc list-inside">
              <li>
                <span className="text-[#1279b4] font-medium">Blog Post</span> -
                Comprehensive articles with proper structure and SEO
                optimization
              </li>
              <li>
                <span className="text-[#1279b4] font-medium">Learn Page</span> -
                Educational content with clear explanations and examples
              </li>
              <li>
                <span className="text-[#1279b4] font-medium">FAQ Page</span> -
                Question and answer format addressing common user queries
              </li>
              <li>
                <span className="text-[#1279b4] font-medium">Meta Content</span>{" "}
                - SEO titles, descriptions, and structured data for better
                rankings
              </li>
            </ul>
          </Card>
        </div>

        <div className="grid grid-cols-2 gap-3 sm:grid-cols-4 sm:gap-4">
          {data.contentTypes.map((type) => (
            <button
              key={type.id}
              onClick={() => handleContentTypeSelect(type.id)}
              className={`group text-left  focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#1279b4] focus-visible:ring-offset-2 ${
                selectedContentType === type.id
                  ? "text-white"
                  : "text-[#1279b4]"
              }`}
            >
              <Card
                className={`group-hover:shadow-none  rounded-md group-hover:scale-[1.02] transition-all border-[#1279b4]/20 group-hover:border-[#1279b4]/30 shadow-none ${
                  selectedContentType === type.id
                    ? "bg-[#1279b4]"
                    : "bg-white group-hover:bg-[#1279b4]/5"
                }`}
              >
                <CardContent className="p-4 flex items-center justify-center gap-2">
                  <div
                    className={`transition-transform group-hover:scale-110 group-active:scale-95 ${
                      selectedContentType === type.id
                        ? "text-white"
                        : "text-[#1279b4]"
                    }`}
                  >
                    {getContentTypeIcon(type.icon)}
                  </div>
                  <div
                    className={`text-sm font-medium ${
                      selectedContentType === type.id
                        ? "text-white"
                        : "text-[#1279b4] group-hover:text-[#1279b4]/80"
                    }`}
                  >
                    {type.label}
                  </div>
                </CardContent>
              </Card>
            </button>
          ))}
        </div>

        <div className="grid gap-4 lg:grid-cols-3 lg:gap-6">
          <Card className="lg:col-span-1 shadow-none rounded-md">
            <CardContent className="p-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="font-semibold">Target Keywords</h3>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1 text-[#1279b4] border-[#1279b4]/20 hover:bg-[#1279b4]/5"
                  onClick={() => setShowHistory(!showHistory)}
                >
                  <History className="h-4 w-4" />
                  <span>History</span>
                  {showHistory ? (
                    <ChevronRight className="h-4 w-4" />
                  ) : (
                    <ChevronLeft className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <div className="space-y-4">
                {keywordsWithMetrics.map((keywordData, index) => (
                  <div
                    key={index}
                    className={`p-3 border rounded-md cursor-pointer hover:border-[#1279b4]/60 transition-colors ${selectedKeyword === keywordData.keyword ? "border-[#1279b4] bg-[#1279b4]/5" : ""}`}
                    onClick={() => handleKeywordSelect(keywordData.keyword)}
                  >
                    <div className="flex justify-between items-center">
                      <span className="font-medium">{keywordData.keyword}</span>
                      <span className="text-[#1279b4] text-sm">
                        {keywordData.potential} potential
                      </span>
                    </div>
                    <div className="text-sm text-gray-500 mt-1">
                      Monthly Volume: {keywordData.volume}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="lg:col-span-2 shadow-none rounded-md">
            <CardContent className="p-4">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 space-y-2 sm:space-y-0">
                <h3 className="font-semibold">Content Editor</h3>
                <div className="flex items-center gap-2 flex-wrap sm:flex-nowrap">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCustomPrompt(customPrompt)}
                    className="border-[#1279b4]/20 text-[#1279b4] hover:bg-[#1279b4]/5"
                  >
                    <Edit2 className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={saveContent}
                    className="border-[#1279b4]/20 text-[#1279b4] hover:bg-[#1279b4]/5"
                  >
                    <Save className="h-4 w-4 mr-1" />
                    Save
                  </Button>
                </div>
              </div>

              {/* Prompt textarea */}
              <Textarea
                value={customPrompt}
                onChange={(e) => setCustomPrompt(e.target.value)}
                placeholder="Enter your custom prompt here..."
                className="min-h-[150px] mb-4 border-[#1279b4]/20 focus-visible:ring-[#1279b4] shadow-none"
              />

              {/* Content generation area with fixed height and scrolling */}
              <div className="flex flex-col h-[500px]">
                {/* Format and copy options */}
                <div className="flex justify-between items-center mb-2">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">Format:</span>
                    <Tabs
                      value={contentFormat}
                      onValueChange={(value) =>
                        setContentFormat(value as "markdown" | "json")
                      }
                      className="h-8"
                    >
                      <TabsList className="h-8 bg-[#1279b4]/10">
                        <TabsTrigger
                          value="markdown"
                          className="h-7 px-2 text-xs data-[state=active]:bg-[#1279b4] data-[state=active]:text-white"
                        >
                          Markdown
                        </TabsTrigger>
                        <TabsTrigger
                          value="json"
                          className="h-7 px-2 text-xs data-[state=active]:bg-[#1279b4] data-[state=active]:text-white"
                        >
                          JSON
                        </TabsTrigger>
                      </TabsList>
                    </Tabs>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={copyToClipboard}
                      disabled={!generatedContent || isLoading}
                      className="h-8 px-2 border-[#1279b4]/20 text-[#1279b4] hover:bg-[#1279b4]/5"
                    >
                      {isCopied ? (
                        <Check className="h-4 w-4 mr-1 text-green-500" />
                      ) : (
                        <Copy className="h-4 w-4 mr-1" />
                      )}
                      <span className="text-xs">
                        {isCopied ? "Copied" : "Copy"}
                      </span>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={downloadContent}
                      disabled={!generatedContent || isLoading}
                      className="h-8 px-2 border-[#1279b4]/20 text-[#1279b4] hover:bg-[#1279b4]/5"
                    >
                      <Download className="h-4 w-4 mr-1" />
                      <span className="text-xs">Download</span>
                    </Button>
                  </div>
                </div>

                {/* Generated content with scrollable area */}
                <div className="border border-[#1279b4]/20 rounded-lg p-4 text-muted-foreground flex-grow overflow-y-auto mb-4">
                  {isLoading ? (
                    <div className="flex justify-center items-center h-full">
                      <Loader2 className="h-8 w-8 animate-spin text-[#1279b4]" />
                    </div>
                  ) : generatedContent ? (
                    contentFormat === "markdown" ? (
                      <div className="prose prose-sm max-w-none text-sm">
                        {generatedContent.split("\n").map((line, index) => {
                          if (line.startsWith("# ")) {
                            return (
                              <h1
                                key={index}
                                className="text-xl font-bold mt-4 mb-2 text-[#1279b4]"
                              >
                                {line.substring(2)}
                              </h1>
                            );
                          } else if (line.startsWith("## ")) {
                            return (
                              <h2
                                key={index}
                                className="text-lg font-semibold mt-4 mb-2 text-[#1279b4]/90"
                              >
                                {line.substring(3)}
                              </h2>
                            );
                          } else if (line.startsWith("### ")) {
                            return (
                              <h3
                                key={index}
                                className="text-base font-medium mt-3 mb-2 text-[#1279b4]/80"
                              >
                                {line.substring(4)}
                              </h3>
                            );
                          } else if (line.startsWith("- ")) {
                            return (
                              <li key={index} className="ml-4 text-sm">
                                {line.substring(2)}
                              </li>
                            );
                          } else if (line.match(/^\d+\.\s/)) {
                            return (
                              <li key={index} className="ml-4 text-sm">
                                {line}
                              </li>
                            );
                          } else if (line === "") {
                            return <br key={index} />;
                          } else {
                            return (
                              <p key={index} className="my-2 text-sm">
                                {line}
                              </p>
                            );
                          }
                        })}
                      </div>
                    ) : (
                      <pre className="text-xs font-mono bg-[#1279b4]/5 p-4 rounded overflow-auto h-full">
                        {convertToJson(generatedContent)}
                      </pre>
                    )
                  ) : (
                    <div className="flex items-center justify-center h-full text-gray-400">
                      Generated content will appear here...
                    </div>
                  )}
                </div>

                {/* Controls fixed at the bottom */}
                <div className="flex flex-col sm:flex-row items-center justify-between text-sm text-muted-foreground space-y-2 sm:space-y-0 py-2 border-t border-[#1279b4]/10">
                  <span>Word count: {wordCount}</span>
                  <span>Reading time: {readingTime} min</span>
                  <Button
                    variant="default"
                    className="bg-[#1279b4] hover:bg-[#1279b4]/90 text-white"
                    onClick={generateContent}
                    disabled={isLoading || !selectedKeyword}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Generating...
                      </>
                    ) : generatedContent ? (
                      "Regenerate"
                    ) : (
                      "Generate"
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <CustomPromptModal
          isOpen={isCustomPromptOpen}
          onClose={() => setIsCustomPromptOpen(false)}
          onGenerate={handleCustomPrompt}
        />
      </Card>

      {/* History Sidebar */}
      {showHistory && (
        <div className="fixed right-0 top-0 h-full w-80 bg-white shadow-lg z-50 overflow-y-auto border-l border-[#1279b4]/10">
          <div className="sticky top-0 bg-white z-10 border-b border-[#1279b4]/10">
            <div className="flex justify-between items-center p-4">
              <h3 className="font-semibold text-[#1279b4]">Content History</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowHistory(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <Tabs
              value={historyTab}
              onValueChange={(value) =>
                setHistoryTab(value as "prompts" | "saved")
              }
              className="px-4 pb-2"
            >
              <TabsList className="grid w-full grid-cols-2 bg-[#1279b4]/5">
                <TabsTrigger
                  value="prompts"
                  className="data-[state=active]:bg-[#1279b4] data-[state=active]:text-white"
                >
                  Prompt History
                </TabsTrigger>
                <TabsTrigger
                  value="saved"
                  className="data-[state=active]:bg-[#1279b4] data-[state=active]:text-white"
                >
                  Saved Content
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <div className="p-4">
            {historyTab === "prompts" ? (
              promptHistory.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>No prompt history yet.</p>
                  <p className="text-sm">
                    Generate content to see your history.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {promptHistory.map((item) => (
                    <Card
                      key={item.id}
                      className="overflow-hidden border-[#1279b4]/10 hover:border-[#1279b4]/30 transition-colors"
                    >
                      <CardContent className="p-3">
                        <div className="flex justify-between items-start mb-1">
                          <div className="font-medium truncate mr-2">
                            {item.keyword}
                          </div>
                          <div className="text-xs bg-[#1279b4]/10 text-[#1279b4] px-2 py-0.5 rounded">
                            {item.contentType}
                          </div>
                        </div>
                        <div className="text-xs text-gray-500 mb-2">
                          {formatDate(item.timestamp)}
                        </div>
                        <div className="text-sm text-gray-700 line-clamp-2 mb-2">
                          {item.prompt.substring(0, 100)}...
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full border-[#1279b4]/20 text-[#1279b4] hover:bg-[#1279b4]/5"
                          onClick={() => loadPromptFromHistory(item)}
                        >
                          Use This Prompt
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )
            ) : savedContents.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <p>No saved content yet.</p>
                <p className="text-sm">
                  Save generated content to see it here.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {savedContents.map((item) => (
                  <Card
                    key={item.id}
                    className="overflow-hidden border-[#1279b4]/10 hover:border-[#1279b4]/30 transition-colors"
                  >
                    <CardContent className="p-3">
                      <div className="flex justify-between items-start mb-1">
                        <div className="font-medium truncate mr-2">
                          {item.keyword}
                        </div>
                        <div className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded">
                          Saved
                        </div>
                      </div>
                      <div className="text-xs text-gray-500 mb-2">
                        {formatDate(item.timestamp)}
                      </div>
                      <div className="text-sm text-gray-700 line-clamp-3 mb-2 border-l-2 border-[#1279b4]/20 pl-2">
                        {item.content.substring(0, 150)}...
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex-1 border-[#1279b4]/20 text-[#1279b4] hover:bg-[#1279b4]/5"
                          onClick={() => {
                            setSelectedKeyword(item.keyword);
                            setSelectedContentType(item.contentType);
                            setCustomPrompt(item.prompt);
                            setGeneratedContent(item.content);

                            // Calculate word count and reading time
                            const words = item.content
                              .split(/\s+/)
                              .filter(Boolean).length;
                            setWordCount(words);
                            setReadingTime(Math.ceil(words / 200));
                          }}
                        >
                          Load
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-500 hover:text-red-700 border-red-100 hover:bg-red-50"
                          onClick={() => deleteSavedContent(item.id)}
                        >
                          Delete
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default AiContentGeneration;
