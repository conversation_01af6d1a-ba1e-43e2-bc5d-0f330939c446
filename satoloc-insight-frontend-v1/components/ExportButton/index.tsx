/**
 * Export Button Component
 * Reusable button component for triggering export functionality
 */

"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Download,
  FileText,
  Code,
  FileImage,
  File,
  ChevronDown,
  Settings,
} from "lucide-react";

import { ExportContent, ExportFormat } from "@/lib/exportUtils";
import { getAvailableFormats, exportContent } from "@/lib/exportServices";
import ExportDialog from "@/components/ExportDialog";
import { useToast } from "@/hooks/use-toast";

interface ExportButtonProps {
  content: ExportContent;
  variant?: "default" | "outline" | "ghost";
  size?: "sm" | "default" | "lg";
  showDropdown?: boolean;
  defaultFormat?: ExportFormat;
  className?: string;
  disabled?: boolean;
}

// Format icons mapping
const formatIcons: Record<
  ExportFormat,
  React.ComponentType<{ className?: string }>
> = {
  pdf: FileImage,
  docx: FileText,
  html: Code,
  markdown: FileText,
  txt: File,
  json: Code,
};

export default function ExportButton({
  content,
  variant = "default",
  size = "default",
  showDropdown = true,
  defaultFormat = "pdf",
  className = "",
  disabled = false,
}: ExportButtonProps) {
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [selectedFormat, setSelectedFormat] =
    useState<ExportFormat>(defaultFormat);
  const [isExporting, setIsExporting] = useState(false);

  const { toast } = useToast();
  const availableFormats = getAvailableFormats().filter(
    (f) => f.format !== "docx"
  ); // Hide DOCX for now

  const handleQuickExport = async (format: ExportFormat) => {
    if (!content.content.trim()) {
      toast({
        title: "Export Failed",
        description:
          "No content to export. Please generate or add content first.",
        variant: "destructive",
      });
      return;
    }

    setIsExporting(true);

    try {
      await exportContent(content, {
        format,
        title: content.title || "Untitled",
        author: content.metadata?.author || "Content Creator",
        keywords: content.metadata?.keywords || "",
        includeMetadata: true,
        includeTimestamp: true,
      });

      toast({
        title: "Export Successful",
        description: `Content exported as ${format.toUpperCase()} successfully.`,
        variant: "default",
      });
    } catch (error) {
      console.error("Export failed:", error);
      toast({
        title: "Export Failed",
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleAdvancedExport = () => {
    setShowExportDialog(true);
  };

  if (!showDropdown) {
    // Simple button that opens the export dialog
    return (
      <>
        <Button
          variant={variant}
          size={size}
          onClick={handleAdvancedExport}
          disabled={disabled || !content.content.trim()}
          className={className}
        >
          <Download className="h-4 w-4 mr-2" />
          Export
        </Button>

        <ExportDialog
          open={showExportDialog}
          onOpenChange={setShowExportDialog}
          content={content}
          defaultFormat={selectedFormat}
        />
      </>
    );
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant={variant}
            size={size}
            disabled={disabled || !content.content.trim() || isExporting}
            className={className}
          >
            <Download className="h-4 w-4 mr-2" />
            Export
            <ChevronDown className="h-4 w-4 ml-2" />
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent align="end" className="w-64">
          <DropdownMenuLabel>Quick Export</DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          {availableFormats.map((format) => {
            const Icon = formatIcons[format.format];
            return (
              <DropdownMenuItem
                key={format.format}
                onClick={() => handleQuickExport(format.format)}
                disabled={isExporting}
                className="cursor-pointer p-3 min-h-[60px]"
              >
                <Icon className="h-4 w-4 mr-3 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm">{format.name}</div>
                  <div className="text-xs text-muted-foreground mt-1 leading-tight">
                    {format.description}
                  </div>
                </div>
              </DropdownMenuItem>
            );
          })}
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem
            onClick={handleAdvancedExport}
            disabled={isExporting}
            className="cursor-pointer p-3 min-h-[60px]"
          >
            <Settings className="h-4 w-4 mr-3 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <div className="font-medium text-sm">Advanced Export</div>
              <div className="text-xs text-muted-foreground mt-1 leading-tight">
                Customize export options
              </div>
            </div>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <ExportDialog
        open={showExportDialog}
        onOpenChange={setShowExportDialog}
        content={content}
        defaultFormat={selectedFormat}
      />
    </>
  );
}

// Quick export buttons for specific formats
export function PDFExportButton({
  content,
  ...props
}: Omit<ExportButtonProps, "defaultFormat">) {
  return (
    <ExportButton
      {...props}
      content={content}
      defaultFormat="pdf"
      showDropdown={false}
    />
  );
}

export function HTMLExportButton({
  content,
  ...props
}: Omit<ExportButtonProps, "defaultFormat">) {
  return (
    <ExportButton
      {...props}
      content={content}
      defaultFormat="html"
      showDropdown={false}
    />
  );
}

export function MarkdownExportButton({
  content,
  ...props
}: Omit<ExportButtonProps, "defaultFormat">) {
  return (
    <ExportButton
      {...props}
      content={content}
      defaultFormat="markdown"
      showDropdown={false}
    />
  );
}
