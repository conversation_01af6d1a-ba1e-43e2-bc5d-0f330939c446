import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Doughnut } from "react-chartjs-2";
import { Chart as ChartJS, ArcElement, Toolt<PERSON>, Legend } from "chart.js";
import { useTheme } from "@mui/material/styles";
import { format } from "date-fns";
import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import ChartDataLabels from "chartjs-plugin-datalabels";

ChartJS.register(ArcElement, Tooltip, Legend, ChartDataLabels);

interface LqaScoreProps {
  projectId: number;
  currentReport: any; // need to define a proper type for your report data
}

export default function LqaScore({ projectId, currentReport }: LqaScoreProps) {
  const theme = useTheme();

  const [projectsError, setProjectsError] = useState<string | null>(null);
  //const [isRefreshing, setIsRefreshing] = useState(false);

  const formatMetricValue = (value: number | undefined) => {
    if (!value) return "0";
    if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}k`;
    }
    return value.toString();
  };

  const calculateProgress = (current: number | undefined, total: number) => {
    if (!current) return 0;
    return Math.min((current / total) * 100, 100);
  };

  const chartOptions = {
    cutout: "70%",
    plugins: {
      legend: {
        position: "bottom" as const,
        labels: {
          padding: 20,
          usePointStyle: true,
          pointStyle: "circle",
        },
      },
      tooltip: {
        backgroundColor: theme.palette.background.paper,
        titleColor: theme.palette.text.primary,
        bodyColor: theme.palette.text.secondary,
        borderColor: theme.palette.divider,
        borderWidth: 1,
        padding: 12,
        displayColors: true,
        usePointStyle: true,
        callbacks: {
          label: function (context: any) {
            const value = context.raw;
            const percentage = (
              (value /
                context.dataset.data.reduce(
                  (a: number, b: number) => a + b,
                  0
                )) *
              100
            ).toFixed(1);
            return `${context.label}: ${value} (${percentage}%)`;
          },
        },
      },
      datalabels: {
        color: theme.palette.text.primary,
        font: {
          weight: "bold" as const,
        },
        formatter: (value: number) => (value > 0 ? value : ""),
      },
    },
    maintainAspectRatio: false,
    animation: {
      duration: 1000,
    },
  };

  if (projectsError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{projectsError}</AlertDescription>
      </Alert>
    );
  }

  if (!currentReport || !currentReport.report?.lqa_analysis) {
    return (
      <Card className="bg-background/50 backdrop-blur-sm">
        <CardContent className="min-h-[200px] flex items-center justify-center">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 mx-auto rounded-full bg-primary/5 flex items-center justify-center">
              <AlertCircle className="h-8 w-8 text-primary/40" />
            </div>
            <div>
              <p className="font-medium">No LQA Data Available</p>
              <p className="text-sm text-muted-foreground">
                This report does not contain LQA analysis data
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="bg-background overflow-hidden shadow-none rounded-md">
        <CardHeader>
          <div className="space-y-1.5 flex items-center justify-between">
            <CardTitle className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              Quality Metrics Overview
            </CardTitle>
            <CardDescription className="flex items-center gap-2 text-sm text-muted-foreground">
              <span className="inline-flex items-center gap-1.5 px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">
                Created{" "}
                {format(
                  new Date(currentReport.created_at),
                  "MMM dd, yyyy HH:mm"
                )}
              </span>
            </CardDescription>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            {/* Source Words Card */}
            <Card className="group hover:shadow-md transition-all shadow-none rounded-md">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    Source Words
                  </CardTitle>
                  <div className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                    {formatMetricValue(currentReport.report.word_count)}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mt-1">
                  Total analyzed words
                </p>
                <Progress
                  value={calculateProgress(
                    currentReport.report.lqa_analysis.word_count,
                    10000
                  )}
                  className="mt-4 h-2"
                />
                <p className="text-xs text-muted-foreground mt-2 text-right">
                  {calculateProgress(
                    currentReport.report.lqa_analysis.word_count,
                    10000
                  )}
                  % of 10k words
                </p>
              </CardContent>
            </Card>

            {/* LQA Score Card */}
            <Card className="group hover:shadow-md transition-all shadow-none rounded-md">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    LQA Score
                  </CardTitle>
                  <div className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                    {currentReport.report.metrics.quality_score}%
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mt-1">
                  Overall quality score
                </p>
                <Progress
                  value={currentReport.report.metrics.quality_score}
                  className="mt-4 h-2"
                />
                <p className="text-xs text-muted-foreground mt-2 text-right">
                  Target: 100%
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Charts section */}
          <div className="grid gap-6 md:grid-cols-2 mt-6">
            {/* Severity Chart */}
            <Card className="group hover:shadow-md transition-all shadow-none rounded-md">
              <CardHeader>
                <CardTitle className="text-xl font-semibold">
                  Issues by Severity
                </CardTitle>
                <CardDescription className="text-sm text-muted-foreground">
                  Distribution of issues across severity levels
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] relative">
                  {currentReport.report.lqa_score.severity_data && (
                    <Doughnut
                      data={{
                        labels: currentReport.report.lqa_score.severity_labels,
                        datasets: [
                          {
                            data: currentReport.report.lqa_score.severity_data,
                            backgroundColor: [
                              theme.palette.error.main + "CC",
                              theme.palette.success.main + "CC",
                              theme.palette.warning.main + "CC",
                              theme.palette.text.disabled + "99",
                            ],
                            borderWidth: 2,
                            borderColor: theme.palette.background.paper,
                          },
                        ],
                      }}
                      options={{
                        ...chartOptions,
                        plugins: {
                          ...chartOptions.plugins,
                          legend: {
                            ...chartOptions.plugins.legend,
                            labels: {
                              ...chartOptions.plugins.legend.labels,
                              font: {
                                size: 12,
                                family: "system-ui",
                              },
                              padding: 16,
                            },
                          },
                          datalabels: {
                            ...chartOptions.plugins.datalabels,
                            font: {
                              weight: "bold",
                              size: 14,
                            },
                            color: theme.palette.background.paper,
                            formatter: (value: number) =>
                              value > 0 ? value : "",
                          },
                        },
                      }}
                    />
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Category Chart */}
            <Card className="group hover:shadow-md transition-all shadow-none rounded-md">
              <CardHeader>
                <CardTitle className="text-xl font-semibold">
                  Issues by Category
                </CardTitle>
                <CardDescription className="text-sm text-muted-foreground">
                  Distribution of issues across categories
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] relative">
                  {currentReport.report.lqa_score.category_data && (
                    <Doughnut
                      data={{
                        labels: currentReport.report.lqa_score.category_labels,
                        datasets: [
                          {
                            data: currentReport.report.lqa_score.category_data,
                            backgroundColor: [
                              theme.palette.error.main + "CC",
                              theme.palette.error.dark + "CC",
                              theme.palette.error.light + "CC",
                              theme.palette.success.main + "CC",
                              theme.palette.success.light + "CC",
                              theme.palette.warning.main + "CC",
                              theme.palette.warning.light + "CC",
                            ],
                            borderWidth: 2,
                            borderColor: theme.palette.background.paper,
                          },
                        ],
                      }}
                      options={{
                        ...chartOptions,
                        plugins: {
                          ...chartOptions.plugins,
                          legend: {
                            ...chartOptions.plugins.legend,
                            labels: {
                              ...chartOptions.plugins.legend.labels,
                              font: {
                                size: 12,
                                family: "system-ui",
                              },
                              padding: 16,
                            },
                          },
                          datalabels: {
                            ...chartOptions.plugins.datalabels,
                            font: {
                              weight: "bold",
                              size: 14,
                            },
                            color: theme.palette.background.paper,
                            formatter: (value: number) =>
                              value > 0 ? value : "",
                          },
                        },
                      }}
                    />
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
