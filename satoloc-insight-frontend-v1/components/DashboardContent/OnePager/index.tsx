import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  BrainCircuit,
  CheckCircle2,
  Languages,
  LineChart,
  RepeatIcon,
  Sparkles,
  Target,
} from "lucide-react";
import { useTheme } from "next-themes";
import { cn } from "@/lib/utils";

interface Issue {
  id: number;
  title: string;
  description: string;
  frequency: number;
  impact: "High" | "Medium" | "Low";
  category: string;
}

interface AISolution {
  id: number;
  title: string;
  description: string;
  benefits: string[];
  example: string;
}

interface Metric {
  id: number;
  total_issues: number;
  critical_issues: number;
  improvement_rate: number;
  quality_score: number;
  timestamp: string;
}

interface OnePagerProps {
  projectId: number;
  currentReport: AIReport | null;
}

const impactColors = {
  High: "destructive",
  Medium: "secondary",
  Low: "default",
};

interface Project {
  id: number;
  name: string;
  description: string;
  created_at: string;
  status: string | undefined;
}

type AIReport = {
  id: number;
  created_at: string;
  report: {
    is_single_url: any;
    metrics: {
      total_issues: number;
      quality_score: number;
      critical_issues: number;
      improvement_rate: number;
      industry_benchmark: {
        score: number;
        percentile: number;
      };
    };
    lqa_score: {
      category_data: number[];
      severity_data: number[];
      category_labels: string[];
      severity_labels: string[];
    };
    ai_solutions: {
      id: string;
      title: string;
      description: string;
      benefits: string[];
      example: string;
    }[];
    lqa_analysis: {
      scores: {
        pwpt: number;
        passed: boolean;
        error_details: {
          category: string;
          penalty_points: number;
        }[];
        quality_score: number;
        penalty_points: number;
      };
      word_count: number;
      is_single_url: boolean;
      edit_distances: {
        distance: number;
        original: string;
        severity: string;
        percentage: number;
        suggestion: string;
      }[];
    };
    common_issues: {
      id: string;
      title: string;
      impact: "High" | "Medium" | "Low";
      category: string;
      frequency: number;
      description: string;
    }[];
    error_reports: {
      seo_score: number;
      localization_score: number;
      accessibility_score: number;
    };
    issue_categories: {
      seo_issues: string[];
      localization_issues: string[];
      accessibility_issues: string[];
    };
    issue_severities: {
      seo: string;
      localization: string;
      accessibility: string;
    };

    report_table_errors: {
      total_errors: number;
      error_details: any[];
    };
  };
  scraped_data: number;
};

interface DualUrlData {
  url: string;
  reportDate: string;
  qualityScore: string;
  metrics: {
    qualityScore: number;
    targetScore: number;
    totalIssues: number;
    criticalIssues: number;
    majorIssues: number;
    minorIssues: number;
    improvementRate: number;
    wordsAnalyzed: string;
    keyChallenge: string;
  };
  smartActions: {
    category: string;
    priority: string;
    percentage: number;
    tasks: string[];
  }[];
  implementationProgress: {
    errorReduction: {
      forecast: string;
      lastMonth: number;
      current: number;
    };
    priorityTasks: {
      name: string;
      status: string;
      assistance: string;
    }[];
  };
  strategicInsights: {
    riskPrediction: string;
    aiSmartActions: string[];
  };
  successScoreboard: {
    kpiTracking: {
      name: string;
      status: string;
      action: string | null;
    }[];
  };
}

interface SingleUrlData {
  url: string;
  reportDate: string;
  translationOverview: {
    sourceLanguage: string;
    targetLanguage: string;
    wordsProcessed: string;
    keyObservations: string[];
  };
  smartActions: {
    category: string;
    priority: string;
    tasks: string[];
    action: string;
  }[];
  progressTracking: {
    priorityTasks: {
      name: string;
      status: string;
      action?: string;
    }[];
  };
  successScoreboard: {
    kpiTracking: {
      name: string;
      status: string;
      action: string | null;
    }[];
  };
}

const MetricCard = ({
  title,
  value,
  icon: Icon,
  iconColor,
}: {
  title: string;
  value: string | number;
  icon: any;
  iconColor: string;
}) => (
  <Card className="overflow-hidden shadow-none rounded-md">
    <CardContent className="p-6">
      <div className="flex flex-col h-[120px] justify-between">
        <div className="flex items-center justify-between">
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <div
            className={cn(
              "p-2 rounded-lg bg-background",
              iconColor.replace("text-", "bg-").replace("500", "100")
            )}
          >
            <Icon className={cn("h-4 w-4", iconColor)} />
          </div>
        </div>
        <div className="mt-2">
          <p className="text-2xl font-semibold tracking-tight">
            {typeof value === "string" && value.includes("Target") ? (
              <>
                <span className="text-primary">
                  {value.split("(")[0].trim()}
                </span>
                <span className="text-sm font-normal text-muted-foreground ml-1">
                  ({value.split("(")[1]}
                </span>
              </>
            ) : value.toString().includes("Critical") ? (
              <div className="flex flex-col gap-1">
                <span className="text-xl">
                  {value.toString().split("(")[0].trim()}
                </span>
                <span className="text-sm font-normal text-muted-foreground">
                  {value.toString().split("(")[1]}
                </span>
              </div>
            ) : (
              value
            )}
          </p>
        </div>
      </div>
    </CardContent>
  </Card>
);

const IssueItem = ({
  issueKey,
  issue,
}: {
  issueKey: string;
  issue: AIReport["report"]["common_issues"][0];
}) => (
  <div className="space-y-2">
    <div className="flex items-center justify-between">
      <div className="space-y-1">
        <div className="flex items-center gap-2">
          <span className="font-medium">{issue.title}</span>
          <Badge variant="outline">{issue.category}</Badge>
          <Badge
            variant={
              (impactColors[issue.impact] as
                | "destructive"
                | "secondary"
                | "default"
                | "outline") || "default"
            }
          >
            {issue.impact} Impact
          </Badge>
        </div>
        <p className="text-sm text-muted-foreground">{issue.description}</p>
      </div>
      <span className="text-sm font-medium">{issue.frequency}%</span>
    </div>
    <Progress value={issue.frequency} className="h-2" />
  </div>
);

const SolutionCard = ({
  solutionKey,
  solution,
}: {
  solutionKey: string;
  solution: AIReport["report"]["ai_solutions"][0];
}) => (
  <Card>
    <CardContent className="pt-6">
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Sparkles className="h-4 w-4 text-primary" />
          <h3 className="font-semibold">{solution.title}</h3>
        </div>
        <p className="text-sm text-muted-foreground">{solution.description}</p>
        <div className="space-y-2">
          <p className="text-sm font-medium">Key Benefits:</p>
          <ul className="text-sm space-y-1">
            {solution.benefits.map((benefit: string, index: number) => (
              <li key={index} className="flex items-center gap-2">
                <CheckCircle2 className="h-3 w-3 text-green-500" />
                {benefit}
              </li>
            ))}
          </ul>
        </div>
        <div className="space-y-2">
          <p className="text-sm font-medium">Example:</p>
          <p className="text-sm text-muted-foreground">{solution.example}</p>
        </div>
      </div>
    </CardContent>
  </Card>
);

// Dummy data for testing
const dummyDualUrlData = {
  url: "https://www.satoloc.com/",
  reportDate: "Jan 18, 2025",
  qualityScore: "85%",
  metrics: {
    qualityScore: 85,
    targetScore: 90,
    totalIssues: 15,
    criticalIssues: 0,
    majorIssues: 5,
    minorIssues: 10,
    improvementRate: 80,
    wordsAnalyzed: "1.0k",
    keyChallenge: "Inconsistent Terminology in Fintech content",
  },
  smartActions: [
    {
      category: "Terminology",
      priority: "High",
      percentage: 33,
      tasks: [
        "Standardize fintech-related terms across content",
        "Suggested glossary update: 'Ticaret' → 'Alım-Satım'",
        "Build a terminology management program",
      ],
    },
    {
      category: "Style & Tone",
      priority: "Medium",
      percentage: 27,
      tasks: [
        "Adjust formal vs. informal tone based on content segment",
        "Standardize style across content segments",
      ],
    },
  ],
  implementationProgress: {
    errorReduction: {
      forecast: "30% improvement within next 3 months",
      lastMonth: 20,
      current: 15,
    },
    priorityTasks: [
      {
        name: "Terminology Database Update",
        status: "pending",
        assistance: "Human Assistance Available",
      },
      {
        name: "Style Guide Implementation",
        status: "pending",
        assistance: "Request Expert Review",
      },
      {
        name: "Localization Consistency Check",
        status: "pending",
        assistance: "Consult with a Specialist",
      },
    ],
  },
  strategicInsights: {
    riskPrediction:
      "70% chance of repeated terminology inconsistencies in new content",
    aiSmartActions: [
      "Weekly monitoring for error trends",
      "Proactive recommendations based on content patterns",
      "Custom reports",
    ],
  },
  successScoreboard: {
    kpiTracking: [
      {
        name: "Error Reduction Progress",
        status: "In Progress",
        action: "Continue implementing recommended fixes to maintain progress",
      },
      {
        name: "Glossary Standardization",
        status: "In Progress",
        action:
          "Implement suggested glossary updates to ensure consistency across content",
      },
      {
        name: "LQA Quality Score Goal",
        status: "Target: 90%",
        action: null,
      },
    ],
  },
};

const dummySingleUrlData = {
  url: "https://www.satoloc.com/",
  reportDate: "Jan 18, 2025",
  translationOverview: {
    sourceLanguage: "English",
    targetLanguage: "Turkish",
    wordsProcessed: "1,000",
    keyObservations: [
      "Certain sections require cultural adaptation for better localization.",
    ],
  },
  smartActions: [
    {
      category: "Terminology Consistency",
      priority: "High",
      tasks: [
        "Build a terminology glossary to ensure consistent brand messaging.",
      ],
      action: "Request expert terminology validation from SatoLOC.",
    },
    {
      category: "Readability & Context",
      priority: "Medium",
      tasks: [
        "Verify that translated content aligns with brand tone.",
        "Adjust key sections (e.g., product descriptions) for better engagement.",
      ],
      action:
        "Reach out to the SatoLOC team for professional review for high-traffic pages.",
    },
    {
      category: "Cultural Sensitivity",
      priority: "Low",
      tasks: [
        "Adapt localized expressions for target audience engagement.",
        "Consider region-specific variations for currency and units.",
      ],
      action:
        "Request a cultural consultation for localization accuracy from SatoLOC.",
    },
  ],
  progressTracking: {
    priorityTasks: [
      { name: "Translate Remaining 50 Pages", status: "pending" },
      { name: "Terminology Creation", status: "pending" },
      {
        name: "SEO-Friendly Content Adaptation",
        status: "pending",
        action: "Consult SatoLOC",
      },
    ],
  },
  successScoreboard: {
    kpiTracking: [
      {
        name: "Translation Completion Progress",
        status: "In Progress",
        action: "Continue with pending pages to meet full localization goals.",
      },
      {
        name: "Glossary Development",
        status: "Pending Approval",
        action: "Finalize glossary for consistency.",
      },
      {
        name: "SEO Optimization for Translated Content",
        status: "Target: 100%",
        action: "Optimize translated content for search engines.",
      },
    ],
  },
};

function isDualUrlData(data: SingleUrlData | DualUrlData): data is DualUrlData {
  return "metrics" in data;
}

export default function OnePager({ projectId, currentReport }: OnePagerProps) {
  const theme = useTheme();

  const data = currentReport?.report.is_single_url
    ? (dummySingleUrlData as SingleUrlData)
    : (dummyDualUrlData as DualUrlData);

  if (!currentReport) {
    return (
      <Card className="transition-all duration-200 hover:shadow-md">
        <CardContent className="py-16">
          <div className="flex flex-col items-center justify-center space-y-6 text-center">
            <Languages className="h-16 w-16 opacity-50 animate-pulse text-primary" />
            <div className="space-y-2">
              <p className="text-xl font-semibold text-primary">
                No report data available
              </p>
              <p className="text-sm text-muted-foreground">
                Please generate a report to view insights
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data) return null;

  return (
    <div className="space-y-8">
      {/* Header Card */}
      <Card className="transition-all duration-200 hover:shadow-md shadow-none rounded-md">
        <CardHeader className="py-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <CardTitle className="text-xl font-bold text-primary">
                {currentReport.report.is_single_url
                  ? "Website Translation Single URL"
                  : "AutoLQA (Dual URL)"}
              </CardTitle>
              <CardDescription className="flex flex-wrap items-center gap-3 text-sm text-muted-foreground ">
                <div className="flex items-center gap-2">
                  <span className="font-medium">URL:</span>
                  <span className="text-primary">{data.url}</span>
                </div>
                <span className="text-muted-foreground">•</span>
                <div className="flex items-center gap-2">
                  <span className="font-medium">Report Date:</span>
                  <span>{data.reportDate}</span>
                </div>
                {isDualUrlData(data) && (
                  <>
                    <span className="text-muted-foreground">•</span>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Quality Score:</span>
                      <span className="text-primary font-semibold">
                        {data.metrics.qualityScore}%
                      </span>
                    </div>
                  </>
                )}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
      </Card>

      {isDualUrlData(data) ? (
        <>
          {/* Metrics Card */}
          <Card className="transition-all duration-200 hover:shadow-md shadow-none rounded-md">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-semibold flex items-center gap-2">
                <BrainCircuit className="h-5 w-5 text-primary" />
                Quality Intelligence Dashboard
              </CardTitle>
              <CardDescription>
                Real-time performance metrics and insights
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-4">
                <MetricCard
                  title="Quality Score"
                  value={`${currentReport.report.metrics.quality_score}% (Target: ${data.metrics.targetScore}%)`}
                  icon={Target}
                  iconColor="text-blue-500"
                />
                <MetricCard
                  title="Total Issues"
                  value={`${currentReport.report.metrics.total_issues} (${currentReport.report.metrics.critical_issues} Critical, ${data.metrics.majorIssues} Major, ${data.metrics.minorIssues} Minor)`}
                  icon={AlertTriangle}
                  iconColor="text-yellow-500"
                />
                <MetricCard
                  title="Improvement Rate"
                  value={`${currentReport.report.metrics.improvement_rate}%`}
                  icon={LineChart}
                  iconColor="text-green-500"
                />
                <MetricCard
                  title="Words Analyzed"
                  value={data.metrics.wordsAnalyzed}
                  icon={Sparkles}
                  iconColor="text-purple-500"
                />
              </div>
              <div className="mt-6">
                <Badge
                  variant="secondary"
                  className="text-sm px-4 py-1.5 bg-muted/80"
                >
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Key Challenge: {data.metrics.keyChallenge}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Smart Actions Card */}
          <Card className="transition-all duration-200 hover:shadow-md shadow-none rounded-md">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-semibold flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-primary" />
                Smart Actions
              </CardTitle>
              <CardDescription>
                AI-powered recommendations for improvement
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                {data.smartActions.map((action, index) => (
                  <div
                    key={index}
                    className="space-y-4 p-4 rounded-lg bg-muted/50 transition-all duration-200 hover:bg-muted"
                  >
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold flex items-center gap-2">
                        <span>{action.category}</span>
                        <span className="text-sm text-muted-foreground">
                          ({action.percentage}% of Issues)
                        </span>
                      </h3>
                      <Badge
                        variant={
                          action.priority === "High"
                            ? "destructive"
                            : "secondary"
                        }
                        className="px-3"
                      >
                        {action.priority} Priority
                      </Badge>
                    </div>
                    <ul className="space-y-3">
                      {action.tasks.map((task, taskIndex) => (
                        <li
                          key={taskIndex}
                          className="flex items-start gap-3 p-2 rounded-md transition-all duration-200 hover:bg-background"
                        >
                          <CheckCircle2 className="h-5 w-5 mt-0.5 text-primary" />
                          <span className="text-sm">{task}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Implementation Progress Card */}
          <Card className="transition-all duration-200 hover:shadow-md shadow-none rounded-md">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-semibold flex items-center gap-2">
                <RepeatIcon className="h-5 w-5 text-primary" />
                Implementation Progress
              </CardTitle>
              <CardDescription>
                Track your improvement journey and milestones
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                <div className="p-4 rounded-lg bg-muted/50">
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <LineChart className="h-5 w-5 text-primary" />
                    Error Reduction
                  </h3>
                  <p className="text-sm text-muted-foreground mb-6">
                    {data.implementationProgress.errorReduction.forecast}
                  </p>
                  <div className="space-y-4">
                    <div className="flex justify-between text-sm">
                      <span className="flex items-center gap-2">
                        <span className="font-medium">Last Month:</span>
                        <span className="text-muted-foreground">
                          {data.implementationProgress.errorReduction.lastMonth}{" "}
                          issues
                        </span>
                      </span>
                      <span className="flex items-center gap-2">
                        <span className="font-medium">Current:</span>
                        <span className="text-primary font-semibold">
                          {data.implementationProgress.errorReduction.current}{" "}
                          issues
                        </span>
                      </span>
                    </div>
                    <Progress
                      value={
                        ((data.implementationProgress.errorReduction.lastMonth -
                          data.implementationProgress.errorReduction.current) /
                          data.implementationProgress.errorReduction
                            .lastMonth) *
                        100
                      }
                      className="h-2"
                    />
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <Target className="h-5 w-5 text-primary" />
                    Priority Tasks
                  </h3>
                  <div className="space-y-3">
                    {data.implementationProgress.priorityTasks.map(
                      (task, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 rounded-lg bg-muted/50 transition-all duration-200 hover:bg-muted"
                        >
                          <div className="flex items-center gap-3">
                            <div
                              className={cn(
                                "h-2.5 w-2.5 rounded-full transition-colors",
                                task.status === "completed"
                                  ? "bg-green-500"
                                  : "bg-yellow-500"
                              )}
                            />
                            <span className="text-sm font-medium">
                              {task.name}
                            </span>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {task.assistance}
                          </Badge>
                        </div>
                      )
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Strategic Insights Card */}
          <Card className="transition-all duration-200 hover:shadow-md shadow-none rounded-md">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-semibold flex items-center gap-2">
                <BrainCircuit className="h-5 w-5 text-primary" />
                Strategic Insights & Next Steps
              </CardTitle>
              <CardDescription>
                AI-powered insights and recommendations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-yellow-500" />
                    Risk Prediction
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {data.strategicInsights.riskPrediction}
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-purple-500" />
                    AI Smart Actions
                  </h3>
                  <ul className="space-y-3">
                    {data.strategicInsights.aiSmartActions.map(
                      (action, index) => (
                        <li
                          key={index}
                          className="flex items-start gap-3 p-2 rounded-md transition-all duration-200 hover:bg-background"
                        >
                          <BrainCircuit className="h-5 w-5 mt-0.5 text-primary" />
                          <span className="text-sm">{action}</span>
                        </li>
                      )
                    )}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Success Scoreboard Card */}
          <Card className="transition-all duration-200 hover:shadow-md shadow-none rounded-md">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-semibold flex items-center gap-2">
                <Target className="h-5 w-5 text-primary" />
                Success Scoreboard
              </CardTitle>
              <CardDescription>KPI Tracking and Action Items</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                {data.successScoreboard.kpiTracking.map((kpi, index) => (
                  <div
                    key={index}
                    className="space-y-4 p-4 rounded-lg bg-muted/50 transition-all duration-200 hover:bg-muted"
                  >
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold flex items-center gap-2">
                        <span>{kpi.name}</span>
                        <Badge variant="secondary" className="px-3">
                          {kpi.status}
                        </Badge>
                      </h3>
                    </div>
                    {kpi.action && (
                      <div className="mt-4">
                        <p className="text-sm text-muted-foreground">
                          <span className="font-medium">Action Required:</span>
                          <br />
                          {kpi.action}
                        </p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Footer */}
          <div className="flex items-center justify-center py-4 text-sm text-muted-foreground">
            Generated by SatoLOC Insight | AI Analysis
          </div>
        </>
      ) : (
        <>
          {/* Translation Overview Card */}
          <Card className="transition-all duration-200 hover:shadow-md shadow-none rounded-md">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-semibold flex items-center gap-2">
                <Languages className="h-5 w-5 text-primary" />
                Translation Overview
              </CardTitle>
              <CardDescription>Current Translation Progress</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                <div className="p-4 rounded-lg bg-muted/50">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Languages className="h-4 w-4 text-blue-500" />
                      <span>
                        Source Language:{" "}
                        {data.translationOverview.sourceLanguage}
                      </span>
                    </div>
                    <span>|</span>
                    <div className="flex items-center gap-2">
                      <Languages className="h-4 w-4 text-green-500" />
                      <span>
                        Target Language:{" "}
                        {data.translationOverview.targetLanguage}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-4 w-4 text-purple-500" />
                    <span>
                      Words Processed: {data.translationOverview.wordsProcessed}
                    </span>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium mb-4">
                      Key Observations
                    </h3>
                    <ul className="space-y-3">
                      {data.translationOverview.keyObservations.map(
                        (observation, index) => (
                          <li
                            key={index}
                            className="flex items-start gap-3 p-2 rounded-md transition-all duration-200 hover:bg-background"
                          >
                            <AlertTriangle className="h-4 w-4 mt-0.5 text-yellow-500" />
                            <span className="text-sm">{observation}</span>
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Smart Actions Card */}
          <Card className="transition-all duration-200 hover:shadow-md">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-semibold flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-primary" />
                Smart Actions
              </CardTitle>
              <CardDescription>
                Recommended steps for improvement
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                {data.smartActions.map((action, index) => (
                  <div
                    key={index}
                    className="space-y-4 p-4 rounded-lg bg-muted/50 transition-all duration-200 hover:bg-muted"
                  >
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold flex items-center gap-2">
                        <span>{action.category}</span>
                        <span className="text-sm text-muted-foreground">
                          ({action.priority} Priority)
                        </span>
                      </h3>
                      <Badge
                        variant={
                          action.priority === "High"
                            ? "destructive"
                            : action.priority === "Medium"
                              ? "secondary"
                              : "default"
                        }
                        className="px-3"
                      >
                        {action.priority}
                      </Badge>
                    </div>
                    <div className="space-y-3">
                      <div className="text-sm text-muted-foreground">
                        [] Suggested Action:
                      </div>
                      <ul className="space-y-3">
                        {action.tasks.map((task, taskIndex) => (
                          <li
                            key={taskIndex}
                            className="flex items-start gap-3 p-2 rounded-md transition-all duration-200 hover:bg-background"
                          >
                            <CheckCircle2 className="h-5 w-5 mt-0.5 text-primary" />
                            <span className="text-sm">{task}</span>
                          </li>
                        ))}
                      </ul>
                      {action.action && (
                        <div className="flex items-center gap-2 text-sm text-blue-500">
                          <span>👉</span>
                          <span>{action.action}</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Translation Progress Tracking Card */}
          <Card className="transition-all duration-200 hover:shadow-md">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-semibold flex items-center gap-2">
                <RepeatIcon className="h-5 w-5 text-primary" />
                Translation Progress Tracking
              </CardTitle>
              <CardDescription>Priority Tasks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                {data.progressTracking.priorityTasks.map((task, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 rounded-lg bg-muted/50 transition-all duration-200 hover:bg-muted"
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-sm font-medium">{task.name}</span>
                    </div>
                    {task.action && (
                      <div className="flex items-center gap-2 text-sm text-blue-500">
                        <span>👉</span>
                        <span>{task.action}</span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Success Scoreboard Card */}
          <Card className="transition-all duration-200 hover:shadow-md">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-semibold flex items-center gap-2">
                <Target className="h-5 w-5 text-primary" />
                Success Scoreboard
              </CardTitle>
              <CardDescription>KPI Tracking</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                {data.successScoreboard.kpiTracking.map((kpi, index) => (
                  <div
                    key={index}
                    className="space-y-4 p-4 rounded-lg bg-muted/50 transition-all duration-200 hover:bg-muted"
                  >
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold flex items-center gap-2">
                        <span>{kpi.name}</span>
                        <Badge variant="secondary" className="px-3">
                          {kpi.status}
                        </Badge>
                      </h3>
                    </div>
                    {kpi.action && (
                      <div className="mt-4">
                        <p className="text-sm text-muted-foreground">
                          <span className="font-medium">Action Required:</span>
                          <br />
                          {kpi.action}
                        </p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
