import { useEffect, useState, useMemo } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { FileText, Filter, X, Save, Edit } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useTheme } from "@mui/material/styles";
import { Input } from "@/components/ui/input";
import { useSession } from "next-auth/react";
import apiClient from "@/lib/apiClient";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

interface ReportTableData {
  id: string;
  errorCategory: string;
  errorSeverity: string;
  errorDescription: string;
  sourceContent: string;
  targetContent: string;
  aiTranslation: string;
  enhancedContent: string;
  screenshot?: string;
}

interface ReportTableProps {
  projectId: number;
  currentReport: any;
}

export default function ReportTable({
  projectId,
  currentReport,
}: ReportTableProps) {
  const theme = useTheme();
  const { data: session } = useSession();
  const queryClient = useQueryClient();
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [severityFilter, setSeverityFilter] = useState<string>("all");
  const [filteredData, setFilteredData] = useState<ReportTableData[]>([]);
  const [hasDualContent, setHasDualContent] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editedEnhancedContent, setEditedEnhancedContent] =
    useState<string>("");
  const [editedAiTranslation, setEditedAiTranslation] = useState<string>("");
  const is_single_url = currentReport?.report?.is_single_url || false;

  const uniqueCategories = useMemo(() => {
    if (!currentReport?.report?.report_table) return [];
    return Array.from(
      new Set(
        currentReport.report.report_table.map((item: any) => item.errorCategory)
      )
    );
  }, [currentReport]);

  const uniqueSeverities = useMemo(() => {
    if (!currentReport?.report?.report_table) return [];
    return Array.from(
      new Set(
        currentReport.report.report_table.map((item: any) => item.errorSeverity)
      )
    );
  }, [currentReport]);

  const severityColors = {
    Critical: {
      variant: "destructive",
      color: theme.palette.error.main,
    },
    Major: {
      variant: "warning",
      color: theme.palette.warning.main,
    },
    Minor: {
      variant: "info",
      color: theme.palette.info.main,
    },
    Neutral: {
      variant: "success",
      color: theme.palette.success.main,
    },
  } as const;

  useEffect(() => {
    const applyFilters = (categoryFilter: string, severityFilter: string) => {
      let filtered = currentReport?.report?.report_table || [];

      // Ensure each item has an id
      filtered = filtered.map((item: any) => {
        if (!item.id) {
          console.warn("Item missing id:", item);
          return item;
        }
        return item;
      });

      if (categoryFilter && categoryFilter !== "all") {
        filtered = filtered.filter(
          (item: any) => item.errorCategory === categoryFilter
        );
      }
      if (severityFilter && severityFilter !== "all") {
        filtered = filtered.filter(
          (item: any) => item.errorSeverity === severityFilter
        );
      }

      const hasTargetContent = filtered.some((item: any) => item.targetContent);
      setHasDualContent(hasTargetContent);

      setFilteredData(filtered);
    };

    applyFilters(categoryFilter, severityFilter);
  }, [categoryFilter, severityFilter, currentReport]);

  const clearFilters = () => {
    setCategoryFilter("all");
    setSeverityFilter("all");
  };

  const handleEdit = (
    id: string,
    content: string,
    isAiTranslation: boolean = false
  ) => {
    if (!id) {
      console.error("Invalid id for edit:", id);
      toast.error("Unable to edit this item");
      return;
    }

    setEditingId(id);
    if (isAiTranslation) {
      setEditedAiTranslation(content);
    } else {
      setEditedEnhancedContent(content);
    }
  };

  const handleSave = async (id: string) => {
    if (!id) {
      console.error("Invalid id:", id);
      toast.error("Unable to save changes");
      return;
    }

    try {
      const payload = {
        contentId: id,
        ...(is_single_url
          ? { aiTranslation: editedAiTranslation }
          : { enhancedContent: editedEnhancedContent }),
      };

      const response = await apiClient.patch(
        `/scraping/ai-reports/${currentReport.id}/`,
        payload
      );

      if (response.status === 200) {
        setFilteredData((prev) =>
          prev.map((item) => {
            if (item.id === id) {
              return {
                ...item,
                ...(is_single_url
                  ? { aiTranslation: editedAiTranslation }
                  : { enhancedContent: editedEnhancedContent }),
              };
            }
            return item;
          })
        );

        // Exit edit mode
        setEditingId(null);
        setEditedAiTranslation("");
        setEditedEnhancedContent("");

        // Force a re-fetch of the data
        await queryClient.invalidateQueries({
          queryKey: ["aiReport", projectId],
        });

        toast.success("Changes saved successfully");
      }
    } catch (error) {
      console.error("Failed to update content:", error);
      toast.error("Failed to save changes");
    }
  };

  if (!currentReport) {
    return (
      <Card className="transition-all duration-200 hover:shadow-md">
        <CardContent className="py-16">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <FileText className="h-16 w-16 text-muted-foreground/30" />
            <div className="space-y-2">
              <p className="text-xl font-semibold text-primary">
                No report data available
              </p>
              <p className="text-sm text-muted-foreground">
                Generate a report to view content analysis
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid gap-4">
      <Card className="bg-background overflow-hidden transition-all duration-200 hover:shadow-md  shadow-none rounded-md">
        <CardHeader>
          <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row md:items-center justify-between">
            <div className="space-y-1.5">
              <CardTitle className="text-2xl font-bold text-primary">
                Content Report
              </CardTitle>
              <CardDescription className="text-sm text-muted-foreground">
                {hasDualContent
                  ? "Detailed breakdown of identified errors and enhanced translations"
                  : "Your website translation in the target language is presented in the table below"}
              </CardDescription>
            </div>

            {hasDualContent && (
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                <div className="flex flex-wrap items-center gap-2">
                  <div className="flex items-center gap-2 min-w-[160px]">
                    <Filter className="h-4 w-4 text-primary" />
                    <Select
                      value={categoryFilter}
                      onValueChange={setCategoryFilter}
                    >
                      <SelectTrigger className="w-full transition-all hover:bg-accent focus:ring-2 focus:ring-primary/20">
                        <SelectValue placeholder="Filter by Category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all" className="hover:bg-primary/5">
                          All Categories
                        </SelectItem>
                        {uniqueCategories.map((category: any) => (
                          <SelectItem
                            key={`category-${category}`}
                            value={category}
                            className="hover:bg-primary/5"
                          >
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center gap-2 min-w-[160px]">
                    <Select
                      value={severityFilter}
                      onValueChange={setSeverityFilter}
                    >
                      <SelectTrigger className="w-full transition-all hover:bg-accent focus:ring-2 focus:ring-primary/20">
                        <SelectValue placeholder="Filter by Severity" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all" className="hover:bg-primary/5">
                          All Severities
                        </SelectItem>
                        {uniqueSeverities.map((severity: any) => (
                          <SelectItem
                            key={`severity-${severity}`}
                            value={severity}
                            className="hover:bg-primary/5"
                          >
                            {severity}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {(categoryFilter !== "all" || severityFilter !== "all") && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={clearFilters}
                      className="h-9 px-3 hover:bg-destructive/10 hover:text-destructive transition-colors"
                    >
                      <X className="h-4 w-4 mr-1" />
                      Clear
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>

          {(categoryFilter !== "all" || severityFilter !== "all") &&
            hasDualContent && (
              <div className="flex items-center gap-2 mt-4 p-2 rounded-md bg-muted/50">
                <p className="text-sm">
                  Showing{" "}
                  <span className="font-medium text-primary">
                    {filteredData.length}
                  </span>{" "}
                  of{" "}
                  <span className="font-medium">
                    {currentReport?.report.report_table.length}
                  </span>{" "}
                  errors
                </p>
              </div>
            )}
        </CardHeader>

        <CardContent className="p-2">
          <div className="rounded-md bg-background overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="hover:bg-transparent bg-muted/30">
                  {hasDualContent && (
                    <>
                      <TableHead className="font-medium text-primary">
                        Error Description
                      </TableHead>
                      <TableHead className="font-medium text-primary">
                        Category
                      </TableHead>
                      <TableHead className="font-medium text-primary">
                        Severity
                      </TableHead>
                    </>
                  )}
                  <TableHead className="hidden md:table-cell font-medium text-primary">
                    Source
                  </TableHead>
                  {hasDualContent ? (
                    <>
                      <TableHead className="hidden lg:table-cell font-medium text-primary">
                        Target
                      </TableHead>
                      <TableHead className="hidden lg:table-cell font-medium text-primary">
                        Enhanced
                      </TableHead>
                    </>
                  ) : (
                    <TableHead className="hidden md:table-cell font-medium text-primary">
                      Target
                    </TableHead>
                  )}
                  <TableHead className="font-medium text-primary text-right">
                    Actions
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.length > 0 ? (
                  filteredData.map((row) => (
                    <TableRow
                      key={row.id}
                      className="group hover:bg-muted/30 transition-colors"
                    >
                      {hasDualContent && (
                        <>
                          <TableCell className="font-medium max-w-[300px]">
                            <div className="text-sm">
                              {row.errorDescription}
                            </div>
                            <div className="md:hidden mt-2 space-y-2">
                              <div className="text-sm bg-muted/20 p-3 rounded-lg border border-border/50">
                                <span className="font-medium text-xs text-primary block mb-1">
                                  Source
                                </span>
                                {row.sourceContent}
                              </div>
                              <div className="text-sm bg-muted/20 p-3 rounded-lg border border-border/50">
                                <span className="font-medium text-xs text-primary block mb-1">
                                  Target
                                </span>
                                {row.targetContent}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant="outline"
                              className="bg-primary/5 hover:bg-primary/10 transition-colors"
                            >
                              {row.errorCategory}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant="outline"
                              className="transition-colors"
                              style={{
                                backgroundColor:
                                  severityColors[
                                    row.errorSeverity as keyof typeof severityColors
                                  ]?.color + "20",
                                color:
                                  severityColors[
                                    row.errorSeverity as keyof typeof severityColors
                                  ]?.color,
                                borderColor:
                                  severityColors[
                                    row.errorSeverity as keyof typeof severityColors
                                  ]?.color,
                              }}
                            >
                              {row.errorSeverity}
                            </Badge>
                          </TableCell>
                        </>
                      )}
                      <TableCell className="hidden md:table-cell">
                        <div className="p-3 rounded-lg bg-muted/20 border border-border/50">
                          <p className="text-sm leading-relaxed whitespace-pre-wrap break-words">
                            {row.sourceContent}
                          </p>
                        </div>
                      </TableCell>
                      {hasDualContent ? (
                        <>
                          <TableCell className="hidden lg:table-cell">
                            <div className="p-3 rounded-lg bg-muted/20 border border-border/50">
                              <p className="text-sm leading-relaxed whitespace-pre-wrap break-words">
                                {row.targetContent}
                              </p>
                            </div>
                          </TableCell>
                          <TableCell className="hidden lg:table-cell">
                            {editingId === row.id ? (
                              <div className="space-y-2">
                                <Input
                                  value={editedEnhancedContent}
                                  onChange={(e) =>
                                    setEditedEnhancedContent(e.target.value)
                                  }
                                  className="min-h-[100px] p-3 text-sm leading-relaxed"
                                />
                                <div className="flex justify-end gap-2">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => {
                                      setEditingId(null);
                                      setEditedEnhancedContent("");
                                    }}
                                    className="h-8 px-3 hover:bg-destructive/10 hover:text-destructive"
                                  >
                                    Cancel
                                  </Button>
                                  <Button
                                    size="sm"
                                    onClick={() => handleSave(row.id)}
                                    className="h-8 px-3"
                                  >
                                    <Save className="h-4 w-4 mr-1" />
                                    Save
                                  </Button>
                                </div>
                              </div>
                            ) : (
                              <div className="p-3 rounded-lg bg-primary/5 border border-primary/10 relative group">
                                <p className="text-sm leading-relaxed whitespace-pre-wrap break-words">
                                  {row.enhancedContent}
                                </p>
                              </div>
                            )}
                          </TableCell>
                        </>
                      ) : (
                        <TableCell className="hidden md:table-cell">
                          {editingId === row.id ? (
                            <div className="space-y-2">
                              <Input
                                value={editedAiTranslation}
                                onChange={(e) =>
                                  setEditedAiTranslation(e.target.value)
                                }
                                className="min-h-[100px] p-3 text-sm leading-relaxed"
                              />
                              <div className="flex justify-end gap-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => {
                                    setEditingId(null);
                                    setEditedAiTranslation("");
                                  }}
                                  className="h-8 px-3 hover:bg-destructive/10 hover:text-destructive"
                                >
                                  Cancel
                                </Button>
                                <Button
                                  size="sm"
                                  onClick={() => handleSave(row.id)}
                                  className="h-8 px-3"
                                >
                                  <Save className="h-4 w-4 mr-1" />
                                  Save
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <div className="p-3 rounded-lg bg-primary/5 border border-primary/10 relative group">
                              <p className="text-sm leading-relaxed whitespace-pre-wrap break-words">
                                {row.aiTranslation}
                              </p>
                            </div>
                          )}
                        </TableCell>
                      )}
                      <TableCell className="text-right">
                        {editingId === row.id ? (
                          <div className="flex justify-end">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleSave(row.id)}
                              className="h-8 w-8 p-0"
                            >
                              <Save className="h-4 w-4 text-primary" />
                            </Button>
                          </div>
                        ) : (
                          <div className="flex justify-end">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() =>
                                handleEdit(
                                  row.id,
                                  is_single_url
                                    ? row.aiTranslation
                                    : row.enhancedContent,
                                  is_single_url
                                )
                              }
                              className="h-8 w-8 p-0 hover:bg-primary/10"
                            >
                              <Edit className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
                            </Button>
                          </div>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={hasDualContent ? 7 : 3}
                      className="h-[200px] text-center"
                    >
                      <div className="flex flex-col items-center justify-center text-muted-foreground">
                        <FileText className="h-8 w-8 mb-2 opacity-50" />
                        <p className="font-medium">No matching results</p>
                        <p className="text-sm text-muted-foreground">
                          Try adjusting your filters or search criteria
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
