// components/ErrorCategorySeverity/index.tsx

"use client";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Bar, Doughnut } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  ChartData,
  ChartOptions,
} from "chart.js";
import { useTheme } from "@mui/material";
import ChartDataLabels from "chartjs-plugin-datalabels";
import { borderColor } from "@mui/system";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  ChartDataLabels
);

type AIReport = {
  id: number;
  created_at: string;
  report: {
    error_category_severity: {
      Major: number;
      Minor: number;
      Neutral: number;
      Critical: number;
      category: string;
    }[];
  };
};

interface ErrorCategorySeverityProps {
  projectId: number;
  currentReport: any; // Replace with proper type
}

type ErrorCategorySeverityData = {
  category: string;
  major: number;
  minor: number;
  neutral: number;
  critical: number;
}[];

export default function ErrorCategorySeverity({
  currentReport,
}: ErrorCategorySeverityProps) {
  const theme = useTheme();

  const transformToChartData = (
    data: AIReport["report"]["error_category_severity"] | undefined
  ) => ({
    labels: data?.map((item) => item.category) || [],
    datasets: [
      {
        label: "Critical",
        data: data?.map((item) => item.Critical) || [],
        backgroundColor: theme.palette.error.main,
        barThickness: 35,
      },
      {
        label: "Major",
        data: data?.map((item) => item.Major) || [],
        backgroundColor: theme.palette.warning.main,
        barThickness: 35,
      },
      {
        label: "Minor",
        data: data?.map((item) => item.Minor) || [],
        backgroundColor: theme.palette.info.main,
        barThickness: 35,
      },
      {
        label: "Neutral",
        data: data?.map((item) => item.Neutral) || [],
        backgroundColor: theme.palette.success.main,
        barThickness: 35,
      },
    ],
  });

  const transformToDoughnutData = (
    data: AIReport["report"]["error_category_severity"] | undefined
  ) => {
    if (!data) return { labels: [], datasets: [] };

    const totals = data.reduce(
      (acc, item) => ({
        Critical: acc.Critical + item.Critical,
        Major: acc.Major + item.Major,
        Minor: acc.Minor + item.Minor,
        Neutral: acc.Neutral + item.Neutral,
      }),
      { Critical: 0, Major: 0, Minor: 0, Neutral: 0 }
    );

    return {
      labels: ["Critical", "Major", "Minor", "Neutral"],
      datasets: [
        {
          data: [totals.Critical, totals.Major, totals.Minor, totals.Neutral],
          backgroundColor: [
            theme.palette.error.main,
            theme.palette.warning.main,
            theme.palette.info.main,
            theme.palette.success.main,
          ],
        },
      ],
    };
  };

  if (!currentReport) {
    return (
      <Card className="bg-muted/30 backdrop-blur-sm">
        <CardContent className="py-16">
          <div className="text-center space-y-2">
            <div className="text-2xl font-semibold text-muted-foreground/60">
              No Data Available
            </div>
            <div className="text-sm text-muted-foreground">
              No error category and severity data to display
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const chartOptions: ChartOptions<"bar"> = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        stacked: true,
        grid: {
          display: false,
        },
        border: {
          display: false,
        },
        ticks: {
          font: {
            family: "Inter",
          },
        },
      },
      y: {
        stacked: true,
        border: {
          dash: [4, 4] as number[],
        },
        grid: {
          color: theme.palette.divider,
          display: true,
        },
        ticks: {
          font: {
            family: "Inter",
          },
        },
      },
    },
    plugins: {
      legend: {
        position: "top",
        labels: {
          padding: 20,
          font: {
            family: "Inter",
            weight: 500,
          },
          usePointStyle: true,
          pointStyle: "circle",
        },
      },
      datalabels: {
        color: theme.palette.text.primary,
        font: (context: any) => ({
          family: "Inter",
          weight: 600,
          size: 11,
        }),
        padding: 6,
        formatter: (value: number) => (value > 0 ? value : ""),
      },
    },
  };

  const doughnutOptions: ChartOptions<"doughnut"> = {
    cutout: "75%",
    plugins: {
      legend: {
        position: "bottom",
        labels: {
          padding: 20,
          font: {
            family: "Inter",
            weight: 500,
          },
          usePointStyle: true,
          pointStyle: "circle",
        },
      },
      datalabels: {
        color: theme.palette.background.paper,
        font: (context: any) => ({
          family: "Inter",
          weight: 600,
          size: 13,
        }),
        formatter: (value: number) => (value > 0 ? value : ""),
      },
    },
    maintainAspectRatio: false,
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-[1fr_400px] gap-6">
      <Card className="group hover:shadow-lg transition-all duration-300 shadow-none rounded-md">
        <CardHeader>
          <CardTitle className="text-xl font-semibold flex items-center justify-between">
            Error Categories and Severity
          </CardTitle>
          <CardDescription className="text-sm text-muted-foreground">
            Distribution of errors across categories and severity levels
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] w-full p-4">
            <Bar
              data={transformToChartData(
                currentReport?.report.error_category_severity
              )}
              options={chartOptions}
            />
          </div>
        </CardContent>
      </Card>

      <Card className="group hover:shadow-lg transition-all duration-300 shadow-none rounded-md">
        <CardHeader>
          <CardTitle className="text-xl font-semibold">
            Overall Distribution
          </CardTitle>
          <CardDescription className="text-muted-foreground/80">
            Breakdown of errors by severity level
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] w-full p-4 flex items-center justify-center">
            <Doughnut
              data={transformToDoughnutData(
                currentReport?.report.error_category_severity
              )}
              options={doughnutOptions}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
