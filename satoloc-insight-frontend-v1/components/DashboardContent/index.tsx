// components/DashboardContent/index.tsx

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import dynamic from "next/dynamic";
import { Container } from "@mui/material";
import apiClient from "@/lib/apiClient";
import { useAIReport } from "@/internal-api/ai-report";
import { ReportNavigation } from "./ReportNavigation";
import { LqaScoreSkeleton } from "./LqaScore/LqaScoreSkeleton";
import InsightsHeader from "../InsightsHeader";
import { useUserData } from "@/internal-api/get-user-data";
import { ReportNavigationSkeleton } from "./ReportNavigation/ReportNavigationSkeleton";
import React from "react";

const LqaScore = dynamic(() => import("./LqaScore"), {
  loading: () => <LqaScoreSkeleton />,
});

const ErrorCategorySeverity = dynamic(() => import("./ErrorCategorySeverity"), {
  loading: () => <ErrorCategorySkeleton />,
});

const ReportTable = dynamic(() => import("./ReportTable"), {
  loading: () => <ReportTableSkeleton />,
});

const OnePager = dynamic(() => import("./OnePager"), {
  loading: () => <OnePagerSkeleton />,
});

function ErrorCategorySkeleton() {
  return (
    <Card className="p-6 space-y-6">
      <Skeleton className="h-8 w-[250px]" />
      <Skeleton className="h-[400px]" />
    </Card>
  );
}

function ReportTableSkeleton() {
  return (
    <Card className="p-6 space-y-4">
      <Skeleton className="h-8 w-[200px]" />
      <div className="space-y-2">
        {[...Array(5)].map((_, i) => (
          <Skeleton key={i} className="h-12 w-full" />
        ))}
      </div>
    </Card>
  );
}

function OnePagerSkeleton() {
  return <Skeleton className="h-[400px] w-full" />;
}

interface DashboardContentProps {
  initialProjectId?: number | null;
}

export default function DashboardContent({
  initialProjectId,
}: DashboardContentProps) {
  const router = useRouter();
  const { data: session, status } = useSession();
  const { data: userData } = useUserData();
  const [activeProject, setActiveProject] = useState<number | null>(
    initialProjectId || null
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentReportIndex, setCurrentReportIndex] = useState(0);
  const [activeTab, setActiveTab] = useState<string>("lqa");

  const getVisibleTabCount = () => {
    let count = 2; // Content Report and One Pager are always visible
    if (hasDualContent) count++;
    if (!is_single_url) count++;
    return count;
  };

  // Fetch AI report data using the custom hook
  const { data: aiReportData, isLoading: aiReportLoading } = useAIReport(
    activeProject ?? 0
  );

  // Sort reports by creation date, latest first
  const sortedReports = React.useMemo(() => {
    if (!aiReportData || !Array.isArray(aiReportData)) return undefined;

    return [...aiReportData].sort((a, b) => {
      const dateA = new Date(a.created_at).getTime();
      const dateB = new Date(b.created_at).getTime();
      return dateB - dateA; // Sort in descending order (latest first)
    });
  }, [aiReportData]);

  // Use sortedReports instead of aiReportData
  const aiReport = sortedReports;

  useEffect(() => {
    const fetchInitialProject = async () => {
      if (status === "authenticated") {
        try {
          setIsLoading(true);
          if (!initialProjectId) {
            const response = await apiClient.get("/scraping/projects/");
            const data = response.data;
            if (data && data.length > 0) {
              setActiveProject(data[0].id);
            }
          }
        } catch (err: any) {
          if (err.response?.status !== 401) {
            console.error("Error fetching initial project:", err);
            setError("Failed to load project data");
          }
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchInitialProject();
  }, [session, status, initialProjectId]);

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  // Get current report details
  const totalReports = aiReport?.length || 0;
  const currentReport = aiReport?.[currentReportIndex];
  const hasDualContent = currentReport?.report?.report_table?.some(
    (item: any) => item.targetContent
  );
  const is_single_url = currentReport?.report?.is_single_url || false;

  // Handle tab changes based on report type
  useEffect(() => {
    if (hasDualContent) {
      setActiveTab("lqa");
    } else if (!hasDualContent && activeTab === "lqa") {
      setActiveTab("report");
    }
  }, [hasDualContent]);

  if (status === "loading" || isLoading) {
    return (
      <div>
        <div className="space-y-6">
          <InsightsHeader isLoading={true} />
          <ReportNavigationSkeleton />
          <LqaScoreSkeleton />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div>
        <Alert>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!activeProject) {
    return (
      <div className="w-full">
        <Alert>
          <AlertDescription>
            No active project found. Please create a project first.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const handleRefresh = () => {
    window.location.reload();
  };

  return (
    <div>
      <div className="space-y-6">
        {totalReports > 0 && (
          <ReportNavigation
            currentIndex={currentReportIndex}
            totalReports={totalReports}
            createdAt={currentReport?.created_at}
            scrapedUrl={currentReport?.report?.url}
            onNavigate={setCurrentReportIndex}
            onRefresh={handleRefresh}
            isRefreshing={false}
            is_single_url={is_single_url}
            reportData={currentReport}
          />
        )}

        {/* Report Content */}
        <Card className="bg-background shadow-none rounded-md p-4">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList
              className="w-full grid border-solid border border-background rounded-sm p-1"
              style={{
                gridTemplateColumns: `repeat(${getVisibleTabCount()}, minmax(0, 1fr))`,
              }}
            >
              {hasDualContent && (
                <TabsTrigger value="lqa" className="text-sm rounded-sm">
                  LQA Score
                </TabsTrigger>
              )}
              {!is_single_url && (
                <TabsTrigger value="errors" className="text-sm rounded-sm">
                  Error Category
                </TabsTrigger>
              )}
              <TabsTrigger value="report" className="text-sm rounded-sm">
                Content Report
              </TabsTrigger>
              <TabsTrigger value="onepager" className="text-sm rounded-sm">
                One Pager
              </TabsTrigger>
            </TabsList>

            {hasDualContent && (
              <TabsContent value="lqa" className="mt-6">
                <LqaScore
                  projectId={activeProject}
                  currentReport={currentReport}
                />
              </TabsContent>
            )}

            {!is_single_url && (
              <TabsContent value="errors" className="mt-6">
                <ErrorCategorySeverity
                  projectId={activeProject}
                  currentReport={currentReport}
                />
              </TabsContent>
            )}

            <TabsContent value="report" className="mt-6">
              <ReportTable
                projectId={activeProject}
                currentReport={currentReport}
              />
            </TabsContent>

            <TabsContent value="onepager" className="mt-6">
              <OnePager
                projectId={activeProject}
                currentReport={currentReport}
              />
            </TabsContent>
          </Tabs>
        </Card>
      </div>
    </div>
  );
}
