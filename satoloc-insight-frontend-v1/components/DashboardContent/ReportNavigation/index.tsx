"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  ChevronLeft,
  ChevronRight,
  RefreshCw,
  Globe,
  Calendar,
  Download,
  FileText,
  Table,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import dayjs from "dayjs";

interface ReportNavigationProps {
  currentIndex: number;
  totalReports: number;
  createdAt?: string;
  scrapedUrl?: string;
  onNavigate: (index: number) => void;
  onRefresh: () => void;
  isRefreshing: boolean;
  is_single_url?: boolean;
  reportData?: any; // Current report data for download
}

// Utility function to convert data to CSV
const convertToCSV = (
  data: any[],
  hasDualContent: boolean,
  is_single_url: boolean
) => {
  if (!data || data.length === 0) return "";

  let headers = [];
  let rows = [];

  if (hasDualContent) {
    headers = [
      "Error Description",
      "Category",
      "Severity",
      "Source Content",
      "Target Content",
      "Enhanced Content",
    ];
    rows = data.map((row) => [
      `"${(row.errorDescription || "").replace(/"/g, '""')}"`,
      `"${(row.errorCategory || "").replace(/"/g, '""')}"`,
      `"${(row.errorSeverity || "").replace(/"/g, '""')}"`,
      `"${(row.sourceContent || "").replace(/"/g, '""')}"`,
      `"${(row.targetContent || "").replace(/"/g, '""')}"`,
      `"${(row.enhancedContent || "").replace(/"/g, '""')}"`,
    ]);
  } else {
    headers = [
      "Source Content",
      is_single_url ? "AI Translation" : "Target Content",
    ];
    rows = data.map((row) => [
      `"${(row.sourceContent || "").replace(/"/g, '""')}"`,
      `"${(is_single_url ? row.aiTranslation || "" : row.targetContent || "").replace(/"/g, '""')}"`,
    ]);
  }

  return [headers.join(","), ...rows.map((row) => row.join(","))].join("\n");
};

// Utility function to convert data to Markdown
const convertToMarkdown = (
  data: any[],
  hasDualContent: boolean,
  is_single_url: boolean
) => {
  if (!data || data.length === 0) return "# Report Data\n\nNo data available.";

  let markdown = "# Report Data\n\n";

  if (hasDualContent) {
    markdown +=
      "| Error Description | Category | Severity | Source Content | Target Content | Enhanced Content |\n";
    markdown += "|---|---|---|---|---|---|\n";

    data.forEach((row) => {
      markdown += `| ${(row.errorDescription || "").replace(/\|/g, "\\|").replace(/\n/g, "<br>")} | `;
      markdown += `${(row.errorCategory || "").replace(/\|/g, "\\|")} | `;
      markdown += `${(row.errorSeverity || "").replace(/\|/g, "\\|")} | `;
      markdown += `${(row.sourceContent || "").replace(/\|/g, "\\|").replace(/\n/g, "<br>")} | `;
      markdown += `${(row.targetContent || "").replace(/\|/g, "\\|").replace(/\n/g, "<br>")} | `;
      markdown += `${(row.enhancedContent || "").replace(/\|/g, "\\|").replace(/\n/g, "<br>")} |\n`;
    });
  } else {
    markdown += `| Source Content | ${is_single_url ? "AI Translation" : "Target Content"} |\n`;
    markdown += "|---|---|\n";

    data.forEach((row) => {
      markdown += `| ${(row.sourceContent || "").replace(/\|/g, "\\|").replace(/\n/g, "<br>")} | `;
      markdown += `${(is_single_url ? row.aiTranslation || "" : row.targetContent || "").replace(/\|/g, "\\|").replace(/\n/g, "<br>")} |\n`;
    });
  }

  return markdown;
};

// Utility function to download content as file
const downloadFile = (content: string, filename: string, mimeType: string) => {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export function ReportNavigation({
  currentIndex,
  totalReports,
  createdAt,
  scrapedUrl,
  onNavigate,
  onRefresh,
  isRefreshing,
  is_single_url,
  reportData,
}: ReportNavigationProps) {
  const handleDownloadCSV = () => {
    if (!reportData?.report?.report_table) {
      console.warn("No report data available for download");
      return;
    }

    const data = reportData.report.report_table;
    const hasDualContent = data.some((item: any) => item.targetContent);
    const csv = convertToCSV(data, hasDualContent, is_single_url || false);
    const timestamp = dayjs().format("YYYY-MM-DD_HH-mm-ss");
    downloadFile(csv, `report_${timestamp}.csv`, "text/csv");
  };

  const handleDownloadMarkdown = () => {
    if (!reportData?.report?.report_table) {
      console.warn("No report data available for download");
      return;
    }

    const data = reportData.report.report_table;
    const hasDualContent = data.some((item: any) => item.targetContent);
    const markdown = convertToMarkdown(
      data,
      hasDualContent,
      is_single_url || false
    );
    const timestamp = dayjs().format("YYYY-MM-DD_HH-mm-ss");
    downloadFile(markdown, `report_${timestamp}.md`, "text/markdown");
  };

  return (
    <Card className="bg-background backdrop-blur-sm shadow-none rounded-md">
      <CardContent className="p-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          {/* Report Info */}
          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span>
                Generated on{" "}
                {createdAt
                  ? dayjs(createdAt).format("DD MMM YYYY, HH:mm")
                  : "N/A"}
              </span>
            </div>
            {scrapedUrl && (
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4 text-primary" />
                <a
                  href={scrapedUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-primary hover:underline truncate max-w-[300px]"
                >
                  {scrapedUrl}
                </a>
              </div>
            )}
          </div>

          {/* Navigation Controls */}
          <div className="flex items-center gap-2 self-end sm:self-auto">
            {/* Report Counter */}
            <div className="text-sm text-muted-foreground mr-2">
              Report {currentIndex + 1} of {totalReports}
            </div>

            {/* Download Dropdown */}
            {reportData?.report?.report_table && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="icon" className="h-8 w-8">
                    <Download className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleDownloadCSV}>
                    <Table className="h-4 w-4 mr-2" />
                    Download as CSV
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleDownloadMarkdown}>
                    <FileText className="h-4 w-4 mr-2" />
                    Download as Markdown
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}

            {/* Navigation Buttons */}
            <div className="flex items-center gap-1">
              <Button
                variant="outline"
                size="icon"
                onClick={() => onNavigate(currentIndex - 1)}
                disabled={currentIndex === 0}
                className="h-8 w-8"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => onNavigate(currentIndex + 1)}
                disabled={currentIndex === totalReports - 1}
                className="h-8 w-8"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={onRefresh}
                disabled={isRefreshing}
                className={cn("h-8 w-8 ml-2", isRefreshing && "animate-spin")}
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
