"use client";

import { usePathname } from "next/navigation";
import { useSession } from "next-auth/react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Container } from "@mui/material";

interface PathMap {
  [key: string]: string;
}

export default function Breadcrumbs() {
  const pathname = usePathname();
  const { data: session } = useSession();
  const isAdmin = session?.user?.isAdmin;

  // Define path mappings for both admin and user routes
  const adminPaths: PathMap = {
    "admin-dashboard": "Admin Dashboard",
    "user-management": "User Management",
  };

  const userPaths: PathMap = {
    dashboard: "Dashboard",
    insights: "Insights",
    scraping: "Scraping",
    profile: "Profile",
    "user-settings": "Settings",
  };

  // Choose the appropriate path mapping based on user role
  const pathMap = isAdmin ? adminPaths : userPaths;

  // Split the pathname into segments and remove empty strings
  const segments = pathname?.split("/").filter(Boolean);

  // Generate breadcrumb items
  const generateBreadcrumbs = () => {
    const breadcrumbs = [];
    let currentPath = "";

    // Add root breadcrumb
    const rootPath = isAdmin ? "/admin-dashboard" : "/dashboard";
    const rootLabel = isAdmin ? "Admin Dashboard" : "Dashboard";

    breadcrumbs.push(
      <BreadcrumbItem key="root">
        <BreadcrumbLink href={rootPath}>{rootLabel}</BreadcrumbLink>
      </BreadcrumbItem>
    );

    // Add separator if there are more segments
    if (segments && segments.length > 0) {
      breadcrumbs.push(<BreadcrumbSeparator key="sep-root" />);
    }

    // Process remaining segments
    segments?.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === segments.length - 1;

      // Skip if it's the dashboard segment
      if (segment === "dashboard" || segment === "admin-dashboard") {
        return;
      }

      const displayName = pathMap[segment] || segment.replace(/-/g, " ");

      breadcrumbs.push(
        <BreadcrumbItem key={currentPath}>
          {isLast ? (
            <span className="font-medium text-foreground">{displayName}</span>
          ) : (
            <BreadcrumbLink href={currentPath}>{displayName}</BreadcrumbLink>
          )}
        </BreadcrumbItem>
      );

      if (!isLast) {
        breadcrumbs.push(<BreadcrumbSeparator key={`sep-${currentPath}`} />);
      }
    });

    return breadcrumbs;
  };

  // Don't show breadcrumbs on the main dashboard pages
  if (
    !pathname ||
    pathname === "/" ||
    pathname === "/dashboard" ||
    pathname === "/admin-dashboard"
  ) {
    return null;
  }

  return (
    <Breadcrumb>
      <BreadcrumbList>{generateBreadcrumbs()}</BreadcrumbList>
    </Breadcrumb>
  );
}
