import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { usePathname } from "next/navigation";
import { SessionExpiredModal } from "../SessionExpiredModal";
import { useSessionExpired } from "@/helpers/useSessionExpired";

export default function SessionHandler() {
  const { status } = useSession();
  const pathname = usePathname();
  const {
    isSessionExpired,
    handleSessionExpired,
    handleCloseModal,
    handleLogin,
  } = useSessionExpired();
  const [isLoggingIn, setIsLoggingIn] = useState(false);

  // Check if we're on a public route
  const isPublicRoute = pathname
    ? ["/", "/login", "/register"].includes(pathname)
    : false;

  useEffect(() => {
    // Listen for custom session expired event (401 errors)
    const handleSessionExpiredEvent = (event: CustomEvent) => {
      // Only show expired modal if we get a 401 error and we're not on a public route
      if (!isLoggingIn && !isPublicRoute && event.detail?.status === 401) {
        handleSessionExpired();
      }
    };

    window.addEventListener(
      "session-expired" as any,
      handleSessionExpiredEvent
    );

    return () => {
      window.removeEventListener(
        "session-expired" as any,
        handleSessionExpiredEvent
      );
    };
  }, [handleSessionExpired, isLoggingIn, isPublicRoute]);

  // Don't show modal on public routes or when not expired
  if (isPublicRoute || !isSessionExpired) {
    return null;
  }

  return (
    <SessionExpiredModal
      isOpen={isSessionExpired}
      onClose={handleCloseModal}
      onConfirm={handleLogin}
    />
  );
}
