"use client";

import React, { useEffect, useState } from "react";
import {
  useCrawlWebsite,
  CrawlResponse,
  CrawlRequest,
} from "@/internal-api/seo";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Info, AlertCircle, CheckCircle } from "lucide-react";
import {
  useWebsiteByUrl,
  useProcessWebsite,
} from "@/internal-api/seo/advance-seo";

interface CrawlFormProps {
  onSuccess?: (response: CrawlResponse | any) => void;
}

export default function CrawlForm({ onSuccess }: CrawlFormProps) {
  const [formData, setFormData] = useState({
    url: "",
    language: "",
    industry: "",
    max_depth: 3, // Fixed depth of 3
    max_pages: 10, // Default to 10 pages
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  // Update the advance-seo hooks
  const [advanceSeoUrl, setAdvanceSeoUrl] = useState<string>("");
  const [shouldFetch, setShouldFetch] = useState(false);

  // Log URL changes for debugging
  useEffect(() => {}, [advanceSeoUrl]);

  const {
    data: websiteData,
    isLoading: isWebsiteLoading,
    refetch: refetchWebsite,
    isError: isWebsiteError,
    error: websiteError,
  } = useWebsiteByUrl(advanceSeoUrl, {
    enabled: shouldFetch, // Only fetch when explicitly enabled
    retry: false, // Don't retry on error
    onError: (error: any) => {
      console.error("Error in useWebsiteByUrl:", error);
      // If we get a 404, it means the website doesn't exist yet
      if (error.response?.status === 404) {
        // Process the website if it doesn't exist
        if (formData.url) {
          const domain = extractDomain(formData.url);
          if (domain) {
            processWebsite({
              url: domain,
              industry: formData.industry || "Technology",
              language: formData.language || "en",
            });
          } else {
            console.error("Domain is empty after extraction");
            toast({
              title: "Error",
              description: "Could not extract a valid domain from the URL",
              variant: "destructive",
            });
            // Reset loading state
            setIsSubmitting(false);
          }
        } else {
          // Reset loading state if no URL
          setIsSubmitting(false);
        }
      } else {
        console.error("Error fetching website data:", error);
        toast({
          title: "Error",
          description: error.message || "Failed to fetch website data",
          variant: "destructive",
        });
        // Reset loading state
        setIsSubmitting(false);
      }
      // Reset the fetch flag
      setShouldFetch(false);
    },
    onSuccess: (data: any) => {
      // Reset the fetch flag after successful fetch
      setShouldFetch(false);
      // Reset loading state
      setIsSubmitting(false);

      // If onSuccess callback is provided, pass the data
      if (onSuccess) {
        onSuccess(data);
      }
    },
  });

  const { mutate: processWebsite, isLoading: isProcessing } = useProcessWebsite(
    {
      onSuccess: (data: any) => {
        toast({
          title: "Success",
          description:
            "SEO analysis has been successfully added to the database.",
        });

        // After processing, fetch the website data
        if (formData.url) {
          const domain = extractDomain(formData.url);
          setAdvanceSeoUrl(domain);
          setTimeout(() => {
            setShouldFetch(true);
            refetchWebsite();
          }, 1000); // Add a small delay to ensure the data is available
        } else {
          // Reset loading state if we're not going to fetch
          setIsSubmitting(false);
        }
      },
      onError: (error: Error) => {
        toast({
          title: "Error",
          description:
            error.message ||
            "Failed to process website. Please try again later.",
          variant: "destructive",
        });
        // Reset loading state
        setIsSubmitting(false);
      },
    }
  );

  // Log the website data when it changes and pass to parent
  useEffect(() => {
    if (websiteData) {
      // If onSuccess callback is provided, pass the website data
      if (onSuccess) {
        onSuccess(websiteData);
      }
    }
  }, [websiteData, onSuccess]);

  // Extract domain from URL (remove protocol, www, and path)
  const extractDomain = (url: string): string => {
    if (!url || url.trim() === "") {
      return "";
    }

    try {
      // First ensure URL has a protocol
      let processedUrl = url;
      if (!url.startsWith("http://") && !url.startsWith("https://")) {
        processedUrl = "https://" + url;
      }

      // Parse the URL
      const urlObj = new URL(processedUrl);

      // Extract domain (remove www if present)
      const domain = urlObj.hostname.replace(/^www\./, "");

      // Return empty string if domain is empty
      return domain || "";
    } catch (error) {
      console.error("Error extracting domain:", error);
      // Return original input if parsing fails, but ensure it's not empty
      const fallbackDomain = url
        .replace(/^https?:\/\//, "")
        .replace(/^www\./, "")
        .split("/")[0];
      return fallbackDomain || "";
    }
  };

  const validateUrl = (url: string): string => {
    if (!url || url.trim() === "") return "";
    // Add protocol if missing
    if (!url.startsWith("http://") && !url.startsWith("https://")) {
      return `https://${url}`;
    }
    return url;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Keep the existing functionality
    if (!formData.url || formData.url.trim() === "") {
      toast({
        title: "Error",
        description: "Please enter a URL",
        variant: "destructive",
      });
      return;
    }

    // Extract domain for the API
    const domain = extractDomain(formData.url);

    // Validate domain is not empty
    if (!domain) {
      toast({
        title: "Error",
        description: "Could not extract a valid domain from the URL",
        variant: "destructive",
      });
      return;
    }

    try {
      // Clear any previous URL and fetch state
      setAdvanceSeoUrl("");
      setShouldFetch(false);

      // Set the new URL
      setAdvanceSeoUrl(domain);

      // Enable fetching
      setShouldFetch(true);

      // Trigger the fetch
      refetchWebsite();
    } catch (error) {
      console.error("Error in handleSubmit:", error);
      toast({
        title: "Error",
        description: "An error occurred while submitting the form",
        variant: "destructive",
      });
    }
  };

  // Add a safety mechanism to reset loading state after a timeout
  React.useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if (isSubmitting) {
      // If still submitting after 30 seconds, reset the state
      timeoutId = setTimeout(() => {
        setIsSubmitting(false);
      }, 30000);
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [isSubmitting]);

  return (
    <Card className="bg-background/60 border">
      <CardContent className="p-6 space-y-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* URL Input and GSC Toggle */}
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
            <div className="flex-1">
              <div className="relative">
                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                  https://
                </span>
                <Input
                  className="pl-16"
                  placeholder="satoloc.com"
                  value={formData.url}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, url: e.target.value }))
                  }
                  required
                />
              </div>
            </div>
          </div>
          {/* Language, Industry Select, Max Pages, and Submit Button */}
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
            <Select
              value={formData.language}
              onValueChange={(value) =>
                setFormData((prev) => ({ ...prev, language: value }))
              }
            >
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Language" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="tr">Turkish</SelectItem>
                <SelectItem value="zh">Chinese</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={formData.industry}
              onValueChange={(value) =>
                setFormData((prev) => ({ ...prev, industry: value }))
              }
            >
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Industry" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="fintech">Fintech</SelectItem>
                <SelectItem value="technology">Technology</SelectItem>
                <SelectItem value="web3">Web3</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex items-center">
              <Select
                value={formData.max_pages.toString()}
                onValueChange={(value) =>
                  setFormData((prev) => ({
                    ...prev,
                    max_pages: parseInt(value),
                  }))
                }
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Max Pages" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10 pages</SelectItem>
                  <SelectItem value="50">50 pages</SelectItem>
                  <SelectItem value="100">100 pages</SelectItem>
                  <SelectItem value="200">200 pages</SelectItem>
                  <SelectItem value="500">500 pages</SelectItem>
                </SelectContent>
              </Select>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <Info className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent className="w-80">
                    <div className="space-y-2">
                      <p className="font-medium">Crawl Settings</p>
                      <div>
                        <p>
                          Maximum number of pages to crawl. Higher values will
                          provide more comprehensive analysis but may take
                          longer to complete.
                        </p>
                      </div>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            <div className="flex-1 md:flex md:justify-end">
              <Button
                type="submit"
                className="w-full md:w-auto bg-[#003B5C] hover:bg-[#002A41]"
                disabled={!formData.url}
              >
                Start Analysis
              </Button>
            </div>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
