import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Bell,
  Mail,
  Moon,
  Globe,
  Lock,
  Eye,
  Languages,
  Save,
  Loader2,
  Calendar,
  Sun,
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useTheme } from "next-themes";
import { cn } from "@/lib/utils";

interface SettingsOption {
  id: string;
  label: string;
  description: string;
  defaultValue: boolean;
  icon: React.ReactNode;
}

const notificationSettings: SettingsOption[] = [
  {
    id: "email-notifications",
    label: "Email Notifications",
    description: "Receive email notifications about your insights and reports",
    defaultValue: true,
    icon: <Mail className="h-4 w-4" />,
  },
  {
    id: "push-notifications",
    label: "Push Notifications",
    description: "Get notified about important updates and events",
    defaultValue: true,
    icon: <Bell className="h-4 w-4" />,
  },
];

const privacySettings: SettingsOption[] = [
  {
    id: "public-profile",
    label: "Public Profile",
    description: "Make your profile visible to other users",
    defaultValue: false,
    icon: <Eye className="h-4 w-4" />,
  },
  {
    id: "two-factor",
    label: "Two-Factor Authentication",
    description: "Add an extra layer of security to your account",
    defaultValue: false,
    icon: <Lock className="h-4 w-4" />,
  },
];

const appearanceSettings: SettingsOption[] = [
  {
    id: "dark-mode",
    label: "Dark Mode",
    description: "Toggle between light and dark mode",
    defaultValue: false,
    icon: <Moon className="h-4 w-4" />,
  },
];

export function UserSettings() {
  const { theme, setTheme } = useTheme();
  const [settings, setSettings] = useState<{ [key: string]: boolean }>({});
  const [mounted, setMounted] = useState(false);
  const [language, setLanguage] = useState("en");
  const [timezone, setTimezone] = useState("UTC");
  const [reportFrequency, setReportFrequency] = useState("weekly");
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleToggle = (id: string) => {
    if (id === "dark-mode") {
      setTheme(theme === "dark" ? "light" : "dark");
    }
    setSettings((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const handleSaveSettings = async () => {
    setIsSaving(true);
    setSaveError(null);

    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
    } catch (error) {
      setSaveError("Failed to save settings. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  const renderSettingsGroup = (options: SettingsOption[]) => {
    return options.map((option) => (
      <div
        key={option.id}
        className="flex items-center justify-between space-x-4 rounded-lg border p-4"
      >
        <div className="flex-1 space-y-1">
          <div className="flex items-center">
            {option.icon}
            <Label
              htmlFor={option.id}
              className="ml-2 font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {option.label}
            </Label>
          </div>
          <p className="text-sm text-muted-foreground">{option.description}</p>
        </div>
        <Switch
          id={option.id}
          checked={settings[option.id] ?? option.defaultValue}
          onCheckedChange={() => handleToggle(option.id)}
          className={cn(
            "data-[state=checked]:bg-[#003c77]", // theme.palette.primary.main for light mode
            "data-[state=unchecked]:bg-gray-200" // grey for unchecked state
          )}
        />
      </div>
    ));
  };

  return (
    <div className="space-y-6">
      {saveError && (
        <Alert variant="destructive">
          <AlertDescription>{saveError}</AlertDescription>
        </Alert>
      )}
      <Card>
        <CardHeader>
          <CardTitle>Notifications</CardTitle>
          <CardDescription>
            Configure how you want to receive notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {renderSettingsGroup(notificationSettings)}

          <Separator className="my-4" />

          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <h3 className="font-medium">Report Frequency</h3>
            </div>
            <RadioGroup
              value={reportFrequency}
              onValueChange={setReportFrequency}
              className="grid gap-4 pt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="daily"
                  id="daily"
                  className="border-primary text-primary"
                />
                <Label htmlFor="daily" className="font-normal">
                  Daily Report
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="weekly"
                  id="weekly"
                  className="border-primary text-primary"
                />
                <Label htmlFor="weekly" className="font-normal">
                  Weekly Report
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="monthly"
                  id="monthly"
                  className="border-primary text-primary"
                />
                <Label htmlFor="monthly" className="font-normal">
                  Monthly Report
                </Label>
              </div>
            </RadioGroup>
            <p className="text-sm text-muted-foreground">
              Choose how frequently you want to receive analytical reports
            </p>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>Privacy & Security</CardTitle>
          <CardDescription>
            Manage your privacy and security preferences
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {renderSettingsGroup(privacySettings)}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Appearance</CardTitle>
          <CardDescription>Customize how the application looks</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {renderSettingsGroup(appearanceSettings)}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Localization</CardTitle>
          <CardDescription>
            Configure your language and timezone preferences
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Languages className="h-4 w-4" />
                <Label>Language</Label>
              </div>
              <Select value={language} onValueChange={setLanguage}>
                <SelectTrigger>
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="tk">Turkish</SelectItem>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="es">Spanish</SelectItem>
                  <SelectItem value="fr">French</SelectItem>
                  <SelectItem value="de">German</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                <Label>Timezone</Label>
              </div>
              <Select value={timezone} onValueChange={setTimezone}>
                <SelectTrigger>
                  <SelectValue placeholder="Select timezone" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="UTC">UTC</SelectItem>
                  <SelectItem value="EST">EST</SelectItem>
                  <SelectItem value="PST">PST</SelectItem>
                  <SelectItem value="GMT">GMT</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button
          onClick={handleSaveSettings}
          disabled={isSaving}
          className="min-w-[120px]"
        >
          {isSaving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
