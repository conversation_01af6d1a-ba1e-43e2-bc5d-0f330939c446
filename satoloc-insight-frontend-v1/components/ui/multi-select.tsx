// Example MultiSelect component (put in components/ui/multi-select.tsx or similar)
import * as React from "react";
import { X } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import {
  Command,
  CommandGroup,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Command as CommandPrimitive } from "cmdk";
import { cn } from "@/lib/utils"; // Ensure you have this utility

type Option = {
  value: string; // Use string for value (e.g., category ID as string)
  label: string;
};

interface MultiSelectProps {
  options: Option[];
  selected: string[]; // Array of selected values (IDs as strings)
  onChange: (selected: string[]) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function MultiSelect({
  options,
  selected,
  onChange,
  placeholder = "Select...",
  className,
  disabled = false,
}: MultiSelectProps) {
  const inputRef = React.useRef<HTMLInputElement>(null);
  const [open, setOpen] = React.useState(false);
  const [inputValue, setInputValue] = React.useState("");

  const handleUnselect = React.useCallback(
    (value: string) => {
      if (disabled) return;
      onChange(selected.filter((s) => s !== value));
    },
    [onChange, selected, disabled]
  );

  const handleKeyDown = React.useCallback(
    (e: React.KeyboardEvent<HTMLDivElement>) => {
      const input = inputRef.current;
      if (input) {
        if (e.key === "Delete" || e.key === "Backspace") {
          if (input.value === "" && selected.length > 0) {
            handleUnselect(selected[selected.length - 1]);
          }
        }
        if (e.key === "Escape") {
          input.blur();
        }
      }
    },
    [handleUnselect, selected]
  );

  const selectedOptions = React.useMemo(
    () => options.filter((option) => selected.includes(option.value)),
    [options, selected]
  );

  return (
    <Command
      onKeyDown={handleKeyDown}
      className={cn("overflow-visible bg-transparent", className)}
      shouldFilter={true} // Let Command handle filtering based on label
    >
      <div
        className={cn(
          "group rounded-md border border-input px-3 py-2 text-sm ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2",
          disabled ? "cursor-not-allowed opacity-50" : ""
        )}
      >
        <div className="flex flex-wrap gap-1">
          {selectedOptions.map((option) => (
            <Badge
              key={option.value}
              variant="secondary"
              className={cn(disabled ? "" : "cursor-pointer")}
            >
              {option.label}
              {!disabled && (
                <button
                  className="ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleUnselect(option.value);
                    }
                  }}
                  onMouseDown={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  onClick={() => handleUnselect(option.value)}
                  title={`Remove ${option.label}`}
                >
                  <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                </button>
              )}
            </Badge>
          ))}
          {/* Avoid input collision with existing badges */}
          <CommandPrimitive.Input
            ref={inputRef}
            value={inputValue}
            onValueChange={setInputValue}
            onBlur={() => setOpen(false)}
            onFocus={() => setOpen(true)}
            placeholder={selected.length === 0 ? placeholder : ""}
            className={cn(
              "ml-2 flex-1 bg-transparent outline-none placeholder:text-muted-foreground",
              selected.length > 0 ? "w-auto" : "w-full" // Adjust width based on selection
            )}
            disabled={disabled}
            onClick={() => setOpen(true)} // Re-open on click if closed
          />
        </div>
      </div>
      <div className="relative mt-2">
        {open && !disabled && (
          <div className="absolute top-0 z-10 w-full rounded-md border bg-popover text-popover-foreground shadow-md outline-none animate-in">
            <CommandList className="max-h-[300px] overflow-y-auto">
              <CommandGroup heading="Available options">
                {options.map((option) => {
                  const isSelected = selected.includes(option.value);
                  return (
                    <CommandItem
                      key={option.value}
                      onMouseDown={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                      }}
                      onSelect={() => {
                        if (isSelected) {
                          handleUnselect(option.value);
                        } else {
                          onChange([...selected, option.value]);
                        }
                        setInputValue(""); // Clear input after selection
                      }}
                      className={cn(
                        "cursor-pointer",
                        isSelected ? "font-bold" : ""
                      )}
                    >
                      {option.label}
                    </CommandItem>
                  );
                })}
              </CommandGroup>
              {options.length === 0 && (
                <CommandItem disabled>No options found.</CommandItem>
              )}
            </CommandList>
          </div>
        )}
      </div>
    </Command>
  );
}
