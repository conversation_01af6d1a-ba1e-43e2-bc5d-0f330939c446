/**
 * Export Dialog Component
 * Professional export interface with format selection and configuration
 */

"use client";

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Download, 
  FileText, 
  Code, 
  FileImage, 
  File,
  Eye,
  Copy,
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

import { 
  ExportContent, 
  ExportConfig, 
  ExportFormat,
  calculateContentStats 
} from '@/lib/exportUtils';
import { 
  exportContent, 
  previewContent, 
  getAvailableFormats,
  copyContentToClipboard 
} from '@/lib/exportServices';

interface ExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  content: ExportContent;
  defaultFormat?: ExportFormat;
}

// Format icons mapping
const formatIcons: Record<ExportFormat, React.ComponentType<{ className?: string }>> = {
  pdf: FileImage,
  docx: FileText,
  html: Code,
  markdown: FileText,
  txt: File,
  json: Code,
};

export default function ExportDialog({
  open,
  onOpenChange,
  content,
  defaultFormat = 'pdf'
}: ExportDialogProps) {
  const [selectedFormat, setSelectedFormat] = useState<ExportFormat>(defaultFormat);
  const [config, setConfig] = useState<ExportConfig>({
    format: defaultFormat,
    filename: '',
    title: content.title || 'Untitled',
    author: 'Content Creator',
    subject: '',
    keywords: content.metadata?.keywords || '',
    includeMetadata: true,
    includeTimestamp: true,
    customStyles: '',
  });
  
  const [isExporting, setIsExporting] = useState(false);
  const [isCopying, setIsCopying] = useState(false);
  const [isPreviewing, setIsPreviewing] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [contentStats, setContentStats] = useState<any>(null);
  
  const { toast } = useToast();
  const availableFormats = getAvailableFormats();

  // Calculate content statistics
  useEffect(() => {
    if (content.content) {
      const stats = calculateContentStats(content.content, true);
      setContentStats(stats);
    }
  }, [content.content]);

  // Update config when format changes
  useEffect(() => {
    setConfig(prev => ({
      ...prev,
      format: selectedFormat,
    }));
  }, [selectedFormat]);

  // Clean up preview URL
  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  const handleExport = async () => {
    if (!content.content.trim()) {
      toast({
        title: "Export Failed",
        description: "No content to export. Please generate or add content first.",
        variant: "destructive",
      });
      return;
    }

    setIsExporting(true);
    
    try {
      await exportContent(content, config);
      
      toast({
        title: "Export Successful",
        description: `Content exported as ${selectedFormat.toUpperCase()} successfully.`,
        variant: "default",
      });
      
      onOpenChange(false);
    } catch (error) {
      console.error('Export failed:', error);
      toast({
        title: "Export Failed",
        description: error instanceof Error ? error.message : 'An unexpected error occurred.',
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handlePreview = async () => {
    if (!content.content.trim()) {
      toast({
        title: "Preview Failed",
        description: "No content to preview.",
        variant: "destructive",
      });
      return;
    }

    setIsPreviewing(true);
    
    try {
      const url = await previewContent(content, config);
      if (url) {
        // Clean up previous preview URL
        if (previewUrl) {
          URL.revokeObjectURL(previewUrl);
        }
        setPreviewUrl(url);
        window.open(url, '_blank');
      } else {
        toast({
          title: "Preview Not Available",
          description: `Preview is not supported for ${selectedFormat.toUpperCase()} format.`,
          variant: "default",
        });
      }
    } catch (error) {
      console.error('Preview failed:', error);
      toast({
        title: "Preview Failed",
        description: error instanceof Error ? error.message : 'An unexpected error occurred.',
        variant: "destructive",
      });
    } finally {
      setIsPreviewing(false);
    }
  };

  const handleCopyToClipboard = async () => {
    setIsCopying(true);
    
    try {
      const success = await copyContentToClipboard(content, 'txt');
      
      if (success) {
        toast({
          title: "Copied to Clipboard",
          description: "Content copied to clipboard successfully.",
          variant: "default",
        });
      } else {
        throw new Error('Failed to copy to clipboard');
      }
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy content to clipboard.",
        variant: "destructive",
      });
    } finally {
      setIsCopying(false);
    }
  };

  const selectedFormatInfo = availableFormats.find(f => f.format === selectedFormat);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5 text-[#1279b4]" />
            Export Content
          </DialogTitle>
          <DialogDescription>
            Export your content in various formats with customizable options.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Content Statistics */}
          {contentStats && (
            <div className="grid grid-cols-4 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-center">
                <div className="text-2xl font-bold text-[#1279b4]">{contentStats.wordCount}</div>
                <div className="text-sm text-muted-foreground">Words</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-[#1279b4]">{contentStats.characterCount}</div>
                <div className="text-sm text-muted-foreground">Characters</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-[#1279b4]">{contentStats.paragraphCount}</div>
                <div className="text-sm text-muted-foreground">Paragraphs</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-[#1279b4]">
                  {Math.ceil(contentStats.wordCount / 250)}
                </div>
                <div className="text-sm text-muted-foreground">Pages</div>
              </div>
            </div>
          )}

          {/* Format Selection */}
          <div className="space-y-3">
            <Label htmlFor="format">Export Format</Label>
            <Select value={selectedFormat} onValueChange={(value) => setSelectedFormat(value as ExportFormat)}>
              <SelectTrigger id="format">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {availableFormats.map((format) => {
                  const Icon = formatIcons[format.format];
                  return (
                    <SelectItem key={format.format} value={format.format}>
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        <div>
                          <div className="font-medium">{format.name}</div>
                          <div className="text-xs text-muted-foreground">{format.description}</div>
                        </div>
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
            {selectedFormatInfo && (
              <div className="flex items-center gap-2">
                <Badge variant="secondary">{selectedFormatInfo.mimeType}</Badge>
                {selectedFormat === 'docx' && (
                  <Badge variant="outline" className="text-orange-600">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Coming Soon
                  </Badge>
                )}
              </div>
            )}
          </div>

          <Separator />

          {/* Basic Configuration */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={config.title}
                onChange={(e) => setConfig(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Document title"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="author">Author</Label>
              <Input
                id="author"
                value={config.author}
                onChange={(e) => setConfig(prev => ({ ...prev, author: e.target.value }))}
                placeholder="Author name"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="filename">Custom Filename (optional)</Label>
            <Input
              id="filename"
              value={config.filename}
              onChange={(e) => setConfig(prev => ({ ...prev, filename: e.target.value }))}
              placeholder={`Leave empty for auto-generated filename`}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="keywords">Keywords</Label>
            <Input
              id="keywords"
              value={config.keywords}
              onChange={(e) => setConfig(prev => ({ ...prev, keywords: e.target.value }))}
              placeholder="Comma-separated keywords"
            />
          </div>

          {/* Advanced Options */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="include-metadata">Include Metadata</Label>
                <div className="text-sm text-muted-foreground">
                  Add document metadata, statistics, and creation info
                </div>
              </div>
              <Switch
                id="include-metadata"
                checked={config.includeMetadata}
                onCheckedChange={(checked) => setConfig(prev => ({ ...prev, includeMetadata: checked }))}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="include-timestamp">Include Timestamp</Label>
                <div className="text-sm text-muted-foreground">
                  Add timestamp to filename
                </div>
              </div>
              <Switch
                id="include-timestamp"
                checked={config.includeTimestamp}
                onCheckedChange={(checked) => setConfig(prev => ({ ...prev, includeTimestamp: checked }))}
              />
            </div>
          </div>

          {/* Custom Styles for HTML */}
          {selectedFormat === 'html' && (
            <div className="space-y-2">
              <Label htmlFor="custom-styles">Custom CSS (optional)</Label>
              <Textarea
                id="custom-styles"
                value={config.customStyles}
                onChange={(e) => setConfig(prev => ({ ...prev, customStyles: e.target.value }))}
                placeholder="Add custom CSS styles..."
                rows={4}
              />
            </div>
          )}
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <div className="flex gap-2 flex-1">
            <Button
              variant="outline"
              onClick={handleCopyToClipboard}
              disabled={isCopying || !content.content.trim()}
              className="flex-1"
            >
              {isCopying ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Copy className="h-4 w-4 mr-2" />
              )}
              Copy Text
            </Button>
            
            {selectedFormat === 'pdf' && (
              <Button
                variant="outline"
                onClick={handlePreview}
                disabled={isPreviewing || !content.content.trim()}
                className="flex-1"
              >
                {isPreviewing ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Eye className="h-4 w-4 mr-2" />
                )}
                Preview
              </Button>
            )}
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleExport}
              disabled={isExporting || !content.content.trim() || selectedFormat === 'docx'}
              className="bg-[#1279b4] hover:bg-[#00497A] text-white"
            >
              {isExporting ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Download className="h-4 w-4 mr-2" />
              )}
              Export {selectedFormat.toUpperCase()}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
