"use client";

import React, { useState, useEffect, useImper<PERSON><PERSON><PERSON><PERSON> } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import { contentEditSchema, ContentEditFormData } from "@/lib/validators";
import {
  cleanHtmlContentEnhanced,
  detectAvadaContent,
  detectPageBuilderContent,
} from "@/lib/markdownUtils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

import { Save, UploadCloud, Loader2, Eye, Code } from "lucide-react"; // Added Eye, Code icons

import { SyncedContent } from "@/types";
import { cn } from "@/lib/utils";
import VisualEditor from "@/components/CustomContent/ContentEditor/VisualEditor";
import AceHtmlEditor from "@/components/CustomContent/ContentEditor/AceHtmlEditor";

interface ContentEditFormProps {
  initialData: SyncedContent;
  onLocalSave: (data: ContentEditFormData) => void;
  onPushToWp: () => void; // Push action now triggered without data arg, uses latest saved state
  isSavingLocal: boolean;
  isPushingWp: boolean;
  canPush: boolean; // Can the push button be enabled (e.g., is connected)
  renderCategories?: () => React.ReactElement; // Optional render prop for categories
}

export interface ContentEditFormRef {
  submitForm: () => void;
  getFormState: () => {
    isDirty: boolean;
    isValid: boolean;
  };
  handlePushClick: () => void;
  getOriginalContent: () => string; // New method to get original content for WordPress push
}

export const ContentEditForm = React.forwardRef<
  ContentEditFormRef,
  ContentEditFormProps
>(
  (
    {
      initialData,
      onLocalSave,
      onPushToWp,
      isSavingLocal,
      isPushingWp,
      canPush,
      renderCategories,
    },
    ref
  ) => {
    // State for display mode: 'html' or 'visual'
    const [displayMode, setDisplayMode] = useState<"html" | "visual">("visual");

    // Store original content for WordPress push operations
    const [originalContent, setOriginalContent] = useState<string>("");

    const form = useForm<ContentEditFormData>({
      resolver: zodResolver(contentEditSchema),
      defaultValues: {
        title: initialData?.title || "",
        content: initialData?.content || "",
        // Ensure status is valid for the enum, default to draft if not
        status:
          initialData?.status &&
          ["publish", "draft", "pending"].includes(initialData.status)
            ? (initialData.status as "publish" | "draft" | "pending")
            : "draft",
      },
    });

    // Reset form if the initial data object changes identity (e.g., after fetch/push)
    useEffect(() => {
      if (!initialData) return; // Skip reset if initialData is undefined

      const originalContentString = initialData.content || "";

      // Store the original content for WordPress operations
      setOriginalContent(originalContentString);

      // Only clean content if it contains page builder shortcodes
      const shouldClean =
        detectAvadaContent(originalContentString) ||
        detectPageBuilderContent(originalContentString);
      const displayContent = shouldClean
        ? cleanHtmlContentEnhanced(originalContentString)
        : originalContentString;

      form.reset({
        title: initialData.title || "",
        content: displayContent, // Use cleaned content for display only if needed
        status: ["publish", "draft", "pending"].includes(initialData.status)
          ? (initialData.status as "publish" | "draft" | "pending")
          : "draft",
      });
    }, [initialData, form]);

    // Watch content field for bilateral live editing
    // This ensures content changes are immediately reflected across all UI elements
    const watchedContent = form.watch("content");

    // Force re-render when content changes to ensure bilateral sync
    useEffect(() => {
      // This effect ensures that content changes trigger immediate UI updates
      // Similar to how VisualEditor handles content prop changes
    }, [watchedContent]); // Dependency on the object itself

    // Handle local save: submit the form data
    const handleSaveSubmit = (data: ContentEditFormData) => {
      onLocalSave(data);
    };

    // Handle push: trigger the push action (assumes data is already saved locally)
    const handlePushClick = () => {
      onPushToWp();
    };

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      submitForm: () => {
        form.handleSubmit(handleSaveSubmit)();
      },
      getFormState: () => ({
        isDirty: form.formState.isDirty,
        isValid: form.formState.isValid,
      }),
      handlePushClick,
      getOriginalContent: () => originalContent,
    }));

    return (
      <Form {...form}>
        {/* Use form's handleSubmit for the local save button */}
        <form onSubmit={form.handleSubmit(handleSaveSubmit)}>
          <div className="space-y-6">
            {/* Categories, Status field and HTML/Markdown toggle on same row */}
            <div className="flex justify-between items-end gap-4">
              {/* Categories section */}
              {renderCategories && renderCategories()}

              {/* Status field */}
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-normal block mb-2 text-[#1279b4]">
                      Status
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={isSavingLocal || isPushingWp}
                    >
                      <FormControl>
                        <SelectTrigger className="w-[130px]">
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="publish">Publish</SelectItem>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="pending">Pending Review</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Title field */}
            <div className="bg-gray-50 dark:bg-background p-4 rounded-lg border">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium text-[#1279b4] dark:text-foreground">
                      Title
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter post title"
                        {...field}
                        disabled={isSavingLocal || isPushingWp}
                        className="mt-2 border-gray-300"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Content field */}
            <div className="bg-gray-50 dark:bg-background p-4 rounded-lg border">
              <FormField
                control={form.control}
                name="content"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex justify-between items-center mb-2">
                      <FormLabel className="font-medium text-[#1279b4] dark:text-foreground">
                        Content
                      </FormLabel>
                      <div className="flex flex-col items-end">
                        <div className="flex items-center">
                          <button
                            type="button"
                            onClick={() => setDisplayMode("html")}
                            className={`px-2 py-1 text-xs rounded-l-md ${
                              displayMode === "html"
                                ? "bg-[#1279b4] text-white"
                                : "bg-gray-200 text-gray-700"
                            }`}
                            disabled={isSavingLocal || isPushingWp}
                          >
                            HTML
                          </button>
                          <button
                            type="button"
                            onClick={() => setDisplayMode("visual")}
                            className={`px-2 py-1 text-xs rounded-r-md ${
                              displayMode === "visual"
                                ? "bg-[#1279b4] text-white"
                                : "bg-gray-200 text-gray-700"
                            }`}
                            disabled={isSavingLocal || isPushingWp}
                          >
                            Visual
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Conditional Rendering based on displayMode */}
                    {displayMode === "html" ? (
                      <FormControl>
                        <div className="h-[500px]">
                          <AceHtmlEditor
                            content={watchedContent || ""}
                            onChange={field.onChange}
                            placeholder="Enter content HTML..."
                            disabled={isSavingLocal || isPushingWp}
                            viewMode="code"
                          />
                        </div>
                      </FormControl>
                    ) : (
                      <FormControl>
                        <div className="h-[500px]">
                          <VisualEditor
                            content={watchedContent || ""}
                            onChange={field.onChange}
                            placeholder="Start editing your content..."
                            disabled={isSavingLocal || isPushingWp}
                          />
                        </div>
                      </FormControl>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Action Buttons moved to SourceContentColumn footer */}
          </div>
        </form>
      </Form>
    );
  }
);

ContentEditForm.displayName = "ContentEditForm";
