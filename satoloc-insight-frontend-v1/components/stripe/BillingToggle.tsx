"use client";

import React from "react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";

interface BillingToggleProps {
  isYearly: boolean;
  onToggle: (isYearly: boolean) => void;
  yearlyDiscount?: string;
  className?: string;
}

export function BillingToggle({
  isYearly,
  onToggle,
  yearlyDiscount = "17% off",
  className = "",
}: BillingToggleProps) {
  return (
    <div
      className={`flex items-center justify-center gap-4 p-4 bg-muted/50 rounded-lg ${className}`}
    >
      <Label
        htmlFor="billing-toggle"
        className={`text-sm font-medium cursor-pointer ${!isYearly ? "text-primary" : "text-muted-foreground"}`}
      >
        Monthly
      </Label>

      <div className="flex items-center gap-2">
        <Switch
          id="billing-toggle"
          checked={isYearly}
          onCheckedChange={onToggle}
          className="data-[state=checked]:bg-primary"
        />
      </div>

      <div className="flex items-center gap-2">
        <Label
          htmlFor="billing-toggle"
          className={`text-sm font-medium cursor-pointer ${isYearly ? "text-primary" : "text-muted-foreground"}`}
        >
          Yearly
        </Label>
        {isYearly && (
          <Badge
            variant="secondary"
            className="text-xs bg-green-100 text-green-800 border-green-200"
          >
            {yearlyDiscount}
          </Badge>
        )}
      </div>
    </div>
  );
}
