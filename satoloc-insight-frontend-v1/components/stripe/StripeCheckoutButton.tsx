"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Loader2, CreditCard } from "lucide-react";
//import { useStripeCheckout } from "@/hooks/use-stripe-checkout";

interface StripeCheckoutButtonProps {
  priceId: string;
  planName: string;
  billingInterval: "monthly" | "yearly";
  userId?: string | number;
  userEmail?: string;
  className?: string;
  children?: React.ReactNode;
  variant?:
    | "default"
    | "outline"
    | "secondary"
    | "ghost"
    | "link"
    | "destructive";
  size?: "default" | "sm" | "lg" | "icon";
  disabled?: boolean;
  successUrl?: string;
  cancelUrl?: string;
}

export function StripeCheckoutButton({
  priceId,
  planName,
  billingInterval,
  userId,
  userEmail,
  className,
  children,
  variant = "default",
  size = "default",
  disabled = false,
  successUrl,
  cancelUrl,
}: StripeCheckoutButtonProps) {
  //const { redirectToCheckout, isLoading } = useStripeCheckout();

  const handleCheckout = async () => {
    // For demo purposes, using placeholder values if user data not provided
    const checkoutData = {
      priceId,
      userId: userId || "demo-user",
      userEmail: userEmail || "<EMAIL>",
      planName,
      billingInterval,
      successUrl,
      cancelUrl,
    };

    //await redirectToCheckout(checkoutData);
  };

  return (
    <Button
      onClick={handleCheckout}
      disabled={disabled || !priceId}
      variant={variant}
      size={size}
      className={className}
    >
      {/*isLoading ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Processing...
        </>
      ) : (
        <>
          {children || (
            <>
              <CreditCard className="mr-2 h-4 w-4" />
              Subscribe to {planName}
            </>
          )}
        </>
      )*/}
    </Button>
  );
}
