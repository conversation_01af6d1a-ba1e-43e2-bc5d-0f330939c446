"use client";

import { useState } from "react";
import {
  Calendar,
  CreditCard,
  Settings,
  AlertCircle,
  CheckCircle,
  XCircle,
  Loader2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useSubscription } from "@/hooks/use-subscription";
import { toast } from "sonner";

export default function SubscriptionManager() {
  const {
    subscription,
    isLoading,
    error,
    createBillingPortalSession,
    cancelSubscription,
    isSubscriptionActive,
    planType,
  } = useSubscription();

  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200";
      case "trialing":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "canceled":
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-200";
      case "past_due":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4" />;
      case "trialing":
        return <AlertCircle className="h-4 w-4" />;
      case "canceled":
      case "cancelled":
        return <XCircle className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const handleManageBilling = async () => {
    setActionLoading("billing");
    try {
      await createBillingPortalSession(`${window.location.origin}/dashboard`);
    } catch (error) {
      console.error("Error opening billing portal:", error);
    } finally {
      setActionLoading(null);
    }
  };

  const handleCancelSubscription = async () => {
    if (!subscription) return;

    const confirmed = window.confirm(
      "Are you sure you want to cancel your subscription? It will remain active until the end of your current billing period."
    );

    if (!confirmed) return;

    setActionLoading("cancel");
    try {
      await cancelSubscription(subscription.stripe_subscription_id, true);
    } catch (error) {
      console.error("Error canceling subscription:", error);
    } finally {
      setActionLoading(null);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Subscription
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading subscription details...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Subscription
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
            <p className="text-red-600">Failed to load subscription details</p>
            <p className="text-sm text-gray-500 mt-1">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!subscription) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Subscription
          </CardTitle>
          <CardDescription>
            You don't have an active subscription
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-4">
              No active subscription found. Choose a plan to get started.
            </p>
            <Button onClick={() => (window.location.href = "/#pricing")}>
              View Pricing Plans
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="h-5 w-5" />
          Subscription
        </CardTitle>
        <CardDescription>Manage your subscription and billing</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Plan Information */}
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold text-lg">
              {planType
                ? planType.charAt(0).toUpperCase() + planType.slice(1)
                : "Unknown"}{" "}
              Plan
            </h3>
            <p className="text-sm text-gray-600">
              Your current subscription plan
            </p>
          </div>
          <Badge className={getStatusColor(subscription.status)}>
            {getStatusIcon(subscription.status)}
            <span className="ml-1 capitalize">{subscription.status}</span>
          </Badge>
        </div>

        <Separator />

        {/* Subscription Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="font-medium">Current Period:</span>
            </div>
            <p className="text-sm text-gray-600 ml-6">
              {formatDate(subscription.current_period_start)} -{" "}
              {formatDate(subscription.current_period_end)}
            </p>
          </div>

          {subscription.trial_end && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <AlertCircle className="h-4 w-4 text-blue-500" />
                <span className="font-medium">Trial Ends:</span>
              </div>
              <p className="text-sm text-gray-600 ml-6">
                {formatDate(subscription.trial_end)}
              </p>
            </div>
          )}

          {subscription.cancel_at_period_end && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <XCircle className="h-4 w-4 text-red-500" />
                <span className="font-medium">Cancels On:</span>
              </div>
              <p className="text-sm text-gray-600 ml-6">
                {formatDate(subscription.current_period_end)}
              </p>
            </div>
          )}
        </div>

        {/* Cancellation Notice */}
        {subscription.cancel_at_period_end && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-yellow-800">
                  Subscription Ending
                </h4>
                <p className="text-sm text-yellow-700 mt-1">
                  Your subscription will end on{" "}
                  {formatDate(subscription.current_period_end)}. You'll continue
                  to have access until then.
                </p>
              </div>
            </div>
          </div>
        )}

        <Separator />

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Button
            variant="outline"
            onClick={handleManageBilling}
            disabled={actionLoading === "billing"}
            className="flex items-center gap-2"
          >
            {actionLoading === "billing" ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Settings className="h-4 w-4" />
            )}
            {actionLoading === "billing" ? "Opening..." : "Manage Billing"}
          </Button>

          {isSubscriptionActive && !subscription.cancel_at_period_end && (
            <Button
              variant="outline"
              onClick={handleCancelSubscription}
              disabled={actionLoading === "cancel"}
              className="flex items-center gap-2 text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              {actionLoading === "cancel" ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <XCircle className="h-4 w-4" />
              )}
              {actionLoading === "cancel"
                ? "Canceling..."
                : "Cancel Subscription"}
            </Button>
          )}
        </div>

        {/* Additional Info */}
        <div className="text-xs text-gray-500">
          <p>Subscription ID: {subscription.stripe_subscription_id}</p>
          <p className="mt-1">
            Last updated: {formatDate(subscription.updated_at)}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
