"use client";

import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Check<PERSON>ircle, XCircle, Loader2 } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useSubscription } from "@/hooks/use-subscription";
import { toast } from "sonner";

export default function CheckoutSuccess() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { fetchSubscription } = useSubscription();

  const success = searchParams?.get("success");
  const canceled = searchParams?.get("canceled");

  useEffect(() => {
    if (success) {
      // Refresh subscription data after successful checkout
      fetchSubscription();
      toast.success("Subscription activated successfully!");
    } else if (canceled) {
      toast.error("Checkout was canceled");
    }
  }, [success, canceled, fetchSubscription]);

  if (success) {
    return (
      <Card className="max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <CheckCircle className="h-16 w-16 text-green-500" />
          </div>
          <CardTitle className="text-green-700">Payment Successful!</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-gray-600">
            Your subscription has been activated successfully. You now have
            access to all premium features.
          </p>
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={() => router.push("/dashboard")}
              className="flex-1"
            >
              Go to Dashboard
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push("/dashboard")}
              className="flex-1"
            >
              View Subscription
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (canceled) {
    return (
      <Card className="max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <XCircle className="h-16 w-16 text-red-500" />
          </div>
          <CardTitle className="text-red-700">Checkout Canceled</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-gray-600">
            Your checkout was canceled. No charges were made to your account.
          </p>
          <div className="flex flex-col sm:flex-row gap-3">
            <Button onClick={() => router.push("/#pricing")} className="flex-1">
              View Pricing
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push("/dashboard")}
              className="flex-1"
            >
              Back to Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="max-w-md mx-auto">
      <CardContent className="text-center py-8">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
        <p className="text-gray-600">Processing...</p>
      </CardContent>
    </Card>
  );
}
