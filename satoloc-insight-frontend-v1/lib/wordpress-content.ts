import sanitizeHtml from "sanitize-html";

export interface WordPressContentOptions {
  allowedTags?: string[];
  allowedAttributes?: { [key: string]: string[] };
  transformImages?: boolean;
  baseUrl?: string;
}

const defaultOptions: WordPressContentOptions = {
  allowedTags: [
    "p",
    "br",
    "strong",
    "b",
    "em",
    "i",
    "u",
    "strike",
    "del",
    "h1",
    "h2",
    "h3",
    "h4",
    "h5",
    "h6",
    "ul",
    "ol",
    "li",
    "a",
    "img",
    "figure",
    "figcaption",
    "blockquote",
    "cite",
    "code",
    "pre",
    "table",
    "thead",
    "tbody",
    "tr",
    "th",
    "td",
    "div",
    "span",
    "hr",
    // WordPress specific blocks
    "wp-block-quote",
    "wp-block-button",
    "wp-block-columns",
    "wp-block-column",
    "wp-block-image",
    "wp-block-gallery",
    "wp-block-code",
  ],
  allowedAttributes: {
    "*": ["class", "id", "style"],
    a: ["href", "title", "target", "rel"],
    img: ["src", "alt", "title", "width", "height", "class", "style"],
    figure: ["class", "style"],
    div: ["class", "style"],
    span: ["class", "style"],
    blockquote: ["class", "cite"],
    table: ["class", "style"],
    th: ["class", "style", "colspan", "rowspan"],
    td: ["class", "style", "colspan", "rowspan"],
  },
  transformImages: true,
};

/**
 * Sanitizes and processes WordPress content for safe display
 */
export function sanitizeWordPressContent(
  content: string,
  options: WordPressContentOptions = {}
): string {
  if (!content) return "";

  const opts = { ...defaultOptions, ...options };

  // First, let's fix common WordPress formatting issues
  let processedContent = content
    // Fix WordPress paragraph spacing
    .replace(/(<p[^>]*>)\s*(<\/p>)/g, "") // Remove empty paragraphs
    .replace(/(<p[^>]*>)\s*(&nbsp;|\s)*\s*(<\/p>)/g, "") // Remove paragraphs with only whitespace

    // Fix WordPress image alignment classes
    .replace(/class="([^"]*?)wp-image-\d+([^"]*?)"/g, 'class="$1$2"')
    .replace(/class="([^"]*?)size-\w+([^"]*?)"/g, 'class="$1$2"')

    // Ensure proper spacing around block elements
    .replace(/(<\/(?:h[1-6]|p|div|blockquote|ul|ol|table)>)(?!\s*<)/g, "$1\n")
    .replace(/(?<!>)(<(?:h[1-6]|p|div|blockquote|ul|ol|table)[^>]*>)/g, "\n$1")

    // Fix WordPress gallery shortcodes (if any remain)
    .replace(/\[gallery[^\]]*\]/g, "")

    // Fix WordPress caption shortcodes
    .replace(
      /\[caption[^\]]*\]([\s\S]*?)\[\/caption\]/g,
      '<figure class="wp-caption">$1</figure>'
    )

    // Clean up multiple consecutive line breaks
    .replace(/\n{3,}/g, "\n\n")
    .trim();

  // Apply image transformations if enabled
  if (opts.transformImages && opts.baseUrl) {
    processedContent = transformImageUrls(processedContent, opts.baseUrl);
  }

  // Sanitize the content using sanitize-html
  const sanitized = sanitizeHtml(processedContent, {
    allowedTags: opts.allowedTags || [],
    allowedAttributes: opts.allowedAttributes || {},
    allowedSchemes: ["http", "https", "ftp", "mailto"],
    allowedSchemesAppliedToAttributes: ["href", "src", "cite"],
    allowProtocolRelative: true,
  });

  return sanitized;
}

/**
 * Transforms relative image URLs to absolute URLs
 */
function transformImageUrls(content: string, baseUrl: string): string {
  return content.replace(
    /<img([^>]+)src="([^"]+)"([^>]*)>/g,
    (match, beforeSrc, src, afterSrc) => {
      // If the src is already absolute, leave it as is
      if (
        src.startsWith("http://") ||
        src.startsWith("https://") ||
        src.startsWith("//")
      ) {
        return match;
      }

      // Convert relative URL to absolute
      const absoluteSrc = new URL(src, baseUrl).toString();
      return `<img${beforeSrc}src="${absoluteSrc}"${afterSrc}>`;
    }
  );
}

/**
 * Extracts the first image from WordPress content
 */
export function extractFirstImage(content: string): string | null {
  const imgMatch = content.match(/<img[^>]+src="([^"]+)"[^>]*>/);
  return imgMatch ? imgMatch[1] : null;
}

/**
 * Strips HTML tags and returns plain text
 */
export function stripHtmlTags(content: string): string {
  return content.replace(/<[^>]*>/g, "").trim();
}

/**
 * Generates a reading time estimate
 */
export function calculateReadingTime(
  content: string,
  wordsPerMinute: number = 200
): number {
  const plainText = stripHtmlTags(content);
  const wordCount = plainText
    .split(/\s+/)
    .filter((word) => word.length > 0).length;
  return Math.ceil(wordCount / wordsPerMinute);
}

/**
 * Truncates content to a specified length while preserving HTML structure
 */
export function truncateWordPressContent(
  content: string,
  maxLength: number = 150
): string {
  const plainText = stripHtmlTags(content);
  if (plainText.length <= maxLength) return content;

  const truncated = plainText.substring(0, maxLength).trim();
  const lastSpace = truncated.lastIndexOf(" ");

  return lastSpace > 0
    ? truncated.substring(0, lastSpace) + "..."
    : truncated + "...";
}
