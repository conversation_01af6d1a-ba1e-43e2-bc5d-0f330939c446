/**
 * Export Utilities for Content Export
 * Provides reusable and expandable content export functionality
 */

import { convertHtmlToMarkdown } from './markdownUtils';

// Supported export formats
export type ExportFormat = 'pdf' | 'docx' | 'html' | 'markdown' | 'txt' | 'json';

// Export configuration interface
export interface ExportConfig {
  format: ExportFormat;
  filename?: string;
  title?: string;
  author?: string;
  subject?: string;
  keywords?: string;
  includeMetadata?: boolean;
  includeTimestamp?: boolean;
  customStyles?: string;
}

// Content data structure for export
export interface ExportContent {
  title?: string;
  content: string;
  metadata?: {
    author?: string;
    createdAt?: Date;
    modifiedAt?: Date;
    keywords?: string;
    tone?: string;
    language?: string;
    prompt?: string;
    wordCount?: number;
    characterCount?: number;
  };
}

/**
 * Clean HTML content for export
 * Removes editor-specific classes and attributes
 */
export function cleanHtmlForExport(html: string): string {
  // Create a temporary DOM element to clean the HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;

  // Remove editor-specific attributes and classes
  const elementsToClean = tempDiv.querySelectorAll('*');
  elementsToClean.forEach((element) => {
    const htmlElement = element as HTMLElement;
    
    // Remove editor-specific attributes
    htmlElement.removeAttribute('contenteditable');
    htmlElement.removeAttribute('data-visual-editor-handlers');
    htmlElement.removeAttribute('spellcheck');
    
    // Clean up classes - remove editor-specific ones
    const classList = Array.from(htmlElement.classList);
    const cleanClasses = classList.filter(className => 
      !className.includes('editor-') &&
      !className.includes('visual-') &&
      !className.includes('hover-') &&
      !className.includes('selected-') &&
      !className.includes('focus-')
    );
    
    // Reset classes
    htmlElement.className = cleanClasses.join(' ');
    
    // Remove empty class attributes
    if (!htmlElement.className.trim()) {
      htmlElement.removeAttribute('class');
    }
    
    // Clean up inline styles that are editor-specific
    if (htmlElement.style) {
      // Remove editor-specific styles
      htmlElement.style.removeProperty('outline');
      htmlElement.style.removeProperty('border');
      htmlElement.style.removeProperty('box-shadow');
      
      // Remove empty style attributes
      if (!htmlElement.style.cssText.trim()) {
        htmlElement.removeAttribute('style');
      }
    }
  });

  return tempDiv.innerHTML;
}

/**
 * Extract text content from HTML
 */
export function extractTextContent(html: string): string {
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;
  return tempDiv.textContent || tempDiv.innerText || '';
}

/**
 * Calculate content statistics
 */
export function calculateContentStats(content: string, isHtml: boolean = true): {
  wordCount: number;
  characterCount: number;
  characterCountNoSpaces: number;
  paragraphCount: number;
} {
  const textContent = isHtml ? extractTextContent(content) : content;
  
  const words = textContent.trim().split(/\s+/).filter(word => word.length > 0);
  const characters = textContent.length;
  const charactersNoSpaces = textContent.replace(/\s/g, '').length;
  
  // Count paragraphs (split by double newlines or <p> tags)
  let paragraphCount = 0;
  if (isHtml) {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;
    paragraphCount = tempDiv.querySelectorAll('p, div, h1, h2, h3, h4, h5, h6').length;
  } else {
    paragraphCount = content.split(/\n\s*\n/).filter(p => p.trim().length > 0).length;
  }
  
  return {
    wordCount: words.length,
    characterCount: characters,
    characterCountNoSpaces: charactersNoSpaces,
    paragraphCount: Math.max(paragraphCount, 1) // At least 1 paragraph
  };
}

/**
 * Generate filename with timestamp
 */
export function generateFilename(
  baseName: string, 
  format: ExportFormat, 
  includeTimestamp: boolean = true
): string {
  const cleanBaseName = baseName
    .replace(/[^a-zA-Z0-9\s-_]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .toLowerCase();
  
  const timestamp = includeTimestamp 
    ? `-${new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-')}`
    : '';
  
  return `${cleanBaseName}${timestamp}.${format}`;
}

/**
 * Prepare content for export based on format
 */
export function prepareContentForExport(
  exportContent: ExportContent,
  config: ExportConfig
): {
  processedContent: string;
  filename: string;
  metadata: any;
} {
  const { content, title, metadata } = exportContent;
  const { format, includeTimestamp = true, includeMetadata = true } = config;
  
  let processedContent = content;
  let filename = config.filename || generateFilename(
    title || 'content',
    format,
    includeTimestamp
  );
  
  // Process content based on format
  switch (format) {
    case 'html':
      processedContent = cleanHtmlForExport(content);
      break;
      
    case 'markdown':
      processedContent = convertHtmlToMarkdown(content);
      break;
      
    case 'txt':
      processedContent = extractTextContent(content);
      break;
      
    case 'json':
      const jsonData = {
        title: title || 'Untitled',
        content: extractTextContent(content), // Use clean text instead of HTML
        ...(includeMetadata && metadata ? {
          metadata: {
            ...metadata,
            stats: calculateContentStats(content, true),
            exportedAt: new Date().toISOString(),
          }
        } : {})
      };
      processedContent = JSON.stringify(jsonData, null, 2);
      break;
      
    case 'pdf':
    case 'docx':
      // These formats require special processing in their respective handlers
      processedContent = cleanHtmlForExport(content);
      break;
      
    default:
      processedContent = cleanHtmlForExport(content);
  }
  
  // Prepare metadata
  const exportMetadata = {
    title: title || 'Untitled',
    author: config.author || 'Content Creator',
    subject: config.subject || title || 'Generated Content',
    keywords: config.keywords || metadata?.keywords || '',
    createdAt: metadata?.createdAt || new Date(),
    exportedAt: new Date(),
    format,
    ...(includeMetadata && metadata ? metadata : {}),
    stats: calculateContentStats(content, true),
  };
  
  return {
    processedContent,
    filename,
    metadata: exportMetadata,
  };
}

/**
 * Download file utility
 */
export function downloadFile(
  content: string | Blob,
  filename: string,
  mimeType: string = 'text/plain'
): void {
  const blob = content instanceof Blob ? content : new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.style.display = 'none';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up the URL object
  setTimeout(() => URL.revokeObjectURL(url), 100);
}

/**
 * Copy content to clipboard
 */
export async function copyToClipboard(content: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(content);
    return true;
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    
    // Fallback method
    try {
      const textArea = document.createElement('textarea');
      textArea.value = content;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      const result = document.execCommand('copy');
      document.body.removeChild(textArea);
      return result;
    } catch (fallbackError) {
      console.error('Fallback copy method also failed:', fallbackError);
      return false;
    }
  }
}
