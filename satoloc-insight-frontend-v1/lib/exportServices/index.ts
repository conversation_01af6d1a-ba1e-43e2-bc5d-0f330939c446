/**
 * Export Services Manager
 * Central hub for all export functionality with extensible architecture
 */

import {
  ExportContent,
  ExportConfig,
  ExportFormat,
  prepareContentForExport,
  downloadFile,
  copyToClipboard,
} from "../exportUtils";
import { exportToPDF, generatePDFPreview } from "./pdfExport";

// Export service interface for extensibility
export interface ExportService {
  format: ExportFormat;
  name: string;
  description: string;
  mimeType: string;
  export: (content: ExportContent, config: ExportConfig) => Promise<void>;
  preview?: (content: ExportContent, config: ExportConfig) => Promise<string>;
}

// HTML Export Service
const htmlExportService: ExportService = {
  format: "html",
  name: "HTML Document",
  description: "Export as a complete HTML document with styling",
  mimeType: "text/html",
  export: async (content: ExportContent, config: ExportConfig) => {
    const { processedContent, filename } = prepareContentForExport(
      content,
      config
    );

    const htmlDocument = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${content.title || "Exported Content"}</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        line-height: 1.6;
        max-width: 800px;
        margin: 0 auto;
        padding: 40px 20px;
        color: #333;
        background-color: #fff;
      }
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        color: #383838;
        margin-bottom: 0.5em;
      }
      h1 {
        font-size: 2.5em;
        border-bottom: 2px solid #1279b4;
        padding-bottom: 0.3em;
      }
      h2 {
        font-size: 2em;
      }
      h3 {
        font-size: 1.5em;
      }
      p {
        margin-bottom: 1em;
        text-align: justify;
        font-size: 1rem;
      }
      blockquote {
        border-left: 4px solid #1279b4;
        margin: 1em 0;
        padding: 0.5em 1em;
        background-color: #f8f9fa;
        font-style: italic;
      }
      code {
        background-color: #f4f4f4;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: "Courier New", monospace;
      }
      pre {
        background-color: #f8f8f8;
        padding: 1em;
        border-radius: 5px;
        overflow-x: auto;
        border: 1px solid #e0e0e0;
      }
      img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 1em auto;
      }
      ul,
      ol {
        margin-bottom: 1em;
      }
      li {
        margin-bottom: 0.5em;
      }
      a {
        color: #1279b4;
        text-decoration: none;
      }
      a:hover {
        text-decoration: underline;
      }
      .header {
        text-align: center;
        margin-bottom: 3em;
        border-bottom: 2px solid #1279b4;
        padding-bottom: 1em;
      }
      .metadata {
        font-size: 0.9em;
        color: #666;
        margin-top: 1em;
      }
      .footer {
        margin-top: 3em;
        padding-top: 1em;
        border-top: 1px solid #e0e0e0;
        text-align: center;
        font-size: 0.9em;
        color: #666;
      }
        ${config.customStyles || ""}
    </style>
</head>
<body>
    <div class="header">
        ${content.metadata?.author ? `<p class="metadata">by ${content.metadata.author}</p>` : ""}
        ${config.includeMetadata !== false ? `<p class="metadata">Exported on ${new Date().toLocaleDateString()}</p>` : ""}
    </div>
    
    <div class="content">
        ${processedContent}
    </div>
    
    <div class="footer">
        <p>Satolo Insight Custom Content</p>
        ${content.metadata?.keywords ? `<p>Keywords: ${content.metadata.keywords}</p>` : ""}
    </div>
</body>
</html>`;

    downloadFile(htmlDocument, filename, "text/html");
  },
};

// Markdown Export Service
const markdownExportService: ExportService = {
  format: "markdown",
  name: "Markdown Document",
  description: "Export as Markdown format for documentation and publishing",
  mimeType: "text/markdown",
  export: async (content: ExportContent, config: ExportConfig) => {
    const { processedContent, filename, metadata } = prepareContentForExport(
      content,
      config
    );

    let markdownDocument = "";

    // Add frontmatter if metadata is included
    if (config.includeMetadata !== false && content.metadata) {
      markdownDocument += "---\n";
      markdownDocument += `title: "${content.title || "Exported Content"}"\n`;
      if (content.metadata.author)
        markdownDocument += `author: "${content.metadata.author}"\n`;
      if (content.metadata.keywords)
        markdownDocument += `keywords: "${content.metadata.keywords}"\n`;
      if (content.metadata.language)
        markdownDocument += `language: "${content.metadata.language}"\n`;
      markdownDocument += `date: "${new Date().toISOString()}"\n`;
      markdownDocument += "---\n\n";
    }

    // Add title if not in frontmatter
    if (content.title && config.includeMetadata === false) {
      markdownDocument += `# ${content.title}\n\n`;
    }

    // Add content
    markdownDocument += processedContent;

    // Add footer
    if (config.includeMetadata !== false) {
      markdownDocument += "\n\n---\n\n";
      markdownDocument += "*Generated with SatoLoc Content Creator*\n";
      if (content.metadata?.keywords) {
        markdownDocument += `\n**Keywords:** ${content.metadata.keywords}\n`;
      }
    }

    downloadFile(markdownDocument, filename, "text/markdown");
  },
};

// Plain Text Export Service
const textExportService: ExportService = {
  format: "txt",
  name: "Plain Text",
  description: "Export as plain text without formatting",
  mimeType: "text/plain",
  export: async (content: ExportContent, config: ExportConfig) => {
    const { processedContent, filename } = prepareContentForExport(
      content,
      config
    );

    let textDocument = "";

    // Add title
    if (content.title) {
      textDocument += `${content.title.toUpperCase()}\n`;
      textDocument += "=".repeat(content.title.length) + "\n\n";
    }

    // Add metadata
    if (config.includeMetadata !== false && content.metadata) {
      if (content.metadata.author)
        textDocument += `Author: ${content.metadata.author}\n`;
      textDocument += `Exported: ${new Date().toLocaleDateString()}\n\n`;
    }

    // Add content
    textDocument += processedContent;

    // Add footer
    if (config.includeMetadata !== false) {
      textDocument += "\n\n" + "-".repeat(50) + "\n";
      textDocument += "Generated with SatoLoc Content Creator\n";
      if (content.metadata?.keywords) {
        textDocument += `Keywords: ${content.metadata.keywords}\n`;
      }
    }

    downloadFile(textDocument, filename, "text/plain");
  },
};

// JSON Export Service
const jsonExportService: ExportService = {
  format: "json",
  name: "JSON Data",
  description: "Export as structured JSON data with metadata",
  mimeType: "application/json",
  export: async (content: ExportContent, config: ExportConfig) => {
    const { processedContent, filename } = prepareContentForExport(
      content,
      config
    );
    downloadFile(processedContent, filename, "application/json");
  },
};

// PDF Export Service
const pdfExportService: ExportService = {
  format: "pdf",
  name: "PDF Document",
  description: "Export as professional PDF document with formatting",
  mimeType: "application/pdf",
  export: exportToPDF,
  preview: generatePDFPreview,
};

// DOCX Export Service (placeholder for future implementation)
const docxExportService: ExportService = {
  format: "docx",
  name: "Word Document",
  description: "Export as Microsoft Word document (coming soon)",
  mimeType:
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  export: async (content: ExportContent, config: ExportConfig) => {
    throw new Error(
      "DOCX export is not yet implemented. Please use PDF or HTML export instead."
    );
  },
};

// Registry of all export services
const exportServices: Map<ExportFormat, ExportService> = new Map([
  ["html", htmlExportService],
  ["markdown", markdownExportService],
  ["txt", textExportService],
  ["json", jsonExportService],
  ["pdf", pdfExportService],
  ["docx", docxExportService],
]);

// Main export function
export async function exportContent(
  content: ExportContent,
  config: ExportConfig
): Promise<void> {
  const service = exportServices.get(config.format);

  if (!service) {
    throw new Error(`Export format '${config.format}' is not supported`);
  }

  try {
    await service.export(content, config);
  } catch (error) {
    console.error(`Export failed for format ${config.format}:`, error);
    throw error;
  }
}

// Preview function (for formats that support it)
export async function previewContent(
  content: ExportContent,
  config: ExportConfig
): Promise<string | null> {
  const service = exportServices.get(config.format);

  if (!service || !service.preview) {
    return null;
  }

  try {
    return await service.preview(content, config);
  } catch (error) {
    console.error(`Preview failed for format ${config.format}:`, error);
    throw error;
  }
}

// Get available export formats
export function getAvailableFormats(): ExportService[] {
  return Array.from(exportServices.values());
}

// Get specific export service
export function getExportService(
  format: ExportFormat
): ExportService | undefined {
  return exportServices.get(format);
}

// Register new export service (for extensibility)
export function registerExportService(service: ExportService): void {
  exportServices.set(service.format, service);
}

// Copy content to clipboard
export async function copyContentToClipboard(
  content: ExportContent,
  format: ExportFormat = "txt"
): Promise<boolean> {
  try {
    const config: ExportConfig = { format, includeMetadata: false };
    const { processedContent } = prepareContentForExport(content, config);

    return await copyToClipboard(processedContent);
  } catch (error) {
    console.error("Failed to copy content to clipboard:", error);
    return false;
  }
}

// Batch export (multiple formats)
export async function batchExport(
  content: ExportContent,
  formats: ExportFormat[],
  baseConfig: Omit<ExportConfig, "format"> = {}
): Promise<{
  success: ExportFormat[];
  failed: { format: ExportFormat; error: string }[];
}> {
  const success: ExportFormat[] = [];
  const failed: { format: ExportFormat; error: string }[] = [];

  for (const format of formats) {
    try {
      await exportContent(content, { ...baseConfig, format });
      success.push(format);
    } catch (error) {
      failed.push({
        format,
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  return { success, failed };
}
