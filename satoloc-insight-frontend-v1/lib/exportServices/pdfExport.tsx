/**
 * PDF Export Service using @react-pdf/renderer
 * Provides professional PDF generation with customizable styling
 */

import React from "react";
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  Font,
  pdf,
  Image as PDFImage,
  Link,
} from "@react-pdf/renderer";
import { ExportContent, ExportConfig, downloadFile } from "../exportUtils";

// Use built-in fonts for reliability
// @react-pdf/renderer has these built-in fonts: Helvetica, Times-Roman, Courier
// We'll use Helvetica family which includes regular, bold, oblique, and bold-oblique

// Helper function to convert image URL to base64
async function convertImageToBase64(imageUrl: string): Promise<string> {
  try {
    // Create a canvas to convert the image
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const img = new Image();

    // Enable CORS for cross-origin images
    img.crossOrigin = "anonymous";

    return new Promise((resolve, reject) => {
      img.onload = () => {
        try {
          // Set canvas dimensions to image dimensions
          canvas.width = img.naturalWidth;
          canvas.height = img.naturalHeight;

          // Draw image to canvas
          ctx?.drawImage(img, 0, 0);

          // Convert to base64
          const base64 = canvas.toDataURL("image/png");

          resolve(base64);
        } catch (error) {
          console.error("❌ DEBUG: Error drawing image to canvas:", error);
          reject(error);
        }
      };

      img.onerror = (error) => {
        console.error("❌ DEBUG: Error loading image:", error);
        reject(new Error(`Failed to load image: ${imageUrl}`));
      };

      // Start loading the image
      img.src = imageUrl;
    });
  } catch (error) {
    console.error("❌ DEBUG: Error in convertImageToBase64:", error);
    throw error;
  }
}

// PDF Styles
const styles = StyleSheet.create({
  page: {
    flexDirection: "column",
    backgroundColor: "#ffffff",
    padding: 40,
    fontFamily: "Helvetica",
    fontSize: 11,
    lineHeight: 1.6,
  },

  // Header styles
  header: {
    marginBottom: 30,
    borderBottom: "2px solid #1279b4",
    paddingBottom: 15,
  },

  title: {
    fontSize: 24,
    fontWeight: 700,
    color: "#1279b4",
    marginBottom: 8,
    textAlign: "center",
  },

  subtitle: {
    fontSize: 12,
    color: "#666666",
    textAlign: "center",
    marginBottom: 5,
  },

  metadata: {
    fontSize: 9,
    color: "#888888",
    textAlign: "center",
    marginTop: 10,
  },

  // Content styles
  content: {
    flex: 1,
    marginBottom: 20,
  },

  // Typography styles
  h1: {
    fontSize: 20,
    fontWeight: 700,
    color: "#1279b4",
    marginTop: 20,
    marginBottom: 12,
    borderBottom: "1px solid #e0e0e0",
    paddingBottom: 4,
  },

  h2: {
    fontSize: 18,
    fontWeight: 700,
    color: "#2c3e50",
    marginTop: 18,
    marginBottom: 10,
  },

  h3: {
    fontSize: 16,
    fontWeight: 600,
    color: "#34495e",
    marginTop: 16,
    marginBottom: 8,
  },

  h4: {
    fontSize: 14,
    fontWeight: 600,
    color: "#34495e",
    marginTop: 14,
    marginBottom: 6,
  },

  h5: {
    fontSize: 13,
    fontWeight: 600,
    color: "#34495e",
    marginTop: 12,
    marginBottom: 6,
  },

  h6: {
    fontSize: 12,
    fontWeight: 600,
    color: "#34495e",
    marginTop: 10,
    marginBottom: 4,
  },

  paragraph: {
    fontSize: 11,
    lineHeight: 1.6,
    marginBottom: 12,
    textAlign: "justify",
    color: "#333333",
  },

  // List styles
  listContainer: {
    marginBottom: 12,
  },

  listItem: {
    fontSize: 11,
    lineHeight: 1.6,
    marginBottom: 4,
    paddingLeft: 15,
    color: "#333333",
  },

  // Quote styles
  blockquote: {
    backgroundColor: "#f8f9fa",
    borderLeft: "4px solid #1279b4",
    paddingLeft: 15,
    paddingRight: 15,
    paddingTop: 10,
    paddingBottom: 10,
    marginBottom: 12,
    fontStyle: "italic",
    color: "#555555",
  },

  // Code styles
  code: {
    fontFamily: "Courier-Bold",
    fontSize: 10,
    backgroundColor: "#f4f4f4",
    padding: 2,
  },

  codeBlock: {
    fontFamily: "Courier",
    fontSize: 10,
    backgroundColor: "#f8f8f8",
    padding: 12,
    marginBottom: 12,
    border: "1px solid #e0e0e0",
  },

  // Link styles
  link: {
    color: "#1279b4",
    textDecoration: "underline",
  },

  // Image styles
  image: {
    maxWidth: "100%",
    marginBottom: 12,
    alignSelf: "center",
  },

  imagePlaceholder: {
    backgroundColor: "#f8f9fa",
    border: "1px solid #e0e0e0",
    padding: 12,
    marginBottom: 12,
    alignItems: "center",
  },

  imagePlaceholderText: {
    fontSize: 10,
    color: "#666666",
    fontStyle: "italic",
    marginBottom: 4,
  },

  imageUrl: {
    fontSize: 8,
    color: "#888888",
    fontFamily: "Courier",
  },

  // Footer styles
  footer: {
    marginTop: 20,
    paddingTop: 15,
    borderTop: "1px solid #e0e0e0",
    fontSize: 9,
    color: "#888888",
    textAlign: "center",
  },

  // Stats section
  stats: {
    flexDirection: "row",
    justifyContent: "space-around",
    backgroundColor: "#f8f9fa",
    padding: 12,
    marginBottom: 20,
    borderRadius: 4,
    border: "1px solid #e0e0e0",
  },

  statItem: {
    alignItems: "center",
  },

  statValue: {
    fontSize: 14,
    fontWeight: 700,
    color: "#1279b4",
  },

  statLabel: {
    fontSize: 9,
    color: "#666666",
    marginTop: 2,
  },
});

// HTML to PDF Component converter
interface PDFContentProps {
  content: string;
  title?: string;
  metadata?: any;
  includeStats?: boolean;
}

// Parse HTML and convert to PDF components
function parseHTMLToPDFComponents(html: string): React.ReactNode[] {
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = html;

  // Debug: Log all images found in the HTML
  const images = tempDiv.querySelectorAll("img");

  images.forEach((img, index) => {
    const imgElement = img as HTMLImageElement;
  });

  const components: React.ReactNode[] = [];
  let key = 0;

  const processNode = (node: Node): React.ReactNode | null => {
    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent?.trim();
      return text ? <Text key={key++}>{text}</Text> : null;
    }

    if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as Element;
      const tagName = element.tagName.toLowerCase();
      const textContent = element.textContent?.trim() || "";

      if (!textContent) return null;

      switch (tagName) {
        case "h1":
          return (
            <Text key={key++} style={styles.h1}>
              {textContent}
            </Text>
          );
        case "h2":
          return (
            <Text key={key++} style={styles.h2}>
              {textContent}
            </Text>
          );
        case "h3":
          return (
            <Text key={key++} style={styles.h3}>
              {textContent}
            </Text>
          );
        case "h4":
          return (
            <Text key={key++} style={styles.h4}>
              {textContent}
            </Text>
          );
        case "h5":
          return (
            <Text key={key++} style={styles.h5}>
              {textContent}
            </Text>
          );
        case "h6":
          return (
            <Text key={key++} style={styles.h6}>
              {textContent}
            </Text>
          );
        case "p":
          return (
            <Text key={key++} style={styles.paragraph}>
              {textContent}
            </Text>
          );
        case "blockquote":
          return (
            <View key={key++} style={styles.blockquote}>
              <Text>{textContent}</Text>
            </View>
          );
        case "ul":
        case "ol":
          const listItems = Array.from(element.querySelectorAll("li")).map(
            (li, index) => (
              <Text key={`${key++}-${index}`} style={styles.listItem}>
                {tagName === "ul" ? "• " : `${index + 1}. `}
                {li.textContent?.trim()}
              </Text>
            )
          );
          return (
            <View key={key++} style={styles.listContainer}>
              {listItems}
            </View>
          );
        case "code":
          return (
            <Text key={key++} style={styles.code}>
              {textContent}
            </Text>
          );
        case "pre":
          return (
            <View key={key++} style={styles.codeBlock}>
              <Text>{textContent}</Text>
            </View>
          );
        case "a":
          const href = element.getAttribute("href");
          return href ? (
            <Link key={key++} src={href} style={styles.link}>
              {textContent}
            </Link>
          ) : (
            <Text key={key++}>{textContent}</Text>
          );
        case "img":
          const src = element.getAttribute("src");
          const alt = element.getAttribute("alt") || "Image";
          const width = element.getAttribute("width");
          const height = element.getAttribute("height");

          if (src) {
            // Check if image is base64 data URL (which works reliably)
            const isBase64 = src.startsWith("data:image/");
            const isHttps = src.startsWith("https://");

            if (isBase64) {
              try {
                return (
                  <View key={key++}>
                    <PDFImage src={src} style={styles.image} />
                    {alt && <Text style={styles.subtitle}>{alt}</Text>}
                  </View>
                );
              } catch (error) {
                console.error("❌ DEBUG: Error rendering base64 image:", error);
              }
            } else if (isHttps) {
              // Try to render external image, but it might not work
              try {
                return (
                  <View key={key++}>
                    <PDFImage src={src} style={styles.image} />
                    {alt && <Text style={styles.subtitle}>{alt}</Text>}
                  </View>
                );
              } catch (error) {
                console.error(
                  "❌ DEBUG: Error rendering external image:",
                  error
                );
              }
            }

            // Fallback: show placeholder with image info

            return (
              <View key={key++} style={styles.imagePlaceholder}>
                <Text style={styles.imagePlaceholderText}>[Image: {alt}]</Text>
                <Text style={styles.imageUrl}>
                  Source: {src.substring(0, 60)}...
                </Text>
                <Text style={styles.imageUrl}>
                  Note: External images may not display in PDF
                </Text>
              </View>
            );
          } else {
            return null;
          }
        case "strong":
        case "b":
          return (
            <Text key={key++} style={{ fontWeight: 700 }}>
              {textContent}
            </Text>
          );
        case "em":
        case "i":
          return (
            <Text key={key++} style={{ fontStyle: "italic" }}>
              {textContent}
            </Text>
          );
        default:
          // For other elements, just return the text content
          return textContent ? (
            <Text key={key++} style={styles.paragraph}>
              {textContent}
            </Text>
          ) : null;
      }
    }

    return null;
  };

  // Process all child nodes
  Array.from(tempDiv.childNodes).forEach((node) => {
    const component = processNode(node);
    if (component) {
      components.push(component);
    }
  });

  return components;
}

// PDF Document Component
const PDFDocument: React.FC<PDFContentProps> = ({
  content,
  title,
  metadata,
  includeStats = true,
}) => {
  const pdfComponents = parseHTMLToPDFComponents(content);

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <View style={styles.header}>
          {title && <Text style={styles.title}>{title}</Text>}
          {metadata?.author && (
            <Text style={styles.subtitle}>by {metadata.author}</Text>
          )}
          {metadata?.exportedAt && (
            <Text style={styles.metadata}>
              Generated on{" "}
              {new Date(metadata.exportedAt).toLocaleDateString("en-US", {
                year: "numeric",
                month: "long",
                day: "numeric",
                hour: "2-digit",
                minute: "2-digit",
              })}
            </Text>
          )}
        </View>

        {/* Content Statistics */}
        {includeStats && metadata?.stats && (
          <View style={styles.stats}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{metadata.stats.wordCount}</Text>
              <Text style={styles.statLabel}>Words</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {metadata.stats.characterCount}
              </Text>
              <Text style={styles.statLabel}>Characters</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {metadata.stats.paragraphCount}
              </Text>
              <Text style={styles.statLabel}>Paragraphs</Text>
            </View>
          </View>
        )}

        {/* Main Content */}
        <View style={styles.content}>{pdfComponents}</View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text>
            {metadata?.keywords && `Keywords: ${metadata.keywords} • `}
            Generated with SatoLoc Content Creator
          </Text>
        </View>
      </Page>
    </Document>
  );
};

// PDF Export Function
export async function exportToPDF(
  exportContent: ExportContent,
  config: ExportConfig = { format: "pdf" }
): Promise<void> {
  try {
    const { title, content, metadata } = exportContent;

    // Validate content
    if (!content || content.trim().length === 0) {
      throw new Error("No content to export. Please add some content first.");
    }

    // Generate PDF blob with timeout
    const pdfPromise = pdf(
      <PDFDocument
        content={content}
        title={title}
        metadata={{
          ...metadata,
          author: config.author || metadata?.author || "Content Creator",
          subject: config.subject || title || "Generated Content",
          keywords: config.keywords || metadata?.keywords || "",
          exportedAt: new Date(),
        }}
        includeStats={config.includeMetadata !== false}
      />
    ).toBlob();

    // Add timeout to prevent hanging
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error("PDF generation timed out")), 30000);
    });

    const pdfBlob = await Promise.race([pdfPromise, timeoutPromise]);

    // Generate filename
    const filename =
      config.filename ||
      `${(title || "content")
        .replace(/[^a-zA-Z0-9\s-_]/g, "")
        .replace(/\s+/g, "-")
        .toLowerCase()}-${new Date().toISOString().slice(0, 19).replace(/[:.]/g, "-")}.pdf`;

    // Download the PDF
    downloadFile(pdfBlob, filename, "application/pdf");
  } catch (error) {
    console.error("PDF export failed:", error);

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes("font") || error.message.includes("Font")) {
        throw new Error(
          "PDF generation failed due to font loading issues. Please try again."
        );
      }
      if (error.message.includes("timeout")) {
        throw new Error(
          "PDF generation timed out. Please try with shorter content."
        );
      }
      if (
        error.message.includes("DataView") ||
        error.message.includes("Offset")
      ) {
        throw new Error(
          "PDF generation failed due to content formatting issues. Please try simplifying your content."
        );
      }
    }

    throw new Error(
      "Failed to generate PDF. Please try again or contact support."
    );
  }
}

// PDF Preview Function (returns blob URL for preview)
export async function generatePDFPreview(
  exportContent: ExportContent,
  config: ExportConfig = { format: "pdf" }
): Promise<string> {
  try {
    const { title, content, metadata } = exportContent;

    const pdfBlob = await pdf(
      <PDFDocument
        content={content}
        title={title}
        metadata={{
          ...metadata,
          author: config.author || metadata?.author || "Content Creator",
          subject: config.subject || title || "Generated Content",
          keywords: config.keywords || metadata?.keywords || "",
          exportedAt: new Date(),
        }}
        includeStats={config.includeMetadata !== false}
      />
    ).toBlob();

    return URL.createObjectURL(pdfBlob);
  } catch (error) {
    console.error("PDF preview generation failed:", error);
    throw new Error("Failed to generate PDF preview. Please try again.");
  }
}
