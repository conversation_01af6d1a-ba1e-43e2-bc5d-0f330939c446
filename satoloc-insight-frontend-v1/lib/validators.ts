import * as z from "zod";

// Schema for the connection form
export const connectionSchema = z.object({
  baseUrl: z
    .string()
    .url({
      message:
        "Please enter a valid WordPress site URL (e.g., https://example.com).",
    })
    .min(1),
  apiKey: z.string().min(10, { message: "API Key seems too short." }), // Basic length check
  agreeToTerms: z.boolean().refine((val) => val === true, {
    message: "You must agree to the Terms of Service",
  }),
  connectionOnly: z.boolean().optional(), // Optional flag to indicate connecting to existing site
});
export type ConnectionFormData = z.infer<typeof connectionSchema>;

// Schema for the content editing form
export const contentEditSchema = z.object({
  title: z.string().min(1, { message: "Title is required." }),
  content: z.string().optional(), // Allow empty content
  status: z.enum(["publish", "draft", "pending"], {
    required_error: "Status is required.",
    invalid_type_error: "Invalid status selected.",
  }),
});
export type ContentEditFormData = z.infer<typeof contentEditSchema>;
