// frontend/lib/api.ts - DEPRECATED: Use apiClient.ts instead
// This file is kept for backward compatibility during transition

import {
  fetchContentItems as fetchLocalContent,
  fetchContentItem as fetchLocalContentItem,
  updateContentItem as updateLocalContentItem,
  deleteContentItem as deleteLocalContentItem,
  syncAllFromWordPress as triggerDjangoFetchAll,
  pushContentToWordPress as triggerDjangoPush,
  deleteContentFromWordPress as triggerDjangoDelete,
  getTranslationPreview as fetchAiTranslationPreview,
  createTranslation as triggerDjangoTranslateAndCreate,
  verifyWpConnection,
  fetchWpCategoriesForConnection,
} from "./apiClient";

// Re-export all functions for backward compatibility
export {
  fetchLocalContent,
  fetchLocalContentItem,
  updateLocalContentItem,
  deleteLocalContentItem,
  triggerDjangoFetchAll,
  triggerDjangoPush,
  triggerDjangoDelete,
  fetchAiTranslationPreview,
  triggerDjangoTranslateAndCreate,
  verifyWpConnection,
  fetchWpCategoriesForConnection,
};
