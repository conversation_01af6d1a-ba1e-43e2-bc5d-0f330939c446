import apiClient from "./apiClient";

/**
 * Fetch SEO data for a website by URL
 * @param url The website URL to fetch data for
 * @returns The website data including SEO metrics, technical analysis, and competitors
 */
export const fetchWebsiteByUrl = async (url: string) => {
  try {
    const response = await apiClient.get(
      `/advance-seo/websites/by-url?url=${encodeURIComponent(url)}`
    );
    return response.data;
  } catch (error: any) {
    console.error("Error fetching website data:", error);
    throw new Error(
      error.response?.data?.detail || "Failed to fetch website data"
    );
  }
};

/**
 * Process a website to fetch SEO data
 * @param url The website URL to process
 * @param industry The industry of the website
 * @param language The language of the website
 * @returns A success message
 */
export const processWebsite = async (
  url: string,
  industry: string,
  language: string
) => {
  try {
    const response = await apiClient.post("/advance-seo/websites/process/", {
      url,
      industry,
      language,
    });
    return response.data;
  } catch (error: any) {
    console.error("Error processing website:", error);
    throw new Error(
      error.response?.data?.detail || "Failed to process website"
    );
  }
};
