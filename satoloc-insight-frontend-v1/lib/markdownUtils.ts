/**
 * Utility functions for converting between HTML and Markdown formats
 */

/**
 * Detects if content contains Avada Fusion Builder shortcodes
 */
export const detectAvadaContent = (content: string): boolean => {
  if (!content) return false;

  const avadaPatterns = [
    /\[fusion_builder_container/i,
    /\[fusion_builder_row/i,
    /\[fusion_builder_column/i,
    /\[fusion_/i,
    /\[\/fusion_/i,
  ];

  return avadaPatterns.some((pattern) => pattern.test(content));
};

/**
 * Detects if content contains other common page builder shortcodes
 */
export const detectPageBuilderContent = (content: string): boolean => {
  if (!content) return false;

  const pageBuilderPatterns = [
    // Elementor
    /\[elementor-template/i,
    /data-elementor-type/i,

    // Divi Builder
    /\[et_pb_/i,
    /\[\/et_pb_/i,

    // WPBakery Page Builder (Visual Composer)
    /\[vc_/i,
    /\[\/vc_/i,

    // Beaver Builder
    /\[fl_builder_insert_layout/i,

    // Gutenberg blocks with complex structure
    /<!-- wp:group.*?class.*?wp-block-group/i,
    /<!-- wp:columns/i,

    // Avada (already covered above but included for completeness)
    /\[fusion_/i,
  ];

  return pageBuilderPatterns.some((pattern) => pattern.test(content));
};

/**
 * Cleans Avada Fusion Builder shortcodes and extracts readable content
 */
export const cleanAvadaContent = (content: string): string => {
  if (!content) return "";

  let cleaned = content;

  // Remove fusion_builder containers and structure
  cleaned = cleaned.replace(/\[fusion_builder_container[^\]]*\]/gi, "");
  cleaned = cleaned.replace(/\[\/fusion_builder_container\]/gi, "");
  cleaned = cleaned.replace(/\[fusion_builder_row[^\]]*\]/gi, "");
  cleaned = cleaned.replace(/\[\/fusion_builder_row\]/gi, "");
  cleaned = cleaned.replace(/\[fusion_builder_column[^\]]*\]/gi, "");
  cleaned = cleaned.replace(/\[\/fusion_builder_column\]/gi, "");

  // Handle fusion_title - extract title content
  cleaned = cleaned.replace(
    /\[fusion_title[^\]]*\]([\s\S]*?)\[\/fusion_title\]/gi,
    "<h2>$1</h2>"
  );

  // Handle fusion_text - extract text content
  cleaned = cleaned.replace(
    /\[fusion_text[^\]]*\]([\s\S]*?)\[\/fusion_text\]/gi,
    "<p>$1</p>"
  );

  // Handle fusion_content_boxes - extract content
  cleaned = cleaned.replace(
    /\[fusion_content_boxes[^\]]*\]([\s\S]*?)\[\/fusion_content_boxes\]/gi,
    "$1"
  );
  cleaned = cleaned.replace(
    /\[fusion_content_box[^\]]*title="([^"]*)"[^\]]*\]([\s\S]*?)\[\/fusion_content_box\]/gi,
    "<h3>$1</h3><p>$2</p>"
  );

  // Handle fusion_imageframe - extract images
  cleaned = cleaned.replace(
    /\[fusion_imageframe[^\]]*\]([^[]*)\[\/fusion_imageframe\]/gi,
    '<img src="$1" alt="Image">'
  );
  cleaned = cleaned.replace(
    /\[fusion_imageframe[^\]]*image_id="[^"]*"[^\]]*\]([^[]*)\[\/fusion_imageframe\]/gi,
    '<img src="$1" alt="Image">'
  );

  // Handle fusion_button - extract button text and links
  cleaned = cleaned.replace(
    /\[fusion_button[^\]]*link="([^"]*)"[^\]]*\]([\s\S]*?)\[\/fusion_button\]/gi,
    '<a href="$1">$2</a>'
  );

  // Handle fusion_separator - convert to line breaks
  cleaned = cleaned.replace(/\[fusion_separator[^\]]*\]/gi, "<br>");

  // Handle fusion_fusionslider - remove entirely as it's complex media
  cleaned = cleaned.replace(/\[fusion_fusionslider[^\]]*\/\]/gi, "");

  // Remove any remaining fusion shortcodes that we haven't specifically handled
  cleaned = cleaned.replace(/\[fusion_[^\]]*\]/gi, "");
  cleaned = cleaned.replace(/\[\/fusion_[^\]]*\]/gi, "");

  // Clean up extra whitespace and line breaks
  cleaned = cleaned.replace(/\n\s*\n\s*\n/g, "\n\n");
  cleaned = cleaned.trim();

  return cleaned;
};

/**
 * Cleans other page builder content and extracts readable content
 */
export const cleanPageBuilderContent = (content: string): string => {
  if (!content) return "";

  let cleaned = content;

  // Elementor cleanup
  cleaned = cleaned.replace(/\[elementor-template[^\]]*\]/gi, "");
  cleaned = cleaned.replace(/data-elementor-[^=]*="[^"]*"/gi, "");

  // Divi Builder cleanup
  cleaned = cleaned.replace(/\[et_pb_section[^\]]*\]/gi, "");
  cleaned = cleaned.replace(/\[\/et_pb_section\]/gi, "");
  cleaned = cleaned.replace(/\[et_pb_row[^\]]*\]/gi, "");
  cleaned = cleaned.replace(/\[\/et_pb_row\]/gi, "");
  cleaned = cleaned.replace(/\[et_pb_column[^\]]*\]/gi, "");
  cleaned = cleaned.replace(/\[\/et_pb_column\]/gi, "");

  // Extract text from Divi text modules
  cleaned = cleaned.replace(
    /\[et_pb_text[^\]]*\]([\s\S]*?)\[\/et_pb_text\]/gi,
    "<p>$1</p>"
  );

  // Remove other et_pb shortcodes
  cleaned = cleaned.replace(/\[et_pb_[^\]]*\]/gi, "");
  cleaned = cleaned.replace(/\[\/et_pb_[^\]]*\]/gi, "");

  // WPBakery (Visual Composer) cleanup
  cleaned = cleaned.replace(/\[vc_row[^\]]*\]/gi, "");
  cleaned = cleaned.replace(/\[\/vc_row\]/gi, "");
  cleaned = cleaned.replace(/\[vc_column[^\]]*\]/gi, "");
  cleaned = cleaned.replace(/\[\/vc_column\]/gi, "");

  // Extract text from VC text blocks
  cleaned = cleaned.replace(
    /\[vc_column_text[^\]]*\]([\s\S]*?)\[\/vc_column_text\]/gi,
    "<p>$1</p>"
  );

  // Remove other vc shortcodes
  cleaned = cleaned.replace(/\[vc_[^\]]*\]/gi, "");
  cleaned = cleaned.replace(/\[\/vc_[^\]]*\]/gi, "");

  // Beaver Builder cleanup
  cleaned = cleaned.replace(/\[fl_builder_insert_layout[^\]]*\]/gi, "");

  // Clean up complex Gutenberg block structures while preserving simple ones
  cleaned = cleaned.replace(/<!-- wp:group[^>]*-->/gi, "");
  cleaned = cleaned.replace(/<!-- \/wp:group -->/gi, "");
  cleaned = cleaned.replace(/<!-- wp:columns[^>]*-->/gi, "");
  cleaned = cleaned.replace(/<!-- \/wp:columns -->/gi, "");
  cleaned = cleaned.replace(/<!-- wp:column[^>]*-->/gi, "");
  cleaned = cleaned.replace(/<!-- \/wp:column -->/gi, "");

  return cleaned;
};

/**
 * Main cleanup function that automatically detects and cleans page builder content
 */
export const cleanPageBuilderContentAuto = (content: string): string => {
  if (!content) return "";

  let cleaned = content;

  // Check for Avada content first (most specific)
  if (detectAvadaContent(cleaned)) {
    cleaned = cleanAvadaContent(cleaned);
  }

  // Check for other page builders
  if (detectPageBuilderContent(cleaned)) {
    cleaned = cleanPageBuilderContent(cleaned);
  }

  // Final cleanup - remove any remaining shortcode-like patterns
  cleaned = cleaned.replace(/\[[a-zA-Z0-9_-]+[^\]]*\]/g, "");
  cleaned = cleaned.replace(/\[\/[a-zA-Z0-9_-]+\]/g, "");

  // Clean up excessive whitespace
  cleaned = cleaned.replace(/\n\s*\n\s*\n/g, "\n\n");
  cleaned = cleaned.replace(/^\s+|\s+$/g, "");

  return cleaned;
};

/**
 * Reconstructs AVADA shortcode structure from translated clean HTML
 * This is used when pushing translated content back to WordPress to maintain AVADA builder compatibility
 */
export const reconstructAvadaContent = (
  originalAvadaContent: string,
  translatedCleanHtml: string
): string => {
  if (!originalAvadaContent || !translatedCleanHtml) return translatedCleanHtml;

  // If original content is not AVADA, return translated content as-is
  if (!detectAvadaContent(originalAvadaContent)) {
    return translatedCleanHtml;
  }

  let reconstructed = originalAvadaContent;

  // Parse the translated HTML to extract text content
  const parser = new DOMParser();
  const translatedDoc = parser.parseFromString(
    translatedCleanHtml,
    "text/html"
  );

  // Extract translated text segments in order
  const translatedTexts: string[] = [];
  const walkTextNodes = (node: Node) => {
    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent?.trim();
      if (text && text.length > 0) {
        translatedTexts.push(text);
      }
    } else {
      node.childNodes.forEach(walkTextNodes);
    }
  };
  translatedDoc.body.childNodes.forEach(walkTextNodes);

  // Replace text content in AVADA shortcodes with translated versions
  let textIndex = 0;

  // Replace fusion_title content
  reconstructed = reconstructed.replace(
    /\[fusion_title[^\]]*\]([\s\S]*?)\[\/fusion_title\]/gi,
    (match, content) => {
      if (textIndex < translatedTexts.length) {
        const translatedText = translatedTexts[textIndex++];
        return match.replace(content, translatedText);
      }
      return match;
    }
  );

  // Replace fusion_text content
  reconstructed = reconstructed.replace(
    /\[fusion_text[^\]]*\]([\s\S]*?)\[\/fusion_text\]/gi,
    (match, content) => {
      if (textIndex < translatedTexts.length) {
        const translatedText = translatedTexts[textIndex++];
        return match.replace(content, translatedText);
      }
      return match;
    }
  );

  // Replace fusion_content_box content (title and content)
  reconstructed = reconstructed.replace(
    /\[fusion_content_box[^\]]*title="([^"]*)"[^\]]*\]([\s\S]*?)\[\/fusion_content_box\]/gi,
    (match, title, content) => {
      let newMatch = match;
      // Replace title if we have translated text
      if (textIndex < translatedTexts.length) {
        const translatedTitle = translatedTexts[textIndex++];
        newMatch = newMatch.replace(
          `title="${title}"`,
          `title="${translatedTitle}"`
        );
      }
      // Replace content if we have translated text
      if (textIndex < translatedTexts.length) {
        const translatedContent = translatedTexts[textIndex++];
        newMatch = newMatch.replace(content, translatedContent);
      }
      return newMatch;
    }
  );

  // Replace fusion_button content
  reconstructed = reconstructed.replace(
    /\[fusion_button[^\]]*\]([\s\S]*?)\[\/fusion_button\]/gi,
    (match, content) => {
      if (textIndex < translatedTexts.length) {
        const translatedText = translatedTexts[textIndex++];
        return match.replace(content, translatedText);
      }
      return match;
    }
  );

  return reconstructed;
};

/**
 * Converts HTML content to Markdown format
 */
export const convertHtmlToMarkdown = (html: string): string => {
  try {
    // Basic HTML to Markdown conversion
    let markdown = html;

    // Headers
    markdown = markdown.replace(/<h1[^>]*>(.*?)<\/h1>/gi, "# $1\n\n");
    markdown = markdown.replace(/<h2[^>]*>(.*?)<\/h2>/gi, "## $1\n\n");
    markdown = markdown.replace(/<h3[^>]*>(.*?)<\/h3>/gi, "### $1\n\n");
    markdown = markdown.replace(/<h4[^>]*>(.*?)<\/h4>/gi, "#### $1\n\n");
    markdown = markdown.replace(/<h5[^>]*>(.*?)<\/h5>/gi, "##### $1\n\n");

    // Paragraphs
    markdown = markdown.replace(/<p[^>]*>(.*?)<\/p>/gi, "$1\n\n");

    // Bold and Italic
    markdown = markdown.replace(
      /<(?:b|strong)[^>]*>(.*?)<\/(?:b|strong)>/gi,
      "**$1**"
    );
    markdown = markdown.replace(/<(?:i|em)[^>]*>(.*?)<\/(?:i|em)>/gi, "*$1*");

    // Lists - Handle more carefully to preserve structure
    // First handle unordered lists
    markdown = markdown.replace(
      /<ul[^>]*>([\s\S]*?)<\/ul>/gi,
      function (match, content) {
        // Clean up the content and convert li items
        let listContent = content.replace(
          /<li[^>]*>([\s\S]*?)<\/li>/gi,
          "- $1\n"
        );
        // Clean up extra whitespace but preserve structure
        listContent = listContent.replace(/\n\s*\n/g, "\n");
        return listContent + "\n";
      }
    );

    // Then handle ordered lists
    markdown = markdown.replace(
      /<ol[^>]*>([\s\S]*?)<\/ol>/gi,
      function (match, content) {
        let index = 0;
        let listContent = content.replace(
          /<li[^>]*>([\s\S]*?)<\/li>/gi,
          function (liMatch: string, liContent: string) {
            index++;
            return `${index}. ${liContent.trim()}\n`;
          }
        );
        // Clean up extra whitespace but preserve structure
        listContent = listContent.replace(/\n\s*\n/g, "\n");
        return listContent + "\n";
      }
    );

    // Links
    markdown = markdown.replace(
      /<a[^>]*href="(.*?)"[^>]*>(.*?)<\/a>/gi,
      "[$2]($1)"
    );

    // Images
    markdown = markdown.replace(
      /<img[^>]*src="(.*?)"[^>]*alt="(.*?)"[^>]*>/gi,
      "![$2]($1)"
    );
    markdown = markdown.replace(
      /<img[^>]*alt="(.*?)"[^>]*src="(.*?)"[^>]*>/gi,
      "![$1]($2)"
    );
    markdown = markdown.replace(/<img[^>]*src="(.*?)"[^>]*>/gi, "![]($1)");

    // Line breaks
    markdown = markdown.replace(/<br\s*\/?>/gi, "\n");

    // Remove other HTML tags
    markdown = markdown.replace(/<[^>]*>/g, "");

    // Fix excessive whitespace
    markdown = markdown.replace(/\n\s*\n\s*\n/g, "\n\n");

    return markdown.trim();
  } catch (e) {
    console.error("Error converting HTML to Markdown:", e);
    return html;
  }
};

/**
 * Converts Markdown content to HTML format
 */
export const convertMarkdownToHtml = (markdown: string): string => {
  try {
    let html = markdown;

    // Headers
    html = html.replace(/^# (.*?)$/gm, "<h1>$1</h1>");
    html = html.replace(/^## (.*?)$/gm, "<h2>$1</h2>");
    html = html.replace(/^### (.*?)$/gm, "<h3>$1</h3>");
    html = html.replace(/^#### (.*?)$/gm, "<h4>$1</h4>");
    html = html.replace(/^##### (.*?)$/gm, "<h5>$1</h5>");

    // Process lists more carefully to avoid conflicts
    // First, handle unordered lists
    html = html.replace(/^- (.*?)$/gm, "<ul-item>$1</ul-item>");
    html = html.replace(/((<ul-item>.*?<\/ul-item>\n?)+)/g, "<ul>$1</ul>");
    html = html.replace(/<ul-item>/g, "<li>");
    html = html.replace(/<\/ul-item>/g, "</li>");

    // Then handle ordered lists
    html = html.replace(/^\d+\. (.*?)$/gm, "<ol-item>$1</ol-item>");
    html = html.replace(/((<ol-item>.*?<\/ol-item>\n?)+)/g, "<ol>$1</ol>");
    html = html.replace(/<ol-item>/g, "<li>");
    html = html.replace(/<\/ol-item>/g, "</li>");

    // Bold and Italic
    html = html.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>");
    html = html.replace(/\*(.*?)\*/g, "<em>$1</em>");

    // Images MUST be processed before links to avoid conflicts
    html = html.replace(/!\[(.*?)\]\((.*?)\)/g, '<img src="$2" alt="$1">');

    // Links (processed after images to avoid conflicts)
    html = html.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2">$1</a>');

    // Paragraphs - wrap lines that aren't already wrapped (but avoid wrapping list items, headers, and images)
    html = html.replace(
      /^([^<](?!.*<\/(ul|ol|h[1-5]|li)>)(?!.*<img).*?)$/gm,
      "<p>$1</p>"
    );

    // Clean up any extra paragraph tags
    html = html.replace(/<p>\s*<\/p>/g, "");

    // Clean up extra newlines in lists
    html = html.replace(/(<\/li>)\n+(<li>)/g, "$1$2");
    html = html.replace(/(<ul>)\n+(<li>)/g, "$1$2");
    html = html.replace(/(<\/li>)\n+(<\/ul>)/g, "$1$2");
    html = html.replace(/(<ol>)\n+(<li>)/g, "$1$2");
    html = html.replace(/(<\/li>)\n+(<\/ol>)/g, "$1$2");

    return html;
  } catch (e) {
    console.error("Error converting Markdown to HTML:", e);
    return markdown;
  }
};

/**
 * Cleans HTML content and fixes common issues with tags
 */
export const cleanHtmlContent = (html: string): string => {
  if (!html) return "";

  // Fix common issues with extra closing tags or unclosed tags
  let cleanedHtml = html;

  // Remove html, body wrapping tags that might be added during translation
  cleanedHtml = cleanedHtml.replace(/<html>|<html [^>]*>|<\/html>/gi, "");
  cleanedHtml = cleanedHtml.replace(/<body>|<body [^>]*>|<\/body>/gi, "");

  // Fix the specific issue with p tag before wp:paragraph comment
  cleanedHtml = cleanedHtml.replace(/<p>(\s*<!-- wp:paragraph -->)/gi, "$1");

  // Fix the specific p tag closing/opening sequence at paragraph start
  cleanedHtml = cleanedHtml.replace(
    /<!-- wp:paragraph -->\s*<\/p><p>/gi,
    "<!-- wp:paragraph -->\n<p>"
  );

  // Fix specific issues with p tags around WordPress block comments
  cleanedHtml = cleanedHtml.replace(/<p>(<!-- wp:paragraph -->)/gi, "$1");
  cleanedHtml = cleanedHtml.replace(/(<!-- \/wp:paragraph -->)<\/p>/gi, "$1");

  // Remove any closing tags after the HTML
  cleanedHtml = cleanedHtml.replace(/(<\/p>)(\s*<\/body>)(\s*<\/html>)/gi, "");

  // Fix duplicate closing p tags
  cleanedHtml = cleanedHtml.replace(/<\/p><\/p>/g, "</p>");

  // Fix improper nesting of p tags
  cleanedHtml = cleanedHtml.replace(/<p>([^<]*)<p>/g, "<p>$1");
  cleanedHtml = cleanedHtml.replace(/<\/p>([^<]*)<\/p>/g, "</p>$1");

  return cleanedHtml;
};

/**
 * Enhanced clean function that applies page builder cleanup and then standard HTML cleanup
 */
export const cleanHtmlContentEnhanced = (html: string): string => {
  if (!html) return "";

  // First apply page builder cleanup
  let cleaned = cleanPageBuilderContentAuto(html);

  // Then apply standard HTML cleanup
  cleaned = cleanHtmlContent(cleaned);

  return cleaned;
};
