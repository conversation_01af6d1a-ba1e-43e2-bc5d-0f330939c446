// lib/authOptions/route.ts
import Next<PERSON><PERSON>, { NextAuthOptions, User as NextAuthUser } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import axios from "axios";

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        username: { label: "Username", type: "text" },
        password: { label: "Password", type: "password" },
        isGoogleAuth: { label: "Google Auth", type: "text" },
        googleAccessToken: { label: "Google Access Token", type: "text" },
        refreshToken: { label: "Refresh Token", type: "text" },
      },
      async authorize(credentials) {
        if (!credentials?.username) {
          throw new Error("Username is required");
        }

        // Handle Google OAuth users
        if (credentials.isGoogleAuth === "true") {
          // For Google OAuth, we already have the user authenticated by our backend
          // Just need to fetch user data and create session
          try {
            const response = await fetch(
              `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/user/`,
              {
                method: "GET",
                headers: {
                  Authorization: `Bearer ${credentials.googleAccessToken}`,
                  "Content-Type": "application/json",
                },
              }
            );

            if (!response.ok) {
              throw new Error("Failed to fetch user data");
            }

            const userData = await response.json();

            return {
              id: userData.id.toString(),
              username: userData.username,
              email: userData.email,
              role: userData.role || "user",
              isAdmin: userData.is_staff || false,
              subscription_plan: userData.subscription_plan || "free",
              subscription_price: userData.subscription_price || "0",
              accessToken: credentials.googleAccessToken,
              refreshToken: credentials.refreshToken || "",
            };
          } catch (error) {
            console.error("Google OAuth user fetch error:", error);
            throw new Error("Failed to authenticate Google user");
          }
        }

        // Regular username/password authentication
        if (!credentials?.password) {
          throw new Error("Password is required");
        }

        try {
          const response = await fetch(
            `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/login/`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                username: credentials.username,
                password: credentials.password,
                isGoogleAuth: credentials.isGoogleAuth || "false",
                googleAccessToken: credentials.googleAccessToken,
              }),
            }
          );

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Invalid credentials");
          }

          const data = await response.json();

          return {
            id: data.user.id.toString(),
            username: data.user.username,
            email: data.user.email,
            role: data.user.role,
            isAdmin: data.user.is_admin,
            subscription_plan: data.user.subscription_plan,
            subscription_price: data.user.subscription_price,
            accessToken: data.access_token,
            refreshToken: data.refresh_token,
          };
        } catch (error: any) {
          throw new Error(error.message || "Authentication failed");
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.accessToken = user.accessToken;
        token.refreshToken = user.refreshToken;
        token.role = user.role;
        token.isAdmin = user.isAdmin;
        token.subscriptionPlan = user.subscription_plan;
        token.subscriptionPrice = user.subscription_price;
      }
      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.sub as string;
        session.user.role = token.role as string;
        session.user.isAdmin = token.isAdmin as boolean;
        session.user.subscription_plan = token.subscriptionPlan as string;
        session.user.subscription_price = token.subscriptionPrice as string;
        session.accessToken = token.accessToken as string;
        session.refreshToken = token.refreshToken as string;
      }
      return session;
    },
  },
  pages: {
    signIn: "/login",
    error: "/login",
  },
  debug: process.env.NODE_ENV === "development",
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  secret: process.env.NEXTAUTH_SECRET,
};

export default NextAuth(authOptions);
