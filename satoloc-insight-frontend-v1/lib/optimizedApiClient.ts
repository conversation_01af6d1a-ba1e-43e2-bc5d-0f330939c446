import axios, { AxiosRequestConfig, AxiosResponse } from "axios";
import { getSession } from "next-auth/react";

// Types
interface CachedSession {
  accessToken: string;
  timestamp: number;
  expiresAt?: number;
}

// Extend Axios types to include metadata
declare module "axios" {
  interface InternalAxiosRequestConfig {
    metadata?: {
      startTime: number;
    };
  }
}

interface RequestCache {
  [key: string]: {
    promise: Promise<any>;
    timestamp: number;
  };
}

// Configuration
const DJANGO_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
const DJANGO_API_ENDPOINT = `${DJANGO_BASE_URL}/api`;
const SESSION_CACHE_DURATION = 30000; // 30 seconds
const REQUEST_CACHE_DURATION = 5000; // 5 seconds for deduplication

// Caches
let sessionCache: CachedSession | null = null;
const requestCache: RequestCache = {};

// Session management with caching
const getCachedSession = async (): Promise<string | null> => {
  const now = Date.now();

  // Check if cached session is still valid
  if (sessionCache && now - sessionCache.timestamp < SESSION_CACHE_DURATION) {
    // Additional check for token expiration if available
    if (sessionCache.expiresAt && now >= sessionCache.expiresAt) {
      sessionCache = null;
    } else {
      return sessionCache.accessToken;
    }
  }

  // Fetch fresh session
  try {
    const session = await getSession();
    if (session?.accessToken) {
      sessionCache = {
        accessToken: session.accessToken,
        timestamp: now,
        // Try to extract expiration from JWT if possible
        expiresAt: session.expires
          ? new Date(session.expires).getTime()
          : undefined,
      };
      return session.accessToken;
    }
  } catch (error) {
    console.warn("Failed to get session:", error);
  }

  sessionCache = null;
  return null;
};

// Request deduplication
const getCacheKey = (config: AxiosRequestConfig): string => {
  return `${config.method?.toLowerCase()}-${config.url}-${JSON.stringify(config.params)}-${typeof config.data === "object" ? JSON.stringify(config.data) : config.data}`;
};

const getCachedRequest = (key: string): Promise<any> | null => {
  const cached = requestCache[key];
  if (cached && Date.now() - cached.timestamp < REQUEST_CACHE_DURATION) {
    return cached.promise;
  }
  return null;
};

const setCachedRequest = (key: string, promise: Promise<any>): void => {
  requestCache[key] = {
    promise,
    timestamp: Date.now(),
  };

  // Cleanup old entries
  setTimeout(() => {
    delete requestCache[key];
  }, REQUEST_CACHE_DURATION * 2);
};

// Create optimized axios instance
const optimizedApiClient = axios.create({
  baseURL: DJANGO_API_ENDPOINT,
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true,
  timeout: 15000, // 15 second timeout
});

// Request interceptor with session caching
optimizedApiClient.interceptors.request.use(
  async (config) => {
    // Ensure baseURL is always set correctly
    config.baseURL = DJANGO_API_ENDPOINT;

    // Get cached session token
    const accessToken = await getCachedSession();
    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }

    // Add timestamp for debugging
    config.metadata = { startTime: Date.now() };

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor with enhanced error handling
optimizedApiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // Log slow requests in development
    if (process.env.NODE_ENV === "development" && response.config.metadata) {
      const duration = Date.now() - response.config.metadata.startTime;
      if (duration > 2000) {
        console.warn(
          `Slow API request: ${response.config.url} took ${duration}ms`
        );
      }
    }

    return response;
  },
  (error) => {
    // Clear session cache on 401 errors
    if (error.response?.status === 401) {
      sessionCache = null;

      // Dispatch session expired event
      const event = new CustomEvent("session-expired", {
        detail: { status: 401 },
      });
      window.dispatchEvent(event);
    }

    // Log API errors in development
    if (process.env.NODE_ENV === "development") {
      console.error("API Error:", {
        url: error.config?.url,
        status: error.response?.status,
        message: error.response?.data?.error || error.message,
      });
    }

    return Promise.reject(error);
  }
);

// Enhanced request function with deduplication for GET requests
export const makeOptimizedRequest = async <T = any>(
  config: AxiosRequestConfig
): Promise<T> => {
  // Only use caching for GET requests
  if (config.method?.toLowerCase() === "get" || !config.method) {
    const cacheKey = getCacheKey(config);
    const cachedRequest = getCachedRequest(cacheKey);

    if (cachedRequest) {
      return cachedRequest;
    }

    // Make the request and cache it
    const requestPromise = optimizedApiClient(config).then(
      (response) => response.data
    );
    setCachedRequest(cacheKey, requestPromise);

    return requestPromise;
  }

  // For non-GET requests, make request directly
  const response = await optimizedApiClient(config);
  return response.data;
};

// Prefetch utility for route optimization
export const prefetchData = async (urls: string[]): Promise<void> => {
  const prefetchPromises = urls.map((url) =>
    makeOptimizedRequest({
      url,
      method: "GET",
    }).catch((error) => {
      // Only log non-404 errors for prefetching
      if (error.response?.status !== 404) {
        console.debug(`Prefetch failed for ${url}:`, error.message);
      }
    })
  );

  await Promise.allSettled(prefetchPromises);
};

// Clear caches utility
export const clearApiCaches = (): void => {
  sessionCache = null;
  Object.keys(requestCache).forEach((key) => delete requestCache[key]);
};

// Navigation-optimized data fetching hook
export const useOptimizedQuery = <T = any>(
  key: string[],
  url: string,
  options: {
    enabled?: boolean;
    staleTime?: number;
    cacheTime?: number;
  } = {}
) => {
  const {
    enabled = true,
    staleTime = 300000, // 5 minutes
    cacheTime = 600000, // 10 minutes
  } = options;

  return {
    queryKey: key,
    queryFn: () => makeOptimizedRequest<T>({ url }),
    enabled,
    staleTime,
    cacheTime,
    retry: 1,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  };
};

export default optimizedApiClient;
