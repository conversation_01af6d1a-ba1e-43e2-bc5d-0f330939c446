// lib/api-client.ts
import axios from "axios";
import { getSession } from "next-auth/react";
import {
  SyncedContent,
  WpConnectionDetails,
  SyncApiResponse,
  WpUpdatePayload,
  WpCategory,
} from "../types";

// Get the base API URL from environment variable with fallback
const DJANGO_BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000";
const DJANGO_API_ENDPOINT = `${DJANGO_BASE_URL}/api`;

// WordPress API Constants
const WP_API_NAMESPACE = "satoloc-insight-connector/v1";
const WP_API_KEY_HEADER = "X-Satoloc-Insight-Key";

const apiClient = axios.create({
  baseURL: DJANGO_API_ENDPOINT, // Set this correctly to the Django API endpoint
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true,
});

// Add request interceptor to always include the latest token
apiClient.interceptors.request.use(
  async (config) => {
    // Ensure baseURL is always set correctly
    config.baseURL = DJANGO_API_ENDPOINT;

    const session = await getSession();
    if (session?.accessToken) {
      config.headers.Authorization = `Bearer ${session.accessToken}`;
    }

    // Log the full URL being requested (for debugging)
    //console.debug(`Making API request to: ${config.baseURL}${config.url}`);

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

const dispatchSessionExpired = () => {
  window.dispatchEvent(new CustomEvent("session-expired"));
};

// Response interceptor
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // Only dispatch session-expired event for 401 errors
    if (error.response?.status === 401) {
      const event = new CustomEvent("session-expired", {
        detail: { status: 401 },
      });
      window.dispatchEvent(event);
    }
    return Promise.reject(error);
  }
);

// Helper to construct WP API URLs dynamically
function getWpApiUrl(
  baseUrl: string,
  namespace: string,
  endpoint: string
): string {
  const cleanedBase = baseUrl.replace(/\/wp-json.*$/, "").replace(/\/$/, "");
  const cleanedNamespace = namespace.replace(/^\/|\/$/g, "");
  const cleanedEndpoint = endpoint.replace(/^\//, "");
  // Ensure /wp-json/ is present
  const baseWithJson = cleanedBase.includes("/wp-json")
    ? cleanedBase.split("/wp-json")[0].replace(/\/$/, "") + "/wp-json/"
    : cleanedBase + "/wp-json/";
  return `${baseWithJson}${cleanedNamespace}/${cleanedEndpoint}`;
}

// Function to verify connection to WP Plugin API by attempting a simple GET
export async function verifyWpConnection(
  details: WpConnectionDetails
): Promise<boolean> {
  if (!details.baseUrl || !details.apiKey) return false;
  try {
    const url = getWpApiUrl(details.baseUrl, WP_API_NAMESPACE, "content");

    const response = await fetch(url, {
      method: "GET",
      headers: {
        [WP_API_KEY_HEADER]: details.apiKey,
      },
    });
    if (response.ok) {
      return true;
    } else {
      console.error(
        `WP Connection Verification Failed: Status ${response.status} - ${response.statusText}`
      );
      try {
        const errorBody = await response.json();
      } catch (e) {}
      return false;
    }
  } catch (error) {
    console.error("WP Connection Verification Network Error:", error);
    return false;
  }
}

// Django API Functions
async function fetchFromDjango(
  endpoint: string,
  options: any = {}
): Promise<any> {
  if (!DJANGO_API_ENDPOINT) {
    throw new Error("Django API URL is not configured.");
  }

  // Ensure endpoint starts with '/'
  const formattedEndpoint = endpoint.startsWith("/")
    ? endpoint
    : `/${endpoint}`;

  // Construct the full URL without relying on baseURL to be set correctly in axios config
  const url = `${DJANGO_API_ENDPOINT}${formattedEndpoint}`;
  //console.debug(`Calling Django API: ${options.method || "GET"} ${url}`);

  try {
    const response = await apiClient({
      url: formattedEndpoint,
      method: options.method || "GET",
      data: options.body ? JSON.parse(options.body) : undefined,
      baseURL: DJANGO_API_ENDPOINT, // Explicitly set baseURL for each request
    });

    return response.data;
  } catch (error) {
    console.error(
      `Error calling Django API endpoint ${formattedEndpoint}:`,
      error
    );

    if (axios.isAxiosError(error) && error.response) {
      const errorData = error.response.data;
      throw new Error(
        errorData?.error ||
          errorData?.detail ||
          `Request failed with status ${error.response.status}`
      );
    }

    throw error;
  }
}

// Fetch list of locally synced content
export async function fetchLocalContent(
  wpSiteUrl?: string
): Promise<SyncedContent[]> {
  let endpoint = "/wp-content-sync/content/";
  const queryParams = new URLSearchParams();
  if (wpSiteUrl) {
    queryParams.append("wp_site_url", wpSiteUrl);
  }
  const queryString = queryParams.toString();
  if (queryString) {
    endpoint += `?${queryString}`;
  }
  const response = await fetchFromDjango(endpoint);
  if (
    response &&
    typeof response === "object" &&
    Array.isArray(response.results)
  ) {
    return response.results as SyncedContent[];
  } else if (Array.isArray(response)) {
    return response as SyncedContent[];
  } else {
    console.error(
      "fetchLocalContent received unexpected data format:",
      response
    );
    throw new Error("Invalid data format received for local content list.");
  }
}

// Fetch a single local content item
export async function fetchLocalContentItem(
  id: number
): Promise<SyncedContent> {
  return fetchFromDjango(`/wp-content-sync/content/${id}/`);
}

// Update a local content item
export async function updateLocalContentItem(
  id: number,
  data: Partial<Pick<SyncedContent, "title" | "content" | "status">>
): Promise<SyncedContent> {
  return fetchFromDjango(`/wp-content-sync/content/${id}/`, {
    method: "PATCH",
    body: JSON.stringify(data),
  });
}

// Delete a local content item ONLY
export async function deleteLocalContentItem(id: number): Promise<void> {
  await fetchFromDjango(`/wp-content-sync/content/${id}/`, {
    method: "DELETE",
  });
}

// Trigger full fetch from WP via Django
export async function triggerDjangoFetchAll(
  details: WpConnectionDetails
): Promise<SyncApiResponse> {
  return fetchFromDjango("/wp-content-sync/content/fetch-all-from-wp/", {
    method: "POST",
    body: JSON.stringify(details),
  });
}

// --- UPDATED: triggerDjangoPush ---
export async function triggerDjangoPush(
  localId: number,
  apiKey: string,
  // NEW: Accept optional category IDs
  categoryIds?: number[]
): Promise<SyncedContent> {
  const payload: { apiKey: string; category_ids?: number[] } = { apiKey };
  if (categoryIds !== undefined) {
    payload.category_ids = categoryIds; // Add if provided
  }
  return fetchFromDjango(`/wp-content-sync/content/${localId}/push-to-wp/`, {
    method: "POST",
    body: JSON.stringify(payload),
  });
}

// Trigger delete from WP via Django
export async function triggerDjangoDelete(
  localId: number,
  apiKey: string,
  force: boolean = false
): Promise<SyncApiResponse> {
  return fetchFromDjango(
    `/wp-content-sync/content/${localId}/delete-from-wp/`,
    {
      method: "POST",
      body: JSON.stringify({ apiKey, force }),
    }
  );
}

// Fetch AI Translation Preview from Django
export async function fetchAiTranslationPreview(
  sourceLocalId: number,
  targetLanguageCode: string
): Promise<{ translated_title: string; translated_content: string }> {
  return fetchFromDjango(
    `/wp-content-sync/content/${sourceLocalId}/ai-translate-preview/`,
    {
      method: "POST",
      body: JSON.stringify({ target_language_code: targetLanguageCode }),
    }
  );
}

// Trigger Translation Creation via Django
export async function triggerDjangoTranslateAndCreate(
  sourceLocalId: number,
  targetLanguageCode: string,
  apiKey: string,
  translatedTitle?: string,
  translatedContent?: string,
  // --- NEW ---
  categoryIds?: number[] // Add category IDs parameter
  // --- END NEW ---
): Promise<{ message: string; new_content_item: SyncedContent }> {
  const payload: any = {
    target_language_code: targetLanguageCode,
    apiKey: apiKey,
  };
  if (translatedTitle !== undefined) {
    payload.translated_title = translatedTitle;
  }
  if (translatedContent !== undefined) {
    payload.translated_content = translatedContent;
  }
  // --- NEW ---
  if (categoryIds !== undefined) {
    payload.category_ids = categoryIds; // Add category IDs if provided
  }
  // --- END NEW ---
  return fetchFromDjango(
    `/wp-content-sync/content/${sourceLocalId}/translate-and-create/`,
    {
      method: "POST",
      body: JSON.stringify(payload),
    }
  );
}

// WordPress Content Sync API methods
// These methods use the apiClient which handles authentication

// --- NEW: Fetch Categories from Django/WP ---
export const fetchWpCategoriesForConnection = async (
  connectionId: number
): Promise<WpCategory[]> => {
  if (!connectionId) {
    console.warn("No connection ID provided, cannot fetch categories.");
    return [];
  }
  try {
    const categories = await fetchFromDjango(
      `/wp-content-sync/connections/${connectionId}/wp-categories/`
    );
    // Ensure it returns an array
    return Array.isArray(categories) ? categories : [];
  } catch (error) {
    console.error(
      `Error fetching WP categories for connection ${connectionId}:`,
      error
    );
    // Return empty array on error to prevent UI crashes
    return [];
  }
};
// --- END NEW ---

/**
 * Fetch all content items from a WordPress site
 */
export const fetchContentItems = (wpSiteUrl?: string) =>
  fetchLocalContent(wpSiteUrl);

/**
 * Fetch a single content item by ID
 */
export const fetchContentItem = async (id: number): Promise<SyncedContent> => {
  try {
    return fetchFromDjango(`/wp-content-sync/content/${id}/`);
  } catch (error) {
    console.error(`Error fetching content item ${id}:`, error);
    throw error;
  }
};

/**
 * Update a content item
 */
export const updateContentItem = async (
  id: number,
  data: Partial<Pick<SyncedContent, "title" | "content" | "status">>
): Promise<SyncedContent> => {
  try {
    return fetchFromDjango(`/wp-content-sync/content/${id}/`, {
      method: "PATCH",
      body: JSON.stringify(data),
    });
  } catch (error) {
    console.error(`Error updating content item ${id}:`, error);
    throw error;
  }
};

/**
 * Delete a content item
 */
export const deleteContentItem = async (id: number): Promise<void> => {
  try {
    await fetchFromDjango(`/wp-content-sync/content/${id}/`, {
      method: "DELETE",
    });
  } catch (error) {
    console.error(`Error deleting content item ${id}:`, error);
    throw error;
  }
};

/**
 * Sync all content from WordPress
 */
export const syncAllFromWordPress = async (
  connectionDetails: WpConnectionDetails,
  options?: {
    cleanup_missing?: boolean;
    keep_wp_generated_translations?: boolean;
  }
): Promise<SyncApiResponse> => {
  const body = {
    baseUrl: connectionDetails.baseUrl,
    apiKey: connectionDetails.apiKey,
    ...(options || {}),
  };
  return fetchFromDjango("/wp-content-sync/content/fetch-all-from-wp/", {
    method: "POST",
    body: JSON.stringify(body),
  });
};

/**
 * Push content to WordPress
 */
export const pushContentToWordPress = async (
  contentId: number,
  apiKey: string,
  categoryIds?: number[],
  originalContent?: string // Original page builder content to preserve structure
): Promise<SyncedContent> => {
  let currentContent: SyncedContent | null = null;

  try {
    // WordPress push with original content preservation

    // If original content is provided, we need to temporarily update the content before push
    if (originalContent) {
      // Get current content to restore later
      currentContent = await fetchContentItem(contentId);

      // Temporarily update with original structure for WordPress push
      await updateContentItem(contentId, { content: originalContent });
    }

    // Pass categoryIds to the backend trigger function
    const result = await triggerDjangoPush(contentId, apiKey, categoryIds);

    // If we temporarily changed the content and the push was successful,
    // restore the cleaned content for the user interface
    if (originalContent && currentContent) {
      await updateContentItem(contentId, { content: currentContent.content });

      // Return result but with the cleaned content for UI consistency
      return {
        ...result,
        content: currentContent.content,
      };
    }

    return result;
  } catch (error) {
    // If push failed and we changed content temporarily, restore it
    if (originalContent && currentContent) {
      try {
        await updateContentItem(contentId, { content: currentContent.content });
      } catch (restoreError) {
        console.error(
          "Failed to restore content after push error:",
          restoreError
        );
      }
    }

    console.error(`Error pushing content ${contentId} to WordPress:`, error);
    throw error;
  }
};

/**
 * Push custom content to WordPress as a new post
 */
export const pushCustomContentToWordPress = async (
  customContentId: number,
  wpBaseUrl: string,
  wpApiKey: string,
  status: string = "draft",
  categories?: number[]
): Promise<{
  message: string;
  wordpress_post_id: number;
  wordpress_url: string;
  status: string;
  custom_content_id: number;
}> => {
  try {
    const response = await apiClient.post(
      `/custom-content/${customContentId}/push_to_wordpress/`,
      {
        wp_base_url: wpBaseUrl,
        wp_api_key: wpApiKey,
        status,
        categories,
      }
    );
    return response.data;
  } catch (error: any) {
    console.error(
      `Error pushing custom content ${customContentId} to WordPress:`,
      error
    );
    throw new Error(
      error.response?.data?.error ||
        `Failed to push custom content to WordPress: ${error.message}`
    );
  }
};

/**
 * Delete content from WordPress
 */
export const deleteContentFromWordPress = (
  localId: number,
  apiKey: string,
  force: boolean = false
): Promise<SyncApiResponse> => {
  return triggerDjangoDelete(localId, apiKey, force);
};

/**
 * Get AI translation preview (HTML-based)
 */
export const getTranslationPreview = async (
  contentId: number,
  targetLanguageCode: string,
  customContent?: string
): Promise<{ translated_title: string; translated_content: string }> => {
  try {
    const payload: any = {
      target_language_code: targetLanguageCode,
    };

    // Add custom content if provided (for AVADA content cleaning)
    if (customContent) {
      payload.translated_content = customContent;
    }

    return fetchFromDjango(
      `/wp-content-sync/content/${contentId}/ai-translate-preview/`,
      {
        method: "POST",
        body: JSON.stringify(payload),
      }
    );
  } catch (error) {
    console.error(
      `Error getting translation preview for content ${contentId}:`,
      error
    );
    throw error;
  }
};

/**
 * Create AI translation
 */
export const createTranslation = async (
  contentId: number,
  targetLanguageCode: string,
  apiKey: string,
  translatedTitle?: string,
  translatedContent?: string,
  // --- NEW ---
  categoryIds?: number[]
  // --- END NEW ---
): Promise<{ message: string; new_content_item: SyncedContent }> => {
  try {
    // Pass categoryIds to the backend trigger function
    return triggerDjangoTranslateAndCreate(
      contentId,
      targetLanguageCode,
      apiKey,
      translatedTitle,
      translatedContent,
      categoryIds // Pass it here
    );
  } catch (error) {
    console.error(
      `Error creating translation for content ${contentId}:`,
      error
    );
    throw error;
  }
};

/**
 * Get AI translation preview for markdown content
 * This is more robust for languages like Chinese, Japanese, Russian
 */
export const getMarkdownTranslationPreview = async (
  contentId: number,
  targetLanguageCode: string,
  markdownContent: string
): Promise<{ translated_title: string; translated_markdown: string }> => {
  try {
    return fetchFromDjango(
      `/wp-content-sync/content/${contentId}/ai-translate-markdown/`,
      {
        method: "POST",
        body: JSON.stringify({
          target_language_code: targetLanguageCode,
          markdown_content: markdownContent,
        }),
      }
    );
  } catch (error) {
    console.error(
      `Error getting markdown translation preview for content ${contentId}:`,
      error
    );
    throw error;
  }
};

/**
 * Delete content locally only (doesn't affect WordPress)
 */
export const deleteContentLocally = (
  localId: number
): Promise<SyncApiResponse> => {
  return fetchFromDjango(
    `/wp-content-sync/content/${localId}/delete-locally/`,
    {
      method: "POST",
      body: JSON.stringify({}),
    }
  );
};

// Functions to manage WordPress connections in the database
export const fetchWpConnections = async (): Promise<any[]> => {
  try {
    // Use the explicit path with leading slash
    const response = await fetchFromDjango("/wp-content-sync/connections/");

    // Ensure we return an empty array if the response is not an array
    if (!Array.isArray(response)) {
      console.warn(
        "fetchWpConnections: Expected array but got",
        typeof response
      );
      return [];
    }

    return response;
  } catch (error) {
    console.error("Error fetching WordPress connections:", error);
    // Return empty array on error to prevent UI issues
    return [];
  }
};

export const saveWpConnection = async (
  connection: WpConnectionDetails
): Promise<any> => {
  try {
    // Make sure we're authenticated by checking the session
    const session = await getSession();
    if (!session?.accessToken) {
      console.error(
        "No access token available. User may not be authenticated."
      );
      throw new Error("Authentication required. Please log in.");
    }

    const response = await fetchFromDjango("/wp-content-sync/connections/", {
      method: "POST",
      body: JSON.stringify({
        site_url: connection.baseUrl,
        api_key: connection.apiKey,
        is_active: true,
      }),
    });

    return response;
  } catch (error) {
    console.error("Error in saveWpConnection:", error);

    // Get more details from the error
    if (axios.isAxiosError(error) && error.response) {
      console.error("API Response data:", error.response.data);
      console.error("API Response status:", error.response.status);

      // If it's a validation error, provide more specific information
      if (error.response.status === 400) {
        const errorData = error.response.data;
        let errorMessage = "Validation error: ";

        if (typeof errorData === "object") {
          // Convert error object to readable message
          errorMessage += Object.entries(errorData)
            .map(
              ([field, errors]) =>
                `${field}: ${Array.isArray(errors) ? errors.join(", ") : errors}`
            )
            .join("; ");
        } else {
          errorMessage += String(errorData);
        }

        throw new Error(errorMessage);
      }
    }

    throw error;
  }
};

export const updateWpConnection = async (
  id: number,
  data: Partial<WpConnectionDetails>
): Promise<any> => {
  const payload: any = {};

  if (data.baseUrl) payload.site_url = data.baseUrl;
  if (data.apiKey) payload.api_key = data.apiKey;
  if (data.isActive !== undefined) payload.is_active = data.isActive;

  return fetchFromDjango(`/wp-content-sync/connections/${id}/`, {
    method: "PATCH",
    body: JSON.stringify(payload),
  });
};

/**
 * Deactivate a WordPress connection (instead of deleting it)
 */
export const deactivateWpConnection = async (id: number): Promise<any> => {
  return fetchFromDjango(`/wp-content-sync/connections/${id}/deactivate/`, {
    method: "POST",
  });
};

export const deleteWpConnection = async (id: number): Promise<any> => {
  return fetchFromDjango(`/wp-content-sync/connections/${id}/`, {
    method: "DELETE",
  });
};

export const verifyWpConnectionInDb = async (id: number): Promise<any> => {
  return fetchFromDjango(
    `/wp-content-sync/connections/${id}/verify_connection/`,
    {
      method: "POST",
    }
  );
};

export const updateWpConnectionLastSync = async (id: number): Promise<any> => {
  return fetchFromDjango(
    `/wp-content-sync/connections/${id}/update_last_sync/`,
    {
      method: "POST",
    }
  );
};

export default apiClient;
