import React, { useState } from "react";
import { useAllWebsites } from "@/internal-api/seo/advance-seo";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ChevronLeft,
  ChevronRight,
  ExternalLink,
  Eye,
  Globe,
  Loader2,
  Search,
  SortAsc,
  SortDesc,
  Pencil,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { format } from "date-fns";

type SortDirection = "asc" | "desc";

interface Website {
  id: number;
  url: string;
  industry: string;
  language: string;
  created_at: string;
  updated_at: string;
  seo_metrics?: {
    domain_rating_score?: number;
    organic_traffic_score?: number;
  };
  technical_analysis?: {
    page_speed?: number;
  };
}

export default function WebsiteTable() {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [search, setSearch] = useState("");
  const [sortField, setSortField] = useState<string>("created_at");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");

  const { data: websitesData, isLoading, isError, error } = useAllWebsites();

  // Filter and sort the websites
  const filteredWebsites = React.useMemo(() => {
    if (!websitesData) return [];

    let filtered = [...websitesData];

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase();
      filtered = filtered.filter(
        (website: Website) =>
          website.url.toLowerCase().includes(searchLower) ||
          (website.industry &&
            website.industry.toLowerCase().includes(searchLower)) ||
          (website.language &&
            website.language.toLowerCase().includes(searchLower))
      );
    }

    // Apply sorting
    filtered.sort((a: any, b: any) => {
      // Handle nested properties
      const getValue = (obj: any, path: string) => {
        const parts = path.split(".");
        let value = obj;
        for (const part of parts) {
          if (value === null || value === undefined) return null;
          value = value[part];
        }
        return value;
      };

      const aValue = getValue(a, sortField) || "";
      const bValue = getValue(b, sortField) || "";

      if (sortDirection === "asc") {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [websitesData, search, sortField, sortDirection]);

  // Paginate the websites
  const paginatedWebsites = React.useMemo(() => {
    const startIdx = (page - 1) * pageSize;
    const endIdx = startIdx + pageSize;
    return filteredWebsites.slice(startIdx, endIdx);
  }, [filteredWebsites, page, pageSize]);

  const totalPages = Math.ceil(filteredWebsites.length / pageSize);

  const toggleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const renderSortIcon = (field: string) => {
    if (sortField !== field) return null;
    return sortDirection === "asc" ? (
      <SortAsc className="ml-1 h-4 w-4" />
    ) : (
      <SortDesc className="ml-1 h-4 w-4" />
    );
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Websites</CardTitle>
          <CardDescription>
            Loading all websites in the database...
          </CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    );
  }

  if (isError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Websites</CardTitle>
          <CardDescription>
            Error loading websites. Please try again.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-destructive">
            {error instanceof Error ? error.message : "Unknown error occurred"}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-[#1279b4]">
          <Globe className="h-5 w-5" />
          All Websites
        </CardTitle>
        <CardDescription className="text-[#1279b4]">
          Manage all websites in the database ({filteredWebsites.length} total)
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col space-y-4">
          {/* Search and filters */}
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search websites..."
                className="pl-8"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <Select
                value={pageSize.toString()}
                onValueChange={(val) => {
                  setPageSize(Number(val));
                  setPage(1); // Reset to first page
                }}
              >
                <SelectTrigger className="w-[100px]">
                  <SelectValue placeholder="10 rows" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 rows</SelectItem>
                  <SelectItem value="10">10 rows</SelectItem>
                  <SelectItem value="20">20 rows</SelectItem>
                  <SelectItem value="50">50 rows</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead
                    className="w-[250px] cursor-pointer"
                    onClick={() => toggleSort("url")}
                  >
                    <div className="flex items-center">
                      URL {renderSortIcon("url")}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => toggleSort("industry")}
                  >
                    <div className="flex items-center">
                      Industry {renderSortIcon("industry")}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => toggleSort("language")}
                  >
                    <div className="flex items-center">
                      Language {renderSortIcon("language")}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer"
                    onClick={() =>
                      toggleSort("seo_metrics.domain_rating_score")
                    }
                  >
                    <div className="flex items-center">
                      Domain Rating{" "}
                      {renderSortIcon("seo_metrics.domain_rating_score")}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => toggleSort("created_at")}
                  >
                    <div className="flex items-center">
                      Created {renderSortIcon("created_at")}
                    </div>
                  </TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedWebsites.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={6}
                      className="h-24 text-center text-muted-foreground"
                    >
                      No websites found
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedWebsites.map((website: Website) => (
                    <TableRow key={website.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          <span className="max-w-[200px] truncate">
                            {website.url}
                          </span>
                          <a
                            href={`https://${website.url}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-muted-foreground hover:text-primary"
                          >
                            <ExternalLink className="h-4 w-4" />
                          </a>
                        </div>
                      </TableCell>
                      <TableCell>
                        {website.industry ? (
                          <Badge variant="outline" className="text-[#1279b4]">
                            {website.industry}
                          </Badge>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {website.language ? (
                          <Badge variant="outline" className="text-[#1279b4]">
                            {website.language.toUpperCase()}
                          </Badge>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {website.seo_metrics?.domain_rating_score !==
                        undefined ? (
                          <span
                            className={
                              website.seo_metrics.domain_rating_score >= 50
                                ? "text-green-600"
                                : website.seo_metrics.domain_rating_score >= 30
                                ? "text-amber-600"
                                : "text-red-600"
                            }
                          >
                            {website.seo_metrics.domain_rating_score}
                          </span>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {website.created_at
                          ? format(
                              new Date(website.created_at),
                              "MMM d, yyyy HH:mm"
                            )
                          : "-"}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            title="View SEO Insights"
                            asChild
                          >
                            <Link
                              href={`/seo-insights?url=${encodeURIComponent(
                                website.url
                              )}`}
                            >
                              <Eye className="h-4 w-4" />
                              <span className="sr-only">View SEO Insights</span>
                            </Link>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            title="Edit Details"
                            asChild
                          >
                            <Link
                              href={`/admin-dashboard/website/${website.id}`}
                            >
                              <Pencil className="h-4 w-4" />
                              <span className="sr-only">Edit Details</span>
                            </Link>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              Showing{" "}
              <strong>
                {filteredWebsites.length > 0 ? (page - 1) * pageSize + 1 : 0} -{" "}
                {Math.min(page * pageSize, filteredWebsites.length)}
              </strong>{" "}
              of <strong>{filteredWebsites.length}</strong> websites
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page - 1)}
                disabled={page === 1}
              >
                <ChevronLeft className="h-4 w-4" />
                <span className="sr-only">Previous Page</span>
              </Button>
              <div className="text-sm font-medium">
                Page {page} of {totalPages || 1}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page + 1)}
                disabled={page >= totalPages}
              >
                <ChevronRight className="h-4 w-4" />
                <span className="sr-only">Next Page</span>
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
