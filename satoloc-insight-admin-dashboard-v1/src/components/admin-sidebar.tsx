"use client";

import * as React from "react";
import Image from "next/image";
import { useRouter, usePathname } from "next/navigation";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
} from "@/components/ui/sidebar";
import { useAuthCheck, useLogout } from "@/hooks/useAuth";
import {
  Shield,
  Users,
  Settings,
  LogOut,
  BarChart3,
  Database,
  UserCheck,
  Globe,
  FileText,
  CreditCard,
  Bell,
  Lock,
  HelpCircle,
  ChevronRight,
  Home,
  Activity,
  Wrench,
  Archive,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { ThemeToggle } from "@/components/theme-toggle";
import <PERSON><PERSON>ogo from "../../public/images/satoloc_insight_logo_white.png";
import <PERSON><PERSON>ogo from "../../public/images/satoloc_insight_logo.png";
import { useTheme } from "next-themes";

const navigationItems = [
  {
    title: "Dashboard",
    icon: Home,
    url: "/dashboard",
    isActive: true,
  },
  {
    title: "User Management",
    icon: Users,
    items: [
      {
        title: "All Users",
        url: "/dashboard",
      },
      //   {
      //     title: "Admin Users",
      //     url: "/dashboard/admins",
      //   },
      //   {
      //     title: "User Analytics",
      //     url: "/dashboard/user-analytics",
      //   },
    ],
  },
  {
    title: "Website Management",
    icon: Globe,
    items: [
      {
        title: "All Websites",
        url: "/dashboard/website",
      },
      //   {
      //     title: "Admin Users",
      //     url: "/dashboard/admins",
      //   },
      //   {
      //     title: "User Analytics",
      //     url: "/dashboard/user-analytics",
      //   },
    ],
  },
  {
    title: "System Analytics",
    icon: BarChart3,
    items: [
      {
        title: "Performance",
        url: "#",
      },
      {
        title: "Usage Statistics",
        url: "#",
      },
      {
        title: "Reports",
        url: "#",
      },
    ],
  },

  {
    title: "Billing & Subscriptions",
    icon: CreditCard,
    items: [
      {
        title: "Subscription Plans",
        url: "#",
      },
      {
        title: "Transactions",
        url: "#",
      },
      {
        title: "Invoices",
        url: "#",
      },
    ],
  },
];

const systemItems = [
  {
    title: "System Settings",
    icon: Settings,
    url: "#",
  },
  {
    title: "Database Management",
    icon: Database,
    url: "#",
  },
  {
    title: "Security & Permissions",
    icon: Lock,
    url: "#",
  },
  {
    title: "API Management",
    icon: Globe,
    url: "#",
  },
  {
    title: "System Logs",
    icon: Activity,
    url: "#",
  },
  {
    title: "Backup & Recovery",
    icon: Archive,
    url: "#",
  },
];

export function AdminSidebar() {
  const router = useRouter();
  const pathname = usePathname();
  const { user: currentUser } = useAuthCheck();
  const logoutMutation = useLogout();
  const [openItems, setOpenItems] = React.useState<Set<string>>(new Set());
  const { theme } = useTheme();
  const logo = theme === "dark" ? WhiteLogo : BlackLogo;
  const handleLogout = () => {
    logoutMutation.mutate();
  };

  const handleNavigation = (url: string) => {
    router.push(url);
  };

  const toggleItem = (title: string) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(title)) {
      newOpenItems.delete(title);
    } else {
      newOpenItems.add(title);
    }
    setOpenItems(newOpenItems);
  };

  return (
    <Sidebar variant="inset" collapsible="icon">
      <SidebarHeader>
        <div className="flex items-center gap-2 px-4 py-2">
          <Image src={logo} alt="Satoloc Logo" width={32} height={32} />
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-semibold">Satoloc Admin</span>
            <span className="truncate text-xs text-muted-foreground">
              Management Console
            </span>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent>
        {/* Main Navigation */}
        <SidebarGroup>
          <SidebarGroupLabel>Main Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  {item.items ? (
                    // Collapsible menu with sub-items
                    <SidebarMenuButton
                      tooltip={item.title}
                      onClick={() => toggleItem(item.title)}
                    >
                      {item.icon && <item.icon />}
                      <span>{item.title}</span>
                      <ChevronRight
                        className={`ml-auto transition-transform ${
                          openItems.has(item.title) ? "rotate-90" : ""
                        }`}
                      />
                    </SidebarMenuButton>
                  ) : (
                    // Simple menu item
                    <SidebarMenuButton
                      tooltip={item.title}
                      isActive={pathname === item.url}
                      onClick={() => handleNavigation(item.url!)}
                    >
                      {item.icon && <item.icon />}
                      <span>{item.title}</span>
                    </SidebarMenuButton>
                  )}
                  {item.items && openItems.has(item.title) && (
                    <SidebarMenuSub>
                      {item.items.map((subItem) => (
                        <SidebarMenuSubItem key={subItem.title}>
                          <SidebarMenuSubButton
                            isActive={pathname === subItem.url}
                            onClick={() => handleNavigation(subItem.url)}
                          >
                            <span>{subItem.title}</span>
                          </SidebarMenuSubButton>
                        </SidebarMenuSubItem>
                      ))}
                    </SidebarMenuSub>
                  )}
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* System Administration */}
        <SidebarGroup>
          <SidebarGroupLabel>System Administration</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {systemItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    tooltip={item.title}
                    isActive={pathname === item.url}
                    onClick={() => handleNavigation(item.url)}
                  >
                    <item.icon />
                    <span>{item.title}</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Quick Actions */}
        <SidebarGroup>
          <SidebarGroupLabel>Quick Actions</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton tooltip="Notifications">
                  <Bell />
                  <span>Notifications</span>
                  <Badge variant="destructive" className="ml-auto">
                    3
                  </Badge>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton tooltip="Help & Support">
                  <HelpCircle />
                  <span>Help & Support</span>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="mb-6">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              tooltip={`${currentUser?.username || "Admin"} - ${
                currentUser?.email || "<EMAIL>"
              }`}
            >
              <Avatar className="h-5 w-5 rounded-lg">
                <AvatarImage
                  src={currentUser?.profile_picture || ""}
                  alt={currentUser?.username || "Admin"}
                />
                <AvatarFallback className="rounded-lg text-xs">
                  {currentUser?.username?.charAt(0).toUpperCase() || "A"}
                </AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">
                  {currentUser?.username || "Admin"}
                </span>
                <span className="truncate text-xs text-muted-foreground">
                  {currentUser?.email || "<EMAIL>"}
                </span>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton tooltip="Toggle Theme">
              <Settings className="h-4 w-4" />
              <span className="flex items-center justify-between flex-1">
                <span className="text-sm font-medium">Theme</span>
                <ThemeToggle variant="button" className="h-7 w-7" />
              </span>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton
              onClick={handleLogout}
              tooltip="Sign out"
              className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950"
            >
              <LogOut />
              <span>Sign out</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
