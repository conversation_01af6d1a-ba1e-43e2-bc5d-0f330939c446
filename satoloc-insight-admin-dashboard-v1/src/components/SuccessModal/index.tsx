import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, User as UserIcon } from "lucide-react";
import { User } from "@/lib/api";

interface SuccessModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  createdUser: User | null;
  onClose: () => void;
  onViewUserDetails: () => void;
}

export default function SuccessModal({
  isOpen,
  onOpenChange,
  createdUser,
  onClose,
  onViewUserDetails,
}: SuccessModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <div className="text-center p-6">
          <div className="mx-auto flex items-center justify-center w-16 h-16 rounded-full bg-green-100 dark:bg-green-900/20 mb-4">
            <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-500" />
          </div>

          <DialogTitle className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            User Created Successfully!
          </DialogTitle>

          <DialogDescription className="text-gray-600 dark:text-gray-400 mb-6">
            The new user account has been created and is ready to use.
          </DialogDescription>

          {createdUser && (
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-6 text-left">
              <div className="flex items-center mb-3">
                <UserIcon className="w-4 h-4 text-gray-500 mr-2" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  User Details
                </span>
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">Username:</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {createdUser.username}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Email:</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {createdUser.email}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Role:</span>
                  <span
                    className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      createdUser.role === "admin"
                        ? "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
                        : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
                    }`}
                  >
                    {createdUser.role}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Plan:</span>
                  <span
                    className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      createdUser.subscription_plan === "pro"
                        ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                        : createdUser.subscription_plan === "enterprise"
                        ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                        : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
                    }`}
                  >
                    {createdUser.subscription_plan}
                  </span>
                </div>
              </div>
            </div>
          )}

          <div className="flex gap-3">
            <Button variant="outline" onClick={onClose} className="flex-1">
              Close
            </Button>
            <Button onClick={onViewUserDetails} className="flex-1">
              View User Details
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
