"use client";

import React from "react";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Heart,
  Shield,
  Database,
  Activity,
  Clock,
  Users,
  BarChart3,
  ChevronUp,
} from "lucide-react";
import { useFooter } from "@/hooks/useFooter";
import { useTheme } from "next-themes";

export function AdminFooter() {
  const currentYear = new Date().getFullYear();
  const { isMinimized, currentTime, toggleMinimized } = useFooter();
  const { theme } = useTheme();

  return (
    <footer
      className={`fixed bottom-0 left-0 right-0 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-t border-border z-40 transition-all duration-300 ${
        isMinimized ? "translate-y-12" : "translate-y-0"
      }`}
    >
      {/* Minimize/Expand button */}
      <Button
        variant="ghost"
        size="sm"
        className="absolute -top-8 right-4 h-8 w-8 p-0 bg-background border border-border rounded-t-md hover:bg-muted transition-colors"
        onClick={toggleMinimized}
      >
        <ChevronUp
          className={`h-4 w-4 transition-transform ${
            isMinimized ? "rotate-180" : ""
          }`}
        />
      </Button>

      <div className="container mx-auto px-4 py-3">
        {!isMinimized ? (
          <>
            {/* Desktop Layout */}
            <div className="hidden lg:flex items-center justify-between">
              {/* Left section - Company info */}
              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <Shield className="h-4 w-4 text-primary" />
                  <span className="font-medium text-foreground">
                    Satoloc Admin
                  </span>
                </div>
                <Separator orientation="vertical" className="h-4" />
                <span>© {currentYear} Satoloc Platform</span>
                <Separator orientation="vertical" className="h-4" />
                <div className="flex items-center space-x-1">
                  <Clock className="h-3 w-3" />
                  <span className="font-mono text-foreground">
                    {currentTime}
                  </span>
                </div>
              </div>

              {/* Center section - System status */}
              <div className="flex items-center space-x-3">
                <Badge
                  variant="success"
                  className="flex items-center space-x-1 bg-green-600 hover:bg-green-700"
                >
                  <Activity className="h-3 w-3" />
                  <span>System Online</span>
                </Badge>
                <Badge
                  variant="outline"
                  className="flex items-center space-x-1 border-green-500 text-green-700 dark:text-green-400"
                >
                  <Database className="h-3 w-3" />
                  <span>DB Connected</span>
                </Badge>
                <Badge
                  variant="secondary"
                  className="flex items-center space-x-1 bg-secondary/60"
                >
                  <Users className="h-3 w-3" />
                  <span>Users Active</span>
                </Badge>
                <Badge
                  variant="outline"
                  className="flex items-center space-x-1 border-blue-500 text-blue-700 dark:text-blue-400"
                >
                  <BarChart3 className="h-3 w-3" />
                  <span>Analytics</span>
                </Badge>
              </div>

              {/* Right section - Links and version */}
              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                <div className="flex items-center space-x-3">
                  <a
                    href="/dashboard/help"
                    className="hover:text-foreground transition-colors"
                  >
                    Help
                  </a>
                  <a
                    href="/dashboard/docs"
                    className="hover:text-foreground transition-colors"
                  >
                    Documentation
                  </a>
                  <a
                    href="/dashboard/support"
                    className="hover:text-foreground transition-colors"
                  >
                    Support
                  </a>
                </div>
                <Separator orientation="vertical" className="h-4" />
                <div className="flex items-center space-x-1">
                  <span>Made with</span>
                  <Heart className="h-3 w-3 text-red-500 fill-red-500" />
                  <span className="font-mono">v1.0.0</span>
                </div>
              </div>
            </div>

            {/* Mobile Layout */}
            <div className="lg:hidden space-y-3">
              {/* Top row - Company and time */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Shield className="h-4 w-4 text-primary" />
                  <span className="font-medium text-foreground text-sm">
                    Satoloc Admin
                  </span>
                </div>
                <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  <span className="font-mono text-foreground">
                    {currentTime}
                  </span>
                </div>
              </div>

              {/* Status badges */}
              <div className="flex flex-wrap items-center justify-center gap-2">
                <Badge
                  variant="success"
                  className="flex items-center space-x-1 text-xs bg-green-600"
                >
                  <Activity className="h-3 w-3" />
                  <span>Online</span>
                </Badge>
                <Badge
                  variant="outline"
                  className="flex items-center space-x-1 text-xs border-green-500 text-green-700 dark:text-green-400"
                >
                  <Database className="h-3 w-3" />
                  <span>DB OK</span>
                </Badge>
                <Badge
                  variant="secondary"
                  className="flex items-center space-x-1 text-xs"
                >
                  <Users className="h-3 w-3" />
                  <span>Active</span>
                </Badge>
              </div>

              {/* Links and copyright */}
              <div className="flex flex-col items-center space-y-2 text-xs text-muted-foreground">
                <div className="flex items-center space-x-4">
                  <a
                    href="/dashboard/help"
                    className="hover:text-foreground transition-colors"
                  >
                    Help
                  </a>
                  <a
                    href="/dashboard/docs"
                    className="hover:text-foreground transition-colors"
                  >
                    Docs
                  </a>
                  <a
                    href="/dashboard/support"
                    className="hover:text-foreground transition-colors"
                  >
                    Support
                  </a>
                </div>
                <div className="flex items-center space-x-1">
                  <span>© {currentYear} Satoloc</span>
                  <Separator orientation="vertical" className="h-3" />
                  <span>Made with</span>
                  <Heart className="h-3 w-3 text-red-500 fill-red-500" />
                  <span className="font-mono">v1.0.0</span>
                </div>
              </div>
            </div>
          </>
        ) : (
          /* Minimized view */
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center space-x-2">
              <Shield className="h-3 w-3 text-primary" />
              <span className="text-foreground">Satoloc Admin</span>
            </div>
            <div className="flex items-center space-x-3">
              <Badge variant="success" className="h-5 px-2 bg-green-600">
                <Activity className="h-2 w-2" />
              </Badge>
              <span className="font-mono text-foreground">{currentTime}</span>
            </div>
          </div>
        )}
      </div>
    </footer>
  );
}

export default AdminFooter;
