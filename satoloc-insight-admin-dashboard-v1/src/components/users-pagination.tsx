"use client";

import {
  Pa<PERSON><PERSON>,
  <PERSON><PERSON>ationContent,
  <PERSON>ginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

interface UsersPaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
}

function generatePaginationItems(currentPage: number, totalPages: number) {
  const items: (number | "ellipsis")[] = [];

  if (totalPages <= 7) {
    // Show all pages if 7 or fewer
    for (let i = 1; i <= totalPages; i++) {
      items.push(i);
    }
  } else {
    // Always show first page
    items.push(1);

    if (currentPage <= 4) {
      // Show 1, 2, 3, 4, 5, ..., last
      for (let i = 2; i <= 5; i++) {
        items.push(i);
      }
      items.push("ellipsis");
      items.push(totalPages);
    } else if (currentPage >= totalPages - 3) {
      // Show 1, ..., last-4, last-3, last-2, last-1, last
      items.push("ellipsis");
      for (let i = totalPages - 4; i <= totalPages; i++) {
        items.push(i);
      }
    } else {
      // Show 1, ..., current-1, current, current+1, ..., last
      items.push("ellipsis");
      for (let i = currentPage - 1; i <= currentPage + 1; i++) {
        items.push(i);
      }
      items.push("ellipsis");
      items.push(totalPages);
    }
  }

  return items;
}

export function UsersPagination({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  onPageChange,
}: UsersPaginationProps) {
  const paginationItems = generatePaginationItems(currentPage, totalPages);

  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  const handlePageClick = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      onPageChange(page);
    }
  };

  if (totalPages <= 1) {
    return (
      <div className="flex items-center justify-between px-2 min-h-[2.5rem]">
        <div className="flex-1 text-sm text-muted-foreground whitespace-nowrap">
          Showing {totalItems} of {totalItems} users
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-between px-2 min-h-[2.5rem]">
      <div className="flex-1 text-sm text-muted-foreground whitespace-nowrap">
        Showing {startItem} to {endItem} of {totalItems} users
      </div>

      <Pagination className="w-auto">
        <PaginationContent className="gap-0.5">
          <PaginationItem>
            <PaginationPrevious
              onClick={() => handlePageClick(currentPage - 1)}
              className={`h-8 px-2 text-xs ${
                currentPage === 1
                  ? "pointer-events-none opacity-50"
                  : "cursor-pointer"
              }`}
            />
          </PaginationItem>

          {paginationItems.map((item, index) => (
            <PaginationItem key={index}>
              {item === "ellipsis" ? (
                <PaginationEllipsis className="h-8 w-8" />
              ) : (
                <PaginationLink
                  onClick={() => handlePageClick(item)}
                  isActive={currentPage === item}
                  className="cursor-pointer h-8 w-8 text-xs"
                >
                  {item}
                </PaginationLink>
              )}
            </PaginationItem>
          ))}

          <PaginationItem>
            <PaginationNext
              onClick={() => handlePageClick(currentPage + 1)}
              className={`h-8 px-2 text-xs ${
                currentPage === totalPages
                  ? "pointer-events-none opacity-50"
                  : "cursor-pointer"
              }`}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
}
