"use client";

import { usePathname } from "next/navigation";
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import { AdminSidebar } from "@/components/admin-sidebar";
import { AdminFooter } from "@/components/admin-footer";

interface ConditionalSidebarLayoutProps {
  children: React.ReactNode;
}

export function ConditionalSidebarLayout({
  children,
}: ConditionalSidebarLayoutProps) {
  const pathname = usePathname();

  // Show sidebar only on dashboard routes
  const shouldShowSidebar = pathname.startsWith("/dashboard");

  if (shouldShowSidebar) {
    return (
      <SidebarProvider>
        <div className="flex min-h-screen w-full">
          <AdminSidebar />
          <SidebarInset>
            <div className="flex flex-col min-h-screen">
              <main className="flex-1 pb-24 relative">{children}</main>
            </div>
          </SidebarInset>
        </div>
        <AdminFooter />
      </SidebarProvider>
    );
  }

  // For non-dashboard pages (like login), show without sidebar but with footer
  return (
    <div className="flex flex-col min-h-screen">
      <main className="flex-1 pb-24 relative">{children}</main>
      <AdminFooter />
    </div>
  );
}
