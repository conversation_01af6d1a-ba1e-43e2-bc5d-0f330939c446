"use client";

import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { LightbulbIcon, Sprout, Trash2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import {
  useAddWebsiteStrategyGap,
  useRemoveWebsiteStrategyGap,
  useAddWebsiteGrowthOpportunity,
  useRemoveWebsiteGrowthOpportunity,
} from "@/internal-api/seo/advance-seo";

interface StrategyGap {
  id: number;
  text: string;
}

interface GrowthOpportunity {
  id: number;
  text: string;
}

interface StrategyAndGrowthProps {
  id: string;
  strategyGaps: StrategyGap[];
  growthOpportunities: GrowthOpportunity[];
  editingStrategyGaps: boolean;
  setEditingStrategyGaps: React.Dispatch<React.SetStateAction<boolean>>;
  editingGrowthOpportunities: boolean;
  setEditingGrowthOpportunities: React.Dispatch<React.SetStateAction<boolean>>;
  newStrategyGapText: string;
  setNewStrategyGapText: React.Dispatch<React.SetStateAction<string>>;
  newGrowthOpportunityText: string;
  setNewGrowthOpportunityText: React.Dispatch<React.SetStateAction<string>>;
  refetch: () => void;
}

export default function StrategyAndGrowth({
  id,
  strategyGaps,
  growthOpportunities,
  editingStrategyGaps,
  setEditingStrategyGaps,
  editingGrowthOpportunities,
  setEditingGrowthOpportunities,
  newStrategyGapText,
  setNewStrategyGapText,
  newGrowthOpportunityText,
  setNewGrowthOpportunityText,
  refetch,
}: StrategyAndGrowthProps) {
  const { toast } = useToast();

  // Mutations
  const addStrategyGap = useAddWebsiteStrategyGap({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Strategy gap added successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add strategy gap",
        variant: "destructive",
      });
    },
  });

  const removeStrategyGap = useRemoveWebsiteStrategyGap({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Strategy gap removed successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to remove strategy gap",
        variant: "destructive",
      });
    },
  });

  const addGrowthOpportunity = useAddWebsiteGrowthOpportunity({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Growth opportunity added successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add growth opportunity",
        variant: "destructive",
      });
    },
  });

  const removeGrowthOpportunity = useRemoveWebsiteGrowthOpportunity({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Growth opportunity removed successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to remove growth opportunity",
        variant: "destructive",
      });
    },
  });

  // Strategy Gap handlers
  const handleToggleStrategyGapEditor = () => {
    setEditingStrategyGaps(!editingStrategyGaps);
  };

  const handleAddStrategyGap = () => {
    if (!newStrategyGapText) return;

    addStrategyGap.mutate({
      websiteId: id,
      text: newStrategyGapText,
    });

    // Clear the input
    setNewStrategyGapText("");
  };

  const handleRemoveStrategyGap = (gapId: number) => {
    removeStrategyGap.mutate({
      websiteId: id,
      gapId: gapId,
    });
  };

  const handleStrategyGapTextChange = (value: string) => {
    setNewStrategyGapText(value);
  };

  // Growth Opportunity handlers
  const handleToggleGrowthOpportunityEditor = () => {
    setEditingGrowthOpportunities(!editingGrowthOpportunities);
  };

  const handleAddGrowthOpportunity = () => {
    if (!newGrowthOpportunityText) return;

    addGrowthOpportunity.mutate({
      websiteId: id,
      text: newGrowthOpportunityText,
    });

    // Clear the input
    setNewGrowthOpportunityText("");
  };

  const handleRemoveGrowthOpportunity = (opportunityId: number) => {
    removeGrowthOpportunity.mutate({
      websiteId: id,
      opportunityId: opportunityId,
    });
  };

  const handleGrowthOpportunityTextChange = (value: string) => {
    setNewGrowthOpportunityText(value);
  };

  return (
    <Card className="shadow-none dark:border-muted-foreground">
      <CardHeader className="text-foreground">
        <CardTitle>Strategy Gaps and Growth Opportunities</CardTitle>
        <CardDescription className="text-muted-foreground text-sm">
          Manage strategy gaps and growth opportunities for this website
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Strategy Gaps Section */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-foreground">
              Strategy Gaps
            </h3>
            <Button
              variant="outline"
              size="sm"
              onClick={handleToggleStrategyGapEditor}
              className="h-7"
            >
              {editingStrategyGaps ? "Done" : "Edit Strategy Gaps"}
            </Button>
          </div>

          {strategyGaps && strategyGaps.length > 0 ? (
            <div className="space-y-2">
              {strategyGaps.map((gap) => (
                <div
                  key={gap.id}
                  className={`flex justify-between items-center p-2 rounded ${
                    editingStrategyGaps ? "bg-amber-50" : ""
                  }`}
                >
                  <div className="flex items-start gap-2">
                    <LightbulbIcon className="h-4 w-4 text-amber-500 mt-1" />
                    <span>{gap.text}</span>
                  </div>

                  {editingStrategyGaps && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 text-red-500"
                      onClick={() => handleRemoveStrategyGap(gap.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <p className="text-muted-foreground text-sm">
              No strategy gaps identified yet. Add some to help track
              improvement areas.
            </p>
          )}

          {editingStrategyGaps && (
            <div className="flex space-x-2 mt-4">
              <Input
                className="border-foreground dark:border-muted-foreground"
                placeholder="Add new strategy gap"
                value={newStrategyGapText}
                onChange={(e) => handleStrategyGapTextChange(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault();
                    handleAddStrategyGap();
                  }
                }}
              />
              <Button
                size="sm"
                onClick={handleAddStrategyGap}
                className="bg-[#1279b4] hover:bg-[#1279b4]/80 text-white"
              >
                Add
              </Button>
            </div>
          )}
        </div>

        <Separator className="border-muted-foreground" />

        {/* Growth Opportunities Section */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-foreground">
              Growth Opportunities
            </h3>
            <Button
              variant="outline"
              size="sm"
              onClick={handleToggleGrowthOpportunityEditor}
              className="h-7"
            >
              {editingGrowthOpportunities ? "Done" : "Edit Opportunities"}
            </Button>
          </div>

          {growthOpportunities && growthOpportunities.length > 0 ? (
            <div className="space-y-2">
              {growthOpportunities.map((opportunity) => (
                <div
                  key={opportunity.id}
                  className={`flex justify-between items-center p-2 rounded ${
                    editingGrowthOpportunities ? "bg-green-50" : ""
                  }`}
                >
                  <div className="flex items-start gap-2">
                    <Sprout className="h-4 w-4 text-green-500 mt-1" />
                    <span>{opportunity.text}</span>
                  </div>

                  {editingGrowthOpportunities && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 text-red-500"
                      onClick={() =>
                        handleRemoveGrowthOpportunity(opportunity.id)
                      }
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <p className="text-muted-foreground text-sm">
              No growth opportunities identified yet. Add some to help track
              potential improvements.
            </p>
          )}

          {editingGrowthOpportunities && (
            <div className="flex space-x-2 mt-4">
              <Input
                placeholder="Add new growth opportunity"
                value={newGrowthOpportunityText}
                onChange={(e) =>
                  handleGrowthOpportunityTextChange(e.target.value)
                }
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault();
                    handleAddGrowthOpportunity();
                  }
                }}
              />
              <Button
                size="sm"
                onClick={handleAddGrowthOpportunity}
                className="bg-[#1279b4] hover:bg-[#1279b4]/80 text-white"
              >
                Add
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
