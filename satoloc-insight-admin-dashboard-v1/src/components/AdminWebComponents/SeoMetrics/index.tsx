"use client";

import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, Save } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useUpdateSEOMetrics } from "@/internal-api/seo/advance-seo";

interface SeoMetrics {
  domain_rating?: {
    description?: string;
    score?: number;
    previous_score?: number | null;
  };
  organic_traffic?: {
    description?: string;
    score?: number;
    previous_score?: number | null;
  };
  search_rankings?: {
    description?: string;
    score?: number;
    previous_score?: number | null;
  };
  search_terms?: {
    description?: string;
    score?: number;
    previous_score?: number | null;
  };
  site_links?: {
    description?: string;
    score?: number;
    previous_score?: number | null;
  };
  content_score?: {
    description?: string;
    score?: number;
    previous_score?: number | null;
  };
  backlinks?: number;
}

interface EditedSeoMetrics {
  domain_rating_score?: number | null;
  domain_rating_previous_score?: number | null;
  organic_traffic_score?: number | null;
  organic_traffic_previous_score?: number | null;
  search_rankings_score?: number | null;
  search_rankings_previous_score?: number | null;
  search_terms_score?: number | null;
  search_terms_previous_score?: number | null;
  site_links_score?: number | null;
  site_links_previous_score?: number | null;
  content_score_value?: number | null;
  content_score_previous_score?: number | null;
  backlinks?: number | null;
}

interface SeoMetricsProps {
  id: string;
  seoMetrics: SeoMetrics;
  editedSeoMetrics: EditedSeoMetrics;
  setEditedSeoMetrics: React.Dispatch<React.SetStateAction<EditedSeoMetrics>>;
  isEditingSeoMetrics: boolean;
  setIsEditingSeoMetrics: React.Dispatch<React.SetStateAction<boolean>>;
  refetch: () => void;
}

export default function SeoMetrics({
  id,
  seoMetrics,
  editedSeoMetrics,
  setEditedSeoMetrics,
  isEditingSeoMetrics,
  setIsEditingSeoMetrics,
  refetch,
}: SeoMetricsProps) {
  const { toast } = useToast();

  // Mutation
  const updateSEOMetrics = useUpdateSEOMetrics({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "SEO metrics updated successfully",
      });
      refetch();
      setIsEditingSeoMetrics(false);
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update SEO metrics",
        variant: "destructive",
      });
    },
  });

  // Handler to submit SEO metrics update
  const handleSaveSeoMetrics = () => {
    // Send flattened data directly matching the backend model field names
    const transformedData = {
      domain_rating_score: editedSeoMetrics.domain_rating_score,
      domain_rating_previous_score:
        editedSeoMetrics.domain_rating_previous_score,
      organic_traffic_score: editedSeoMetrics.organic_traffic_score,
      organic_traffic_previous_score:
        editedSeoMetrics.organic_traffic_previous_score,
      search_rankings_score: editedSeoMetrics.search_rankings_score,
      search_rankings_previous_score:
        editedSeoMetrics.search_rankings_previous_score,
      search_terms_score: editedSeoMetrics.search_terms_score,
      search_terms_previous_score: editedSeoMetrics.search_terms_previous_score,
      site_links_score: editedSeoMetrics.site_links_score,
      site_links_previous_score: editedSeoMetrics.site_links_previous_score,
      content_score: editedSeoMetrics.content_score_value,
      content_score_previous_score:
        editedSeoMetrics.content_score_previous_score,
      backlinks_count: editedSeoMetrics.backlinks,
    };

    // Submit the transformed data
    updateSEOMetrics.mutate({
      websiteId: id,
      data: transformedData,
    });
  };

  return (
    <Card className="shadow-none dark:border-muted-foreground">
      <CardHeader className="text-foreground flex flex-row items-center justify-between">
        <div>
          <CardTitle>SEO Metrics</CardTitle>
          <CardDescription className="text-muted-foreground text-sm">
            Manage SEO metrics for the website
          </CardDescription>
        </div>
        <Button
          className="bg-[#1279b4] hover:bg-[#1279b4]/80 text-white"
          variant={isEditingSeoMetrics ? "destructive" : "outline"}
          onClick={() => {
            if (isEditingSeoMetrics) {
              // If canceling edit, revert to original values
              setEditedSeoMetrics({ ...seoMetrics });
            }
            setIsEditingSeoMetrics(!isEditingSeoMetrics);
          }}
        >
          {isEditingSeoMetrics ? "Cancel" : "Edit Metrics"}
        </Button>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Domain Rating Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-foreground">
            Domain Rating
          </h3>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="domain_rating_score">Current Score</Label>
              <Input
                className="border-foreground dark:border-muted-foreground"
                id="domain_rating_score"
                type="number"
                placeholder={
                  seoMetrics.domain_rating?.score !== undefined
                    ? `${seoMetrics.domain_rating.score}`
                    : "Current domain rating"
                }
                disabled={!isEditingSeoMetrics}
                value={editedSeoMetrics.domain_rating_score ?? ""}
                onChange={(e) =>
                  setEditedSeoMetrics({
                    ...editedSeoMetrics,
                    domain_rating_score: e.target.value
                      ? parseFloat(e.target.value)
                      : null,
                  })
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="domain_rating_previous_score">
                Previous Score
              </Label>
              <Input
                className="border-foreground dark:border-muted-foreground"
                id="domain_rating_previous_score"
                type="number"
                placeholder={
                  seoMetrics.domain_rating?.previous_score !== undefined
                    ? `${seoMetrics.domain_rating.previous_score}`
                    : "Previous domain rating"
                }
                disabled={!isEditingSeoMetrics}
                value={editedSeoMetrics.domain_rating_previous_score ?? ""}
                onChange={(e) =>
                  setEditedSeoMetrics({
                    ...editedSeoMetrics,
                    domain_rating_previous_score: e.target.value
                      ? parseFloat(e.target.value)
                      : null,
                  })
                }
              />
            </div>
          </div>
        </div>

        {/* Organic Traffic Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-foreground">
            Organic Traffic
          </h3>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="organic_traffic_score">Current Traffic</Label>
              <Input
                className="border-foreground dark:border-muted-foreground"
                id="organic_traffic_score"
                type="number"
                placeholder={
                  seoMetrics.organic_traffic?.score !== undefined
                    ? `${seoMetrics.organic_traffic.score}`
                    : "Current organic traffic"
                }
                disabled={!isEditingSeoMetrics}
                value={editedSeoMetrics.organic_traffic_score ?? ""}
                onChange={(e) =>
                  setEditedSeoMetrics({
                    ...editedSeoMetrics,
                    organic_traffic_score: e.target.value
                      ? parseInt(e.target.value)
                      : null,
                  })
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="organic_traffic_previous_score">
                Previous Traffic
              </Label>
              <Input
                className="border-foreground dark:border-muted-foreground"
                id="organic_traffic_previous_score"
                type="number"
                placeholder={
                  seoMetrics.organic_traffic?.previous_score !== undefined
                    ? `${seoMetrics.organic_traffic.previous_score}`
                    : "Previous organic traffic"
                }
                disabled={!isEditingSeoMetrics}
                value={editedSeoMetrics.organic_traffic_previous_score ?? ""}
                onChange={(e) =>
                  setEditedSeoMetrics({
                    ...editedSeoMetrics,
                    organic_traffic_previous_score: e.target.value
                      ? parseInt(e.target.value)
                      : null,
                  })
                }
              />
            </div>
          </div>
        </div>

        {/* Search Rankings Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-foreground">
            Search Rankings
          </h3>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search_rankings_score">Current Ranking</Label>
              <Input
                className="border-foreground dark:border-muted-foreground"
                id="search_rankings_score"
                type="number"
                placeholder={
                  seoMetrics.search_rankings?.score !== undefined
                    ? `${seoMetrics.search_rankings.score}%`
                    : "Current search ranking"
                }
                disabled={!isEditingSeoMetrics}
                value={editedSeoMetrics.search_rankings_score ?? ""}
                onChange={(e) =>
                  setEditedSeoMetrics({
                    ...editedSeoMetrics,
                    search_rankings_score: e.target.value
                      ? parseFloat(e.target.value)
                      : null,
                  })
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="search_rankings_previous_score">
                Previous Ranking
              </Label>
              <Input
                className="border-foreground dark:border-muted-foreground"
                id="search_rankings_previous_score"
                type="number"
                placeholder={
                  seoMetrics.search_rankings?.previous_score !== undefined
                    ? `${seoMetrics.search_rankings.previous_score}%`
                    : "Previous search ranking"
                }
                disabled={!isEditingSeoMetrics}
                value={editedSeoMetrics.search_rankings_previous_score ?? ""}
                onChange={(e) =>
                  setEditedSeoMetrics({
                    ...editedSeoMetrics,
                    search_rankings_previous_score: e.target.value
                      ? parseFloat(e.target.value)
                      : null,
                  })
                }
              />
            </div>
          </div>
        </div>

        {/* Search Terms Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-foreground">
            Search Terms
          </h3>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search_terms_score">Current Terms</Label>
              <Input
                className="border-foreground dark:border-muted-foreground"
                id="search_terms_score"
                type="number"
                placeholder={
                  seoMetrics.search_terms?.score !== undefined
                    ? `${seoMetrics.search_terms.score}`
                    : "Current search terms"
                }
                disabled={!isEditingSeoMetrics}
                value={editedSeoMetrics.search_terms_score ?? ""}
                onChange={(e) =>
                  setEditedSeoMetrics({
                    ...editedSeoMetrics,
                    search_terms_score: e.target.value
                      ? parseInt(e.target.value)
                      : null,
                  })
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="search_terms_previous_score">
                Previous Terms
              </Label>
              <Input
                className="border-foreground dark:border-muted-foreground"
                id="search_terms_previous_score"
                type="number"
                placeholder={
                  seoMetrics.search_terms?.previous_score !== undefined
                    ? `${seoMetrics.search_terms.previous_score}`
                    : "Previous search terms"
                }
                disabled={!isEditingSeoMetrics}
                value={editedSeoMetrics.search_terms_previous_score ?? ""}
                onChange={(e) =>
                  setEditedSeoMetrics({
                    ...editedSeoMetrics,
                    search_terms_previous_score: e.target.value
                      ? parseInt(e.target.value)
                      : null,
                  })
                }
              />
            </div>
          </div>
        </div>

        {/* Site Links Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-foreground">Site Links</h3>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="site_links_score">Current Links</Label>
              <Input
                className="border-foreground dark:border-muted-foreground"
                id="site_links_score"
                type="number"
                placeholder="Current site links"
                disabled={!isEditingSeoMetrics}
                value={editedSeoMetrics.site_links_score || ""}
                onChange={(e) =>
                  setEditedSeoMetrics({
                    ...editedSeoMetrics,
                    site_links_score: e.target.value
                      ? parseInt(e.target.value)
                      : null,
                  })
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="site_links_previous_score">Previous Links</Label>
              <Input
                className="border-foreground dark:border-muted-foreground"
                id="site_links_previous_score"
                type="number"
                placeholder="Previous site links"
                disabled={!isEditingSeoMetrics}
                value={editedSeoMetrics.site_links_previous_score || ""}
                onChange={(e) =>
                  setEditedSeoMetrics({
                    ...editedSeoMetrics,
                    site_links_previous_score: e.target.value
                      ? parseInt(e.target.value)
                      : null,
                  })
                }
              />
            </div>
          </div>
        </div>

        {/* Content Score Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-foreground">
            Content Score
          </h3>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="content_score">Current Score</Label>
              <Input
                className="border-foreground dark:border-muted-foreground"
                id="content_score"
                type="number"
                placeholder="Current content score"
                disabled={!isEditingSeoMetrics}
                value={editedSeoMetrics.content_score_value || ""}
                onChange={(e) =>
                  setEditedSeoMetrics({
                    ...editedSeoMetrics,
                    content_score_value: e.target.value
                      ? parseFloat(e.target.value)
                      : null,
                  })
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="content_score_previous_score">
                Previous Score
              </Label>
              <Input
                className="border-foreground dark:border-muted-foreground"
                id="content_score_previous_score"
                type="number"
                placeholder="Previous content score"
                disabled={!isEditingSeoMetrics}
                value={editedSeoMetrics.content_score_previous_score || ""}
                onChange={(e) =>
                  setEditedSeoMetrics({
                    ...editedSeoMetrics,
                    content_score_previous_score: e.target.value
                      ? parseFloat(e.target.value)
                      : null,
                  })
                }
              />
            </div>
          </div>
        </div>

        {/* Backlinks Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-foreground">Backlinks</h3>
          <div className="space-y-2">
            <Label htmlFor="backlinks_count">Backlinks Count</Label>
            <Input
              className="border-foreground dark:border-muted-foreground"
              id="backlinks_count"
              type="number"
              placeholder="Number of backlinks"
              disabled={!isEditingSeoMetrics}
              value={editedSeoMetrics.backlinks || ""}
              onChange={(e) =>
                setEditedSeoMetrics({
                  ...editedSeoMetrics,
                  backlinks: e.target.value ? parseInt(e.target.value) : null,
                })
              }
            />
          </div>
        </div>
      </CardContent>
      {isEditingSeoMetrics && (
        <CardFooter>
          <Button
            className="bg-[#1279b4] hover:bg-[#1279b4]/80 text-white"
            onClick={handleSaveSeoMetrics}
            disabled={updateSEOMetrics.isPending}
          >
            {updateSEOMetrics.isPending ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save SEO Metrics
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}
