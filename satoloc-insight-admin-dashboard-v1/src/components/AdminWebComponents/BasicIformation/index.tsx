"use client";

import React from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>oot<PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2, Save } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useUpdateWebsiteBasicInfo } from "@/internal-api/seo/advance-seo";

interface WebsiteData {
  url: string;
  industry: string;
  language: string;
}

interface BasicInformationProps {
  id: string;
  websiteData: WebsiteData;
  setWebsiteData: React.Dispatch<React.SetStateAction<WebsiteData>>;
  refetch: () => void;
}

export default function BasicInformation({
  id,
  websiteData,
  setWebsiteData,
  refetch,
}: BasicInformationProps) {
  const { toast } = useToast();

  // Mutation
  const updateBasicInfo = useUpdateWebsiteBasicInfo({
    onSuccess: (data: any) => {
      toast({
        title: "Success",
        description: "Website information updated successfully",
      });

      // Force a refetch to get the updated data
      setTimeout(() => {
        refetch();
      }, 500);
    },
    onError: (error: Error) => {
      let errorMessage =
        error.message || "Failed to update website information";

      // Try to extract more detailed error information if available
      if (error instanceof Error && "response" in error) {
        const responseData = (error as any).response?.data;
        if (responseData) {
          // Format and display all validation errors
          const errorDetails = Object.entries(responseData)
            .map(([key, value]) => `${key}: ${value}`)
            .join(", ");
          errorMessage = errorDetails || errorMessage;
        }
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  const handleSaveBasicInfo = async () => {
    // No URL formatting - use URL exactly as entered
    const data = {
      ...websiteData,
    };

    try {
      updateBasicInfo.mutate({
        id,
        data,
      });
    } catch (error) {
      console.error("Error in handleSaveBasicInfo:", error);
      toast({
        title: "Error",
        description:
          "There was an error updating the website information. Please check the console for details.",
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="shadow-none dark:border-muted-foreground">
      <CardHeader>
        <CardTitle className="text-foreground">Basic Information</CardTitle>
        <CardDescription className="text-foreground">
          Edit the basic details of the website
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="url">URL</Label>
          <Input
            className="border-foreground dark:border-muted-foreground"
            id="url"
            placeholder="example.com"
            value={websiteData.url}
            onChange={(e) =>
              setWebsiteData({ ...websiteData, url: e.target.value })
            }
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="industry">Industry</Label>
          <Select
            value={websiteData.industry}
            onValueChange={(value) => {
              setWebsiteData({ ...websiteData, industry: value });
            }}
            defaultValue={websiteData.industry}
          >
            <SelectTrigger
              id="industry"
              className="border-foreground dark:border-muted-foreground"
            >
              <SelectValue placeholder="Select industry" />
            </SelectTrigger>
            <SelectContent className="border-foreground dark:border-muted-foreground">
              <SelectItem value="Technology">Technology</SelectItem>
              <SelectItem value="Finance">Finance</SelectItem>
              <SelectItem value="Healthcare">Healthcare</SelectItem>
              <SelectItem value="Education">Education</SelectItem>
              <SelectItem value="E-commerce">E-commerce</SelectItem>
              <SelectItem value="Travel">Travel</SelectItem>
              <SelectItem value="Food & Beverage">Food & Beverage</SelectItem>
              <SelectItem value="Entertainment">Entertainment</SelectItem>
              <SelectItem value="Fintech">Fintech</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="language">Primary Language</Label>
          <Select
            value={websiteData.language}
            onValueChange={(value) =>
              setWebsiteData({ ...websiteData, language: value })
            }
          >
            <SelectTrigger
              id="language"
              className="border-foreground dark:border-muted-foreground"
            >
              <SelectValue placeholder="Select language" />
            </SelectTrigger>
            <SelectContent className="border-foreground dark:border-muted-foreground">
              <SelectItem value="en">English</SelectItem>
              <SelectItem value="tr">Turkish</SelectItem>
              <SelectItem value="es">Spanish</SelectItem>
              <SelectItem value="fr">French</SelectItem>
              <SelectItem value="de">German</SelectItem>
              <SelectItem value="zh">Chinese</SelectItem>
              <SelectItem value="ja">Japanese</SelectItem>
              <SelectItem value="ar">Arabic</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardContent>
      <CardFooter>
        <Button
          onClick={handleSaveBasicInfo}
          disabled={!!updateBasicInfo.isPending}
          className="bg-[#1279b4] hover:bg-[#1279b4]/80 text-white"
        >
          {updateBasicInfo.isPending ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          Save Changes
        </Button>
      </CardFooter>
    </Card>
  );
}
