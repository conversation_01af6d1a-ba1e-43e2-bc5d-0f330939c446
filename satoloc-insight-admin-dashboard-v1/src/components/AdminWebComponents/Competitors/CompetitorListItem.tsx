"use client";

import React, { useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  CheckCircle,
  ExternalLink,
  KeySquare,
  Loader2,
  PlusCircle,
  Save,
  Trash2,
  AlertTriangle,
  X,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { useUploadCompetitorKeywordsCsv } from "@/internal-api/seo/advance-seo";

interface Competitor {
  id?: number;
  name: string;
  target_url: string;
  description: string;
  rank?: number;
  top_keywords?: string[];
  strategy_gaps?: { id: number; text: string }[];
  growth_opportunities?: { id: number; text: string }[];
  ranking_issues?: {
    id: number;
    title: string;
    description: string;
    impact: string;
  }[];
  content_recommendations?: {
    id: number;
    title: string;
    description: string;
    impact: string;
    estimated_hours: number;
    is_opportunity: boolean;
  }[];
}

// --- CSV upload component for competitor keywords ---
type CompetitorKeywordsCsvUploadProps = {
  websiteId: string;
  competitorId?: number;
  onSuccess: (added_count: number) => void;
};

const CompetitorKeywordsCsvUpload: React.FC<
  CompetitorKeywordsCsvUploadProps
> = ({ websiteId, competitorId, onSuccess }) => {
  const { toast } = useToast();
  const [file, setFile] = useState<File | undefined>(undefined);
  const uploadKeywordsCsv = useUploadCompetitorKeywordsCsv();

  if (!competitorId) return null;

  return (
    <form
      className="flex items-center gap-2"
      onSubmit={async (e) => {
        e.preventDefault();
        if (!file) return;
        uploadKeywordsCsv.mutate(
          { websiteId, competitorId, file },
          {
            onSuccess: (data: { added_count: number }) => {
              onSuccess(data.added_count);
              setFile(undefined);
            },
            onError: (err: any) => {
              toast({
                title: "Upload failed",
                description: err?.response?.data?.error || err.message,
                variant: "destructive",
              });
            },
          }
        );
      }}
    >
      <Input
        type="file"
        accept=".csv,text/csv"
        onChange={(e) => {
          if (e.target.files && e.target.files[0]) {
            setFile(e.target.files[0]);
          }
        }}
        className="w-auto dark:border-muted-foreground"
      />
      <Button
        type="submit"
        size="sm"
        disabled={!file || uploadKeywordsCsv.isPending}
        className="bg-[#1279b4] hover:bg-[#1279b4]/80 text-white"
      >
        {uploadKeywordsCsv.isPending ? (
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
        ) : null}
        Upload Keywords CSV
      </Button>
      {file && (
        <span className="text-xs text-muted-foreground">{file.name}</span>
      )}
    </form>
  );
};

interface CompetitorListItemProps {
  competitor: Competitor;
  index: number;
  id: string;
  refetch: () => void;
  editingCompetitor: number | null;
  editedCompetitor: any;
  setEditedCompetitor: (value: any) => void;
  updateCompetitor: any;
  handleSaveCompetitorEdit: (index: number) => void;
  handleCancelCompetitorEdit: () => void;
  handleStartEditingCompetitor: (index: number) => void;
  handleRemoveCompetitor: (index: number) => void;
  editingKeywords: boolean[];
  handleStartEditingKeywords: (index: number) => void;
  handleCancelEditingKeywords: (index: number) => void;
  handleSaveKeywords: (index: number) => void;
  updateCompKeywords: any;
  editedKeywords: { [key: number]: string[] };
  handleRemoveCompetitorKeyword: (
    competitorIndex: number,
    keywordIndex: number
  ) => void;
  newKeywordText: { [key: number]: string };
  handleKeywordTextChange: (index: number, text: string) => void;
  handleAddNewKeyword: (index: number) => void;
  editingRankingIssues: { [key: number]: boolean };
  handleToggleRankingIssuesEditor: (index: number) => void;
  handleRemoveRankingIssue: (issueId: number) => void;
  newRankingIssue: { [key: number]: any };
  setNewRankingIssue: (value: any) => void;
  handleAddRankingIssue: (index: number) => void;
  editingContentRecommendations: { [key: number]: boolean };
  handleToggleContentRecommendationsEditor: (index: number) => void;
  handleRemoveContentRecommendation: (recommendationId: number) => void;
  newContentRecommendation: { [key: number]: any };
  setNewContentRecommendation: (value: any) => void;
  handleAddContentRecommendation: (index: number) => void;
}

export default function CompetitorListItem({
  competitor,
  index,
  id,
  refetch,
  editingCompetitor,
  editedCompetitor,
  setEditedCompetitor,
  updateCompetitor,
  handleSaveCompetitorEdit,
  handleCancelCompetitorEdit,
  handleStartEditingCompetitor,
  handleRemoveCompetitor,
  editingKeywords,
  handleStartEditingKeywords,
  handleCancelEditingKeywords,
  handleSaveKeywords,
  updateCompKeywords,
  editedKeywords,
  handleRemoveCompetitorKeyword,
  newKeywordText,
  handleKeywordTextChange,
  handleAddNewKeyword,
  editingRankingIssues,
  handleToggleRankingIssuesEditor,
  handleRemoveRankingIssue,
  newRankingIssue,
  setNewRankingIssue,
  handleAddRankingIssue,
  editingContentRecommendations,
  handleToggleContentRecommendationsEditor,
  handleRemoveContentRecommendation,
  newContentRecommendation,
  setNewContentRecommendation,
  handleAddContentRecommendation,
}: CompetitorListItemProps) {
  const { toast } = useToast();

  return (
    <div key={index} className="p-4 border rounded space-y-2">
      {editingCompetitor === index ? (
        // Edit mode
        <div className="grid grid-cols-5 gap-2 items-start">
          <div>
            <Input
              value={editedCompetitor.name}
              onChange={(e) =>
                setEditedCompetitor({
                  ...editedCompetitor,
                  name: e.target.value,
                })
              }
              placeholder="Competitor name"
            />
          </div>
          <div>
            <Input
              value={editedCompetitor.target_url}
              onChange={(e) =>
                setEditedCompetitor({
                  ...editedCompetitor,
                  target_url: e.target.value,
                })
              }
              placeholder="https://competitor.com"
            />
          </div>
          <div>
            <Input
              value={editedCompetitor.description}
              onChange={(e) =>
                setEditedCompetitor({
                  ...editedCompetitor,
                  description: e.target.value,
                })
              }
              placeholder="Description"
            />
          </div>
          <div>
            <Input
              type="number"
              value={editedCompetitor.rank}
              onChange={(e) =>
                setEditedCompetitor({
                  ...editedCompetitor,
                  rank: parseInt(e.target.value) || 0,
                })
              }
              placeholder="Rank"
            />
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleSaveCompetitorEdit(index)}
              disabled={updateCompetitor.isPending}
            >
              {updateCompetitor.isPending ? (
                <Loader2 className="h-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCancelCompetitorEdit}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ) : (
        // View mode
        <div className="space-y-4">
          <div className="grid grid-cols-5 gap-2 items-start">
            <div>
              <h3 className="font-medium">{competitor.name}</h3>
              <span className="text-xs text-gray-400">
                ID: {competitor.id || "No ID"}
              </span>
            </div>
            <div>
              <a
                href={
                  competitor.target_url.startsWith("http")
                    ? competitor.target_url
                    : `https://${competitor.target_url}`
                }
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-muted-foreground hover:underline flex items-center gap-1"
              >
                {competitor.target_url}
                <ExternalLink className="h-3 w-3" />
              </a>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">
                {competitor.description || "No description provided"}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium">
                {competitor.rank || "Not set"}
              </p>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleStartEditingCompetitor(index)}
              >
                Edit
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="text-destructive"
                onClick={() => handleRemoveCompetitor(index)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Keywords Section */}
          <div className="mt-2 pt-2 border-t dark:border-muted-foreground">
            {editingKeywords[index] ? (
              // Edit mode for keywords
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h4 className="text-sm font-medium flex items-center">
                    <KeySquare className="h-4 w-4 mr-1" />
                    <span>Edit Keywords</span>
                  </h4>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSaveKeywords(index)}
                      disabled={updateCompKeywords.isPending}
                    >
                      {updateCompKeywords.isPending ? (
                        <Loader2 className="h-3 w-3 animate-spin" />
                      ) : (
                        <Save className="h-3 w-3" />
                      )}
                      <span className="ml-1">Save</span>
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleCancelEditingKeywords(index)}
                    >
                      <X className="h-3 w-3" />
                      <span className="ml-1">Cancel</span>
                    </Button>
                  </div>
                </div>

                <div className="flex flex-wrap gap-2 p-2 bg-muted/10 rounded-md min-h-16">
                  {(editedKeywords[index] || []).map(
                    (keyword, keywordIndex) => (
                      <Badge
                        key={keywordIndex}
                        variant="secondary"
                        className="px-2 py-1 flex items-center"
                      >
                        {keyword}
                        <button
                          onClick={() =>
                            handleRemoveCompetitorKeyword(index, keywordIndex)
                          }
                          className="ml-1 text-muted-foreground hover:text-destructive"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    )
                  )}
                  {(editedKeywords[index] || []).length === 0 && (
                    <p className="text-xs text-muted-foreground p-2">
                      No keywords added yet
                    </p>
                  )}
                </div>

                <div className="flex space-x-2">
                  <Input
                    placeholder="Add new keyword"
                    value={newKeywordText[index] || ""}
                    onChange={(e) =>
                      handleKeywordTextChange(index, e.target.value)
                    }
                    className="text-sm dark:border-muted-foreground"
                    onKeyPress={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                        handleAddNewKeyword(index);
                      }
                    }}
                  />
                  <Button
                    size="sm"
                    onClick={() => handleAddNewKeyword(index)}
                    className="bg-[#1279b4] hover:bg-[#1279b4]/80 text-white"
                  >
                    <PlusCircle className="h-3 w-3 mr-1" />
                    Add
                  </Button>
                </div>
              </div>
            ) : (
              // View mode for keywords - direct display instead of collapsible
              <div>
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-1">
                    <KeySquare className="h-4 w-4" />
                    <span className="font-medium text-sm">
                      Top Keywords ({competitor.top_keywords?.length || 0})
                    </span>
                  </div>

                  {competitor.id && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleStartEditingKeywords(index)}
                      className="h-7"
                    >
                      Edit Keywords
                    </Button>
                  )}
                </div>

                <div className="flex flex-wrap gap-2 mt-2 p-2 bg-muted/20 rounded-md">
                  {competitor.top_keywords &&
                  competitor.top_keywords.length > 0 ? (
                    competitor.top_keywords.map((keyword, keywordIndex) => (
                      <Badge
                        key={keywordIndex}
                        variant="secondary"
                        className="px-2 py-1"
                      >
                        {keyword}
                      </Badge>
                    ))
                  ) : (
                    <p className="text-xs text-muted-foreground">
                      No keywords added yet
                    </p>
                  )}

                  {/* --- CSV upload for competitor keywords --- */}
                  <div className="mt-2 flex items-center gap-2">
                    <CompetitorKeywordsCsvUpload
                      websiteId={id}
                      competitorId={competitor.id}
                      onSuccess={(added_count: number) => {
                        toast({
                          title: "Keywords uploaded!",
                          description: `Imported ${added_count} keywords from CSV for ${competitor.name}.`,
                          variant: "default",
                        });
                        refetch();
                      }}
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* After the keywords section, right before closing the competitor view */}
          {!editingCompetitor && (
            <>
              {/* Ranking Issues Section */}
              <div className="mt-2 pt-2 border-t dark:border-muted-foreground">
                <div className="flex justify-between items-center">
                  <h4 className="text-sm font-medium flex items-center">
                    <AlertTriangle className="h-4 w-4 mr-1 text-amber-500" />
                    <span>Ranking Issues</span>
                  </h4>
                  {competitor.id && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleToggleRankingIssuesEditor(index)}
                      className="h-7"
                    >
                      {editingRankingIssues[index] ? "Done" : "Edit Issues"}
                    </Button>
                  )}
                </div>

                {editingRankingIssues[index] ? (
                  <div className="space-y-4 mt-2">
                    {/* Display current ranking issues */}
                    {competitor.ranking_issues &&
                    competitor.ranking_issues.length > 0 ? (
                      <div className="space-y-2">
                        {competitor.ranking_issues.map((issue) => (
                          <div
                            key={issue.id}
                            className="bg-amber-50 p-3 rounded shadow-sm relative"
                          >
                            <Button
                              variant="ghost"
                              size="sm"
                              className="absolute top-2 right-2 text-red-500 h-6 w-6 p-0"
                              onClick={() => handleRemoveRankingIssue(issue.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                            <div className="pr-8">
                              <h5 className="font-medium mb-1 flex items-center gap-1">
                                <span>{issue.title}</span>
                                <Badge
                                  variant={
                                    issue.impact === "high"
                                      ? "destructive"
                                      : issue.impact === "low"
                                      ? "outline"
                                      : "secondary"
                                  }
                                  className="ml-2"
                                >
                                  {issue.impact.charAt(0).toUpperCase() +
                                    issue.impact.slice(1)}
                                </Badge>
                              </h5>
                              <p className="text-sm text-gray-600">
                                {issue.description}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500 italic">
                        No issues found
                      </p>
                    )}

                    {/* Form to add new ranking issue */}
                    <div className="bg-gray-50 p-3 rounded space-y-3">
                      <h5 className="font-medium">Add New Ranking Issue</h5>
                      <Input
                        placeholder="Issue title"
                        value={newRankingIssue[index]?.title || ""}
                        onChange={(e) =>
                          setNewRankingIssue((prev: any) => ({
                            ...prev,
                            [index]: {
                              ...prev[index],
                              title: e.target.value,
                            },
                          }))
                        }
                        className="mb-2"
                      />
                      <Textarea
                        placeholder="Issue description"
                        value={newRankingIssue[index]?.description || ""}
                        onChange={(e) =>
                          setNewRankingIssue((prev: any) => ({
                            ...prev,
                            [index]: {
                              ...prev[index],
                              description: e.target.value,
                            },
                          }))
                        }
                        className="mb-2"
                      />
                      <div className="flex items-center space-x-4">
                        <Label htmlFor={`issue-impact-${index}`}>Impact:</Label>
                        <Select
                          value={newRankingIssue[index]?.impact || "medium"}
                          onValueChange={(value) =>
                            setNewRankingIssue((prev: any) => ({
                              ...prev,
                              [index]: {
                                ...prev[index],
                                impact: value as "high" | "medium" | "low",
                              },
                            }))
                          }
                        >
                          <SelectTrigger
                            id={`issue-impact-${index}`}
                            className="w-32"
                          >
                            <SelectValue placeholder="Select impact" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="high">High</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="low">Low</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <Button
                        onClick={() => handleAddRankingIssue(index)}
                        className="mt-2"
                      >
                        Add Issue
                      </Button>
                    </div>
                  </div>
                ) : (
                  // Display issues in view mode
                  <div className="mt-2">
                    {competitor.ranking_issues &&
                    competitor.ranking_issues.length > 0 ? (
                      <div className="space-y-2">
                        {competitor.ranking_issues.slice(0, 2).map((issue) => (
                          <div
                            key={issue.id}
                            className="bg-amber-50 p-3 rounded shadow-sm"
                          >
                            <h5 className="font-medium mb-1 flex items-center gap-1">
                              <span>{issue.title}</span>
                              <Badge
                                variant={
                                  issue.impact === "high"
                                    ? "destructive"
                                    : issue.impact === "low"
                                    ? "outline"
                                    : "secondary"
                                }
                                className="ml-2"
                              >
                                {issue.impact.charAt(0).toUpperCase() +
                                  issue.impact.slice(1)}
                              </Badge>
                            </h5>
                            <p className="text-sm text-gray-600">
                              {issue.description}
                            </p>
                          </div>
                        ))}
                        {competitor.ranking_issues.length > 2 && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-xs"
                            onClick={() =>
                              handleToggleRankingIssuesEditor(index)
                            }
                          >
                            +{competitor.ranking_issues.length - 2} more issues
                          </Button>
                        )}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500 italic">
                        No issues found
                      </p>
                    )}
                  </div>
                )}
              </div>

              {/* Content Recommendations Section */}
              <div className="mt-4 pt-2 border-t">
                <div className="flex justify-between items-center">
                  <h4 className="text-sm font-medium flex items-center">
                    <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
                    <span>Content Recommendations</span>
                  </h4>
                  {competitor.id && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        handleToggleContentRecommendationsEditor(index)
                      }
                      className="h-7"
                    >
                      {editingContentRecommendations[index]
                        ? "Done"
                        : "Edit Recommendations"}
                    </Button>
                  )}
                </div>

                {editingContentRecommendations[index] ? (
                  <div className="space-y-4 mt-2">
                    {/* Display current content recommendations */}
                    {competitor.content_recommendations &&
                    competitor.content_recommendations.length > 0 ? (
                      <div className="space-y-2">
                        {competitor.content_recommendations.map(
                          (recommendation) => (
                            <div
                              key={recommendation.id}
                              className={`p-3 rounded shadow-sm relative ${
                                recommendation.is_opportunity
                                  ? "bg-amber-50 border border-amber-200"
                                  : "bg-white border"
                              }`}
                            >
                              <Button
                                variant="ghost"
                                size="sm"
                                className="absolute top-2 right-2 text-red-500 h-6 w-6 p-0"
                                onClick={() =>
                                  handleRemoveContentRecommendation(
                                    recommendation.id
                                  )
                                }
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                              <div className="pr-8 space-y-1">
                                <div className="flex justify-between">
                                  <h5 className="font-medium mb-1">
                                    {recommendation.title}
                                  </h5>
                                  <div className="flex items-center gap-2">
                                    <Badge
                                      variant={
                                        recommendation.impact === "high"
                                          ? "default"
                                          : recommendation.impact === "low"
                                          ? "outline"
                                          : "secondary"
                                      }
                                    >
                                      {recommendation.impact
                                        .charAt(0)
                                        .toUpperCase() +
                                        recommendation.impact.slice(1)}
                                    </Badge>
                                    {recommendation.is_opportunity && (
                                      <Badge
                                        variant="outline"
                                        className="bg-amber-50"
                                      >
                                        Opportunity
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                                <p className="text-sm text-gray-600">
                                  {recommendation.description}
                                </p>
                                <div className="text-xs text-gray-500">
                                  Est. time: {recommendation.estimated_hours}{" "}
                                  hrs
                                </div>
                              </div>
                            </div>
                          )
                        )}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500 italic">
                        No recommendations found
                      </p>
                    )}

                    {/* Form to add new content recommendation */}
                    <div className="bg-gray-50 p-3 rounded space-y-3">
                      <h5 className="font-medium">Add New Recommendation</h5>
                      <Input
                        placeholder="Recommendation title"
                        value={newContentRecommendation[index]?.title || ""}
                        onChange={(e) =>
                          setNewContentRecommendation((prev: any) => ({
                            ...prev,
                            [index]: {
                              ...prev[index],
                              title: e.target.value,
                            },
                          }))
                        }
                        className="mb-2"
                      />
                      <Textarea
                        placeholder="Recommendation description"
                        value={
                          newContentRecommendation[index]?.description || ""
                        }
                        onChange={(e) =>
                          setNewContentRecommendation((prev: any) => ({
                            ...prev,
                            [index]: {
                              ...prev[index],
                              description: e.target.value,
                            },
                          }))
                        }
                        className="mb-2"
                      />
                      <div className="grid grid-cols-2 gap-4 mb-2">
                        <div className="space-y-2">
                          <Label htmlFor={`rec-impact-${index}`}>Impact:</Label>
                          <Select
                            value={
                              newContentRecommendation[index]?.impact ||
                              "medium"
                            }
                            onValueChange={(value) =>
                              setNewContentRecommendation((prev: any) => ({
                                ...prev,
                                [index]: {
                                  ...prev[index],
                                  impact: value as "high" | "medium" | "low",
                                },
                              }))
                            }
                          >
                            <SelectTrigger id={`rec-impact-${index}`}>
                              <SelectValue placeholder="Select impact" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="high">High</SelectItem>
                              <SelectItem value="medium">Medium</SelectItem>
                              <SelectItem value="low">Low</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`rec-hours-${index}`}>
                            Est. Hours:
                          </Label>
                          <Input
                            id={`rec-hours-${index}`}
                            type="number"
                            min="1"
                            value={
                              newContentRecommendation[index]
                                ?.estimated_hours || 2
                            }
                            onChange={(e) =>
                              setNewContentRecommendation((prev: any) => ({
                                ...prev,
                                [index]: {
                                  ...prev[index],
                                  estimated_hours:
                                    parseInt(e.target.value) || 2,
                                },
                              }))
                            }
                          />
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id={`is-opportunity-${index}`}
                          checked={
                            newContentRecommendation[index]?.is_opportunity ||
                            false
                          }
                          onCheckedChange={(checked) =>
                            setNewContentRecommendation((prev: any) => ({
                              ...prev,
                              [index]: {
                                ...prev[index],
                                is_opportunity: checked === true,
                              },
                            }))
                          }
                        />
                        <Label htmlFor={`is-opportunity-${index}`}>
                          Mark as special opportunity
                        </Label>
                      </div>
                      <Button
                        onClick={() => handleAddContentRecommendation(index)}
                        className="mt-2"
                      >
                        Add Recommendation
                      </Button>
                    </div>
                  </div>
                ) : (
                  // Display recommendations in view mode
                  <div className="mt-2">
                    {competitor.content_recommendations &&
                    competitor.content_recommendations.length > 0 ? (
                      <div className="space-y-2">
                        {competitor.content_recommendations
                          .slice(0, 2)
                          .map((recommendation) => (
                            <div
                              key={recommendation.id}
                              className={`p-3 rounded shadow-sm ${
                                recommendation.is_opportunity
                                  ? "bg-amber-50 border border-amber-200"
                                  : "bg-white border"
                              }`}
                            >
                              <div className="flex justify-between items-start">
                                <h5 className="font-medium mb-1">
                                  {recommendation.title}
                                </h5>
                                <Badge
                                  variant={
                                    recommendation.impact === "high"
                                      ? "default"
                                      : recommendation.impact === "low"
                                      ? "outline"
                                      : "secondary"
                                  }
                                >
                                  {recommendation.impact
                                    .charAt(0)
                                    .toUpperCase() +
                                    recommendation.impact.slice(1)}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-600">
                                {recommendation.description}
                              </p>
                              <div className="text-xs text-gray-500 mt-1">
                                Est. time: {recommendation.estimated_hours} hrs
                              </div>
                            </div>
                          ))}
                        {competitor.content_recommendations.length > 2 && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-xs"
                            onClick={() =>
                              handleToggleContentRecommendationsEditor(index)
                            }
                          >
                            +{competitor.content_recommendations.length - 2}{" "}
                            more recommendations
                          </Button>
                        )}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500 italic">
                        No recommendations found
                      </p>
                    )}
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
}
