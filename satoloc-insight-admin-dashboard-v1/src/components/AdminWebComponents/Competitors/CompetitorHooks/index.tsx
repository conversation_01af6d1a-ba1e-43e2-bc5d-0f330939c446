"use client";

import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import {
  useAddCompetitorKeyword,
  useAddWebsiteCompetitor,
  useRemoveCompetitorKeyword,
  useRemoveWebsiteCompetitor,
  useUpdateCompetitorKeywords,
  useUpdateWebsiteCompetitor,
  useUploadCompetitorsCsv,
  useUploadCompetitorKeywordsCsv,
  useAddRankingIssue,
  useRemoveRankingIssue,
  useAddContentRecommendation,
  useRemoveContentRecommendation,
} from "@/internal-api/seo/advance-seo";

interface Competitor {
  id?: number;
  name: string;
  target_url: string;
  description: string;
  rank?: number;
  top_keywords?: string[];
  strategy_gaps?: { id: number; text: string }[];
  growth_opportunities?: { id: number; text: string }[];
  ranking_issues?: {
    id: number;
    title: string;
    description: string;
    impact: string;
  }[];
  content_recommendations?: {
    id: number;
    title: string;
    description: string;
    impact: string;
    estimated_hours: number;
    is_opportunity: boolean;
  }[];
}

export const useCompetitorManagement = (
  id: string,
  competitors: Competitor[],
  setCompetitors: React.Dispatch<React.SetStateAction<Competitor[]>>,
  refetch: () => void
) => {
  const { toast } = useToast();
  // Pagination state for competitors
  const [currentCompetitorPage, setCurrentCompetitorPage] = useState(1);
  const competitorsPerPage = 5;

  // Sorting state for competitors
  const [competitorSortBy, setCompetitorSortBy] = useState<
    "rank" | "name" | "none"
  >("none");

  // Calculate total pages for competitors
  const totalCompetitorPages = Math.ceil(
    competitors.length / competitorsPerPage
  );

  // Sort competitors based on sorting preference
  const sortedCompetitors = [...competitors].sort((a, b) => {
    if (competitorSortBy === "rank") {
      // Sort by rank (lower rank = higher priority)
      const rankA = a.rank !== undefined ? a.rank : Number.MAX_SAFE_INTEGER;
      const rankB = b.rank !== undefined ? b.rank : Number.MAX_SAFE_INTEGER;
      return rankA - rankB;
    } else if (competitorSortBy === "name") {
      // Sort by name alphabetically
      return a.name.localeCompare(b.name);
    }
    // Default: no sorting (keep original order)
    return 0;
  });

  // Get current competitors for pagination
  const indexOfLastCompetitor = currentCompetitorPage * competitorsPerPage;
  const indexOfFirstCompetitor = indexOfLastCompetitor - competitorsPerPage;
  const currentCompetitors = sortedCompetitors.slice(
    indexOfFirstCompetitor,
    indexOfLastCompetitor
  );

  // State for CSV upload
  const [csvFile, setCsvFile] = useState<File | undefined>(undefined);
  const uploadCsv = useUploadCompetitorsCsv();
  const [newCompetitor, setNewCompetitor] = useState({
    name: "",
    target_url: "",
    description: "",
    rank: 0,
  });
  const [editingCompetitor, setEditingCompetitor] = useState<number | null>(
    null
  );
  const [editedCompetitor, setEditedCompetitor] = useState({
    name: "",
    target_url: "",
    description: "",
    rank: 0,
  });

  // Add these new state variables for keyword editing
  const [editingKeywords, setEditingKeywords] = useState<boolean[]>([]);
  const [editedKeywords, setEditedKeywords] = useState<{
    [key: number]: string[];
  }>({});
  const [newKeywordText, setNewKeywordText] = useState<{
    [key: number]: string;
  }>({});

  // Add these new state variables after the existing state declarations
  const [editingRankingIssues, setEditingRankingIssues] = useState<{
    [key: number]: boolean;
  }>({});
  const [editingContentRecommendations, setEditingContentRecommendations] =
    useState<{ [key: number]: boolean }>({});
  const [newRankingIssue, setNewRankingIssue] = useState<{
    [key: number]: {
      title: string;
      description: string;
      impact: "high" | "medium" | "low";
    };
  }>({});
  const [newContentRecommendation, setNewContentRecommendation] = useState<{
    [key: number]: {
      title: string;
      description: string;
      impact: "high" | "medium" | "low";
      estimated_hours: number;
      is_opportunity: boolean;
    };
  }>({});

  // Mutations
  const addCompetitor = useAddWebsiteCompetitor({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Competitor added successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add competitor",
        variant: "destructive",
      });
    },
  });

  const removeCompetitor = useRemoveWebsiteCompetitor({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Competitor removed successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to remove competitor",
        variant: "destructive",
      });
    },
  });

  const updateCompetitor = useUpdateWebsiteCompetitor({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Competitor updated successfully",
      });
      refetch();
      setEditingCompetitor(null);
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update competitor",
        variant: "destructive",
      });
    },
  });

  // Add these mutation hooks
  const addCompKeyword = useAddCompetitorKeyword({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Keyword added successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add keyword",
        variant: "destructive",
      });
    },
  });

  const removeCompKeyword = useRemoveCompetitorKeyword({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Keyword removed successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to remove keyword",
        variant: "destructive",
      });
    },
  });

  const updateCompKeywords = useUpdateCompetitorKeywords({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Keywords updated successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update keywords",
        variant: "destructive",
      });
    },
  });

  // Add the new mutation hooks
  const addRankingIssue = useAddRankingIssue({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Ranking issue added successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add ranking issue",
        variant: "destructive",
      });
    },
  });

  const removeRankingIssue = useRemoveRankingIssue({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Ranking issue removed successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to remove ranking issue",
        variant: "destructive",
      });
    },
  });

  const addContentRecommendation = useAddContentRecommendation({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Content recommendation added successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add content recommendation",
        variant: "destructive",
      });
    },
  });

  const removeContentRecommendation = useRemoveContentRecommendation({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Content recommendation removed successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to remove content recommendation",
        variant: "destructive",
      });
    },
  });

  // Initialize editing states when competitors data changes
  useEffect(() => {
    if (competitors && competitors.length > 0) {
      setEditingKeywords(new Array(competitors.length).fill(false));

      // Initialize edited keywords with current values
      const keywordsMap: { [key: number]: string[] } = {};
      competitors.forEach((competitor, index) => {
        if (competitor.top_keywords) {
          keywordsMap[index] = [...competitor.top_keywords];
        } else {
          keywordsMap[index] = [];
        }
      });
      setEditedKeywords(keywordsMap);

      // Initialize new keyword input only
      const newKeywordMap: { [key: number]: string } = {};
      competitors.forEach((_, index) => {
        newKeywordMap[index] = "";
      });

      setNewKeywordText(newKeywordMap);
    }
  }, [competitors]);

  // Initialize editing states for ranking issues and content recommendations when competitors data changes
  useEffect(() => {
    if (competitors && competitors.length > 0) {
      // Initialize ranking issues editing state
      const rankingIssuesMap: { [key: number]: boolean } = {};
      competitors.forEach((_, index) => {
        rankingIssuesMap[index] = false;
      });
      setEditingRankingIssues(rankingIssuesMap);

      // Initialize content recommendations editing state
      const contentRecommendationsMap: { [key: number]: boolean } = {};
      competitors.forEach((_, index) => {
        contentRecommendationsMap[index] = false;
      });
      setEditingContentRecommendations(contentRecommendationsMap);

      // Initialize new ranking issue forms
      const newRankingIssueMap: {
        [key: number]: {
          title: string;
          description: string;
          impact: "high" | "medium" | "low";
        };
      } = {};
      competitors.forEach((_, index) => {
        newRankingIssueMap[index] = {
          title: "",
          description: "",
          impact: "medium",
        };
      });
      setNewRankingIssue(newRankingIssueMap);

      // Initialize new content recommendation forms
      const newContentRecommendationMap: {
        [key: number]: {
          title: string;
          description: string;
          impact: "high" | "medium" | "low";
          estimated_hours: number;
          is_opportunity: boolean;
        };
      } = {};
      competitors.forEach((_, index) => {
        newContentRecommendationMap[index] = {
          title: "",
          description: "",
          impact: "medium",
          estimated_hours: 2,
          is_opportunity: false,
        };
      });
      setNewContentRecommendation(newContentRecommendationMap);
    }
  }, [competitors]);

  // Functions to handle editing
  const handleAddCompetitor = () => {
    if (!newCompetitor.name || !newCompetitor.target_url) {
      toast({
        title: "Warning",
        description: "Name and URL are required for competitors",
        variant: "destructive",
      });
      return;
    }

    addCompetitor.mutate({
      websiteId: id,
      name: newCompetitor.name,
      targetUrl: newCompetitor.target_url,
      description: newCompetitor.description,
      rank: newCompetitor.rank,
    });

    setNewCompetitor({
      name: "",
      target_url: "",
      description: "",
      rank: 0,
    });
  };

  const handleRemoveCompetitor = (index: number) => {
    const competitor = competitors[index];

    if (competitor.id) {
      removeCompetitor.mutate({
        websiteId: id,
        competitorId: competitor.id,
      });
    } else {
      // If competitor doesn't have an ID, it might be a new one not saved to the database yet
      setCompetitors(competitors.filter((_, i) => i !== index));

      toast({
        title: "Success",
        description: "Competitor removed",
      });
    }
  };

  const handleStartEditingCompetitor = (index: number) => {
    const competitor = competitors[index];
    setEditingCompetitor(index);
    setEditedCompetitor({
      name: competitor.name,
      target_url: competitor.target_url,
      description: competitor.description || "",
      rank: competitor.rank || 0,
    });
  };

  const handleSaveCompetitorEdit = (index: number) => {
    const competitor = competitors[index];

    // Use ID if available, otherwise use the original target_url to identify the competitor
    if (competitor.id) {
      updateCompetitor.mutate({
        websiteId: id,
        competitorId: competitor.id,
        name: editedCompetitor.name,
        targetUrl: editedCompetitor.target_url,
        description: editedCompetitor.description,
        rank: editedCompetitor.rank,
      });
    } else {
      updateCompetitor.mutate({
        websiteId: id,
        originalTargetUrl: competitor.target_url,
        name: editedCompetitor.name,
        targetUrl: editedCompetitor.target_url,
        description: editedCompetitor.description,
        rank: editedCompetitor.rank,
      });
    }
  };

  const handleCancelCompetitorEdit = () => {
    setEditingCompetitor(null);
  };

  // Add these new handler functions
  const handleStartEditingKeywords = (index: number) => {
    const newEditingKeywords = [...editingKeywords];
    newEditingKeywords[index] = true;
    setEditingKeywords(newEditingKeywords);

    // Make sure we have the current keywords in our state
    const keywordsMap = { ...editedKeywords };
    if (!keywordsMap[index] && competitors[index].top_keywords) {
      keywordsMap[index] = [...competitors[index].top_keywords];
    } else if (!keywordsMap[index]) {
      keywordsMap[index] = [];
    }
    setEditedKeywords(keywordsMap);
  };

  const handleCancelEditingKeywords = (index: number) => {
    const newEditingKeywords = [...editingKeywords];
    newEditingKeywords[index] = false;
    setEditingKeywords(newEditingKeywords);

    // Reset to original values
    const keywordsMap = { ...editedKeywords };
    if (competitors[index].top_keywords) {
      keywordsMap[index] = [...competitors[index].top_keywords];
    } else {
      keywordsMap[index] = [];
    }
    setEditedKeywords(keywordsMap);

    // Clear the new keyword input
    const newKeywordMap = { ...newKeywordText };
    newKeywordMap[index] = "";
    setNewKeywordText(newKeywordMap);
  };

  const handleSaveKeywords = (index: number) => {
    const competitor = competitors[index];

    if (!competitor.id) {
      toast({
        title: "Error",
        description: "Cannot update keywords for a competitor without an ID",
        variant: "destructive",
      });
      return;
    }

    updateCompKeywords.mutate({
      websiteId: id,
      competitorId: competitor.id,
      keywords: editedKeywords[index] || [],
    });

    // Exit edit mode
    const newEditingKeywords = [...editingKeywords];
    newEditingKeywords[index] = false;
    setEditingKeywords(newEditingKeywords);
  };

  const handleRemoveCompetitorKeyword = (
    competitorIndex: number,
    keywordIndex: number
  ) => {
    const keywordsMap = { ...editedKeywords };
    const updatedKeywords = [...(keywordsMap[competitorIndex] || [])];
    updatedKeywords.splice(keywordIndex, 1);
    keywordsMap[competitorIndex] = updatedKeywords;
    setEditedKeywords(keywordsMap);
  };

  const handleAddNewKeyword = (index: number) => {
    if (!newKeywordText[index] || newKeywordText[index].trim() === "") {
      return;
    }

    const keywordsMap = { ...editedKeywords };
    if (!keywordsMap[index]) {
      keywordsMap[index] = [];
    }

    // Check if keyword already exists
    if (keywordsMap[index].includes(newKeywordText[index].trim())) {
      toast({
        title: "Warning",
        description: "This keyword already exists",
        variant: "destructive",
      });
      return;
    }

    keywordsMap[index] = [...keywordsMap[index], newKeywordText[index].trim()];
    setEditedKeywords(keywordsMap);

    // Clear the input
    const newKeywordMap = { ...newKeywordText };
    newKeywordMap[index] = "";
    setNewKeywordText(newKeywordMap);
  };

  const handleKeywordTextChange = (index: number, value: string) => {
    const newKeywordMap = { ...newKeywordText };
    newKeywordMap[index] = value;
    setNewKeywordText(newKeywordMap);
  };

  // Ranking Issues handlers
  const handleToggleRankingIssuesEditor = (index: number) => {
    setEditingRankingIssues((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  const handleAddRankingIssue = (index: number) => {
    const competitor = competitors[index];

    if (!competitor.id) {
      toast({
        title: "Error",
        description: "Cannot add ranking issues for a competitor without an ID",
        variant: "destructive",
      });
      return;
    }

    const { title, description, impact } = newRankingIssue[index];

    if (!title || !description) {
      toast({
        title: "Warning",
        description: "Title and description are required for ranking issues",
        variant: "destructive",
      });
      return;
    }

    addRankingIssue.mutate({
      websiteId: id,
      competitorId: competitor.id,
      title,
      description,
      impact,
    });

    // Reset the form
    setNewRankingIssue((prev) => ({
      ...prev,
      [index]: { title: "", description: "", impact: "medium" },
    }));
  };

  const handleRemoveRankingIssue = (issueId: number) => {
    removeRankingIssue.mutate({
      websiteId: id,
      rankingIssueId: issueId,
    });
  };

  // Content Recommendations handlers
  const handleToggleContentRecommendationsEditor = (index: number) => {
    setEditingContentRecommendations((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  const handleAddContentRecommendation = (index: number) => {
    const competitor = competitors[index];

    if (!competitor.id) {
      toast({
        title: "Error",
        description:
          "Cannot add content recommendations for a competitor without an ID",
        variant: "destructive",
      });
      return;
    }

    const { title, description, impact, estimated_hours, is_opportunity } =
      newContentRecommendation[index];

    if (!title || !description) {
      toast({
        title: "Warning",
        description:
          "Title and description are required for content recommendations",
        variant: "destructive",
      });
      return;
    }

    addContentRecommendation.mutate({
      websiteId: id,
      competitorId: competitor.id,
      title,
      description,
      impact,
      estimatedHours: estimated_hours,
      isOpportunity: is_opportunity,
    });

    // Reset the form
    setNewContentRecommendation((prev) => ({
      ...prev,
      [index]: {
        title: "",
        description: "",
        impact: "medium",
        estimated_hours: 2,
        is_opportunity: false,
      },
    }));
  };

  const handleRemoveContentRecommendation = (recommendationId: number) => {
    removeContentRecommendation.mutate({
      websiteId: id,
      recommendationId: recommendationId,
    });
  };

  return {
    // state
    currentCompetitorPage,
    setCurrentCompetitorPage,
    competitorSortBy,
    setCompetitorSortBy,
    totalCompetitorPages,
    currentCompetitors,
    csvFile,
    setCsvFile,
    newCompetitor,
    setNewCompetitor,
    editingCompetitor,
    setEditingCompetitor,
    editedCompetitor,
    setEditedCompetitor,
    editingKeywords,
    editedKeywords,
    newKeywordText,
    setNewKeywordText,
    editingRankingIssues,
    editingContentRecommendations,
    newRankingIssue,
    setNewRankingIssue,
    newContentRecommendation,
    setNewContentRecommendation,

    // mutations
    addCompetitor,
    uploadCsv,
    updateCompetitor,
    updateCompKeywords,

    // handlers
    handleAddCompetitor,
    handleRemoveCompetitor,
    handleStartEditingCompetitor,
    handleSaveCompetitorEdit,
    handleCancelCompetitorEdit,
    handleStartEditingKeywords,
    handleCancelEditingKeywords,
    handleSaveKeywords,
    handleRemoveCompetitorKeyword,
    handleAddNewKeyword,
    handleKeywordTextChange,
    handleToggleRankingIssuesEditor,
    handleAddRankingIssue,
    handleRemoveRankingIssue,
    handleToggleContentRecommendationsEditor,
    handleAddContentRecommendation,
    handleRemoveContentRecommendation,
  };
};
