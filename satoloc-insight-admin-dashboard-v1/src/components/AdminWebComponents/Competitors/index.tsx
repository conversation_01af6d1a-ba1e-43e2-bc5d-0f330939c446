"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  CheckCircle,
  ChevronLeft,
  ChevronRight,
  ExternalLink,
  KeySquare,
  Loader2,
  PlusCircle,
  Save,
  Trash2,
  AlertTriangle,
  X,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { useCompetitorManagement } from "./CompetitorHooks";
import { useUploadCompetitorKeywordsCsv } from "@/internal-api/seo/advance-seo";
import CompetitorListItem from "./CompetitorListItem";

interface Competitor {
  id?: number;
  name: string;
  target_url: string;
  description: string;
  rank?: number;
  top_keywords?: string[];
  strategy_gaps?: { id: number; text: string }[];
  growth_opportunities?: { id: number; text: string }[];
  ranking_issues?: {
    id: number;
    title: string;
    description: string;
    impact: string;
  }[];
  content_recommendations?: {
    id: number;
    title: string;
    description: string;
    impact: string;
    estimated_hours: number;
    is_opportunity: boolean;
  }[];
}

// --- CSV upload component for competitor keywords ---
type CompetitorKeywordsCsvUploadProps = {
  websiteId: string;
  competitorId?: number;
  onSuccess: (added_count: number) => void;
};

const CompetitorKeywordsCsvUpload: React.FC<
  CompetitorKeywordsCsvUploadProps
> = ({ websiteId, competitorId, onSuccess }) => {
  const { toast } = useToast();
  const [file, setFile] = useState<File | undefined>(undefined);
  const uploadKeywordsCsv = useUploadCompetitorKeywordsCsv();

  if (!competitorId) return null;

  return (
    <form
      className="flex items-center gap-2"
      onSubmit={async (e) => {
        e.preventDefault();
        if (!file) return;
        uploadKeywordsCsv.mutate(
          { websiteId, competitorId, file },
          {
            onSuccess: (data: { added_count: number }) => {
              onSuccess(data.added_count);
              setFile(undefined);
            },
            onError: (err: any) => {
              toast({
                title: "Upload failed",
                description: err?.response?.data?.error || err.message,
                variant: "destructive",
              });
            },
          }
        );
      }}
    >
      <Input
        type="file"
        accept=".csv,text/csv"
        onChange={(e) => {
          if (e.target.files && e.target.files[0]) {
            setFile(e.target.files[0]);
          }
        }}
        className="w-auto"
      />
      <Button
        type="submit"
        size="sm"
        disabled={!file || uploadKeywordsCsv.isPending}
      >
        {uploadKeywordsCsv.isPending ? (
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
        ) : null}
        Upload Keywords CSV
      </Button>
      {file && (
        <span className="text-xs text-muted-foreground">{file.name}</span>
      )}
    </form>
  );
};

interface CompetitorsProps {
  id: string;
  competitors: Competitor[];
  setCompetitors: React.Dispatch<React.SetStateAction<Competitor[]>>;
  refetch: () => void;
}

export default function Competitors({
  id,
  competitors,
  setCompetitors,
  refetch,
}: CompetitorsProps) {
  const { toast } = useToast();
  const competitorsPerPage = 5;
  const {
    // state
    currentCompetitorPage,
    setCurrentCompetitorPage,
    competitorSortBy,
    setCompetitorSortBy,
    totalCompetitorPages,
    currentCompetitors,
    csvFile,
    setCsvFile,
    newCompetitor,
    setNewCompetitor,
    editingCompetitor,
    setEditingCompetitor,
    editedCompetitor,
    setEditedCompetitor,
    editingKeywords,
    editedKeywords,
    newKeywordText,
    setNewKeywordText,
    editingRankingIssues,
    editingContentRecommendations,
    newRankingIssue,
    setNewRankingIssue,
    newContentRecommendation,
    setNewContentRecommendation,

    // mutations
    addCompetitor,
    uploadCsv,
    updateCompetitor,
    updateCompKeywords,

    // handlers
    handleAddCompetitor,
    handleRemoveCompetitor,
    handleStartEditingCompetitor,
    handleSaveCompetitorEdit,
    handleCancelCompetitorEdit,
    handleStartEditingKeywords,
    handleCancelEditingKeywords,
    handleSaveKeywords,
    handleRemoveCompetitorKeyword,
    handleAddNewKeyword,
    handleKeywordTextChange,
    handleToggleRankingIssuesEditor,
    handleAddRankingIssue,
    handleRemoveRankingIssue,
    handleToggleContentRecommendationsEditor,
    handleAddContentRecommendation,
    handleRemoveContentRecommendation,
  } = useCompetitorManagement(id, competitors, setCompetitors, refetch);

  return (
    <Card className="shadow-none">
      <CardHeader className="text-foreground">
        <CardTitle>Website Competitors</CardTitle>
        <CardDescription className="text-muted-foreground text-sm">
          Manage competitors for the website
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex justify-between items-center mb-4">
          <div className="text-sm text-muted-foreground">
            {competitors.length} competitor
            {competitors.length !== 1 ? "s" : ""}
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-foreground">Sort by:</span>
            <Select
              value={competitorSortBy}
              onValueChange={(value) =>
                setCompetitorSortBy(value as "rank" | "name" | "none")
              }
            >
              <SelectTrigger className="w-[140px] h-8 dark:border-muted-foreground">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent className="dark:border-muted-foreground">
                <SelectItem value="none">No sorting</SelectItem>
                <SelectItem value="rank">Rank (low to high)</SelectItem>
                <SelectItem value="name">Name (A-Z)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="space-y-4">
          <div className="grid grid-cols-5 gap-2 px-4 py-2 font-medium bg-muted text-foreground rounded-lg dark:border border-foreground dark:text-white">
            <div>Name</div>
            <div>Target URL</div>
            <div>Description</div>
            <div>Rank</div>
            <div>Delete?</div>
          </div>

          {currentCompetitors.length > 0 ? (
            currentCompetitors.map((competitor, index) => (
              <CompetitorListItem
                key={index}
                competitor={competitor}
                index={index}
                id={id}
                refetch={refetch}
                editingCompetitor={editingCompetitor}
                editedCompetitor={editedCompetitor}
                setEditedCompetitor={setEditedCompetitor}
                updateCompetitor={updateCompetitor}
                handleSaveCompetitorEdit={handleSaveCompetitorEdit}
                handleCancelCompetitorEdit={handleCancelCompetitorEdit}
                handleStartEditingCompetitor={handleStartEditingCompetitor}
                handleRemoveCompetitor={handleRemoveCompetitor}
                editingKeywords={editingKeywords}
                handleStartEditingKeywords={handleStartEditingKeywords}
                handleCancelEditingKeywords={handleCancelEditingKeywords}
                handleSaveKeywords={handleSaveKeywords}
                updateCompKeywords={updateCompKeywords}
                editedKeywords={editedKeywords}
                handleRemoveCompetitorKeyword={handleRemoveCompetitorKeyword}
                newKeywordText={newKeywordText}
                handleKeywordTextChange={handleKeywordTextChange}
                handleAddNewKeyword={handleAddNewKeyword}
                editingRankingIssues={editingRankingIssues}
                handleToggleRankingIssuesEditor={
                  handleToggleRankingIssuesEditor
                }
                handleRemoveRankingIssue={handleRemoveRankingIssue}
                newRankingIssue={newRankingIssue}
                setNewRankingIssue={setNewRankingIssue}
                handleAddRankingIssue={handleAddRankingIssue}
                editingContentRecommendations={editingContentRecommendations}
                handleToggleContentRecommendationsEditor={
                  handleToggleContentRecommendationsEditor
                }
                handleRemoveContentRecommendation={
                  handleRemoveContentRecommendation
                }
                newContentRecommendation={newContentRecommendation}
                setNewContentRecommendation={setNewContentRecommendation}
                handleAddContentRecommendation={handleAddContentRecommendation}
              />
            ))
          ) : (
            <p className="text-muted-foreground">No competitors added yet</p>
          )}
        </div>
        {/* Pagination for competitors list */}
        {competitors.length > competitorsPerPage && (
          <div className="flex items-center justify-center space-x-2 py-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setCurrentCompetitorPage((prev) => Math.max(prev - 1, 1))
              }
              disabled={currentCompetitorPage === 1}
              className="text-[#1279b4] border-[#1279b4]/20 hover:bg-[#1279b4]/5"
            >
              <ChevronLeft className="h-4 w-4" />
              <span className="sr-only">Previous Page</span>
            </Button>

            <div className="flex items-center gap-1">
              {Array.from(
                { length: totalCompetitorPages },
                (_, i) => i + 1
              ).map((page) => (
                <Button
                  key={page}
                  variant={
                    currentCompetitorPage === page ? "default" : "outline"
                  }
                  size="sm"
                  onClick={() => setCurrentCompetitorPage(page)}
                  className={
                    currentCompetitorPage === page
                      ? "bg-[#1279b4] hover:bg-[#1279b4]/90 text-white"
                      : "text-[#1279b4] border-[#1279b4]/20 hover:bg-[#1279b4]/5"
                  }
                >
                  {page}
                </Button>
              ))}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setCurrentCompetitorPage((prev) =>
                  Math.min(prev + 1, totalCompetitorPages)
                )
              }
              disabled={currentCompetitorPage === totalCompetitorPages}
              className="text-[#1279b4] border-[#1279b4]/20 hover:bg-[#1279b4]/5"
            >
              <ChevronRight className="h-4 w-4" />
              <span className="sr-only">Next Page</span>
            </Button>
          </div>
        )}

        {/* CSV Upload for Competitors */}
        <div className="space-y-4 pt-4 border-t dark:border-muted-foreground">
          <h3 className="font-medium text-foreground">
            Upload Competitors CSV
          </h3>
          <form
            className="flex flex-col md:flex-row items-start md:items-end gap-4"
            onSubmit={async (e) => {
              e.preventDefault();
              if (!csvFile) return;
              uploadCsv.mutate(
                { websiteId: id, file: csvFile },
                {
                  onSuccess: (data: { added_count: number }) => {
                    toast({
                      title: "Competitors uploaded!",
                      description: `Imported ${data.added_count} competitors from CSV.`,
                      variant: "default",
                    });
                    setCsvFile(undefined);
                    refetch();
                  },
                  onError: (err: any) => {
                    toast({
                      title: "Upload failed",
                      description: err?.response?.data?.error || err.message,
                      variant: "destructive",
                    });
                  },
                }
              );
            }}
          >
            <Input
              type="file"
              accept=".csv,text/csv"
              onChange={(e) => {
                if (e.target.files && e.target.files[0]) {
                  setCsvFile(e.target.files[0]);
                }
              }}
              className="w-full md:w-auto dark:border-muted-foreground"
            />
            <Button
              type="submit"
              disabled={!csvFile || uploadCsv.isPending}
              className="w-full md:w-auto bg-[#1279b4] hover:bg-[#1279b4]/80 text-white"
            >
              {uploadCsv.isPending ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : null}
              Upload CSV
            </Button>
            {csvFile && (
              <span className="text-xs text-muted-foreground">
                {csvFile.name}
              </span>
            )}
          </form>

          <h3 className="font-medium">Add New Competitor</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="competitor-name" className="text-foreground">
                Name
              </Label>
              <Input
                className="dark:border-muted-foreground"
                id="competitor-name"
                placeholder="Competitor name"
                value={newCompetitor.name}
                onChange={(e) =>
                  setNewCompetitor({
                    ...newCompetitor,
                    name: e.target.value,
                  })
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="competitor-url" className="text-foreground">
                URL
              </Label>
              <Input
                className="dark:border-muted-foreground"
                id="competitor-url"
                placeholder="competitor.com"
                value={newCompetitor.target_url}
                onChange={(e) =>
                  setNewCompetitor({
                    ...newCompetitor,
                    target_url: e.target.value,
                  })
                }
              />
            </div>
            <div className="space-y-2">
              <Label
                htmlFor="competitor-description"
                className="text-foreground"
              >
                Description
              </Label>
              <Input
                className="dark:border-muted-foreground"
                id="competitor-description"
                placeholder="Description of the competitor"
                value={newCompetitor.description}
                onChange={(e) =>
                  setNewCompetitor({
                    ...newCompetitor,
                    description: e.target.value,
                  })
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="competitor-rank" className="text-foreground">
                Rank
              </Label>
              <Input
                className="dark:border-muted-foreground"
                id="competitor-rank"
                type="number"
                placeholder="Competitor rank"
                value={newCompetitor.rank || ""}
                onChange={(e) =>
                  setNewCompetitor({
                    ...newCompetitor,
                    rank: parseInt(e.target.value) || 0,
                  })
                }
              />
            </div>
          </div>
          <Button
            onClick={handleAddCompetitor}
            disabled={!!addCompetitor.isPending}
            className="bg-[#1279b4] hover:bg-[#1279b4]/80 text-white"
          >
            {addCompetitor.isPending ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <PlusCircle className="h-4 w-4 mr-2" />
            )}
            Add Competitor
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
