import { useMutation, useQuery } from "@tanstack/react-query";
import api from "../../lib/api";

/**
 * Connect to Google Search Console
 */
export const connectToGSC = async ({
  authCode,
  redirectUri,
}: {
  authCode: string;
  redirectUri?: string;
}) => {
  try {
    const response = await api.post("/advance-seo/gsc-connect/", {
      auth_code: authCode,
      redirect_uri: redirectUri || `${window.location.origin}/gsc-callback`,
    });
    return response.data;
  } catch (error) {
    console.error("Error connecting to Google Search Console:", error);
    throw error;
  }
};

/**
 * Disconnect from Google Search Console
 */
export const disconnectFromGSC = async () => {
  try {
    const response = await api.post("/advance-seo/gsc-disconnect/");
    return response.data;
  } catch (error) {
    console.error("Error disconnecting from Google Search Console:", error);
    throw error;
  }
};

/**
 * Check Google Search Console connection status
 */
export const checkGSCConnectionStatus = async () => {
  try {
    const response = await api.get("/advance-seo/gsc-status/");
    return response.data;
  } catch (error) {
    console.error(
      "Error checking Google Search Console connection status:",
      error
    );
    throw error;
  }
};

/**
 * Get GSC Sites
 */
export const getGSCSites = async () => {
  try {
    const response = await api.get("/advance-seo/gsc-sites/");
    return response.data;
  } catch (error) {
    console.error("Error getting GSC sites:", error);
    throw error;
  }
};

/**
 * Check data availability for a site
 */
export const checkGSCDataAvailability = async (params: {
  site_url: string;
  type?: string;
  days_to_check?: number;
}) => {
  try {
    const response = await api.post(
      "/advance-seo/gsc-data-availability/",
      params
    );
    return response.data;
  } catch (error) {
    console.error("Error checking GSC data availability:", error);
    throw error;
  }
};

/**
 * Get GSC Performance Data
 */
export const getGSCPerformanceData = async (params: {
  site_url: string;
  days_back?: number;
  type?: string;
  aggregation_type?: string;
  include_detailed_data?: boolean;
  get_search_appearance?: boolean;
}) => {
  try {
    const response = await api.post(
      "/advance-seo/gsc-performance-data/",
      params
    );
    return response.data;
  } catch (error) {
    console.error("Error getting GSC performance data:", error);
    throw error;
  }
};

/**
 * Get GSC Advanced Search Analytics Data
 */
export const getGSCAdvancedSearchAnalytics = async (params: {
  site_url: string;
  start_date: string;
  end_date: string;
  dimensions?: string[];
  type?: string;
  row_limit?: number;
  start_row?: number;
  aggregation_type?: string;
  data_state?: string;
  dimension_filter_groups?: any[];
}) => {
  try {
    const response = await api.post(
      "/advance-seo/gsc-advanced-search-analytics/",
      params
    );
    return response.data;
  } catch (error) {
    console.error("Error getting GSC advanced search analytics:", error);
    throw error;
  }
};

/**
 * React Query hook to connect to Google Search Console
 */
export const useConnectToGSC = (options = {}) => {
  return useMutation({
    mutationFn: connectToGSC,
    ...options,
  });
};

/**
 * React Query hook to disconnect from Google Search Console
 */
export const useDisconnectFromGSC = (options = {}) => {
  return useMutation({
    mutationFn: disconnectFromGSC,
    ...options,
  });
};

/**
 * React Query hook to check Google Search Console connection status
 */
export const useGSCConnectionStatus = (options = {}) => {
  return useQuery({
    queryKey: ["gscConnectionStatus"],
    queryFn: checkGSCConnectionStatus,
    staleTime: 30000, // Consider data stale after 30 seconds
    gcTime: 300000, // Keep cached data for 5 minutes
    retry: (failureCount, error: any) => {
      // Always retry connection status checks as they're lightweight
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options,
  });
};

/**
 * React Query hook to get GSC sites
 */
export const useGSCSites = (enabled: boolean = true, options = {}) => {
  return useQuery({
    queryKey: ["gscSites"],
    queryFn: getGSCSites,
    enabled,
    staleTime: 30000, // Consider data stale after 30 seconds
    gcTime: 300000, // Keep cached data for 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on authorization errors
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        return false;
      }
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options,
  });
};

/**
 * React Query hook to check data availability
 */
export const useGSCDataAvailability = (options = {}) => {
  return useMutation({
    mutationFn: checkGSCDataAvailability,
    ...options,
  });
};

/**
 * React Query hook to get performance data
 */
export const useGSCPerformanceData = (options = {}) => {
  return useMutation({
    mutationFn: getGSCPerformanceData,
    ...options,
  });
};

/**
 * React Query hook to get advanced search analytics data
 */
export const useGSCAdvancedSearchAnalytics = (options = {}) => {
  return useMutation({
    mutationFn: getGSCAdvancedSearchAnalytics,
    ...options,
  });
};
