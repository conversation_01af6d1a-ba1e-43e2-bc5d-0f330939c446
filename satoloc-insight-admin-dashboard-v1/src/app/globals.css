@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  /* Pure black background with white text for maximum contrast */
  --background: oklch(0.05 0 0); /* Almost pure black */
  --foreground: oklch(0.98 0 0); /* Almost pure white */

  /* Cards with very dark background */
  --card: oklch(0.08 0 0); /* Very dark grey */
  --card-foreground: oklch(0.98 0 0); /* Pure white text */

  /* Popovers */
  --popover: oklch(0.08 0 0);
  --popover-foreground: oklch(0.98 0 0);

  /* Primary colors - white on black theme */
  --primary: oklch(0.95 0 0); /* Almost white */
  --primary-foreground: oklch(0.05 0 0); /* Almost black */

  /* Secondary with subtle grey */
  --secondary: oklch(0.15 0 0); /* Dark grey */
  --secondary-foreground: oklch(0.9 0 0); /* Light grey */

  /* Muted elements */
  --muted: oklch(0.12 0 0); /* Darker grey */
  --muted-foreground: oklch(0.7 0 0); /* Medium grey */

  /* Accent colors */
  --accent: oklch(0.15 0 0); /* Dark grey */
  --accent-foreground: oklch(0.95 0 0); /* Almost white */

  /* Error state */
  --destructive: oklch(0.6 0.3 15); /* Red for errors */

  /* Borders and inputs with subtle contrast */
  --border: oklch(0.2 0 0); /* Subtle border */
  --input: oklch(0.12 0 0); /* Dark input background */
  --ring: oklch(0.6 0 0); /* Focus ring */

  /* Chart colors optimized for dark theme */
  --chart-1: oklch(0.7 0.3 250); /* Blue */
  --chart-2: oklch(0.8 0.25 160); /* Green */
  --chart-3: oklch(0.75 0.3 50); /* Orange */
  --chart-4: oklch(0.65 0.35 300); /* Purple */
  --chart-5: oklch(0.85 0.2 30); /* Yellow */

  /* Sidebar with pure black theme */
  --sidebar: oklch(0.03 0 0); /* Almost pure black */
  --sidebar-foreground: oklch(0.95 0 0); /* Almost white */
  --sidebar-primary: oklch(0.9 0 0); /* White for active items */
  --sidebar-primary-foreground: oklch(0.05 0 0); /* Black text on white */
  --sidebar-accent: oklch(0.1 0 0); /* Very dark hover state */
  --sidebar-accent-foreground: oklch(0.9 0 0); /* Light text */
  --sidebar-border: oklch(0.15 0 0); /* Subtle border */
  --sidebar-ring: oklch(0.6 0 0); /* Focus ring */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground transition-colors duration-300;
  }

  /* Enhanced scrollbar for dark mode */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-background;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/20 rounded-full hover:bg-muted-foreground/30;
  }

  /* Improved selection styling for dark mode */
  ::selection {
    @apply bg-primary/20 text-foreground;
  }

  /* Focus visible styling */
  :focus-visible {
    @apply outline-2 outline-ring outline-offset-2;
  }
}

/* Custom animations for theme transitions */
@layer utilities {
  .theme-transition {
    transition: background-color 0.3s ease, color 0.3s ease,
      border-color 0.3s ease;
  }

  .glass-effect {
    backdrop-filter: blur(12px);
    background: oklch(from var(--background) l c h / 0.8);
  }

  /* Dark mode specific utilities */
  .dark {
    .shadow-lg {
      box-shadow: 0 10px 15px -3px oklch(0 0 0 / 0.3),
        0 4px 6px -2px oklch(0 0 0 / 0.1);
    }

    .shadow-xl {
      box-shadow: 0 20px 25px -5px oklch(0 0 0 / 0.3),
        0 10px 10px -5px oklch(0 0 0 / 0.1);
    }
  }
}
