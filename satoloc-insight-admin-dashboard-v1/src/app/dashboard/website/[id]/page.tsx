"use client";

import React, { useEffect, useState } from "react";
import { useAuthCheck } from "@/hooks/useAuth";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";

import {
  ArrowLeft,
  Globe,
  Loader2,
  Users2,
  BarChart3,
  InfoIcon,
  TargetIcon,
  UsersIcon,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import Link from "next/link";
import {
  useAddWebsiteKeyword,
  useAddWebsiteLanguage,
  useRemoveWebsiteKeyword,
  useRemoveWebsiteLanguage,
  useUpdateWebsiteKeyword,
  useUploadCompetitorsCsv,
  useWebsiteById,
} from "@/internal-api/seo/advance-seo";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";

import { SidebarTrigger } from "@/components/ui/sidebar";
import { ThemeToggle } from "@/components/theme-toggle";
import BasicInformation from "@/components/AdminWebComponents/BasicIformation";
import StrategyAndGrowth from "@/components/AdminWebComponents/StrategyAndGrowth";
import SeoMetrics from "@/components/AdminWebComponents/SeoMetrics";
import Competitors from "@/components/AdminWebComponents/Competitors";

export default function WebsiteDetailsPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const [id, setId] = React.useState<string>("");

  // Handle async params in Next.js 15
  React.useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params;
      setId(resolvedParams.id);
    };

    getParams();
  }, [params]);

  const {
    user: currentUser,
    isAuthenticated,
    isLoading: authLoading,
  } = useAuthCheck();
  const router = useRouter();
  const { toast } = useToast();

  // Fetch website data
  const {
    data: website,
    isLoading,
    isError,
    error,
    refetch,
  } = useWebsiteById(id, { enabled: !!id });

  // State for edited values
  const [websiteData, setWebsiteData] = useState({
    url: "",
    industry: "",
    language: "",
  });

  // State for languages
  const [languages, setLanguages] = useState<string[]>([]);
  const [newLanguage, setNewLanguage] = useState("");

  // State for keywords
  const [keywords, setKeywords] = useState<
    {
      keyword: string;
      is_target: boolean;
    }[]
  >([]);
  const [newKeyword, setNewKeyword] = useState("");
  const [newKeywordIsTarget, setNewKeywordIsTarget] = useState(false);

  // State for competitors
  const [competitors, setCompetitors] = useState<
    {
      id?: number;
      name: string;
      target_url: string;
      description: string;
      rank?: number;
      top_keywords?: string[];
      strategy_gaps?: { id: number; text: string }[];
      growth_opportunities?: { id: number; text: string }[];
      ranking_issues?: {
        id: number;
        title: string;
        description: string;
        impact: string;
      }[];
      content_recommendations?: {
        id: number;
        title: string;
        description: string;
        impact: string;
        estimated_hours: number;
        is_opportunity: boolean;
      }[];
    }[]
  >([]);

  // State for SEO metrics
  const [seoMetrics, setSeoMetrics] = useState<{
    domain_rating?: {
      description?: string;
      score?: number;
      previous_score?: number | null;
    };
    organic_traffic?: {
      description?: string;
      score?: number;
      previous_score?: number | null;
    };
    search_rankings?: {
      description?: string;
      score?: number;
      previous_score?: number | null;
    };
    search_terms?: {
      description?: string;
      score?: number;
      previous_score?: number | null;
    };
    site_links?: {
      description?: string;
      score?: number;
      previous_score?: number | null;
    };
    content_score?: {
      description?: string;
      score?: number;
      previous_score?: number | null;
    };
    backlinks?: number;
  }>({});

  // State for editing SEO metrics - flattened for easier form handling
  const [editedSeoMetrics, setEditedSeoMetrics] = useState<{
    domain_rating_score?: number | null;
    domain_rating_previous_score?: number | null;
    organic_traffic_score?: number | null;
    organic_traffic_previous_score?: number | null;
    search_rankings_score?: number | null;
    search_rankings_previous_score?: number | null;
    search_terms_score?: number | null;
    search_terms_previous_score?: number | null;
    site_links_score?: number | null;
    site_links_previous_score?: number | null;
    content_score_value?: number | null;
    content_score_previous_score?: number | null;
    backlinks?: number | null;
  }>({});

  // State for editing SEO metrics
  const [isEditingSeoMetrics, setIsEditingSeoMetrics] = useState(false);

  // Pagination state for competitors
  const [currentCompetitorPage, setCurrentCompetitorPage] = useState(1);
  const competitorsPerPage = 5;

  // Sorting state for competitors
  const [competitorSortBy, setCompetitorSortBy] = useState<
    "rank" | "name" | "none"
  >("none");

  // Calculate total pages for competitors
  const totalCompetitorPages = Math.ceil(
    competitors.length / competitorsPerPage
  );

  // Sort competitors based on sorting preference
  const sortedCompetitors = [...competitors].sort((a, b) => {
    if (competitorSortBy === "rank") {
      // Sort by rank (lower rank = higher priority)
      const rankA = a.rank !== undefined ? a.rank : Number.MAX_SAFE_INTEGER;
      const rankB = b.rank !== undefined ? b.rank : Number.MAX_SAFE_INTEGER;
      return rankA - rankB;
    } else if (competitorSortBy === "name") {
      // Sort by name alphabetically
      return a.name.localeCompare(b.name);
    }
    // Default: no sorting (keep original order)
    return 0;
  });

  // Get current competitors for pagination
  const indexOfLastCompetitor = currentCompetitorPage * competitorsPerPage;
  const indexOfFirstCompetitor = indexOfLastCompetitor - competitorsPerPage;
  const currentCompetitors = sortedCompetitors.slice(
    indexOfFirstCompetitor,
    indexOfLastCompetitor
  );

  // State for CSV upload
  const [csvFile, setCsvFile] = useState<File | undefined>(undefined);
  const uploadCsv = useUploadCompetitorsCsv();
  const [newCompetitor, setNewCompetitor] = useState({
    name: "",
    target_url: "",
    description: "",
    rank: 0,
  });
  const [editingCompetitor, setEditingCompetitor] = useState<number | null>(
    null
  );
  const [editedCompetitor, setEditedCompetitor] = useState({
    name: "",
    target_url: "",
    description: "",
    rank: 0,
  });

  // Add these new state variables for keyword editing
  const [editingKeywords, setEditingKeywords] = useState<boolean[]>([]);
  const [editedKeywords, setEditedKeywords] = useState<{
    [key: number]: string[];
  }>({});
  const [newKeywordText, setNewKeywordText] = useState<{
    [key: number]: string;
  }>({});

  // Add states for strategy gaps and growth opportunities
  const [editingStrategyGaps, setEditingStrategyGaps] =
    useState<boolean>(false);
  const [editingGrowthOpportunities, setEditingGrowthOpportunities] =
    useState<boolean>(false);
  const [newStrategyGapText, setNewStrategyGapText] = useState<string>("");
  const [newGrowthOpportunityText, setNewGrowthOpportunityText] =
    useState<string>("");

  // Add states for website strategy gaps and growth opportunities
  const [strategyGaps, setStrategyGaps] = useState<
    { id: number; text: string }[]
  >([]);
  const [growthOpportunities, setGrowthOpportunities] = useState<
    { id: number; text: string }[]
  >([]);

  // Add these new state variables after the existing state declarations
  const [editingRankingIssues, setEditingRankingIssues] = useState<{
    [key: number]: boolean;
  }>({});
  const [editingContentRecommendations, setEditingContentRecommendations] =
    useState<{ [key: number]: boolean }>({});
  const [newRankingIssue, setNewRankingIssue] = useState<{
    [key: number]: {
      title: string;
      description: string;
      impact: "high" | "medium" | "low";
    };
  }>({});
  const [newContentRecommendation, setNewContentRecommendation] = useState<{
    [key: number]: {
      title: string;
      description: string;
      impact: "high" | "medium" | "low";
      estimated_hours: number;
      is_opportunity: boolean;
    };
  }>({});

  // Populate state when website data is loaded
  useEffect(() => {
    if (website) {
      setWebsiteData({
        url: website.url || "",
        industry: website.industry || "",
        language: website.language || "",
      });

      // Set languages
      if (website.available_languages) {
        setLanguages(
          website.available_languages.map((lang: any) => lang.language)
        );
      }

      // Set keywords
      if (website.keywords) {
        setKeywords(website.keywords);
      }

      // Set competitors
      if (website.competitors) {
        setCompetitors(website.competitors);
      }

      // Set strategy gaps and growth opportunities
      if (website.strategy_gaps) {
        setStrategyGaps(website.strategy_gaps);
      }

      if (website.growth_opportunities) {
        setGrowthOpportunities(website.growth_opportunities);
      }

      // Set SEO metrics
      if (website.seo_metrics) {
        // Set the nested structure
        setSeoMetrics(website.seo_metrics);

        // Create a flattened version for the form
        const flattenedMetrics = {
          domain_rating_score: website.seo_metrics.domain_rating?.score || null,
          domain_rating_previous_score:
            website.seo_metrics.domain_rating?.previous_score || null,
          organic_traffic_score:
            website.seo_metrics.organic_traffic?.score || null,
          organic_traffic_previous_score:
            website.seo_metrics.organic_traffic?.previous_score || null,
          search_rankings_score:
            website.seo_metrics.search_rankings?.score || null,
          search_rankings_previous_score:
            website.seo_metrics.search_rankings?.previous_score || null,
          search_terms_score: website.seo_metrics.search_terms?.score || null,
          search_terms_previous_score:
            website.seo_metrics.search_terms?.previous_score || null,
          site_links_score: website.seo_metrics.site_links?.score || null,
          site_links_previous_score:
            website.seo_metrics.site_links?.previous_score || null,
          content_score_value: website.seo_metrics.content_score?.score || null,
          content_score_previous_score:
            website.seo_metrics.content_score?.previous_score || null,
          backlinks: website.backlinks || 0,
        };

        setEditedSeoMetrics(flattenedMetrics);
      }
    }
  }, [website]);

  useEffect(() => {
    if (authLoading) return;

    if (!isAuthenticated) {
      router.push("/login");
      return;
    }

    if (currentUser?.role !== "admin") {
      router.push("/dashboard");
    }
  }, [currentUser, isAuthenticated, authLoading, router]);

  // Mutations

  const addLanguage = useAddWebsiteLanguage({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Language added successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add language",
        variant: "destructive",
      });
    },
  });

  const removeLanguage = useRemoveWebsiteLanguage({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Language removed successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to remove language",
        variant: "destructive",
      });
    },
  });

  const addKeyword = useAddWebsiteKeyword({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Keyword added successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add keyword",
        variant: "destructive",
      });
    },
  });

  const removeKeyword = useRemoveWebsiteKeyword({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Keyword removed successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to remove keyword",
        variant: "destructive",
      });
    },
  });

  const updateKeyword = useUpdateWebsiteKeyword({
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Keyword updated successfully",
      });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update keyword",
        variant: "destructive",
      });
    },
  });

  // Functions to handle editing

  const handleAddLanguage = () => {
    if (!newLanguage) return;

    if (languages.includes(newLanguage)) {
      toast({
        title: "Warning",
        description: "This language is already added",
        variant: "destructive",
      });
      return;
    }

    addLanguage.mutate({
      websiteId: id,
      language: newLanguage,
    });

    setNewLanguage("");
  };

  const handleRemoveLanguage = (language: string) => {
    removeLanguage.mutate({
      websiteId: id,
      language,
    });
  };

  const handleAddKeyword = () => {
    if (!newKeyword) return;

    if (keywords.some((k) => k.keyword === newKeyword)) {
      toast({
        title: "Warning",
        description: "This keyword is already added",
        variant: "destructive",
      });
      return;
    }

    addKeyword.mutate({
      websiteId: id,
      keyword: newKeyword,
      isTarget: newKeywordIsTarget,
    });

    setNewKeyword("");
    setNewKeywordIsTarget(false);
  };

  const handleRemoveKeyword = (keyword: string) => {
    removeKeyword.mutate({
      websiteId: id,
      keyword,
    });
  };

  const handleToggleTargetKeyword = (keyword: string) => {
    const isCurrentlyTarget =
      keywords.find((k) => k.keyword === keyword)?.is_target || false;

    updateKeyword.mutate({
      websiteId: id,
      keyword,
      isTarget: !isCurrentlyTarget,
    });
  };

  // Tab handling
  const tabs = [
    {
      id: "basic",
      label: "Basic Info",
      icon: <InfoIcon className="h-4 w-4" />,
    },
    {
      id: "strategy",
      label: "Strategy and Growth",
      icon: <TargetIcon className="h-4 w-4" />,
    },
    {
      id: "seo",
      label: "SEO Metrics",
      icon: <BarChart3 className="h-4 w-4" />,
    },
    {
      id: "competitors",
      label: "Competitors",
      icon: <UsersIcon className="h-4 w-4" />,
    },
  ];

  if (authLoading || isLoading || !id) {
    return (
      <div>
        <div>
          <div className="flex justify-center items-center min-h-[60vh]">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </div>
      </div>
    );
  }

  if (currentUser?.role !== "admin") return null;

  if (isError) {
    return (
      <div>
        <div>
          <div className="flex flex-col gap-4">
            <div className="flex items-center">
              <Link href="/dashboard/website" className="mr-4">
                <Button variant="outline" size="icon">
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              </Link>
              <h1 className="text-2xl font-bold">Website Details</h1>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Error</CardTitle>
                <CardDescription>
                  Failed to load website details
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-destructive">
                  {error instanceof Error
                    ? error.message
                    : "Unknown error occurred"}
                </p>
              </CardContent>
              <CardFooter>
                <Button
                  variant="outline"
                  onClick={() => router.push("/dashboard/website")}
                >
                  Back to Dashboard
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <header className="flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <div className="h-4 w-px bg-sidebar-border" />
          <div>
            <h1 className="text-lg font-semibold">Admin Dashboard</h1>
            <p className="text-xs text-muted-foreground">
              Satoloc Management Console
            </p>
          </div>
        </div>
        <div className="px-4">
          <ThemeToggle />
        </div>
      </header>
      <div>
        <div className="flex flex-col gap-4 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link href="/dashboard/website" className="mr-4">
                <Button variant="outline" size="icon">
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              </Link>
            </div>

            {website && (
              <div className="flex items-center gap-2">
                <Badge
                  variant="outline"
                  className="text-foreground dark:text-white border-foreground dark:border-white"
                >
                  {website.url}
                </Badge>
                <Badge
                  variant="secondary"
                  className="text-foreground dark:text-white border-foreground dark:border-white"
                >
                  Created: {format(new Date(website.created_at), "MMM d, yyyy")}
                </Badge>
              </div>
            )}
          </div>

          <Tabs defaultValue="basic-info" className="w-full">
            <TabsList className="grid grid-cols-4 mb-4">
              <TabsTrigger
                value="basic-info"
                className="flex items-center gap-2 text-foreground data-[state=active]:text-black dark:text-white dark:data-[state=active]:bg-white dark:data-[state=active]:text-black"
              >
                <Globe className="h-4 w-4" />
                <span>Basic Info</span>
              </TabsTrigger>
              <TabsTrigger
                value="strategy"
                className="flex items-center gap-2 text-foreground data-[state=active]:text-black dark:text-white dark:data-[state=active]:bg-white dark:data-[state=active]:text-black"
              >
                <TargetIcon className="h-4 w-4" />
                <span>Strategy and Growth </span>
              </TabsTrigger>
              <TabsTrigger
                value="seo"
                className="flex items-center gap-2 text-foreground data-[state=active]:text-black dark:text-white dark:data-[state=active]:bg-white dark:data-[state=active]:text-black"
              >
                <BarChart3 className="h-4 w-4" />
                <span>SEO Metrics</span>
              </TabsTrigger>
              <TabsTrigger
                value="competitors"
                className="flex items-center gap-2 text-foreground data-[state=active]:text-black dark:text-white dark:data-[state=active]:bg-white dark:data-[state=active]:text-black"
              >
                <Users2 className="h-4 w-4" />
                <span>Competitors</span>
              </TabsTrigger>
            </TabsList>

            {/* Basic Info Tab */}
            <TabsContent value="basic-info">
              <BasicInformation
                id={id}
                websiteData={websiteData}
                setWebsiteData={setWebsiteData}
                refetch={refetch}
              />
            </TabsContent>

            {/* Strategy & Growth Tab */}
            <TabsContent value="strategy">
              <StrategyAndGrowth
                id={id}
                strategyGaps={strategyGaps}
                growthOpportunities={growthOpportunities}
                editingStrategyGaps={editingStrategyGaps}
                setEditingStrategyGaps={setEditingStrategyGaps}
                editingGrowthOpportunities={editingGrowthOpportunities}
                setEditingGrowthOpportunities={setEditingGrowthOpportunities}
                newStrategyGapText={newStrategyGapText}
                setNewStrategyGapText={setNewStrategyGapText}
                newGrowthOpportunityText={newGrowthOpportunityText}
                setNewGrowthOpportunityText={setNewGrowthOpportunityText}
                refetch={refetch}
              />
            </TabsContent>

            {/* SEO Metrics Tab */}
            <TabsContent value="seo">
              <SeoMetrics
                id={id}
                seoMetrics={seoMetrics}
                editedSeoMetrics={editedSeoMetrics}
                setEditedSeoMetrics={setEditedSeoMetrics}
                isEditingSeoMetrics={isEditingSeoMetrics}
                setIsEditingSeoMetrics={setIsEditingSeoMetrics}
                refetch={refetch}
              />
            </TabsContent>

            {/* Competitors Tab */}
            <TabsContent value="competitors">
              <Competitors
                id={id}
                competitors={competitors}
                setCompetitors={setCompetitors}
                refetch={refetch}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
