"use client";

import { useState } from "react";
import { useAllWebsites } from "@/internal-api/seo/advance-seo";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { ThemeToggle } from "@/components/theme-toggle";
import SEOAnalysisForm from "@/components/SEOAnalysisForm";
import {
  Globe,
  Plus,
  Search,
  ExternalLink,
  Calendar,
  Loader2,
  BarChart3,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

export default function WebsitePage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [isAnalysisModalOpen, setIsAnalysisModalOpen] = useState(false);

  const { data: websites = [], isLoading, error } = useAllWebsites();

  // Filter websites based on search term
  const filteredWebsites = websites.filter(
    (website: any) =>
      website.url.toLowerCase().includes(searchTerm.toLowerCase()) ||
      website.industry?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <>
      {/* Header */}
      <header className="flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <div className="h-4 w-px bg-sidebar-border" />
          <div>
            <h1 className="text-lg font-semibold">Website Management</h1>
            <p className="text-xs text-muted-foreground">
              Manage and analyze all websites
            </p>
          </div>
        </div>
        <div className="px-4">
          <ThemeToggle />
        </div>
      </header>

      {/* Main Content */}
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="px-4 py-6 sm:px-0">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card className="shadow-none dark:border-muted-foreground">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Websites
                </CardTitle>
                <Globe className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{websites.length}</div>
                <p className="text-xs text-muted-foreground">
                  Websites under analysis
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-none dark:border-muted-foreground">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Active Analyses
                </CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {websites.filter((w: any) => w.last_analysis_date).length}
                </div>
                <p className="text-xs text-muted-foreground">
                  Websites with SEO data
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-none dark:border-muted-foreground">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Industries
                </CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {
                    new Set(
                      websites.map((w: any) => w.industry).filter(Boolean)
                    ).size
                  }
                </div>
                <p className="text-xs text-muted-foreground">
                  Different industries covered
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Search and Add Website */}
          <div className="flex items-center justify-between mb-6 dark:border-muted-foreground">
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search websites..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64 dark:border-muted-foreground"
                />
              </div>
            </div>
            <Dialog
              open={isAnalysisModalOpen}
              onOpenChange={setIsAnalysisModalOpen}
            >
              <DialogTrigger asChild>
                <Button className="flex items-center bg-[#1279b4] hover:bg-[#1279b4]/80 text-white">
                  <Plus className="h-4 w-4 mr-2" />
                  Analyze New Website
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-5xl dark:border-muted-foreground">
                <DialogHeader>
                  <DialogTitle>Add New Website Analysis</DialogTitle>
                  <DialogDescription>
                    Analyze a new website for SEO insights and performance data
                  </DialogDescription>
                </DialogHeader>
                <SEOAnalysisForm />
              </DialogContent>
            </Dialog>
          </div>

          {/* Websites Table */}
          <Card className="shadow-none dark:border-muted-foreground">
            <CardHeader>
              <CardTitle>All Websites</CardTitle>
              <CardDescription>
                Complete list of websites under SEO analysis (
                {filteredWebsites.length} shown)
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">Loading websites...</span>
                </div>
              ) : error ? (
                <div className="text-center py-8 text-red-600">
                  Error loading websites: {error.message}
                </div>
              ) : filteredWebsites.length === 0 ? (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  {searchTerm
                    ? "No websites found matching your search"
                    : "No websites found"}
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-muted-foreground">
                    <thead className="bg-gray-50 dark:bg-black">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Website
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Industry
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Language
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Last Analysis
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-black divide-y divide-gray-200 dark:divide-gray-700">
                      {filteredWebsites.map((website: any) => (
                        <tr key={website.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <Globe className="h-5 w-5 text-gray-400 mr-3" />
                              <div>
                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                  {website.url}
                                </div>
                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                  ID: {website.id}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {website.industry ? (
                              <Badge variant="secondary">
                                {website.industry}
                              </Badge>
                            ) : (
                              <span className="text-gray-400">
                                Not specified
                              </span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {website.language ? (
                              <Badge variant="outline">
                                {website.language.toUpperCase()}
                              </Badge>
                            ) : (
                              <span className="text-gray-400">
                                Not specified
                              </span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {website.last_analysis_date ? (
                              <div className="flex items-center">
                                <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                                {new Date(
                                  website.last_analysis_date
                                ).toLocaleDateString()}
                              </div>
                            ) : (
                              <span className="text-gray-400">
                                Never analyzed
                              </span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() =>
                                  window.open(
                                    `https://${website.url}`,
                                    "_blank"
                                  )
                                }
                              >
                                <ExternalLink className="h-4 w-4 mr-1" />
                                Visit
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  // Navigate to website details page
                                  window.location.href = `/dashboard/website/${website.id}`;
                                }}
                              >
                                <BarChart3 className="h-4 w-4 mr-1" />
                                Analyze
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
}
