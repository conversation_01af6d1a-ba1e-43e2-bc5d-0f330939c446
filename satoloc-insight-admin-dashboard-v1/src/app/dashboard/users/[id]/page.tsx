"use client";

import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAuthCheck } from "@/hooks/useAuth";
import { useUsers, useUpdateUser, useCreateUser } from "@/hooks/useUsers";
import { User, CreateUserData } from "@/lib/api";
import {
  ArrowLeft,
  User as UserIcon,
  Mail,
  Calendar,
  CalendarDays,
  Globe,
  Building,
  CreditCard,
  Shield,
  Phone,
  MapPin,
  Camera,
  Edit,
  MoreVertical,
  Trash2,
  Lock,
  Unlock,
  Check,
  X,
  Save,
  Plus,
  Users,
  CheckCircle,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTit<PERSON>,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import ClientOnly from "@/components/ClientOnly";
import { ThemeToggle } from "@/components/theme-toggle";
import { SidebarTrigger } from "@/components/ui/sidebar";

function UserDetailsPageContent() {
  const router = useRouter();
  const params = useParams();
  const userId = params.id as string;

  const {
    user: currentUser,
    isAuthenticated,
    isAdmin,
    isLoading: authLoading,
  } = useAuthCheck();
  const { data: users = [] } = useUsers();
  const updateUserMutation = useUpdateUser();
  const createUserMutation = useCreateUser();

  const [user, setUser] = useState<User | null>(null);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValues, setEditValues] = useState<Record<string, any>>({});
  const [isUpdating, setIsUpdating] = useState(false);
  const [lastUpdatedField, setLastUpdatedField] = useState<string | null>(null);

  // User creation state
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);
  const [createdUser, setCreatedUser] = useState<User | null>(null);
  const [createUserData, setCreateUserData] = useState({
    username: "",
    email: "",
    first_name: "",
    last_name: "",
    role: "user",
    subscription_plan: "freemium",
    subscription_price: "",
    company_name: "",
    website: "",
    industry: "",
    password: "",
    confirmPassword: "",
  });

  // Industry options
  const industryOptions = [
    { value: "web3", label: "Web3" },
    { value: "fintech", label: "Fintech" },
    { value: "technology", label: "Technology" },
  ];

  // Subscription plan options
  const subscriptionPlanOptions = [
    { value: "freemium", label: "Freemium" },
    { value: "pro", label: "Pro" },
    { value: "enterprise", label: "Enterprise" },
  ];

  // Role options
  const roleOptions = [
    { value: "user", label: "User" },
    { value: "sub_admin", label: "Sub Admin" },
    { value: "admin", label: "Admin" },
  ];

  // Status options
  const statusOptions = [
    { value: "true", label: "Active" },
    { value: "false", label: "Inactive" },
  ];

  // Get sub-admin options for "Created By" field
  const subAdminOptions = users
    .filter((u) => u.role === "sub_admin")
    .map((u) => ({
      value: String(u.id),
      label:
        `${u.username} (${u.first_name || ""} ${u.last_name || ""})`.trim() ||
        u.username,
    }));

  // Add "None" option for removing assignment
  const createdByOptions = [
    { value: "none", label: "None (No Assignment)" },
    ...subAdminOptions,
  ];

  // Find the user by ID
  useEffect(() => {
    if (users.length > 0 && userId) {
      const foundUser = users.find((u) => u.id.toString() === userId);
      setUser(foundUser || null);
    }
  }, [users, userId]);

  // Redirect to login if not authenticated or not admin
  useEffect(() => {
    if (!authLoading && (!isAuthenticated || !isAdmin)) {
      router.push("/login");
    }
  }, [isAuthenticated, isAdmin, authLoading, router]);

  const handleGoBack = () => {
    router.push("/dashboard");
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatDateShort = (dateString: string | null) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "admin":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
      case "sub_admin":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200";
    }
  };

  const getPlanBadgeColor = (plan: string) => {
    switch (plan) {
      case "pro":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "enterprise":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200";
    }
  };

  // Editing functions
  const handleEditField = (fieldName: string, currentValue: string) => {
    setEditingField(fieldName);
    // For date fields, convert back to YYYY-MM-DD format for the input
    let initialValue = currentValue || "";
    if (fieldName === "subscription_start_date") {
      initialValue = currentValue ? currentValue.split("T")[0] : "";
    } else if (fieldName === "is_active") {
      // Convert boolean to string for the select
      initialValue = String(currentValue);
    }
    setEditValues({ [fieldName]: initialValue });
  };

  const handleSaveField = async () => {
    if (!user || !editingField) return;

    setIsUpdating(true);
    try {
      // Prepare the data for API - use processed values when available
      const apiData: any = {};
      const processedKey = `${editingField}_processed`;

      if (editValues[processedKey] !== undefined) {
        // Use processed value for API
        apiData[editingField] = editValues[processedKey];
      } else {
        // Use regular value for API
        apiData[editingField] = editValues[editingField];
      }

      await updateUserMutation.mutateAsync({
        userId: user.id,
        userData: apiData,
      });

      // Update local user state with the processed value
      setUser({ ...user, ...apiData });
      setLastUpdatedField(editingField);
      setEditingField(null);
      setEditValues({});

      // Clear success indicator after 2 seconds
      setTimeout(() => setLastUpdatedField(null), 2000);
    } catch (error) {
      console.error("Failed to update user:", error);
      // You could add a toast notification here
    } finally {
      setIsUpdating(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingField(null);
    setEditValues({});
  };

  const handleInputChange = (fieldName: string, value: string) => {
    // Handle different field types properly
    let processedValue: any = value;
    let displayValue = value; // Keep the raw input value for display

    if (fieldName === "subscription_price") {
      // Keep as string since API expects string format like "69.00"
      processedValue = value === "" ? null : value;
    } else if (fieldName === "subscription_start_date") {
      // For API: Set to null if empty, otherwise convert to ISO string format with timezone
      processedValue =
        value === "" ? null : value ? `${value}T00:00:00.000000Z` : null;
      // For display: keep the YYYY-MM-DD format
      displayValue = value;
    } else if (fieldName === "is_active") {
      // Convert string boolean to actual boolean
      processedValue = value === "true";
      displayValue = value;
    } else if (fieldName === "created_by") {
      // Handle created_by field - convert string ID to number or null
      processedValue =
        value === "none" || value === "" ? null : parseInt(value, 10);
      displayValue = value;
    }

    // Update edit values with both the display value and processed value
    setEditValues({
      ...editValues,
      [fieldName]: displayValue, // Use display value for immediate preview
      [`${fieldName}_processed`]: processedValue, // Store processed value for API
    });
  };

  // User creation functions
  const handleCreateUser = () => {
    setIsCreateModalOpen(true);
  };

  const handleCloseCreateModal = () => {
    setIsCreateModalOpen(false);
    setCreateUserData({
      username: "",
      email: "",
      first_name: "",
      last_name: "",
      role: "user",
      subscription_plan: "freemium",
      subscription_price: "",
      company_name: "",
      website: "",
      industry: "",
      password: "",
      confirmPassword: "",
    });
  };

  const handleCreateUserSubmit = async () => {
    setIsCreating(true);
    try {
      // Basic validation
      if (
        !createUserData.username ||
        !createUserData.email ||
        !createUserData.password
      ) {
        alert("Please fill in required fields: username, email, and password");
        return;
      }

      if (createUserData.password !== createUserData.confirmPassword) {
        alert("Passwords do not match");
        return;
      }

      // Prepare data for API
      const userData: CreateUserData = {
        username: createUserData.username,
        email: createUserData.email,
        first_name: createUserData.first_name,
        last_name: createUserData.last_name,
        role: createUserData.role,
        subscription_plan: createUserData.subscription_plan,
        subscription_price: createUserData.subscription_price || null,
        company_name: createUserData.company_name,
        website: createUserData.website,
        industry: createUserData.industry,
        password: createUserData.password,
        confirmPassword: createUserData.confirmPassword,
      };

      // Call create user API
      const newUser = await createUserMutation.mutateAsync(userData);

      // Close create modal and show success modal
      handleCloseCreateModal();
      setCreatedUser(newUser);
      setIsSuccessModalOpen(true);
    } catch (error) {
      console.error("Failed to create user:", error);
      alert("Failed to create user. Please try again.");
    } finally {
      setIsCreating(false);
    }
  };

  const handleCreateUserInputChange = (field: string, value: string) => {
    setCreateUserData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Get created users for sub-admins
  const getCreatedUsers = () => {
    if (!user || user.role !== "sub_admin") return [];
    // First try to use the created_users array from the API response
    if (user.created_users && user.created_users.length > 0) {
      return user.created_users;
    }
    // Fallback to filtering all users by created_by
    return users.filter((u) => u.created_by === user.id);
  };

  const canCreateMoreUsers = () => {
    if (!user) return false;
    if (user.role === "admin") return true;
    if (user.role === "sub_admin") {
      const createdCount = getCreatedUsers().length;
      return createdCount < 3;
    }
    return false;
  };

  const getRemainingSlots = () => {
    if (!user) return 0;
    if (user.role === "admin") return null; // Unlimited
    if (user.role === "sub_admin") {
      const createdCount = getCreatedUsers().length;
      return Math.max(0, 3 - createdCount);
    }
    return 0;
  };

  // EditableField component
  const EditableField = ({
    fieldName,
    label,
    value,
    placeholder = "Not provided",
    type = "text",
    options,
  }: {
    fieldName: string;
    label: string;
    value: string | null;
    placeholder?: string;
    type?: string;
    options?: Array<{ value: string; label: string }>;
  }) => {
    const isEditing = editingField === fieldName;
    const displayValue = value || placeholder;
    const currentValue = value || "";
    const wasJustUpdated = lastUpdatedField === fieldName;

    return (
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          {label}
        </label>
        {isEditing ? (
          <div className="flex items-center space-x-2">
            {options ? (
              <Select
                value={editValues[fieldName] || currentValue}
                onValueChange={(value) => handleInputChange(fieldName, value)}
              >
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder={placeholder} />
                </SelectTrigger>
                <SelectContent>
                  {options.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <div className="relative flex-1">
                <Input
                  type={type}
                  value={editValues[fieldName] || ""}
                  onChange={(e) => handleInputChange(fieldName, e.target.value)}
                  className={type === "date" ? "pr-10" : ""}
                  autoFocus
                />
                {type === "date" && (
                  <CalendarDays className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                )}
              </div>
            )}
            <Button
              size="sm"
              onClick={handleSaveField}
              disabled={isUpdating}
              className="px-2"
            >
              {isUpdating ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Check className="h-4 w-4" />
              )}
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={handleCancelEdit}
              disabled={isUpdating}
              className="px-2"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        ) : (
          <div
            onClick={() => handleEditField(fieldName, currentValue)}
            className={`text-sm text-gray-900 dark:text-white p-2 rounded cursor-pointer transition-colors flex items-center justify-between group ${
              wasJustUpdated
                ? "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800"
                : "bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600"
            }`}
          >
            {fieldName === "subscription_plan" && value ? (
              <span
                className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPlanBadgeColor(
                  value
                )}`}
              >
                {value.charAt(0).toUpperCase() + value.slice(1)}
              </span>
            ) : fieldName === "subscription_price" && value ? (
              <span className="font-semibold">${value}/month</span>
            ) : fieldName === "subscription_start_date" && value ? (
              <span className="font-medium">{formatDateShort(value)}</span>
            ) : fieldName === "subscription_start_date" && !value ? (
              <span className="text-gray-500 italic">No start date set</span>
            ) : fieldName === "role" && value ? (
              <span
                className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleBadgeColor(
                  value
                )}`}
              >
                {value === "admin"
                  ? "Administrator"
                  : value === "sub_admin"
                  ? "Sub Admin"
                  : "User"}
              </span>
            ) : fieldName === "is_active" ? (
              <span
                className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  value === "true" || String(value) === "true"
                    ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                    : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                }`}
              >
                {value === "true" || String(value) === "true"
                  ? "Active"
                  : "Inactive"}
              </span>
            ) : fieldName === "created_by" ? (
              <span className={value ? "" : "text-gray-500 italic"}>
                {value
                  ? users.find((u) => u.id === parseInt(String(value)))
                      ?.username || `User ID: ${value}`
                  : "No Assignment"}
              </span>
            ) : (
              <span className={value ? "" : "text-gray-500 italic"}>
                {displayValue}
              </span>
            )}
            {wasJustUpdated ? (
              <Check className="h-4 w-4 text-green-600" />
            ) : (
              <Edit className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
            )}
          </div>
        )}
      </div>
    );
  };

  const LoadingSpinner = () => (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <p>Loading...</p>
      </div>
    </div>
  );

  if (authLoading) {
    return <LoadingSpinner />;
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                User Not Found
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                The user you're looking for doesn't exist or has been removed.
              </p>
              <Button onClick={handleGoBack} className="flex items-center">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-black">
      {/* Header */}
      <header className="flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <div className="h-4 w-px bg-sidebar-border" />
          <div>
            <h1 className="text-lg font-semibold">User Details</h1>
            <p className="text-xs text-muted-foreground">
              View and manage user details
            </p>
          </div>
        </div>
        <div className="px-4">
          <ThemeToggle />
        </div>
      </header>

      {/* Main Content */}
      <main className=" mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* User Overview Card */}
          <Card className="mb-8 shadow-none">
            <CardContent className="p-6">
              <div className="flex items-start space-x-6">
                <div className="flex-shrink-0">
                  <div className="h-20 w-20 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white text-2xl font-bold">
                    {user.first_name
                      ? user.first_name[0]
                      : user.username[0].toUpperCase()}
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-3 mb-2">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                      {user.first_name || user.last_name
                        ? `${user.first_name} ${user.last_name}`.trim()
                        : user.username}
                    </h2>
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleBadgeColor(
                        user.role
                      )}`}
                    >
                      {user.role === "admin" ? "Administrator" : user.role}
                    </span>
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        user.is_active
                          ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                          : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                      }`}
                    >
                      {user.is_active ? "Active" : "Inactive"}
                    </span>
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 mr-1" />
                      {user.email}
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      Joined {formatDateShort(user.registration_date)}
                    </div>
                    <div className="flex items-center">
                      <UserIcon className="h-4 w-4 mr-1" />
                      ID #{user.id}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Details Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column - Basic & Contact Info */}
            <div className="lg:col-span-2 space-y-6">
              {/* Administrative Controls */}
              <Card className="shadow-none">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Shield className="h-5 w-5 mr-2" />
                    Administrative Controls
                  </CardTitle>
                  <CardDescription>
                    Manage user permissions and account status
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <EditableField
                      fieldName="role"
                      label="User Role"
                      value={user.role}
                      options={roleOptions}
                      placeholder="Select role"
                    />
                    <EditableField
                      fieldName="is_active"
                      label="Account Status"
                      value={String(user.is_active)}
                      options={statusOptions}
                      placeholder="Select status"
                    />
                  </div>

                  {/* Only show Created By field for admins */}
                  {currentUser?.role === "admin" && (
                    <div className="pt-4 border-t border-gray-200 dark:border-gray-600">
                      <EditableField
                        fieldName="created_by"
                        label="Created By (Sub-Admin Assignment)"
                        value={
                          user.created_by ? String(user.created_by) : "none"
                        }
                        options={createdByOptions}
                        placeholder="Select sub-admin"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Assign this user to a sub-admin. This determines which
                        sub-admin manages this user.
                        {user.created_by && (
                          <span className="block mt-1 text-blue-600">
                            Currently assigned to:{" "}
                            {users.find((u) => u.id === user.created_by)
                              ?.username || "Unknown"}
                          </span>
                        )}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Basic Information */}
              <Card className="shadow-none">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <UserIcon className="h-5 w-5 mr-2" />
                    Basic Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <EditableField
                      fieldName="username"
                      label="Username"
                      value={user.username}
                    />
                    <EditableField
                      fieldName="email"
                      label="Email Address"
                      value={user.email}
                      type="email"
                    />
                    <EditableField
                      fieldName="first_name"
                      label="First Name"
                      value={user.first_name}
                      placeholder="Not provided"
                    />
                    <EditableField
                      fieldName="last_name"
                      label="Last Name"
                      value={user.last_name}
                      placeholder="Not provided"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Business Information */}
              <Card className="shadow-none">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Building className="h-5 w-5 mr-2" />
                    Business Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <EditableField
                      fieldName="company_name"
                      label="Company Name"
                      value={user.company_name}
                      placeholder="Not provided"
                    />
                    <EditableField
                      fieldName="industry"
                      label="Industry"
                      value={user.industry}
                      placeholder="Not specified"
                      options={industryOptions}
                    />
                    <div className="md:col-span-2">
                      <EditableField
                        fieldName="website"
                        label="Website"
                        value={user.website}
                        placeholder="Not provided"
                        type="url"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Right Column - Subscription & Timeline */}
            <div className="space-y-6">
              {/* Subscription Information */}
              <Card className="shadow-none">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CreditCard className="h-5 w-5 mr-2" />
                    Subscription
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <EditableField
                    fieldName="subscription_plan"
                    label="Plan"
                    value={user.subscription_plan}
                    options={subscriptionPlanOptions}
                    placeholder="Select plan"
                  />
                  <EditableField
                    fieldName="subscription_price"
                    label="Monthly Price ($)"
                    value={user.subscription_price}
                    placeholder="0.00"
                    type="number"
                  />
                  <EditableField
                    fieldName="subscription_start_date"
                    label="Start Date"
                    value={
                      user.subscription_start_date
                        ? user.subscription_start_date.split("T")[0]
                        : null
                    }
                    placeholder="No start date set"
                    type="date"
                  />
                </CardContent>
              </Card>

              {/* Account Timeline */}
              <Card className="shadow-none">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Calendar className="h-5 w-5 mr-2" />
                    Account Timeline
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Registration Date
                    </label>
                    <p className="text-sm text-gray-900 dark:text-white">
                      {formatDate(user.registration_date)}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Created Date
                    </label>
                    <p className="text-sm text-gray-900 dark:text-white">
                      {formatDate(user.created)}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Profile Picture
                    </label>
                    <div className="flex items-center space-x-2">
                      <Camera
                        className={`h-4 w-4 ${
                          user.profile_picture
                            ? "text-green-500"
                            : "text-gray-400"
                        }`}
                      />
                      <span
                        className={`text-sm ${
                          user.profile_picture
                            ? "text-green-600"
                            : "text-gray-600 dark:text-gray-300"
                        }`}
                      >
                        {user.profile_picture ? "Available" : "Not set"}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* User Management - Only show for sub-admins */}
              {user.role === "sub_admin" && (
                <Card className="shadow-none">
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Users className="h-5 w-5 mr-2" />
                      User Management
                    </CardTitle>
                    <CardDescription>
                      Manage users created by this sub-admin (
                      {getCreatedUsers().length}/3 used)
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Created Users List */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Created Users
                      </label>
                      {getCreatedUsers().length > 0 ? (
                        <div className="space-y-2">
                          {getCreatedUsers().map((createdUser) => (
                            <div
                              key={createdUser.id}
                              className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md"
                            >
                              <div>
                                <p className="font-medium text-sm">
                                  {createdUser.username}
                                </p>
                                <p className="text-xs text-gray-600 dark:text-gray-400">
                                  {createdUser.email}
                                </p>
                              </div>
                              <div className="flex items-center space-x-2">
                                <span
                                  className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                    createdUser.is_active
                                      ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                      : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                                  }`}
                                >
                                  {createdUser.is_active
                                    ? "Active"
                                    : "Inactive"}
                                </span>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() =>
                                    router.push(
                                      `/dashboard/users/${createdUser.id}`
                                    )
                                  }
                                >
                                  View
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-sm text-gray-600 dark:text-gray-400 italic">
                          No users created yet
                        </p>
                      )}
                    </div>

                    {/* Create User Button */}
                    <div className="pt-3 border-t border-gray-200 dark:border-gray-600">
                      {canCreateMoreUsers() ? (
                        <Button
                          onClick={handleCreateUser}
                          className="w-full flex items-center justify-center"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Create New User ({getRemainingSlots()} slots
                          remaining)
                        </Button>
                      ) : (
                        <div className="p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md">
                          <div className="flex items-center">
                            <Shield className="h-4 w-4 text-amber-600 mr-2" />
                            <span className="text-sm font-medium text-amber-800 dark:text-amber-200">
                              User Creation Limit Reached
                            </span>
                          </div>
                          <p className="text-xs text-amber-600 dark:text-amber-300 mt-1">
                            Sub-admins can create up to 3 users. This limit has
                            been reached.
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Quick Actions */}
              <Card className="shadow-none">
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button variant="outline" className="w-full justify-start">
                    <Mail className="h-4 w-4 mr-2" />
                    Send Email
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() =>
                      handleEditField("is_active", String(!user.is_active))
                    }
                  >
                    {user.is_active ? (
                      <>
                        <Lock className="h-4 w-4 mr-2" />
                        Deactivate Account
                      </>
                    ) : (
                      <>
                        <Unlock className="h-4 w-4 mr-2" />
                        Activate Account
                      </>
                    )}
                  </Button>
                  {user.role !== "admin" && (
                    <Button
                      variant="destructive"
                      className="w-full justify-start"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete User
                    </Button>
                  )}
                  {user.role === "admin" && (
                    <div className="p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md">
                      <div className="flex items-center">
                        <Shield className="h-4 w-4 text-amber-600 mr-2" />
                        <span className="text-sm font-medium text-amber-800 dark:text-amber-200">
                          Admin Account
                        </span>
                      </div>
                      <p className="text-xs text-amber-600 dark:text-amber-300 mt-1">
                        Admin accounts cannot be deleted and have special
                        privileges.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>

      {/* Create User Modal */}
      <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New User</DialogTitle>
            <DialogDescription>
              Add a new user to the system. All fields marked with * are
              required.
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6">
            {/* Left Column - Basic Info */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Basic Information</h3>

              <div>
                <Label htmlFor="username">Username *</Label>
                <Input
                  id="username"
                  type="text"
                  placeholder="Enter username"
                  value={createUserData.username}
                  onChange={(e) =>
                    handleCreateUserInputChange("username", e.target.value)
                  }
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter email address"
                  value={createUserData.email}
                  onChange={(e) =>
                    handleCreateUserInputChange("email", e.target.value)
                  }
                  className="mt-1"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="first_name">First Name</Label>
                  <Input
                    id="first_name"
                    type="text"
                    placeholder="First name"
                    value={createUserData.first_name}
                    onChange={(e) =>
                      handleCreateUserInputChange("first_name", e.target.value)
                    }
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="last_name">Last Name</Label>
                  <Input
                    id="last_name"
                    type="text"
                    placeholder="Last name"
                    value={createUserData.last_name}
                    onChange={(e) =>
                      handleCreateUserInputChange("last_name", e.target.value)
                    }
                    className="mt-1"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="password">Password *</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Enter password"
                  value={createUserData.password}
                  onChange={(e) =>
                    handleCreateUserInputChange("password", e.target.value)
                  }
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="confirmPassword">Confirm Password *</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  placeholder="Confirm password"
                  value={createUserData.confirmPassword}
                  onChange={(e) =>
                    handleCreateUserInputChange(
                      "confirmPassword",
                      e.target.value
                    )
                  }
                  className="mt-1"
                />
              </div>
            </div>

            {/* Right Column - Additional Info */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Additional Information</h3>

              <div>
                <Label htmlFor="role">User Role</Label>
                <Select
                  value={createUserData.role}
                  onValueChange={(value) =>
                    handleCreateUserInputChange("role", value)
                  }
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user">User</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 mt-1">
                  Sub-admins can only create regular users
                </p>
              </div>

              <div>
                <Label htmlFor="company_name">Company Name</Label>
                <Input
                  id="company_name"
                  type="text"
                  placeholder="Company name"
                  value={createUserData.company_name}
                  onChange={(e) =>
                    handleCreateUserInputChange("company_name", e.target.value)
                  }
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  type="url"
                  placeholder="https://example.com"
                  value={createUserData.website}
                  onChange={(e) =>
                    handleCreateUserInputChange("website", e.target.value)
                  }
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="industry">Industry</Label>
                <Select
                  value={createUserData.industry}
                  onValueChange={(value) =>
                    handleCreateUserInputChange("industry", value)
                  }
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select industry" />
                  </SelectTrigger>
                  <SelectContent>
                    {industryOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="subscription_plan">Subscription Plan</Label>
                <Select
                  value={createUserData.subscription_plan}
                  onValueChange={(value) =>
                    handleCreateUserInputChange("subscription_plan", value)
                  }
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select plan" />
                  </SelectTrigger>
                  <SelectContent>
                    {subscriptionPlanOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="subscription_price">Monthly Price ($)</Label>
                <Input
                  id="subscription_price"
                  type="number"
                  placeholder="0.00"
                  value={createUserData.subscription_price}
                  onChange={(e) =>
                    handleCreateUserInputChange(
                      "subscription_price",
                      e.target.value
                    )
                  }
                  className="mt-1"
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={handleCloseCreateModal}
              disabled={isCreating}
            >
              Cancel
            </Button>
            <Button onClick={handleCreateUserSubmit} disabled={isCreating}>
              {isCreating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating User...
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  Create User
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Success Modal */}
      <Dialog open={isSuccessModalOpen} onOpenChange={setIsSuccessModalOpen}>
        <DialogContent className="max-w-md p-2">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <CheckCircle className="h-6 w-6 text-green-600 mr-2 " />
              User Created Successfully
            </DialogTitle>
            <DialogDescription>
              The new user has been created and is now available in the system.
            </DialogDescription>
          </DialogHeader>

          {createdUser && (
            <div className="py-4">
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4">
                <h4 className="font-medium text-green-900 dark:text-green-100">
                  User Details:
                </h4>
                <div className="mt-2 text-sm text-green-800 dark:text-green-200">
                  <p>
                    <strong>Username:</strong> {createdUser.username}
                  </p>
                  <p>
                    <strong>Email:</strong> {createdUser.email}
                  </p>
                  <p>
                    <strong>Role:</strong> {createdUser.role}
                  </p>
                  <p>
                    <strong>ID:</strong> #{createdUser.id}
                  </p>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsSuccessModalOpen(false)}
            >
              Close
            </Button>
            <Button
              onClick={() => {
                setIsSuccessModalOpen(false);
                if (createdUser) {
                  router.push(`/dashboard/users/${createdUser.id}`);
                }
              }}
            >
              View User Details
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default function UserDetailsPage() {
  return (
    <ClientOnly
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <p>Loading...</p>
          </div>
        </div>
      }
    >
      <UserDetailsPageContent />
    </ClientOnly>
  );
}
