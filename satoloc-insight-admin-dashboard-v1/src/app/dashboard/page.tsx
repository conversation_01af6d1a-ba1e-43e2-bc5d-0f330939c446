"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { ThemeToggle } from "@/components/theme-toggle";
import { useAuthCheck } from "@/hooks/useAuth";
import { useUsers, useUserStats, useCreateUser } from "@/hooks/useUsers";
import { User, CreateUserData } from "@/lib/api";
import { UsersPagination } from "@/components/users-pagination";
import AllUsersTable from "@/components/AllUsersTable";
import SuccessModal from "@/components/SuccessModal";
import {
  Shield,
  Users,
  Settings,
  Loader2,
  BarChart3,
  Database,
  UserCheck,
  Plus,
  X,
  CheckCircle,
  User as UserIcon,
} from "lucide-react";

export default function DashboardPage() {
  const router = useRouter();
  const {
    user: currentUser,
    isAuthenticated,
    isAdmin,
    isLoading,
    error,
  } = useAuthCheck();
  const { data: users = [], isLoading: usersLoading } = useUsers();
  const { stats } = useUserStats();
  const createUserMutation = useCreateUser();

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Create user modal state
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);
  const [createdUser, setCreatedUser] = useState<User | null>(null);
  const [createUserData, setCreateUserData] = useState({
    username: "",
    email: "",
    first_name: "",
    last_name: "",
    role: "user",
    subscription_plan: "freemium",
    subscription_price: "",
    company_name: "",
    website: "",
    industry: "",
    password: "",
    confirmPassword: "",
  });

  // Calculate pagination
  const totalItems = users.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedUsers = users.slice(startIndex, endIndex);

  // Reset to first page when users data changes (e.g., new user created)
  useEffect(() => {
    if (totalPages > 0 && currentPage > totalPages) {
      setCurrentPage(1);
    }
  }, [totalPages, currentPage]);

  // Redirect to login if not authenticated or not admin
  useEffect(() => {
    if (!isLoading && (!isAuthenticated || !isAdmin)) {
      router.push("/login");
    }
  }, [isAuthenticated, isAdmin, isLoading, router]);

  const handleViewDetails = (user: User) => {
    router.push(`/dashboard/users/${user.id}`);
  };

  const handleCreateUser = () => {
    setIsCreateModalOpen(true);
  };

  const handleCloseCreateModal = () => {
    setIsCreateModalOpen(false);
    setCreateUserData({
      username: "",
      email: "",
      first_name: "",
      last_name: "",
      role: "user",
      subscription_plan: "freemium",
      subscription_price: "",
      company_name: "",
      website: "",
      industry: "",
      password: "",
      confirmPassword: "",
    });
  };

  const handleCreateUserSubmit = async () => {
    setIsCreating(true);
    try {
      // Basic validation
      if (
        !createUserData.username ||
        !createUserData.email ||
        !createUserData.password
      ) {
        alert("Please fill in required fields: username, email, and password");
        return;
      }

      if (createUserData.password !== createUserData.confirmPassword) {
        alert("Passwords do not match");
        return;
      }

      // Prepare data for API
      const userData: CreateUserData = {
        username: createUserData.username,
        email: createUserData.email,
        first_name: createUserData.first_name,
        last_name: createUserData.last_name,
        role: createUserData.role,
        subscription_plan: createUserData.subscription_plan,
        subscription_price: createUserData.subscription_price || null,
        company_name: createUserData.company_name,
        website: createUserData.website,
        industry: createUserData.industry,
        password: createUserData.password,
        confirmPassword: createUserData.confirmPassword,
      };

      // Call create user API
      const newUser = await createUserMutation.mutateAsync(userData);

      // Close create modal and show success modal
      handleCloseCreateModal();
      setCreatedUser(newUser);
      setIsSuccessModalOpen(true);
    } catch (error) {
      console.error("Failed to create user:", error);
      alert("Failed to create user. Please try again.");
    } finally {
      setIsCreating(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setCreateUserData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Header */}
      <header className="flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <div className="h-4 w-px bg-sidebar-border" />
          <div>
            <h1 className="text-lg font-semibold">Admin Dashboard</h1>
            <p className="text-xs text-muted-foreground">
              Satoloc Management Console
            </p>
          </div>
        </div>
        <div className="px-4">
          <ThemeToggle />
        </div>
      </header>

      {/* Main Content */}
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="px-4 py-6 sm:px-0">
          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {error.message || "An error occurred"}
            </div>
          )}

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card className="shadow-none">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Users
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalUsers}</div>
                <p className="text-xs text-muted-foreground">
                  Registered users in the system
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-none">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Admin Users
                </CardTitle>
                <UserCheck className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.adminUsers}</div>
                <p className="text-xs text-muted-foreground">
                  Users with admin privileges
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-none">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  System Status
                </CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">Online</div>
                <p className="text-xs text-muted-foreground">
                  All systems operational
                </p>
              </CardContent>
            </Card>
          </div>

          {/* All Users Table */}
          <AllUsersTable
            users={paginatedUsers}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            setItemsPerPage={setItemsPerPage}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            totalPages={totalPages}
            onCreateUser={handleCreateUser}
            onViewDetails={handleViewDetails}
            onPageChange={handlePageChange}
          />
        </div>
      </div>

      {/* Create User Modal */}
      <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
        <DialogContent className="max-w-4xl max-h-[95vh] overflow-hidden flex flex-col">
          <DialogHeader className="pb-4">
            <DialogTitle className="text-xl font-semibold">
              Create New User
            </DialogTitle>
            <DialogDescription className="text-sm text-gray-600 dark:text-gray-400">
              Add a new user to the system. Required fields are marked with *.
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto p-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 py-2">
              {/* Basic Information */}
              <div className="space-y-5">
                <h3 className="font-semibold text-base text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Basic Information
                </h3>

                <div className="space-y-2">
                  <Label
                    htmlFor="username"
                    className="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Username *
                  </Label>
                  <Input
                    id="username"
                    value={createUserData.username}
                    onChange={(e) =>
                      handleInputChange("username", e.target.value)
                    }
                    placeholder="Enter username"
                    className="h-10"
                  />
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="email"
                    className="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Email *
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={createUserData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    placeholder="Enter email address"
                    className="h-10"
                  />
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="first_name"
                    className="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    First Name
                  </Label>
                  <Input
                    id="first_name"
                    value={createUserData.first_name}
                    onChange={(e) =>
                      handleInputChange("first_name", e.target.value)
                    }
                    placeholder="Enter first name"
                    className="h-10"
                  />
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="last_name"
                    className="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Last Name
                  </Label>
                  <Input
                    id="last_name"
                    value={createUserData.last_name}
                    onChange={(e) =>
                      handleInputChange("last_name", e.target.value)
                    }
                    placeholder="Enter last name"
                    className="h-10"
                  />
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="password"
                    className="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Password *
                  </Label>
                  <Input
                    id="password"
                    type="password"
                    value={createUserData.password}
                    onChange={(e) =>
                      handleInputChange("password", e.target.value)
                    }
                    placeholder="Enter password"
                    className="h-10"
                  />
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="confirmPassword"
                    className="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Confirm Password *
                  </Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={createUserData.confirmPassword}
                    onChange={(e) =>
                      handleInputChange("confirmPassword", e.target.value)
                    }
                    placeholder="Confirm password"
                    className="h-10"
                  />
                </div>
              </div>

              {/* Additional Information */}
              <div className="space-y-5">
                <h3 className="font-semibold text-base text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Additional Information
                </h3>

                <div className="space-y-2">
                  <Label
                    htmlFor="role"
                    className="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    User Role
                  </Label>
                  <Select
                    value={createUserData.role}
                    onValueChange={(value) => handleInputChange("role", value)}
                  >
                    <SelectTrigger className="h-10">
                      <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="user">User</SelectItem>
                      <SelectItem value="sub_admin">Sub Admin</SelectItem>
                      <SelectItem value="admin">Admin</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="subscription_plan"
                    className="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Subscription Plan
                  </Label>
                  <Select
                    value={createUserData.subscription_plan}
                    onValueChange={(value) =>
                      handleInputChange("subscription_plan", value)
                    }
                  >
                    <SelectTrigger className="h-10">
                      <SelectValue placeholder="Select plan" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="freemium">Freemium</SelectItem>
                      <SelectItem value="pro">Pro</SelectItem>
                      <SelectItem value="enterprise">Enterprise</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="subscription_price"
                    className="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Monthly Price ($)
                  </Label>
                  <Input
                    id="subscription_price"
                    type="number"
                    value={createUserData.subscription_price}
                    onChange={(e) =>
                      handleInputChange("subscription_price", e.target.value)
                    }
                    placeholder="0.00"
                    className="h-10"
                  />
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="company_name"
                    className="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Company Name
                  </Label>
                  <Input
                    id="company_name"
                    value={createUserData.company_name}
                    onChange={(e) =>
                      handleInputChange("company_name", e.target.value)
                    }
                    placeholder="Enter company name"
                    className="h-10"
                  />
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="website"
                    className="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Website
                  </Label>
                  <Input
                    id="website"
                    type="url"
                    value={createUserData.website}
                    onChange={(e) =>
                      handleInputChange("website", e.target.value)
                    }
                    placeholder="https://example.com"
                    className="h-10"
                  />
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="industry"
                    className="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Industry
                  </Label>
                  <Select
                    value={createUserData.industry}
                    onValueChange={(value) =>
                      handleInputChange("industry", value)
                    }
                  >
                    <SelectTrigger className="h-10">
                      <SelectValue placeholder="Select industry" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="web3">Web3</SelectItem>
                      <SelectItem value="fintech">Fintech</SelectItem>
                      <SelectItem value="technology">Technology</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="pt-4 border-t">
            <Button variant="outline" onClick={handleCloseCreateModal}>
              Cancel
            </Button>
            <Button onClick={handleCreateUserSubmit} disabled={isCreating}>
              {isCreating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  Create User
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Success Modal */}
      <SuccessModal
        isOpen={isSuccessModalOpen}
        onOpenChange={setIsSuccessModalOpen}
        createdUser={createdUser}
        onClose={() => setIsSuccessModalOpen(false)}
        onViewUserDetails={() => {
          if (createdUser) {
            router.push(`/dashboard/users/${createdUser.id}`);
          }
          setIsSuccessModalOpen(false);
        }}
      />
    </>
  );
}
