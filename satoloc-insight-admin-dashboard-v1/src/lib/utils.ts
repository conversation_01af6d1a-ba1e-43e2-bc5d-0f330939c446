import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Theme utilities for the admin dashboard
 */
export const themeUtils = {
  /**
   * Get the appropriate icon based on current theme
   */
  getThemeIcon: (theme: string) => {
    switch (theme) {
      case "dark":
        return "🌙";
      case "light":
        return "☀️";
      default:
        return "🌓";
    }
  },

  /**
   * Get theme display name
   */
  getThemeDisplayName: (theme: string) => {
    switch (theme) {
      case "dark":
        return "Dark Mode";
      case "light":
        return "Light Mode";
      case "system":
        return "System";
      default:
        return "Auto";
    }
  },

  /**
   * Check if the current theme is dark
   */
  isDarkTheme: (theme: string, systemTheme?: string) => {
    if (theme === "system") {
      return systemTheme === "dark";
    }
    return theme === "dark";
  },
};
