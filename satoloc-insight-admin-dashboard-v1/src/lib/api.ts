import axios from "axios";
import Cookies from "js-cookie";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add token to requests
api.interceptors.request.use((config: any) => {
  const token = Cookies.get("admin_access_token");
  if (token && config.headers) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle token refresh
api.interceptors.response.use(
  (response: any) => response,
  async (error: any) => {
    if (error.response?.status === 401) {
      // Clear tokens and redirect to login
      Cookies.remove("admin_access_token");
      Cookies.remove("admin_refresh_token");
      if (typeof window !== "undefined") {
        window.location.href = "/login";
      }
    }
    return Promise.reject(error);
  }
);

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface AuthResponse {
  access_token: string;
  refresh_token: string;
  user: {
    id: number;
    username: string;
    email: string;
    role: string;
    is_admin: boolean;
  };
}

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
  profile_picture: string | null;
  registration_date: string;
  is_active: boolean;
  website: string | null;
  industry: string | null;
  created: string | null;
  company_name: string | null;
  subscription_plan: string;
  subscription_price: string | null;
  subscription_start_date: string | null;
  created_by?: number | null;
  created_by_username?: string | null;
  created_users_count?: number;
  remaining_user_slots?: number | null;
  can_create_more_users?: boolean;
  created_users?: Array<{
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
    is_active: boolean;
    registration_date: string;
  }>;
}

// Auth functions
export const authAPI = {
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    const response = await api.post("/auth/login/", credentials);
    return response.data;
  },

  getUserData: async (): Promise<User> => {
    const response = await api.get("/auth/user/");
    return response.data;
  },

  logout: async (): Promise<void> => {
    Cookies.remove("admin_access_token");
    Cookies.remove("admin_refresh_token");
  },
};

export interface CreateUserData {
  username: string;
  email: string;
  first_name?: string;
  last_name?: string;
  role?: string;
  subscription_plan?: string;
  subscription_price?: string | null;
  company_name?: string;
  website?: string;
  industry?: string;
  password: string;
  confirmPassword: string;
}

// Admin functions
export const adminAPI = {
  getUsers: async (): Promise<User[]> => {
    const response = await api.get("/auth/admin/admin-users/");
    return response.data;
  },

  createUser: async (userData: CreateUserData): Promise<User> => {
    const response = await api.post("/auth/admin/admin-users/", userData);
    return response.data;
  },

  deleteUser: async (userId: number): Promise<void> => {
    await api.delete(`/auth/admin/admin-users/${userId}/`);
  },

  updateUser: async (
    userId: number,
    userData: Partial<User>
  ): Promise<User> => {
    const response = await api.patch(
      `/auth/admin/admin-users/${userId}/`,
      userData
    );
    return response.data;
  },
};

export default api;
