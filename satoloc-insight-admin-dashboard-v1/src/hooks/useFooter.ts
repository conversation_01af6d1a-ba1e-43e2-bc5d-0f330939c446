import { useState, useEffect } from "react";

export function useFooter() {
  const [isMinimized, setIsMinimized] = useState(false);
  const [currentTime, setCurrentTime] = useState("");

  // Update time every second
  useEffect(() => {
    const updateTime = () => {
      setCurrentTime(
        new Date().toLocaleTimeString("en-US", {
          hour12: false,
          hour: "2-digit",
          minute: "2-digit",
        })
      );
    };

    // Set initial time
    updateTime();

    const timer = setInterval(updateTime, 1000);
    return () => clearInterval(timer);
  }, []);

  // Handle scroll behavior for auto-minimize
  useEffect(() => {
    let lastScrollY = window.scrollY;
    let scrollTimeout: NodeJS.Timeout;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // Clear existing timeout
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }

      // Minimize when scrolling down past 100px
      if (currentScrollY > 100 && currentScrollY > lastScrollY) {
        setIsMinimized(true);
      }
      // Auto-show when scrolling up or at top
      else if (currentScrollY < lastScrollY || currentScrollY < 50) {
        setIsMinimized(false);
      }

      // Reset to expanded after 3 seconds of no scrolling
      scrollTimeout = setTimeout(() => {
        if (window.scrollY < 100) {
          setIsMinimized(false);
        }
      }, 3000);

      lastScrollY = currentScrollY;
    };

    window.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      window.removeEventListener("scroll", handleScroll);
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
    };
  }, []);

  const toggleMinimized = () => {
    setIsMinimized(!isMinimized);
  };

  return {
    isMinimized,
    currentTime,
    toggleMinimized,
  };
}
