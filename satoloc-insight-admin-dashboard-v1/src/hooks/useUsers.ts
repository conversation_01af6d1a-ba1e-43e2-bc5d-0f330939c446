import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { adminAPI, User, CreateUserData } from "@/lib/api";

// Users hooks
export function useUsers() {
  return useQuery({
    queryKey: ["users"],
    queryFn: () => adminAPI.getUsers(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useCreateUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (userData: CreateUserData) => adminAPI.createUser(userData),
    onSuccess: (newUser) => {
      // Invalidate and refetch all users to get updated relationships
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
}

export function useDeleteUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (userId: number) => adminAPI.deleteUser(userId),
    onSuccess: () => {
      // Invalidate and refetch users
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
}

export function useUpdateUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      userId,
      userData,
    }: {
      userId: number;
      userData: Partial<User>;
    }) => adminAPI.updateUser(userId, userData),
    onSuccess: (updatedUser) => {
      // Update the user in the cache
      queryClient.setQueryData(["users"], (oldData: User[] | undefined) => {
        if (!oldData) return [updatedUser];
        return oldData.map((user) =>
          user.id === updatedUser.id ? updatedUser : user
        );
      });
    },
  });
}

// Helper hooks for specific user stats
export function useUserStats() {
  const { data: users, isLoading } = useUsers();

  const stats = {
    totalUsers: users?.length || 0,
    adminUsers: users?.filter((user) => user.role === "admin").length || 0,
    regularUsers: users?.filter((user) => user.role === "user").length || 0,
    proUsers:
      users?.filter((user) => user.subscription_plan === "pro").length || 0,
    enterpriseUsers:
      users?.filter((user) => user.subscription_plan === "enterprise").length ||
      0,
  };

  return {
    stats,
    isLoading,
  };
}
