import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { authAPI, LoginCredentials, User } from "@/lib/api";
import Cookies from "js-cookie";

// Auth hooks
export function useLogin() {
  const router = useRouter();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (credentials: LoginCredentials) => authAPI.login(credentials),
    onSuccess: (data) => {
      // Check if user is admin
      if (data.user.role !== "admin") {
        throw new Error("Access denied. Admin privileges required.");
      }

      // Store tokens
      Cookies.set("admin_access_token", data.access_token, { expires: 1 });
      Cookies.set("admin_refresh_token", data.refresh_token, { expires: 7 });

      // Set user data in cache
      queryClient.setQueryData(["currentUser"], data.user);

      // Redirect to dashboard
      router.push("/dashboard");
    },
    onError: (error: any) => {
      console.error("Login error:", error);
    },
  });
}

export function useCurrentUser() {
  return useQuery({
    queryKey: ["currentUser"],
    queryFn: async () => {
      const token = Cookies.get("admin_access_token");
      if (!token) {
        throw new Error("No authentication token");
      }
      return authAPI.getUserData();
    },
    enabled: !!Cookies.get("admin_access_token"),
    retry: false,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useLogout() {
  const router = useRouter();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => authAPI.logout(),
    onSuccess: () => {
      // Clear all queries
      queryClient.clear();

      // Redirect to login
      router.push("/login");
    },
  });
}

// Helper hook for checking if user is authenticated and admin
export function useAuthCheck() {
  const { data: user, isLoading, error } = useCurrentUser();

  const isAuthenticated = !!user && !error;
  const isAdmin = user?.role === "admin";

  return {
    user,
    isAuthenticated,
    isAdmin,
    isLoading,
    error,
  };
}
