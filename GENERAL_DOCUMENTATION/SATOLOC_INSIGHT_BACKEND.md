# SATOLOC Insight Backend - Comprehensive Technical Documentation

## Executive Summary

SATOLOC Insight Backend is a sophisticated Django-based microservices architecture designed for enterprise-scale localization, SEO analysis, and AI-powered content processing. The backend handles complex workflows including multi-language translation quality assurance, advanced SEO crawling, content generation, and real-time data processing through asynchronous task management.

## Core Architecture

### Framework & Infrastructure

- **Framework**: Django 4.2.16 with Django REST Framework 3.15.2
- **Language**: Python 3.x with type annotations
- **Database**: PostgreSQL 15 (production) with connection pooling
- **Cache & Message Broker**: Redis 5.2.0 for caching and task queues
- **Task Queue**: Celery 5.4.0 with Redis backend
- **Web Server**: Gunicorn with 3 workers, 300-second timeout
- **Deployment**: Render.com with Docker containerization

### Authentication & Security

- **Authentication**: JWT tokens via djangorestframework-simplejwt 5.3.1
- **Token Lifecycle**: 12-hour access tokens, 7-day refresh tokens with rotation
- **Security**: CORS headers, CSRF protection, secure static file serving
- **User Management**: Custom User model with role-based access control

## Database Architecture

### Core Models (12 Main Entities)

#### User Management (`users` app)

**CustomUser Model:**

- **Role System**: Admin, Sub Admin, Regular User
- **Industry Support**: Web3, Fintech, Technology
- **Subscription Plans**: Freemium, Pro, Enterprise with pricing
- **OAuth Integration**: Google OAuth2 with unique Google ID tracking
- **Profile Management**: Profile pictures, company info, website links

#### Localization System (`scraping` app)

**Project Model:**

- **Multi-language Support**: Source/target language pairs from 12 languages
- **Industry Categorization**: Web3, Fintech, Technology-specific content
- **Status Tracking**: Pending, processing, completed, failed states
- **User Association**: User-specific project isolation

**ScrapedData Model:**

- **Dual URL Support**: Source URL (required) + Target URL (optional)
- **Content Storage**: JSON field for flexible content structure
- **Status Management**: Real-time status tracking through workflow
- **Timestamp Tracking**: Creation and update timestamps

**AIReport Model:**

- **LQA Scoring**: Comprehensive quality assessment data
- **Error Analysis**: Detailed error categorization and severity
- **Metrics Storage**: Translation quality metrics and statistics

#### SEO Analytics (`seo_insights` & `advance_seo` apps)

**SEOProject Model:**

- **Website Analysis**: URL-based project management
- **Multi-crawl Support**: Historical crawl data retention
- **User-specific**: Isolated SEO projects per user

**SEOCrawl Model:**

- **Crawl Management**: Start/end time tracking with duration calculation
- **Status Monitoring**: Pending, processing, completed, failed states
- **Progress Tracking**: Real-time crawl progress indicators

**SEOMetadata Model (30+ Analysis Types):**

- **Content Analysis**: Title, meta description, heading structure (H1-H6)
- **Technical SEO**: Status codes, content types, encoding
- **JavaScript Analysis**: Dynamic content, dependencies, error detection
- **SERP Features**: Featured snippets, rich results, preview data
- **Backlink Analysis**: Toxic backlink detection, anchor text analysis

#### Custom Content (`custom_content` app)

**CustomContent Model:**

- **Content Management**: Title, prompts, keywords, multilingual content
- **User Association**: User-specific content isolation
- **Language Support**: Content generation in 12+ languages

**ContentEnhancement Model:**

- **Enhancement Types**: 5 distinct enhancement categories
- **Data Storage**: JSON field for flexible enhancement data
- **Timestamp Tracking**: Creation and modification history

#### WordPress Integration (`wp_content_sync` app)

**WordPressConnection Model:**

- **Site Management**: WordPress site URL and authentication
- **API Integration**: WordPress REST API credentials
- **Connection Status**: Active/inactive connection monitoring

**SyncedContent Model:**

- **Content Mapping**: WordPress post ID to internal content mapping
- **Sync Status**: Publication status tracking
- **Metadata Management**: WordPress-specific metadata handling

## API Architecture

### RESTful Endpoints (9 Core Modules)

#### 1. Authentication (`/api/auth/`)

- **User Registration**: POST `/api/auth/register/`
- **Login/Logout**: POST `/api/auth/login/`, POST `/api/auth/logout/`
- **Token Management**: POST `/api/auth/token/refresh/`
- **Profile Management**: GET/PUT `/api/auth/profile/`
- **Google OAuth**: POST `/api/auth/google-login/`

#### 2. Localization & Scraping (`/api/scraping/`)

- **Project Management**: Full CRUD operations for localization projects
- **Scraping Initiation**: POST `/api/scraping/scrape/`
- **Status Monitoring**: GET `/api/scraping/status/{id}/`
- **Results Retrieval**: GET `/api/scraping/results/{id}/`
- **AI Report Generation**: Automatic LQA scoring and analysis

#### 3. SEO Analytics (`/api/advance-seo/`)

- **Website Management**: CRUD operations for website analysis
- **Crawl Initiation**: POST `/api/advance-seo/start-crawl/`
- **Progress Monitoring**: GET `/api/advance-seo/crawl-status/{id}/`
- **Report Generation**: GET `/api/advance-seo/reports/{id}/`
- **Google Search Console**: Integration for enhanced data

#### 4. AI Content Generation (`/api/ai/`)

- **Content Creation**: POST `/api/ai/generate/`
- **Enhancement Services**: 5 types of content enhancement
- **Multi-language Support**: Generation in 12+ languages
- **Template Management**: Pre-built content templates

#### 5. Custom Content (`/api/custom-content/`)

- **Content CRUD**: Full lifecycle management
- **Enhancement Integration**: Real-time content improvements
- **Export Functions**: PDF, DOCX, WordPress publishing

#### 6. WordPress Sync (`/api/wp-content-sync/`)

- **Connection Management**: WordPress site authentication
- **Content Publishing**: Direct WordPress post creation
- **Sync Status**: Publication status monitoring

#### 7. Payment Processing (`/api/payments/`)

- **Stripe Integration**: Subscription management
- **Plan Management**: Freemium, Pro, Enterprise tiers
- **Usage Tracking**: Feature usage monitoring

#### 8. AI Assistant (`/api/ai-assistant/`)

- **Conversation Management**: Chat history and context
- **Analysis Support**: AI-powered content analysis
- **Report Generation**: Automated insight generation

#### 9. Health Monitoring (`/api/health/`)

- **System Status**: Application health checks
- **Service Monitoring**: Database and Redis connectivity

## Asynchronous Task Processing

### Celery Configuration

**Technical Specifications:**

- **Broker**: Redis with connection pooling (max 100 connections)
- **Serialization**: JSON for tasks and results
- **Task Limits**: 30-minute hard limit, 25-minute soft limit
- **Worker Configuration**: Max 50 tasks per child, prefetch multiplier 1
- **Acknowledgment**: Late ACK for reliability

### Core Async Tasks (5 Categories)

#### 1. SEO Crawling Tasks (`seo_insights.tasks`)

**start_seo_crawl(crawl_id):**

- **Crawl4AI Integration**: Advanced web crawling with JavaScript rendering
- **Multi-page Analysis**: Up to 10+ pages with depth 3 crawling
- **Real-time Status**: Progress updates through database
- **Error Handling**: Comprehensive retry logic with exponential backoff

**analyze_seo_data(crawl_id):**

- **30+ Analysis Types**: Complete SEO audit across all categories
- **Performance Metrics**: Core Web Vitals, page speed analysis
- **Technical Issues**: Broken links, missing meta tags, structure problems

#### 2. Localization Processing (`scraping.tasks`)

**scrape_website(scraped_data_id):**

- **Dual URL Processing**: Source and optional target URL analysis
- **Content Extraction**: Advanced content parsing and cleaning
- **LQA Integration**: Automatic quality assessment initiation
- **Error Categorization**: 7 error types with severity classification

#### 3. Translation Tasks

**Translation Processing:**

- **OpenAI GPT-4.1 Integration**: High-quality translation with 0.3 temperature
- **Chunk Processing**: 6,000 character, 2,000 token limits
- **HTML Preservation**: Complex content structure maintenance
- **Glossary Integration**: Terminology consistency enforcement

#### 4. Content Generation Tasks

**AI Content Creation:**

- **Multi-format Generation**: Blog posts, product descriptions, FAQs
- **SEO Optimization**: Keyword integration and meta tag generation
- **Language Adaptation**: Cultural and linguistic localization

#### 5. Background Processing

**Data Synchronization:**

- **WordPress Publishing**: Automated content publication
- **Export Generation**: PDF and DOCX document creation
- **Report Compilation**: Comprehensive analysis report generation

## External Service Integrations

### AI & Machine Learning Services

- **OpenAI GPT-4.1**: Content generation, translation, enhancement
- **DALL-E Integration**: AI image generation for content
- **spaCy NLP**: Text analysis and processing (en_core_web_sm)
- **NLTK**: Natural language processing tasks

### Web Technologies

- **Crawl4AI**: Advanced web crawling with JavaScript support
- **Scrapy 2.11.1**: Web scraping framework
- **Selenium 4.18.1**: Browser automation for dynamic content
- **Playwright 1.50.0**: Modern web automation

### Cloud & Infrastructure

- **AWS S3**: File storage and media management (eu-north-1 region)
- **Google APIs**: Search Console integration, OAuth2 authentication
- **Stripe 11.2.0**: Payment processing and subscription management
- **Redis**: Caching and task queue management

### Content & SEO Tools

- **WordPress REST API**: Direct content publishing
- **Beautiful Soup**: HTML parsing and content extraction
- **Newspaper3k**: Article extraction and analysis
- **Google Search Console API**: SERP data and performance metrics

## Performance & Scalability

### Optimization Features

- **Connection Pooling**: PostgreSQL and Redis optimization
- **Caching Strategy**: Redis-based caching with 5-second timeouts
- **Async Processing**: Non-blocking task execution
- **Worker Scaling**: Multiple Celery workers for parallel processing

### Monitoring & Reliability

- **Health Checks**: Application and service monitoring
- **Error Tracking**: Comprehensive logging and error handling
- **Task Retry Logic**: Automatic retry with exponential backoff
- **Database Optimization**: Query optimization and indexing

### Security Features

- **JWT Token Security**: Secure authentication with rotation
- **CORS Configuration**: Controlled cross-origin access
- **Environment-based Configuration**: Secure secrets management
- **Role-based Access**: User permission management

## Deployment & DevOps

### Production Environment

- **Platform**: Render.com with Docker containers
- **Database**: PostgreSQL with automated backups
- **Cache**: Redis cluster for high availability
- **Workers**: Dedicated Celery worker instances
- **Monitoring**: Health checks and service monitoring

### Development Tools

- **Code Quality**: Pylint, pytest for testing
- **Documentation**: Comprehensive API documentation
- **Version Control**: Git with feature branch workflow
- **CI/CD**: Automated testing and deployment pipelines

This backend architecture supports enterprise-scale operations with robust data processing, real-time analytics, and comprehensive API services for the SATOLOC Insight platform.
