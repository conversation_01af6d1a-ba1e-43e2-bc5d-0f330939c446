/*
 * Button mixin- creates a button effect with correct
 * highlights/shadows, based on a base color.
 */
@mixin button( $button-color, $button-text-color: #fff ) {
	background: $button-color;
	border-color: $button-color;
	color: $button-text-color;

	&:hover,
	&:focus {
		background: lighten( $button-color, 3% );
		border-color: darken( $button-color, 3% );
		color: $button-text-color;
	}

	&:focus {
		box-shadow:
			0 0 0 1px #fff,
			0 0 0 3px $button-color;
	}

	&:active {
		background: darken( $button-color, 5% );
		border-color: darken( $button-color, 5% );
		color: $button-text-color;
	}

	&.active,
	&.active:focus,
	&.active:hover {
		background: $button-color;
		color: $button-text-color;
		border-color: darken( $button-color, 15% );
		box-shadow: inset 0 2px 5px -3px darken( $button-color, 50% );
	}
}
