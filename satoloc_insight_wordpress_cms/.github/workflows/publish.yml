name: Generate a build and push to main branch

on:
  push:
    branches:
      - main # The branch name you are committing the new changes to

# Add this permissions block
permissions:
  contents: write

jobs:
  build:
    runs-on: ubuntu-latest
    name: Build and Push
    steps:
      - name: git-checkout
        uses: actions/checkout@v3

      # Uncomment and modify these steps if you're using a build process
      # - name: Setup Node.js
      #   uses: actions/setup-node@v3
      #   with:
      #     node-version: '14' # Specify the Node.js version you're using

      # - name: Install dependencies
      #   run: npm ci # Use 'npm ci' instead of 'npm install' for CI environments

      # - name: Build
      #   run: npm run build # Make sure this script is defined in your package.json

      - name: Push
        uses: s0/git-publish-subdir-action@develop
        env:
          REPO: self
          BRANCH: main # Pushing to the main branch
          FOLDER: . # The directory where your files are located (current directory)
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          MESSAGE: "Build: ({sha}) {msg}" # The commit message
